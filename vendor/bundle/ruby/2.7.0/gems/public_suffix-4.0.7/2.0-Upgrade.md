# Welcome to PublicSuffix 2.0!

PublicSuffix 2.0 contains a rewritten internal representation and comparison logic, that drastically increases the lookup performance. The new version also changes several internal and external API.

This document documents the most relevant changes to help you upgrading from PublicSuffix 1.0 to 2.0.

## What's New

- The library is now 100% compliants with the official PublicSuffix tests. The major breaking change you may experience, is that if a domain passed as input doesn't match any rule, the rule `*` is assumed. You can override this behavior by passing a custom default rule with the `default_rule` option. The old behavior can be restored by passing `default_rule: nil`.
- `PublicSuffix.domain` is a new method that parses the input and returns the domain (combination of second level domain + suffix). This is a convenient helper to parse a domain name, for example when you need to determine the cookie or SSL scope.
- Added the ability to disable the use of private domains either at runtime, in addition to the ability to not load the private domains section when reading the list (`private_domains: false`). This feature also superseded the `private_domains` class-level attribute, that is no longer available.

## Upgrade

When upgrading, here's the most relevant changes to keep an eye on:

- Several futile utility helpers were removed, such as `Domain#rule`, `Domain#is_a_domain?`, `Domain#is_a_subdomain?`, `Domain#valid?`. You can easily obtain the same result by having a custom method that reconstructs the logic, and/or calling `PublicSuffix.{domain|parse}(domain.to_s)`.
- `PublicSuffix::List.private_domains` is no longer available. Instead, you now have two ways to enable/disable the private domains:

    1. At runtime, by using the `ignore_private` option

    ```ruby
    PublicSuffix.domain("something.blogspot.com", ignore_private: true)
    ```

    1. Loading a filtered list:

    ```ruby
    # Disable support for private TLDs
    PublicSuffix::List.default = PublicSuffix::List.parse(File.read(PublicSuffix::List::DEFAULT_LIST_PATH), private_domains: false)
    # => "blogspot.com"
    PublicSuffix.domain("something.blogspot.com")
    # => "blogspot.com"
    ```
- Now that the library is 100% compliant with the official PublicSuffix algorithm, if a domain passed as input doesn't match any rule, the wildcard rule `*` is assumed. This means that unlisted TLDs will be considered valid by default, when they would have been invalid in 1.x. However, you can override this behavior to emulate the 1.x behavior if needed:

    ```ruby
    # 1.x:

    PublicSuffix.valid?("google.commm")
    # => false

    # 2.x:

    PublicSuffix.valid?("google.commm")
    # => true

    # Overriding 2.x behavior if needed:

    PublicSuffix.valid?("google.commm", default_rule: nil)
    # => false
    ````
