rbx.platform.typedef.() = pointer
rbx.platform.typedef.*caddr_t = char
rbx.platform.typedef.Psocklen_t = pointer
rbx.platform.typedef.avl_index_t = uint
rbx.platform.typedef.blkcnt64_t = long_long
rbx.platform.typedef.blkcnt_t = long_long
rbx.platform.typedef.blksize_t = long
rbx.platform.typedef.clock_t = long
rbx.platform.typedef.clockid_t = int
rbx.platform.typedef.cnt_t = short
rbx.platform.typedef.cpu_flag_t = ushort
rbx.platform.typedef.ctid_t = long
rbx.platform.typedef.daddr_t = long
rbx.platform.typedef.dev_t = ulong
rbx.platform.typedef.diskaddr_t = ulong_long
rbx.platform.typedef.disp_lock_t = uchar
rbx.platform.typedef.fd_mask = long
rbx.platform.typedef.fds_mask = long
rbx.platform.typedef.fsblkcnt64_t = ulong_long
rbx.platform.typedef.fsblkcnt_t = ulong_long
rbx.platform.typedef.fsfilcnt64_t = ulong_long
rbx.platform.typedef.fsfilcnt_t = ulong_long
rbx.platform.typedef.gid_t = long
rbx.platform.typedef.hrtime_t = long_long
rbx.platform.typedef.id_t = long
rbx.platform.typedef.in_addr_t = uint
rbx.platform.typedef.in_port_t = ushort
rbx.platform.typedef.index_t = short
rbx.platform.typedef.ino64_t = ulong_long
rbx.platform.typedef.ino_t = ulong_long
rbx.platform.typedef.int) = pointer
rbx.platform.typedef.int) = pointer
rbx.platform.typedef.int16_t = short
rbx.platform.typedef.int32_t = int
rbx.platform.typedef.int64_t = long_long
rbx.platform.typedef.int8_t = char
rbx.platform.typedef.int_fast16_t = int
rbx.platform.typedef.int_fast32_t = int
rbx.platform.typedef.int_fast64_t = long_long
rbx.platform.typedef.int_fast8_t = char
rbx.platform.typedef.int_least16_t = short
rbx.platform.typedef.int_least32_t = int
rbx.platform.typedef.int_least64_t = long_long
rbx.platform.typedef.int_least8_t = char
rbx.platform.typedef.intmax_t = long_long
rbx.platform.typedef.intptr_t = int
rbx.platform.typedef.ipaddr_t = uint
rbx.platform.typedef.k_fltset_t = uint
rbx.platform.typedef.key_t = int
rbx.platform.typedef.kid_t = int
rbx.platform.typedef.len_t = ulong_long
rbx.platform.typedef.lock_t = uchar
rbx.platform.typedef.longlong_t = long_long
rbx.platform.typedef.major_t = ulong
rbx.platform.typedef.minor_t = ulong
rbx.platform.typedef.mode_t = ulong
rbx.platform.typedef.model_t = uint
rbx.platform.typedef.nfds_t = ulong
rbx.platform.typedef.nlink_t = ulong
rbx.platform.typedef.o_dev_t = short
rbx.platform.typedef.o_gid_t = ushort
rbx.platform.typedef.o_ino_t = ushort
rbx.platform.typedef.o_mode_t = ushort
rbx.platform.typedef.o_nlink_t = short
rbx.platform.typedef.o_pid_t = short
rbx.platform.typedef.o_uid_t = ushort
rbx.platform.typedef.off64_t = long_long
rbx.platform.typedef.off_t = long_long
rbx.platform.typedef.offset_t = long_long
rbx.platform.typedef.pad64_t = long_long
rbx.platform.typedef.pfn_t = ulong
rbx.platform.typedef.pgcnt_t = ulong
rbx.platform.typedef.pid_t = long
rbx.platform.typedef.poolid_t = long
rbx.platform.typedef.pri_t = short
rbx.platform.typedef.projid_t = long
rbx.platform.typedef.pthread_key_t = uint
rbx.platform.typedef.pthread_t = uint
rbx.platform.typedef.ptrdiff_t = int
rbx.platform.typedef.rlim64_t = ulong_long
rbx.platform.typedef.rlim_t = ulong_long
rbx.platform.typedef.sa_family_t = ushort
rbx.platform.typedef.size_t = uint
rbx.platform.typedef.size_t) = pointer
rbx.platform.typedef.socklen_t = uint
rbx.platform.typedef.spgcnt_t = long
rbx.platform.typedef.ssize_t = int
rbx.platform.typedef.suseconds_t = long
rbx.platform.typedef.sysid_t = short
rbx.platform.typedef.t_scalar_t = long
rbx.platform.typedef.t_uscalar_t = ulong
rbx.platform.typedef.taskid_t = long
rbx.platform.typedef.time_t = long
rbx.platform.typedef.timer_t = int
rbx.platform.typedef.ts_t = long_long
rbx.platform.typedef.u_char = uchar
rbx.platform.typedef.u_int = uint
rbx.platform.typedef.u_long = ulong
rbx.platform.typedef.u_longlong_t = ulong_long
rbx.platform.typedef.u_offset_t = ulong_long
rbx.platform.typedef.u_short = ushort
rbx.platform.typedef.uchar_t = uchar
rbx.platform.typedef.uid_t = long
rbx.platform.typedef.uint = uint
rbx.platform.typedef.uint16_t = ushort
rbx.platform.typedef.uint32_t = uint
rbx.platform.typedef.uint64_t = ulong_long
rbx.platform.typedef.uint8_t = uchar
rbx.platform.typedef.uint_fast16_t = uint
rbx.platform.typedef.uint_fast32_t = uint
rbx.platform.typedef.uint_fast64_t = ulong_long
rbx.platform.typedef.uint_fast8_t = uchar
rbx.platform.typedef.uint_least16_t = ushort
rbx.platform.typedef.uint_least32_t = uint
rbx.platform.typedef.uint_least64_t = ulong_long
rbx.platform.typedef.uint_least8_t = uchar
rbx.platform.typedef.uint_t = uint
rbx.platform.typedef.uintmax_t = ulong_long
rbx.platform.typedef.uintptr_t = uint
rbx.platform.typedef.ulong = ulong
rbx.platform.typedef.ulong_t = ulong
rbx.platform.typedef.unchar = uchar
rbx.platform.typedef.upad64_t = ulong_long
rbx.platform.typedef.use_t = uchar
rbx.platform.typedef.useconds_t = uint
rbx.platform.typedef.ushort = ushort
rbx.platform.typedef.ushort_t = ushort
rbx.platform.typedef.zoneid_t = long
