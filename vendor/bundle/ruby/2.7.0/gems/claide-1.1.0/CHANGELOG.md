# CLAide Changelog

## 1.1.0 (2022-01-12)

##### Enhancements

* None.  

##### Bug Fixes

* None.  


## 1.0.3 (2019-08-02)

##### Enhancements

* None.  

##### Bug Fixes

* Correctly handle `--help` flags when using `argv.remainder!` after initialization  
  [<PERSON>](https://github.com/amorde),
  [tripleCC](https://github.com/tripleCC)
  [#87](https://github.com/CocoaPods/CLAide/pull/87)


## 1.0.2 (2017-06-06)

##### Enhancements

* None.  

##### Bug Fixes

* Avoid a method redefinition warning when requiring `claide`.  
  [<PERSON>](https://github.com/segiddins)


## 1.0.1 (2016-10-10)

##### Bug Fixes

* Adds a fix for older versions of Rubygems when CLAide crashes.  
  [<PERSON>](https://github.com/segiddins)
  [#73](https://github.com/CocoaPods/CLAide/issues/73)


## 1.0.0 (2016-05-10)

##### Enhancements

* None.  

##### Bug Fixes

* None.  


## 1.0.0.rc.1 (2016-04-30)

##### Enhancements

* None.  

##### Bug Fixes

* Fix circular require of `claide/ansi` in `claide/ansi/string_escaper`.  
  [bootstraponline](https://github.com/bootstraponline)
  [#66](https://github.com/CocoaPods/CLAide/issues/66)


## 1.0.0.beta.3 (2016-03-15)

##### Enhancements

* Added `Command.option` to easily add a single option to a command class.  
  [Samuel Giddins](https://github.com/segiddins)
  [#64](https://github.com/CocoaPods/CLAide/issues/64)

##### Bug Fixes

* None.  


## 1.0.0.beta.2 (2016-03-08)

##### Bug Fixes

* Attempt to get the terminal width without shelling out to `tput`.  
  [Samuel Giddins](https://github.com/segiddins)


## 1.0.0.beta.1 (2015-12-30)

##### Bug Fixes

* The plugin manager will now properly activate plugin gems, ensuring all of
  their files are requirable.  
  [Samuel Giddins](https://github.com/segiddins)


## 0.9.1 (2015-07-05)

##### Bug Fixes

* Fix a regression when contradictory flags were given in `ARGV` -- the last
  flag given will once again be the value returned, and all entries for that key
  are removed.  
  [Samuel Giddins](https://github.com/segiddins)


## 0.9.0 (2015-07-02)

##### Enhancements

* Properly parse everything in `ARGV` after `--` as an argument.  
  [Samuel Giddins](https://github.com/segiddins)
  [#48](https://github.com/CocoaPods/CLAide/issues/48)

* Allow parsing an option that occurs multiple times.  
  [Samuel Giddins](https://github.com/segiddins)


## 0.8.2 (2015-06-27)

##### Enhancements

* Add `ARGV#remainder!`, which returns all the remaining arguments, deleting
  them from the receiver.  
  [Samuel Giddins](https://github.com/segiddins)


## 0.8.1 (2015-02-25)

###### Bug Fixes

* Silence errors while loading plugins.  
  [Clément Beffa](https://github.com/cl3m)
  [#44](https://github.com/CocoaPods/CLAide/issues/44)


## 0.8.0 (2014-12-25)

###### Breaking

* Removes the `ShellCompletionHelper` along with completion script for ZSH. This is out of the scope of CLAide.  
  [Eloy Durán](https://github.com/alloy)
  [#43](https://github.com/CocoaPods/CLAide/issues/43)

* Various refactoring replacing “Helper” API’s which specialised classes such as ArgumentSuggester, TextWrapper and PluginManager.  
  [Eloy Durán](https://github.com/alloy)

###### Enhancements

* Added convenience method to invoke commands more easily.
  [Olivier Halligon](https://github.com/AliSoftware)
  [#33](https://github.com/CocoaPods/CLAide/issues/40)

* Changes to the PluginManager to handle multiple plugin prefixes, which by default adds the `clad` plugin prefix.  
  [Eloy Durán](https://github.com/alloy)

## 0.7.0 (2014-09-11)

###### Breaking

* Plugins are now expected to include the `cocoapods-plugin.rb` file in
  `./lib`.  
  [Fabio Pelosin](https://github.com/fabiopelosin)
  [#28](https://github.com/CocoaPods/CLAide/pull/28)

###### Enhancements

* Improved messages for exceptions generated by plugins.  
  [Fabio Pelosin](https://github.com/fabiopelosin)
  [#28](https://github.com/CocoaPods/CLAide/pull/28)

* Use the Argument class to describe arguments.  
  [Olivier Halligon](https://github.com/AliSoftware)
  [#33](https://github.com/CocoaPods/CLAide/issues/33)

* Support for argument alternatives and repeatable arguments (ellipsis).  
  [Olivier Halligon](https://github.com/AliSoftware)
  [#33](https://github.com/CocoaPods/CLAide/issues/33)

* No stack trace if --help and --vebose are combined.  
  [Marius Rackwitz](https://github.com/mrackwitz)
  [#36](https://github.com/CocoaPods/CLAide/issues/36)


## 0.6.1 (2014-05-20)

###### Bug Fixes

* Respect the ANSI flag for the help banner.  
  [Fabio Pelosin](https://github.com/fabiopelosin)
  [#34](https://github.com/CocoaPods/CLAide/issues/34)

* Underline the colon of the titles of the help banner.  
  [Fabio Pelosin](https://github.com/fabiopelosin)

## 0.6.0 (2014-05-19)

###### Enhancements

* Use an array to describe arguments.  
  [Fabio Pelosin][fabiopelosin]
  [#26](https://github.com/CocoaPods/CLAide/issues/26)

* Improved layout and contents of help banner  
  [Fabio Pelosin](https://github.com/fabiopelosin)
  [#25](https://github.com/CocoaPods/CLAide/pull/25)

* Colorize option, arguments, and example commands in the help banner.  
  [Fabio Pelosin](https://github.com/fabiopelosin)
  [#12](https://github.com/CocoaPods/CLAide/issues/12)

* Add support for ANSI escape sequences.  
  [Fabio Pelosin](https://github.com/fabiopelosin)
  [#17](https://github.com/CocoaPods/CLAide/issues/17)
  [#20](https://github.com/CocoaPods/CLAide/pull/20)
  [#24](https://github.com/CocoaPods/CLAide/pull/24)

* Add support for completion script  
  [Fabio Pelosin](https://github.com/fabiopelosin)
  [#19](https://github.com/CocoaPods/CLAide/pull/19)

* Add support for version logic via the introduction of the `version` class
  attribute to the `CLAide::Commmand` class. If a value for the attribute is
  specified the `--version` flag is added. The `--version --verbose` flags
  include the version of the plugins in the output.  
  [Fabio Pelosin](https://github.com/fabiopelosin)
  [#13](https://github.com/CocoaPods/CLAide/issues/13)
  [#14](https://github.com/CocoaPods/CLAide/issues/14)

## 0.5.0 (2014-03-26)

###### Enhancements

* Add a `ignore_in_command_lookup` option to commands, which makes it possible
  to have anonymous command classes that are or only meant to provide common
  functionality, but are otherwise completely ignored during parsing, command
  lookup, and help banner printing.  
  [Eloy Durán](https://github.com/alloy)

* Deprecate the `color` option in favor of `ansi`. This is more abstract and
  can be used for commands that only prettify output by using, for instance,
  the bold ANSI code. This applies to the `CLAide` APIs as well.  
  [Eloy Durán](https://github.com/alloy)

* Add more hooks that allow the user to customize how to prettify output.  
  [Eloy Durán](https://github.com/alloy)

* Word wrap option descriptions to terminal width.  
  [Eloy Durán](https://github.com/alloy)
  [#6](https://github.com/CocoaPods/CLAide/issues/6)


## 0.4.0 (2013-11-14)

###### Enhancements

* Added support for plugins.  
  [Les Hill](https://github.com/leshill)
  [#1](https://github.com/CocoaPods/CLAide/pull/1)
