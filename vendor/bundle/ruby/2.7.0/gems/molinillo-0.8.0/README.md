# Molinillo

[![Build Status](https://github.com/CocoaPods/Molinillo/workflows/test/badge.svg)](https://github.com/CocoaPods/Molinillo/actions?query=branch%3Amaster)
[![Coverage](https://img.shields.io/codeclimate/coverage/github/CocoaPods/Molinillo.svg?style=flat)](https://codeclimate.com/github/CocoaPods/Molinillo)
[![Code Climate](https://img.shields.io/codeclimate/github/CocoaPods/Molinillo.svg?style=flat)](https://codeclimate.com/github/CocoaPods/Molinillo)

A generic dependency-resolution implementation.

## Installation

Add this line to your application's Gemfile:

```ruby
gem 'molinillo', :git => 'https://github.com/CocoaPods/Molinillo'
```

And then execute:

```bash
$ bundle install
```

Or install it yourself as:

```bash
$ gem install molinillo
```

## Usage

See the [ARCHITECTURE](ARCHITECTURE.md) file for an overview and look at the test suite for example usage.

## Contributing

1. Fork it
2. Create your feature branch (`git checkout -b my-new-feature`)
3. Commit your changes (`git commit -am 'Add some feature'`)
4. Push to the branch (`git push origin my-new-feature`)
5. Create a pull request

## The Name

[Molinillo](http://en.wikipedia.org/wiki/Molinillo_(whisk)) is a special whisk used in Mexico in the preparation of beverages such as hot chocolate.
Much like a dependency resolver, a molinillo helps take a list of ingredients and turn it into a delicious concoction!
