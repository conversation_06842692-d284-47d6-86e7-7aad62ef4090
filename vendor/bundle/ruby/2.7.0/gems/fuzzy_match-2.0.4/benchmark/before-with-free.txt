   1962 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/activesupport-3.0.5/lib/active_support/core_ext/object/blank.rb:68:String
   1957 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/fastercsv-1.5.4/lib/faster_csv.rb:1632:String
    342 ./benchmark/../lib/fuzzy_match/wrapper.rb:29:String
    326 ./benchmark/../lib/fuzzy_match/wrapper.rb:29:Array
    325 benchmark/memory.rb:21:String
    325 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table/hasher.rb:20:String
    325 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table/format/delimited.rb:22:ActiveSupport::OrderedHash
    325 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table.rb:65:String
    325 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/activesupport-3.0.5/lib/active_support/ordered_hash.rb:39:Array
    325 ./benchmark/../lib/fuzzy_match/wrapper.rb:25:FuzzyMatch::Similarity
    325 ./benchmark/../lib/fuzzy_match/similarity.rb:57:Array
    325 ./benchmark/../lib/fuzzy_match/similarity.rb:25:FuzzyMatch::Score
    325 ./benchmark/../lib/fuzzy_match/score.rb:13:Float
    325 ./benchmark/../lib/fuzzy_match.rb:35:FuzzyMatch::Wrapper
    320 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table/format/delimited.rb:28:String
    303 ./benchmark/../lib/fuzzy_match/similarity.rb:21:Float
    201 ./benchmark/../lib/fuzzy_match/normalizer.rb:20:String
    184 ./benchmark/../lib/fuzzy_match/normalizer.rb:14:String
    140 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch.bundle:0:__node__
     41 ./benchmark/../lib/fuzzy_match/similarity.rb:49:__node__
     31 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:27:Regexp
     28 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:19:__node__
     22 ./benchmark/../lib/fuzzy_match/similarity.rb:57:__node__
     22 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:20:__node__
     21 ./benchmark/../lib/fuzzy_match.rb:199:FuzzyMatch::Grouping
     17 ./benchmark/../lib/fuzzy_match/similarity.rb:21:__node__
     16 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch.bundle:0:Class
     14 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:4:__node__
     14 ./benchmark/../lib/fuzzy_match/similarity.rb:37:__node__
     13 ./benchmark/../lib/fuzzy_match/wrapper.rb:15:__node__
     13 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:27:__node__
     12 ./benchmark/../lib/fuzzy_match/wrapper.rb:29:__node__
     12 ./benchmark/../lib/fuzzy_match/wrapper.rb:19:__node__
     11 ./benchmark/../lib/fuzzy_match/identity.rb:18:__node__
     11 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:26:__node__
     11 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:25:__node__
     11 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:24:__node__
     10 ./benchmark/../lib/fuzzy_match/similarity.rb:55:__node__
     10 ./benchmark/../lib/fuzzy_match/similarity.rb:39:__node__
     10 ./benchmark/../lib/fuzzy_match/similarity.rb:25:__node__
     10 ./benchmark/../lib/fuzzy_match.rb:193:FuzzyMatch::Identity
      9 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch.bundle:0:String
      9 ./benchmark/../lib/fuzzy_match/wrapper.rb:10:__node__
      9 ./benchmark/../lib/fuzzy_match/similarity.rb:49:String
      9 ./benchmark/../lib/fuzzy_match/similarity.rb:42:__node__
      9 ./benchmark/../lib/fuzzy_match/similarity.rb:41:__node__
      8 ./benchmark/../lib/fuzzy_match/wrapper.rb:31:__node__
      8 ./benchmark/../lib/fuzzy_match/normalizer.rb:27:__node__
      8 ./benchmark/../lib/fuzzy_match/normalizer.rb:14:__node__
      8 ./benchmark/../lib/fuzzy_match/similarity.rb:38:__node__
      8 ./benchmark/../lib/fuzzy_match/score.rb:13:__node__
      8 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:23:__node__
      8 ./benchmark/../lib/fuzzy_match/grouping.rb:24:__node__
      7 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:7:__node__
      7 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:6:__node__
      7 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:5:__node__
      7 ./benchmark/../lib/fuzzy_match/wrapper.rb:9:__node__
      7 ./benchmark/../lib/fuzzy_match/wrapper.rb:25:__node__
      7 ./benchmark/../lib/fuzzy_match/similarity.rb:45:__node__
      7 ./benchmark/../lib/fuzzy_match/score.rb:17:__node__
      7 ./benchmark/../lib/fuzzy_match/identity.rb:19:__node__
      7 ./benchmark/../lib/fuzzy_match/grouping.rb:27:__node__
      7 ./benchmark/../lib/fuzzy_match.rb:209:String
      6 ./benchmark/../lib/fuzzy_match/wrapper.rb:8:__node__
      6 ./benchmark/../lib/fuzzy_match/similarity.rb:44:__node__
      6 ./benchmark/../lib/fuzzy_match/similarity.rb:15:__node__
      6 ./benchmark/../lib/fuzzy_match/similarity.rb:13:__node__
      6 ./benchmark/../lib/fuzzy_match/score.rb:25:__node__
      6 ./benchmark/../lib/fuzzy_match/score.rb:21:__node__
      6 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:10:__node__
      6 ./benchmark/../lib/fuzzy_match/grouping.rb:22:__node__
      5 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/fastercsv-1.5.4/lib/faster_csv.rb:1640:String
      5 ./benchmark/../lib/fuzzy_match/wrapper.rb:34:__node__
      5 ./benchmark/../lib/fuzzy_match/normalizer.rb:9:__node__
      5 ./benchmark/../lib/fuzzy_match/normalizer.rb:19:__node__
      5 ./benchmark/../lib/fuzzy_match/similarity.rb:8:__node__
      5 ./benchmark/../lib/fuzzy_match/similarity.rb:33:__node__
      5 ./benchmark/../lib/fuzzy_match/similarity.rb:29:__node__
      5 ./benchmark/../lib/fuzzy_match/similarity.rb:12:__node__
      5 ./benchmark/../lib/fuzzy_match/score.rb:9:__node__
      5 ./benchmark/../lib/fuzzy_match/result.rb:16:__node__
      5 ./benchmark/../lib/fuzzy_match/identity.rb:10:__node__
      5 ./benchmark/../lib/fuzzy_match/grouping.rb:26:__node__
      5 ./benchmark/../lib/fuzzy_match/grouping.rb:25:__node__
      5 ./benchmark/../lib/fuzzy_match/grouping.rb:15:__node__
      4 ./benchmark/../lib/fuzzy_match/wrapper.rb:33:__node__
      4 ./benchmark/../lib/fuzzy_match/wrapper.rb:30:__node__
      4 ./benchmark/../lib/fuzzy_match/normalizer.rb:20:__node__
      4 ./benchmark/../lib/fuzzy_match/similarity.rb:59:__node__
      4 ./benchmark/../lib/fuzzy_match/similarity.rb:54:__node__
      4 ./benchmark/../lib/fuzzy_match/score.rb:5:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:9:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:8:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:7:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:6:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:5:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:4:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:3:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:13:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:12:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:11:__node__
      4 ./benchmark/../lib/fuzzy_match/result.rb:10:__node__
      4 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:8:__node__
      4 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:22:__node__
      4 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:21:__node__
      4 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:20:String
      4 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:11:__node__
      3 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:8:__node__
      3 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:3:__node__
      3 ./benchmark/../lib/fuzzy_match/wrapper.rb:28:__node__
      3 ./benchmark/../lib/fuzzy_match/wrapper.rb:24:__node__
      3 ./benchmark/../lib/fuzzy_match/wrapper.rb:18:__node__
      3 ./benchmark/../lib/fuzzy_match/wrapper.rb:15:String
      3 ./benchmark/../lib/fuzzy_match/wrapper.rb:14:__node__
      3 ./benchmark/../lib/fuzzy_match/normalizer.rb:8:__node__
      3 ./benchmark/../lib/fuzzy_match/normalizer.rb:26:__node__
      3 ./benchmark/../lib/fuzzy_match/normalizer.rb:18:__node__
      3 ./benchmark/../lib/fuzzy_match/normalizer.rb:13:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:7:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:6:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:58:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:56:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:48:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:36:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:32:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:28:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:24:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:20:__node__
      3 ./benchmark/../lib/fuzzy_match/similarity.rb:11:__node__
      3 ./benchmark/../lib/fuzzy_match/score.rb:8:__node__
      3 ./benchmark/../lib/fuzzy_match/score.rb:7:__node__
      3 ./benchmark/../lib/fuzzy_match/score.rb:24:__node__
      3 ./benchmark/../lib/fuzzy_match/score.rb:20:__node__
      3 ./benchmark/../lib/fuzzy_match/score.rb:16:__node__
      3 ./benchmark/../lib/fuzzy_match/score.rb:12:__node__
      3 ./benchmark/../lib/fuzzy_match/result.rb:21:__node__
      3 ./benchmark/../lib/fuzzy_match/result.rb:19:__node__
      3 ./benchmark/../lib/fuzzy_match/result.rb:15:__node__
      3 ./benchmark/../lib/fuzzy_match/identity.rb:9:__node__
      3 ./benchmark/../lib/fuzzy_match/identity.rb:17:__node__
      3 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:3:__node__
      3 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:18:__node__
      3 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:16:String
      3 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:15:String
      3 ./benchmark/../lib/fuzzy_match/grouping.rb:33:__node__
      3 ./benchmark/../lib/fuzzy_match/grouping.rb:14:__node__
      3 ./benchmark/../lib/fuzzy_match.rb:77:Array
      2 /Users/<USER>/.rvm/rubies/ruby-1.8.7-p334/lib/ruby/1.8/uri/common.rb:387:String
      2 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:3:String
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:6:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:5:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:4:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:3:Class
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:35:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:32:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:26:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:22:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:20:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:16:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:12:__node__
      2 ./benchmark/../lib/fuzzy_match/wrapper.rb:11:__node__
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:6:__node__
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:3:Class
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:28:__node__
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:27:String
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:24:__node__
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:23:__node__
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:22:__node__
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:15:__node__
      2 ./benchmark/../lib/fuzzy_match/normalizer.rb:10:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:60:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:50:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:4:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:46:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:3:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:34:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:30:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:2:Class
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:26:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:22:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:17:__node__
      2 ./benchmark/../lib/fuzzy_match/similarity.rb:16:__node__
      2 ./benchmark/../lib/fuzzy_match/score.rb:4:Class
      2 ./benchmark/../lib/fuzzy_match/score.rb:26:__node__
      2 ./benchmark/../lib/fuzzy_match/score.rb:22:__node__
      2 ./benchmark/../lib/fuzzy_match/score.rb:18:__node__
      2 ./benchmark/../lib/fuzzy_match/score.rb:17:String
      2 ./benchmark/../lib/fuzzy_match/score.rb:14:__node__
      2 ./benchmark/../lib/fuzzy_match/score.rb:13:String
      2 ./benchmark/../lib/fuzzy_match/result.rb:2:Class
      2 ./benchmark/../lib/fuzzy_match/result.rb:17:__node__
      2 ./benchmark/../lib/fuzzy_match/identity.rb:7:__node__
      2 ./benchmark/../lib/fuzzy_match/identity.rb:4:Class
      2 ./benchmark/../lib/fuzzy_match/identity.rb:23:__node__
      2 ./benchmark/../lib/fuzzy_match/identity.rb:22:__node__
      2 ./benchmark/../lib/fuzzy_match/identity.rb:21:__node__
      2 ./benchmark/../lib/fuzzy_match/identity.rb:11:__node__
      2 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:7:__node__
      2 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:6:__node__
      2 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:5:__node__
      2 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:23:String
      2 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:12:__node__
      2 ./benchmark/../lib/fuzzy_match/grouping.rb:9:Class
      2 ./benchmark/../lib/fuzzy_match/grouping.rb:34:__node__
      2 ./benchmark/../lib/fuzzy_match/grouping.rb:32:__node__
      2 ./benchmark/../lib/fuzzy_match/grouping.rb:30:__node__
      2 ./benchmark/../lib/fuzzy_match/grouping.rb:29:__node__
      2 ./benchmark/../lib/fuzzy_match/grouping.rb:23:__node__
      2 ./benchmark/../lib/fuzzy_match/grouping.rb:16:__node__
      2 ./benchmark/../lib/fuzzy_match/grouping.rb:12:__node__
      2 ./benchmark/../lib/fuzzy_match.rb:86:Array
      1 benchmark/memory.rb:50:String
      1 benchmark/memory.rb:49:FuzzyMatch
      1 /Users/<USER>/.rvm/rubies/ruby-1.8.7-p334/lib/ruby/1.8/uri/common.rb:492:URI::Generic
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table/executor.rb:19:Process::Status
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table/executor.rb:10:Bignum
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table.rb:63:Array
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table.rb:121:RemoteTable::Transformer
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table.rb:116:RemoteTable::Format::Delimited
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table.rb:111:RemoteTable::Properties
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/remote_table-1.1.6/lib/remote_table.rb:106:RemoteTable::LocalFile
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:4:Regexp
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:4:Array
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:1:__node__
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:1:String
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/amatch-0.2.5/lib/amatch/version.rb:1:Module
      1 /Users/<USER>/.rvm/gems/ruby-1.8.7-p334/gems/activesupport-3.0.5/lib/active_support/core_ext/hash/keys.rb:18:Hash
      1 ./benchmark/../lib/fuzzy_match/wrapper.rb:3:__node__
      1 ./benchmark/../lib/fuzzy_match/wrapper.rb:3:String
      1 ./benchmark/../lib/fuzzy_match/wrapper.rb:25:String
      1 ./benchmark/../lib/fuzzy_match/wrapper.rb:1:__node__
      1 ./benchmark/../lib/fuzzy_match/wrapper.rb:10:String
      1 ./benchmark/../lib/fuzzy_match/normalizer.rb:4:String
      1 ./benchmark/../lib/fuzzy_match/normalizer.rb:4:FuzzyMatch::ExtractRegexp
      1 ./benchmark/../lib/fuzzy_match/normalizer.rb:3:__node__
      1 ./benchmark/../lib/fuzzy_match/normalizer.rb:3:String
      1 ./benchmark/../lib/fuzzy_match/normalizer.rb:1:__node__
      1 ./benchmark/../lib/fuzzy_match/similarity.rb:9:__node__
      1 ./benchmark/../lib/fuzzy_match/similarity.rb:2:__node__
      1 ./benchmark/../lib/fuzzy_match/similarity.rb:2:String
      1 ./benchmark/../lib/fuzzy_match/similarity.rb:25:String
      1 ./benchmark/../lib/fuzzy_match/similarity.rb:1:__node__
      1 ./benchmark/../lib/fuzzy_match/score.rb:4:__node__
      1 ./benchmark/../lib/fuzzy_match/score.rb:4:String
      1 ./benchmark/../lib/fuzzy_match/score.rb:3:__node__
      1 ./benchmark/../lib/fuzzy_match/score.rb:1:String
      1 ./benchmark/../lib/fuzzy_match/score.rb:13:Array
      1 ./benchmark/../lib/fuzzy_match/score.rb:10:__node__
      1 ./benchmark/../lib/fuzzy_match/result.rb:2:__node__
      1 ./benchmark/../lib/fuzzy_match/result.rb:2:String
      1 ./benchmark/../lib/fuzzy_match/result.rb:1:__node__
      1 ./benchmark/../lib/fuzzy_match/identity.rb:5:FuzzyMatch::ExtractRegexp
      1 ./benchmark/../lib/fuzzy_match/identity.rb:4:__node__
      1 ./benchmark/../lib/fuzzy_match/identity.rb:4:String
      1 ./benchmark/../lib/fuzzy_match/identity.rb:1:__node__
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:4:__node__
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:2:__node__
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:2:String
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:2:Module
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:28:__node__
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:26:String
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:25:String
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:24:String
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:23:Regexp
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:1:__node__
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:17:Hash
      1 ./benchmark/../lib/fuzzy_match/extract_regexp.rb:10:String
      1 ./benchmark/../lib/fuzzy_match/grouping.rb:9:__node__
      1 ./benchmark/../lib/fuzzy_match/grouping.rb:9:String
      1 ./benchmark/../lib/fuzzy_match/grouping.rb:1:__node__
      1 ./benchmark/../lib/fuzzy_match/grouping.rb:10:FuzzyMatch::ExtractRegexp
      1 ./benchmark/../lib/fuzzy_match.rb:62:FuzzyMatch::Wrapper
      1 ./benchmark/../lib/fuzzy_match.rb:39:String
      1 ./benchmark/../lib/fuzzy_match.rb:39:FuzzyMatch::Result
      1 ./benchmark/../lib/fuzzy_match.rb:35:String
      1 ./benchmark/../lib/fuzzy_match.rb:209:Array
      1 ./benchmark/../lib/fuzzy_match.rb:199:String
      1 ./benchmark/../lib/fuzzy_match.rb:198:Array
      1 ./benchmark/../lib/fuzzy_match.rb:193:String
      1 ./benchmark/../lib/fuzzy_match.rb:192:Array
      1 ./benchmark/../lib/fuzzy_match.rb:187:String
      1 ./benchmark/../lib/fuzzy_match.rb:186:Array
      1 ./benchmark/../lib/fuzzy_match.rb:101:Array
