# -*- encoding: utf-8 -*-
# stub: concurrent-ruby 1.1.10 ruby lib/concurrent-ruby

Gem::Specification.new do |s|
  s.name = "concurrent-ruby".freeze
  s.version = "1.1.10"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "changelog_uri" => "https://github.com/ruby-concurrency/concurrent-ruby/blob/master/CHANGELOG.md", "source_code_uri" => "https://github.com/ruby-concurrency/concurrent-ruby" } if s.respond_to? :metadata=
  s.require_paths = ["lib/concurrent-ruby".freeze]
  s.authors = ["Jerry <PERSON>Antonio".freeze, "Petr <PERSON>".freeze, "The Ruby Concurrency Team".freeze]
  s.date = "2022-03-22"
  s.description = "Modern concurrency tools including agents, futures, promises, thread pools, actors, supervisors, and more.\nInspired by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Go, JavaScript, actors, and classic concurrency patterns.\n".freeze
  s.email = "<EMAIL>".freeze
  s.extra_rdoc_files = ["README.md".freeze, "LICENSE.txt".freeze, "CHANGELOG.md".freeze]
  s.files = ["CHANGELOG.md".freeze, "LICENSE.txt".freeze, "README.md".freeze]
  s.homepage = "http://www.concurrent-ruby.com".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.2".freeze)
  s.rubygems_version = "3.1.6".freeze
  s.summary = "Modern concurrency tools for Ruby. Inspired by Erlang, Clojure, Scala, Haskell, F#, C#, Java, and classic concurrency patterns.".freeze

  s.installed_by_version = "3.1.6" if s.respond_to? :installed_by_version
end
