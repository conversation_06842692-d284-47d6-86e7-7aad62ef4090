import { AccessLevelTypeNames, AuthService } from '../services/authService';
import { CustomReportWithoutStreamingService } from '../services/customReportWithoutStreamingService';
const { RequestHandler } = require('../services/requestHandler');

import { WebApp } from "meteor/webapp";

if (Meteor.isServer) {
    const bodyParser = require('body-parser');
    const router = WebApp.express();
    WebApp.handlers.use(bodyParser.json());
    WebApp.handlers.use(
        router
            .get('/api/v2/reports/invoices', async function (req, res) {
                const requestHandler = new RequestHandler(req, res);
                try{
                    await requestHandler.handleRequestOverhead({ isAnonymous: true, role: AccessLevelTypeNames.READ });
                    const result = await CustomReportWithoutStreamingService.getReport(req.index);
                    requestHandler.addResourceToResponse(result);
                    requestHandler.responseBuilderService.sendResponse(res);
                }catch(err){
                    requestHandler.error(err);
                }
            })

            .post('/api/v2/reports/invoices/fields', async function (req, res) {
                const requestHandler = new RequestHandler(req, res);
                try{
                    await requestHandler.handleRequestOverhead({ isAnonymous: true });
                    CustomReportWithoutStreamingService.fieldCache = {};
                    CustomReportWithoutStreamingService.dataCache = {};
                    const result = await CustomReportWithoutStreamingService.getFields(req.body.index);
                    requestHandler.addResourceToResponse(result);
                    requestHandler.responseBuilderService.sendResponse(res);
                }catch(err){
                    requestHandler.error(err);
                }
            })
            .post('/api/v2/reports/invoices/members', async function (req, res) {
                const requestHandler = new RequestHandler(req, res);
                try {
                    await requestHandler.handleRequestOverhead({ isAnonymous: true });
                    CustomReportWithoutStreamingService.org = req.body.org;
                    const result = await CustomReportWithoutStreamingService.getMembers(req.body.index, req.body.field, req.body.page, req.body.org);
                    requestHandler.addResourceToResponse(result);
                    requestHandler.responseBuilderService.sendResponse(res);
                } catch (err) {
                    requestHandler.error(err);
                }
            })
            .post('/api/v2/reports/invoices/select', async function (req, res) {
                const requestHandler = new RequestHandler(req, res);
                try {
                    await requestHandler.handleRequestOverhead({ isAnonymous: true });
                    CustomReportWithoutStreamingService.org = req.body.org;
                    const result = await CustomReportWithoutStreamingService.getSelectResult(req.body.index, req.body.query, req.body.page);
                    requestHandler.addResourceToResponse(result);
                    requestHandler.responseBuilderService.sendResponse(res);
                } catch (err) {
                    requestHandler.error(err);
                }
            })
            .post('/api/v2/reports/invoices/handshake', async function (req, res) {
                const requestHandler = new RequestHandler(req, res);
                try{
                    await requestHandler.handleRequestOverhead({ isAnonymous: true });
                    const result = await CustomReportWithoutStreamingService.handshake();
                    requestHandler.addResourceToResponse(result);
                    requestHandler.responseBuilderService.sendResponse(res);
                }catch(err){
                    requestHandler.error(err);
                }
            })
    )        
}