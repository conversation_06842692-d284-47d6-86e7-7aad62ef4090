const { PublicConfigurationSettingsService, PublicConfigurationConstants } = require('../../../../lib/config/publicConfigurationSettingsService');
const { RequestHandler } = require('../../services/requestHandler');
const { ResponsePayloadTypes } = require('../../services/responseBuilder');
import { WebApp } from "meteor/webapp";

if (Meteor.isServer) {
    const bodyParser = require('body-parser');
    const router = WebApp.express();
    if (PublicConfigurationSettingsService.getPublicConfigurationSetting(PublicConfigurationConstants.DEBUG_MODE)) {
        WebApp.handlers.use(bodyParser.json());
        WebApp.handlers.use(
            router
                .get('/api/v2/test-get-one', async function (req, res) {
                    const requestHandler = new RequestHandler(req, res);
                    requestHandler.handleRequestOverhead();
                    requestHandler.addResourceToResponse({ test: 'test' });
                    requestHandler.responseBuilderService.sendResponse(res);
                })
                .get('/api/v2/test-get-many', async function (req, res) {
                    const requestHandler = new RequestHandler(req, res, ResponsePayloadTypes.MULTIPLE);
                    requestHandler.handleRequestOverhead();
                    requestHandler.addResourceToResponse({ test: 'test1' });
                    requestHandler.addResourceToResponse({ test: 'test2' });
                    requestHandler.responseBuilderService.setTotalCount(2);
                    requestHandler.responseBuilderService.sendResponse(res);
                })
                .get('/api/v2/test-no-data', async function (req, res) {
                    const requestHandler = new RequestHandler(req, res, ResponsePayloadTypes.NONE);
                    requestHandler.handleRequestOverhead();
                    requestHandler.responseBuilderService.sendResponse(res);
                })
        );
    }
}
