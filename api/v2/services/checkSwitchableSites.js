export class CheckGetSwitchableSites {
  constructor() { }

  /**
   * Determines if a person can switch between organizations.
   * @param {Object} person - The person object.
   * @param {Object} org - The organization object.
   * @returns {boolean} True if the person can switch organizations, otherwise false.
   */
  static canGetSwitchableSites(person, org) {
    return (
      !!person &&
      !!org &&
      person.type === 'admin' &&
      !!(person.masterAdmin || person.superAdmin) &&
      !!(org.parentOrgId || org.enableSwitchOrg)
    );
  }
}
