import { InvoicesRepository } from "../repositories/invoiceRepository";
import { OrgsRepository } from "../repositories/orgsRepository";
import { HateaosLinkService, LinkTypes } from "./hateaosLinkService";
import { PaginationService } from "./paginationService";
import { PeopleRepository } from "../repositories/peopleRepository";
import moment from "moment";
import { OrgHierarchy } from './utils/orgHierarchy';
import { InvoiceUtils } from '../../../lib/util/invoiceUtils';
import { BillingUtils } from '../../../lib/util/billingUtils';
import { ResourceNotFoundProblem } from "../exceptions/resourceNotFoundProblem";
import _ from "../../../lib/util/underscore";

export class InvoicesApiService {
    constructor() {
        this.InvoicesRepo = new InvoicesRepository();
        this.orgHierarchy = new OrgHierarchy();
        this.OrgsRepo = new OrgsRepository();
        this.PeopleRepo = new PeopleRepository();
    }

    /**
     * Get the invoices.
     *
     * @param req
     * @param userOrgId The user's org id
     * @param { PaginationService } pagination
     */
    async getInvoices(req, userOrgId, pagination) {

        const queryParam = await this.getBaseQuery(req, userOrgId);
        let options = {};

        if (pagination) {
            options = { limit: pagination.limit, skip: pagination.offset }
        }

        const records = await this.InvoicesRepo.find(queryParam, options);
        const convertedISODateRecords = await this.convertResponseFormat(records);
        return this.generateOutput(convertedISODateRecords);
    }

    /**
     * Get the invoices count.
     *
     * @param req
     * @param userOrgId The user's org id
     */
    async getInvoicesCount(req, userOrgId) {
        return await this.InvoicesRepo.count(await this.getBaseQuery(req, userOrgId), { readPreference: "secondaryPreferred" });
    }

    /**
     * Create some property values.
     *
     * @param ArrayOfResponseObj
     * @returns {*}
     */
    async convertResponseFormat(ArrayOfResponseObj) {
        const convertedArray = await Promise.all( ArrayOfResponseObj.map(async item => {
            const orgReso = await this.OrgsRepo.findById(item.orgId)
            const createdByReso = await this.PeopleRepo.findById(item.createdBy)
            const personReso = await this.PeopleRepo.findById(item.personId)
            const payerSources = orgReso?.availablePayerSources(true) ?? [];

            let openPayerName = null;
            _.each(item.openPayerAmounts, (opaAmount, opaSource) => {
                const source = _.find(payerSources, (ps) => ps.type == opaSource);
                const paidAmount = _.reduce(
                    item.credits,
                    (memo, credit) => {
                        if (credit.creditReason === 'reimbursable' && credit.creditPayerSource == opaSource) {
                            return memo + credit.amount;
                        }
                        return memo;
                    },
                    0
                );
                const openAmount = item.openAmountForPayer(opaSource);
                if (source && openAmount > 0) {
                    openPayerName = source.description;
                }
            });

            return {
                ...item,
                createdAt: new moment(item.createdAt).toISOString(),
                org: orgReso,
                orgId: item.orgId ? HateaosLinkService.createLink(orgReso, 'org') : "",
                createdBy: (item.createdBy !== 'SYSTEM') ? HateaosLinkService.createLink(createdByReso, 'person') : "SYSTEM",
                personId: item.personId ? HateaosLinkService.createLink(personReso, 'person') : "",
                dueDate: new moment(item.dueDate).toISOString(),
                batchStamp: new moment(item.batchStamp).toISOString(),
                voidedAt: new moment(item.voidedAt).toISOString(),
                credits: (this.checkKeyExist(item, 'credits')) ? await Promise.all( item.credits.map(async credit => ({
                    ...credit,
                    createdAt: new moment(credit.createdAt).toISOString(),
                    voidedAt: new moment(credit.voidedAt).toISOString(),
                    paidBy: credit.paidBy ? HateaosLinkService.createLink(await this.PeopleRepo.findById(credit.paidBy), 'person') : "",
                }))) : [],
                lineItems: 
                    (this.checkKeyExist(item, 'lineItems')) ? 
                        await Promise.all( item.lineItems.map(async lineItem => {
                            if (lineItem.type === 'plan') {
                            const periodStartDate = lineItem.periodStartDate ?
                                new moment(lineItem.periodStartDate).toISOString() :
                                new moment(BillingUtils.getPeriodByEffectiveDate(item.dueDate, lineItem.frequency, orgReso, personReso).start, 'MM/DD/YYYY').toISOString();
                            return {
                                ...lineItem,
                                periodStartDate: periodStartDate,
                                plan: await this.fetchPlanDetailsForHateosLink(item.orgId, lineItem.enrolledPlan._id)
                            }
                            } else {
                                return lineItem
                            }
                        })) : 
                        [],
                openPayerName
            };
        }));

        return convertedArray;
    }

    /**
     * Check if key exists in object.
     *
     * @param object
     * @param key
     * @returns {boolean}
     */
    checkKeyExist(object, key) {
        if (key in object) {
            return true
        }
        else {
            return false;
        }
    }

    /**
     * Converts 'MM/DD/YYYY' date strings to Unix timestamps.
     *
     * @param startDate
     * @param endDate
     * @returns {{endTimestamp: number, startTimestamp: number}}
     */
    convertDatesToTimestamps(startDate, endDate) {
        const startTimestamp = parseInt(moment(startDate).format("x"));
        const endTimestamp = parseInt(moment(endDate).format("x"));
        return {
            startTimestamp,
            endTimestamp
        };
    }

    /**
     * Fetch details for hateos link.
     *
     * @param orgId
     * @param planId
     * @returns {*|null}
     */
    async fetchPlanDetailsForHateosLink(orgId, planId) {
        let filterBillingPlan;
        let QueryObj;
        if (orgId) {
            QueryObj = { '_id': orgId };
        }
        QueryObj['billing.plansAndItems._id'] = planId;
        let record = await this.OrgsRepo.findById(QueryObj, {});
        filterBillingPlan = record ? record?.billing?.plansAndItems?.filter((item) => (item._id === planId)) : [];
        let filterbillingObject;
        if (filterBillingPlan.length) {
            filterBillingPlan[0]['orgId'] = orgId;
            filterbillingObject = filterBillingPlan[0];
        }

        let hateoasObj = HateaosLinkService.createLink(filterbillingObject, 'orgplan');
        return hateoasObj;
    }

    /**
     * Generate output for an Invoice document.
     *
     * @param inputObject
     * @returns {*}
     */
    generateOutput(inputObject) {
        const output = inputObject.map(item => ({
            invoice_id: item._id,
            original_amount: item.originalAmount,
            open_amount: item.openAmount,
            discount_amount: item.discountAmount,
            due_date: item.dueDate,
            invoice_number: item.invoiceNumber,
            org: item.orgId,
            created_at: item.createdAt,
            created_by: item.createdBy,
            invoiced_person: item.personId,
            line_items: item.lineItems.map(lineItem => ({
                description: lineItem.description || '',
                type: lineItem.type || null,
                ledger_code: InvoiceUtils.getMappedLedgerAccountForLineItem(item.org, lineItem)?.accountName ?? null,
                amount: lineItem.amount || 0,
                frequency: lineItem.frequency || null,
                category: lineItem.category || null,
                plan: lineItem.plan || {},
                period_start_date: lineItem.periodStartDate || null
            })),
            credits: item.credits?.map(credit => ({
                type: credit.type || null,
                ledger_code: InvoiceUtils.getMappedLedgerAccountForCredit(item.org, credit, item)?.accountName ?? null,
                payment_type: credit.payment_type || "Payment",
                amount: credit.amount || 0,
                paid_by: credit.paidBy
            })) ?? [],
            responsible_party: item.openPayerName ?? 'Family',
            payment_status: this.getInvoicePaymentStatus(item),
            payment_processor_ids: item.paymentTransactions?.map(transaction => transaction?.pspReference) ?? []
        }));
        return output;
    }

    /**
     * Get the base invoices query.
     *
     * @param req The request object.
     * @param userOrgId
     * @returns {{}}
     * @private
     */
    async getBaseQuery(req, userOrgId) {
        let queryParam = {};
        let options = {};
        const startDate = req.query.invoice_date_start;
        const endDate = req.query.invoice_date_end;
        const invoiceDueStartDate = req.query.invoice_due_start;
        const invoiceDueEndDate = req.query.invoice_due_end;
        const invoicePaidStartDate = req.query.invoice_paid_start;
        const invoicePaidEndDate = req.query.invoice_paid_end;
        const invoiceVoidedStartDate = req.query.invoice_voided_start;
        const invoiceVoidedEndDate = req.query.invoice_voided_end;
        const invoicePeriodStartDate = req.query.period_date_start;
        const invoicePeriodEndDate = req.query.period_date_end;

        const orgIds = await this.orgHierarchy.findOrgHierarchy(req.query.org_id, userOrgId);
        queryParam.orgId = { $in: orgIds };

        if (startDate || endDate) {
            queryParam.invoiceDate = {
                $gte: moment(startDate).format('M/DD/YYYY'),
                $lte: moment(endDate).format('M/DD/YYYY')
            }
        }
        if (invoiceDueStartDate && invoiceDueEndDate) {
            let unixStampConvertedDueDate = this.convertDatesToTimestamps(invoiceDueStartDate, invoiceDueEndDate);
            queryParam.dueDate = {
                $gte: unixStampConvertedDueDate.startTimestamp,
                $lte: unixStampConvertedDueDate.endTimestamp
            }
        }
        if (invoicePaidStartDate && invoicePaidEndDate) {
            let unixStampConvertedPaidDate = this.convertDatesToTimestamps(invoicePaidStartDate, invoicePaidEndDate);
            queryParam['credits.createdAt'] = {

                $gte: unixStampConvertedPaidDate.startTimestamp,
                $lte: unixStampConvertedPaidDate.endTimestamp

            }

        }
        if (invoiceVoidedStartDate && invoiceVoidedEndDate) {
            let unixStampConvertedVoidedDate = this.convertDatesToTimestamps(invoiceVoidedStartDate, invoiceVoidedEndDate);
            queryParam.voidedAt = {
                $gte: unixStampConvertedVoidedDate.startTimestamp,
                $lte: unixStampConvertedVoidedDate.endTimestamp
            }
        }
        if (invoicePeriodStartDate && invoicePeriodEndDate) {
            let unixStampConvertedPeriodDate = this.convertDatesToTimestamps(invoicePeriodStartDate, invoicePeriodEndDate);
            queryParam['lineItems.createdAt'] = {
                $gte: unixStampConvertedPeriodDate.startTimestamp,
                $lte: unixStampConvertedPeriodDate.endTimestamp
            }
        }

        return queryParam;
    }

    /**
     * Get the payment status of the invoice.
     *
     * @param invoice
     * @returns { string | InvoicePaymentStatus }
     * @private
     */
    getInvoicePaymentStatus(invoice) {
        if (invoice.voided) {
            return InvoicePaymentStatus.VOIDED;
        } else if (invoice.openAmount === 0) {
            return InvoicePaymentStatus.PAID;
        } else {
            return InvoicePaymentStatus.UNPAID;
        }
    }

    /**
     * Get a single invoice object with specified fields.
     * @param id { string }
     * @returns {*}
    */
    async getInvoiceById(id) {
        const fieldsToRetrieve = {
            originalAmount: 1,
            openAmount: 1,
            discountAmount: 1,
            dueDate: 1,
            invoiceNumber: 1,
            orgId: 1,
            createdBy: 1,
            personId: 1,
            lineItems: 1,
            credits: 1
        };
        const result = await this.InvoicesRepo.getObjectByIdWithFields(id, fieldsToRetrieve);
        if (!result) {
            throw new ResourceNotFoundProblem('Invoice not found');
        }
        return result
    }

    /**
     * Generate output for an invoice object with snake case keys and required hateaos links.
     * @param item { object }
     * @returns {*}
     */
    async formatOne(item) {
        let outputObj = {}
        outputObj.invoice_id = item._id
        outputObj.original_amount = parseFloat(item.originalAmount)
        outputObj.open_amount = parseFloat(item.openAmount)
        outputObj.discount_amount = parseFloat(item.discountAmount)
        outputObj.due_date = new moment(item.dueDate).toISOString()
        outputObj.invoice_number = item.invoiceNumber
        
        let orgObj = await this.OrgsRepo.findById(item?.orgId)
        outputObj.org = HateaosLinkService.createLink(orgObj, 'org')

        let invoicedPersonObj = await this.PeopleRepo.findById(item.personId)
        outputObj.invoiced_person = HateaosLinkService.createLink(invoicedPersonObj, 'person')
        
        if (item.createdBy !== 'SYSTEM') {
            let createdByObj = await this.PeopleRepo.findById(item.createdBy)
            outputObj.created_by = HateaosLinkService.createLink(createdByObj, 'person')
        } else {
            outputObj.created_by = 'SYSTEM'
        }
       
        outputObj.line_items = []
        if (item.lineItems) {
            outputObj.line_items = await Promise.all( item.lineItems.map(async lineItem => {
                const newObj = {
                    description: lineItem.description || '',
                    type: lineItem.type || null,
                    amount: parseFloat(lineItem.amount) || 0,
                    frequency: lineItem.frequency || null,
                    category: lineItem.category || null,
                }
                newObj.period_start_date = lineItem.periodStartDate ?
                        new moment(lineItem.periodStartDate).toISOString() :
                        new moment(BillingUtils.getPeriodByEffectiveDate(item?.dueDate, lineItem?.frequency, orgObj, invoicedPersonObj).start, 'MM/DD/YYYY').toISOString();
                newObj.plan = await this.fetchPlanDetailsForHateosLink(item?.orgId, lineItem?.enrolledPlan?._id)
                return newObj
            }))
        }

        outputObj.credits = []
        if (item.credits) {
            let paidByPersonIds = item.credits.filter(credit => credit.paidBy).map(credit => credit.paidBy)
            paidByPersonIds = [...new Set(paidByPersonIds)]
            const query = {
                _id: { $in: paidByPersonIds }
            }
            const paidByPersonArray = await this.PeopleRepo.find(query)
            outputObj.credits = item.credits?.map(credit => ({
                type: credit.type || null,
                payment_type: credit.payment_type || "Payment",
                amount: parseFloat(credit.amount) || 0,
                paid_by: HateaosLinkService.createLink(paidByPersonArray.find(paidByPerson => paidByPerson._id === credit.paidBy), 'person')
            }))
        }
        return outputObj
    }
}

export const InvoicePaymentStatus = {
    PAID: 'Paid',
    UNPAID: 'Unpaid',
    VOIDED: 'Voided'
};
Object.freeze(InvoicePaymentStatus);