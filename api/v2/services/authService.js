import { JwtService } from "./jwtService";
import { UsersRepository } from "../repositories/usersRepository";
import { AWSCognitoService } from "./cognitoService";
import { OrgsRepository } from "../repositories/orgsRepository";
import { UnauthorizedProblem } from "../exceptions/unauthorizedProblem";
import { PeopleRepository } from "../repositories/peopleRepository";
import { HateaosLinkService, LinkTypes } from "./hateaosLinkService";
import { Meteor } from 'meteor/meteor';
import { Accounts } from 'meteor/accounts-base';
const bcrypt = require('bcrypt');
import crypto from 'crypto';

export class AuthService {
    constructor() {
        this.jwtService = new JwtService()
        this.usersRepo = new UsersRepository();
        this.orgsRepo = new OrgsRepository();
        this.peopleRepo = new PeopleRepository();
        this.cognitoService = new AWSCognitoService();
    }

    /**
     * @param username { string }
     * @param password { string }
     * @returns {Promise<User>}
     */
    async checkLogin(username, password) {
        const user = await this.getValidUserByEmail(username);

        let valid = false;
        if (Meteor.settings.apiBypassAws) {
            valid = await Accounts._checkPasswordAsync(user, password);
        } else {
            valid = await this.cognitoService.checkCredentials(username, password);
        }
        if (!valid) {
            throw new UnauthorizedProblem('Invalid credentials');
        }

        return user;
    }

    /**
     * @param refreshToken { string }
     * @returns {Promise<User>}
     */
    async checkRefresh(refreshToken) {
        const storedToken = await this.jwtService.getRefreshToken(refreshToken);
        if (!storedToken) {
            throw new UnauthorizedProblem('Invalid refresh token');
        }
        return await this.getValidUserById(storedToken.userId);
    }


    /**
     * @param req { Request }
     * @returns {Promise<{ token: string, refresh_token: string, links: Array}>}
     */
    async login(req) {
        const { username, password } = req?.body;
        const user = await this.checkLogin(username, password);
        return await this.getTokenResponse(user);
    }

    /**
     * @param req { Request }
     * @returns {Promise<{ token: string, refresh_token: string, links: Array}>}
     */
    async refresh(req) {
        const refreshToken = req?.body?.refresh_token;
        if (!refreshToken) {
            throw new UnauthorizedProblem('Invalid refresh token');
        }

        const user = await this.checkRefresh(refreshToken);
        return await this.getTokenResponse(user);
    }

    /**
     * @private
     * @param user
     * @returns {Promise<{refresh_token: string, links: Array, token: string}>}
     */
    async getTokenResponse(user) {
        const token = await this.jwtService.getJwt(user);
        if (!token) {
            throw new UnauthorizedProblem('Could not generate token');
        }
        const refreshToken = await this.jwtService.createRefreshToken(user._id);
        return {
            token,
            refresh_token: refreshToken,
            links: HateaosLinkService.createLink(user, LinkTypes.USER ).links
        }
    }

    /**
     * @param req { Request }
     * @returns {Promise<User|null>}
     */
    async authenticateRequest(req) {
        let token = req.headers['x-auth-token'];

        if (!token) {
            const authorizationHeader = req.headers.authorization;
            if (authorizationHeader) {
                const authorizationHeaderParts = authorizationHeader.split(' ');
                if (authorizationHeaderParts.length === 2 && authorizationHeaderParts[0] === 'Bearer') {
                    token = authorizationHeaderParts[1];
                }
            }
        }

        if (!token) {
            throw new UnauthorizedProblem('Missing authentication token');
        }

        const claims = await this.jwtService.decodeJwt(token);
        if (claims && claims.username) {
            return await this.getValidUserByEmail(claims.username);
        }

        const hashedToken = crypto.createHash('sha256').update(token).digest('base64');

        const user = await Meteor.users.findOneAsync({
            'services.resume.loginTokens.hashedToken': hashedToken
        });

        if (!user) {
            throw new UnauthorizedProblem("Invalid authentication token");
        }

        return user;
    }

    /**
     * @param email { string }
     * @returns {User}
     */
    async getValidUserByEmail(email) {
        const user = await this.usersRepo.findUserByEmail(email);
        if (!user) {
            throw new UnauthorizedProblem('User not found');
        }
        await this.checkValidUser(user);
        return user;
    }

    /**
     * @param id { string }
     * @returns {User}
     */
    async getValidUserById(id) {
        const user = await this.usersRepo.findById(id);
        if (!user) {
            throw new UnauthorizedProblem('User not found');
        }
        await this.checkValidUser(user);
        return user;
    }

    /**
     * @private
     * @param user {User}
     */
    async checkValidUser(user) {
        const person = await this.peopleRepo.findById(user.personId);
        if (!person || person.inActive) {
            throw new UnauthorizedProblem('Active person not found');
        }

        const org = await this.orgsRepo.findById(person.orgId);
        if (!org || org.inActive) {
            throw new UnauthorizedProblem('Active org not found')
        }
    }

    /**
     * Checks if the user has the required role.
     * @param {string} role - The required role.
     * @param {object} user - The user object.
     * @returns {boolean} True if the user has the required role, otherwise false.
     */
    async isGranted(role, user) {
        const person = await this.peopleRepo.findById(user.personId);

        if (!person) {
            return false;
        }

        return RoleTypes[person.type] >= AccessLevelTypes[role];
    }
}

/**
 * The types of roles with corresponding access levels.
 */
export const RoleTypes = {
    'admin': 3,
    'staff': 2,
    'family': 1,
    'serviceAccount': 1
}
Object.freeze(RoleTypes);

/**
 * The access level types with corresponding levels of access.
 */
export const AccessLevelTypes = {
    ROLE_DELETE: 3,
    ROLE_WRITE: 2,
    ROLE_READ: 1,
}
Object.freeze(AccessLevelTypes);

/**
 * The access level type names.
 */
export const AccessLevelTypeNames = {
    DELETE: 'ROLE_DELETE',
    WRITE: 'ROLE_WRITE',
    READ: 'ROLE_READ'
}
Object.freeze(AccessLevelTypeNames);
