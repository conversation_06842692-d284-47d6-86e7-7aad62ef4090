export class PaginationService {
    // Since we are trying for consistency between <PERSON><PERSON> and <PERSON><PERSON> I modeled this to be exactly like the Enroll Pagination.php Class.

    /**
     * Create a Pagination instance.
     * @param {number} [limit=100] - The limit for the pagination.
     * @param {number} [offset=0] - The offset for the pagination.
     */
    constructor(limit = 100, offset = 0) {
        this.limit = limit;
        this.offset = offset;
    }

    /**
     * Get the current limit value.
     * @returns {number} The current limit value.
     */
    getLimit() {
        return this.limit;
    }

    /**
     * Set the limit for pagination.
     * @param {number} limit - The limit to be set.
     * @returns {PaginationService} The updated Pagination object.
     */
    setLimit(limit) {
        this.limit = limit > 100 ? 100 : limit;
        return this;
    }

    /**
     * Get the current offset value.
     * @returns {number} The current offset value.
     */
    getOffset() {
        return this.offset;
    }

    /**
     * Set the offset for pagination.
     * @param {number} offset - The offset to be set.
     * @returns {PaginationService} The updated Pagination object.
     */
    setOffset(offset) {
        this.offset = offset;
        return this;
    }

    /**
     * Create a Pagination object from the request.
     * @param {object} request - The request object.
     * @returns {PaginationService|null} A Pagination object created from the request, or null if no pagination is requested.
     * @throws {Error} Throws an error if the request parameters are invalid.
     */
    static createFromRequest(request) {
        if (request && request.query.no_pagination) {
            // Do not do pagination.
            return null;
        }

        if (!request) {
            // Return initiated object with defaults.
            return new this();
        }

        let limit = parseInt(request.query.limit) || 100;
        const offset = parseInt( request.query.offset) || 0;

        if (isNaN(limit) || limit < 0) {
            throw new Error("`limit` is not a valid value.");
        }

        if (isNaN(offset) || offset < 0) {
            throw new Error("`offset` is not a valid value.");
        }

        limit = Math.min(limit, 100); // Limit cannot exceed 100.

        return new this(limit, offset);
    }
}