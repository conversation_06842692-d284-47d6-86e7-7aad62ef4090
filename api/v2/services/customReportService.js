import { Redshift } from './redshiftDbService.js';
import { Log } from '../../../lib/util/log';
//import moment from 'moment';
import moment from 'moment-timezone';
import { defaultImmunizationTypes } from '../../../lib/constants/immunizationTypeConstant';
import { FilterCustomReportData } from './utils/filterCustomReportData.js';
import _ from '../../../lib/util/underscore.js';

const MS_DAY = 24 * 60 * 60 * 1000;
const API_VERSION = '2.8.5';
const monthOrder = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
const shortMonthOrder = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const MEMBERS_PAGE_SIZE = 30000;
const SELECT_PAGE_SIZE = 30000;
const DATE_FIELDS=['checkin_date','checkout_date','birthday','cancellation_date','createdDate'];
const NUM_FIELDS=['elapsed_time','variance_minutes','variance_hours','hours_worked','pay_rate'];
const PEOPLE_DATE_FIELDS=['waitlistAddedDate','enrollmentDate','registrationSubmissionDate'];
const dataCacheCount = {};

export class CustomReportService {
    constructor(report) {
        this.report = report;
    }

    static org;
    static fieldCache = {};
    static dataCache = {};
    static dataCacheCount = {};
    static aggsCache = {};
    
    static timezone = '';
    /**
     * 
     * @param {string} index - table or report name of flexmonster
     * @returns {array} - returns data in flexmonster format  
     */
    static async getReport(index) {
        try {
            const result = this.getData(index);
            return result;
        } catch (e) {
            Log.info('Error: attendance report', e.message);
            throw e;
        }
    }
   
    static getTimezone() {
        if(this.timezone==null || this.timezone.trim() == ''){
            this.timezone = 'America/New_York';
        }
        return this.timezone
    }

    /**
     * 
     * @param {string} index - table or report name of flexmonster
     * @returns {object} - return fields and datatypes ofr report
     */
    static async getFields(index) {
        if (!this.fieldCache[index]) {
            try {
                const result = await Redshift.executeQuery(`SELECT flex_label,column_name,flex_datatype FROM manage.redshiftFlexMap WHERE table_name = '${index}' ORDER BY flex_order ASC`);
                const outputField = this.convertLaborField(result);
                this.fieldCache[index] = outputField;
            } catch (e) {
                Log.info('Error: attendance report', e.message);
                throw e;
            }
        }
        return this.fieldCache[index];
    }
    /**
     * 
     * @param {array} fetchedData - data of report in array of object format
     * @returns 
     */
    static convertLaborField(fetchedData) {
        const output = {
            fields: [],
            aggregations: {
                any: ['count', 'distinctcount'],
                date: ['count', 'distinctcount', 'min', 'max'],
                number: ['sum', 'average', 'count', 'distinctcount', 'min', 'max']
            },
            filters : {
                any: {
                    members: true,
                    query: true,
                    valueQuery: true
                }
            },
            sorted: false
        };
        fetchedData.forEach(record => {
            let fieldType;
            if (record.flex_datatype === 'String') {
                fieldType = 'string';
            } else if (record.flex_datatype === 'Number') {
                fieldType = 'number';
            } else if (record.flex_datatype === 'Date') {
                fieldType = 'date';
            }
            output.fields.push({
                uniqueName: record.column_name,
                type: fieldType,
                caption: record.flex_label,
            });
        });
        return output;
    }
    /**
     * To get the API version of flexmonster
     * @returns {object} - return API version
     */
    static async handshake() {
        try {
            return ({ version: API_VERSION });
        } catch (err) {
            this.handleError(err, res);
        }
    }

    /**
     * get the alternate value for null value based on data type
     * @param {String} fieldName - unique name of field 
     * @returns - returns alternate value for null 
     */
    static getNullAltValue(fieldName) {
        let altValue = '\'(blank)\'';
        if(NUM_FIELDS.includes(fieldName)) {
            altValue = 0
        }
        if(DATE_FIELDS.includes(fieldName)) {
            altValue = '\'1970-01-01\''
        }
        return altValue;
    }

    /**
     * Gets field members.
     * @param {string} index index name
     * @param {string} field field object
     * @param {number} page page number to load
     * @returns {object} - get members data based on field
     */
    static async getMembers(org, index, field, page) {
        const fieldName = field.uniqueName;
        const fields = await this.getFields(index);
        const fieldType = this.getFieldType(fields, fieldName);
        const altValue = this.getNullAltValue(fieldName);

        let distinctField = ` DISTINCT coalesce("${fieldName}",${altValue}) as "${fieldName}" `;
        //let distinctField = ` DISTINCT coalesce("${fieldName}",'${altValue}') `;
        let data = [];
        let totalRowCount = 1;
        if(index ==='people') {
            data = await this.getPeopleData(org,index,fieldName);
            totalRowCount = data.length;
        }else{
            data = await this.getData(org,index,distinctField,page,MEMBERS_PAGE_SIZE);
            // This will returns the total unique count 
            totalRowCount = await this.getDataCount(org,index,fieldName);
        }

        const output = {
            members: []
        };
        const members = [...new Set(data.map(item => item[fieldName]))];
        /*if (this.checkMonths(members)) { // custom sort for months
            members.sort(this.monthCompare);
            output.sorted = true;
        }*/
        page = isNaN(page) ? 0 : page;
        const pageTotal = Math.ceil(totalRowCount / MEMBERS_PAGE_SIZE);
        if (pageTotal > 1) {
            output.page = page;
            output.pageTotal = pageTotal;
        }
        /*
        const from = page * MEMBERS_PAGE_SIZE;
        const size = Math.min(totalRowCount, from + MEMBERS_PAGE_SIZE);
        for (let i = from; i < size; i++) {
            output.members.push(this.createMember(members[i], fieldType));
        }
        */
        for (let i = 0; i < members.length; i++) {
            output.members.push(this.createMember(members[i], fieldType));
        }
        return output;
    }

    /**
     * Create members , returns member values filtered
     * @param {*} value 
     * @param {*} fieldType 
     * @returns field value as string or object
     */
    static createMember(value, fieldType) {
        if (value || value === 0 || value === false || value === NaN) {
            return {
                value: value
            };
        } else {
            return {
                value: ''
            };
        }
    }

    /**
     * Checks whether the members are months.
     * @param {Array} members array of a field's members
     * @returns true / false based on condition
     */
    static checkMonths(members) {
        if (typeof members === 'undefined' || members === null || typeof members.length === 'undefined' || members.length === null) return false;
        return (monthOrder.indexOf(members[0]) != -1 || (shortMonthOrder.indexOf(members[0]) != -1))
    }

    /**
     * calculate order for month between two month
     * @param {string} a - month name 
     * @param {string} b - month name
     * @returns - order for month between two month
     */
    static monthCompare(a, b) {
        if (monthOrder.indexOf(a) > -1 || monthOrder.indexOf(b) > -1) {
            return monthOrder.indexOf(a) - monthOrder.indexOf(b);
        }
        return shortMonthOrder.indexOf(a) - shortMonthOrder.indexOf(b);
    }

    /**
     * Gets select data.
     * @param {string} index index name
     * @param {object} query query object
     * @param {number} page page number
     * @returns - array of data pulled from db
     */
    static async getSelectResult(org,index, query, page) {
        let fields = '*';
        let timestamp = this.getTimezone();
        if (query.fields && index !== 'people') {
            fields = query.fields.map(function (f) {
                return `"${f.uniqueName}"`;
            });
            fields = fields.join(',');
        }
        if(query.aggs.by?.rows){
            fields = query.aggs.by.rows.map(function (f) {
                return `${f.uniqueName}`;
            });
            fields = '"' +fields.join('","')+'"';
        }
        const output = {};
        let data = [];
        let totalRowCount = 1;
        
        if (query.aggs && query.aggs.values) {
            output.aggs = [];
            if (query.aggs.by) {
                const rows = query.aggs.by.rows || [];
                const cols = query.aggs.by.cols || [];
                // group by row e.g by name and columns are (elapsed_time, varience_hours etc..)
                // push group by row e.g. group by name
                // add aggs to col with func
                output.aggs = await this.getAggsData(org,index,query.aggs.values,rows,cols,query.filter);
            }
        }

        if (query.fields) {
            if(index === 'people'){
                data = await this.getPeopleData(org,index,'*');
                if (query?.filter) {
                    const fields = await this.getFields(index);
                    for (const filter of query.filter) {
                        filter['field'].type = this.getFieldType(fields, filter['field'].uniqueName);
                    }
                    data = this.filterData(data, query.filter);
                }
                totalRowCount = data.length;
            }else{
                data = await this.getData(org,index,fields,page,SELECT_PAGE_SIZE,query.filter);
                totalRowCount = await this.getDataCount(org,index,'*',query.filter);
            }
            output.fields = query.fields.map(function (f) {
                return {
                    'uniqueName': f.uniqueName
                };
            });
            output.hits = [];
            const limit = isNaN(query.limit) ? data.length : Math.min(query.limit, data.length);
            for (let i = 0; i < limit; i++) {
                const row = query.fields.map(f => {
                    const value = data[i][f.uniqueName];
                    if(NUM_FIELDS.includes(f.uniqueName)){
                        return Number(value);
                    }
                    if (value === undefined || value === null) {
                        return '';
                    }
                    return value;
                });
                output.hits.push(row);
            }
            page = isNaN(page) ? 0 : page;
            const pageTotal = Math.ceil(totalRowCount / SELECT_PAGE_SIZE);
            if (pageTotal > 1) {
                output.page = page;
                output.pageTotal = pageTotal;
            }
        }
        return output;
    }

    /**
     * Filters data.
     * @param {Array} data input data
     * @param {Array} filters filters to apply
     * @returns - filtered the data based on filters supplied
     */
    static filterData(data, filters) {
        if (filters.length === 0) {
            return data;
        }
        return data.filter(d => {
            for (const filter of filters) {
                const check = this.checkFilter(d, filter);
                if (!check) {
                    return false;
                }
            }
            return true;
        });
    }

    /**
     * Checks whether the data item meets the filter query.
     * @param {object} item data item
     * @param {object} filter filter object
     * @returns boolean, true if filter present or false
     */
    static checkFilter(item, filter) {
        let check = true;
        const fieldName = filter['field'].uniqueName;
        let value = item[fieldName];
        if (value === undefined || value === null) {
            value = '';
        }
        if (filter['include']) {
            check = filter['include'].some(include => value?.toString() === (include?.member)?.toString());
        } else if (filter['exclude']) {
            check = !filter['exclude'].some(exclude => value === exclude.member);
        } else if (filter['query']) {
            const query = filter['query'];
            switch (filter['field'].type) {
                case 'date':
                    check = this.checkDateFilterQuery(value, query);
                    break;
                case 'number':
                    check = this.checkNumberFilterQuery(value, query);
                    break;
                default:
                    check = this.checkStringFilterQuery(value, query);
            }
        }
        return check;
    }

    /**
     * Checks whether the timestamp meets the query condition.
     * @param {number} value Unix timestamp
     * @param {object} query query object
     * @returns boolean 
     */
    static checkDateFilterQuery(value, query) {
        let d;
        if (query['equal'] !== undefined) {
            d = query['equal'];
            return value - d >= 0 && value - d < MS_DAY;
        }
        if (query['not_equal'] !== undefined) {
            d = query['not_equal'];
            return value < d || value >= d + MS_DAY;
        }
        if (query['after'] !== undefined) {
            d = query['after'];
            return value >= d + MS_DAY;
        }
        if (query['after_equal'] !== undefined) {
            d = query['after_equal'];
            return value >= d;
        }
        if (query['before'] !== undefined) {
            d = query['before'];
            return value < d;
        }
        if (query['before_equal'] !== undefined) {
            d = query['before_equal'];
            return value < d + MS_DAY;
        }
        if (query['between'] !== undefined) {
            const [d1, d2] = query['between'];
            return value >= d1 && value <= d2;
        }
        if (query['not_between'] !== undefined) {
            const [d1, d2] = query['not_between'];
            return value < d1 || value > d2;
        }
        return false;
    }

    /**
     * Checks whether the string value meets the query condition.
     * @param {string} value string value
     * @param {object} query query object
     * @returns boolean based on the query check
     */
    static checkStringFilterQuery(value, query) {
        value = String(value).toLowerCase();
        if (query['equal'] !== undefined) {
            return value === query['equal'].toLowerCase();
        }
        if (query['not_equal'] !== undefined) {
            return value !== query['not_equal'].toLowerCase();
        }
        if (query['begin'] !== undefined) {
            return value.startsWith(query['begin'].toLowerCase());
        }
        if (query['not_begin'] !== undefined) {
            return !value.startsWith(query['not_begin'].toLowerCase());
        }
        if (query['end'] !== undefined) {
            return value.endsWith(query['end'].toLowerCase());
        }
        if (query['not_end'] !== undefined) {
            return !value.endsWith(query['not_end'].toLowerCase());
        }
        if (query['contain'] !== undefined) {
            return value.includes(query['contain'].toLowerCase());
        }
        if (query['not_contain'] !== undefined) {
            return !value.includes(query['not_contain'].toLowerCase());
        }
        // Number comparisons for string fields will not be logically correct but kept for completeness
        if (query['greater'] !== undefined) {
            return value > query['greater'].toLowerCase();
        }
        if (query['greater_equal'] !== undefined) {
            return value >= query['greater_equal'].toLowerCase();
        }
        if (query['less'] !== undefined) {
            return value < query['less'].toLowerCase();
        }
        if (query['less_equal'] !== undefined) {
            return value <= query['less_equal'].toLowerCase();
        }
        if (query['between'] !== undefined) {
            const v1 = query['between'][0].toLowerCase();
            const v2 = query['between'][1].toLowerCase();
            return value >= v1 && value <= v2;
        }
        if (query['not_between'] !== undefined) {
            const v1 = query['not_between'][0].toLowerCase();
            const v2 = query['not_between'][1].toLowerCase();
            return value < v1 || value > v2;
        }
        return false;
    }

    /**
     * Checks whether the numeric value meets the query condition.
     * @param {number} value numeric value
     * @param {object} query query object
     * @returns boolean based on the number query filter in value
     */
    static checkNumberFilterQuery(value, query) {
        if (query['equal'] !== undefined) {
            return value === query['equal'];
        }
        if (query['not_equal'] !== undefined) {
            return value !== query['not_equal'];
        }
        if (query['greater'] !== undefined) {
            return value > query['greater'];
        }
        if (query['greater_equal'] !== undefined) {
            return value >= query['greater_equal'];
        }
        if (query['less'] !== undefined) {
            return value < query['less'];
        }
        if (query['less_equal'] !== undefined) {
            return value <= query['less_equal'];
        }
        if (query['between'] !== undefined) {
            const [v1, v2] = query['between'];
            return value >= v1 && value <= v2;
        }
        if (query['not_between'] !== undefined) {
            const [v1, v2] = query['not_between'];
            return value < v1 || value > v2;
        }
        return false;
    }
    /**
     * group by object based on the key
     * @param {*} array 
     * @param {*} keyName 
     * @returns reduced array of object
     */
    static groupBy = (array, keyName) =>
        array.reduce((objectsByKeyValue, obj) => {
            let valueObject = obj[keyName];
            if (valueObject === undefined || valueObject === null) {
                valueObject = "";
            }
            objectsByKeyValue[valueObject] = objectsByKeyValue[valueObject] || {};
            objectsByKeyValue[valueObject].key = valueObject;
            objectsByKeyValue[valueObject].values = objectsByKeyValue[valueObject].values || [];
            objectsByKeyValue[valueObject].values.push(obj);
            return objectsByKeyValue;
        }, {});

    /**
     * Groups data by fields and calculates numeric data. Works recursively.
     * @param {object[]} data input data
     * @param {string[]} fields all fields to group by
     * @param {string[]} cols fields in columns to group by
     * @param {object[]} values values to calculate
     * @param {object[]} output output response
     * @param {object} keys key-value pairs that describes specific tuple
     * @returns array data by fields and calculates numeric data
     */
    static calcByFields(data, fields, cols, values, output, keys, org, index) {
        if (fields.length < 1) {
            return;
        }
        const fieldName = fields[0].uniqueName;
        const subfields = fields.slice(1);
        const groups = this.groupBy(data, fieldName);
        for (const index in groups) {
            const key = groups[index].key;
            const subdata = groups[index].values;
            const item = this.calcValues(subdata, values, org, index);
            item.keys = keys ? _.clone(keys) : {};
            item.keys[fieldName] = key;
            output.push(item);
            this.calcByFields(subdata, subfields, cols, values, output, item.keys, org, index);
        }

        if (cols && cols.length > 0 && fields.length > cols.length) {
            const colFieldName = cols[0].uniqueName;
            const subCols = cols.slice(1);
            const colGroups = this.groupBy(data, colFieldName);
            for (const index in colGroups) {
                const key = colGroups[index].key;
                const subdata = colGroups[index].values;
                const item = this.calcValues(subdata, values, org, index);
                item.keys = keys ? _.clone(keys) : {};
                item.keys[colFieldName] = key;
                output.push(item);
                this.calcByFields(subdata, subCols, null, values, output, item.keys);
            }
        }
    }

    /**
     * Calculates aggregated values.
     * @param {object[]} data input data
     * @param {object} values values to calculate
     * @returns object - return calculates aggregated values
     */
    static calcValues(data, values, org, index) {
        const output = {
            values: {}
        };
        // cached the output value so that it can reuse on other sections
        if(this.aggsCache[org] === undefined) {
            this.aggsCache[org] = {
                [index] : {}
            }
        }else{
            if(this.aggsCache[org][index] === undefined) {
                this.aggsCache[org][index] = {}
            }
        }
        for (const value of values) {
            const fieldName = value.field.uniqueName;
            if (!output.values[fieldName]) {
                output.values[fieldName] = {};
            }
            output.values[fieldName][value.func] = this.calcValue(data, fieldName, value.func, org, index);
        }
        return output;
    }

    /**
     * Calculates aggregated value for the specific field.
     * @param {object} data input data
     * @param {string} fieldName field's name
     * @param {string} func aggregation name
     * @returns object - return calculates aggregated value of specified field
     */
    static calcValue(data, fieldName, func, org, index) {
        if(this.aggsCache[org][index][fieldName] === undefined){
            this.aggsCache[org][index][fieldName] = {
                [func] : 0
            }
        }
        if (func === 'sum' || func === 'none') {
            const sum = data.reduce((total, item) => total + (parseFloat(item[fieldName]) || 0), 0);
            this.aggsCache[org][index][fieldName][func] = this.aggsCache[org][index][fieldName][func] + sum;
            return this.aggsCache[org][index][fieldName][func];
        }
        if (func === 'count') {
            const result = data.filter(value => typeof value[fieldName] === 'number' || typeof value[fieldName] === 'string').length;
            this.aggsCache[org][index][fieldName][func] = this.aggsCache[org][index][fieldName][func] + result;
            return this.aggsCache[org][index][fieldName][func];
        }
        if (func === 'distinctcount') {
            const notEmptyData = data.filter(value => typeof value[fieldName] === 'number' || typeof value[fieldName] === 'string');
            const distinctMembers = [...new Set(notEmptyData.map(item => item[fieldName]))];
            this.aggsCache[org][index][fieldName][func] = this.aggsCache[org][index][fieldName][func] + distinctMembers.length;
            return this.aggsCache[org][index][fieldName][func];
        }
        if (func === 'average') {
            const avg =  this.calcAverage(data, fieldName);
            this.aggsCache[org][index][fieldName][func] = this.aggsCache[org][index][fieldName][func] + avg;
            return this.aggsCache[org][index][fieldName][func];
        }
        if (func === 'min') {
            const minItem = data.reduce((min, item) => item[fieldName] < min[fieldName] ? item : min, data[0]);
            if(this.aggsCache[org][index][func][fieldName] == undefined || 
               minItem[fieldName] < this.aggsCache[org][index][fieldName][func]){
                this.aggsCache[org][index][fieldName][func] = minItem[fieldName];
            }
            return this.aggsCache[org][index][fieldName][func];
        }
        if (func === 'max') {
            const max =  data.reduce((max, item) => max[fieldName] > item[fieldName] ? max : item)[fieldName];
            if(this.aggsCache[org][index][fieldName][func] == undefined || 
                max > this.aggsCache[org][index][fieldName][func]){
                 this.aggsCache[org][index][fieldName][func] = max;
             }
             return this.aggsCache[org][index][fieldName][func];
        }
        return NaN;
    }

    /**
     * Calculates average value for the specific field.
     * @param {object} data input data
     * @param {string} fieldName field's name
     * @returns object - return average value of specified field
     */
    static calcAverage(data, fieldName) {
        let sum = 0;
        let count = 0;
        for (let i = 0; i < data.length; i++) {
            const value = data[i][fieldName];
            if (isNaN(value) || typeof value != 'number') {
                continue;
            }
            sum += value;
            count++;
        }
        return sum / count;
    }
    /**
     * 
     * @param {*} field 
     * @returns 
     */
    static async isImmunization(field){
        const validFields = ['birthday'];
        if (field == '*') return true;
        if (validFields.includes(field)) return true;
        return false;
    }

    /**
     * 
     * @param {*} row 
     * @param {*} funcs 
     * @param {*} groupBy 
     * @returns 
     */
    static getAggsItem(row,funcs,groupBy,parents) {
        let groups = parents;
        const currentGroup = {uniqueName: groupBy, value: row[groupBy]}
        groups.push(currentGroup);
        let item = {
            keys:   {},//[groupBy] : row[groupBy]},
            values: {}
        }
        for (const group of groups){
            item.keys[group.uniqueName] = group.value
        }
        item.values = funcs.reduce((colValues,item)=>{
            colValues[item.field.uniqueName] = {
                [item.func] : row[item.field.uniqueName]
            }
            return colValues;
        },{})
        return item;
    }

    /**
     * 
     * @param {*} index 
     * @param {*} org 
     * @param {*} funcs 
     * @param {Object[]} rows 
     * @param {Object[]} cols 
     * @returns 
     */
    static async getAggsData(org,index,funcs,rows,cols,filters){
        let output = [];
        let parents = []
        if(funcs.length === 0 ) {
            return output;
        }
        let mappedFunc = funcs.map((item) => {
            const altValue = this.getNullAltValue(item.field.uniqueName);
            return `${item.func}(coalesce("${item.field.uniqueName}",${altValue})) AS "${item.field.uniqueName}"`;
        });
        mappedFunc = mappedFunc.join(",")
        const groups = rows.concat(cols);
        for (const group of groups) {
            let groupByField = group.uniqueName;
            let groupParent = {
                uniqueName : groupByField
            }
            const altValue = this.getNullAltValue(groupByField);
            if (!mappedFunc?.indexOf('distinctcount')) 
                mappedFunc = mappedFunc.replace('distinctcount(', 'count(distinct ');

            if (!mappedFunc?.indexOf('average')) 
                mappedFunc = mappedFunc.replace('average', 'avg');
            
            let sql = ` SELECT coalesce("${groupByField}",${altValue}) as "${groupByField}", ${mappedFunc} FROM manage.${index}`;
            sql = sql + ` WHERE "orgId" = '${org}'`;

            if(filters !== undefined && filters.length > 0) {
                sql = FilterCustomReportData.applyAggFilterSql(filters,sql);
            }
            sql = sql + ` GROUP BY "${groupByField}"`
            const data = await Redshift.executeQuery(sql);
            for (let row of data) {
                if(row[groupByField] !== undefined && groupParent.value === null){
                    groupParent.value  = row[groupByField];
                }
                output.push(this.getAggsItem(row,funcs,groupByField,parents));
            }
            if(groupParent.hasOwnProperty('value')){
                parents.push(groupParent);
            }
        }
        return output;
    }
    /**
     * Gets index raw data.
     * @param {string} index table name in redshift
     * @param {string} fields fields to fetch from query
     * @param {string} page page no 
     * @param {string} context select or members
     * @returns {array} - data from cache
     */
    static async getData(org,index,fields,page,size,filters) {
        const timezone = this.getTimezone();
        fields = fields || '*';
        // calc limit and offset from page
        let limit = size;
        let offset = page * size;
        let sql = 'SELECT ' + fields + ` FROM manage.${index}  `
        sql = sql + ` WHERE "orgId"='${org}' `

        if(filters !== undefined && filters.length > 0) {
            sql = FilterCustomReportData.applySqlFilter(filters,sql,timezone);
        }

        sql = sql + ` ORDER BY "createdDate" DESC LIMIT ${limit} OFFSET ${offset} `
        const data = await Redshift.executeQuery(sql);
        this.parseDates(data);
        return data;
    }

    /**
     * Gets index raw data.
     * @param {string} index table name in redshift
     * @param {string} fields fields to fetch from query
     * @param {string} page page no 
     * @param {string} context select or members
     * @returns {array} - data from cache
     */
    static async getPeopleData(org,index,uniqueField) {
        console.log("in people data")
        if (!this.dataCache[org][index]) {
            console.log("in people data inside")
            let sql = ` SELECT * FROM manage.${index} WHERE "orgId"='${org}' ORDER BY "createdDate" DESC `
            console.log("sql ==>",sql)
            const data = await Redshift.executeQuery(sql);
            const isImmunization =  this.isImmunization(uniqueField);
            if(data?.length > 0 && index === 'people' && isImmunization){
                this.getImmunizationStatus(data);
            }
            this.parseDates(data);
            this.dataCache[org][index] = data;
        }
        //return data;
        return this.dataCache[org][index];
    }
    /**
     * Gets index raw data.
     * @param {string} index index name
     * @returns {array} - data from cache
     */
    static async getDataCount(org,index,field,filters) {
        const timezone = this.getTimezone();
        let f = field === '*' ? 'total' : field;
        if(this.dataCacheCount[org] === undefined){
            this.dataCacheCount[org] = {}
        }
        if(this.dataCacheCount[org][index] === undefined){
            this.dataCacheCount[org][index] = {}
        }
        if(this.dataCacheCount[org][index][f] === undefined || (filters !== undefined && filters.length > 0)){
            field = (field !=='*') ? ` COUNT( DISTINCT "${field}" ) as total ` : ` COUNT( ${field} ) as total `;   
            let sql = 'SELECT ' + field + ` FROM manage.${index}  `
            sql = sql + ` WHERE "orgId"='${org}' `

            if(filters !== undefined && filters.length > 0) {
                sql = FilterCustomReportData.applySqlFilter(filters,sql,timezone);
            }
            const data = await Redshift.executeQuery(sql);
            this.dataCacheCount[org][index][f] = data[0]['total'];
        }
        return this.dataCacheCount[org][index][f];
    }


    /**
     * Resolving data type
     * @param {*} value 
     * @returns string - data type of value
     */
    static resolveDataType(value, fieldName='') {
        if (typeof value === 'number') {
            return 'number';
        }
        if (typeof value === 'object' && typeof value?.getMonth === 'function'){
            return 'date';
        }
        if(DATE_FIELDS.includes(fieldName)){
            return 'date';
        }
        if(PEOPLE_DATE_FIELDS.includes(fieldName)){
            return 'date';
        }
        return 'string';
    }

    /**
     * Get the type of the field
     * @param {array} fields 
     * @param {string} fieldName 
     * @returns {string} - return type of teh field within array
     */
    static getFieldType(fields, fieldName) {
        for (let i = 0; i < fields.length; i++) {
            if (fieldName === fields[i].uniqueName) {
                return fields[i].type;
            }
        }
        return undefined;
    }
    /**
     * Format the date fields in Month-Day-Year format for the fields applicable
     * @param {array} data - array data 
     * @returns 
     */
    static monthDateFormat(data){
        data = moment(data).format('MM/DD/YYYY');
        return data;
    }
    /**
     * Format the date fields in Month-Day-Year-hour-min-sec format for the fields applicable
     * @param {array} data - array data 
     * @returns 
     */
    static monthDateHourFormat(data){
        const timezone = this.getTimezone();
        data = moment.tz(data,timezone).format('MM/DD/YYYY hh:mm a');
        return data;
    }
    /**
     * Format the date fields in Day-Month-Year format for the fields in People report
     * @param {array} data - array data 
     * @returns 
     */
    static monthDateFormatPeople(data){
        data = moment(data).format('DD/MM/YYYY');
        return data;
    }

    /**
     * Derive the immunization status for people report.
     * @param {array} data - array data 
     * @returns 
     */
    static getImmunization(birthday , _doc) {
        const people = _doc;
        const birthdayMoment = birthday;
        const validBirthday = birthdayMoment && moment(birthdayMoment).isValid();
        const ageMonths =
            validBirthday && moment().diff(birthdayMoment, "months", true);
        let groupedRecords = [];
        defaultImmunizationTypes?.forEach((immunization) => {
            const immunizationEntries = people?.immunizationEntries?.filter(
                (i) => {
                  if (immunization?._id) {
                    return immunization?._id == i?.immunizationDefinitionId;
                  } else {
                    return i?.type == immunization?.type;
                  }
                }
              );

              for (let i in immunizationEntries) {
                if(((immunizationEntries[i]?.date?.$numberDouble)?.toUpperCase())?.includes('E') || ((immunizationEntries[i]?.date?.$numberLong)?.toUpperCase())?.includes('E')){
                    immunizationEntries[i].date = parseFloat(immunizationEntries[i]?.date?.$numberDouble ||  immunizationEntries[i]?.date?.$numberLong); 
                }
                else{
                    immunizationEntries[i].date = parseInt(immunizationEntries[i]?.date?.$numberDouble ||  immunizationEntries[i]?.date?.$numberLong);
                }
              }
              
              const sortedEntries = lodash.sortBy(immunizationEntries, "date"),
              monthsRequired = immunization?.monthsRequired || [],
              dosesByNow =
                validBirthday &&
                monthsRequired?.filter((m) => m <= ageMonths)?.length,
              dosesIn30 =
                validBirthday &&
                monthsRequired?.filter((m) => m <= ageMonths + 1)?.length,
              nextDoseDue =
                validBirthday &&
                monthsRequired?.length > 0 &&
                monthsRequired?.length > immunizationEntries?.length &&
                monthsRequired[immunizationEntries?.length],
              dueDate =
                nextDoseDue >= 0 &&
                birthdayMoment &&
                moment(birthdayMoment)
                  .clone()
                  .add(nextDoseDue, "months")
                  .format("DD/MM/YYYY"),
              exemptionEntry = immunizationEntries?.find((ie) => ie.exemption);
              
            let retDueDate = !immunization.exempt && !exemptionEntry && dueDate;
            let retDueSoon =
              !immunization.exempt &&
              !exemptionEntry &&
              sortedEntries?.length < dosesIn30 &&
              sortedEntries?.length >= dosesByNow;
            let retOverdue =
              !immunization.exempt &&
              !exemptionEntry &&
              sortedEntries?.length < dosesByNow;
            if (immunization?.annual && !immunization?.exempt && !exemptionEntry) {
              retDueSoon = false;
              retOverdue = false;
              retDueDate = "";
              if (sortedEntries?.length > 0) {
                const lastEntry = sortedEntries?.last();
                if (lastEntry) {
                  const lastEntryMoment = new moment(lastEntry.date);
                  const nowMoment = new moment();
                  const nextEntry = lastEntryMoment.add(12, "months");
                  if (
                    nextEntry.diff(nowMoment, "days") < 30 &&
                    nextEntry.diff(nowMoment, "days") > 0
                  ) {
                    retDueSoon = true;
                  } else if (nextEntry.diff(nowMoment, "days") < 0) {
                    retOverdue = true;
                  }
                  retDueDate = nextEntry.format("DD/MM/YYYY");
                }
              }
            }
      
            const rec = {
              type: immunization?.type,
              dose1:
                sortedEntries?.length > 0 &&
                moment(
                    sortedEntries[0].date
                ).format("DD/MM/YYYY"),
              dose2:
                sortedEntries?.length > 1 &&
                moment(
                    sortedEntries[1].date
                ).format("DD/MM/YYYY"),
              dose3:
                sortedEntries?.length > 2 &&
                moment(
                      sortedEntries[2].date
                ).format("DD/MM/YYYY"),
              dose4:
                sortedEntries?.length > 3 &&
                moment(
                      sortedEntries[3].date
                ).format("DD/MM/YYYY"),
              lastDose:
                sortedEntries?.length > 0 &&
                moment(
                      sortedEntries[sortedEntries?.length - 1].date
                ).format("DD/MM/YYYY"),
              dueDate: retDueDate,
              dueSoon: retDueSoon,
              overdue: retOverdue,
            };
            groupedRecords.push(rec);
        });

        return groupedRecords;
    }

    /**
     * 
     * @param {string} data 
     * @returns 
     */
    static isNullString(data) {
        if(data === 'null' || data === null){
            return true;
        }

        return false;
    }

    /**
     * Check the date field and parse accordingly
     * @param {array} data - array data 
     * @returns 
     */
    static parseDates(data) {
        const dateFields = [];
        const dataRow = data[0];
        if (dataRow) {
            for (const fieldName in dataRow) {
                if (this.resolveDataType(dataRow[fieldName],fieldName) === 'date') {
                    dateFields.push(fieldName);
                }
            }
        }
        if (dateFields.length > 0) {
            for (let i = 0; i < data.length; i++) {
                for (const fieldName of dateFields) {
                    if((data[i][fieldName] === '' || this.isNullString(data[i][fieldName]))){
                        data[i][fieldName] = '(blank)';
                        continue;
                    }
                    if((fieldName === 'birthday' || fieldName === 'cancellation_date' || fieldName === 'createdDate') && (data[i][fieldName] !== '' && !this.isNullString(data[i][fieldName]))){
                        data[i][fieldName]=this.monthDateFormat(data[i][fieldName]);
                        continue;
                    }

                    if((fieldName === 'checkin_date' || fieldName === 'checkout_date') && !this.isNullString(data[i][fieldName])){
                        data[i][fieldName] = this.monthDateHourFormat(data[i][fieldName]);
                        continue;
                    }
                    if(PEOPLE_DATE_FIELDS.includes(fieldName) && (data[i][fieldName] !== '') && !this.isNullString(data[i][fieldName])){
                        data[i][fieldName] = this.monthDateFormatPeople(data[i][fieldName]);
                        continue;
                    }
                    if(!this.isNullString(data[i][fieldName]) && data[i][fieldName] !== ''){
                        data[i][fieldName] = moment(data[i][fieldName]).format('DD/MM/YYYY');
                        continue;
                    } 
                }
            }
        }
    }

    /**
     * Calculate age based on birthdate.
     * @param {string} data - string data 
     * @returns 
    */
    static calAge(birthday){
        const today = moment();
        const birthDate = moment(birthday);
        const year = today.diff(birthDate,'years');
        birthDate.add(year,'years');
        const months = today.diff(birthDate,'months');
        return `${year} yr ${months} mo`;
    }

    /**
     * check date is valid or not.
     * @param {string} dateInvalid 
     * @returns 
     */
    static checkInvalidDate(dateInvalid){
        if(dateInvalid === 'Invalid date'){
            return true;
        }
        return false;
    }

    /**
     * device immunization status for people report
     * @param {array} data - array data 
     * @returns 
    */
    static getImmunizationStatus(data){
        for (let j = 0; j < data?.length; j++) {
            if(data[j]?.birthday !== null) {
                data[j].age = this.calAge(data[j].birthday);
                if(data[j].type === 'person') {
                    const response = this.getImmunization(data[j]?.birthday , JSON.parse(data[j]?._doc));  
                    for(let i = 0; i < response?.length; i++) {        
                        if (response[i]?.type == "HepB" && data[j]?.hepBVacStatus !== undefined) {
                            data[j].hepBVacStatus = response[i].dueSoon
                                ? "Due Soon"
                                : response[i].overdue
                                ? "Overdue"
                                : null;
                            data[j].firstHepBShot = this.checkInvalidDate(response[i].dose1) ? null : (response[i].dose1 || null);
                            data[j].secondHepBShot = this.checkInvalidDate(response[i].dose2) ? null : (response[i].dose2 || null);
                            data[j].lastHepBShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        }  
                        else if (response[i]?.type == "DTaP" && data[j]?.DTapVacStatus !== undefined) {
                            data[j].DTapVacStatus = response[i].dueSoon
                                ? "Due Soon"
                                : response[i].overdue
                                ? "Overdue"
                                : null;
                            data[j].firstDTapShot = this.checkInvalidDate(response[i].dose1) ? null : (response[i].dose1 || null);
                            data[j].secondDTapShot = this.checkInvalidDate(response[i].dose2) ? null : (response[i].dose2 || null);
                            data[j].thirdDTapShot = this.checkInvalidDate(response[i].dose3) ? null : (response[i].dose3 || null);
                            data[j].lastDTapShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "Hib"  && data[j]?.hibVacStatus !== undefined) {
                            data[j].hibVacStatus = response[i].dueSoon
                                ? "Due Soon"
                                : response[i].overdue
                                ? "Overdue"
                                : null;
                            data[j].firstHibShot = this.checkInvalidDate(response[i].dose1) ? null : (response[i].dose1 || null);
                            data[j].secondHibShot = this.checkInvalidDate(response[i].dose2) ? null : (response[i].dose2 || null);
                            data[j].lastHibShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "PCV13" && data[j]?.lastPCV13Shot !== undefined) {
                            data[j].lastPCV13Shot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "IPV" && data[j]?.polioVacStatus !== undefined) {
                            data[j].polioVacStatus = response[i].dueSoon
                                ? "Due Soon"
                                : response[i].overdue
                                ? "Overdue"
                                : null;
                            data[j].firstIPVShot = this.checkInvalidDate(response[i].dose1) ? null : (response[i].dose1 || null);
                            data[j].secondIPVShot = this.checkInvalidDate(response[i].dose2) ? null : (response[i].dose2 || null);
                            data[j].lastIPVShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "IIV" && data[j]?.lastInfluenzaShot !== undefined) {
                            data[j].lastInfluenzaShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "MMR" && data[j]?.MMRVacStatus !== undefined) {
                            data[j].MMRVacStatus = response[i].dueSoon
                                ? "Due Soon"
                                : response[i].overdue
                                ? "Overdue"
                                : null;
                            data[j].lastMMRShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "VAR" && data[j]?.vericellaVacStatus !== undefined) {
                            data[j].vericellaVacStatus = response[i].dueSoon
                                ? "Due Soon"
                                : response[i].overdue
                                ? "Overdue"
                                : null;
                            data[j].lastVericellaShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "HepA" && data[j]?.lastHepAShot !== undefined) {
                            data[j].lastHepAShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "MenACWY" && data[j]?.lastMenACWYShot !== undefined) {
                            data[j].lastMenACWYShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "UCHR" && data[j]?.lastUHCR !== undefined) {
                            data[j].lastUHCR = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "H1N1" && data[j]?.lastH1N1Shot !== undefined) {
                            data[j].lastH1N1Shot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        } 
                        else if (response[i]?.type == "PPSV23" && data[j]?.lastPPSV23Shot !== undefined) {
                            data[j].lastPPSV23Shot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        }
                        else if (response[i]?.type == "RV" && data[j]?.lastRotavirusShot !== undefined) {
                            data[j].lastRotavirusShot = this.checkInvalidDate(response[i].lastDose) ? null : (response[i].lastDose || null);
                        }
                    }
                } 
            } 
        }
    }

    /**
     * Handles the error of the api
     * @param {object} err - error object 
     * @param {object} res - response 
     * @param {numeric} status - status of request object of requet
     */
    static handleError(err, res, status) {
        if (!res) {
            throw new Error('The second parameter is required');
        }
        if (err instanceof URIError) {
            status = 400;
        }
        Log.info(err)
        status = status || 500;
        let message = 'Unknown server error.';
        if (typeof err === 'string') {
            message = err;
        } else if (err.message) {
            message = err.message;
        }
        res.status(status).json({ message });
    }

}
