import { AbstractRepository } from "./abstractRepository";
import { RefreshTokens } from "../../../lib/collections/refreshTokens";

/**
 * @extends { AbstractRepository<RefreshToken> }
 */
export class RefreshTokenRepository extends AbstractRepository {
    constructor(props) {
        super(props);
        this.collection = RefreshTokens;
    }

    /**
     * @param token
     * @return { RefreshToken | null}
     */
    async findOneByToken(token) {
        return await this.collection.findOneAsync({ token });
    }

        /**
     * @param token
     * @return { {} | null}
     */
    async insertOneByToken(token) {
        return await this.collection.insertAsync(token);
    }
}