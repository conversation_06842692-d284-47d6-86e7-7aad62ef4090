import { Reservations } from "../../../lib/collections/reservations";

/**
 * @abstract
 * @template T
 */
export class AbstractRepository {
    collection = null;

    /**
     * Gets the count of documents in the collection based on the provided query and options.
     *
     * @param {Object} [query={}] - The filter object to specify which documents should be counted.
     * @param {Object} [options={}] - Additional options for the count operation (e.g., limit, skip).
     * @returns {number} The count of documents that match the query.
     */
    async count(query, options) {
        return await this.collection.find(query, options).countAsync();
    }

    /**
     * Find and return a single instance of a collection document.
     *
     * @param id
     * @returns { T | null }
     */
    async findById(id) {
        return await this.collection.findOneAsync(id);
    }

    /**
     * Find and return instance(s) of a collection.
     *
     * @param query
     * @param options
     * @returns {Object[]}
     */
    async find(query, options) {
        return await this.collection.find(query, options).fetchAsync();
    }

    /**
     * Return a single document based on the provided query.
     *
     * @param query
     * @returns {*}
     */
    async findOne(query){
        return await this.collection.findOneAsync(query);
    }

    /**
     * Fetches an object by its ID with specific fields.
     * @param {string} id - The ID of the object to fetch.
     * @param {Object} fieldsToRetrieve - The fields to retrieve from the object.
     * @returns {Object|null} The object with specific fields, or null if not found.
     */
    async getObjectByIdWithFields(id, fieldsToRetrieve) {
        const object = await this.collection.findOneAsync(
            { _id: id },
            { fields: fieldsToRetrieve }
        );
        return object;
    }

    /**
     * Insert a single document into the collection.
     *
     * @param object { T }
     * @returns { string }
     */
    async insertOne(object) {
        return await this.collection.insertAsync(object);
    }
}