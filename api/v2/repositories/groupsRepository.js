import { AbstractRepository } from "./abstractRepository";
import { Groups } from "../../../lib/collections/groups";
/**
 * @extends { AbstractRepository<Group> }
 */
export class GroupsRepository extends AbstractRepository {
    constructor() {
        super();
        this.collection = Groups;
    }

    /**
     * @param orgIds { Array<strings> }
     * @param pagination { PaginationService | null }
     * @returns { Promise<Array<Group>> }
     */
    async findClassroomsByOrgIds(orgIds, pagination) {
        const aggregateQuery = [
            {
                $match: {
                    orgId: {$in: orgIds}
                },
            },
            {
                $lookup: {
                    from: 'orgs',
                    localField: 'orgId',
                    foreignField: '_id',
                    as: 'org'
                },
            },
            {
                $unwind: '$org',
            },
            {
                $sort: {
                    'org.name': 1,
                    'name': 1
                }
            }
        ]
        if (pagination) {
            aggregateQuery.push({
                $skip: pagination.offset
            });
            aggregateQuery.push({
                $limit: pagination.limit
            });
        }
        return await ( await this.collection.aggregate(aggregateQuery) ).toArray();
    }

    /**
     * @param { Array<string> } orgIds  
     * @return { number }
     */
    async countClassroomsByOrgIds(orgIds) {
        return await this.collection.find({ orgId: {$in: orgIds}}).countAsync();
    }
}