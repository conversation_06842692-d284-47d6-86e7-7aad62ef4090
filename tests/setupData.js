// See https://packosphere.com/dburles/factory

// Import lodash:

import _ from 'lodash';
import assert from "assert";

const fs = require('fs');
const path = require('path');

const testHubspotData = process.env.PWD + '/tests/hubspot_sample_data';
const jsonFiles = fs.readdirSync(testHubspotData).filter(file => file.endsWith('.json'));

import '../server/agenda/agendaScheduler.js';
import '../server/rightAtSchoolService.js';
import '../server/methodsZkTeco.js';
import '../server/util.js';
import '../lib/collections/peopleDataValidation.js';
import '../lib/collections/orgs.js';
import '../lib/collections/people.js';
import '../lib/collections/timeCards.js';
import '../server/collections/qrCodes.js';
import { People } from '../lib/collections/people.js';
import { Orgs } from '../lib/collections/orgs.js';

async function setupData() {

    /*
    In the context of our code, Factory.define() sets up a blueprint
    for creating new organizations in the 'Orgs' collection.
    The object { name: "[Organization Name]" } provides default values
    that will be used to populate fields in new documents when they're created.

    In this case, it means that any time you create a new organization using
    this factory and don't specify a name, it will default to "[Organization Name]".
    */

    Factory.define("org", Orgs, {
        name: "[Organization Name]",
        after: (org) => {
            // If we need to do anything with org, we do it here.
            // May need to check for race conditions or adjust this accordingly.
        },
    });

    Factory.define("person", People, {
        firstName: "John",
        lastName: "Doe",
        type: "person",
        orgId: Factory.get("org"),
        after: (person) => {
            // If we need to do anything with person, we do it here.
        },
    });

    // Create a new organization using the factory:

    Factory.create("org", { _id: "test-id", name: "Test Organization" });

    // Create a new person using the factory:

    Factory.create("person", { _id: "test-person-id", firstName: "Test", lastName: "Person", orgId: "test-id" });

    // The HubspotService expects the following to have data:

    /*
    hubspotFamilies = People.find({ orgId, type: "family", "hubspotId": { "$ne": null }, hubspotFamilyDisenrolledAt: { "$eq": null } }).fetch();
    */

    if (jsonFiles.length === 0) {
        throw new Error("No JSON files found in the hubspot_data directory. Have you ran Meteor.callAsync('hubspot/downloadContacts')?");
    }

    /*
    JSON files look like:
    [
        ...
        {
            "id": "1497951",
            "properties": {
            "child_1_leave_date": null,
            "child_1_start_date": null,
            "child_2_birthday": null,
            "child_2_leave_date": null,
            "child_2_name_": null,
            "child_2_start_date": null,
            "child_3_birthday": null,
            "child_3_leave_date": null,
            "child_3_name_": null,
            "child_3_start_date": null,
            "child_4_birthday": null,
            "child_4_leave_date": null,
            "child_4_name_": null,
            "child_4_start_date": null,
            "child_5_birthday": null,
            "child_5_leave_date": null,
            "child_5_name_": null,
            "child_5_start_date": null,
            "child_birthday": "2021-03-30",
            "child_name_": "REDACTED",
            "createdate": "2023-01-12T13:52:23.256Z",
            "email": "REDACTED",
            "firstname": "REDACTED",
            "hs_object_id": "1497951",
            "lastmodifieddate": "2023-06-07T05:00:23.567Z",
            "lastname": "REDACTED"
            },
            "createdAt": "2023-01-12T13:52:23.256Z",
            "updatedAt": "2023-06-07T05:00:23.567Z",
            "archived": false
        },
        ...
    ]
    */

    // For each JSON file we iterate over all items and create a "family" person
    // for the main name and simply a single child (if they have one) for the first
    // child name.

    _.each(jsonFiles, (file) => {
        const data = JSON.parse(fs.readFileSync(path.join(testHubspotData, file), 'utf8'));

        _.each(data, (item) => {

            const hubspotId = item.id;
            const firstName = item.properties.firstname;
            const lastName = item.properties.lastname;
            const childName = item.properties.child_name_;

            // We may use these in the future:
            // const email = item.properties.email;
            // const childBirthday = item.properties.child_birthday;

            Factory.create("person", {
                firstName: firstName,
                lastName: lastName,
                orgId: "test-id",
                type: "family",
                hubspotId: hubspotId,
                importIndex: `0` // Not sure about this
            });

            if (childName) {
                Factory.create("person", {
                    firstName: childName,
                    lastName: lastName,
                    orgId: "test-id",
                    type: "person",
                    hubspotId: hubspotId,
                    importIndex: `0` // Not sure about this
                });
            }
        });
    });

    // Next, let's create a staff user:
    // We will use this staff person for timecard testing for now.

    Factory.create("person", {
        _id: "test-staff-id",
        firstName: "Test",
        lastName: "Staff",
        orgId: "test-id",
        type: "staff",
    });

    // The count is 8 because there is 1 test user / hard-coded user,
    // 3 sample parents who each have 3 sample children (for the hubspot tests),
    // and 1 test staff user.
    // -CC

    assert.equal(await People.find().countAsync(), 8);
    assert.equal(await Orgs.find().countAsync(), 1);
}

export default setupData;
