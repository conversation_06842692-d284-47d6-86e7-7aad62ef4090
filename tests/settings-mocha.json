{"debug": true, "rollbarClientToken": "foo", "rollbarServerToken": "", "adpEmail1": "<EMAIL>", "awsBucket": "my-example-staging", "AWSAccessKeyId": "a<PERSON>key", "AWSSecretAccessKey": "awssecret", "cognitoUserPoolId": "poolId", "cognitoAccessKey": "foo", "cognitoSecretKey": "bar", "cognitoClientId": "clientId", "cognitoEndpoint": "http://localhost:4566", "cognitoRegion": "us-east-1", "apiBypassAws": true, "kmsKeyId": "alias/api-jwt-signer", "kmsAccessKey": "foo", "kmsSecretKey": "bar", "kmsRegion": "us-east-1", "kmsEndpoint": "http://localhost:4566", "kmsSigningAlgo": "ECDSA_SHA_512", "kmsAlgoId": "ECDSA_SHA_512", "mpAWSaccessKey": "a<PERSON>key", "mpAWSsecretKey": "awssecret", "mpEntityKey": "key", "mpEventBridgeQueueUrl": "https://fifo", "mpMomentEventBridgeQueueUrl": "https://sqs.us-east-1.amazonaws.com/79", "wmgPresenceUrl": "https://presence.internal.watchmegrow.com", "mpEventBridgeMessageGroup": "staging", "mailchimpKey": "mckey", "mpSMSQueueUrl": "https://sqs.us-east-1.amazonaws.com/7", "defaultOriginationNumber": "18335555555", "childcareCrmUrl": "http://localhost", "childcareCrmUIUrl": "http://localhost:3000/#", "zkTecoUrl": "https://example.com:8098", "zkTecoApiToken": "apikey", "idpBaseUrl": "http://127.0.0.1:8000", "idpClientSecret": "Use Your Secret", "intercomSecret": "", "kinderConnectUrl": "", "mobileApiKey": "apikey", "public": {"rollbarClientToken": "", "supportSite": "https://help-beta.momentpath.com/knowledge", "AWSAccessKeyId": "key", "awsBucketUrl": "https://my-meteor-example.s3.amazonaws.com", "environment": "staging", "photoBaseUrl": "https://cfm.momentpath.com/", "stripe": {"publishableKey": "key"}, "adyen": {"clientKey": "key"}, "whitelabel": {"enabled_sites": ["lightbridge", "connect", "luvnotes", "<PERSON>sunshinehouse", "myfoundations", "myquestzone", "dayearlylearning", "cnmoments"], "lightbridge": {"large_logo": "/img/lb_logo-white.svg", "small_logo": "/img/lb_icon-white.svg", "header_logo": "https://assets.momentpath.com/customers/lightbridge/summaryemailv2019/logo.png", "colors": [{"template": "--primary", "color": "#218ACA"}, {"template": "--people-ratio-template-color", "color": "#218ACA"}, {"template": "--people-ratio-staff-template-color", "color": "#8BC53D"}, {"template": "--dark-primary", "color": "#0e3852"}, {"template": "--light-primary", "color": "#b8d2e3"}, {"template": "--lighter-primary", "color": "#dbf1ff"}], "sessionVars": [{"name": "wmgLabel", "value": "ParentView® powered by WatchMeGrow"}]}, "connect": {"large_logo": "/img/bklogo.svg", "small_logo": "/img/bklogo.svg", "header_logo": "https://assets.momentpath.com/customers/buildingkidz/bk.png", "title": "BuildingKidz Connect", "favicon": "/bkfav.ico", "colors": [{"template": "--primary", "color": "#560F2D"}, {"template": "--people-ratio-template-color", "color": "#560F2D"}, {"template": "--people-ratio-staff-template-color", "color": "#939598"}, {"template": "--dark-primary", "color": "#0E3852"}, {"template": "--light-primary", "color": "#DDCFD5"}, {"template": "--lighter-primary", "color": "#EFE7EA"}], "sessionVars": [{"name": "learningPathLink", "value": "https://training.buildingkidzschool.com"}]}, "luvnotes": {"large_logo": "/img/lsp_icon.png", "small_logo": "/img/lsp_icon.png", "header_logo": "https://assets.momentpath.com/customers/littlesunshine/luvnotes.png", "colors": [{"template": "--primary", "color": "#538177"}, {"template": "--people-ratio-template-color", "color": "#538177"}, {"template": "--people-ratio-staff-template-color", "color": "#C67D6D"}, {"template": "--dark-primary", "color": "#09473a"}, {"template": "--light-primary", "color": "#a6ded2"}, {"template": "--lighter-primary", "color": "#cffcf3"}], "sessionVars": [{"name": "eduTitle", "value": "Red Carpet Experience"}]}, "mysunshinehouse": {"large_logo": "/img/sshouse.png", "small_logo": "/img/sshouse.png", "header_logo": "https://assets.momentpath.com/customers/sshouse/header.png", "colors": [{"template": "--primary", "color": "#41748D"}, {"template": "--people-ratio-template-color", "color": "#41748D"}, {"template": "--people-ratio-staff-template-color", "color": "#54585A"}, {"template": "--dark-primary", "color": "#012b40"}, {"template": "--light-primary", "color": "#bde0f2"}, {"template": "--lighter-primary", "color": "#cfecfa"}], "sessionVars": []}, "myfoundations": {"large_logo": "/img/foundations.png", "small_logo": "/img/foundations.png", "header_logo": "https://assets.momentpath.com/customers/foundations/header.png", "colors": [{"template": "--primary", "color": "#2A317D"}, {"template": "--people-ratio-template-color", "color": "#2A317D"}, {"template": "--people-ratio-staff-template-color", "color": "#81BD41"}, {"template": "--dark-primary", "color": "#000430"}, {"template": "--light-primary", "color": "#989feb"}, {"template": "--lighter-primary", "color": "#c9ceff"}], "sessionVars": []}, "myquestzone": {"large_logo": "/img/questzone.png", "small_logo": "/img/questzone.png", "header_logo": "https://assets.momentpath.com/customers/questzone/header.png", "colors": [{"template": "--primary", "color": "#4D008C"}, {"template": "--people-ratio-template-color", "color": "#4D008C"}, {"template": "--people-ratio-staff-template-color", "color": "#F18A00"}, {"template": "--dark-primary", "color": "#10011c"}, {"template": "--light-primary", "color": "#b98fdb"}, {"template": "--lighter-primary", "color": "#e4c5fc"}], "sessionVars": []}, "dayearlylearning": {"large_logo": "/img/dayearlylearning.png", "small_logo": "/img/dayearlylearning.png", "header_logo": "https://assets.momentpath.com/customers/dayearlylearning/header.png", "colors": [{"template": "--primary", "color": "#2A6AA6"}, {"template": "--people-ratio-template-color", "color": "#0C2338"}, {"template": "--people-ratio-staff-template-color", "color": "#EC7D30"}, {"template": "--dark-primary", "color": "#0C2338"}, {"template": "--light-primary", "color": "#5D9BD3"}, {"template": "--lighter-primary", "color": "#7ABCF7"}], "sessionVars": []}, "cnmoments": {"large_logo": "/img/cnmoments.png", "small_logo": "/img/cnmoments.png", "header_logo": "https://assets.momentpath.com/customers/cnmoments/header.png", "colors": [{"template": "--primary", "color": "#007DC3"}, {"template": "--people-ratio-template-color", "color": "#007DC3"}, {"template": "--people-ratio-staff-template-color", "color": "#FFC425"}, {"template": "--dark-primary", "color": "#01314D"}, {"template": "--light-primary", "color": "#59B5EB"}, {"template": "--lighter-primary", "color": "#ADDCF7"}], "sessionVars": []}}}, "stripe": {"secretKey": "key"}, "adyen": {"apiKey": "key", "notificationUsername": "xyz123", "notificationPassword": "mypass", "reportingUsername": "report@com", "reportingPassword": "pass", "reportingPasswordx": "pass", "mpapipwX": "pw", "mpapiuserX": "u", "mpapipw": "pw", "mpapiuser": "u", "defaultAccountCode": "code"}, "transloaditKey": "key", "TWILIO_ACCOUNT_SID": "sid", "TWILIO_AUTH_TOKEN": "tok", "TWILIO_NUMBER": "+***********", "TARGET_URL": "http://localhost:3000", "kadira": {"appId": "id", "appSecret": "secret", "options": {"endpoint": "https://meteor-apm-engine.nodechef.com"}}, "suppressJobs": true, "applicationJobs": true, "airSlate": {"bucketLambdaApiKey": "key", "url": "https://bots.airslate.com", "overrideWebhookUrl": "https://lb112.momentpath.com"}}