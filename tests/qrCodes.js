import assert from "assert";
import moment from "moment-timezone";
import { Orgs } from "../lib/collections/orgs";
import { QrCodes } from "../server/collections/qrCodes";

describe("momentpathweb - QR code check-in", function () {

  it("QR codes start with no data...", async function () {
    assert(await QrCodes.find().countAsync() === 0);
  });

  it("QrCodes can be created and checked for expiration...", async function () {
    const useTokenString = "hardcoded-token-string"
    const result = await QrCodes.insertWithTokenString({
      orgId: "test-id",
      tokenString: useTokenString,
      createdByPersonId: "test-staff-id"
    });
    assert(await QrCodes.find().countAsync() === 1);
    // Assert the result has the correct return values:
    /*
    {
      authString: string
      qrCodeId: string
    }
    */
    assert(typeof result === "object");
    assert(typeof result.authString === "string");
    assert(typeof result.qrCodeId === "string");

    // qrdata right now is result.authString
    // The naming is a little inconsistent but we want to match the naming
    // of the codebase for now.

    const qrdata = result.authString;

    const qrdataMatch = await QrCodes.retrieveFromQrData("test-id", qrdata); // Returns the QrCode object

    if (!qrdataMatch) {
      throw new Meteor.Error(400, "Unauthorized. QR code not found.");
    }

    const expirationResult = await QrCodes.checkForExpiration("test-id", qrdataMatch._id);

    // Assert that expirationResult looks like: 

    /*
      {
        expiringQrCodesEnabled: false,
        error: null,
      };
    */

    assert.deepStrictEqual(expirationResult, {
      error: null,
      expiringQrCodesEnabled: false,
    });

    // Next, we need to enable expiring QR codes for the org.
    // We'll do that by setting the org's people/checkInCheckOutQrCodesExpire/enabled setting to true.

    await Orgs.changeCustomizationValue("test-id", "people/checkInCheckOutQrCodesExpire/enabled", true);

    const expirationResult2 = await QrCodes.checkForExpiration("test-id", qrdataMatch._id);

    // Assert that expirationResult looks like:

    /*
      {
        expiringQrCodesEnabled: true,
        error: null,
      };
    */

    assert.deepStrictEqual(expirationResult2, {
      error: null,
      expiringQrCodesEnabled: true,
    });

    // Now we'll set the org's people/checkInCheckOutQrCodesExpireAfterMinutes setting to 1 (minute)
    // We use Orgs.updateCheckInCheckOutQrCodesExpireAfterMinutes(currentOrg._id, updatedValue);

    await Orgs.updateCheckInCheckOutQrCodesExpireAfterMinutes("test-id", 1);

    // Before we do this, we need to alter the QrCodes createdAt value

    await QrCodes.updateAsync(qrdataMatch._id, {
      $set: {
        createdAt: moment().subtract(2, "minutes").toDate()
      }
    });

    const expirationResult3 = await QrCodes.checkForExpiration("test-id", qrdataMatch._id);

    // Assert that expirationResult looks like:

    /*
      {
        expiringQrCodesEnabled: true,
        error: "This QR code has expired. Please create a new one.",
      };
    */

    assert.deepStrictEqual(expirationResult3, {
      error: "This QR code has expired. Please create a new one.",
      expiringQrCodesEnabled: true,
    });

    // Finally, for a final test we can update the org to disable auto-refreshing / expiring
    // QR codes and then this expired QR code should still be valid.

    await Orgs.changeCustomizationValue("test-id", "people/checkInCheckOutQrCodesExpire/enabled", false);

    const expirationResult4 = await QrCodes.checkForExpiration("test-id", qrdataMatch._id);

    assert.deepStrictEqual(expirationResult4, {
      error: null,
      expiringQrCodesEnabled: false,
    });

  })

});
