<template name="search">
    <div id="searchModal" class="modal {{#if shouldFade}}fade{{/if}}">
        <div class="modal-dialog modal-dialog-scrollable modal-xl">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="mb-6 mx-auto" style="width: 80%">
                        <div class="box box-solid">
                            <div class="box-header with-border">
                                <h3 class="box-title">Search</h3>
                            </div>
                            <div class="box-body">
                                <form id="mainSearchForm">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="mainSearchFormText" value="{{query}}" data-cy="main-search-input">
                                        <div class="d-flex align-items-center pl-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                    <rect x="0" y="0" width="24" height="24" />
                                                    <path d="M14.2928932,16.7071068 C13.9023689,16.3165825 13.9023689,15.6834175 14.2928932,15.2928932 C14.6834175,14.9023689 15.3165825,14.9023689 15.7071068,15.2928932 L19.7071068,19.2928932 C20.0976311,19.6834175 20.0976311,20.3165825 19.7071068,20.7071068 C19.3165825,21.0976311 18.6834175,21.0976311 18.2928932,20.7071068 L14.2928932,16.7071068 Z" fill="var(--primary)" fill-rule="nonzero" opacity="0.3" />
                                                    <path d="M11,16 C13.7614237,16 16,13.7614237 16,11 C16,8.23857625 13.7614237,6 11,6 C8.23857625,6 6,8.23857625 6,11 C6,13.7614237 8.23857625,16 11,16 Z M11,18 C7.13400675,18 4,14.8659932 4,11 C4,7.13400675 7.13400675,4 11,4 C14.8659932,4 18,7.13400675 18,11 C18,14.8659932 14.8659932,18 11,18 Z" fill="var(--primary)" fill-rule="nonzero" />
                                                </g>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="pl-3" style="font-weight: 700; font-size: 12px; color: rgba(0, 0, 0, 0.6);" data-cy="user-prompt-message">Enter at least 3 characters</div>
                                </form>
                            </div>
                        </div>
                    </div>
                    {{#if executedSearch}}
                    <div class="px-3">
                        <h4>Results for '{{query}}':</h4>
                        {{# if canExpand }}
                        <div class="row my-4">
                            <div class="col-sm-12 checkbox-list">
                                {{# if canSearchMultipleOrgs }}
                                <label class="checkbox checkbox-primary" data-cy="expand-search-label">
                                    <input type="checkbox" id="expandSearch" checked="{{expandedSearch}}" data-cy="expand-search-chkbox">
                                    <span></span>
                                    Expand search to all available organizations
                                </label>
                                {{/ if }}
                                <label class="checkbox checkbox-primary" data-cy="include-inactive-label">
                                    <input type="checkbox" id="includeInactive" checked="{{includeInactive}}" data-cy="include-inactive-chkbox">
                                    <span></span>
                                    Include inactive
                                </label>
                            </div>
                        </div>
                        {{/ if }}
                        <div class="list-table">
                            {{#if workingSearch}}
                            {{> loading}}
                            {{else}}
                            {{#each results}}
                            <div class="card card-custom card-flat mb-2" data-cy="search-data-list">
                                <div class="card-body mr-n4 pl-8 pr-4">
                                    <div class="row col-md-12 align-items-center pr-0">
                                        {{# unless avatarUrl }}
                                        <div
                                          class="d-flex avatar-circle avatar-small align-items-center justify-content-center"
                                          style="background-color: {{ getAvatarBackground initials}}"
                                        >
                                            <span class="initials initials-search">{{ initials }}</span>
                                        </div>
                                        {{else}}
                                        <div class="people-list-user-img avatar-small" style="background-image:url({{ avatarUrl }})"></div>
                                        {{/ unless }}
                                        <div class="col-md-4">
                                            <div class="font-weight-bolder pt-1" style="font-size: 18px; color: rgba(0, 0, 0, 0.87); line-height: 22px;" data-cy="name">{{name}}</div>
                                            {{#if type}}
                                            <div class="font-weight-bolder pt-1" style="color: rgba(0, 0, 0, 0.87); line-height: 22px;" data-cy="entity-type">{{translationForEntityType type}}</div>
                                            {{else}}
                                            <div class="font-weight-bolder pt-1" style="color: rgba(0, 0, 0, 0.87); line-height: 22px;" data-cy="enroll-type">{{enrollType}}</div>
                                            {{/if}}
                                            {{# if inActive }}<span style="color:#ff0000">(i)</span>{{/if}}
                                            <div class="pt-2 pb-2" style="color: var(--gray-light);" data-cy="org-name">{{ orgName }}</div>
                                        </div>
                                        <div class="d-flex flex-wrap align-items-center col">
                                            {{# if statusCode }}
                                            <span class="badge badge-search{{# if classroom }} mr-2{{/ if }} my-1">{{ statusCode }}</span>
                                            {{/ if }}
                                            {{# if classroom }}
                                            <span class="badge badge-search my-1">{{ classroom }}</span>
                                            {{/ if }}
                                        </div>
                                        {{# if hasEnrollAccount }}
                                        <div class="col-md-2 px-0">
                                            {{# if childcareCrmUrl }}
                                            {{# if hasWhiteLabel }}
                                            <a href="{{ childcareCrmUrl }}" target="_blank" class="btn btn-enroll search primary-bg">
                                                <label class="btn-enroll-label">Enroll</label>
                                            </a>
                                            {{ else }}
                                            <a href="{{ childcareCrmUrl }}" target="_blank" class="btn btn-enroll search">
                                                <img alt="LineLeader Enroll"
                                                     src="/media/svg/icons/ll_enroll_fullcolor.svg"
                                                     class="d-block"/>
                                            </a>
                                            {{/ if }}
                                            {{/ if }}
                                        </div>
                                        {{/ if }}
                                        <div class="col-md-{{# if hasEnrollAccount }}2{{ else }}3{{/ if }} px-0">
                                            {{# if dataId }}
                                            {{# if hasWhiteLabel }}
                                            <a href="{{pathFor route=route _id=dataId}}" data-cy="manage-search-button" class="btn btn-manage search destination-link primary-bg">
                                                <label class="btn-enroll-label">Manage</label>
                                            </a>
                                            {{ else }}
                                            <a href="{{pathFor route=route _id=dataId}}" data-cy="manage-search-button" class="btn btn-manage search destination-link">
                                                <img alt="LineLeader Manage"
                                                     src="/media/svg/icons/ll_manage_fullcolor.svg"
                                                     class="d-block"/>
                                            </a>
                                            {{/ if }}
                                            {{/ if }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="list-table-spacer-row"></div>
                            {{/each}}
                            {{#unless results}}
                            <h4>{{getEmptySet}}</h4>
                            {{/unless}}
                            {{/if}}
                        </div>
                    </div>
                    {{/if}}
                </div>
            </div>
        </div>
    </div>
</template>
