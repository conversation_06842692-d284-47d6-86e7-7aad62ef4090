import moment from 'moment';
const NO_RESULTS = "Sorry, no results found";

import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Orgs } from '../../../lib/collections/orgs';
import './search.html';
import '../../layout/loading';

Template.search.onCreated( function () {
	var self = this;
	self.emptySet = new ReactiveVar('')
	self.expandSearch = new ReactiveVar(false);
	self.includeInactive = new ReactiveVar(false);
	self.workingSearch = new ReactiveVar(false);
	self.searchDebounce = new ReactiveVar(new moment().subtract(400, 'milliseconds'));
	self.hasInput = new ReactiveVar(false);

	this.autorun(() => {
		const query = Session.get("searchParams") || "";
		const expandedSearch = this.expandSearch.get();
		const includeInactive = this.includeInactive.get();
		const now = new moment();
		const duration = moment.duration(now.diff(this.searchDebounce.get()))

		if (!this.hasInput.get()) {
			return;
		}

		if (query.trim().length < 3) {
			Session.set("searchResults", null);
			this.emptySet.set('');
			return;
		}

		if (duration.as('milliseconds') < 300) {
			return;
		}

		this.searchDebounce.set(new moment());
		this.workingSearch.set(true);
		Meteor.callAsync("search", { expandSearch: expandedSearch, includeInactive, query }).then(result => {
			Session.set("searchResults", result);
			self.emptySet.set(NO_RESULTS);
			self.workingSearch.set(false);
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
			self.workingSearch.set(false);
		});
	});
});

Template.search.onRendered(function() {
	setTimeout(() => {
		$('#mainSearchFormText').eq(0).focus();
	}, '300')
});

Template.search.helpers({
	"results": function() {
		return Session.get("searchResults");
	},
	"workingSearch": function() {
		return Template.instance().workingSearch.get();
	},
	"query": function() { return Session.get("searchParams")},
	"executedSearch": function() {
		var q = Session.get("searchParams");
		return (q != undefined && q != "");
	},
	"canExpand": function() {
		const user = Meteor.user(), person = user && user.fetchPerson();
		return person && (person.type === 'admin' || person.masterAdmin || person.superAdmin);
	},
	"canSearchMultipleOrgs": function () {
		const user = Meteor.user(), person = user && user.fetchPerson();
		return person && (person.masterAdmin || person.superAdmin);
	},
	"expandedSearch": function() {
		return Template.instance().expandSearch.get();
	},
	"includeInactive": function() {
		return Template.instance().includeInactive.get();
	},
	"getEmptySet": function() {
		return Template.instance().emptySet.get();
	},
});

Template.search.events({
	"click #submitSearchForm": function(event, instance) {
		event.preventDefault();
		var q=$("#mainSearchFormText").val();
		Session.set("searchParams", q);
	},
	"click .destination-link": function(event) {
		const destination = $(event.currentTarget).attr("href");
		event.preventDefault();
		$(".search-box-dynamic").val("");
		const org = Orgs.current();
		if (org._id != this.orgId) {
			Meteor.callAsync('adminSwitchOrg', this.orgId).then(result => {
				location.replace("/loading?setLoc=redirect&desturl=" + encodeURIComponent(destination));
			}).catch(error => {
				mpSwal.fire("Error", error.reason, "error");
			});
		} else {
			FlowRouter.go(destination);
		}
			
	},
	"change #expandSearch": function(e, i) {
		const t = $(e.currentTarget).prop("checked");
		i.expandSearch.set(t);
	},
	"change #includeInactive": function(e, i) {
		const t = $(e.currentTarget).prop("checked");
		i.includeInactive.set(t);
	},
	'click .btn.search': function() {
		// Make sure to close the modal when one of the product logos is clicked
		$('#searchModal').modal('hide');
	},
	'input #mainSearchFormText, change #mainSearchFormText': _.debounce(function (e, i) {
		// When the search text changes, run search after 400ms wait
		const q = $("#mainSearchFormText").val();
		i.hasInput.set(true);
		Session.set("searchParams", q);
	}, 400)
})
