import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { Curriculums } from '../../../lib/collections/curriculum';
import _ from '../../../lib/util/underscore';
import { processPermissions } from '../../../lib/permissions';
import { showModal, hideModal } from "../main";
import '../simpleModal/simpleModal.js';
import './_curriculumFormModal';
import './_curriculumBuilderAddActivityModal';
import './curriculumBank.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Log } from '../../../lib/util/log';
import { remapDownloadUrl } from '../../../mpweb';
import { USER_TYPES } from '../../../lib/constants/profileConstants';
import { AvailableActionTypes, AvailablePermissions } from '../../../lib/constants/permissionsConstants';
import { AvailableCustomizations } from '../../../lib/customizations';
import { StringUtils } from '../../../lib/util/stringUtils';

const BANK_LIMIT = 100;
const NEW_MESSAGES_SESSION = "newMessages";

Template.curriculumBank.onCreated( function() {
	this.searchResults = new ReactiveVar();
	this.filterApproval = new ReactiveVar(false);
	this.filterDraft = new ReactiveVar(false);
	this.filterPublished = new ReactiveVar(false);
	this.page = new ReactiveVar(0);
	this.totalResults = new ReactiveVar(0);
	this.filterAgeGroup = new ReactiveVar();
	this.savedData = new ReactiveVar([]);
	this.bankLimit = new ReactiveVar(BANK_LIMIT);
	this.nextPage = new ReactiveVar(false)
	this.previousPage = new ReactiveVar(false)
	this.currentOrg = Orgs.current();
	this.userHasAdminEditPermissions = processPermissions({
		assertions: [{ context: AvailablePermissions.CURRICULUM_BANK, action: AvailableActionTypes.EDIT }],
		evaluator: (thisPerson) => thisPerson.type === USER_TYPES.ADMIN
	});
	this.orgHasGlobalAndLocalCustomization = this.currentOrg?.hasCustomization(AvailableCustomizations.CURRICULUM_BANK_GLOBAL_AND_LOCAL);
	Session.set(NEW_MESSAGES_SESSION, []);
	checkForNewMessages(NEW_MESSAGES_SESSION);
});

Template.curriculumBank.onRendered(async function() {
	const currentPerson = Meteor.user() && Meteor.user().fetchPerson();

	if (currentPerson.type === USER_TYPES.STAFF && currentPerson.defaultGroupId) {
		const defaultGroup = Groups.findOne({_id: currentPerson.defaultGroupId});

		if (defaultGroup && defaultGroup.activitiesAgeGroup) {
			const activitiesAgeGroups = Curriculums.activitiesAgeGroups();

			for (const item of activitiesAgeGroups) {
				// if the activity matches the teacher default group Activities Age Group (from the group configuration), then move it to the top of the array
				if (item.label === defaultGroup.activitiesAgeGroup) {
					Template.instance().filterAgeGroup.set( item.label )
					$("#inputFilterAge").val(item.label);
				}
			}
		}
	} else {
		Template.instance().filterAgeGroup.set( "" )
	}

	await refreshResults(Template.instance());
});

Template.curriculumBank.helpers({
	'getItemCountStart': function () {
		const itemCount = Template.instance().page.get() * BANK_LIMIT;
		const totalItems = Template.instance().totalResults.get();

		if (itemCount > totalItems || totalItems === 0) {
			return totalItems
		}

		return itemCount + 1;
	},

	'getItemCountEnd': function (totalCount) {
		const totalItems = (Template.instance().page.get() + 1) * BANK_LIMIT

		if (totalItems > totalCount) {
			return totalCount
		}

		return totalItems;
	},

	'getTotalCount': function () {
		return Template.instance().totalResults.get();
	},

	'getColSize': function (curriculumBank) {
		const baseColSize = 'col-3';
		if (!curriculumBank.approvedBy && !curriculumBank.modifiedBy && !curriculumBank.updateRequestBy) {
			return 'col-7'
		}

		if ((!curriculumBank.approvedBy || !curriculumBank.updateRequestBy) && !curriculumBank.modifiedBy) {
			return 'col-5'
		}

		return baseColSize;
	},

	'getActionDropdownLabel': function (curriculumBank) {
		if (curriculumBank.waitingForApproval) {
			return "Waiting For Approval";
		}

		return curriculumBank.published ? "Published" : "Draft";
	},

	'getActionBtnColor': function (curriculumBank) {
		if (curriculumBank.waitingForApproval) {
			return "btn-info";
		}

		return curriculumBank.published ? "btn-primary" : "btn-secondary";
	},

	'getTitle': function (curriculumBank) {
		return curriculumBank.sourceData?.headline || curriculumBank.updateData?.headline;
	},

	'getAgeGroups': function (curriculumBank) {
		return getActivitiesAgeGroup(curriculumBank);
	},

	'availableAgeGroups': function () {
		return Curriculums.activitiesAgeGroups();
	},

	'userCanAddOrModifyCurriculumBank': function () {
		return processPermissions({
			assertions: [{context: AvailablePermissions.CURRICULUM_BANK, action: AvailableActionTypes.EDIT}],
			evaluator: (thisPerson) => thisPerson.type === USER_TYPES.ADMIN || thisPerson.type === USER_TYPES.STAFF
		});
	},

	'hasCurriculumBankNotification': function () {
		return Session.get(NEW_MESSAGES_SESSION).length > 0;
	},

	'curriculumBankNotifications': function () {
		return Session.get(NEW_MESSAGES_SESSION);
	},

	"getStatusColor": function (status) {
		if (status.toLowerCase() === "waiting for approval") {
			return "alert-light-info";
		}

		if (status.toLowerCase() === "approved") {
			return "alert-light-success";
		}

		if (status.toLowerCase() === "rejected") {
			return "alert-light-danger";
		}
	},

	'results': function () {
		return Template.instance().searchResults.get();
	},

	'availableCurriculumTypes': function () {
		return Template.instance().currentOrg.availableCurriculumTypes({mapped: true});
	},

	'replaceBackticks': function (string) {
		return string.replace('`', ' > ');
	},

	'canDismissNotification': function (status) {
		return status.toLowerCase() !== "waiting for approval";
	},

	'canEdit': function (item) {
		const org = Template.instance().currentOrg;
		const inScope = item && (!(Template.instance().orgHasGlobalAndLocalCustomization) || item.bankId === org._id);
		const hasPermissions = processPermissions({
			evaluator: (thisPerson) => thisPerson.type === USER_TYPES.ADMIN || thisPerson.type === USER_TYPES.STAFF
		});

		return inScope && hasPermissions;
	},

	'canArchive': function (item) {
		const org = Template.instance().currentOrg;
		const inScope = item && (!(Template.instance().orgHasGlobalAndLocalCustomization) || item.bankId === org._id);
		const hasPermissions = Template.instance().userHasAdminEditPermissions;

		return inScope && hasPermissions;
	},

	'canPublish': function (item) {
		const org = Template.instance().currentOrg;
		const inScope = item && (!(Template.instance().orgHasGlobalAndLocalCustomization) || item.bankId === org._id);
		const hasPermissions = Template.instance().userHasAdminEditPermissions;

		return inScope && hasPermissions;
	},

	'canApprove': function (item) {
		const org = Template.instance().currentOrg;
		const inScope = item && (!(Template.instance().orgHasGlobalAndLocalCustomization) || item.bankId === org._id);
		const hasPermissions = Template.instance().userHasAdminEditPermissions;

		return inScope && hasPermissions;
	},

	'canManageContent': function () {
		const canEdit = processPermissions({
			assertions: [{context: AvailablePermissions.CURRICULUM_BANK, action: AvailableActionTypes.EDIT}],
			evaluator: (thisPerson) => thisPerson.type === USER_TYPES.ADMIN || thisPerson.type === USER_TYPES.STAFF
		});

		const canArchive = Template.instance().userHasAdminEditPermissions;

		return (canEdit || canArchive);
	},

	'hasSourceLabel': function () {
		return Template.instance().orgHasGlobalAndLocalCustomization;
	},

	'getSourceLabel': function (curriculumBank) {
		const org = Template.instance().currentOrg;
		if (Template.instance().orgHasGlobalAndLocalCustomization) {
			return (curriculumBank.bankId === org._id) ? "Local" : "Global";
		}
	}
});

Template.curriculumBank.events({
	"click .page-item-set": async function (event, template) {
		const action = $(event.currentTarget).attr("data-action");
		const currentPage = template.page.get();

		if (action === "add" && currentPage < (template.totalResults.get() / BANK_LIMIT) - 1) {
			if (template.previousPage.get()) {
				template.savedData.set([]);
			}

			template.nextPage.set(true)
			template.previousPage.set(false);
			template.page.set(currentPage + 1);

			await refreshResults(template, false, action);
		} else if (action === "subtract" && currentPage >= 1) {
			if (template.nextPage.get()) {
				template.savedData.set([]);
			}

			template.nextPage.set(false);
			template.previousPage.set(true);
			template.page.set(currentPage - 1);

			await refreshResults(template, false, action);
		}
	},

	"change #filterPublished": async function (event, template) {
		template.savedData.set([]);
		const filterVal = $("#filterPublished").prop('checked');

		if (filterVal === true) {
			template.filterApproval.set(false);
			$("#filterApproval").prop('checked', false);
		}

		template.filterPublished.set(filterVal);

		await refreshResults(template, true);
	},

	"change #inputFilterAge": async (event, template) => {
		template.savedData.set([]);
		const selection = $(event.currentTarget).val();
		template.filterAgeGroup.set(selection);

		await refreshResults(template, true);
	},

	"change #filterDraft": async function (event, template) {
		template.savedData.set([]);
		const filterVal = $("#filterDraft").prop('checked');
		template.filterDraft.set(filterVal);

		if (filterVal === true) {
			template.filterApproval.set(false);
			$("#filterApproval").prop('checked', false);
		}

		await refreshResults(template, true);
	},

	"change #filterApproval": async function (event, template) {
		template.savedData.set([]);
		const filterVal = $("#filterApproval").prop('checked');
		template.filterApproval.set(filterVal);

		if (filterVal === true) {
			template.filterDraft.set(false);
			template.filterPublished.set(false);
			$("#filterDraft").prop("checked", false);
			$("#filterPublished").prop('checked', false);
		}

		await refreshResults(template, true);
	},

	"click #newCurriculumBankLink": function (event, template) {
		event.preventDefault();
		const personType = Meteor.user()?.fetchPerson?.()?.type || USER_TYPES.PERSON;
		const adminPreCheck = function () {
			return new Promise(resolve => {
				mpSwal.fire({
					title: "Publish Now?",
					showCancelButton: false,
					html: `<div id='frmAdminPublish'>
						<div class="radio-list">
							<label class="radio radio-primary">
								<input type="radio" name="publish" value="yes" checked>
								<span></span>
								Yes, publish
							</label>
							<label class="radio radio-primary">
								<input type="radio" name="publish" value="no">
								<span></span>
								Set as Draft
							</label>
						</div></div><br/>
						`,
					preConfirm: function () {
						return new Promise(function (resolve) {
							resolve([
								$('#frmAdminPublish input[name=publish]:checked').val(),
							]);
						});
					}
				}).then(result => {
					const options = {};

					if (result?.value?.[0] === "yes"){
						options.publish = true;
					}

					resolve(options);
				})
			})
		}

		if (template.currentOrg.hasCustomization(AvailableCustomizations.CURRICULUM_BANK_REQUIRE_THEMES)) {
			showModal("simpleModal", {
				title: "Create New Activity",
				template: "_curriculumBuilderAddActivityModal",
				data: {hideCopy: true},
				onSave: (e, i, formFieldData) => {

					if (formFieldData.notes) {
						// backwards compatability
						formFieldData.message = formFieldData.notes;
						delete formFieldData.notes;
					}

					formFieldData.selectedTypes = $("#inputCurriculumAddActivityTypes").val();
					formFieldData.selectedStandards = $("#inputCurriculumAddActivityStandards").val();
					formFieldData.selectedAgeGroup = $("#inputAge").val();

					const submitCurriculumForm = function () {
						if (personType === USER_TYPES.ADMIN) {
							adminPreCheck().then(options => {
								options.data = formFieldData;
								Meteor.callAsync("createCurriculumBankRecord", options).then(async result => {
									if (result) {
										mpSwal.fire("Success", "Activity created successfully", "success");
										await refreshResults(template);
									}
									hideModal("#simpleModal");
								}).catch(error => {
									$(e.target).html('Save').prop("disabled", false);
									mpSwal.fire("Error", error.reason, "error");
								});
							})
						} else {
							Meteor.callAsync("createCurriculumBankRecord", {data: formFieldData}).then(async result => {
								if (result) {
									mpSwal.fire("Success", "Activity created successfully", "success");
									await refreshResults(template);
								}

								hideModal("#simpleModal");
							}).catch(error => {
								$(e.target).html('Save').prop("disabled", false);
								mpSwal.fire("Error", error.reason, "error");
							});
						}

						checkForNewMessages(NEW_MESSAGES_SESSION);
					}

					const uploadFile = document.getElementById("curriculumInputFile");

					if (uploadFile && uploadFile.files.length > 0) {
						processFilesAndSend(formFieldData, uploadFile, submitCurriculumForm, e)
					} else {
						submitCurriculumForm();
					}
				}
			}, "#simpleModal");
		} else {
			const submitCurriculumFormModal = function (data) {
				delete data.scheduledDate;
				delete data.selectedGroups;

				if (personType === USER_TYPES.ADMIN) {
					adminPreCheck().then(options => {
						options.data = data;
						Meteor.callAsync("createCurriculumBankRecord", options).then(async result => {
							if (result) {
								mpSwal.fire("Success", "Activity created successfully", "success");
								await refreshResults(template);
							}

							hideModal("#_curriculumFormModal");
						}).catch(error => {
							mpSwal.fire("Error", error.reason, "error");
						});
					})
				} else {
					Meteor.callAsync("createCurriculumBankRecord", {data}).then(async result => {
						if (result) {
							mpSwal.fire("Success", "Activity created successfully", "success");
							await refreshResults(template);
						}

						hideModal("#_curriculumFormModal")
					}).catch(error => {
						mpSwal.fire("Error", error.reason, "error");
					});
				}

				checkForNewMessages(NEW_MESSAGES_SESSION);
			}

			showModal("_curriculumFormModal", {
				onSave: submitCurriculumFormModal,
				bankDataEntry: true
			}, "#_curriculumFormModal")
		}
	},

	"click .publishCurriculumBankLink": function (event, template) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");
		Meteor.callAsync("publishUnpublishCurriculumBankRecord", _id, true ).then(async result => {
			if (result) {
				mpSwal.fire("Success", "Activity published successfully", "success");
				await refreshResults(template);
			}
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	},

	"click .unpublishCurriculumBankLink": function (event, template) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");
		Meteor.callAsync("publishUnpublishCurriculumBankRecord", _id, false).then(async result => {
			if (result) {
				mpSwal.fire("Success", "Activity unpublished successfully", "success");
				await refreshResults(template);
			}
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	},

	"click .deleteCurriculumBankLink": function (event, template) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");

		mpSwal.fire({
			title: "Are you sure?",
			text: "You cannot recover this Activity once deleted!",
			icon: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, delete it!"
		}).then(result => {
			if (result.value) {
				Meteor.callAsync("deleteCurriculumBankRecord", _id).then(async result => {
					if (result) {
						mpSwal.fire("Success", "Activity deleted successfully", "success");
						await refreshResults(template);
					}
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},

	"click .reviewCurriculumBankLink": function(event) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");

		FlowRouter.go(`/activities/review/${_id}`);
	},

	"click .notificationClose": function(event) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");

		Meteor.callAsync("dismissCurriculumBankMessageNotification", _id).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	},

	"click #btn-search-definitions": async function (event, template) {
		template.savedData.set([]);

		await refreshResults(template, true);
	},

	"click .editCurriculumDefinitionLink": async function (event, template) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");
		const bankObj = await Meteor.callAsync('getCurriculumBankRecordById', _id);
		const personType = Meteor.user()?.fetchPerson?.()?.type || USER_TYPES.PERSON;

		const adminPreCheck = function () {
			if (bankObj?.published && personType === USER_TYPES.ADMIN) {
				return new Promise(resolve => {
					mpSwal.fire({
						title: "Edit Scheduled Activities(s)",
						showCancelButton: false,
						html: `<div id='frmAdminPublish'>
						<div class="mt-4 radio-list">
							<label class="radio radio-primary">
								<input type="radio" name="activityEdit" value="current">
								<span></span>
								Today's activities
							</label>
							<label class="radio radio-primary">
								<input type="radio" name="activityEdit" value="currentFuture" checked>
								<span></span>
								Today's and future activities
							</label>
							<label class="radio radio-primary">
								<input type="radio" name="activityEdit" value="all">
								<span></span>
								All activities
							</label>
						</div></div><br/>
							`,
						preConfirm: function () {
							return new Promise(function (resolve) {
								resolve([
									$('#frmAdminPublish input[name=activityEdit]:checked').val(),
								]);
							});
						}
					}).then(result => {
						const options = {};
						options.propagate = result?.value?.[0] ?? "currentFuture";
						resolve(options);
					})
				})
			} else {
				return new Promise(resolve => resolve({propagate: false}))
			}
		}


		if (template.currentOrg.hasCustomization(AvailableCustomizations.CURRICULUM_BANK_REQUIRE_THEMES)) {
			showModal("simpleModal", {
				title: "Update Activity",
				template: "_curriculumBuilderAddActivityModal",
				data: {hideCopy: true, pendingCurriculum: bankObj.sourceData},
				onSave: (e, i, formFieldData) => {

					if (formFieldData.notes) {
						// backwards compatability
						formFieldData.message = formFieldData.notes;
						delete formFieldData.notes;
					}

					formFieldData.selectedTypes = $("#inputCurriculumAddActivityTypes").val();
					formFieldData.selectedStandards = $("#inputCurriculumAddActivityStandards").val();
					formFieldData.selectedAgeGroup = $("#inputAge").val();
					formFieldData.removeFiles = $.map($("input[name='remove-current-file']:checked"), (n) => {
						return n.value
					})

					delete formFieldData['remove-current-file'];

					const submitCurriculumForm = function () {
						adminPreCheck().then(options => {
							Meteor.callAsync("updateCurriculumBankRecord", {data: formFieldData, _id, ...options}).then(async result => {
								if (result) {
									mpSwal.fire("Success", "Activity updated successfully", "success");
									await refreshResults(template);
								}
								hideModal("#simpleModal");
							}).catch(error => {
								$(e.target).html('Save').prop("disabled", false);
								mpSwal.fire("Error", error.reason, "error");
							});
						})
					}

					const uploadFile = document.getElementById("curriculumInputFile");

					if (uploadFile && uploadFile.files.length > 0) {
						processFilesAndSend(formFieldData, uploadFile, submitCurriculumForm, e)
					} else {
						submitCurriculumForm();
					}
				}
			}, "#simpleModal");
		} else {
			const submitCurriculumFormModal = function (data) {
				delete data.scheduledDate;
				delete data.selectedGroups;
				adminPreCheck().then(options => {
					Meteor.callAsync("updateCurriculumBankRecord", {data, _id, ...options}).then(async result => {
						if (result) {
							mpSwal.fire("Success", "Activity updated successfully", "success");
							await refreshResults(template);
						}

						hideModal("#_curriculumFormModal");
					}).catch(error => {
						$(event.target).html('Save').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				})
			}

			showModal("_curriculumFormModal", {
				onSave: submitCurriculumFormModal,
				bankDataEntry: true,
				pendingCurriculum: bankObj.sourceData
			}, "#_curriculumFormModal")
		}
	},

	"click .archiveCurriculumDefinitionLink": function(event, template) {
		event.preventDefault();
		const activityId = $(event.currentTarget).data("id");

		mpSwal.fire({
			title:"Are you sure?",
			text:"Archiving this activity will prevent it from appearing in future search results.",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, archive it"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("archiveCurriculum", activityId).then(async () => {
					await refreshResults(template);
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	}
});

function processFilesAndSend(formFieldData, uploadFile, submitCurriculumForm, event) {
	formFieldData.newMediaFile = [];
	let uploadsCompleted = 0;

	_.each(uploadFile.files, (file) => {
		const metaContext = {tokenId: StringUtils.tokenString()};
		const uploader = new Slingshot.Upload("myDocumentUploads", metaContext);

		uploader.send(file, function (error, downloadUrl) {
			if (error) {
				alert (error);
				$(event.target).html('Save').prop("disabled", false);
			} else {
				downloadUrl = remapDownloadUrl(downloadUrl);
				const mediaType = file.type;
				const uploadedFile = {
					name: file.name,
					mediaUrl: downloadUrl,
					mediaToken: metaContext.tokenId,
					mediaFileType: mediaType,
					mediaPath: Meteor.user().orgId + "/" + Meteor.user()._id + "/" + metaContext.tokenId
				};
				
				formFieldData.newMediaFile.push(uploadedFile);
				uploadsCompleted++;

				if (uploadsCompleted >= uploadFile.files.length) {
					submitCurriculumForm();
				}
			}
		});
	});
}

async function refreshResults(instance, resetPage = false, action = "") {
	instance.searchResults.set(null);

	if (resetPage) {
		instance.page.set(0);
	}

	let search = $("input[name='search-definitions']").val() || "";

	let skip = instance.page.get() * BANK_LIMIT;
	const query = {};
	const filterPublished = instance.filterPublished.get();
	const filterDraft = instance.filterDraft.get();
	const filterApproval = instance.filterApproval.get();
	let savedData = instance.savedData.get();
	const filterAgeGroup = instance.filterAgeGroup.get();

	if (filterPublished) {
		query.published = true;
	} else if (filterDraft) {
		query.published = false;
	}

	if (filterDraft && filterPublished) {
		delete query.published
	}

	if (filterApproval) {
		query.waitingForApproval = {$ne: null};
	}


	if (filterAgeGroup && search.length > 0) {
		const searchRegex = new RegExp(search, "i");
		query["$and"] = [
			{$or: [{"sourceData.selectedAgeGroup": filterAgeGroup}, {"updateData.selectedAgeGroup": filterAgeGroup}]},
			{$or: [{"sourceData.headline": searchRegex}, {"updateData.headline": searchRegex}]}
		]
	}

	if (filterAgeGroup && !search.length) {
		// AgeGroup should be filter on 2 fields sourceData.selectedAgeGroup or updateData.selectedAgeGroup.
		query["$or"] = [{"sourceData.selectedAgeGroup": filterAgeGroup}, {"updateData.selectedAgeGroup": filterAgeGroup}];
	}

	if (!filterAgeGroup && search.length > 0) {
		const searchRegex = new RegExp(search, "i");
		query["$or"] = [{"sourceData.headline": searchRegex}, {"updateData.headline": searchRegex}];
	}

	if (!savedData.length) {
		instance.bankLimit.set(BANK_LIMIT * 2)
		skip = instance.page.get() * BANK_LIMIT;
		if (action === 'subtract') {
			if (instance.page.get() !== 0) {
				skip = ((instance.page.get() - 1) * (BANK_LIMIT));
			} else {
				skip = ((instance.page.get()) * (BANK_LIMIT * 2));
			}
		}
	}

	if (savedData.length) {
		if (action === 'add') {
			skip = (instance.page.get() + 1) * BANK_LIMIT;
			instance.bankLimit.set(BANK_LIMIT)
		}

		if (action === 'subtract') {
			if (instance.page.get() === 0) {
				instance.bankLimit.set(BANK_LIMIT)
				skip = (instance.page.get()) * instance.bankLimit.get();
			} else {
				instance.bankLimit.set(BANK_LIMIT)
				skip = (instance.page.get() - 1) * instance.bankLimit.get();
			}

		}

	}

	Meteor.callAsync('getPaginatedCurriculumBankData', query, skip, instance.bankLimit.get(), {waitingForApproval: -1}).then(result => {
		reorderResults(result);

		let displayItems;
		if (savedData.length) {
			displayItems = instance.savedData.get();
			instance.savedData.set(result);
		} else {
			displayItems = result.slice(0, BANK_LIMIT);
			instance.savedData.set(result.slice(BANK_LIMIT));
			if (action === 'subtract') {
				if (instance.page.get() !== 0) {
					displayItems = result.slice(BANK_LIMIT);
					instance.savedData.set(result.slice(0, BANK_LIMIT));
				}
			} else {
				displayItems = result.slice(0, BANK_LIMIT);
				instance.savedData.set(result.slice(BANK_LIMIT));
			}

		}
		instance.searchResults.set(displayItems);

		Meteor.callAsync('getTotalCurriculumBankCount', query).then(totalCount => {
			instance.totalResults.set(totalCount);
		});
	})
		.catch(error => {
			Log.error(error);
			mpSwal.fire("Error", error.reason || error.message, "error");
			instance.searchResults.set([]);
		});
}

function getActivitiesAgeGroup(curriculumBank) {
	return curriculumBank.sourceData?.selectedAgeGroup || curriculumBank.updateData?.selectedAgeGroup;
}

function reorderResults(curriculumBanks) {
	const currentPerson = Meteor.user() && Meteor.user().fetchPerson();
	if (currentPerson.type === USER_TYPES.STAFF && currentPerson.defaultGroupId) {
		const defaultGroup = Groups.findOne({_id: currentPerson.defaultGroupId});

		if (defaultGroup && defaultGroup.activitiesAgeGroup) {
			for (const curriculumBank of curriculumBanks) {
				// if the activity matches the teacher default group Activities Age Group (from the group configuration), then move it to the top of the array
				if (getActivitiesAgeGroup(curriculumBank) === defaultGroup.activitiesAgeGroup) {
					const index = curriculumBanks.indexOf(curriculumBank);
					if (index !== -1) {
						curriculumBanks.unshift(curriculumBanks.splice(index, 1)[0]);
					}
				}
			}
		}
	}
}

function checkForNewMessages(sessionName) {
	if (!sessionName) {
		return;
	}

	const personId = Meteor.user()?.fetchPerson()?._id;
	if (personId) {
		Meteor.callAsync('getPersonMessages', personId).then(result => {
			if (result?.curriculumBankNotifications?.length > 0) {
				Session.set(sessionName, result.curriculumBankNotifications);
			}
		}).catch(error => {
			const message = error.reason || error.message;
			Log.error(`Error fetching curriculum bank messages: ${message}`);
		});
	}
}
