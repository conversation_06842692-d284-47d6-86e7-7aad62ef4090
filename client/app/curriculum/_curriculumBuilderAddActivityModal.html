<template name="_curriculumBuilderAddActivityModal">
	{{#with activityData}}
	<form id="formCurriculumAddActivity">
		<input type="hidden" id="inputOriginalCurriculumId" value="">
    {{#if canShowCopy}}
      <div class="form-group row" id="copy-btn-label">
        <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
        <div class="col-lg-9 col-xl-6">
          <button class="btn btn-primary font-weight-bolder" id="show-copy-activity-panel">Copy Existing Activity</button>
        </div>
      </div>
      <div class="form-group row" id="copy-btn-select" hidden>
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Copy Existing Activity</label>
        <div class="col-lg-9 col-xl-6">
          <select class="form-control form-control-lg" name="existingActivities" id="inputExistingActivities">
						<option value="">Select to Search</option>
          </select>
          <br/>
          <button class="btn btn-primary font-weight-bolder" id="copy-activity" style="margin-top:6px">Copy</button>
        </div>
      </div>
    {{/if}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Title</label>
      <div class="col-lg-9 col-xl-6">
        <input type="text" class="form-control form-control-lg" name="headline" value="{{headline}}">
      </div>
    </div>
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Choose {{ getActivitiesTag }}</label>
      <div class="col-lg-9 col-xl-6">
        <select class="form-control form-control-lg" multiple id="inputCurriculumAddActivityTypes">
          {{#each availableCurriculumTypes}}
  				    <option value="{{this}}" selected={{isTypeSelected this}}>{{replaceBackticks this}}</option>
  				{{/each}}
        </select>
      </div>
    </div>
    {{#if availableAgeGroups}}
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Age</label>
        <div class="col-lg-9 col-xl-6">
          <select class="form-control form-control-lg" id="inputAge">
						<option value=""></option>
            {{#each ageGroup in availableAgeGroups}}
    					<option value="{{ageGroup.label}}" {{selectedIfEqual selectedAgeGroup ageGroup.label}}>{{ageGroup.label}}</option>
    				{{/each}}
          </select>
        </div>
      </div>
    {{/if}}
    {{#if showCurriculumStandards}}
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Standard(s)</label>
        <div class="col-lg-9 col-xl-6">
          <select class="form-control form-control-lg" multiple id="inputCurriculumAddActivityStandards">
            {{#each availableCurriculumStandards}}
    				  <option label="{{source}}|{{standardId}}" value="{{source}}|{{standardId}}" selected={{isStandardSelected source standardId}}>{{label}}</option>
    				{{/each}}
          </select>
        </div>
      </div>
    {{/if}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Message</label>
      <div class="col-lg-9 col-xl-6">
        <textarea class="form-control form-control-lg" rows="3" placeholder="Notes" id="inputMessage" name="notes">{{message}}</textarea>
      </div>
    </div>
    {{#if showMaterials}}
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Materials</label>
        <div class="col-lg-9 col-xl-6">
          <textarea class="form-control form-control-lg" rows="3" placeholder="Materials" id="inputMaterials" name="materials">{{materials}}</textarea>
        </div>
      </div>
    {{/if}}
    {{#if showHomework}}
      <div class="form-group row">
        <label class="col-xl-3 col-lg-3 text-right col-form-label">Homework</label>
        <div class="col-lg-9 col-xl-6">
          <textarea class="form-control form-control-lg" rows="3" placeholder="Homework" id="inputHomework" name="homework">{{homework}}</textarea>
        </div>
      </div>
    {{/if}}
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Internal Notes</label>
      <div class="col-lg-9 col-xl-6">
        <textarea class="form-control form-control-lg" rows="3" placeholder="Internal notes (Viewable by Staff/Admins only)" id="inputInternalNotes" name="internalNotes">{{internalNotes}}</textarea>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Internal Link</label>
      <div class="col-lg-9 col-xl-6">
        <input type="text" class="form-control form-control-lg" id="inputInternalLink" name="internalLink" placeholder="Internal Link (Viewable by Staff/Admins only)" value="{{internalLink}}">
      </div>
    </div>
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right">Current Attachments</label>
      <div class="col-lg-9 col-xl-6">
        <div class="checkbox-list">
          {{#each mediaFiles}}
            <label class="checkbox checkbox-primary">
              <input type="checkbox" name="remove-current-file" value="{{mediaToken}}"/>
              <span></span>
              Remove {{mediaName}}
            </label>
          {{/each}}
        </div>
      </div>
    </div>
    <div class="form-group row">
      <label class="col-xl-3 col-lg-3 text-right col-form-label">Attach PDF(s)</label>
      <div class="col-lg-9 col-xl-6">
        <label id="curriculumInputLabel" for="curriculumInputFile" class="btn btn-primary font-weight-bolder">
          <input type="file" id="curriculumInputFile" multiple style="display:none;"> 
          <i class="fad fa-cloud-upload"></i> Attach Media/File(s)
        </label>
        <span id="curriculumFileLabelSpan"></span>
      </div>
    </div>
	</form>
	{{/with}}
</template>
