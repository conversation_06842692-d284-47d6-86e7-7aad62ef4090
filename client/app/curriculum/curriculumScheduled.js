import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { Curriculum } from '../../../lib/collections/curriculum';
import { processPermissions } from '../../../lib/permissions';
import { showModal } from "../main";
import _ from '../../../lib/util/underscore';
import './curriculumBankActivityModal';
import './_curriculumFormModal';
import './curriculumScheduled.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import moment from 'moment-timezone';
import { offlinePreventCheck } from '../../../mpweb';

Template.curriculumScheduled.onCreated(function () {
	this.filterGroup = new ReactiveVar("");
	this.filterStandard = new ReactiveVar("");
	const startDate = new moment().format("MM/DD/YYYY"),
		endDate = new moment().add(7, "days").format("MM/DD/YYYY");

	this.startDate = new ReactiveVar(startDate);
	this.endDate = new ReactiveVar(endDate);

	this.filterText = new ReactiveVar("");
	this.curriculumFetchedRecord = new ReactiveVar([]);
	this.curriculumsThemeFetchedRecord = new ReactiveVar([]);

	var self = this;
	self.autorun(() => {
		var sd = new moment(self.startDate.get(), "MM/DD/YYYY").format("MM/DD/YYYY");
		var ed = new moment(self.endDate.get(), "MM/DD/YYYY").format("MM/DD/YYYY");
		Meteor.callAsync('getCurriculumData', { startDate: sd, endDate: ed },{sort: {scheduledDate: 1}}).then(res => {
			this.curriculumFetchedRecord.set(res);
		}).catch(err => {
			mpSwal.fire("Error", err.reason || err.message, "error");
			this.isLoading.set(false);
		});
		Meteor.callAsync('getCurriculumThemesData', { startDate: sd, endDate: ed }).then(res => {
			this.curriculumsThemeFetchedRecord.set(res);
		}).catch(err => {
			mpSwal.fire("Error", err.reason || err.message, "error");
			this.isLoading.set(false);
		});
	});

});

Template.curriculumScheduled.onRendered(function () {
	var self = this;
	$("#startDate").datepicker({ autoclose: true, todayHighlight: true }).on("changeDate", function (e) {
		var d = new moment(e.date).format("MM/DD/YYYY");
		self.startDate.set(d)
	});

	$("#endDate").datepicker({ autoclose: true, todayHighlight: true }).on("changeDate", function (e) {
		var d = new moment(e.date).format("MM/DD/YYYY");
		self.endDate.set(d);
	});

	handleTabHistory();
});

Template.curriculumScheduled.events({
	'click #newCurriculumLink': function (event, template) {
		if (offlinePreventCheck()) return false;
		const org = Orgs.current();
		Session.set("curriculumId", "");

		if (org.hasCustomization("curriculumBank/activities")) {
			showModal("curriculumBankActivityModal", { showSchedulingFields: true }, "#curriculumBankActivityModal");
		} else {
			showModal("_curriculumFormModal", {}, "#_curriculumFormModal")
		}

	},
	"click .manageThemeLink": (e, i) => {
		e.preventDefault();
		var id = $(e.currentTarget).attr("data-id");
		FlowRouter.go("/activities/builder/" + id);
	},
	'click #newThemeLink': function (e, i) {
		FlowRouter.go("/activities/builder");
	},
	'click .editCurriculumLink': async function (event) {
		event.preventDefault();
		const activityId = $(event.target).attr("data-id");
		if (offlinePreventCheck()) return false;
		Session.set("curriculumId", activityId);
		const org = Orgs.current();

		if (org.hasCustomization("curriculumBank/activities")) {
			let curriculum;
			await Meteor.callAsync('getCurriculumById', activityId).then((res) => {
				curriculum = new Curriculum(res);
			});
			showModal("curriculumBankActivityModal", { curriculum, showSchedulingFields: true }, "#curriculumBankActivityModal");
		} else {
			showModal("_curriculumFormModal", { curriculumId: activityId }, "#_curriculumFormModal")
		}

	},
	'click .deleteCurriculumLink': function (event) {
		if (offlinePreventCheck()) return false;
		var curriculumId = $(event.target).attr("data-id");
		return mpSwal.fire({
			title: "Are you sure?",
			text: "You will not be able to recover this curriculum once deleted!",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, delete it!"
		}).then(async result => {
			if (result.value) await Meteor.callAsync("deleteCurriculum", curriculumId);
		});
	},
	"change #filterGroup": function (event, template) {
		template.filterGroup.set($("#filterGroup").val());
	},
	"change #filterStandard": function (event, template) {
		template.filterStandard.set($("#filterStandard").val());
	},
	"click .headlineLink": function (event) {
		if (offlinePreventCheck()) return false;
		Session.set("curriculumId", this._id);
		$("#curriculumFormModal").modal();
	},
	"change #searchText": function (e, i) {
		i.filterText.set($("#searchText").val());
	},
	"submit form": function (e, i) {
		e.preventDefault();
	},

});



Template.curriculumScheduled.helpers({
	'userCanAddOrModifyCurriculum': function () {
		return processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff"
		});
	},
	'curriculum': function () {
		const curriculumResult = filterCurriculumsAndThemes(Template.instance(), Orgs.current());
		return curriculumResult;
	},
	'scheduledDateFormatted': function () {
		return moment(this.scheduledDate).format("MM/DD/YYYY");
	},
	"groups": function () {
		return Groups.find({}, { sort: { name: 1 } }).fetch();
	},
	'startDate': function () {
		return Template.instance().startDate.get();
	},
	'endDate': function () {
		return Template.instance().endDate.get();
	},
	'requireThemes': function () {
		return Orgs.current().hasCustomization("modules/curriculum/requireThemes");
	},
	'formatScheduledDays': function (scheduledDays) {
		const startDay = _.min(scheduledDays), endDay = _.max(scheduledDays);
		return new moment(startDay).format("MM/DD/YYYY") +
			(startDay != endDay ? "-" + new moment(endDay).format("MM/DD/YYYY") : "");
	},
	'sendToAll': function() {
        return (this.selectedGroups && this.selectedGroups.length == 0);
    },
	'findRecipientGroups': function () {
		return Groups.find({_id: {$in: this.selectedGroups || [] }}).fetch();
	},
	'findTheme': function() {
		const themes = Template.instance().curriculumsThemeFetchedRecord.get();
		return themes.filter((t)=>{
			return t._id === this.curriculumThemeId
		})[0]
	},
	'getRecipientGroupList': function (recipients) {
		const arr = [];
		_.each(recipients, (r) => {
			arr.push(r.name)
		});
		return arr.join(', ');
	},
	showConfigure() {
		return processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff"
		});
	}
});

function filterCurriculumsAndThemes(instance, org) {
    let curriculums = instance.curriculumFetchedRecord.get();
    let themes = instance.curriculumsThemeFetchedRecord.get();
    let filterGroup = instance.filterGroup.get();
    let filterStandard = instance.filterStandard.get();
    let searchText = instance.filterText.get();
 
	let query = {scheduledDate: 
		{$gte: new moment.tz(Template.instance().startDate.get(), "MM/DD/YYYY", org.getTimezone()).startOf('day').valueOf(), 
		 $lt: new moment.tz(Template.instance().endDate.get(), "MM/DD/YYYY", org.getTimezone()).endOf('day').valueOf() } };
	const themeQuery = {selectedDays: query["scheduledDate"]};
 
    // Apply group filter if specified
    if (filterGroup || filterGroup !== "") {
        query.selectedGroups = filterGroup;
        themeQuery.selectedGroups = filterGroup;
    }
 
    // Apply standard filter if specified
    if (filterStandard !== "") {
        query.selectedStandards = filterStandard;
    }
 
    // Apply search text filter if specified
    if ((searchText || searchText !== "") && curriculums) {
        const searchEx = new RegExp(escapeRegExp(searchText), "i");
        curriculums = curriculums.filter(c => c.headline.search(searchEx) >= 0 || c.message.search(searchEx) >= 0);
    }
	

    // Filter curriculums based on query
	if(query.selectedGroups){
		curriculums = curriculums.filter(curriculum => {
			let match = true;
				if (Array.isArray(curriculum["selectedGroups"])) {
					if (!curriculum["selectedGroups"].includes(query.selectedGroups)) {
						match = false;
					}
				}
			return match;
		});
	}
	if(themeQuery.selectedGroups){
		// Filter themes based on themeQuery
		themes = themes.filter(theme => {
			let match = true;
				if (Array.isArray(theme["selectedGroups"])) {
					if (!theme["selectedGroups"].includes(themeQuery.selectedGroups)) {
						match = false;
					}
				} 
			
			return match;
		});
	}
	if(themeQuery.selectedDays){
		themes = themes.filter(item => {
			return item.selectedDays.some(selectedDay => 
				(selectedDay >= themeQuery.selectedDays.$gte && 
				selectedDay < themeQuery.selectedDays.$lt)
			)
		});
	}
 
    // Initialize result object
    let result = { allCurriculums: curriculums, unmatchedThemes: [] };
 
    // Check for matching themes and unmatched curriculums
    if (themes && curriculums) {
        themes.forEach(mt => {
            if (curriculums.filter(c => c.curriculumThemeId == mt._id).length == 0) {
                result.unmatchedThemes.push(mt);
            }
        });
    }
 
    // Check if result is empty
    result.empty = curriculums.length == 0 && result.unmatchedThemes.length == 0;
 
    return result;
}
function escapeRegExp(string) {
	return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
}
