<template name="curriculumThemeBankList">
  <div class="d-flex flex-column-fluid">
    <div data-cy="theme-container" class="container">
      {{#if userCanAddOrModifyCurriculumThemeBank}}
      <div class="d-flex flex-row justify-content-end mb-4">
        <div data-cy="create-new-theme-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="newCurriculumThemeBankLink">
          <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Create New Theme
        </div>
      </div>
    {{/if}}
      <div class="card card-custom gutter-b">
        <div class="card-body">
          <div class="row">
            <div class="col-md-4 col-sm-4">
              <div class="form-group">
                <input data-cy="input-theme-search" name="search-definitions" type="text" class="form-control">
              </div>
            </div>
            <div class="col-md-2 col-sm-2">
              <div data-cy="search-theme-btn" class="btn btn-primary font-weight-bolder" id="btn-search-definitions">Search</div>
            </div>
            <div class="col-md-6 col-sm-6">
              <div class="checkbox-inline">
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" name="filterPublished" id="filterPublished" data-cy="filter-publish-chkbox" />
                  <span></span>
                  Published
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" name="filterDraft" id="filterDraft" data-cy="filter-draft-chkbox"/>
                  <span></span>
                  Draft
                </label>
                <label class="checkbox checkbox-primary">
                  <input type="checkbox" name="filterApproval" id="filterApproval" data-cy="filter-approval-chkbox"/>
                  <span></span>
                  Waiting for Approval
                </label>
              </div>
              <span class="form-text text-muted">Theme Filter</span>
            </div>
          </div>
          {{#if hasCurriculumThemeBankNotification}}
            <div class="d-flex flex-row mt-2">
              <span class="font-weight-bolder font-size-h3">My Theme Notifications</span>
            </div>
          {{/if}}
          {{#each notification in curriculumThemeBankNotifications}}
            <div class="d-flex flex-row align-items-center mt-2">
              <div class="alert alert-custom alert-notice {{getStatusColor notification.status}} fade show max-w-600px" id="no-announcement-alert" role="alert">
                <div class="d-flex flex-column">
                  <div class="alert-text font-size-h5 font-weight-bold">{{notification.title}} - {{notification.status}}</div>
                  {{#if notification.note}}
                    <div class="alert-text ml-4">{{notification.note}}</div>
                  {{/if}}
                  {{#if notification.publishNote}}
                    <div class="alert-text ml-4">{{notification.publishNote}}</div>
                  {{/if}}
                </div>
                {{#if canDismissNotification notification.status}}
                  <div class="alert-close">
                    <button type="button" class="close notificationClose" data-id="{{notification._id}}" data-dismiss="alert" aria-label="Close">
                      <span aria-hidden="true"><i class="icon-2x fad fa-times"></i></span>
                    </button>
                  </div>
                {{/if}}
              </div>
            </div>
          {{/each}}
        </div>
        <div class="container d-flex flex-row justify-content-end mb-4" >
          <div class="d-flex flex-row ml-6 justify-content-center align-items-center">
            <span class="page-item-set" style="cursor:pointer;" data-action="subtract" data-cy="pagination-previous-btn"><i class="fad fad-primary fa-chevron-left icon-2x mr-4" ></i></span>
            <span class="font-size-h2" data-cy="pagination-records-count">{{pageStart}}-{{pageEnd}} of {{pageTotal}}</span>
            <span class="page-item-set" style="cursor:pointer;" data-action="add" data-cy="pagination-next-btn"><i class="fad fad-primary fa-chevron-right icon-2x ml-4"></i></span>
          </div>
        </div>
      </div>
      {{#each result in results}}
        <div class="card card-custom gutter-b" data-id="{{result._id}}">
          <div class="card-body">
            <div data-cy="theme-item" class="row">
                <div class="d-flex align-items-center col-3">
                  <span data-cy="theme-title" class="font-size-h3 text-primary font-weight-bolder viewThemeLink" data-id="{{result._id}}" style="cursor:pointer;">{{getTitle result}}</span>
                </div>
                {{#if result.createdBy}}
                  <div class="d-flex align-items-center col-2">
                    <div class="d-flex align-items-end justify-content-start">
                      <span class="mr-2">
                        <i class="icon-2x text-bright-blue fad fa-user-plus"></i>
                      </span>
                      <div class="d-flex flex-column text-dark-75">
                        <span class="font-weight-bolder font-size-xs mb-1">Created By</span>
                        <span data-cy="theme-created-by-name" class="font-weight-bolder font-size-lg">{{result.createdBy.name}}</span>
                        <span data-cy="theme-created-by-org" class="font-weight-bold font-size-sm text-muted">{{result.createdBy.orgName}}</span>
                      </div>
                    </div>
                  </div>
                {{/if}}
                {{#if result.modifiedBy}}
                  <div class="d-flex align-items-center col-2">
                    <div class="d-flex align-items-end justify-content-start">
                      <span class="mr-2">
                        <i class="icon-2x text-bright-blue fad fa-user-edit"></i>
                      </span>
                      <div class="d-flex flex-column text-dark-75">
                        <span class="font-weight-bolder font-size-xs mb-1">Modified By</span>
                        <span data-cy="theme-modified-by-name" class="font-weight-bolder font-size-lg">{{result.modifiedBy.name}}</span>
                        <span data-cy="theme-modified-by-org" class="font-weight-bold font-size-sm text-muted">{{result.modifiedBy.orgName}}</span>
                      </div>
                    </div>
                  </div>
                {{/if}}
                {{#if result.updateRequestBy}}
                  <div class="d-flex align-items-center col-2">
                    <div class="d-flex align-items-end justify-content-start">
                      <span class="mr-2">
                        <i class="icon-2x text-bright-blue fad fa-pen-square"></i>
                      </span>
                      <div class="d-flex flex-column text-dark-75">
                        <span class="font-weight-bolder font-size-xs mb-1">Update Requested By</span>
                        <span data-cy="theme-update-requested-by-name" class="font-weight-bolder font-size-lg">{{result.updateRequestBy.name}}</span>
                        <span data-cy="theme-update-requested-by-org" class="font-weight-bold font-size-sm text-muted">{{result.updateRequestBy.orgName}}</span>
                      </div>
                    </div>
                  </div>
                {{else}}
                  {{#if result.approvedBy}}
                    <div class="d-flex align-items-center col-2">
                      <div class="d-flex align-items-end justify-content-start">
                        <span class="mr-2">
                          <i class="icon-2x text-bright-blue fad fa-user-check"></i>
                        </span>
                        <div class="d-flex flex-column text-dark-75">
                          <span class="font-weight-bolder font-size-xs mb-1">Approved By</span>
                          <span data-cy="theme-approved-by-name" class="font-weight-bolder font-size-lg">{{result.approvedBy.name}}</span>
                          <span data-cy="theme-approved-by-org" class="font-weight-bold font-size-sm text-muted">{{result.approvedBy.orgName}}</span>
                        </div>
                      </div>
                    </div>
                  {{/if}}
                {{/if}}
              {{#if canManageContent result}}
                <div class="d-flex justify-content-end align-items-center {{getColSize result}}">
                  <div class="d-flex flex-row align-items-center justify-content-end">
                    <div class="dropdown">
                      <div data-cy="dropdown-theme-options" class="btn {{getActionBtnColor result}} font-weight-bolder btn-text-white" id="btnCurriculumThemeBankActions" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                      {{getActionDropdownLabel result}}<i class="fad-regular fad fa-angle-down ml-4 text-white"></i>
                      </div>
                      <div class="dropdown-menu dropdown-menu-right" aria-labelledby="btnCurriculumThemeBankActions">
                        {{#if result.waitingForApproval}}
                          {{#if canApprove result}}
                            <span data-cy="review-theme" class="dropdown-item clickable-row reviewCurriculumThemeBankLink" data-id="{{result._id}}">Review</span>
                          {{else}}
                            <span data-cy="locked-theme" class="dropdown-item clickable-row">Locked Until Review Complete</span>
                          {{/if}}
                        {{else}}
                          {{#if result.published}}
                            <span data-cy="copy-to-new-theme" class="dropdown-item clickable-row copyCurriculumThemeDefinitionLink" data-id="{{result._id}}">Copy to New Theme</span>
                          {{/if}}
                          {{#if canEdit result}}
                            <span data-cy="edit-theme" class="dropdown-item clickable-row editCurriculumThemeDefinitionLink" data-id="{{result._id}}">Edit</span>
                          {{/if}}
                          {{#if canPublish result}}
                            {{#if result.published}}
                              <span data-cy="unpublish-theme" class="dropdown-item clickable-row unpublishCurriculumThemeBankLink" data-id="{{result._id}}">Unpublish</span>
                            {{else}}
                              <span data-cy="publish-theme" class="dropdown-item clickable-row publishCurriculumThemeBankLink" data-id="{{result._id}}">Publish</span>
                              <span data-cy="delete-theme" class="dropdown-item clickable-row deleteCurriculumThemeBankLink" data-id="{{result._id}}">Delete</span>
                            {{/if}}
                          {{/if}}
                        {{/if}}
                      </div>
                    </div>
                  </div>
                </div>
              {{/if}}
            </div>
          </div>
        </div>
      {{/each}}
    </div>
  </div>
</template>