import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../../lib/collections/orgs';
import { NewMessages } from '../../../../lib/collections/newMessages';
import { processPermissions } from '../../../../lib/permissions';
import { showModal } from "../../main";
import { hideModal } from "../../main";
import './copyModal';
import './newModal';
import './list.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { AvailableCustomizations } from '../../../../lib/customizations';
import { AvailableActionTypes, AvailablePermissions } from '../../../../lib/constants/permissionsConstants';
import { USER_TYPES } from '../../../../lib/constants/profileConstants';
const PAGE_LIMIT = 100;
Template.curriculumThemeBankList.onCreated(function () {
	this.searchResults = new ReactiveVar();
	this.filterApproval = new ReactiveVar(false);
	this.filterDraft = new ReactiveVar(false);
	this.filterPublished = new ReactiveVar(false);
	this.totalCurriculumThemeBank = new ReactiveVar(0);
	this.skip = new ReactiveVar(0);
	this.previousCurriculumThemeBanks = new ReactiveVar([]);
	this.currentCurriculumThemeBanks = new ReactiveVar([]);
	this.nextCurriculumThemeBanks = new ReactiveVar([]);
	this.currentOrg = Orgs.current();

	this.userHasAdminEditPermission = processPermissions({
		assertions: [{ context: AvailablePermissions.THEME_BANK, action: AvailableActionTypes.EDIT}],
		evaluator: (thisPerson) => thisPerson.type === USER_TYPES.ADMIN
	});

	this.userHasAdminOrStaffEditPermissions = processPermissions({
		assertions: [{ context: AvailablePermissions.THEME_BANK, action: AvailableActionTypes.EDIT}],
		evaluator: (thisPerson) => thisPerson.type === USER_TYPES.ADMIN || thisPerson.type === USER_TYPES.STAFF
	});
});

Template.curriculumThemeBankList.onRendered(async function () {
	await refreshResults(Template.instance());
});

Template.curriculumThemeBankList.helpers({
	'getColSize': function (curriculumTheme) {
		const baseColSize = 'col-3';

		if (!curriculumTheme.approvedBy && !curriculumTheme.modifiedBy && !curriculumTheme.updateRequestBy) {
			return 'col-7'
		}

		if ((!curriculumTheme.approvedBy || !curriculumTheme.updateRequestBy) && !curriculumTheme.modifiedBy) {
			return 'col-5'
		}

		return baseColSize;
	},

	'getActionDropdownLabel': function (curriculumTheme) {
		if (curriculumTheme.waitingForApproval) {
			return "Waiting For Approval";
		}

		return curriculumTheme.published ? "Published" : "Draft";
	},

	'getActionBtnColor': function (curriculumTheme) {
		if (curriculumTheme.waitingForApproval) {
			return "btn-info";
		}

		return curriculumTheme.published ? "btn-primary" : "btn-secondary";
	},

	'getTitle': function (curriculumTheme) {
		return curriculumTheme?.sourceData?.name || "N/A";
	},

	'userCanAddOrModifyCurriculumThemeBank': function () {
		return Template.instance().userHasAdminOrStaffEditPermissions;
	},

	'hasCurriculumThemeBankNotification': function () {
		const messageDoc = NewMessages.findOne({});
		return (messageDoc?.curriculumThemeBankNotifications?.length > 0);
	},

	'curriculumThemeBankNotifications': function () {
		const messageDoc = NewMessages.findOne({});
		return messageDoc?.curriculumThemeBankNotifications || [];
	},

	"getStatusColor": function (status) {
		if (status.toLowerCase() === "waiting for approval") {
			return "alert-light-info";
		}

		if (status.toLowerCase() === "approved") {
			return "alert-light-success";
		}

		if (status.toLowerCase() === "rejected") {
			return "alert-light-danger";
		}
	},

	'results': function () {
		return Template.instance().searchResults.get();
	},

	'availableCurriculumTypes': function () {
		return Template.instance().currentOrg.availableCurriculumTypes({ mapped: true });
	},

	'replaceBackticks': function (string) {
		return string.replace('`', ' > ');
	},

	'canDismissNotification': function (status) {
		return status.toLowerCase() !== "waiting for approval";
	},

	'canEdit': function (item) {
		const org = Template.instance().currentOrg;
		const inScope = item && (!(org?.hasCustomization(AvailableCustomizations.CURRICULUM_BANK_GLOBAL_AND_LOCAL)) || item.bankId === org._id);

		return inScope && Template.instance().userHasAdminOrStaffEditPermissions;
	},

	'canArchive': function (item) {
		const org = Template.instance().currentOrg;
		const inScope = item && (!(org?.hasCustomization(AvailableCustomizations.CURRICULUM_BANK_GLOBAL_AND_LOCAL)) || item.bankId === org._id);

		return inScope && Template.instance().userHasAdminEditPermission;
	},

	'canPublish': function (item) {
		const org = Template.instance().currentOrg;
		const inScope = item && (!(org?.hasCustomization(AvailableCustomizations.CURRICULUM_BANK_GLOBAL_AND_LOCAL)) || item.bankId === org._id);

		return inScope && Template.instance().userHasAdminEditPermission;
	},

	'canApprove': function (item) {
		const org = Template.instance().currentOrg;
		const inScope = item && (!(org?.hasCustomization(AvailableCustomizations.CURRICULUM_BANK_GLOBAL_AND_LOCAL)) || item.bankId === org._id);

		return inScope && Template.instance().userHasAdminEditPermission;
	},

	'canManageContent': function (item) {
		const edit = Template.instance().userHasAdminOrStaffEditPermissions;
		const archive = Template.instance().userHasAdminEditPermission;
		const canCopy = item.published;

		return (edit || archive || canCopy);
	},

	'pageStart': function () {
		return (Template.instance().totalCurriculumThemeBank.get() > 0 ? Template.instance().skip.get() + 1 : 0);
	},

	'pageEnd': function () {
		return (Template.instance().totalCurriculumThemeBank.get() > (Template.instance().skip.get() + PAGE_LIMIT) ? Template.instance().skip.get() + PAGE_LIMIT : Template.instance().totalCurriculumThemeBank.get());
	},

	'pageTotal': function () {
		return Template.instance().totalCurriculumThemeBank.get();
	}
});

Template.curriculumThemeBankList.events({
	"click .viewThemeLink": async function (event) {
		const _id = $(event.currentTarget).attr("data-id");
		const theme = await Meteor.callAsync("getCurriculumThemeBankRecordById", _id);

		showModal(
			"curriculumThemeBankNewModal",
			{
				curriculumThemeBankSource: theme.sourceData,
				disabled: true,
				title: "View Theme"
			},
			"#curriculumThemeBankNewModal"
		);
	},

	"change #filterPublished": async function (event, template) {
		const filterVal = $("#filterPublished").prop('checked');

		if (filterVal === true) {
			template.filterApproval.set(false);
			$("#filterApproval").prop('checked', false);
		}

		template.skip.set(0);
		template.filterPublished.set(filterVal);
		await refreshResults(template);
	},

	"change #filterDraft": async function (event, template) {
		const filterVal = $("#filterDraft").prop('checked');
		template.filterDraft.set(filterVal);

		if (filterVal === true) {
			template.filterApproval.set(false);
			$("#filterApproval").prop('checked', false);
		}

		template.skip.set(0);
		await refreshResults(template);
	},

	"change #filterApproval": async function (event, template) {
		const filterVal = $("#filterApproval").prop('checked');
		template.filterApproval.set(filterVal);

		if (filterVal === true) {
			template.filterDraft.set(false);
			template.filterPublished.set(false);
			$("#filterDraft").prop("checked", false);
			$("#filterPublished").prop('checked', false);
		}

		template.skip.set(0);
		await refreshResults(template);
	},

	"click #newCurriculumThemeBankLink": function (event, template) {
		event.preventDefault();
		const personType = Meteor.user()?.fetchPerson?.()?.type || "person";

		const adminPreCheck = function () {
			return new Promise(resolve => {
				mpSwal.fire({
					title: "Publish Now?",
					showCancelButton: false,
					html: `<div id='frmAdminPublish'>
						<div class="radio-list">
							<label class="radio radio-primary">
								<input type="radio" name="publish" value="yes" checked>
								<span></span>
								Yes, publish
							</label>
							<label class="radio radio-primary">
								<input type="radio" name="publish" value="no">
								<span></span>
								Set as Draft
							</label>
						</div></div><br/>
						`,
					preConfirm: function () {
						return new Promise(function (resolve) {
							resolve([
								$('#frmAdminPublish input[name=publish]:checked').val(),
							]);
						});
					}
				}).then(result => {
					const options = {};

					if (result?.value?.[0] === "yes") {
						options.publish = true;
					}

					resolve(options);
				})
			})
		}

		const submitForm = function(data) {
			if (personType === USER_TYPES.ADMIN) {
				adminPreCheck().then(options => {
					options.data = data;
					Meteor.callAsync("createCurriculumThemeBankRecord", options).then(async result => {
						if (result) {
							mpSwal.fire("Success", "Theme created successfully", "success");
							await refreshResults(template)
						}

						hideModal("#curriculumThemeBankNewModal");
					}).catch(error => {
						mpSwal.fire("Error", error.reason, "error");
					});
				})
			} else {
				Meteor.callAsync("createCurriculumThemeBankRecord", { data }).then(async result => {
					if (result) {
						mpSwal.fire("Success", "Theme created successfully", "success");
						await refreshResults(template)
					}

					hideModal("#curriculumThemeBankNewModal");
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		}

		showModal(
			"curriculumThemeBankNewModal",
			{
				curriculumThemeBankSource: {},
				isNew: true,
				onSave: submitForm
			},
			"#curriculumThemeBankNewModal"
		);
	},

	"click .publishCurriculumThemeBankLink": function (event, template) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");
		Meteor.callAsync("publishUnpublishCurriculumThemeBankRecord", _id, true).then(async result => {
			if (result) {
				mpSwal.fire("Success", "Theme unpublished successfully", "success");
				await refreshResults(template);
			}
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	},

	"click .unpublishCurriculumThemeBankLink": function (event, template) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");
		Meteor.callAsync("publishUnpublishCurriculumThemeBankRecord",  _id, false).then(async result => {
			if (result) {
				mpSwal.fire("Success", "Theme unpublished successfully", "success");
				await refreshResults(template);
			}
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	},

	"click .deleteCurriculumThemeBankLink": function (event, template) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");

		mpSwal.fire({
			title: "Are you sure?",
			text: "You cannot recover this Theme once deleted!",
			icon: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, delete it!"
		}).then(result => {
			if (result.value) {
				Meteor.callAsync("deleteCurriculumThemeBankRecord", _id).then(async result => {
					if (result) {
						mpSwal.fire("Success", "Theme deleted successfully", "success");
						await refreshResults(template);
					}
				})
					.catch(error => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},

	"click .copyCurriculumThemeDefinitionLink": async function (event) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");
		const theme = await Meteor.callAsync("getCurriculumThemeBankRecordById", _id);
		showModal(
			"curriculumThemeBankCopyModal",
			{
				_id,
				theme
			},
			"#curriculumThemeBankCopyModal"
		);
	},

	"click .reviewCurriculumThemeBankLink": function (event) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");
		FlowRouter.go(`/activities/theme/review/${_id}`);
	},

	"click .notificationClose": function (event) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");

		Meteor.callAsync("dismissCurriculumThemeBankMessageNotification",  _id).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	},

	"click #btn-search-definitions": async function (event, instance) {
		await refreshResults(instance);
	},

	"click .editCurriculumThemeDefinitionLink": async function (event, template) {
		event.preventDefault();
		const _id = $(event.currentTarget).attr("data-id");
		const theme = await Meteor.callAsync("getCurriculumThemeBankRecordById", _id);
		const personType = Meteor.user()?.fetchPerson?.()?.type || "person";

		const adminPreCheck = function () {
			if (theme?.published && personType === USER_TYPES.ADMIN) {
				return new Promise(resolve => {
					mpSwal.fire({
						title: "Edit Scheduled Theme(s)",
						showCancelButton: false,
						html: `<div id='frmAdminPublish'>
						<div class="mt-4 radio-list">
							<label class="radio radio-primary">
								<input type="radio" name="themeEdit" value="current">
								<span></span>
								Current themes
							</label>
							<label class="radio radio-primary">
								<input type="radio" name="themeEdit" value="currentFuture" checked>
								<span></span>
								Current and future themes
							</label>
							<label class="radio radio-primary">
								<input type="radio" name="themeEdit" value="all">
								<span></span>
								All themes
							</label>
						</div></div><br/>
							`,
						preConfirm: function () {
							return new Promise(function (resolve) {
								resolve([
									$('#frmAdminPublish input[name=themeEdit]:checked').val(),
								]);
							});
						}
					}).then(result => {
						const options = {};
						options.propagate = result?.value?.[0] ?? "currentFuture";
						resolve(options);
					})
				});
			} else {
				return new Promise(resolve => resolve({propagate: false}));
			}
		}

		const submitForm = function (data) {
			adminPreCheck().then(options => {
				data.selectedDays = [];
				const lists = document.querySelectorAll('.sortable-activities-list');

				for (const list of lists) {
					const activities = list.querySelectorAll('.sortable-activity');
					data.selectedDays.push(Array.from(activities).map(a => a?.dataset.id));
				}

				Meteor.callAsync("updateCurriculumThemeBankRecord", {data, _id, ...options}).then(async result => {
					if (result) {
						mpSwal.fire("Success", "Theme updated successfully", "success");
						await refreshResults(template);
					}
					hideModal("#curriculumThemeBankNewModal");
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
				});
			});
		}
		const dataObj = theme.sourceData || theme.updateData;
		showModal(
			"curriculumThemeBankNewModal",
			{
			curriculumThemeBankSource: dataObj,
			onSave: submitForm
			},
			"#curriculumThemeBankNewModal"
		);

	},

	"click .archiveCurriculumDefinitionLink": function (event, template) {
		event.preventDefault();
		const activityId = $(event.currentTarget).data("id");

		mpSwal.fire({
			title: "Are you sure?",
			text: "Archiving this activity will prevent it from appearing in future search results.",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, archive it"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("archiveCurriculum", activityId).then(async () => {
					await refreshResults(template);
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},

	"click .page-item-set": function (event, template) {
		const action = $(event.currentTarget).attr("data-action");

		if (action === "add") {
			if (template.totalCurriculumThemeBank.get() > (template.skip.get() + PAGE_LIMIT)) {
				template.skip.set(template.skip.get() + PAGE_LIMIT);
				nextPageCurriculumThemeBank(template);
			}
		} else if (action === "subtract") {
			if (template.skip.get() > 0) {
				template.skip.set(template.skip.get() - PAGE_LIMIT);
				PreviousPageCurriculumThemeBank(template);
			}
		}
	}
});

function buildQuery(instance) {
	let search = $("input[name='search-definitions']").val() || "";
	const query = {};
	const filterPublished = instance.filterPublished.get();
	const filterDraft = instance.filterDraft.get();
	const filterApproval = instance.filterApproval.get();

	if (filterPublished) {
		query.published = true;
	} else if (filterDraft) {
		query.published = false;
	}

	if (filterDraft && filterPublished) {
		delete query.published
	}

	if (filterApproval) {
		query.waitingForApproval = { $ne: null };
	}

	if (search.length > 0) {
		search = search.replace(/\W/g, "");
		const searchRegex = new RegExp(search, "i");
		query["$or"] = [{ "sourceData.name": searchRegex }, { "updateData.name": searchRegex }];
	}

	return query;
}

async function refreshResults(instance) {
	instance.searchResults.set(null);

	const sortOptions = { waitingForApproval: -1 };
	const skipValue = instance.skip.get();
	const limitValue = PAGE_LIMIT * 2;
	
	const query = buildQuery(instance);

	Meteor.callAsync('getPaginatedCurriculumThemeBankData', query, skipValue, limitValue, sortOptions).then((response) => {
		instance.previousCurriculumThemeBanks.set([]);
		const currentThemes = response.slice(0, PAGE_LIMIT);
		instance.currentCurriculumThemeBanks.set(currentThemes);
		instance.searchResults.set(currentThemes);
		instance.nextCurriculumThemeBanks.set(response.slice(PAGE_LIMIT));

		Meteor.callAsync('getTotalCurriculumThemeBankCount', query).then((total) => {
			instance.totalCurriculumThemeBank.set(total);
		});
	});
}

function nextPageCurriculumThemeBank(instance) {
	const sortOptions = { waitingForApproval: -1 };
	const query = buildQuery(instance)
	const skipValue = instance.skip.get() + PAGE_LIMIT;

	instance.previousCurriculumThemeBanks.set(instance.currentCurriculumThemeBanks.get());
	const newCurrentCurriculumThemeBanks = instance.nextCurriculumThemeBanks.get();
	instance.currentCurriculumThemeBanks.set(newCurrentCurriculumThemeBanks);
	instance.searchResults.set(newCurrentCurriculumThemeBanks);

	Meteor.callAsync('getPaginatedCurriculumThemeBankData', query, skipValue, PAGE_LIMIT, sortOptions).then((response) => {
		instance.nextCurriculumThemeBanks.set(response);
	});
}

function PreviousPageCurriculumThemeBank(instance){
	const sortOptions = { waitingForApproval: -1 };
	const query = buildQuery(instance)
	const skipValue = instance.skip.get() - PAGE_LIMIT;

	instance.nextCurriculumThemeBanks.set(instance.currentCurriculumThemeBanks.get())
	const newCurrentCurriculumThemeBanks = instance.previousCurriculumThemeBanks.get();
	instance.currentCurriculumThemeBanks.set(newCurrentCurriculumThemeBanks);
	instance.searchResults.set(newCurrentCurriculumThemeBanks);

	Meteor.callAsync('getPaginatedCurriculumThemeBankData', query, skipValue, PAGE_LIMIT, sortOptions).then((response) => {
		instance.previousCurriculumThemeBanks.set(response);
	});
}



