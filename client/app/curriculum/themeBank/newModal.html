<template name="curriculumThemeBankNewModal">
  <div id="curriculumThemeBankNewModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" style="width: 100%;height: 100%;">
      <div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">{{#if title}}{{title}}{{else}}{{#if isNew}}New{{else}}Update{{/if}} Theme{{/if}}</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-gray-100">
          {{> curriculumThemeBankDetailForm }}
        </div>
        <div class="modal-footer">
          {{#unless disabled}}
            <button data-cy="save-theme" type="button" class="btn btn-primary font-weight-bolder mr-2" id="curriculumThemeBankFormSubmit">Save</button>
          {{/unless}}
          <button data-cy="close-theme" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>