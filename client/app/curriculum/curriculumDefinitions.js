import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';
import { showModal, hideModal } from "../main";
import './curriculumDefinitions.html';
import { orgLanguageTranformationUtil } from "../../../lib/util/orgLanguageTranformationUtil";
import $ from 'jquery';
import { processPermissions } from '../../../lib/permissions';
import '../simpleModal/simpleModal';

Template.curriculumDefinitions.onCreated( function() {
	var self = this;
	self.searchResults = new ReactiveVar();
});

Template.curriculumDefinitions.onRendered( function() {
	$("#input-search-tags").select2( {
		templateSelection: function(item)
		    {
		        return item.text;
			}
		}
	);
});

Template.curriculumDefinitions.helpers({
	results() {
		return Template.instance().searchResults.get();
	},
	availableCurriculumTypes() {
		return Orgs.current().availableCurriculumTypes({mapped: true});
	},
	'replaceBackticks': function(s) {
		return s.replace('`', ' > ');
	},
	'canEdit'() {
		return processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
		});
	},
	'canArchive'() {
		return processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type=="admin" 
    });
	},
  'canManageContent'() {
    var edit = processPermissions({
      assertions: [{ context: "activities", action: "edit" }],
      evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
    });
    
    var archive = processPermissions({
      assertions: [{ context: "activities", action: "edit" }],
      evaluator: (thisPerson) => thisPerson.type=="admin" 
    });

    return edit || archive;
  },
  'getActivitiesTag': function() {
	return orgLanguageTranformationUtil.getActivities('tags');
  },
});

Template.curriculumDefinitions.events({
	"click #btn-search-definitions"(e, instance) {
		refreshResults(instance);
	},
	"click .editCurriculumDefinitionLink"(e, i) {
		e.preventDefault();
		const activityId = $(e.currentTarget).data("id"),
			searchResult = i.searchResults.get().find( (item) => item.id == activityId),
			activity = searchResult && searchResult.item;

		if (!activity) return;

		showModal("simpleModal", {
			title: "Edit Activity",
			template: "_curriculumBuilderAddActivityModal",
			data: activity,
			onSave: (e, instanceModal, formFieldData) => {
				formFieldData.selectedTypes = $("#inputCurriculumAddActivityTypes").val();
				formFieldData.selectedStandards = $("#inputCurriculumAddActivityStandards").val();
				formFieldData.curriculumId = activityId;

				formFieldData.removeFiles = $.map($("input[name='remove-current-file']:checked"), (n, index) => { return n.value} )

				const submitCurriculumForm = function () {
					Meteor.callAsync("updateCurriculum", formFieldData).then(result => {
						hideModal("#simpleModal");
						refreshResults(i);
						Session.set("refreshCurriculum", true);
					}).catch(error => {
						$(e.target).html('Save').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				}

				var uploadFile = document.getElementById("curriculumInputFile");

				if (uploadFile && uploadFile.files.length > 0) {
					processFilesAndSend( formFieldData, uploadFile, submitCurriculumForm)
					
				} else
					submitCurriculumForm();

			}
		}, "#simpleModal");
	},
	"click .archiveCurriculumDefinitionLink"(e, i) {
		e.preventDefault();
		const activityId = $(e.currentTarget).data("id");

		mpSwal.fire({
			title:"Are you sure?",
			text:"Archiving this activity will prevent it from appearing in future search results.",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, archive it"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("archiveCurriculum", activityId).then(result => {
					refreshResults(i);
				}).catch(error => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	}
});

function processFilesAndSend ( formFieldData, uploadFile, submitCurriculumForm ) {
	formFieldData.newMediaFile = [];
	let uploadsCompleted = 0;

	_.each(uploadFile.files, (file) => {
		var metaContext = {tokenId: tokenString()};

		var uploader = new Slingshot.Upload("myDocumentUploads", metaContext);

		uploader.send(file, function (error, downloadUrl) {
			if (error) {
				alert (error);
				$(e.target).html('Save').prop("disabled", false);
			}
			else {
				downloadUrl = remapDownloadUrl (downloadUrl);
				console.log("download = " + downloadUrl);
				var mediaType = file.type;
				
				var uploadedFile = {
					name: file.name,
					mediaUrl: downloadUrl,
					mediaToken: metaContext.tokenId,
					mediaFileType: mediaType,
					mediaPath: Meteor.user().orgId + "/" + Meteor.user()._id + "/" + metaContext.tokenId
				};
				
				formFieldData.newMediaFile.push(uploadedFile);
				uploadsCompleted++;
				if (uploadsCompleted >= uploadFile.files.length)
					submitCurriculumForm();
			}
		});
	});
}

function refreshResults(instance) {
	instance.searchResults.set(null);
	const options = {
		searchText: $("input[name='search-definitions']").val(),
		searchTags: $("#input-search-tags").val()
	};
	Meteor.callAsync("searchCurriculumItems", options).then(result => {
		instance.searchResults.set(result);
	}).catch(error => {
		mpSwal.fire(error.reason);
	});
}

var tokenString = function() {
  var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
  var string_length = 20;
  var randomstring = '';
  for (var i=0; i<string_length; i++) {
    var rnum = Math.floor(Math.random() * chars.length);
    randomstring += chars.substring(rnum,rnum+1);
  }
  return randomstring;
};
