import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { Curriculums } from '../../../lib/collections/curriculum';
import _ from '../../../lib/util/underscore';
import './curriculumBankActivityModal.html';
import { sentenceCase } from 'change-case';
import $ from 'jquery';
import { hideModal } from '../main';

Template.curriculumBankActivityModal.onCreated( function() {
	var self = this;
	self.activeCurriculum = new ReactiveVar(null);
  self.curriculumActivities = new ReactiveVar([]);
});

Template.curriculumBankActivityModal.rendered = function() {
  const data = Template.instance().data;
  let query = { bankId: Meteor.user()["orgId"], published: true };
  if(data.selectedGroups && data.selectedGroups.length > 0) {
    let selectedGroups = data.selectedGroups;
    query['sourceData.selectedAgeGroup'] = { '$in': selectedGroups };
  }
  Meteor.callAsync("getCurriculumBankActivities", query).then(result => {
    this.curriculumActivities.set(result);
  }).catch(error => {
    this.curriculumActivities.set(undefined);
  });
  if (data.curriculum) {
    this.activeCurriculum.set(data.curriculum);
    if (data.curriculum.selectedGroups & data.curriculum.selectedGroups.length > 0) {
      setTimeout(function() {
        $("#selectedGroups").multiselect("select", data.curriculum.selectedGroups);
        $("#selectedGroups").multiselect("refresh");
      }, 300);
    }
  }

  $("#inputExistingActivities").select2({
		selectionCssClass:"form-control form-control-lg form-control-solid"
	});

  setTimeout(function(){
    $("#scheduleCurriculumDate").datepicker({ autoclose: true, todayHighlight: true });
  }, 300)
};

Template.curriculumBankActivityModal.helpers({
  "showSchedulingFields": function() {
    return Template.instance().data.showSchedulingFields;
  },
  "curriculumInBank": function () {
    return Template.instance().curriculumActivities.get();
  },
  "curriculum": function() {
    return Template.instance().activeCurriculum.get();
  },
  "getLockedFields": function() {
    const data = Template.instance().activeCurriculum.get();
    const renderArr = [];
    const standards = Curriculums.getStandards();
    for (const [key, value] of Object.entries(data)) {
      let fieldType = "textarea";
      let label = sentenceCase(key);
      let displayValues = value;
      if (key == "selectedStandards") {
        displayValues = [];
        for (const val of value) {
          const standardId = val.split("|")
          const standard = _.find(standards, function(i) {
            return i.standardId == standardId?.[1];
          });
          displayValues.push(standard?.benchmark);
        }
      } else if (key == "mediaFiles") {
        displayValues = [];
        fieldType = "links";
        for (const val of value) {
          displayValues.push({name: val.mediaName, link: val.mediaUrl});
        }
      }

      //ignore following fields
      if (!_.contains(["teacherNotes","scheduledDate", "selectedGroups", "_id", "curriculumBankSourceId", "orgId", "createdAt", "createdBy", "curriculumThemeId"], key)) {
        renderArr.push({ label, value: displayValues, fieldType })
      }

    }
    return renderArr;
  },
  "checkedIfScheduledDate": function(dateVal) {
    const scheduledDate = Template.instance().activeCurriculum.get()?.scheduledDate;
		if (dateVal=="today")
			return (scheduledDate == new Date().setHours(0,0,0,0)) ? true : false;
		else
			return (scheduledDate != new Date().setHours(0,0,0,0)) ? true : false;
	},
  "formattedScheduledDate": function() {
    const scheduledDate = Template.instance().activeCurriculum.get()?.scheduledDate;
		return moment(scheduledDate).format("MM/DD/YYYY");
	},
  "checkedIfGroups": function(groupsValue) {
    const selectedGroups = Template.instance().activeCurriculum.get()?.selectedGroups || [];
		return ( (groupsValue == "empty" && selectedGroups.length === 0) ||
				 (groupsValue != "empty" && selectedGroups.length > 0) )? true : false;
	},
  "groups": function() {
		return Groups.find({
			orgId: Meteor.user()["orgId"]
		}, {sort: {"name" : 1}});
	},
  'sourceLabel'(bankId) {
		const org = Orgs.current();
		if (org?.hasCustomization("curriculumBank/globalAndLocal")) {
		  return (bankId == org._id) ? "Local" : "Global";			
		}
  }
});

Template.curriculumBankActivityModal.events({
  "click #copyActivityFromBank": function(e, i) {
    let allActivities = i.curriculumActivities.get();
    const selections = $("#inputExistingActivities").select2('data'),
    selectedActivityId = selections && selections[0] && selections[0].id;
    
    const result = allActivities.filter(
      (act)=> {
        return act._id === selectedActivityId;
      }
    )
    const sourceData = result[0].sourceData;
    sourceData.curriculumBankSourceId = selectedActivityId;
    i.activeCurriculum.set(sourceData);
    setTimeout(function() {
      $("#selectedGroups").multiselect("refresh");
      $("#scheduleCurriculumDate").datepicker({ autoclose: true, todayHighlight: true });
    }, 300)
  },
  "click #curriculumBankActivityFormSave": function(e, i) {
    $(e.target).html('Saving').prop("disabled", true);
    const existingCurriculum = i.data.curriculum;
    const passedScheduledDate = i.data.selectedDate || new moment($("#scheduleCurriculumDate").val(),"MM/DD/YYYY").valueOf();
    const passedThemeId = i.data.themeId;
    const teacherNotes = $("#inputTeacherNotes").val();

    const curriculumData = {
      ...i.activeCurriculum.get()
    };

    if (teacherNotes) curriculumData.teacherNotes = teacherNotes;
    if (passedScheduledDate) curriculumData.scheduledDate = passedScheduledDate;
    
    //themes set the selectedGroups, otherwise pull from form
    if (passedThemeId) {
      curriculumData.curriculumThemeId = passedThemeId;
    } else if ($("input[name='sendTo']:checked").val() == "selectGroups") {
      curriculumData.selectedGroups = [];
			$("#selectedGroups option:selected").each(function(i, selected) {
				curriculumData.selectedGroups.push($(selected).val());
			});
		}

    if (existingCurriculum) {
      curriculumData.curriculumId = existingCurriculum._id;
      Meteor.callAsync("updateCurriculum", curriculumData).then(result => {
        hideModal("#curriculumBankActivityModal")
      }).catch(error => {
        $(e.target).html('Save').prop("disabled", false);
        mpSwal.fire("Error", error.reason, "error");
      });
    } else {
      Meteor.callAsync("insertCurriculum", curriculumData).then(result => {
        hideModal("#curriculumBankActivityModal");
      }).catch(error => {
        $(e.target).html('Save').prop("disabled", false);
        mpSwal.fire("Error", error.reason, "error");
      });
    }

  }
})
