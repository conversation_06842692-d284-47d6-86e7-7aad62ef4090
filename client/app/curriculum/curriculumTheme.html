<template name="curriculumTheme">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      <div class="card card-custom gutter-b">
        <div class="card-body">
          <div class="d-flex flex-row flex-grow-1 align-items-center justify-content-between my-4">
            <span class="font-size-h3 text-primary font-weight-bold">Theme: {{name}}</span>
            {{#if showManageButton}}
              <div class="btn btn-primary font-weight-bolder btn-text-white" id="btnManageTheme">
                Manage Theme
              </div>
            {{/if}}
          </div>
          <div class="d-flex flex-column">
            <span class="font-size-h3 text-primary font-weight-bold">Description</span>
            <span>{{description}}</span>
          </div>
        </div>
      </div>
      {{#each day in days}}
        <div class="card card-custom gutter-b">
          <div class="card-body">
            <div class="d-flex flex-row align-items-center justify-content-start mb-4">
              <div>
                <span class="font-size-h3 text-primary font-weight-bold">{{formatDate day "dddd MMMM Do, YYYY"}}</span>
              </div>
            </div>
            {{#each activity in (dayActivities day)}}
              <div class="separator separator-dashed my-8"></div>
              <div class="row">
                <div class="col-sm-5">
                  <a href="/activities/{{activity._id}}" class="font-size-h4 font-weight-bolder">{{activity.headline}}</a>
                  <p class="font-size-base">{{activity.message}}</p>
                  {{#if activity.homework}}
                    <div class="font-size-lg font-weight-bold">Homework:</div>
                    <p class="font-size-base">{{activity.homework}}</p>
                  {{/if}}
                  {{#if activity.materials}}
                    <div class="font-size-lg font-weight-bold">Materials:</div>
                    <p class="font-size-base">{{activity.materials}}</p>
                  {{/if}}
                  {{#if activity.teacherNotes}}
                    <div class="font-size-lg font-weight-bold">Teacher Notes:</div>
                    <p class="font-size-base">{{activity.teacherNotes}}</p>
                  {{/if}}
                  {{#if activity.internalNotes}}
                    <div class="font-size-lg font-weight-bold">Internal Notes:</div>
                    <p class="font-size-base">{{activity.internalNotes}}</p>
                  {{/if}}
                  {{#if activity.internalLink}}
                    <div class="font-size-lg font-weight-bold">Internal Link:</div>
                    <a href="{{activity.internalLink}}" target="_blank" class="font-size-base">View Internal Link</a>
                  {{/if}}
                </div>
                <div class="col-sm-5">
                  {{#if activity.selectedTypes}}
                    <div class="font-size-lg font-weight-bold">Tag(s):</div>
                    {{formatSelectedTypes activity.selectedTypes}}<br/><br/>
                  {{/if}}
                  {{#if activity.selectedStandards}}
                    <div class="font-size-lg font-weight-bold">Standard(s):</div>
                  <!--{{formatSelectedStandards activity.selectedStandards}}<br/>-->
                    {{#each standard in activity.findStandards}}
                      {{standard}}<br/>
                    {{/each}}
                    <br/>
                  {{/if}}
                  {{#if activity.mediaFiles}}
                    <div class="font-size-lg font-weight-bold">Attachment(s):</div>
                    {{#each activity.mediaFiles}}
                      {{mediaName}} <a href="#" onclick="window.open('{{attachmentHandler mediaUrl}}', '_blank');" style="font-weight:normal;font-size:14px"> (View PDF)</a><br/>
                    {{/each}}
                  {{/if}}
                </div>
              </div>
            {{/each}}
          </div>
        </div>
      {{/each}}
    </div>
  </div>
</template>
