<template name="curriculumReview">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      <div class="d-flex flex-row justify-content-end mb-4">
        <div data-cy="approve-review-btn" class="btn btn-success font-weight-bolder mr-4" id="btnApproveCurriculumBank">
          <i class="fad-regular fad fa-check mr-2 text-white"></i>Approve
        </div>
        <div data-cy="reject-review-btn" class="btn btn-danger font-weight-bolder mr-4" id="btnRejectCurriculumBank">
          <i class="fad-regular fad fa-stop-circle mr-2 fa-swap-opacity text-white"></i>Reject
        </div>
        <div data-cy="edit-update-review-btn" class="btn btn-primary font-weight-bolder" id="btnEditUpdateCurriculumBank">
          <i class="fad-regular fad fa-pen-square mr-2 fa-swap-opacity text-white"></i>Edit Update
        </div>
      </div>
      <div class="card card-custom gutter-b">
        <div class="d-flex flex-row justify-content-between my-4">
          <div class="d-flex flex-column" style="width: 50%;">
            <span class="font-size-h3 ml-12 my-4">Existing Activity</span>
            {{#each getDataSource "sourceData"}}
              {{#if trueIfEq fieldType "links"}}
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right col-form-label">{{label}}</label>
                  <div class="col-lg-9 col-xl-6">
                    {{#each v in value}}
                      <a href="{{v.link}}" target=”_blank”>{{v.name}}</a>
                    {{/each}}
                  </div>
                </div>
              {{else}}
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right col-form-label">{{label}}</label>
                  <div class="col-lg-9 col-xl-6">
                    <textarea class="form-control form-control-lg" value="{{value}}" disabled></textarea>
                  </div>
                </div>
              {{/if}}
            {{/each}}
          </div>
          <div class="d-flex flex-column" style="width: 50%; border-left: 1px dashed var(--primary);">
            <span class="font-size-h3 ml-12 my-4">Proposed Update</span>
            {{#each getDataSource "updateData"}}
              {{#if trueIfEq fieldType "links"}}
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right col-form-label">{{label}}</label>
                  <div class="col-lg-9 col-xl-6">
                    {{#each v in value}}
                      <a href="{{v.link}}" target=”_blank”>{{v.name}}</a>
                    {{/each}}
                  </div>
                </div>
              {{else}}
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right col-form-label">{{label}}</label>
                  <div class="col-lg-9 col-xl-6">
                    <textarea class="form-control form-control-lg" value="{{value}}" disabled></textarea>
                  </div>
                </div>
              {{/if}}
            {{/each}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>