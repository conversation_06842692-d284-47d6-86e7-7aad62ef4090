import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import _ from '../../../lib/util/underscore';
import './_curriculumFormModal.html';
import { orgLanguageTranformationUtil } from "../../../lib/util/orgLanguageTranformationUtil";
import $ from 'jquery';
import { Curriculum, Curriculums } from '../../../lib/collections/curriculum';
import { hideModal } from '../main';
import { remapDownloadUrl } from '../../../mpweb';

Template._curriculumFormModal.events({
	'change #inputCurriculumTypeNew': function(event) {
		var benchmark = $("#inputCurriculumType").val();
	},
	'change #curriculumInputFile': function(event, instance) {
		event.preventDefault();
		var uploadFiles = $(event.target);
		var uploadFile = uploadFiles[0];
		if (uploadFile && uploadFile.files.length > 0 ) {
			var arr = [];
			for (let x=0; x < uploadFile.files.length; x++) {
				arr.push(uploadFile.files[x].name);
			}
			$("#curriculumFileLabelSpan").text(arr.join(', '));
		}
	},
	'click #curriculumFormSubmit': function(event, instance) {
		event.preventDefault();
		$("#curriculumFormSubmit").prop('disabled', true).prop("value", "Saving");

		var curriculumData = {};
		curriculumData.headline = $("#inputHeadline").val();
		//curriculumData.type = $("#inputCurriculumType").val();
		curriculumData.selectedTypes = $("#inputCurriculumTypes").val();
		curriculumData.message = $("#inputMessage").val();
		curriculumData.materials = $("#inputMaterials").val();
		curriculumData.homework = $("#inputHomework").val();
		curriculumData.internalNotes = $("#inputInternalNotes").val();
		curriculumData.internalLink = $("#inputInternalLink").val();
		
		if ($("input[name='scheduleFor']:checked").val() == "today") 
			curriculumData.scheduledDate = new Date().setHours(0,0,0,0);
		else
			curriculumData.scheduledDate = moment($("#scheduleCurriculumDate").val(), "MM/DD/YYYY").valueOf();
		curriculumData.selectedGroups = [];
		if ($("input[name='sendTo']:checked").val() == "selectGroups") {
			$("#selectedGroups option:selected").each(function(i, selected) {
				curriculumData.selectedGroups.push($(selected).val());
			});
		}

		curriculumData.selectedStandards = $("#inputCurriculumStandards").val();

		curriculumData.removeFiles = $.map($("input[name='remove-current-file']:checked"), (n, i) => { return n.value} );

		var submitCurriculumForm = function() {
			if (instance && instance.data && typeof instance.data.onSave === "function") {
				$("#curriculumFormSubmit").prop('disabled', false).prop("value", "Save");
				return instance.data.onSave(curriculumData)
			}

			if (!Session.equals("curriculumId", "") && !Session.equals("curriculumId", null)) {
				curriculumData.curriculumId = Session.get("curriculumId");
				console.log("data to save", curriculumData);
				Meteor.callAsync('updateCurriculum', curriculumData).then(response => {
					$("#curriculumFormSubmit").prop('disabled', false).prop("value", "Save");
					Session.set("refreshCurriculum", true);
					hideModal("#_curriculumFormModal");
				}).catch(error => {
					console.log("error updateCurriculum: ", error);
					$("#curriculumFormSubmit").prop('disabled', false).prop("value", "Save");
					mpSwal.fire("Error", error.reason, "error");
				});
			} else
				Meteor.callAsync('insertCurriculum', curriculumData).then(response => {
					console.log("data to save", curriculumData);
					$("#curriculumFormSubmit").prop('disabled', false).prop("value", "Save");
					hideModal("#_curriculumFormModal");
				}).catch(error => {
					console.log("data to save", curriculumData);
					console.log("error insertCurriculum: ", error);
					$("#curriculumFormSubmit").prop('disabled', false).prop("value", "Save");
					mpSwal.fire("Error", error.reason, "error");
				});
		};
		
		var uploadFile = document.getElementById("curriculumInputFile");
		if (uploadFile && uploadFile.files.length > 0) {
			curriculumData.newMediaFile = [];
			let uploadsCompleted = 0;

			_.each(uploadFile.files, (file) => {
				var metaContext = {tokenId: tokenString()};

				var uploader = new Slingshot.Upload("myDocumentUploads", metaContext);

				uploader.send(file, function (error, downloadUrl) {
					if (error) {
						alert (error);
						$("#curriculumFormSubmit").prop('disabled', false).prop("value", "Save");
					}
					else {
						downloadUrl = remapDownloadUrl(downloadUrl);
						console.log("download = " + downloadUrl);
						var mediaType = file.type;
						
						var uploadedFile = {
							name: file.name,
							mediaUrl: downloadUrl,
							mediaToken: metaContext.tokenId,
							mediaFileType: mediaType,
							mediaPath: Meteor.user().orgId + "/" + Meteor.user()._id + "/" + metaContext.tokenId
						};
						
						curriculumData.newMediaFile.push(uploadedFile);
						uploadsCompleted++;
						if (uploadsCompleted >= uploadFile.files.length)
							submitCurriculumForm();
					}
				});
			});
			
		} else
			submitCurriculumForm();
	}
});

Template._curriculumFormModal.rendered = function() {	
	$("#scheduleCurriculumDate").datepicker({ autoclose: true, todayHighlight: true });

	$("#inputCurriculumStandards").select2( {
		multiple: true,
		dropdownParent: $('#_curriculumFormModal'),
		templateSelection: function(item)
		    {
		        return item.text;
			}
		}
	);
	$("#inputCurriculumTypes").select2( {
		multiple: true,
		dropdownParent: $('#_curriculumFormModal'),
		templateSelection: function(item)
		    {
		        return item.text;
			}
		}
	);
	
	var self = this;

	self.autorun(function() {
		const curriculumId = Session.get("curriculumId");
		
		$("#selectedGroups").multiselect("select", curriculum() && curriculum().selectedGroups);
		$("#selectedGroups").multiselect("refresh");

		$("#inputCurriculumStandards").val(curriculum() && curriculum().selectedStandards);
		$("#inputCurriculumStandards").trigger('change');

		$("#inputCurriculumTypes").val(curriculum() && curriculum().selectedTypes);
		$("#inputCurriculumTypes").trigger('change');

	});

	$("#_curriculumFormModal").on("hidden.bs.modal", function() {
		Session.set("curriculumId", null);
	});
};

var curriculum = async function() {
	const instance = Template.instance();
	if (instance.data.pendingCurriculum) return instance.data.pendingCurriculum

	if (Session.equals("curriculumId", ""))
		return { headline: "", message: "", scheduledDate: new Date().setHours(0,0,0,0), selectedGroups: [], selectedStandards: [], selectedTypes: []};
	else if (Session.equals("curriculumId", null ))
		return { selectedGroups: [], selectedStandards: []}
	else {
		let activity;
		await Meteor.callAsync('getCurriculumById', Session.get("curriculumId")).then((res) => {
			activity = new Curriculum(res);
		});
		return activity;
	}
}

Template._curriculumFormModal.helpers({
	"groups": function() {
		return Groups.find({
			orgId: Meteor.user()["orgId"]
		}, {sort: {"name" : 1}});
	},
	"curriculum": function() {
		return curriculum();
	},
	"checkedIfScheduledDate": function(dateVal) {
		if (dateVal=="today")
			return (this.scheduledDate == new Date().setHours(0,0,0,0)) ? true : false;
		else
			return (this.scheduledDate != new Date().setHours(0,0,0,0)) ? true : false;
	},
	"formattedScheduledDate": function() {
		return moment(this.scheduledDate).format("MM/DD/YYYY");
	},
	"checkedIfGroups": function(groupsValue) {
		const selectedGroups = Array.isArray(this.selectedGroups) ? this.selectedGroups : [];

		const isEmptySelected = groupsValue === "empty" && selectedGroups.length === 0;
		const isNonEmptySelected = groupsValue !== "empty" && selectedGroups.length > 0;

		const result = isEmptySelected || isNonEmptySelected;

		return result;
	},
	"isGroupIdSelected": function(selectedGroups, groupId) {
		return (selectedGroups.indexOf(groupId) >= 0) ? true : false;
	},
	"isNewCurriculum": function() {
		if (Template.instance().data.pendingCurriculum) return false;
		return (Session.equals("curriculumId", "") || Session.equals("curriculumId", null)) ? true : false;
	},
	'availableCurriculumTypes': function() {
		return Orgs.current().availableCurriculumTypes({mapped: true});
	},
	'availableCurriculumStandards': function() {
		return Curriculums.getStandards();
	},
	'replaceBackticks': function(s) {
		return s.replace('`', ' > ');
	},
	'bankDataEntry': function() {
		return Template.instance().data.bankDataEntry;
	},
	'getActivitiesTag': function() {
		return orgLanguageTranformationUtil.getActivities('tags');
	},
});



function resetCurriculumForm() {
	$("#curriculumForm")[0].reset();
	$("input[name=sendTo][value=allGroups]").attr('checked', 'checked');
	$("#scheduleCurriculumDate").val(moment(new Date().setHours(0,0,0,0)).format("MM/DD/YYYY"));
}

var tokenString = function() {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i=0; i<string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum,rnum+1);
	}
	return randomstring;
};
