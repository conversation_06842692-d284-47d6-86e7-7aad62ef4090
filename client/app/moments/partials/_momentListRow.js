import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './_momentListRow.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from '../../../../lib/collections/people';
import { processPermissions } from '../../../../lib/permissions';
import { showModal } from '../../main';
import { Moments } from '../../../../lib/collections/moments';
import './_videoPlayerModal';
import '../momentFormModal';

Template._momentListRow.onCreated(async function() {
	this.formattedDescription = new ReactiveVar();

    // `this.data` contains the moment data
	Tracker.autorun(async () => {
		if (this.data && typeof this.data.formattedDescription === 'function') {
			try {
				const description = await this.data.formattedDescription();
				this.formattedDescription.set(description);
			} catch (error) {
				console.error('Error fetching formatted description:', error);
			}
		}
	});
})

Template._momentListRow.helpers({
	"getAttributionName": function() {
		return (this.attributionName) ? this.attributionName : false;
	},
	"showShareButton": function() {
		return (this.attachedMedia().length > 0 && window.plugins && window.plugins.socialsharing);
	},
	"showLikeButton": function() {
		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
		return (this.momentType != "checkin" && this.momentType !="checkout" && currentPerson && currentPerson.type=="family" && !this.likedByPerson(currentPerson._id));
	},
	"isMomentEditable": function() {
		if (this.momentType === 'doorUnlocked') {
			return false;
		}
		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
		return ( !FlowRouter.current().path.includes("/welcome") && currentPerson && 
					processPermissions({
						assertions: [{ context: "moments", action: "edit"}],
						evaluator: (person) => person.type=="admin" || person.type == "staff"
					})
				) ? true : false;
	},
	"reactionText": function() {
		const meteorUser = Meteor.user(), person = meteorUser && meteorUser.fetchPerson();
		var personType = person.type;
		if (personType=="staff" || personType=="admin") {
			outputText = "";
			_.each(this.reactions, function(r) { 
				outputText += (outputText != "" ? ", " : "") + r.sender;
			});
			if (outputText != "")
				return "Liked by: " + outputText;
		} else if (personType=="family" && this.likedByPerson(person._id)) {
			return "Liked by you";
		}
	},
	"reactionData": function() {
		const reactionsMap = [], meteorUser = Meteor.user(), person = meteorUser && meteorUser.fetchPerson();
		const iconMap = {
			like: 'thumb-up-2.png',
			smile: 'thumb-up-2.png',
			love: 'heart-2.png',
			celebrate: 'party-popper.png',
			laugh: 'big-grin.png',
			wink: 'upside-down-face.png',
			not_sure: 'upside-down-face.png',
			meh: 'frowning-face.png',
			sad: 'frowning-face.png',
		};
		let activePersonalReaction = false;
		_.each( this.reactions, (r, ownerId) => {
			const reactionType = r.likeType || "smile", item = _.find( reactionsMap, (ri) => ri.reactionType == reactionType);
			const rp = People.findOne({_id: ownerId});
			if (item) {
				if (rp) item.nameList += ", " + rp.firstName + " " + rp.lastName
				item.count++;
			}
			else {
				const reaction = {reactionType, count: 1, icon: iconMap[reactionType]};
				if (rp && (person.type == "admin" || person.type == "staff"))
					reaction["nameList"] = rp.firstName + " " + rp.lastName;
				reactionsMap.push(reaction );
			}
			if (person._id == ownerId) activePersonalReaction = true;
		});

		return reactionsMap;
	},
	"includeInlineVideo": function() {
		return this.isVideo && Meteor.isCordova;
	},
	"engagementData": function() {
		if (!FlowRouter.current().path.includes("welcome")) return;

		return true;
	},
	"hasMedia": function() {
		return this.attachedMedia().length > 0;
	},
	"isMedia": function() {
		return (this && this.mediaFileType && _.contains(["image","video"],this.mediaFileType));
	},
	"attachmentLink": function() {
		return Meteor.settings.public.photoBaseUrl + "uploads/" + this.mediaPath;
	},
	"attachmentName": function() {
		return this.mediaFileName || "view";
	},
	"formattedDesc": function() {
        return Template.instance().formattedDescription.get();
    }
});

Template._momentListRow.events({
	"click #moment-delete": function(e, template) {
		var momentId = template.data._id;
		return mpSwal.fire({  
			title: "Are you sure?",   
			text: "You will not be able to recover this moment once deleted!",   
			icon: "warning",   
			showCancelButton: true,
			confirmButtonText: "Yes, delete it!"
		}).then( result => {   
			if (result.value) Meteor.callAsync("deleteMoment", momentId).then(result => {}).catch(error => {
				mpSwal.fire("Error", error.reason, "error");
			});
		});
	},
	"click #moment-data": function(e, template) {
		var m = Moments.findOne(template.data._id);
		if (m) {
			
			mpSwal.fire({
				title: "Moment Data",
				html: "<b>Created Time:</b> " + moment(m.createdAt).format("M/D/YY h:mm a") +
					"<br/><b>Moment Time:</b> " + moment(m.sortStamp).format("M/D/YY h:mm a") 
			});
		}
	},
	"click #moment-modify": function (e, template) {
	const { momentType, _id , owner} = template.data;
	const person = People.findOne({ _id: owner });
	const isNonPersonType = person?.type !== "person";

	if (['checkin', 'checkout'].includes(momentType) && isNonPersonType) {
		showErrorModal();
	} else {
		showModal("momentFormModal", { momentId: _id }, "#momentFormModal");
	}
	},
	"click .showMediaLink": function(e) {
		var videoId = $(e.currentTarget).data("id");
		var tokenId = $(e.currentTarget).data("mediatoken");
		var currentMoment = Moments.findOne(videoId);
		
		var currentMedia = _.find(currentMoment.attachedMedia(), function (m) { return m.mediaToken == tokenId});
		
		if (currentMedia && currentMedia.isVideo) {
			pathString = currentMedia.pathForVideoFormat("mp4") + "|" + currentMedia.pathForVideoFormat("webm");
			Session.set("videoPlayerLocation", pathString );
			showModal("_videoPlayerModal", {}, "#_videoPlayerModal");
		}
	},
	"click .sendLike": function(e) {
		e.preventDefault();
		const momentId = $(e.currentTarget).data("id");
		const likeType = $(e.currentTarget).data("type");
		Meteor.callAsync("timelineLikeMoment", {momentId, likeType}).then(res => {}).catch(err => {});
	},
});

Template.checkinMomentDisplay.helpers({
	"extractCheckInHealthChecks": function() {
		return _.map(this.checkInHealthChecks, function(value, key) {
			return {checkName: key, checkValue: value, checkIcon: value=="good" ? "fa-smile" : "fa-frown-open"};
		});
	},
	"pinCodeFormFields": function() {
		let values = [];
		const currentMoment = this;
		_.each(Orgs.current() && Orgs.current().pinCodeCheckinFields(), (field) => {
			if (currentMoment[field.dataId]) values.push({fieldLabel: field.label, fieldValue: currentMoment[field.dataId]});
		})
		return values;
	}
});

Template.checkoutMomentDisplay.helpers({
	"extractCheckOutHealthChecks": function() {
		return _.map(this.checkOutHealthChecks, function(value, key) { 
			return {checkName: key, checkValue: value, checkIcon: value=="good" ? "fa-smile" : "fa-frown-open"};
		});
	}
});

function showErrorModal() {
	mpSwal.fire({
		title: 'Error',
		text: 'Please visit Time → Time Cards to edit this entry',
		icon: 'error',
		showCancelButton: true,
		confirmButtonText: 'Go to time cards',
		cancelButtonText: 'Cancel'
	}).then((result) => {
		if (result.isConfirmed) {
			FlowRouter.go('/time');
		}
	});
}