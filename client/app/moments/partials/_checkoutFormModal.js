import { Template } from 'meteor/templating';
import './_checkoutFormModal.html';
import { People } from "../../../../lib/collections/people";
import './_checkoutForm';

Template._checkoutFormModal.helpers({
  "getPersonId": function() {
    return Template.instance().data.personId;
  },
  "getPersonName": function() {
    var person = People.findOne(Template.instance().data.personId);
    return `${person.firstName} ${person.lastName}`;
  }
});

Template._checkoutFormModal.events({
  "click #checkout-modal-save": function(e, template) {
    console.log("parent");
    $("#checkOutFormSubmit").trigger("click");
  }
})
