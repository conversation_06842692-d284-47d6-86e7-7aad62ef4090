<template name="momentFormModal">
  <div id="momentFormModal" class="modal {{#if shouldFade}}fade{{/if}}">
  	<div class="modal-dialog modal-dialog-scrollable modal-xl" style="width: 100%;height: 100%;">
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">{{#if getMomentId}}Modify{{else}}New{{/if}} Moment</h5>
          <div class="d-flex align-items-center">
            <div data-cy="close-modal" class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-gray-100">
          {{> momentForm momentId=getMomentId preTag=getPreTag}}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary font-weight-bolder mr-2" id="momentFormSave" data-cy="save-moment-btn">Save</button>
          <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal" data-cy="close-moment-btn">Close</button>
        </div>
  		</div>
  	</div>
  </div>
</template>
