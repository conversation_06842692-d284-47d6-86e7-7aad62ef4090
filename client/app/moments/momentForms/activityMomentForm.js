import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './activityMomentForm.html';
import './momentFormTimePicker';

Template.activityMomentForm.events({
	'click #activityEngagement button': function(event) {
		let name = $(event.target).data('activityengagement');
		Session.set("activeMomentActivityEngagement", name);
	}
});

Template.activityMomentForm.helpers({
	'availableActivityTypes': function() {
		let options = [];
		if (Orgs.current().hasCustomization("moments/activity/childDefaults")) 
 			options = ["", "Table Times", "Outdoor Play", "Ball Play", "Table Toys", "Tummy Time", "Dance"];
 		else if(Orgs.current().hasCustomization("moments/activity/organizationSpecificTypes"))
 			options = Orgs.current().valueOverrides ? Orgs.current().valueOverrides.activityTypes : [];
		else
			options = ["", "Beauty/Barber Shop", "Cognitive Games", "Daily Living Skills", "Discussion/Reminisce/Read", "Entertainment/Speakers", 
				"Exercise", "Fine Motor", "Gross Motor", "Intergenerational", "Music", "Outings", "Pet Therapy", "Shower", "Social", "Spiritual",    
				"Walking", "Other"];
		return _.map(options,
			function(val) {
				return {name: val}
			});
	},
	'currentMomentActivityEngagement': function() {
		return Session.get("activeMomentActivityEngagement");
	},
	
	'currentMomentActivityType': function() {
		return Session.get("activeMomentActivityType");
	},
});
