import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './suppliesMomentForm.html';
import './momentFormTimePicker';

Template.suppliesMomentForm.helpers({
	'availableSupplyTypes': function() {
		let options;
		if (Orgs.current() && Orgs.current().isChildCare())
			options = ["Breast Milk", "Clothing", "Diapers", "Formula", "Pull Ups", "Undergarments", "Wipes"];
		else
			options = ["Full change of clothes", "Slacks", "Underwear", "Return center's clothes", "Pads/Depends", "Other"];
		if(Orgs.current().hasCustomization("moments/supplies/organizationSpecificTypes"))
 			options = Orgs.current().valueOverrides ? Orgs.current().valueOverrides.suppliesTypes : [];
		return _.map(options,
			function(val) {
				return {name: val}
			});
	},
	'currentMomentSupplyType': function() {
		return Session.get("activeMomentSupplyType");
	}
});
Template.suppliesMomentForm.onRendered( function () {
	let self = this;
	const activeSupplyType = Session.get("activeMomentSupplyType");
	$("#supplyType").multiselect();
	if (activeSupplyType) {
		const arraySupplyType = activeSupplyType.split(",");
		$("#supplyType").multiselect("select", arraySupplyType);
		$("#supplyType").multiselect("refresh");
	}
	
});