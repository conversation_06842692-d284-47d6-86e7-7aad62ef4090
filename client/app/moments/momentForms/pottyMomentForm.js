import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import './pottyMomentForm.html';
import { orgLanguageTranformationUtil } from "../../../../lib/util/orgLanguageTranformationUtil";
import './momentFormTimePicker';

Template.pottyMomentForm.events({
	'click #pottyType button': function (event) {
		let name = $(event.target).data('pottytype');
		Session.set("activeMomentPottyType", name);
	},
	'click #pottyTypeContinence button': function (event) {
		let name = $(event.target).data('pottytypecontinence');
		Session.set("activeMomentPottyTypeContinence", name);
	},
	'click #pottyTraining button': function (event) {
		let name = $(event.target).data('pottytraining');
		Session.set("activeMomentPottyTraining", name);
	}
});

Template.pottyMomentForm.helpers({
	'currentMomentPottyType': function () {
		return Session.get("activeMomentPottyType");
	},
	'currentMomentPottyTypeContinence': function () {
		return Session.get("activeMomentPottyTypeContinence");
	},
	'currentMomentPottyTraining': function () {
		return Session.get("activeMomentPottyTraining");
	},
	'showTraining': function () {
		return (Orgs.current() && Orgs.current().language != "translationsEnAdultCare");
	},
	'getMomentTypesPottyWet': function () {
		return orgLanguageTranformationUtil.getMomentTypesPotty('Wet');
	},
	'getMomentTypesPottyBM': function () {
		return orgLanguageTranformationUtil.getMomentTypesPotty('BM');
	},
	'getMomentTypesPottyWetBM': function () {
		return orgLanguageTranformationUtil.getMomentTypesPotty('WetBM');
	},
	'getMomentTypesPottyDry': function () {
		return orgLanguageTranformationUtil.getMomentTypesPotty('Dry');
	}
});
