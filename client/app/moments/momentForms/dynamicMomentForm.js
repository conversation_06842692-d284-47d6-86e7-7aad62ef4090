import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './dynamicMomentForm.html';
import {getPeopleById, getPeopleData} from "../../../services/peopleMeteorService";
import './momentFormTimePicker';

Template.dynamicMomentForm.onCreated(function() {
	this.staffPeopleRes = new ReactiveVar();
	this.taggedPeopleRes = new ReactiveVar();
	const query = {
		orgId: Meteor.user().orgId, 
		inActive:{$ne:true},
		$or: [{type:"admin"}, {type:"staff"}]
	};
	
	getPeopleData(query).then((res) => {
		if (res) {
			const result = res.map((d) => {
				return {_id:d._id, name: d.firstName + " " + d.lastName};
			});
			this.staffPeopleRes.set(result);
		}
	}).catch(err => {
		console.log(err);
	});
	
	this.autorun(() => {
		let taggedPeople = Session.get("taggedPeople");

		if (Array.isArray(taggedPeople) && taggedPeople.length > 0 && taggedPeople[0].split('|')[0] == "person") {
			getPeopleById(taggedPeople[0].split('|')[1]).then((res) => {
				if (res) {
					Session.set("taggedPersonDynamicForm", res);
				}
			}).catch(err => {
				console.log(err);
			});
		} else {
			Session.set("taggedPersonDynamicForm", {});
		}
	});
});

Template.dynamicMomentForm.helpers({
	"momentDefinition": function() {
		let momentType = Session.get("activeMomentType");
		let momentDefinition;
		
		if (momentType == "previewDynamicMoment")
			momentDefinition = Session.get("momentPreviewContent");
		else if ( (momentType == "pinCodeCheckinForm" && Orgs.current() && Orgs.current().pinCodeCheckinFields()) ||
					(momentType == "familyCheckinForm") )
			momentDefinition = {
				hideTimePicker: true,
				momentFields:  Orgs.current().pinCodeCheckinFields(),
				hideComment: true
			};
		else if (Orgs.current() && Orgs.current().availableDynamicMomentTypes())
			momentDefinition = _.find( Orgs.current().availableDynamicMomentTypes(), 
				function(mt) { return mt.momentType == momentType; });
		
		return momentDefinition;
	},
	"iconForFieldType": function(fieldType) {
		switch (fieldType) {
			case "text": 
				return "glyphicon-comment";
			case "string":
			case "select":
			default:
				return "glyphicon-check";
		}
	},
	"momentDataFor": function(fieldId) {
		return (this.dynamicFieldValues && this.dynamicFieldValues[fieldId]) || "";
	},
	'staffPeople': function() {
		return Template.instance().staffPeopleRes.get();
	},
	'getFieldValuePart': function(momentFieldValue, partType) {

		if (typeof momentFieldValue === "string")
			return momentFieldValue;
		else if (partType=="label")
			return momentFieldValue.fieldValueLabel;
		else
			return momentFieldValue.fieldValue;
	},
	'customerDefinedListValues': function(momentFieldDefinition) {
		Session.get("taggedPeople");
		let dataSourceParts = momentFieldDefinition.source.split('.');
		switch (dataSourceParts[0]) {
			case 'careplan':
				let taggedPeople = Session.get("taggedPeople");

				const taggedPersonDynamicForm = Session.get("taggedPersonDynamicForm");

				if (Array.isArray(taggedPeople) && taggedPeople.length > 0 && taggedPeople[0].split('|')[0] == "person") {
					let taggedPerson = taggedPersonDynamicForm;
					if (taggedPerson && taggedPerson.carePlan && taggedPerson.carePlan[dataSourceParts[1]]) {
						return _.map(taggedPerson.carePlan[dataSourceParts[1]].split('\n'), function(listPart) {
							return {value: listPart};
						});
					}
				}
				return [];
		}
	},
	'formatTimeField': function(timeValue) {
		return timeValue && new moment(timeValue, "h:mm a").format("H:mm");
	}
});

Template.dynamicMomentForm.events({
	"click .btn-group button": function(event) {
		$(event.currentTarget).siblings().removeClass("active");
		$(event.currentTarget).addClass("active");
	}
})

Template.dynamicMomentForm.onDestroyed(function() {
	delete Session.keys["taggedPersonDynamicForm"];
});