<template name="moodMomentForm">
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Mood</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <div class="btn-group" id="moodLevel">
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentMoodLevel 'Happy'}}" data-moodlevel="Happy"><i class="icon-2x fad fa-swap-opacity fa-grin-beam text-white"></i></button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentMoodLevel 'SoSo'}}" data-moodlevel="SoSo"><i class="icon-2x fad fa-swap-opacity fa-meh text-white"></i></button>
        <button type="button" class="btn btn-primary {{activeIfEq currentMomentMoodLevel 'Sad'}}" data-moodlevel="Sad"><i class="icon-2x fad fa-swap-opacity fa-frown-open text-white"></i></button>
      </div>
    </div>
  </div>
  {{> momentFormTimePicker}}
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label">Comment</label>
    <div class="col-lg-6 col-md-9 col-sm-12">
      <textarea class="form-control" rows="3" placeholder="What's happening?" id="comment">{{comment}}</textarea>
    </div>
  </div>
</template>
