import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { Orgs } from '../../../../lib/collections/orgs';
import _ from '../../../../lib/util/underscore';
import './foodMomentForm.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../../lib/util/orgLanguageTranformationUtil';
import './bottleAmountsForm';
import { Foods } from '../../../../lib/collections/food';
Template.foodMomentForm.onCreated(function() {  
	this.subscribe('theFood');
})

Template.foodMomentForm.events({
	'click #foodType button': function(event, instance) {
		let name = $(event.target).data('foodtype');
		Session.set("activeMomentFoodType", name);

		let selectedGroupsQuery = [{selectedGroups: []}];
		let currentGroupId = Session.get("currentGroupId");
		if (currentGroupId) 
			selectedGroupsQuery.push({selectedGroups: currentGroupId});
		if (FlowRouter.current().path.includes("groups") && Session.get("activeMomentParentSource") == "group")
			selectedGroupsQuery.push({selectedGroups: {"$in": [Session.get("activeMomentParentSourceId")]}});

		let query = {$and: [
						{$or: selectedGroupsQuery},
					  	{meal: Session.get("activeMomentFoodType")} 
					]};
					
		const matchingFoods = Foods.findWithRecurrence({query:query}),
			q = matchingFoods.map(function(d) { return d.description;}),
			uniqueVal = _.first(q),
			matchingFoodItems = matchingFoods && matchingFoods.length > 0 && matchingFoods[0].foodItems;
		if ($("textarea#comment").val().trim() == "" ) $("textarea#comment").val( uniqueVal);

		if (matchingFoodItems) {
			$("input[name='food-id']").val(matchingFoods._id);
			Session.set("activeMomentFoodFoodItemSelections", _.map(matchingFoodItems, (fi) => ({
				name: fi,
				amount: null
			})));
		} else {
			$("input[name='food-id']").val("");
			Session.set("activeMomentFoodFoodItemSelections", []);
		}
	},
	'click #foodAmount button': function(event) {
		let name = $(event.target).data('foodamount');
		Session.set("activeMomentFoodAmount", name);
	},
	'click #foodBabyFoodType button': function(event) {
		let name = $(event.target).data('babyfoodtype');
		Session.set("activeMomentFoodBabyFoodType", name);
	},
	'click .food-item-container button': function(event, instance) {
		const clickedItem = $(event.currentTarget).data("id"),
			clickedItemValue = $(event.currentTarget).data("food-item-amount"),
			currentItems = Session.get("activeMomentFoodFoodItemSelections"),
			matchedItem = currentItems && currentItems[clickedItem];
		
		if (matchedItem) {
			currentItems[clickedItem].amount = clickedItemValue;
			Session.set("activeMomentFoodFoodItemSelections", currentItems);
		}
	}
});

Template.foodMomentForm.helpers({
	'currentMomentFoodType': function() {
		return Session.get("activeMomentFoodType");
	},
	'currentMomentFoodAmount': function() {
		return Session.get("activeMomentFoodAmount");
	},
	'currentMomentFoodAmountPercent': function() {
		return Session.get("activeMomentFoodAmountPercent");
	},
	'currentMomentBabyFoodType': function() {
		return Session.get("activeMomentFoodBabyFoodType");
	},
	'showBottleAmount': function() {
		return (Session.get("activeMomentFoodType") == "Bottle");
	},
	'showTubeAmount': function() {
		return (Session.get("activeMomentFoodType") == "Tube");
	},
	'showBabyFoodAmount': function() {
		return (Session.get("activeMomentFoodType") == "Baby Food");
	},
	'showCerealAmount': function() {
		return (Session.get("activeMomentFoodType") == "Cereal");
	},
	'showBottle': function() {
		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
		if (!currentPerson) return false;
		const currentGroup = currentPerson.findCheckedInGroup();
		return Orgs.current()  
				&& !Orgs.current().hasCustomization("moments/food/hideBottle") 
				&& (!Orgs.current().hasCustomization("moments/food/infantGroupOptions") || 
					 (currentGroup && currentGroup.typeInfant ));
	},
	'showBabyFood': function() {
		if (!Meteor.user() || !Meteor.user().fetchPerson()) return false;
		const currentGroup = Meteor.user().fetchPerson().findCheckedInGroup();
		return Orgs.current()  
				&& (Orgs.current().hasCustomization("moments/food/infantGroupOptions") &&  
				   currentGroup && currentGroup.typeInfant );
	},
	'showCereal': function() {
		if (!Meteor.user() || !Meteor.user().fetchPerson()) return false;
		const currentGroup = Meteor.user().fetchPerson().findCheckedInGroup();
		return Orgs.current()  
				&& (Orgs.current().hasCustomization("moments/food/infantGroupOptions") &&  
				   currentGroup && currentGroup.typeInfant );
	},
	'currentMomentFoodBottleWholeAmount': function() {
		return $.isNumeric(Session.get("activeMomentFoodBottleAmount")) ? 
					Math.floor(Session.get("activeMomentFoodBottleAmount")).toString() : "0";
	},
	'currentMomentFoodFoodTubeAmount': function() {
		return (Session.get("activeMomentFoodTubeAmount"));
	},
	'currentMomentFoodBottleFractionalAmount': function() {
		switch(Session.get("activeMomentFoodBottleAmount") % 1) {
			case 0.25:
				return "1/4";
			case 0.5:
				return "1/2";
			case 0.75:
				return "3/4";
			default:
				return "0";
		}
	},
	'foodUnits': function(foodType) {
		return Orgs.current().foodUnits(foodType);
	},
	'availableFoodItems': function() {
		return Session.get("activeMomentFoodFoodItemSelections");
	},
	'activeIfItemAmountEq': function(itemName, amount) {
		const selections = Session.get("activeMomentFoodFoodItemSelections"),
			matchedSelection = _.find( selections, (s) => s.name == itemName );
		return matchedSelection && matchedSelection.amount == amount ? "active" : "";
	},
	'getMomentTypesFoodAll': function () {
		return orgLanguageTranformationUtil.getMomentTypesFood('All');
	},
	'getMomentTypesFoodMost': function () {
		return orgLanguageTranformationUtil.getMomentTypesFood('Most');
	},
	'getMomentTypesFoodSome': function () {
		return orgLanguageTranformationUtil.getMomentTypesFood('Some');
	},
	'getMomentTypesFoodNone': function () {
		return orgLanguageTranformationUtil.getMomentTypesFood('None');
	},
	'getMomentTypesFoodNotOffered': function () {
		return orgLanguageTranformationUtil.getMomentTypesFood('NotOffered');
	}
});
