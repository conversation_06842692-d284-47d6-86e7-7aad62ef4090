<template name="mediaRequirementSettings">
	<form id="frmCredit">
		<div class="row">
			<div class="col-12 form-group">
				<label>Number of media attachments required</label>
				<input data-cy="media-number" type="text" class="form-control" name="mediaNumber" value="{{mediaRequirement.mediaNumber}}" {{disabledIfNotEditable cannotEditMediaRequirement}}/>
			</div>
		</div>
		<div class="row">
			<div class="col-12 form-group">
				<label>Media requirement timespan (# of days in which required attachments must appear)</label>
				<input data-cy="media-days" type="text" class="form-control" name="mediaDays" value="{{mediaRequirement.mediaDays}}" {{disabledIfNotEditable cannotEditMediaRequirement}}/>
			</div>
		</div>
		<div class="row">
			<div class="col-12 form-group">
				<label>Absence exclusion in days (if not checked in for greater than this number, exclude from report)</label>
				<input data-cy="media-absence-days" type="text" class="form-control" name="mediaAbsenceDays" value="{{mediaRequirement.mediaAbsenceDays}}" {{disabledIfNotEditable cannotEditMediaRequirement}}/>
			</div>
		</div>
		<div class="row">
		<div class="col-12 form-group">
			<label>Require Media Review for all Moments</label>
			<p class="font-weight-bolder">
				When this is selected, any media attached to a moment will require review by an Administrator. This will override individual child profile settings for Media Review.
			</p>
			<select class="form-control form-control-solid" name="mediaReviewRequired" value="{{mediaRequirement.mediaReviewRequired}}" {{disabledIfNotEditable cannotEditMediaRequirement}}>
				<option value="Yes">Yes</option>
				<option value="No">No</option>
			</select>
		</div>
		</div>

	</form>
</template>

