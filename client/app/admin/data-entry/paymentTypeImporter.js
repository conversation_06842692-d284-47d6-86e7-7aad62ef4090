import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './paymentTypeImporter.html';


Template.paymentTypeImporter.onCreated( function () {
	var self = this;
	self.results = new ReactiveVar();
});

Template.paymentTypeImporter.helpers({
	results() {
		return Template.instance().results.get();
	}
});

Template.paymentTypeImporter.events({
	"click #btnUpload" : function(e, i) {
		e.preventDefault();
		$("#btnUpload").prop("disabled", true).text("Processing...");
		i.results.set(null);
		var f = document.getElementById('fileInput').files[0];
		console.log("read file");
		const importMode = $("#import-mode").val();
		readFile(f, function(content) {
			Meteor.callAsync('processPaymentTypeImport', {fileContents: content, importMode})
			.then((result)=>{
				$("#btnUpload").prop("disabled", false).text("Upload");
				i.results.set(result.results);
			})
			.catch((error)=>{
				$("#btnUpload").prop("disabled", false).text("Upload");
				mpSwal.fire("Error", error.reason, "error");
			});
		});
	}
});
   
function readFile(f,onLoadCallback) {
	var reader = new FileReader();
	reader.onload = function (e) {
		var contents = e.target.result;
		onLoadCallback(contents);
	}
	reader.readAsText(f);
};