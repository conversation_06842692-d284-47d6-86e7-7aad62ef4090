import Sortable from "sortablejs";
import './earlyPickDropReasons.html';
import '../announcements/_announcementOrgsField';
import { Orgs } from "../../../lib/collections/orgs";

Template.earlyPickDropReasons.created = function() {
    this.reasons = new ReactiveVar([]);
    this.newIndex = new ReactiveVar(0);
    this.canSave = new ReactiveVar(false);
    Meteor.callAsync('getEarlyPickDropReasons').then(reasons => {
        this.reasons.set(reasons);
        if (reasons.length === 0) {
            this.reasons.set([{ reason: '', order: 0, _id: '-1' }]);
            this.newIndex.set(1);
        }
    });
    this.switchableOrgs = new ReactiveVar([]);
    Meteor.callAsync('getSwitchableSites').then(result => {
        this.switchableOrgs.set(result);
    });
}

Template.earlyPickDropReasons.rendered = function() {
    const sortable = Sortable.create(document.getElementById('earlyPickDropReasonsList'), {
        handle: ".sort-handle",
        direction: 'vertical',
        onUpdate: () => {
            document.getElementById('earlyPickDropReasonsForm').dispatchEvent(new Event('input'));
        }
    });
}

Template.earlyPickDropReasons.helpers({
    reasons: function() {
        return Template.instance().reasons.get();
    },
    canDelete: function(id) {
        return id < 0;
    },
    disabled: function() {
        return !Template.instance().canSave.get();
    },
    showPropagate: function() {
        return Template.instance().switchableOrgs.get().length > 1;
    },
    getEarlyPickDropMinuteValue: function() {
        const currentValueInMinutes = (Orgs.current() && Orgs.current().earlyPickDropMinute) ?? 0;
		return parseInt(currentValueInMinutes);
    },
});

Template.earlyPickDropReasons.events({
    'input #earlyPickDropReasonsForm': function(e, instance) {
        const canSave = document.getElementById('earlyPickDropReasonsForm').checkValidity();
        if (canSave) {
            const reasonNodes = document.querySelectorAll('.earlyPickDropReasonGroup');
            for (const node of reasonNodes) {
                if (node.querySelector('.earlyPickDropReason').value.trim() === '') {
                    instance.canSave.set(false);
                    return;
                }
            }
            instance.canSave.set(true);
            return;
        }
        instance.canSave.set(false);
    },
    'input .earlyPickDropReason': function(e, instance) {
        const id = e.currentTarget.dataset.id;
        const value = e.currentTarget.value;
        instance.reasons.set(instance.reasons.get().map((reason) => {
            if (String(reason._id) === String(id)) {
                reason.reason = value;
            }
            return reason;
        }))
    },
    'click .archiveEarlyPickDropReason': function(e, instance) {
        const id = e.currentTarget.dataset.id;
        const value = e.currentTarget.checked;
        instance.reasons.set(instance.reasons.get().map((reason) => {
            if (String(reason._id) === String(id)) {
                reason.archived = value;
            }
            return reason;
        }))
    },
    'click #addEarlyPickDropReason': function(e, instance) {
        e.preventDefault()
        const reasons = instance.reasons.get();
        instance.newIndex.set(instance.newIndex.get() + 1);
        reasons.push({ reason: '', _id: String(-(instance.newIndex.get())) });
        instance.reasons.set(reasons);
        setTimeout(() => {
            document.getElementById('earlyPickDropReason--' + instance.newIndex.get()).focus();
        }, 100);
    },
    'click .removeEarlyPickDropReason': function(e, instance) {
        e.preventDefault();
        const id = e.currentTarget.dataset.id;
        const reasons = instance.reasons.get().filter((reason) => String(reason._id) !== String(id));
        instance.reasons.set(reasons);
    },
    'click #saveEarlyPickDropReasons': function(e, instance) {
        e.preventDefault();
        const reasons = [];
        const reasonNodes = document.querySelectorAll('.earlyPickDropReasonGroup');
        for (const node of reasonNodes) {
            reasons.push({
                _id: node.querySelector('.earlyPickDropReason').dataset.id,
                reason: node.querySelector('.earlyPickDropReason').value.trim(),
                archived: node.querySelector('.archiveEarlyPickDropReason').checked,
            })
        }
        let reasonOrgs = null;
        const orgsSelect = document.querySelector('.earlyPickDropReasonOrgs select.announcement-orgs');
        if (orgsSelect) {
            reasonOrgs = Array.from(orgsSelect.selectedOptions).map(option => option.value);
        }
        const earlyPickDropMinute = $('#earlyPickDropMinute').val();
        Meteor.callAsync('saveEarlyPickDropReasons', reasons, reasonOrgs, earlyPickDropMinute).then(res => {
            instance.reasons.set(res);
            instance.canSave.set(false)
            mpSwal.fire('Saved!');
        }).catch(err => {
            mpSwal.fire('Error saving!');
        });
    },
    'input #earlyPickDropMinute'(event) {
        let input = event.target.value;
        const val = input.slice(0, 3); // Limit input to 3 digits
        if(Number(val) < 0){
            event.target.value = "";
        }
        else{
            event.target.value = val
        } 
    }
});