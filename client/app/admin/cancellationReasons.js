import Sortable from "sortablejs";
import './cancellationReasons.html';
import '../announcements/_announcementOrgsField';

Template.cancellationReasons.created = function() {
    this.reasons = new ReactiveVar([]);
    this.newIndex = new ReactiveVar(0);
    this.canSave = new ReactiveVar(false);
    Meteor.callAsync('getCancellationReasons').then(reasons => {
        this.reasons.set(reasons);
        if (reasons.length === 0) {
            this.reasons.set([{ reason: '', order: 0, _id: '-1' }]);
            this.newIndex.set(1);
        }
    });
    this.switchableOrgs = new ReactiveVar([]);
    Meteor.callAsync('getSwitchableSites').then(result => {
        this.switchableOrgs.set(result);
    });
}

Template.cancellationReasons.rendered = function() {
    const sortable = Sortable.create(document.getElementById('cancellationReasonsList'), {
        handle: ".sort-handle",
        direction: 'vertical',
        onUpdate: () => {
            document.getElementById('cancellationReasonsForm').dispatchEvent(new Event('input'));
        }
    });
}

Template.cancellationReasons.helpers({
    reasons: function() {
        return Template.instance().reasons.get();
    },
    canDelete: function(id) {
        return id < 0;
    },
    disabled: function() {
        return !Template.instance().canSave.get();
    },
    showPropagate: function() {
        return Template.instance().switchableOrgs.get().length > 1;
    }
});

Template.cancellationReasons.events({
    'input #cancellationReasonsForm': function(e, instance) {
        const canSave = document.getElementById('cancellationReasonsForm').checkValidity();
        if (canSave) {
            const reasonNodes = document.querySelectorAll('.cancellationReasonGroup');
            for (const node of reasonNodes) {
                if (node.querySelector('.cancellationReason').value.trim() === '') {
                    instance.canSave.set(false);
                    return;
                }
            }
            instance.canSave.set(true);
            return;
        }
        instance.canSave.set(false);
    },
    'input .cancellationReason': function(e, instance) {
        const id = e.currentTarget.dataset.id;
        const value = e.currentTarget.value;
        instance.reasons.set(instance.reasons.get().map((reason) => {
            if (String(reason._id) === String(id)) {
                reason.reason = value;
            }
            return reason;
        }))
    },
    'click .archiveCancellationReason': function(e, instance) {
        const id = e.currentTarget.dataset.id;
        const value = e.currentTarget.checked;
        instance.reasons.set(instance.reasons.get().map((reason) => {
            if (String(reason._id) === String(id)) {
                reason.archived = value;
            }
            return reason;
        }))
    },
    'click #addCancellationReason': function(e, instance) {
        e.preventDefault()
        const reasons = instance.reasons.get();
        instance.newIndex.set(instance.newIndex.get() + 1);
        reasons.push({ reason: '', _id: String(-(instance.newIndex.get())) });
        instance.reasons.set(reasons);
        setTimeout(() => {
            document.getElementById('cancellationReason--' + instance.newIndex.get()).focus();
        }, 100);
    },
    'click .removeCancellationReason': function(e, instance) {
        e.preventDefault();
        const id = e.currentTarget.dataset.id;
        console.log({id});
        console.log({reasons: instance.reasons.get()});
        const reasons = instance.reasons.get().filter((reason) => String(reason._id) !== String(id));
        instance.reasons.set(reasons);
    },
    'click #saveCancellationReasons': function(e, instance) {
        e.preventDefault();
        const reasons = [];
        const reasonNodes = document.querySelectorAll('.cancellationReasonGroup');
        for (const node of reasonNodes) {
            reasons.push({
                _id: node.querySelector('.cancellationReason').dataset.id,
                reason: node.querySelector('.cancellationReason').value.trim(),
                archived: node.querySelector('.archiveCancellationReason').checked,
            })
        }
        let reasonOrgs = null;
        const orgsSelect = document.querySelector('.cancellationReasonOrgs select.announcement-orgs');
        if (orgsSelect) {
            reasonOrgs = Array.from(orgsSelect.selectedOptions).map(option => option.value);
        }
        Meteor.callAsync('saveCancellationReasons', reasons, reasonOrgs).then(res => {
            instance.reasons.set(res);
            mpSwal.fire('Saved!');
        }).catch(err => {
            mpSwal.fire('Error saving!');
        });
    }
});