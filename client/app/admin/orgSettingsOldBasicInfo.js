import { Orgs } from "../../../lib/collections/orgs";
import './orgSettingsOldBasicInfo.html';

// Delete file with CRM-14894
const timezones = {
    "America/New_York": "Eastern Time",
    "America/Chicago": "Central Time",
    "America/Denver": "Mountain Time",
    "America/Phoenix": "Mountain Time (Arizona)",
    "America/Los_Angeles": "Pacific Time",
    "Pacific/Honolulu": "Hawaii-Aleutian Time"
};


Template.orgSettingsOldBasicInfo.onCreated(function () {
    var self = this;
    self.hasDreambox = new ReactiveVar(false);
    Meteor.callAsync('topHasDreambox', Orgs.current()._id).then(result => {
        self.hasDreambox.set(true);
    });
})

Template.orgSettingsOldBasicInfo.helpers({
    "orgName": function () {
        return Orgs.current().name;
    },
    "hasDreambox": function () {
        return Template.instance().hasDreambox.get();
    },
    "industry": function () {
        switch (Orgs.current().language) {
            case "translationsEnAdultCare":
                return "Adult / Senior";
            default:
                return "Child Care / Schools";
        }
    },
    "timezoneLabel": () => {
        return Orgs.current() && Orgs.current().getTimezone() && timezones[Orgs.current().getTimezone()];
    },
    "shortCodeLabel": () => {
        return Orgs.current() && Orgs.current().getShortCode()
    },
    "legalFacilityName"() {
        return Orgs.current() && Orgs.current().legalFacilityName;
    },
    "facilityLicenseNumber"() {
        return Orgs.current() && Orgs.current().facilityLicenseNumber;
    },
    "phoneNumber"() {
        return Orgs.current() && Orgs.current().phoneNumber;
    },
    "schoolHighGrade"() {
        return Orgs.current() && Orgs.current().schoolHighGrade;
    },
    "schoolLowGrade"() {
        return Orgs.current() && Orgs.current().schoolLowGrade;
    },
})

Template.orgSettingsOldBasicInfo.events({
    "click .btnEditBasicInfo": function (event) {
        event.preventDefault();
        mpSwal.fire({
            title: "Edit Organization Name",
            text: "Please enter the name you would like to use for your organization",
            input: "text",
            showCancelButton: true,
            inputValue: Orgs.current() && Orgs.current().name
        }).then(async result => {
            if (result.value) {
                await Meteor.callAsync("changeOrgName", result.value);
            }
        })
    },
    "click .btnEditShortCode": function (event) {
        event.preventDefault();
        mpSwal.fire({
            title: "Edit Site/School Code",
            text: "Please enter the short code of your site, It must be less than 31 characters",
            input: "text",
            showCancelButton: true,
            inputValue: Orgs.current() && Orgs.current().shortCode
        }).then(result => {
            if (result.value) {
                Meteor.callAsync("changeOrgShortCode", result.value).then(response => {
                    mpSwal.fire({ title: "Save Successful!" });
                }).catch(error => {
                    mpSwal.fire({ icon: "error", title: "Problem with saving short code", text: error.reason });
                });
            }
        });
    },
    "click .btnEditTimezone": function () {
        mpSwal.fire({
            title: "Select organization timezone",
            text: "Please select the timezone for your organization.  Please note that all times will reflect the chosen timezone.",
            input: "select",
            inputOptions: timezones,
            inputValue: Orgs.current() && Orgs.current().getTimezone(),
            showCancelButton: true,
        }).then(async result => {
            if (result.value) {
                await Meteor.callAsync("updateOrgInfo", "timezone", result.value);
            }
        });
    },
    'click .btnEditFacilityName': function (e, i) {
        event.preventDefault();
        mpSwal.fire({
            title: "Edit Organization Legal Facility Name",
            text: "Please enter the Legal Facility Name you would like to use for your organization",
            input: "text",
            showCancelButton: true,
            inputValue: Orgs.current() && Orgs.current().legalFacilityName
        }).then(async result => {
            if (result.value) {
                await Meteor.callAsync("changeLegalFacilityName", result.value);
            }
        })
    },
    'click .btnEditPhoneNumber': function (e, i) {
        event.preventDefault();
        mpSwal.fire({
            title: "Edit Phone Number",
            text: "Please enter the phone number for your organization",
            input: "text",
            showCancelButton: true,
            inputValue: Orgs.current() && Orgs.current().phoneNumber
        }).then(async result => {
            if (result.value) {
                await Meteor.callAsync("changeCustomerProperty", Orgs.current()._id, { area: 'phoneNumber', value: result.value });
            }
        })
    },
    'click .btnEditSchoolLowGrade': function (e, i) {
        event.preventDefault();
        mpSwal.fire({
            title: "Edit School Low Grade",
            text: "Please enter the Low Grade for your organization (if applicable)",
            input: "text",
            showCancelButton: true,
            inputValue: Orgs.current() && Orgs.current().schoolLowGrade
        }).then(async result => {
            if (result.value) {
                await Meteor.callAsync("changeCustomerProperty", Orgs.current()._id, { area: 'schoolLowGrade', value: result.value });
            }
        })
    },
    'click .btnEditSchoolHighGrade': function (e, i) {
        event.preventDefault();
        mpSwal.fire({
            title: "Edit School High Grade",
            text: "Please enter the High Grade for your organization (if applicable)",
            input: "text",
            showCancelButton: true,
            inputValue: Orgs.current() && Orgs.current().schoolHighGrade
        }).then(async result => {
            if (result.value) {
                await Meteor.callAsync("changeCustomerProperty", Orgs.current()._id, { area: 'schoolHighGrade', value: result.value });
            }
        })
    },
    'click .btnEditFacilityLicence': function (e, i) {
        event.preventDefault();
        mpSwal.fire({
            title: "Edit Organization Facility License Number",
            text: "Please enter the Facility License Number you would like to use for your organization",
            input: "text",
            showCancelButton: true,
            inputValue: Orgs.current() && Orgs.current().facilityLicenseNumber
        }).then(async result => {
            if (result.value) {
                await Meteor.callAsync("changeFacilityLicenseNumber", result.value);
            }
        })
    },
})