<template name="airslateSlates">
{{#if isLoaded}}
	<table class='table'>
		<tr>
			<th>Updated Date/time</th>
			<th>Flow</th>
			<th>Status</th>
			<th>Actions</th>
		</tr>
	{{#each slate in slates}}
		<tr>
			<td>
				{{formatDate slate.updatedAt "MM/DD/YYYY hh:mm:ss a"}}
			</td>
			<td>
				<a href="/admin/integrations/airslate/flow?id={{slate.airslateConfigurationId}}&slate-id={{slate._id}}">{{deep slate "configuration.description"}}</a>
			</td>
			<td>
				{{#if trueIfEq slate.statusFlag "error"}}
				<a href="#" class="btn-view-slate-error" data-slate-id="{{slate._id}}"><i class="fa fa-exclamation-circle" aria-hidden="true"></i></a>
				{{else if trueIfEq slate.statusFlag "pending"}}
				<i class="fa fa-clock-o" aria-hidden="true"></i>
				{{else}}
				<i class="fa fa-check" aria-hidden="true"></i>
				{{/if}}
			</td>
			<td>
				{{#if canManageAirslate}}
				<a href="#" class="btn-reload-slate" data-id="{{slate._id}}">Reload</a> | 
				{{/if}}
				<a href="#" class="btn-view-slate" data-id="{{slate._id}}">Preview</a> 
			</td>
		</tr>
	{{/each}}
	</table>
{{else}}
{{> loading}}
{{/if}}
</template>

<template name="airslatePreviewDocument">
	<table class="table">
	{{#each field in sourceFields}}
		<tr style="font-size:10px">
			<td>{{field.documentName}}</td>
			<td>{{field.name}}</td>
			<td>{{field.location}}</td>
			<td>{{field.sampleValue}}</td>
		</tr>
	{{/each}}
	</table>
</template>