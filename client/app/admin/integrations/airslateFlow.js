import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from '../../../../lib/collections/people';
const dot = require('dot-object');
import './airslateFlow.html';
import { processPermissions } from '../../../../lib/permissions';
import { Orgs } from '../../../../lib/collections/orgs';
import $ from 'jquery';
import { showModal } from '../../main';
import '../../simpleModal/simpleModal';

Template.airslateFlow.onCreated( function() {
	var self = this;
	self.flow = new ReactiveVar();
	self.flowDocuments = new ReactiveVar();

	this.autorun( async () => {
		try {
			self.flowDocuments.set(await getOutputFlowDocuments(self));
		} catch (error) {
			console.log(error);
		}
	});
	getFlow(self);
});

Template.airslateFlow.helpers({
	isLoading(){
		return !Template.instance().flow.get();
	},
	flow() {
		return Template.instance().flow.get();
	},
	flowDocuments() {
		return Template.instance().flowDocuments.get();
	},
	mappingsSorted(mappings) {
		_.each(mappings, (m, i) => { m.mappingIndex = i;});
		return _.sortBy(mappings, m => `${m.action}|${m.target}|${m.index}|${m.destination}`);
	},
	canManageAirslate() {
		return processPermissions({
			assertions: [{ context: "integrations/airslate", action: "edit" }],
			evaluator: (person) => person.type=="admin"
		  });
	}
});

Template.airslateFlow.events({
	"click .btn-edit-mapping, click #btn-add-mapping": async (e, i) => {
		e.preventDefault();
		const itemIdx = $(e.currentTarget).data("idx"), item = parseInt(itemIdx) >= 0 && i.flow.get().mapping.mappings[itemIdx];
		
		const recentDocument = i.flow.get().recentDocument;
		if (!recentDocument)
			return mpSwal.fire("No recent document", "A slate has not yet been received by MomentPath to facilitate mapping.  Please try submitting a slate through airSlate.");

		const {sourceDocuments, sourceFields, availableDestinationFields } = await processDocument(recentDocument.content);		

		showModal("simpleModal", {
			title: "Edit Mapping",
			template: "airslateAddEditMapping",
			data: {  itemIdx, item: item || {}, sourceDocuments, sourceFields, availableDestinationFields },
			onSave: (saveEvent, formInstance, formFieldData) => {
				const selectedDocument = formFieldData["source-document"] && sourceDocuments.find( sd => sd.name == formFieldData["source-document"]);
				formFieldData.selectedDocumentIdx = selectedDocument && selectedDocument.location.match(/documents\[(\d+)\]/)[1];
				const matchedFieldSource = formFieldData["source-field"] >= 0 && sourceFields[formFieldData["source-field"]];
				formFieldData.sourceFieldPath = matchedFieldSource?.location;
				formFieldData.sourceFieldName = matchedFieldSource?.name;

				Meteor.callAsync("airslateUpsertMapping", {...formFieldData, itemIdx, mappingId: i.flow.get().airslateMappingId}).then(result => {
					mpSwal.fire("Mapping added successfully.");
					$("#simpleModal").modal("hide");
					getFlow(i);
				}).catch(error => {
					$(saveEvent.target).html('Save').prop("disabled", false);
					return mpSwal.fire("Error adding mapping", error.reason, "error");
				});
			}
		});
	},
	"click .btn-delete-mapping"(e, i) {
		e.preventDefault();
		const itemIdx = $(e.currentTarget).data("idx"), mappingId = i.flow.get().airslateMappingId;
		
		mpSwal.fire({
			title: "Confirm Deletion",
			text: `You are about to delete this mapping. Are you sure?`,
			showCancelButton:true
		}).then( result => {
			if (result.value) {
				
				Meteor.callAsync("airslateDeleteMapping", {mappingId, index: itemIdx}).then(response => {
					mpSwal.fire("Map deleted successfully");
					getFlow(i);
				}).catch(error => {
					mpSwal.fire("Error deleting mapping", error.reason, "error");
				});
			}
		});
	},
	"click #btn-copy-existing-definition"(e, i) {
		e.preventDefault();
		Meteor.callAsync("airslateGetAvailableDefinitions").then(result => {
			showModal("simpleModal", {
				title: "Copy Existing Definition",
				template: "airslateCopyDefinition",
				data: { definitions: result },
				onSave: (saveEvent, formInstance, formFieldData) => {
					formFieldData.flowId = i.flow.get()._id;
					Meteor.callAsync("airslateAssociateOrCreateDefinition", formFieldData).then(result => {
						mpSwal.fire("Definition added successfully.");
						$("#simpleModal").modal("hide");
						getFlow(i);
					}).catch(error => {
						$(saveEvent.target).html('Save').prop("disabled", false);
						return mpSwal.fire("Error assigning definition", error.reason, "error");
					});
				}
			});
		}).catch(error => {
			showModal("simpleModal", {
				title: "Copy Existing Definition",
				template: "airslateCopyDefinition",
				data: { definitions: undefined },
				onSave: (saveEvent, formInstance, formFieldData) => {
					formFieldData.flowId = i.flow.get()._id;
					Meteor.callAsync("airslateAssociateOrCreateDefinition", formFieldData).then(result => {
						mpSwal.fire("Definition added successfully.");
						$("#simpleModal").modal("hide");
						getFlow(i);
					}).catch(error => {
						$(saveEvent.target).html('Save').prop("disabled", false);
						return mpSwal.fire("Error assigning definition", error.reason, "error");
					});
				}
			});
		});
	},
	"click #btn-create-new-definition"(e, i) {
		e.preventDefault();
		mpSwal.fire({
			title: "Create New Mapping Template",
			text: "Name your new mapping template:",
			input: "text",
			showCancelButton: true
		}).then( formResult => {
			if (formResult.value) {
				Meteor.callAsync("airslateAssociateOrCreateDefinition", {name: formResult.value, flowId: i.flow.get()._id}).then(result => {
					mpSwal.fire("Definition created successfully.");
					getFlow(i);
				}).catch(error => {
					return mpSwal.fire("Error creating definition", error.reason, "error");
				});
			}
		});
	},
	"click .btn-edit-document": async (e, i) => {
		e.preventDefault();
		const itemIdx = $(e.currentTarget).data("idx"), item = parseInt(itemIdx) >= 0 && await getOutputFlowDocuments(i)[itemIdx];
		
		const recentDocument = i.flow.get().recentDocument;
		if (!recentDocument)
			return mpSwal.fire("No recent document", "A slate has not yet been received by MomentPath to facilitate mapping.  Please try submitting a slate through airSlate.");

		const {sourceDocuments } = await processDocument(recentDocument.content),
			destinationDocuments = Orgs.current().documentDefinitions?.filter(dd => !dd.deletedAt)
				.map(dd => { dd.displayName = (dd.section ? dd.section + ": " : "" ) + dd.name; return dd; });		

		showModal("simpleModal", {
			title: "Edit Document Assignment",
			template: "airslateAddEditDocument",
			data: {  itemIdx, item: item || {}, sourceDocuments, destinationDocuments },
			onSave: (saveEvent, formInstance, formFieldData) => {
				formFieldData.itemIdx = item.itemIdx;
				Meteor.callAsync("airslateUpsertDocumentAssignment", {...formFieldData, mappingId: i.flow.get().airslateMappingId}).then(result => {
					mpSwal.fire("Document updated successfully.");
					$("#simpleModal").modal("hide");
					getFlow(i);
				}).catch(error => {
					$(saveEvent.target).html('Save').prop("disabled", false);
					return mpSwal.fire("Error adding document assignment", error.reason, "error");
				});
			}
		});
	},
	"click .btn-fix-mapping"(e, i) {
		e.preventDefault();
		$(e.target).html('Fixing').prop("disabled", true);
		
		const itemIdx = $(e.currentTarget).data("idx"), flow = i.flow.get(), mappingItem = flow.mapping.mappings[itemIdx];
		if (!mappingItem || !mappingItem.newLocation) return;

		const options = {
			mappingId: flow.mapping._id,
			itemIdx,
			newLocation: mappingItem.newLocation
		}
		Meteor.callAsync("airslateFixMapping", options).then(result => {
			mpSwal.fire("Mapping updated successfully.");
			getFlow(i);
		}).catch(error => {
			$(e.target).html('Fix').prop("disabled", false);
			return mpSwal.fire("Error fixing mapping", error.reason, "error");
		});
	}
});

function getFlow(instance) {
	const flowId = FlowRouter.current().queryParams.id, slateId = FlowRouter.current().queryParams["slate-id"];

	Meteor.callAsync("airslateGetFlow", {flowId, slateId}).then(async data => {
		instance.flow.set(data);
		await validateMappings(instance);
	}).catch(error => {
		return mpSwal.fire("Error", "Error retrieving flow definition.", "error");
	});
}

async function processDocument( documentContent ) {
	const processedMap = dot.dot(documentContent),
	mapEntries = Object.entries(processedMap),
	reDocument = /data\[.+\]\.attributes\.payload\.documents\[.+\]\.attributes\.name/i,
	reField = /data\[.+\]\.attributes\.payload\.documents\[.+\]\.fields\[.+\]\.data\.attributes\.name/i,
	sourceDocuments = mapEntries.filter( entry => entry[0].match(reDocument)).map(entry => ({name: entry[1], location: entry[0]})),
	sourceFields = mapEntries.filter( entry => entry[0].match(reField)).map(entry => {
		const documentNumber = parseInt(entry[0].match(/documents\[(\d+)\]/)[1]);
		return {
			name: entry[1], 
			location: entry[0].replace(".name", ".value"),
			documentNumber,
			documentName: sourceDocuments[documentNumber]?.name
		};
	}),
	availableDestinationFields = { person: await getDestinationFields("person"), family: await getDestinationFields("family") };
	sourceFields.forEach(sourceField => {
		sourceField.sampleValue = processedMap[sourceField.location];
	});

	return {
		sourceDocuments,
		sourceFields,
		availableDestinationFields
	};
}

async function getOutputFlowDocuments( instance ) {
	const org = Orgs.current(), flow = instance.flow.get(), 
		recentDocument = flow?.recentDocument,
		{ sourceDocuments } = await processDocument(recentDocument.content);	

	let outputFlowDocuments = [];
	flow.mapping?.documents?.forEach( (documentMapping, itemIdx) => {
		const template = org?.documentDefinitions?.find( dd => dd._id == documentMapping.templateId);
		documentMapping.template = template;
		documentMapping.templateDisplayName = template && (template.section ? template.section + ": " : "" ) + template.name;
		documentMapping.itemIdx = itemIdx;
		outputFlowDocuments.push(documentMapping);
		const matchedSource = sourceDocuments?.find( sd=> sd.name.includes(documentMapping.name));
		if (matchedSource) {
			matchedSource.handled = true;
		}
	});

	sourceDocuments?.filter( sd => !sd.handled).forEach( sd => {
		sd.unmatched = true;
		sd.existingItemIdx = -1;
		outputFlowDocuments.push(sd);
	});
	return outputFlowDocuments;
}

async function validateMappings( instance ) {
	const flow = instance.flow.get(), recentDocument = flow.recentDocument;
	if (!recentDocument || !flow.mapping?.mappings)
		return ;

	const {sourceFields } = await processDocument(recentDocument.content);

	let errorCount = 0;
	flow.mapping.mappings.forEach( mapping => {
		const sourceField = sourceFields.find( sf => sf.location == mapping.sourceFieldAbsolute);
		if (!sourceField) {
			mapping.fieldError = "Source field location no longer exists in current sample document";
			const newLocation = sourceFields.find( sf => sf.name == mapping.sourceFieldName);
			if (newLocation) 
				mapping.newLocation = newLocation;
			errorCount++;
		}
		else if (sourceField && sourceField.name != mapping.sourceFieldName) {
			mapping.fieldError = "Source field name no longer matches location in current sample document";
			const newLocation = sourceFields.find( sf => sf.name == mapping.sourceFieldName);
			if (newLocation) 
				mapping.newLocation = newLocation;
			errorCount++;
		}
		else {
			mapping.sampleValue = sourceField.sampleValue;
		}
	});

	flow.includesValidation = true;
	if (errorCount > 0)
		flow.validationMessage = "There are 1 or more errors with your current map.  Please see below for details.";

	instance.flow.set(flow);
}

Template.airslateAddEditMapping.onCreated( function () {
	var self = this;
	self.selectedType = new ReactiveVar( self.data?.item?.target);
});

Template.airslateAddEditMapping.onRendered( function() {
	var self = this;
	
	$("select[name='source-field']").select2({
		data: [{id: -1, text: "None", name: "", location: "", sampleValue: ""}]
			.concat(self.data.sourceFields.map( (sf, idx) => { 
				sf.id = idx; 
				sf.text = sf.name + ' ' + sf.location; 
				sf.selected = sf.location == self.data.item.sourceFieldAbsolute;
				return sf; 
			})),
		dropdownParent: $('#formAirslateAddEditMapping'),
		templateResult: function ( item ) {
			return `<div class="clearfix">
				<div class="d-flex justify-content-between"><div>${item.name}</div><div style="font-style:italic">${item.sampleValue}</div></div>
				<div style="font-size:8px">${item.location}</div>
			</div>`;
		},
		escapeMarkup: function (markup) { return markup; }, // let our custom formatter work
		placeHolder: "Select a field..."
	});
});

Template.airslateAddEditMapping.helpers( {
	"destinationFields"() {
		const i = Template.instance(), selectedType = i.selectedType.get();
		return i.data.availableDestinationFields[selectedType];
	},
	"currentAction"() {
		return Template.instance().selectedType.get();
	}
});

Template.airslateAddEditMapping.events( {
	"change select[name='action']"(e,i) {
		const newVal = $("select[name='action']").val();
		i.selectedType.set(newVal);
	}
});

async function getDestinationFields(personType) {
	let fieldList = (await People.getCollapsedProfileFieldList(personType, true)).map(field => "profile." + field.prefixedName);
	return fieldList.concat(["firstName", "lastName", "phoneNumber", "profileEmailAddress", "customerOrganizationID", "hubspotID"]).sort();
}

Template.airslateCopyDefinition.onRendered(function() {
	var self = this;
	$("select[name='source-definition']").select2();
	$("#source-definition").on("change", function() {
		const selectedId = $("#source-definition").val(),
			selectedElement = self.data.definitions.find(d => d._id == selectedId),
			currentName = $("input[name='name']").val() || "";

		if (currentName == "") 
			$("input[name='name']").val(selectedElement.name + " copy");
	});
});
