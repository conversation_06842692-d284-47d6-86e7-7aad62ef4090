import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './adp.html';
import { Orgs } from '../../../../lib/collections/orgs';

Template.adp.onCreated(function() {
    this.saveDisabled = new ReactiveVar(true);
})

Template.adp.onRendered(function() {
    $('#btnSaveFtpCredentials').prop('disabled', this.saveDisabled.get());
});


Template.adp.helpers({
    getFtpUrl() {
        const org = Orgs.current();
        return org.ftpCredentials?.adp?.url
    },
    getFtpLogin() {
        const org = Orgs.current();
        return org.ftpCredentials?.adp?.login
    },
    getFtpPassword() {
        const org = Orgs.current();
        return org.ftpCredentials?.adp?.password
    },
    getFtpFilePath() {
        const org = Orgs.current();
        return org.ftpCredentials?.adp?.filePath
    },
    adpSettings() {
        return Orgs.current()?.adp || {};
    }
});

Template.adp.events({
   'click #btnSaveFtpCredentials': function(e) {
       e.preventDefault();
       const url = $("#ftpUrl").val();
       const login = $("#ftpLogin").val();
       const password = $("#ftpPassword").val();
       const filePath = $("#ftpFilePath").val();

       Meteor.callAsync("updateAdpFtpLoginCredentials", { url, filePath, login, password }).then(result => {
           mpSwal.fire({title:"Save Successful!"});
       }).catch(error => {
           mpSwal.fire({icon:"error", title:"Problem Ftp login Credentials", text: error.reason});
       });
   },
    'click #btnSaveEmailSettings': function(e) {
        e.preventDefault();
        if (!document.getElementById('emailSettingsForm').reportValidity()) {
            mpSwal.fire({
                icon: 'warning',
                text: 'Invalid email(s)'
            });
            return;
        }
        const email1 = document.getElementById('email1').value;
        const email2 = document.getElementById('email2').value;

        Meteor.callAsync("updateAdpEmailSettings", { email1, email2 }).then(result => {
            mpSwal.fire({title:"Save Successful!"});
        }).catch(error => {
            mpSwal.fire({icon:"error", title:"Problem updating email settings", text: error.reason});
        });
    },
   'input #ftpUrl, input #ftpLogin, input #ftpPassword': function(event, instance) {
       const ftpUrl = $('#ftpUrl').val();
       const ftpLogin = $('#ftpLogin').val();
       const ftpPassword = $('#ftpPassword').val();
       instance.saveDisabled.set((ftpUrl.length === 0 || ftpLogin.length === 0 || ftpPassword.length === 0) && (ftpUrl.length > 0 || ftpLogin.length > 0 || ftpPassword.length > 0) );
       $('#btnSaveFtpCredentials').prop('disabled', instance.saveDisabled.get());
   },
    "click #btnProcessCsv": function () {
        Meteor.callAsync("processAdpCsv" , Orgs.current()).then(response => {
            mpSwal.fire("Successfully Processed CSV");
        }).catch(error => {
            mpSwal.fire("Error", error.reason, "error");
        });
    },
});
