<!-- Delete file with CRM-14894 -->

<template name="orgSettingsOldBasicInfo">
    <div class="tab-pane active" id="basicInfo" role="tabpanel">
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Organization Name</label>
            <label class="col-lg-6 col-xl-3 col-form-label">
                {{orgName}}
            </label>
            <label class="col-lg-3 col-xl-3">
                <div class="btn btn-primary font-weight-bolder btn-text-white btnEditBasicInfo ml-4">
                    <i class="fad-regular fad fa-pen-square fa-swap-opacity mr-2 text-white"></i>Edit
                </div>
            </label>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Short Code</label>
            <label class="col-lg-6 col-xl-3 col-form-label">
                {{shortCodeLabel}}
            </label>
            <label class="col-lg-3 col-xl-3">
                <div class="btn btn-primary font-weight-bolder btn-text-white btnEditShortCode ml-4">
                    <i class="fad-regular fad fa-pen-square fa-swap-opacity mr-2 text-white"></i>Edit
                </div>
            </label>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right">Industry</label>
            <label class="col-lg-9 col-xl-6">
                {{industry}}
                <a href="#" data-toggle="tooltip" data-placement="right" title="Industry controls the terms used in MomentPath to refer to people (such as 'children' in child care or 'client' in senior care)"><i class="fad fad-primary fa-info-circle"></i></a>
            </label>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Timezone</label>
            <label class="col-lg-6 col-xl-3 col-form-label">
                {{timezoneLabel}}
            </label>
            <label class="col-lg-3 col-xl-3">
                <div class="btn btn-primary font-weight-bolder btn-text-white btnEditTimezone ml-4">
                    <i class="fad-regular fad fa-pen-square fa-swap-opacity mr-2 text-white"></i>Edit
                </div>
            </label>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Legal Facility Name</label>
            <label class="col-lg-6 col-xl-3 col-form-label">
                {{legalFacilityName}}
            </label>
            <label class="col-lg-3 col-xl-3">
                <div class="btn btn-primary font-weight-bolder btn-text-white btnEditFacilityName ml-4">
                    <i class="fad-regular fad fa-pen-square fa-swap-opacity mr-2 text-white"></i>Edit
                </div>
            </label>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Facility License Number</label>
            <label class="col-lg-6 col-xl-3 col-form-label">
                {{facilityLicenseNumber}}
            </label>
            <label class="col-lg-3 col-xl-3">
                <div class="btn btn-primary font-weight-bolder btn-text-white btnEditFacilityLicence ml-4">
                    <i class="fad-regular fad fa-pen-square fa-swap-opacity mr-2 text-white"></i>Edit
                </div>
            </label>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">Phone Number</label>
            <label class="col-lg-6 col-xl-3 col-form-label">
                {{phoneNumber}}
            </label>
            <label class="col-lg-3 col-xl-3">
                <div class="btn btn-primary font-weight-bolder btn-text-white btnEditPhoneNumber ml-4">
                    <i class="fad-regular fad fa-pen-square fa-swap-opacity mr-2 text-white"></i>Edit
                </div>
            </label>
        </div>
        {{#if hasDreambox}}
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">School Low Grade</label>
            <label class="col-lg-6 col-xl-3 col-form-label">
                {{schoolLowGrade}}
            </label>
            <label class="col-lg-3 col-xl-3">
                <div class="btn btn-primary font-weight-bolder btn-text-white btnEditSchoolLowGrade ml-4">
                    <i class="fad-regular fad fa-pen-square fa-swap-opacity mr-2 text-white"></i>Edit
                </div>
            </label>
        </div>
        <div class="form-group row">
            <label class="col-xl-3 col-lg-3 text-right col-form-label">School High Grade</label>
            <label class="col-lg-6 col-xl-3 col-form-label">
                {{schoolHighGrade}}
            </label>
            <label class="col-lg-3 col-xl-3">
                <div class="btn btn-primary font-weight-bolder btn-text-white btnEditSchoolHighGrade ml-4">
                    <i class="fad-regular fad fa-pen-square fa-swap-opacity mr-2 text-white"></i>Edit
                </div>
            </label>
        </div>
        {{/if}}
    </div>
</template>