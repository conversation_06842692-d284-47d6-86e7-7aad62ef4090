import { Template } from 'meteor/templating';
import { Orgs } from '../../../lib/collections/orgs';
import './_addScheduleType.html'
import { AddScheduleTypeClientService } from '../../services/admin/AddScheduleTypeClientService';

Template.addScheduleType.onCreated(function () {
    this.addScheduleTypeClientService = new ReactiveVar(null);
    this.tracker = Tracker.autorun(() => {
        this.addScheduleTypeClientService.set(new AddScheduleTypeClientService(Orgs.current()));
    });
});

Template.addScheduleType.onDestroyed(function () {
    Template.instance().tracker.stop()
});

Template.addScheduleType.helpers({
    hasRegistrationFlow() {
        return Template.instance().addScheduleTypeClientService.get().hasRegistrationFlow();
    },
    hasWeekendsEnabled() {
      return Template.instance().addScheduleTypeClientService.get().hasWeekendsEnabled();
    },
    hasAutomaticGroupUpdating() {
        return Template.instance().addScheduleTypeClientService.get().hasAutomaticGroupUpdating();
    },
    getScheduleTypes() {
        return Template.instance().addScheduleTypeClientService.get().getScheduleTypes();
    },
    getGroups() {
        return Template.instance().addScheduleTypeClientService.get().getGroups();
    },
    getDefaultGroupName(groupId) {
        return Template.instance().addScheduleTypeClientService.get().getDefaultGroupName(groupId);
    }
})

Template.addScheduleType.events({
    'click .btnDeleteScheduleType': function (event, instance) {
        event.preventDefault();
        instance.addScheduleTypeClientService.get().removeScheduleType(this._id);
    },

    'click .btnEditScheduleType': function (event, instance) {
        event.preventDefault();
        instance.addScheduleTypeClientService.get().editScheduleType(this);
    },

    'click #btnSaveAddScheduleType': async function (event, instance) {
        event.preventDefault();
        await instance.addScheduleTypeClientService.get().saveAddScheduleType();
        instance.addScheduleTypeClientService.get().hideAndResetForm();
    },

    'click #btnCancelAddScheduleType': function (event, instance) {
        event.preventDefault();
        instance.addScheduleTypeClientService.get().hideAndResetForm();
    },
})