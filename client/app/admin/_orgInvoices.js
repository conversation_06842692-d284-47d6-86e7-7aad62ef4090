import './_orgInvoices.html';

Template._orgInvoices.onCreated(function() {
	var self = this;
	self.invoiceData = new ReactiveVar();
});

Template._orgInvoices.onRendered(function() {
	$('.input-daterange').datepicker({
		keepEmptyValues: false,
	});
})

Template._orgInvoices.helpers({
	"startDate"() {
		return new moment().add(-1, "years").format("MM/DD/YYYY");
	},
	"endDate"() {
		return new moment().format("MM/DD/YYYY");
	},
	"isLoading"() {
		return Template.instance().invoiceData.get() == "loading";
	},
	"invoices"() {
		const invoiceData = Template.instance().invoiceData.get();
		if (invoiceData && invoiceData != "loading") return invoiceData;
	},
	"paymentStatus"() {
		let status = "";
		if (this.openAmount == 0) {
			return "Paid";
		} else {
			return "Open";
		}
	}
});

Template._orgInvoices.events({
	"click #btn-update"(e, instance) {
		const startDate = $("input[name=start-date]").val(), endDate = $("input[name=end-date]").val();
		pullInvoiceData(instance, startDate, endDate);
	}
});

function pullInvoiceData(instance, startDate, endDate) {
	instance.invoiceData.set("loading");
	Meteor.callAsync("getOrgInvoices", {startDate, endDate}).then(result => {
		instance.invoiceData.set(result);
	}).catch(error => {
		instance.invoiceData.set(null);
		return mpSwal.fire("Error", "There was an issue loading your organization's invoices.", "error");
	});
}
