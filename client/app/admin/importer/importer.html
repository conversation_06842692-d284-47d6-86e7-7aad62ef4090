<template name="importer">
	<div class="container">
		
	  	<div class="flex-row-fluid ml-lg-8">
			<div class="card">
				<div class="card-header">
					<h1>Data Importer</h1>
				</div>
				<div class="card-body">
					
					<div class="row">
						<div class="col-12">
							<b>Current Customer Name:</b> {{customer.name}}<br/>
							<b>Current Customer ID:</b> {{customer._id}}<br/>
							<b>Current Customer # family &amp; people records:</b> {{customer.familyPeopleRecordCount}}<br/>
							<b>Current Customer Timezone:</b> {{customer.timezone}}<br/>
						</div>
						
					</div>

					<div class="row mt-12">
						<div class="col-12">
							<b>File Selection(CSV only):</b><br/>
							<input type="file" id="csv-file" class="form-control"> <br/>
	
							<div class="checkbox-list">
								<label class="checkbox checkbox-primary">
									<input type="checkbox" class="form-check-input" id="chkBypassClassrooms" name="bypass-classrooms" >
									<span></span>
										Allow Empty Classrooms
								  </label>
								
								<label class="checkbox checkbox-primary">
								  <input type="checkbox" class="form-check-input" id="chkClear" name="clear-existing" >
								  <span></span>
									  Clear Existing Data (removes existing people, schedules, and invitations)
								</label>
								
								<label class="checkbox checkbox-primary">
								  <input type="checkbox" class="form-check-input" id="chkPreview" name="preview" checked>
								  <span></span>
									  Preview (shows warnings and errors without writing changes)
								</label>
							</div>

							<br/>
							<button class="btn btn-primary" id="btnSubmit">Process</button><br/>
							<br/>

							{{#if results}}
								<span style="white-space: pre-wrap;">{{resultMessages}}</span>
								<br>
								<b>Successful records:</b> {{results.successCount}}<br/>
								<b>Failed records:</b> {{results.failCount}}<br/>
							{{/if}}
						</div>
					</div>
				</div>
				
			</div>
		</div>

	</div>
</template>