import { importerConfigs } from './importerConfigs';
import './_importerModal.html';


Template._importerModal.helpers({
    importers() {
        return importerConfigs;
    }
});

Template._importerModal.events({
    'click .download-template'(event, instance) {
        const importerId = event.currentTarget.dataset.id;
        const options = instance.data;

        const config = importerConfigs.find(cfg => cfg.id === importerId);

        if (!config) {
            mpSwal.fire('error', `No importer found for: ${importerId}`, 'Error');
        }

        if (config?.downloadTemplate) {
            config.downloadTemplate(options);
        } else {
            mpSwal.fire('error', `No download handler for importer: ${importerId}`, 'Error');
        }
    },

    'click .upload-csv'(event, instance) {
        const importerId = event.currentTarget.dataset.id;
        const options = instance.data;

        const config = importerConfigs.find(cfg => cfg.id === importerId);

        if (!config) {
            mpSwal.fire('error', `No importer found for: ${importerId}`, 'Error');
        }

        if (config?.uploadCSV) {
            config.uploadCSV(options);
        } else {
            mpSwal.fire('error', `No upload handler for importer: ${importerId}`, 'Error');
        }
    }
});
