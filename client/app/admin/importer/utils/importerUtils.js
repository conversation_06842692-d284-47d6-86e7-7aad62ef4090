import { importerTypes } from '../../../../../lib/constants/importers/importerConstants';

export class ImporterUtils {
    /**
     * Returns a context-specific set of options required by the specified importer.
     *
     * This utility method is used on the client side (typically from the upload modal)
     * to dynamically build the options object that will be passed to the `runImport` Meteor method.
     * It allows each importer to define its own required context (e.g., `rootOrg`, `currentUserId`, etc.)
     * without hardcoding that logic in the UI layer.
     *
     * As new importers are added, they can define their own case block to extract
     * the appropriate data from the provided context object.
     *
     * @param {string} importerId - The identifier for the importer (must match an entry in `importerTypes`)
     * @param {Object} [context={}] - A general-purpose object containing contextual data (e.g., org, user)
     * @returns {Object} An object of options to be passed into the importer's constructor
     *
     * @example
     * const options = ImporterOptionResolver.getImporterOptionsById('org-importer', {
     *   rootOrg: { _id: 'abc123', name: 'Acme Corp' }
     * });
     * // → { rootOrg: { _id: 'abc123', name: 'Acme Corp' } }
     */
    static getImporterOptionsById(importerId, context = {}) {
        switch (importerId) {
            case importerTypes.ORG:
            case importerTypes.BILLING_PLANS:
            case importerTypes.TIME_PERIOD:
            case importerTypes.ITEMS:
            case importerTypes.SUBSIDY:
            case importerTypes.PROGRAM:
            case importerTypes.GSI:
            case importerTypes.SCHEDULE_TYPE:
            case importerTypes.INVOICES:
            case importerTypes.FAMILY:
            case importerTypes.STAFF_AND_ADMIN:
                return {
                    rootOrg: context.rootOrg,
                    allOrgs: context.allOrgs
                };
            case importerTypes.DISCOUNT:
                return {
                    rootOrg: context.rootOrg,
                    allOrgs: context.allOrgs
                };
            case importerTypes.DISTRICT_FUNDED:
                return {
                    allOrgs: context.allOrgs
                };
            // Add more cases here as you add importers...
            default:
                return {}; // No special options required
        }
    }
}