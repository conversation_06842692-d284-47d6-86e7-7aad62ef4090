<template name="orgSettings">
  <div class="container">
    <div class="flex-row-fluid ml-lg-8">
      <div class="card card-custom gutter-bs">
        <div class="card-body px-0">
          <div class="tab-content pt-5">
            {{#if isActiveTab 'orgSettings'}}
            <div id="orgSettings">
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                  Moment Configuration
                </div>
                <div class="col-7">
                  <p class="font-weight-bolder">MomentPath offers additional moment types and configuration options to suit the needs of your organization. Expand to learn more and enable additional moment types.</p>
                </div>
                <div class="col-3">
                  <div data-cy="view-moment-types" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelMomentTypes" aria-expanded="false" aria-controls="panelMomentTypes" id="btnViewMoments">
                    View Moment Types
                  </div>
                </div>
              </div>
              <div id="panelMomentTypes" class="collapse container">
                <ul>
                  {{#each switchableMomentTypes}}
                  <li class="switchRow">
                    <span class="switchLabel">{{prettyName}} </span>
                    <span class="switchHolder">
                      <label class="switch">
                        <input data-cy="moment-type-slider" type="checkbox" name="moment" data-type="{{momentType}}" data-flag="{{flag}}" {{checkedIfActive "momentType" momentType}}>
                        <span class="slider round"></span>
                      </label>
                    </span>
                    <div style="display:flex;flex-direction:row;align-items:center;margin-left:48px">
                      {{#if adminOnly}}
                      <span class="switchLabel" style="margin-right:16px">Admin Only</span>
                      <span class="switchHolder">
                        <label class="switch">
                          <input data-cy="admin-only-slider" type="checkbox" name="moment" data-type="{{momentType}}" data-flag="{{adminOnly}}" {{checkedIfActive "adminOnly" momentType}}>
                          <span class="slider round"></span>
                        </label>
                      </span>
                      {{/if}}
                    </div>
                  </li>
                  {{/each}}
                  <!-- <li><a href="#" class="btn btn-primary" id="btnHideMoments">Hide Moment Types</a></li> -->
                </ul>
                  <ul>
                      <li class="row switchRow my-10">
                          <span class="switchLabel">Require Staff Checkin for Moment Creation</span>
                          <span class="switchHolder override-switchHolder">
                      <label class="switch">
                        <input data-cy="staff-checkin-required" type="checkbox" name="moment" data-flag="moments/staffRequiredCheckin/enabled" {{checkedIfActive "staffRequiredCheckin"}}>
                        <span class="slider round"></span>
                      </label>
                    </span>
                      </li>
					  <li class="row switchRow my-10">
						<span class="switchLabel">Require complete name to face check</span>
						<span class="switchHolder override-switchHolder">
					<label class="switch">
					  <input data-cy="complete-name-to-face-check" type="checkbox" name="moment" data-flag="moments/incompleteFTF/enabled" {{checkedIfActive "incompleteFTF"}}>
					  <span class="slider round"></span>
					</label>
				  </span>
					</li>
                  </ul>
                <div class="separator separator-dashed my-8"></div>
              </div>
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                    Check In/Out
                </div>
                <div class="col-7">
                  <p class="font-weight-bolder">Effortless Entry makes check-ins and check-outs a breeze for your staff and families.</p>
                </div>
                <div class="col-3">
                  <div data-cy="configure-check-in-out" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelEffortlessEntry" aria-expanded="false" aria-controls="panelEffortlessEntry" id="btnConfigureEffortlessEntry">
                    Configure
                  </div>
                </div>
              </div>
              <div id="panelEffortlessEntry" class="collapse container mt-4">
                <div class="row">
                  <div class="col-sm-8 font-weight-bold col-form-label">PIN Code Check In</div>
                  <div class="col-sm-4">
                    <label class="switch">
                      <input data-cy="pin-code-check-in" type="checkbox" name="pinCodeCheckIn" {{checkedIfActive "pinCodeCheckin"}}>
                      <span class="slider round"></span>
                    </label>
                  </div>
                </div>
                <p> Enabling PIN Code Check In allows your staff and families to check-in with a simple 4-digit PIN code. PIN codes are
                  set on the person's profile.
                </p>
                {{#if trueIfEq (checkedIfActive "pinCodeCheckin") "checked"}}
                  <div class="row mt-6">
                      <div class="col-sm-8 font-weight-bold col-form-label">
                          Automated PIN Codes
                          <p class="font-weight-normal">
                              Automatically assigns and emails login/check-in PIN codes to Family members and check-in PIN codes to Authorized Pickups.
                          </p>
                      </div>
                      <div class="col-sm-4">
                          <i class="fad fad-primary fa-hashtag-lock"></i>
                          <label class="switch">
                              <input type="checkbox" name="autoPin" {{checkedIfActive "autoPin"}}>
                              <span class="slider round"></span>
                          </label>
                      </div>
                  </div>
                  <p>
                      Note that when enabled, this feature will assign and email PIN Codes to any Family
                      that doesn't currently have a PIN Code. Note also that this feature provides convenience
                      but reduces security, as emails are sometimes shared or could be subject to hacking.
                  </p>
                  <div class="row mt-6">
                    <div class="col-sm-8 font-weight-bold col-form-label">Staff Required PIN Code Check In</div>
                    <div class="col-sm-4">
                      <label class="switch">
                        <input data-cy="staff-required-pin-code" type="checkbox" name="staffRequiredPinCodeCheckIn" {{checkedIfActive "staffRequiredPinCodeCheckin"}}>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  <p> Requires that staff use PIN Code Check In in order to check in or out (making staff unable to modify check-in or check-out time).
                    Administrators can still check staff in or out through the staff member's profile.
                  </p>
                <div class="row mt-6">
                  <div class="col-sm-8 font-weight-bold col-form-label">Require Phone + PIN</div>
                  <div class="col-sm-4">
                    <label class="switch">
                      <input data-cy="phone-pin-checkin" type="checkbox" name="phonePinCheckIn" {{checkedIfActive "phonePinCheckIn"}}>
                      <span class="slider round"></span>
                    </label>
                  </div>
                </div>
                <p>
                  For additional security, require people to enter their phone number as well as their PIN to check in.
                </p>
                  <div class="row mt-6">
                    <div class="col-sm-8 font-weight-bold col-form-label">Kiosk Master PIN Code</div>
                    <div class="col-sm-4">
                      <div data-cy="change-master-pin-code" class="btn btn-primary font-weight-bolder" id="btnChangeMasterPinCode">{{#if hasMasterPinCode}}Change{{else}}Enable{{/if}}</div>
                    </div>
                  </div>
                  <p> The Master PIN Code allows you to enter and exit kiosk mode once PIN Code Check In is enabled.</p>
                  <div class="row mt-6">
                    <div class="col-sm-8 font-weight-bold col-form-label">Kiosk Master PIN Administrator Access Only</div>
                    <div class="col-sm-4">
                      <label class="switch">
                        <input data-cy="kiosk-pin-admin-only" type="checkbox" name="kioskPinAdminOnly" {{checkedIfActive "kioskPinAdminOnly"}}>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  <p>Selecting this will restrict putting a device in kiosk mode to only users with Administrator access.</p>
                  <div class="row mt-6">
                    <div class="col-sm-8 font-weight-bold col-form-label">Express Drive Up</div>
                    <div class="col-sm-4">
                      <label class="switch">
                        <input type="checkbox" name="familyCheckIn" {{checkedIfActive "informedArrival"}}>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                <p> Informed Arrival allows parents to check-in using their mobile devices, providing you and your staff with
                  a valuable heads-up even prior to arrival.
                </p>
                  <div class="row mt-6">
                    <div class="col-sm-8 font-weight-bold col-form-label">QR Code Check In</div>
                    <div class="col-sm-4">
                      <label class="switch">
                        <input data-cy="qr-code-check-in" type="checkbox" name="qrCodeCheckIn" {{checkedIfActive "qrCodeCheckin"}}>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  <p> Allows families to use QR codes within the mobile app to touchlessly check in or out.
                  </p>
                  {{#if trueIfEq (checkedIfActive "qrCodeCheckin") "checked"}}
                    <div class="row mt-6">
                      <div class="col-sm-8 font-weight-bold col-form-label">Auto-Refresh QR Codes</div>
                      <div class="col-sm-4">
                        <label class="switch">
                          <input type="checkbox" name="checkInCheckOutQrCodesExpire" {{checkedIfActive "checkInCheckOutQrCodesExpire"}}>
                          <span class="slider round"></span>
                        </label>
                      </div>
                    </div>
                    {{#if trueIfEq (checkedIfActive "checkInCheckOutQrCodesExpire") "checked"}}
                    <p>
                      For security purposes, the system should update QR codes every...
                      <br /><br />
                      <input style="display: inline-block;width: 80px;" type="number" class="form-control" id="checkInCheckOutQrCodesExpireInHours" value={{getCheckInCheckOutQrCodesExpireAfterHoursValue}}>
                      <span style="margin: 0 10px;">hours</span>
                      <button style="margin: 0 !important" id="btnSaveQrCodeExpirationTime" class="btn btn-primary font-weight-bolder mt-4">Save</button>
                    </p>
                    {{else}}
                    <p>Enables automatic refreshing of QR codes to ensure optimal security and privacy.</p>
                    {{/if}}
                  {{/if}}
                {{/if}}
                <div class="row mt-6">
                  <div class="col-sm-8 font-weight-bold col-form-label">Multiple Check-in/Check-out</div>
                  <div class="col-sm-4">
                    <label class="switch">
                      <input data-cy="multiple-check-in" type="checkbox" name="multipleCheckin" {{checkedIfActive "multipleCheckin"}}>
                      <span class="slider round"></span>
                    </label>
                  </div>
                </div>
                <p> Multiple Check-in/Check-out allows you to select multiple people in the group grid view for quickly
                  checking in or out more than one person.
                </p>
                <div class="row mt-6">
                  <div class="col-sm-8 font-weight-bold col-form-label">Require Early Pick up/Late Drop Off Reason</div>
                  <div class="col-sm-4">
                    <label class="switch">
                      <input data-cy="checkin-checkout-pick-drop-reason" type="checkbox" name="checkInCheckOutPickDropReason" {{checkedIfActive "checkInCheckOutPickDropReason"}}>
                      <span class="slider round"></span>
                    </label>
                  </div>
                </div>
                <p>Require users to select a reason for early departure and late arrivals to your program</p>
                {{#if trueIfEq (checkedIfActive "checkInCheckOutPickDropReason") "checked"}}
                {{> earlyPickDropReasons }}
                {{/if}}
                <div class="separator separator-dashed my-8"></div>
                <!-- <a href="#" class="btn btn-primary" id="btnHideEffortlessEntry">Close</a> -->
              </div>
              {{# if canViewRegistrationSettings }}
                  <div class="row col-12 mb-10">
                    <div class="col-2 text-right">
                      Registration
                    </div>
                    <div class="col-7">
                      <p class="font-weight-bolder">Configure your registration questions and settings.</p>
                    </div>
                    <div class="col-3">
                      <div class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelRegistration" aria-expanded="false" aria-controls="panelRegistration" id="btnConfigureRegistration" data-cy="configure-registration">
                        Configure
                      </div>
                    </div>
                  </div>
                  <div id="panelRegistration" class="collapse container mt-4">
                    <div class="row">
                        <div class="col-10 offset-1">
                            <form id="frmRegistration" class="form-horizontal" role="form">
                                <div class="row form-group">
                                    <label for="registrationGroups" class="col-form-label col-auto">
                                        Auto Enroll in Group
                                    </label>
                                    <div class="col-2 ml-35">
                                        <select data-cy="auto-group-selection" id="autoGroupSelection"  class="form-control form-control-solid w-200px">
                                            <option></option>
                                           {{#each availableGroups}}
                                            <option value="{{_id}}" {{isOptionSelected _id}}>
                                                {{name}}
                                            </option>
                                            {{/each}}
                                        </select>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <label for="registrationContacts" class="col-form-label col-auto">
                                        Required Emergency Contacts/Additional Pickups
                                    </label>
                                    <div class="col-2">
                                        <input data-cy="registration-required-contact" type="number" class="form-control" id="registrationContacts" min="0" value="{{ getRegistrationRequiredContactsCount }}" required disabled={{ cannotEditRegistrationSettings }} />
                                    </div>
                                </div>
                                {{# each question in getRegistrationQuestions }}
                                    <fieldset class="form-group registrationQuestion" disabled={{ cannotEditRegistrationSettings }}>
                                        <legend style="font-size: inherit; font-weight: 600;">
                                            Question {{ incremented @index }}
                                            <a href="#" class="btnRemoveQuestion ml-2" data-id="{{ @index }}" title="Remove"><i class="fad fad-primary fa-trash-alt"></i></a>
                                        </legend>
                                        <div class="form-group row mt-2">
                                            <label for="registrationQuestionsType-{{ @index }}" class="col-form-label col-2 text-right">
                                                Type
                                            </label>
                                            <div class="col-3">
                                                <select data-cy="registration-question-type" id="registrationQuestionsType-{{ @index }}" data-id="{{ @index }}" class="form-control form-control-solid registrationQuestionType">
                                                    <option value="text" {{ selectedIfEqual question.type 'text' }}>Text</option>
                                                    <option value="date" {{ selectedIfEqual question.type 'date' }}>Date</option>
                                                    <option value="selectSingle" {{ selectedIfEqual question.type 'selectSingle' }}>Select List - Single Choice</option>
                                                    <option value="checkbox" {{ selectedIfEqual question.type 'checkbox' }}>Checkbox</option>
                                                    <option value="selectMultiple" {{ selectedIfEqual question.type 'selectMultiple' }}>Checkbox - Multiple Choice</option>
                                                    <option value="conditional" {{ selectedIfEqual question.type 'conditional' }}>Conditional: Yes/No</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div>
                                          {{#if logicalEquals question.type 'conditional'}}
                                            <div class="form-group row">
                                                <label for="registrationQuestionsQuestion-{{ @index }}" class="col-form-label col-2 text-right">
                                                    Question
                                                </label>
                                                <div class="col-4">
                                                    <textarea class="form-control registrationQuestionQuestion" id="registrationQuestionsQuestion-{{ @index }}" rows="1" required>{{ question.question }}</textarea>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="offset-2 col-4 checkbox-inline">
                                                    <label for="registrationQuestionsRequired-{{ @index }}" class="checkbox checkbox-primary">
                                                        <input type="checkbox" class="form-check-input registrationQuestionRequired" id="registrationQuestionsRequired-{{ @index }}" checked="{{ isQuestionRequired question }}" />
                                                        <span></span>
                                                        Required
                                                    </label>
                                                </div>
                                            </div>
                                            <!-- If yes: -->
                                            <div class="if-yes-container">
                                              <div class="form-group row mt-2">
                                                  <label for="registrationQuestionsType-{{ @index }}" class="col-form-label col-2 text-right">
                                                    If "Yes"
                                                  </label>
                                                  <div class="col-3">
                                                      <select id="registrationQuestionsType-{{ @index }}" data-question-index="{{ @index }}" class="form-control form-control-solid registrationQuestionIfYesType">
                                                          <option value="text" {{ selectedIfEqual question.ifYes.type 'text' }}>Text</option>
                                                          <option value="date" {{ selectedIfEqual question.ifYes.type 'date' }}>Date</option>
                                                          <option value="selectSingle" {{ selectedIfEqual question.ifYes.type 'selectSingle' }}>Select List - Single Choice</option>
                                                          <option value="checkbox" {{ selectedIfEqual question.ifYes.type 'checkbox' }}>Checkbox</option>
                                                          <option value="selectMultiple" {{ selectedIfEqual question.ifYes.type 'selectMultiple' }}>Checkbox - Multiple Choice</option>
                                                          <option value="noFollowUpNeeded" {{ selectedIfEqual question.ifYes.type 'noFollowUpNeeded' }}>No Follow Up Needed</option>
                                                      </select>
                                                  </div>
                                              </div>
                                              {{#unless isNoFollowUpNeededSelectedForYesNoItem question true }}
                                                <!-- 'noFollowUpNeeded' is not selected -->
                                                <div class="form-group row">
                                                    <label for="registrationQuestionsQuestion-{{ @index }}-yes" class="col-form-label col-2 text-right">
                                                        &nbsp;
                                                    </label>
                                                    <div class="col-4">
                                                        <textarea class="form-control registrationQuestionIfYesQuestion" id="registrationQuestionsQuestion-{{ @index }}-yes" rows="1" required>{{ question.ifYes.question }}</textarea>
                                                    </div>
                                                    <div class="col-form-label col-auto">
                                                        <i class="fa fa-arrow-circle-right" style="color: black; font-size: 20px;"></i>
                                                    </div>
                                                    <label for="registrationQuestionsMapping-{{ @index }}-yes" class="col-form-label col-auto text-right">
                                                        <span>Map to Profile Field</span>
                                                    </label>
                                                    <div class="col-3">
                                                        <select id="registrationQuestionsMapping-{{ @index }}-yes" class="form-control form-control-solid registrationQuestionIfYesMapping">
                                                            {{# each field in getPersonProfileFields question.ifYes true }}
                                                                <option value="{{ field.name }}" {{ selectedIfEqual question.ifYes.mappedTo field.name }}>{{ field.description }}</option>
                                                            {{/ each }}
                                                        </select>
                                                    </div>
                                                </div>
                                                {{# if shouldShowAddChoicesForYesNoItem question true }}
                                                    <div class="form-group row choices-row">
                                                        <label for="registrationQuestionsChoices-{{ @index }}" class="col-form-label control-label col-2 text-right">
                                                            Choices
                                                        </label>
                                                        <div class="col-10">
                                                            {{#let question_index=@index}}
                                                              {{# each ifYesChoice in question.ifYes.choices }}
                                                                  <div class="row choice-row align-items-center mr-3">
                                                                      <div class="col-5 mr-n3">
                                                                          <input type="text" class="form-control registrationQuestionIfYesChoice mb-2 mr-2" value="{{ ifYesChoice.value }}" data-question-index="{{ question_index }}" data-choice-index="{{ @index }}" />
                                                                      </div>
                                                                      <div class="col-1">
                                                                          <a href="#" class="btnRemoveQuestionChoiceYes" title="Remove" data-question-index="{{ question_index }}" data-choice-index="{{ @index }}">
                                                                            <i class="fad fad-primary fa-trash-alt"></i>
                                                                          </a>
                                                                      </div>
                                                                  </div>
                                                              {{/ each }}
                                                              <button class="btn btn-primary font-weight-bolder btnAddRegistrationQuestionChoiceYes" data-question-index="{{ question_index }}" disabled={{ cannotEditRegistrationSettings }}>
                                                                  <i class="fad fa-plus fa-swap-opacity text-white"></i>Add Choice
                                                              </button>
                                                            {{/let}}
                                                        </div>
                                                    </div>
                                                {{/ if }}
                                                <div class="form-group row">
                                                    <div class="offset-2 col-4 checkbox-inline">
                                                        <label for="registrationQuestionsRequiredYes-{{ @index }}" class="checkbox checkbox-primary">
                                                            <input type="checkbox" class="form-check-input registrationQuestionIfYesRequired" id="registrationQuestionsRequiredYes-{{ @index }}" checked="{{ isQuestionConditionalForYesNoRequired question true }}" />
                                                            <span></span>
                                                            Required
                                                        </label>
                                                    </div>
                                                </div>
                                              {{/unless}}
                                            </div>
                                            <!-- end if yes -->
                                            <!-- If no: -->
                                            <div class="if-no-container">
                                              <div class="form-group row mt-2">
                                                <label for="registrationQuestionsType-{{ @index }}" class="col-form-label col-2 text-right">
                                                  If "No"
                                                </label>
                                                <div class="col-3">
                                                    <select id="registrationQuestionsType-{{ @index }}" data-question-index="{{ @index }}" class="form-control form-control-solid registrationQuestionIfNoType">
                                                        <option value="text" {{ selectedIfEqual question.ifNo.type 'text' }}>Text</option>
                                                        <option value="date" {{ selectedIfEqual question.ifNo.type 'date' }}>Date</option>
                                                        <option value="selectSingle" {{ selectedIfEqual question.ifNo.type 'selectSingle' }}>Select List - Single Choice</option>
                                                        <option value="checkbox" {{ selectedIfEqual question.ifNo.type 'checkbox' }}>Checkbox</option>
                                                        <option value="selectMultiple" {{ selectedIfEqual question.ifNo.type 'selectMultiple' }}>Checkbox - Multiple Choice</option>
                                                        <option value="noFollowUpNeeded" {{ selectedIfEqual question.ifNo.type 'noFollowUpNeeded' }}>No Follow Up Needed</option>
                                                    </select>
                                                </div>
                                              </div>
                                              {{#unless isNoFollowUpNeededSelectedForYesNoItem question false }}
                                              <!-- Conditional mapping 'noFollowUpNeeded' is not selected -->
                                              <div class="form-group row">
                                                  <label for="registrationQuestionsQuestion-{{ @index }}-no" class="col-form-label col-2 text-right">
                                                      &nbsp;
                                                  </label>
                                                  <div class="col-4">
                                                      <textarea class="form-control registrationQuestionIfNoQuestion" id="registrationQuestionsQuestion-{{ @index }}-no" rows="1" required>{{ question.ifNo.question }}</textarea>
                                                  </div>
                                                  <div class="col-form-label col-auto">
                                                      <i class="fa fa-arrow-circle-right" style="color: black; font-size: 20px;"></i>
                                                  </div>
                                                  <label for="registrationQuestionsMapping-{{ @index }}-no" class="col-form-label col-auto text-right">
                                                      <span>Map to Profile Field</span>
                                                  </label>
                                                  <div class="col-3">
                                                      <select id="registrationQuestionsMapping-{{ @index }}-no" class="form-control form-control-solid registrationQuestionIfNoMapping">
                                                          {{# each field in getPersonProfileFields question.ifNo true }}
                                                              <option value="{{ field.name }}" {{ selectedIfEqual question.ifNo.mappedTo field.name }}>{{ field.description }}</option>
                                                          {{/ each }}
                                                      </select>
                                                  </div>
                                              </div>
                                              {{# if shouldShowAddChoicesForYesNoItem question false }}
                                                  <div class="form-group row choices-row">
                                                      <label for="registrationQuestionsChoices-{{ @index }}" class="col-form-label control-label col-2 text-right">
                                                          Choices
                                                      </label>
                                                      <div class="col-10">
                                                          {{#let question_index=@index}}
                                                            {{# each ifNoChoice in question.ifNo.choices }}
                                                                <div class="row choice-row align-items-center mr-3">
                                                                    <div class="col-5 mr-n3">
                                                                        <input type="text" class="form-control registrationQuestionIfNoChoice mb-2 mr-2" value="{{ ifNoChoice.value }}" data-question-index="{{ question_index }}" data-choice-index="{{ @index }}" />
                                                                    </div>
                                                                    <div class="col-1">
                                                                        <a href="#" class="btnRemoveQuestionChoiceNo" title="Remove" data-question-index="{{ question_index }}" data-choice-index="{{ @index }}">
                                                                          <i class="fad fad-primary fa-trash-alt"></i>
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            {{/ each }}
                                                            <button class="btn btn-primary font-weight-bolder btnAddRegistrationQuestionChoiceNo" data-question-index="{{ question_index }}" disabled={{ cannotEditRegistrationSettings }}>
                                                                <i class="fad fa-plus fa-swap-opacity text-white"></i>Add Choice
                                                            </button>
                                                          {{/let}}
                                                      </div>
                                                  </div>
                                              {{/ if }}
                                              <div class="form-group row">
                                                  <div class="offset-2 col-4 checkbox-inline">
                                                      <label for="registrationQuestionsRequiredNo-{{ @index }}" class="checkbox checkbox-primary">
                                                          <input type="checkbox" class="form-check-input registrationQuestionIfNoRequired" id="registrationQuestionsRequiredNo-{{ @index }}" checked="{{ isQuestionConditionalForYesNoRequired question false }}" />
                                                          <span></span>
                                                          Required
                                                      </label>
                                                  </div>
                                              </div>
                                              {{/unless}}
                                            </div>
                                            <!-- end if no -->
                                          {{else}}
                                            <!-- This is the normal question mapping, not a conditional -->
                                            <div class="form-group row">
                                                <label for="registrationQuestionsQuestion-{{ @index }}" class="col-form-label col-2 text-right">
                                                    Question
                                                </label>
                                                <div class="col-4">
                                                    <textarea data-cy="registration-question" class="form-control registrationQuestionQuestion" id="registrationQuestionsQuestion-{{ @index }}" rows="1" required>{{ question.question }}</textarea>
                                                </div>
                                                <div class="col-form-label col-auto">
                                                    <i class="fa fa-arrow-circle-right" style="color: black; font-size: 20px;"></i>
                                                </div>
                                                <label for="registrationQuestionsMapping-{{ @index }}" class="col-form-label col-auto text-right">
                                                    <span>Map to Profile Field</span>
                                                </label>
                                                <div class="col-3">
                                                    <select data-cy="question-mapping" id="registrationQuestionsMapping-{{ @index }}" class="form-control form-control-solid registrationQuestionMapping">
                                                        {{# each field in getPersonProfileFields }}
                                                            <option value="{{ field.name }}" {{ selectedIfEqual question.mappedTo field.name }}>{{ field.description }}</option>
                                                        {{/ each }}
                                                    </select>
                                                </div>
                                            </div>
                                            {{# if isShowRegistrationQuestionChoices @index }}
                                                <div class="form-group row choices-row">
                                                    <label for="registrationQuestionsChoices-{{ @index }}" class="col-form-label control-label col-2 text-right">
                                                        Choices
                                                    </label>
                                                    <div class="col-10">
                                                        {{#each choice in question.choices}}
                                                            <div class="row choice-row align-items-center mr-3">
                                                                <div class="col-5 mr-n3">
                                                                    <input data-cy="registration-question-choice" 
                                                                          type="text" 
                                                                          class="form-control registrationQuestionChoice mb-2 mr-2" 
                                                                          value="{{choice.value}}" 
                                                                          data-choice-id="{{choice.id}}" />
                                                                </div>
                                                                <div class="col-1">
                                                                  <a href="#" class="btnRemoveQuestionChoice" title="Remove"><i class="fad fad-primary fa-trash-alt"></i></a>
                                                                </div>
                                                            </div>
                                                        {{/each}}
                                                        <button data-cy="add-choice" type="button" class="btn btn-primary font-weight-bolder btnAddRegistrationQuestionChoice" data-id="{{ @index }}" disabled={{ cannotEditRegistrationSettings }}>
                                                            <i class="fad fa-plus fa-swap-opacity text-white"></i>Add Choice
                                                        </button>
                                                    </div>
                                                </div>
                                            {{/ if }}
                                            <div class="form-group row">
                                                <label for="registrationQuestionsUrl-{{ @index }}" class="col-form-label control-label col-2 text-right">
                                                    URL
                                                </label>
                                                <div class="col-4 input-group">
                                                    <input type="text" class="form-control border-right-0 registrationQuestionUrl" id="registrationQuestionsUrl-{{ @index }}" value="{{ question.includedLink }}" />
                                                    <div class="input-group-append">
                                                        <span class="input-group-text" style="background-color: #E5EAEE; border: none;"><i class="fad-regular fad fa-link"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <div class="offset-2 col-4 checkbox-inline">
                                                    <label for="registrationQuestionsRequired-{{ @index }}" class="checkbox checkbox-primary">
                                                        <input type="checkbox" class="form-check-input registrationQuestionRequired" id="registrationQuestionsRequired-{{ @index }}" checked="{{ isQuestionRequired question }}" />
                                                        <span></span>
                                                        Required
                                                    </label>
                                                </div>
                                            </div>
                                          {{/if}}
                                        </div>
                                    </fieldset>
                                {{/ each }}
                                <div class="row col-8 d-flex">
                                    <button data-cy="add-registration-question" id="btnAddRegistrationQuestion" class="btn btn-primary font-weight-bolder" disabled={{ cannotEditRegistrationSettings }}>
                                        <i class="fad fa-plus fa-swap-opacity text-white"></i>Add Additional Question
                                    </button>
                                    {{#if showPropagationSettings}}
                                    <button id="btnPropagateRegistrationSettings"
                                            class="btn btn-danger font-weight-bolder ml-5"
                                    >
                                        Propagate Settings
                                    </button>
                                    {{/if}}
                                </div>
                                <div id="propRegistrationSettings" class="row col-12" style="display: none">
                                    <div class="d-flex flex-column mt-5">
                                        {{#each org in orgHierarchies}}
                                            <div class="checkbox-inline">
                                                <label for="org-{{ @index }}" class="checkbox checkbox-primary">
                                                    <input type="checkbox" class="form-check-input orgHierarchyCheckbox" data-id="{{ @index }}" id="org-{{@index}}" checked="{{org.checked}}" />
                                                    <span></span>
                                                    {{org.name}}
                                                </label>
                                            </div>
                                        {{/each}}
                                    </div>
                                    <div class="row col-12 d-flex justify-content-end mt-5">
                                        <button id="btnSavePropagateRegistrationSettings"
                                                class="btn btn-danger font-weight-bolder mr-5"
                                        >
                                            Propagate
                                        </button>
                                        <button id="btnCancelPropagateRegistrationSettings"
                                                class="btn btn-secondary font-weight-bolder"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                                <div class="row col-4">
                                    <button data-cy="save-registration-settings" id="btnSaveRegistrationSettings" class="btn btn-primary font-weight-bolder mt-4" disabled={{ cannotEditRegistrationSettings }}>Save</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="separator separator-dashed my-8"></div>
                  </div>
                  <div class="row col-12 mb-10">
                    <div id="registration-alerts-title" class="col-2 text-right">
                      Registration Alerts
                    </div>
                    <div class="col-7">
                      <p id="registration-alerts-desc" class="font-weight-bolder">
                        Receive email notifications when a family successfully registered at your location
                      </p>
                    </div>
                    <div class="col-3">
                      <div id="btnPanelRegistrationAlerts" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-cy="configure-registration-alerts">
                        Configure
                      </div>
                    </div>
                  </div>
                  <div class="row col-12 mb-10">
                      <div class="col-2 text-right">
                          Registration Cancellation Reasons
                      </div>
                      <div class="col-7">
                          <p class="font-weight-bolder">
                              Configure registration cancellation reasons.
                          </p>
                      </div>
                      <div class="col-3">
                          <div data-cy="configure-registration-cancelation-reasons" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelRegistrationCancellationReasons" aria-expanded="false" aria-controls="panelRegistrationCancellationReasions" id="btnConfigureRegistrationCancellationReasons">
                              Configure
                          </div>
                      </div>
                  </div>
                  <div id="panelRegistrationCancellationReasons" class="collapse container mt-4">
                      {{> cancellationReasons }}
                      <div class="separator separator-dashed my-8"></div>
                  </div>
                  <div class="row col-12 mb-10">
                      <div class="col-2 text-right">
                          Designations
                      </div>
                      <div class="col-7">
                          <p class="font-weight-bolder">
                              Manage profile designations
                          </p>
                      </div>
                      <div class="col-3">
                          <div data-cy="configure-designations" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelDesignations" aria-expanded="false" aria-controls="panelDesignations" id="btnConfigureDesignations">
                              Configure
                          </div>
                      </div>
                  </div>
                  <div id="panelDesignations" class="collapse container mt-4">
                      {{> designations }}
                      <div class="separator separator-dashed my-8"></div>
                  </div>
              {{/ if }}
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                  Auto Checkout
                </div>
                <div class="col-7">
                  <p class="font-weight-bolder">Manage the time all people are automatically checked out by the system.</p>
                </div>
                <div class="col-3">
                  <div data-cy="configure-auto-checkout" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelAutoCheckout" aria-expanded="false" aria-controls="panelAutoCheckout" id="btnConfigAutoCheckout">
                    Configure
                  </div>
                </div>
              </div>
              <div id="panelAutoCheckout" class="collapse container">
                <ul>
                  <li class="switchRow">
                    <span class="switchLabel">Enabled </span>
                    <span class="switchHolder">
                      <label class="switch">
                        <input data-cy="enabled-auto-checkout" type="checkbox" name="autocheckout" {{checkedIfActive "autoCheckout" "true"}}>
                        <span class="slider round"></span>
                      </label>
                    </span>
                  </li>
                  <li class="switchRow">
                    <span class="switchLabel">Time </span>
                    <span class="switchHolder">
                      <input data-cy="auto-checkout-time" type="time" step="3600" id="autoCheckoutTime" value="{{getAutoCheckoutTime}}" >
                    </span>
                  </li>
                </ul>
                <div class="separator separator-dashed my-8"></div>
              </div>
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                  {{getEduTitle}}
                </div>
                <div class="col-7">
                  <p class="font-weight-bolder">Manage {{getEduTitle}} settings.</p>
                </div>
                <div class="col-3">
                  <div data-cy="configure-express-drive-up" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelExpressDriveUp" aria-expanded="false" aria-controls="panelExpressDriveUp" id="btnConfigExpressDriveUp">
                    Configure
                  </div>
                </div>
              </div>
              <div id="panelExpressDriveUp" class="collapse container">
                <ul>
                  {{#unless hasCustomization "people/familyCheckin/enabled"}}
                    <li class="switchRow">
                      <span class="switchLabel">Enabled </span>
                      <span class="switchHolder">
                        <label class="switch">
                          <input type="checkbox" name="expressDriveUp" id="expressDriveUpEnabled" {{getExpressDriveUpEnabled}}>
                          <span class="slider round"></span>
                        </label>
                      </span>
                    </li>
                  {{/unless}}
                  <li class="switchRow">
                    <span class="switchLabel">Arrived Text </span>
                    <span class="switchHolder">
                      <input data-cy="arrived-text" type="text" class="form-control" style="min-width: 500px;" id="expressDriveUpArrivedText" value="{{getExpressDriveUpArrivedText}}">
                    </span>
                  </li>
                  <li class="switchRow">
                    <div class="d-flex flex-row">
                      <div data-cy="save-express-drive-up" class="btn btn-primary font-weight-bolder mr-4" id="btnSaveExpressDriveUp">Save</div>
                    </div>
                  </li>
                </ul>
                <div class="separator separator-dashed my-8"></div>
              </div>
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                  Standard Immunizations
                </div>
                <div class="col-7">
                  <p class="font-weight-bolder">Toggle the exemption for standard immunizations. This will prevent reminders to users within the mobile application.</p>
                </div>
                <div class="col-3">
                  <div data-cy="configure-standard-inmunization" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelStandardImmunization" aria-expanded="false" aria-controls="panelStandardImmunization" id="btnConfigureStandardImmunization">
                    Configure
                  </div>
                </div>
              </div>
              <div data-cy="panel-standard-inmunization" id="panelStandardImmunization" class="collapse container">
                <div class="row">
                  <div class="col-md-6 checkbox-list">
                    <label class="checkbox checkbox-primary">
                      <input data-cy="disable-inmunization-reminder" type="checkbox" class="form-check-input" id="immunizationReminder" checked={{hasCustomization "mobile/immunizationAlert/disabled"}} >
                      <span></span>
                      Disable Mobile Immunization Reminders
                    </label>
                  </div>
                </div>
                {{#each getStandardImmunizationOverride}}
                  <div class="row my-6">
                    <form data-cy="standard-inmunization-{{type}}" id="frmUpdateStandardImmunizationType-{{type}}" class="row col-md-12">
                      <div class="col-md-2">
                        <label data-cy="inmunization-type">{{type}}</label>
                      </div>
                      <div class="col-md-4">
                        <label data-cy="inmunization-description">{{description}}</label>
                      </div>
                      <div class="col-md-2 checkbox-list">
                        <label class="checkbox checkbox-primary">
                          <input data-cy="inmunization-{{type}}-exempt" type="checkbox" class="form-check-input" id="{{type}}-exempt" checked={{exempt}} >
                          <span></span>
                          Exempt
                        </label>
                      </div>
                      <div class="col-md-2 checkbox-list">
                        <label class="checkbox checkbox-primary">
                          <input data-cy="inmunization-{{type}}-annual" type="checkbox" class="form-check-input" id="{{type}}-annual" checked={{annual}} >
                          <span></span>
                          Annual
                        </label>
                      </div>
                      <div class="col-md-2">
                        <div data-cy="inmunization-{{type}}-save" class="btn btn-primary font-weight-bolder" id="btnSaveStandardImmunization" fieldid="{{type}}">Save</div>
                      </div>
                    </form>
                  </div>
                {{/each}}
                <div class="separator separator-dashed my-8"></div>
              </div>
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                  Custom Immunization Types
                </div>
                <div class="col-7">
                  <p class="font-weight-bolder">Manage custom immunization types that can be applied to a child's profile.</p>
                </div>
                <div class="col-3">
                  <div data-cy="configure-custom-inmunization" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelCustomImmunization" aria-expanded="false" aria-controls="panelCustomImmunization" id="btnConfigureCustomImmunization">
                    Configure
                  </div>
                </div>
              </div>
              <div id="panelCustomImmunization" class="collapse container">
                <div class="row col-12 mb-4">
                  <div data-cy="add-inmunization-type-btn" id="btnAddImmunizationType" class="btn btn-primary font-weight-bolder ml-4"><i class="fad fa-swap-opacity fa-plus text-white"></i> Add Immunization Type</div>
                </div>
                <div class="row">
                  <form id="frmAddImmunizationType" style="display:none" class="row col-md-12 mb-6">
                    <div class="row col-md-12">
                      <div class="col-md-2">
                        <label>Type:</label>
                        <input data-cy="add-inmunization-type" type="text" class="form-control" id="AddImmunizationType-Type" required>
                      </div>
                      <div class="col-md-4">
                        <label>Description:</label>
                        <input data-cy="add-inmunization-description" type="text" class="form-control" id="AddImmunizationType-Desc" required>
                      </div>
                      <div class="col-md-4 pt-8">
                        <div data-cy="save-inmunization-btn" class="btn btn-primary font-weight-bolder mr-4" id="btnSaveAddImmunizationType">Save</div>
                        <div class="btn btn-secondary font-weight-bolder" id="btnCancelAddImmunizationType">Cancel</div>
                      </div>
                    </div>
                  </form>
                </div>
                {{#each getCustomImmunizations}}
                  <div class="row">
                    <form data-cy="inmunization-type-item" id="frmUpdateImmunizationType-{{_id}}" class="row col-md-12 mb-6">
                      <div class="col-md-2">
                        <label>Type:</label>
                        <input data-cy="update-inmunization-type" type="text" required class="form-control" id="{{_id}}-Type" value={{type}}>
                      </div>
                      <div class="col-md-4">
                        <label>Description:</label>
                        <input data-cy="update-inmunization-description" type="text" required class="form-control" id="{{_id}}-Desc" value={{description}}>
                      </div>
                      <div class="col-md-2 checkbox-list pt-10">
                        <label class="checkbox checkbox-primary">
                          <input data-cy="checkbox-annual" type="checkbox" class="form-check-input" id="{{_id}}-Annual" checked={{annual}} >
                          <span></span>
                          Annual
                        </label>
                      </div>
                      <div class="col-md-2 checkbox-list pt-10" >
                        <label class="checkbox checkbox-primary">
                          <input data-cy="checkbox-archived" type="checkbox" class="form-check-input" id="{{_id}}-Archived" checked={{archived}} >
                          <span></span>
                          Archived
                        </label>
                      </div>
                      <div class="col-md-2 pt-8">
                        <div data-cy="save-update-inmunization" class="btn btn-primary font-weight-bolder" id="btnSaveUpdateImmunizationType" fieldid="{{_id}}">Save</div>
                      </div>
                    </form>
                  </div>
                {{/each}}
                <div class="separator separator-dashed my-8"></div>
              </div>
              {{#if hasCustomization "reservations/enabled"}}
                <div class="row col-12 mb-10">
                  <div class="col-2 text-right">
                    Schedule Types
                  </div>
                  <div class="col-7">
                    <p class="font-weight-bolder">Schedule types allow you to define blocks of schedulable time.</p>
                  </div>
                  <div class="col-3">
                    <div data-cy="configure-schedule-types" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelScheduleTypes" aria-expanded="false" aria-controls="panelScheduleTypes" id="btnConfigureScheduleTypes">
                      Configure
                    </div>
                  </div>
                </div>
                <div id="panelScheduleTypes" class="collapse container">
                    {{> _orgCustomizationToggleRow getScheduleTypesForcedLinkCustomizationData }}
                  <div class="row col-12 mb-4">
                    <div data-cy="add-schedule-type-btn" id="btnAddScheduleType" class="btn btn-primary font-weight-bolder ml-4"><i class="fad fa-swap-opacity fa-plus text-white"></i> Add Schedule Type</div>
                  </div>
                  {{> addScheduleType}}
                  <div class="separator separator-dashed my-8"></div>
                </div>
                <div class="row col-12 mb-10">
                  <div class="col-2 text-right">
                    Holidays
                  </div>
                  <div class="col-7">
                    <p class="font-weight-bolder">Holidays are scheduled close days that influence scheduling and forecasting.</p>
                  </div>
                  <div class="col-3">
                    <div data-cy="configure-holidays" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelHolidays" aria-expanded="false" aria-controls="panelHolidays" id="btnConfigureHolidays">
                      Configure
                    </div>
                  </div>
                </div>
                {{> _orgHolidayPanel }}
                <div class="row col-12 mb-10">
                  <div class="col-2 text-right">
                    Forecasting Defaults
                  </div>
                  <div class="col-7">
                    <p class="font-weight-bolder">Various defaults related to forecasting reports.</p>
                  </div>
                  <div class="col-3">
                    <div class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelForecasting" aria-expanded="false" aria-controls="panelForecasting" id="btnConfigureForecasting">
                      Configure
                    </div>
                  </div>
                </div>
                <div id="panelForecasting" class="collapse container">
                  <div class="row mb-6">

                    <div class="col-4 text-right">
                      Ideal Revenue %
                    </div>
                    <div class="col-2">
                      <input type="text" class='form-control' id="forecasting-default-ideal-revenue-percent" value="{{getForecastingSettings.idealRevenue}}">
                    </div>
                  </div>
                  <div class="row mb-6">

                    <div class="col-4 text-right">
                      Target Payroll %
                    </div>
                    <div class="col-2">
                      <input type="text" class="form-control" id="forecasting-default-target-payroll-percent" value="{{getForecastingSettings.targetPayroll}}">
                    </div>
                  </div>
                  <div class="row mb-6">
                    <div class="col-4"></div>
                    <div class="col-6">
                      <button class='btn btn-primary' id="btnUpdateForecastingDefaults">Update</button>
                    </div>
                  </div>
                </div>
              {{/if}}
              {{#if hasCustomization "people/staffPay/enabled"}}
                <div class="row col-12 mb-10">
                  <div class="col-2 text-right">
                    Staff Timekeeping
                  </div>
                  <div class="col-7">
                    <p class="font-weight-bolder">Require weekly time confirmation.</p>
                  </div>
                  <div class="col-3">
                    <div class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelCustomPay" aria-expanded="false" aria-controls="panelCustomPay" id="btnConfigureCustomPayType">
                      Configure
                    </div>
                  </div>
                </div>
                <div id="panelCustomPay" class="collapse container">
                  {{#if canViewTimeCardLockConfig}}

                  <div class="row col-md-12 mb-6">
                    <form class="row col-md-12 mb-6">
                      <div class="col-md-4">
                          <div class="row col-md-12 align-items-center">
                            <span class="col-md-6 px-0 switchLabel">Time Cards Lock</span>
                            <span class="flex-column switchHolder">
                                <label class="switch">
                                  <input type="checkbox" name="timeCardsLock" id="timeCardsLockedToggle" checked="{{getTimeCardsLockedToggleEnabled}}">
                                  <span class="slider round"></span>
                                </label>
                              </span>
                          </div>
                      </div>
                      <div class="row col-md-12 mb-6">
                          <div class="col-md-5 ml-sm-7 ml-md-6 ml-lg-5">
                              <p class="{{#unless getTimeCardsLockedToggleEnabled}} timeCardsLockedLabelDisabled {{else}} timeCardsLockedLabel {{/unless}}">Choose a date to lock all prior time cards</p>
                              <div class="col-md-4 px-0 input-group">
                                <input type="text" class="form-control {{#unless getTimeCardsLockedToggleEnabled}} timeCardsLockedDateDisabled {{/unless}}" disabled="{{getTimeCardsLockedToggleDisabled}}" id="timeCardsLockedDate" value="{{formattedTimeCardsLockedDate}}">
                              </div>
                            </div >
                          <div class="col-md-3">
                              {{#if showPropagateTimecardLock}}
                              <div class="form-group timecard-lock-orgs">
                                  <label>Apply to Org(s):</label>
                                  <div class="input-group">
                                      {{> announcementOrgsField preselectAll="true" }}
                                  </div>
                              </div>
                              {{/if}}
                          </div>
                            <div class="offset-md-2 col-md-1 text-center pt-8">
                              <div class="btn btn-primary font-weight-bolder" id="btnSaveTimeCardsLocked">Save</div>
                            </div>
                        </div>
                    </form>
                  </div>
                  {{/if}}
                  <div class="row col-md-12 mb-10">
                    <form class="row col-md-12 mb-6">
                      <div class="row col-md-4 mb-6 align-items-center">
                          <span class="col-md-6 switchLabel">Time Confirmation</span>
                          <span class="flex-column switchHolder">
                            <label class="switch">
                              <input type="checkbox" name="timeConfirmation" id="timeConfirmationEnabled" checked={{getTimeConfirmationEnabled}} >
                              <span class="slider round"></span>
                            </label>
                          </span>
                      </div>
                      <div class="row col-md-12 mb-6">
                          <div class="col-md-10 ml-sm-7 ml-md-6 ml-lg-5">
                            <p class={{disableTimeConfirmationText}} >What day of the week should staff confirm their time?</p>
                            <div class="{{disableTimeConfirmationStyling}}">
                              <input type="radio" id="SUN" class="dayInput" name="dayInput" value="SUN" disabled={{disableTimeConfirmationRadios}} />
                              <label for="SUN">
                                <p class="mt-1">SUN</p>
                              </label>
                            </div>
                            <div class="{{disableTimeConfirmationStyling}}">
                              <input type="radio" id="MON" class="dayInput" name="dayInput" value="MON" disabled={{disableTimeConfirmationRadios}} />
                              <label for="MON">
                                <p class="mt-1">MON</p>
                              </label>
                            </div>
                            <div class="{{disableTimeConfirmationStyling}}">
                              <input type="radio" id="TUE" class="dayInput" name="dayInput" value="TUE" disabled={{disableTimeConfirmationRadios}} />
                              <label for="TUE">
                                <p class="mt-1">TUE</p>
                              </label>
                            </div>
                            <div class="{{disableTimeConfirmationStyling}}">
                              <input type="radio" id="WED" class="dayInput" name="dayInput" value="WED" disabled={{disableTimeConfirmationRadios}} />
                              <label for="WED">
                                <p class="mt-1">WED</p>
                              </label>
                            </div>
                            <div class="{{disableTimeConfirmationStyling}}">
                              <input type="radio" id="THU" class="dayInput" name="dayInput" value="THU" disabled={{disableTimeConfirmationRadios}} />
                              <label for="THU">
                                <p class="mt-1">THU</p>
                              </label>
                            </div>
                            <div class="{{disableTimeConfirmationStyling}}">
                              <input type="radio" id="FRI" class="dayInput" name="dayInput" value="FRI" disabled={{disableTimeConfirmationRadios}} />
                              <label for="FRI">
                                <p class="mt-1">FRI</p>
                              </label>
                            </div>
                            <div class="{{disableTimeConfirmationStyling}}">
                              <input type="radio" id="SAT" class="dayInput" name="dayInput" value="SAT" disabled={{disableTimeConfirmationRadios}} />
                              <label for="SAT">
                                <p class="mt-1">SAT</p>
                              </label>
                            </div>
                          </div>
                          <div class="col-md-1 text-center pt-8">
                            <div class="btn btn-primary font-weight-bolder" id="btnSaveTimeConfirmation">Save</div>
                          </div>
                      </div>
                    </form>

                  </div>
                  <div class="row col-md-12 mb-6">
                    <form class="row col-md-12 mb-6">
                      <div class="col-md-2">
                        <p>OverTime Settings</p>
                      </div>
                      <div class="col-md-2">
                        <label>Hours Threshold:</label>
                        <input type="number" class="form-control" id="customPayOverTimeThreshold" value={{getOTValue}}>
                      </div>
                      <div class="col-md-2 checkbox-list pt-10">
                        <label class="checkbox checkbox-primary">
                          <input type="checkbox" class="form-check-input" id="customPayOverTimeActive" checked={{getOTActive}} >
                          <span></span>
                          Active
                        </label>
                      </div>
                      <div class="col-md-4 font-weight-bold">
                        <label>Frequency: </label>
                        <select id="customOvertimeFrequency" class="form-control form-control-solid">
                          <option value="weekly" {{selectedIfEqual getOTFrequency "weekly"}}>Weekly</option>
                          <option value="daily" {{selectedIfEqual getOTFrequency "daily"}}>Daily</option>
                        </select>
                      </div>
                      <div class="col-md-1 pt-8">
                        <div class="btn btn-primary font-weight-bolder" id="btnSaveCustomPaySettings">Save</div>
                      </div>
                    </form>
                  </div>
                  <div class="row col-md-4 mb-6">
                    <div id="btnAddCustomPayType" class="btn btn-primary font-weight-bolder"><i class="fad fa-plus fa-swap-opacity text-white"></i> Add Custom Pay Type</div>
                  </div>
                  <form id="frmAddCustomPayType" style="display:none" class="row col-md-12 mb-6">
                    <div class="row col-md-12">
                      <div class="col-md-2">
                        <label>Type:</label>
                        <input type="text" class="form-control" id="AddCustomPay-Type" required>
                      </div>
                    {{#if hasAdp}}
                        <div class="col-md-1">
                            <label>ADP Code:</label>
                            <input type="text" maxlength=6 class="form-control" id="AddCustomPay-AdpCode" required>
                        </div>
                    {{/if}}
                      <div class="col-md-2">
                        <label>Rate (per hour)</label>
                        <input type="number" class="form-control" id="AddCustomPay-Rate">
                      </div>
                      <div class="col-md-{{#if hasAdp}}3{{else}}4{{/if}} checkbox-list pt-4">
                        <label class="checkbox checkbox-primary">
                          <input type="checkbox" class="form-check-input" id="AddCustomPay-StaffProfile" >
                          <span></span>
                          Rate Determined on Staff Profile
                        </label>
                        <label class="checkbox checkbox-primary">
                          <input type="checkbox" class="form-check-input" id="AddCustomPay-OvertimeEligible" >
                          <span></span>
                          Overtime Eligible
                        </label>
                      </div>
                      <div class="col-md-3 pt-8" style="padding-top:28px">
                        <div class="btn btn-primary font-weight-bolder mr-4" id="btnSaveCustomPayType">Save</div>
                        <div class="btn btn-secondary font-weight-bolder" id="btnCancelCustomPayType">Cancel</div>
                      </div>
                    </div>
                  </form>
                  <form class="row col-md-12">
                  <div class="row col-md-12 mb-6">
                    <div class="col-md-2">
                      <label>Type:</label>
                      <input type="text" class="form-control" id="StandardPay-Type" disabled value="Standard">
                    </div>
                      {{#if hasAdp}}
                      <div class="col-md-1">
                          <label>ADP Code:</label>
                          <input type="text" maxlength=6 class="form-control" id="Standard-AdpCode" value="{{standardPayAdpCode}}" required>
                      </div>
                      {{/if}}
                    <div class="col-md-3">
                      <label>Rate (per hour)</label>
                      <input type="text" class="form-control" id="StandardPay-Rate" disabled value="Determined on Staff Profile">
                    </div>
                      {{#if hasAdp}}
                      <div class="offset-md-4 col-md-1 pt-8">
                          <div class="btn btn-primary font-weight-bolder" id="btnUpdateStandardPayAdpCode">Save</div>
                      </div>
                      {{/if}}
                  </div>
                  </form>
                  {{#each getCustomPayTypes}}
                    <div class="row col-md-12 mb-6">
                      <form id="frmUpdateCustomPayType-{{_id}}" class="row col-md-12">
                        <div class="col-md-2">
                          <label>Type:</label>
                          <input type="text" required class="form-control" id="{{_id}}-Type" value={{type}}>
                        </div>
                      {{#if hasAdp}}
                      <div class="col-md-1">
                          <label>ADP Code:</label>
                          <input type="text" maxlength=6 class="form-control" id="{{_id}}-AdpCode" value="{{adpCode}}" required>
                      </div>
                      {{/if}}
                        <div class="col-md-2">
                          <label>Rate (per hour)</label>
                          <input type="number" required class="form-control" id="{{_id}}-Rate" value={{rate}} >
                        </div>
                        <div class="col-md-{{#if hasAdp }}3{{else}}4{{/if}} checkbox-list pt-4">
                          <label class="checkbox checkbox-primary">
                            <input type="checkbox" class="form-check-input" id="{{_id}}-StaffProfile" checked={{staffProfileRate}} >
                            <span></span>
                            Rate Determined on Staff Profile
                          </label>
                          <label class="checkbox checkbox-primary">
                            <input type="checkbox" class="form-check-input" id="{{_id}}-OvertimeEligible" checked={{staffOTEligible}}>
                            <span></span>
                            Overtime Eligible
                          </label>
                        </div>
                        <div class="col-md-2 checkbox-list pt-10">
                          <label class="checkbox checkbox-primary">
                            <input type="checkbox" class="form-check-input" id="{{_id}}-Archived" checked={{archived}} >
                            <span></span>
                            Archived
                          </label>
                        </div>
                        <div class="col-md-1 pt-8">
                          <div class="btn btn-primary font-weight-bolder" id="btnSaveUpdateCustomPayType" fieldid="{{_id}}">Save</div>
                        </div>
                      </form>
                    </div>
                  {{/each}}
                  <div class="separator separator-dashed my-8"></div>
                </div>
              {{/if}}
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                  Message Center
                </div>
                <div class="col-7">
                  <p class="font-weight-bolder">Manage message center visibility and notifications.</p>
                </div>
                <div class="col-3">
                  <div data-cy="configure-message-center" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelMessageCenter" aria-expanded="false" aria-controls="panelMessageCenter" id="btnConfigureMessageCenter">
                    Configure
                  </div>
                </div>
              </div>
              <div id="panelMessageCenter" class="collapse container">
                <div class="row col-md-12">
                  <div class="col">
                    <p><b>Administrative Visibility:</b> Allows administrators to see all messages in message center regardless of recipient.</p>
                    {{#if trueIfEq (checkedIfActive "messageVisibility") "checked"}}
        							<p><i>Currently enabled.</i></p>
        							<div data-cy="disable-administrative-visibility" class="btn btn-primary font-weight-bolder" id="btnDisableMessageVisibility">Disable Administrative Visibility</div>
      							{{else}}
        							<p><i>Currently disabled.</i></p>
        							<div data-cy="enable-administrative-visibility" class="btn btn-primary font-weight-bolder" id="btnEnableMessageVisibility">Enable Administrative Visibility</div>
      							{{/if}}
                  </div>
                  <div class="col">
                    <p><b>Suppress Staff Notifications:</b> Prevent staff members from getting message center notifications via email.</p>
                    {{#if trueIfEq (checkedIfActive "suppressStaffMessageCenterNotifications") "checked"}}
        							<p><i>Currently suppressed.</i></p>
        							<div class="btn btn-primary font-weight-bolder" id="btnEnableStaffMessageNotifications">Enable Staff Message Notifications</div>
      							{{else}}
        							<p><i>Notifications currently enabled.</i></p>
        							<div class="btn btn-primary font-weight-bolder" id="btnSuppressStaffMessageNotifications">Suppress Staff Message Notifications</div>
      							{{/if}}
                  </div>
                  <div class="col">
                    <p><b>Disable Message Center for Staff:</b> When selected, this will remove access to the Message Center for all staff.</p>
                    {{#if trueIfEq (checkedIfActive "disableStaffMessages") "checked"}}
        							<p><i>Currently disabled.</i></p>
        							<div data-cy="enable-staff-message-center" class="btn btn-primary font-weight-bolder" id="btnEnableStaffMessageCenter">Enable Staff Message Center</div>
      							{{else}}
        							<p><i>Currently enabled.</i></p>
        							<div data-cy="disable-staff-message-center" class="btn btn-primary font-weight-bolder" id="btnDisableStaffMessageCenter">Disable Staff Message Center</div>
      							{{/if}}
                  </div>
                </div>
                <div class="separator separator-dashed my-8"></div>
              </div>
                <div class="row col-12 mb-10">
                    <div class="col-2 text-right">
                        Chat Support
                    </div>
                    <div class="col-7">
                        <p class="font-weight-bolder">Enable app users to chat online with your customer service
                            team.</p>
                    </div>
                    <div class="col-3">
                        <div data-cy="configure-chat-support" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse"
                             data-target="#panelChatSupport" aria-expanded="false" aria-controls="panelChatSupport"
                             id="btnConfigureChatSupport">
                            Configure
                        </div>
                    </div>
                </div>
                <div id="panelChatSupport" class="collapse container">

                    <ul>

                        <li class="switchRow">
                            <div class="switchContainer">
                                <span class="switchLabel" style="margin-right:90px">Enabled</span>
                                <span class="switchHolder">
                          <label class="switch">
                            <input data-cy="chat-support-enabled-check" type="checkbox" id="chatSupportActive" {{checkedIfActive "chatSupport"}}>
                            <span class="slider round"></span>
                          </label>
                        </span>
                            </div>
                        </li>

                        <li class="switchRow">
                            <div class="switchContainer">
                                <span class="switchLabel" style="margin-right:78px">Chat URL</span>
                                <span class="switchHolder">
                        <input data-cy="chat-url-text" type="text" class="form-control" style="min-width: 400px; margin-bottom: 10px;"
                               id="charUrlText" maxlength="2000" placeholder="Max 2000 characters."
                               value={{getChatSupportChatUrl}}>
                      </span>
                            </div>
                        </li>

                        <li class="switchRow">
                            <div class="switchContainer">
                      <span class="switchLabel" style="margin-right:10px">
                        Menu verbiage
                        <span
                                class="btn btn-default btn-icon btn-sm"
                                data-toggle="tooltip" id="btn-archive-all"
                                title=""
                                data-original-title="This is the label/wording in the help menu that users will tap to chat with your support team.">
                          <span class="svg-icon svg-icon-md">
                            <span><i class="fad fa-duotone fa-info-circle"></i></span>
                          </span>
                        </span>
                      </span>
                                <span class="switchHolder">
                        <input data-cy="menu-verbiage" type="text" class="form-control" style="min-width: 400px;" id="menuVerbiageText"
                               value={{getChatSupportMenuVerbiage}} maxlength="24" placeholder="Max 24 characters.">
                      </span>
                            </div>
                        </li>

                        <li class="switchRow">
                            <button data-cy="save-chat-support-btn" type="button" class="btn btn-primary font-weight-bolder"
                                    style="margin-left: 48px; margin-bottom: 30px;"
                                    id="btnSaveChatSupport" {{saveChatSupportEnabled}}>Save
                            </button>
                        </li>

                    </ul>
                </div>
                <div class="row col-12 mb-10">
                    <div class="col-2 text-right">
                        Dunning Communications
                    </div>
                    <div class="col-7">
                        <p class="font-weight-bolder">Automate follow-up balance statement emails for past-due
                            payments.</p>
                    </div>
                    <div class="col-3">
                        <div class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse"
                             data-target="#panelDunningComs" aria-expanded="false" aria-controls="panelDunningComs"
                             id="btnConfigDunningComs">
                            Configure
                        </div>
                    </div>
                </div>
                <div id="panelDunningComs" class="collapse container">
                    <ul class="pl-0 ml-5">
                        <li class="switchRow pl-0">
                            <div class="col font-weight-bold col-form-label pl-1 ml-6">Enable the following balance
                                statement email schedule for unpaid, past due invoices
                            </div>
                            <span class="switchHolder pl-4">
                        <label class="switch">
                          <input type="checkbox" name="dunningComs" id="dunningComsEnabled"
                                 checked="{{getDunningComsEnabled}}">
                          <span class="slider round"></span>
                        </label>
                      </span>
                        </li>
                        <div class="row justify-content-start pl-0 ml-4 align-items-center">
                            <div class="pl-1 ml-2">What is the earliest invoice date you want to attempt to collect debt
                                on?
                            </div>
                            <div class="col-2">
                                <div class="input-group">
                                    <input type="text" class="form-control pull-right" id="dunningComDate"
                                           value="{{formatDate epochDate 'MM/DD/YYYY'}}">
                                </div>
                            </div>
                        </div>
                        <div class="row align-items-center ml-6 mt-4">
                            <div class="row font-weight-bold ml-1 pr-8">All plans:</div>
                            <div class="">Send first email after</div>
                            <input type="text" class="form-control mx-2 dunningComs p-0"
                                   style="max-width: 38px; text-align: center;" id="dunningComDay"
                                   value="{{getDunningComsDay}}">
                            <div class="">days past due</div>
                            <div class="ml-10">Send follow-up emails every</div>
                            <input type="text" class="form-control mx-2 p-0"
                                   style="max-width: 38px; text-align: center;" id="dunningComFollowUp"
                                   value="{{getDunningComsFollowUp}}">
                            <div class="">days</div>
                            <div class="ml-10">Stop after</div>
                            <input type="text" class="form-control mx-2 dunningComs p-0"
                                   style="max-width: 38px; text-align: center;" id="dunningComTotal"
                                   value="{{getDunningComsTotal}}" required>
                            <div class="">total email(s)</div>
                        </div>
                        <div class="btn btn-primary font-weight-bolder mr-4 ml-4" id="btnSaveDunningComs">Save</div>
                    </ul>
                    <div class="separator separator-dashed my-8"></div>
                </div>
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                  Return Address
                </div>
                <div class="col col-7">
                    <p class="font-weight-bolder"> You can designate a reply-to address for emails sent from MomentPath on your organization's behalf. When recipients
                      hit reply, their message will come to this address instead of MomentPath support.
                    </p>
                    {{#if replyToAddress}}
                      <b>Current Reply-to Address:</b> {{replyToAddress}}
                    {{else}}
                      <i>Currently not enabled.</i>
                    {{/if}}
                </div>
                <div class="col-3">
                  <div data-cy="configure-reply-to-address" class="btn btn-primary font-weight-bolder btn-text-white ml-4" id="btnEditReplyToAddress">
                    Configure
                  </div>
                </div>
              </div>
               {{#if canModifyMediaRequirement}}
                 <div class="row col-12 mb-10">
                   <div class="col-2 text-right">
                     Media Requirement
                   </div>
                   <div class="col col-7">
                    <p class="font-weight-bolder"> Specify the number of media items (photos, videos) posted per child over a specified period of time. Exceptions
                      will be reported on the dashboard.
                    </p>
                    {{#if mediaRequirementDescription}}
                      <b>Current Media Requirement:</b> {{mediaRequirementDescription}}
                    {{else}}
                      <i>Currently not enabled.</i>
                    {{/if}}
                   </div>
                   <div class="col-3">
                    <div data-cy="btn-edit-media-requirements" class="btn btn-primary font-weight-bolder btn-text-white ml-4" id="btnEditMediaRequirement">
                        Configure
                    </div>
                   </div>
                 </div>
              {{/if}}
            </div>
            {{/if}}
              <div class="row col-12 mb-10">
              <div class="col-2 text-right">
                Bus Routes
              </div>
              <div class="col col-7">
                  <p class="font-weight-bolder">Enter the bus routes that take your children to/from school.</p>
              </div>
              <div class="col-3">
                <div data-cy="bus-routes-conf" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelBusRoutes" aria-expanded="false" aria-controls="panelBusRoutes" id="btnConfigureBusRoutes">
                  Configure
                </div>
              </div>
            </div>
            <div id="panelBusRoutes" class="collapse container">
              <div class="row col-12 mb-4">
                <div data-cy="btn-add-bus-routes" id="btnAddBusRoute" class="btn btn-primary font-weight-bolder ml-4"><i class="fad fa-swap-opacity fa-plus text-white"></i> Add Bus Route</div>
              </div>
              <div class="row">
                <form id="frmAddBusRoute" style="display:none" class="row col-12 mb-6">
                  <b>Add/Edit Bus Route</b><br/>
                  <input type="hidden" name="busRoute-id">
                  <div class="row col-12 mb-4">
                    <div class='col-8'>
                      <label>Name of Bus Route:</label>
                      <input data-cy="add-bus-route-name" type="text" class="form-control" id="AddBusRoute-Name" required>
                    </div>
                  </div>
                  <div class="row col-12">
                    <div class="col-md-6 checkbox-list">
                      <label class="checkbox checkbox-primary">
                        <input data-cy="check-am" type="checkbox" class="form-check-input" id="AddBusRoute-AM" checked="checked" >
                        <span></span>
                        AM Route
                      </label>
                      <label class="checkbox checkbox-primary">
                        <input data-cy="check-pm" type="checkbox" class="form-check-input" id="AddBusRoute-PM" checked="checked" >
                        <span></span>
                        PM Route
                      </label>
                    </div>
                  </div>
                  <div class="row col-4 pt-8">
                    <div data-cy="btn-save-bus-route" class="btn btn-primary font-weight-bolder mr-4" id="btnSaveAddBusRoute">Save</div>
                    <div class="btn btn-secondary font-weight-bolder" id="btnCancelAddBusRoute">Cancel</div>
                  </div>
                </form>
                <table data-cy="bus-route-table" class="table">
                  <tr style="border-bottom:1px solid #000">
                    <th>Bus Route Name</th>
                    <th>AM Route</th>
                    <th>PM Route</th>
                    <th></th>
                  </tr>
                  {{#each getBusRoutes}}
                    <tr>
                      <td class="text-break">
                        {{name}}
                      </td>
                      <td>
                        {{#if am}}
                          <i class="fad fa-check"></i>
                        {{else}}
                          <i class="fad fa-times"></i>
                        {{/if}}
                      </td>
                      <td>
                        {{#if pm}}
                          <i class="fad fa-check"></i>
                        {{else}}
                          <i class="fad fa-times"></i>
                        {{/if}}
                      </td>
                      <td>
                        <span data-cy="btn-edit-bus-route" id="btnEditBusRoute" class="text-primary" style="cursor:pointer;" data-id="{{_id}}">Edit</span> |
                        <span data-cy="btn-delete-bus-route" id="btnDeleteBusRoute" class="text-primary" style="cursor:pointer;" data-id="{{_id}}">Delete</span>
                      </td>
                    </tr>
                  {{/each}}
                </table>
              </div>
              <div class="separator separator-dashed my-8"></div>
                <div class="row col-12 mb-4">
                    <div data-cy="btn-add-school" id="btnAddSchool" class="btn btn-primary font-weight-bolder ml-4"><i class="fad fa-swap-opacity fa-plus text-white"></i> Add School </div>
                </div>
                <div class="row">
                    <form id="frmAddSchool" style="display:none" class="row col-12 mb-6">
                        <b>Add/Edit School: </b><br/>
                        <input type="hidden" name="addSchool-id">
                        <div class="row col-12 mb-4">
                            <div class='col-8'>
                                <label>Name of School: </label>
                                <input data-cy="add-school-name" type="text" class="form-control w-50" id="AddSchool-Name" required>
                            </div>
                        </div>
                        <div class="row col-4 pt-8">
                            <div data-cy="btn-save-school" class="btn btn-primary font-weight-bolder mr-4" id="btnSaveAddSchool">Save</div>
                            <div class="btn btn-secondary font-weight-bolder" id="btnCancelAddSchool">Cancel</div>
                        </div>
                    </form>
                </div>
                <table data-cy="school-table" class="table" style="width: 400px;">
                    <tr style="border-bottom:1px solid#000;">
                        <th>School Name</th>
                        <th></th>
                    </tr>
                    {{#each getSchools}}
                    <tr>
                        <td class="text-break w-75" data-index="{{@index}}">
                            {{this}}
                        </td>
                        <td>
                            <span data-cy="btn-edit-school" id="btnEditSchoolName" class="text-primary" style="cursor:pointer;" data-id="{{@index}}">Edit</span> |
                            <span data-cy="btn-delete-school" id="btnDeleteSchoolName" class="text-primary" style="cursor:pointer;" data-id="{{@index}}">Delete</span>
                        </td>
                    </tr>
                    {{/each}}
                </table>
            </div>
              {{#if hasRegistrationFlow }}
              <div class="row col-12 mb-10">
                  <div class="col-2 text-right">
                      Grades Served
                  </div>
                  <div class="col col-7">
                      <p class="font-weight-bolder">Configure the grade levels that your organization offers.</p>
                  </div>
                  <div class="col-3">
                      <div data-cy="grades-configure" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelGradesServed" aria-expanded="false" aria-controls="panelGradesServed" id="btnConfigureGrades">
                          Configure
                      </div>
                  </div>
              </div>
              <div id="panelGradesServed" class="collapse container">
                  <div class="col-10 offset-1">
                      <div id="frmGradesServed" class="select-multi-group row">
                          {{#each choice in defaultGrades}}
                              <div class="col-4">
                                  <div class="checkbox-inline">
                                      <label class="checkbox checkbox-primary">
                                          <input data-cy="check-{{choice}}" type="checkbox" value="{{choice}}" class="select-multi-option" {{checkedIfContains allowableProfileGrades choice}}>
                                          <span></span>
                                          {{choice}}
                                      </label>
                                  </div>
                              </div>
                          {{/each}}
                      </div>
                  </div>
                  <div class="row offset-1 mt-5">
                      <div data-cy="btn-save-grades" class="btn btn-primary font-weight-bolder" id="btnSaveGrades">Save</div>
                  </div>
                  <div class="separator separator-dashed my-8"></div>
              </div>
              {{/if}}
              <div class="row col-12 mb-10">
                <div class="col-2 text-right">
                  Service Accounts
                </div>
                <div class="col-7">
                  <p class="font-weight-bolder">User credentials to access your organization's data via an API</p>
                </div>
                <div class="col-3">
                  <div data-cy="service-account-manage-btn" class="btn btn-primary font-weight-bolder btn-text-white ml-4" data-toggle="collapse" data-target="#panelServiceAccounts" aria-expanded="false" aria-controls="panelServiceAccounts" id="btnManageServiceAccount">
                    Manage
                  </div>
                </div>
              </div>
              <div id="panelServiceAccounts" class="collapse container">
                <div class="row col-12 mb-4">
                  <div data-cy="add-service-account-btn" id="btnAddServiceAccountUser" class="btn btn-primary font-weight-bolder ml-4"><i class="fad fa-swap-opacity fa-plus text-white"></i> Add Service Account User</div>
                </div>
                <div class="row">
                  <form id="frmAddServiceAccount" style="display:none" class="row col-12 mb-6">
                    <b>Add/Edit Service Account</b><br/>
                    <input type="hidden" name="serviceAccount-id">
                    <div class="row col-12 mb-4">
                      <div class='col-8'>
                        <label>User Email:</label>
                        <input data-cy="add-service-account-email" type="text" class="form-control" id="AddServiceAccount-email" required {{serviceAccountEmailDisabled}}>
                        {{#if serviceAccountError}}
                          <label style="color: red;font-weight: bold;">{{serviceAccountError}}</label>
                        {{/if}}
                      </div>
                    </div>
                    <div class="row col-12 mb-4"> 
                      <div class="col-8">
                          <label>Password:</label>
                          <input data-cy="add-service-account-password" type="{{passwordInput}}" class="form-control" style="background-color: #E5EAEE;" id="AddServiceAccount-password" required disabled>
                          {{#unless isMaskedPassword}}
                            <div style="color: red; font-weight: bold; padding-top: 10px;">Record this password-it cannot be viewed again after clicking Save!</div>
                          {{/unless}}
                      </div>
                      {{#unless isMaskedPassword}}
                        <div class="col-1" style="display: flex; align-items: center; justify-content: left; cursor: pointer;">
                          <i class="fad fa-copy" id="btnCopyText" 
                          style="--fa-primary-color: #ac52db; --fa-secondary-color: #ac52db; font-size: 36px;"
                          data-toggle="tooltip" data-placement="bottom" title="Copy to Clipboard">
                        </i>
                        </div>
                      {{/unless}}
                    </div>
                    <div class="row col-4 pt-8">
                      {{#if isMaskedPassword}}
                        <div data-cy="change-password-service-account-btn" class="btn btn-primary font-weight-bolder mr-4" id="btnChangePasswordServiceAccount">Change Password</div>
                      {{else}}
                        <div data-cy="save-service-user-btn" class="btn btn-primary font-weight-bolder mr-4" id="btnSaveServiceAccount">Save</div>
                      {{/if}}
                      <div class="btn btn-secondary font-weight-bolder" id="btnCancelServiceAccount">Cancel</div>
                    </div>
                  </form>
                  <table data-cy="service-account-table" class="table">
                    <tr style="border-bottom:1px solid #000">
                      <th>User Email</th>
                    </tr>
                    {{#each serviceAccounts}}
                      <tr>
                        <td>
                          {{email}}
                        </td>
                        <td>
                          <span data-cy="edit-service-account" class="btnEditServiceAccount text-primary" style="cursor:pointer;" data-id="{{_id}}">Edit</span> |
                          <span data-cy="delete-service-account" class="btnDeleteServiceAccount text-primary" style="cursor:pointer;" data-id="{{_id}}">Delete</span>
                        </td>
                      </tr>
                    {{/each}}
                  </table>
                </div>
                <div class="separator separator-dashed my-8"></div>
              </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
