<template name="designations">
	<div class="row">
		<div class="col-11 offset-1">
			{{# unless showAdd }}
			<div class="col-3">
				<button data-cy="add-designations" class="btn btn-primary mb-4" id="addDesignation">+ Add</button>
			</div>
			{{ /unless }}
			{{# if showAdd }}
			<form id="designationForm" class="form-horizontal">
				<div class="form-group row mt-2">
					<label for="newDesignation" class="col-form-label col-1 text-right">
						Name:
					</label>
					<br>
					<div class="col-4 ml-n4">
						<input data-cy="designation-name" value="" required id="newDesignation" class="form-control form-control-solid designation">
					</div>
				</div>
				<div class="row col-12 mb-4 mt-n2">
					<div class="col-3 ml-4">
						<button data-cy="save-designation" class="btn btn-primary" id="saveDesignation" disabled="{{disabled}}">Save</button>
						<button data-cy="cancel-designation" class="btn btn-secondary" id="cancelAddDesignation">Cancel</button>
					</div>
				</div>
			</form>
			{{ /if }}
			<table class="table">
				<tr style="border-bottom:1px solid #000">
					<th>Name</th>
					<th>Registration URL</th>
				</tr>
				{{#each designation in designations}}
					<tr data-cy="designation-item">
						<td data-cy="designation">
							{{designation}}
						</td>
						<td data-cy="registration-url">
							{{getUrl designation}}
						</td>
					</tr>
				{{/each}}
			</table>
		</div>
	</div>
</template>
