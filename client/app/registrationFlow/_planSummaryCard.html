<template name="planSummaryCard">
    <div data-cy="plan-summaries" class="card registration-card active ml-4 mb-4">
        <div class="card-body">
            <div>
                <h3 data-cy="plan-description"><strong>{{plan.description}}</strong></h3>
            </div>
            {{#if hasProgramDetails plan}}
                {{> programDetails plan=plan}}
            {{/if}}
            <div class="mt-1">
                <h6 data-cy="plan-pricing">Pricing: {{priceRange plan childIndex}}</h6>
            </div>
            {{#if checkIfItemOrPunchcard plan.type}}
                <div class="mb-4">
                    <span>Amount: {{formatCurrency plan.amount}} -- Due Today</span>
                </div>
            {{/if}}
            {{#if isSelectiveWeekPlan plan}}
                <div class="mt-1">
                    <span><strong>Billing Frequency: </strong> Charged weekly based on weeks selected</span>
                </div>
                <div class="mt-1">
                    <span><strong>Service Dates: </strong>{{getServiceDates plan}}</span>
                </div>
                <div class="mt-1">
                    <span><strong>Time: </strong>{{getTime plan}}</span>
                </div>
                <div class="mt-1">
                    <span><strong>Eligible Grades: </strong>{{getGrades plan}}</span>
                </div>
                <div class="mt-1">
                    <span><strong>Registration Period: </strong>{{getRegWindow plan}}</span>
                </div>
            {{/if}}
            {{#if plan.selectedDays}}
                {{#unless isSelectiveWeekPlan plan}}
                <div class="mb-4">
                    <span data-cy="selected-days">{{selectedDaysText plan.selectedDays}}</span>
                </div>
                {{/unless}}
            {{/if}}
            {{#if plan.allocations}}
                {{#each allocation in plan.allocations}}
                    {{> planAllocation allocation=allocation type='card'}}
                {{/each}}
            {{/if}}
            {{#if isSelectiveWeekPlan plan}}
                {{#each weeks plan}}
                    <div class="card week-card my-4">
                        <div class="card-body">
                            <div>
                                <h3><strong>{{../plan.description}} - Week {{getWeekNum this}}</strong></h3>
                            </div>
                            <div class="mt-1">
                                <h6>Pricing: {{priceRange ../plan ../childIndex this}}</h6>
                            </div>
                            <div class="mt-1">
                                <span><strong>Service Dates: </strong>{{formatWeekDates ../plan this}}</span>
                            </div>
                            <div class="mt-1">
                                <span><strong>Time: </strong>{{getTime ../plan}}</span>
                            </div>
                        </div>
                    </div>
                {{/each}}
            {{/if}}
        </div>
    </div>
</template>