import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import moment from 'moment-timezone';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';
import './registrationFlow.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { AsYouType } from "libphonenumber-js";
import { DiscountTypes } from "../../../lib/discountTypes";
import { ITEM_TYPE, PLAN_TYPE, PUNCH_CARD_TYPE } from "../../../lib/constants/billingConstants";
import { MiscUtils } from "../../../lib/util/miscUtils";
import { Log } from '../../../lib/util/log';
import onresize from "resize-event";
import { RegistrationUtils } from '../../../lib/util/registrationUtils';
import { BillingUtils } from '../../../lib/util/billingUtils';
import {AvailableCustomizations} from "../../../lib/customizations";
import logger from "../../../imports/winston";
import './registrationFlowStep1';
import './registrationFlowStep2';
import './registrationFlowStep3';
import './registrationFlowStep4';
import './registrationFlowStep5';
import './_questionStub';


export const selectedPlans = new ReactiveVar([]);
export const validNext = new ReactiveVar(false);

export const isParentEdit = new ReactiveVar(false);

export const primaryContact = new ReactiveVar(new Set());

export const registrationDataSessionName = 'registrationFlowData';

export const couponCodes = 'couponCodes';
export const employeeDiscount = 'employeeDiscount';
export const includeSiblingDiscount = true;
Session.set(includeSiblingDiscount, true);

const registrationValidationFlag = 'registrationFlowValidationFlag';
const maxSteps = 5;

Template.registrationFlow.onCreated(async function () {
    const self = this;
    const orgId = FlowRouter.getQueryParam('orgId');

    // Subscribe to theRegistrationOrg publication so we have access to the org from the client.
    // This could help streamline some of the method calls below. But thats a problem for future Travis.
    this.subscribe('theRegistrationOrg', orgId);

    // Reactive variables
    this.addingChild = new ReactiveVar(false);
    this.addingContact = new ReactiveVar(false);
    this.addingProgram = new ReactiveVar(false);
    this.bundles = new ReactiveVar([]);
    this.contactsSummaryView = new ReactiveVar(false);
    this.currentChildIndex = new ReactiveVar(0);
    this.currentContactIndex = new ReactiveVar(0);
    this.currentStep = new ReactiveVar(1);
    this.editingChild = new ReactiveVar(false);
    this.editingContact = new ReactiveVar(false);
    this.loaded = new ReactiveVar(false);
    this.maxChildIndex = new ReactiveVar(0);
    this.maxContactIndex = new ReactiveVar(Session.get(registrationDataSessionName)?.contacts.length || 0);
    this.regSettings = new ReactiveVar(null);
    this.registrationFee = new ReactiveVar(null);
    this.showPayModal = new ReactiveVar(false);
    this.disabledSubmitBtn = new ReactiveVar(false);
    this.siblingDiscount = new ReactiveVar(null);
    this.topLinePercentDiscounts = new ReactiveVar(false);
    this.orgHasEmployeeIdCustomization = new ReactiveVar(false);
    if (FlowRouter.getQueryParam('step')) {
        this.currentStep.set(FlowRouter.getQueryParam('step'));
    }
    this.personIsAdmin = new ReactiveVar(false);
    this.designation = FlowRouter.getQueryParam('designation') || null;

    if (FlowRouter.getQueryParam('person')) {
        Meteor.callAsync('isPersonAdmin', FlowRouter.getQueryParam('person'), orgId).then((result) => {
            this.personIsAdmin.set(result);
        }).catch((err) => {
            Log.error(err);
        });
    }

    Meteor.callAsync('hasCustomizationByOrgId', orgId, AvailableCustomizations.SHOW_EMPLOYEE_ID_PLR).then((res) => {
        this.orgHasEmployeeIdCustomization.set(res);
    }).catch((err) => {
        Log.error("Error getting org data", err);
        return;
    });

    Meteor.callAsync('getOrgFamilyRegistrationSettings', orgId).then( async (results) => {
       
        if (FlowRouter.getQueryParam('personId')) {
            isParentEdit.set(true);
            Meteor.callAsync('buildExistingFamilyRegistrationData', orgId, FlowRouter.getQueryParam('personId'), true).then((data) => {
                self.regSettings.set(results);
                const existingData = { ...data };
                Session.set(registrationDataSessionName, data);
                RegistrationUtils.setPrimaryContact(data.contacts, primaryContact, self.currentChildIndex.get());
                self.maxContactIndex.set(Number(data.contacts.length ? data.contacts.length - 1 : 0));
                self.currentContactIndex.set(Number(self.maxContactIndex.get()));
                self.currentStep.set(1);
                self.loaded.set(true);
                setTimeout(() => {
                    validNext.set(checkValidity(self));
                }, 1000);
                setTimeout(() => {
                    const element = document.querySelector('.registration-flow-wrapper');
                    const observer = onresize(element, () => {
                        MiscUtils.sendHeight();
                    });
                }, 200);
            }).catch((e) => {
                if (e) {
                    if (e.reason) {
                        mpSwal.fire("Error", `${e.reason} ${e.details}`, "error");
                    } else {
                        mpSwal.fire("Error", `${e.message}`, "error");
                    }
                }
            });
        } else if (FlowRouter.getQueryParam('reregistrationfamilyid')) {
            /* TODO:
            1. check if redirect for registration from adv dashboard; if so, set contactsOnly to false
            2. Get list of available plans (preferably from the server method).  
            3. Add selected plans to the session data matching child array index
            4. set current step to 5 (eventuall set to 6 for filling in missing fields)
            5. step 6 should redirect to 5
            */
            isParentEdit.set(true);
            const data = await Meteor.callAsync('buildExistingFamilyRegistrationData', orgId, FlowRouter.getQueryParam('reregistrationfamilyid'), false);
            self.regSettings.set(data);
            const originalData = { ...data };
            console.log("originalData 1", originalData);
            const passedInChildren = FlowRouter.getQueryParam('reregistrationchildids') ? FlowRouter.getQueryParam('reregistrationchildids').split(',') : [];
            console.log("filtering children", passedInChildren);
            const updatingData = { ...originalData,
                children: originalData.children.filter((child) => passedInChildren.includes(child._id) )
            };
            
            const orgInfo = await Meteor.callAsync('getOrgInformation', orgId, true, this.designation);
            const {plans} = orgInfo;
            const selectedPlan = plans.filter((plan) => plan._id === FlowRouter.getQueryParam('reregistrationplanid'));
            updatingData.plans = Array(updatingData.children.length).fill(selectedPlan);

            console.log("updatingData 2", updatingData);
            Session.set(registrationDataSessionName, updatingData);
            self.currentStep.set(5);
            self.loaded.set(true);
            
            

            setTimeout(() => {
                validNext.set(checkValidity(self));
            }, 1000);
            setTimeout(() => {
                const element = document.querySelector('.registration-flow-wrapper');
                const observer = onresize(element, () => {
                    MiscUtils.sendHeight();
                });
            }, 200);
            
        } else {
            const sessionData = Session.get(registrationDataSessionName);
            if (sessionData && sessionData.contacts) {
                RegistrationUtils.setPrimaryContact(sessionData.contacts, primaryContact, self.currentChildIndex.get());
            }
            this.regSettings.set(results);
            self.loaded.set(true);
            setTimeout(() => {
                validNext.set(checkValidity(self));
            }, 1000);
            setTimeout(() => {
                const element = document.querySelector('.registration-flow-wrapper');
                const observer = onresize(element, () => {
                    MiscUtils.sendHeight();
                });
            }, 200);
        }
    }).catch((err) => {
        console.log("error in getOrgFamilyRegistrationSettings", err);
    });

    Meteor.callAsync('getOrgBundles', orgId, true, this.designation).then((result) => {
        this.bundles.set(result ?? []);
    }).catch((err) => {
        console.log("error in getOrgBundles", err);
    });
    Meteor.callAsync('getSiblingDiscount', orgId).then((result) => {
        this.siblingDiscount.set(result);
    }).catch((err) => {
        console.log("error in getSiblingDiscount", err);
    });
    Meteor.callAsync('getTopLinePercentDiscounts', orgId).then((result) => {
        this.topLinePercentDiscounts.set(result);
    }).catch((err) => {
        console.log("error in getTopLinePercentDiscounts", err);
    });
    Meteor.callAsync('getRegistrationFeeItem', orgId).then((result) => {
        this.registrationFee.set(result);
    }).catch((err) => {
        console.log("error in getRegistrationFeeItem", err);
    });

    this.totalCharges = new ReactiveVar(0);
    Tracker.autorun(async () => {
        const savedData = Session.get(registrationDataSessionName);
        const totalCharges = await RegistrationUtils.totalCharges(savedData, FlowRouter.getQueryParam('orgId'), true, true);
        this.totalCharges.set(totalCharges);
    });
});

Template.registrationFlow.rendered = function () {
    document.querySelector('body').style.backgroundColor = 'white';
    $.getScript("https://checkoutshopper-live-us.adyen.com/checkoutshopper/sdk/3.12.1/adyen.js");
}

Template.registrationFlow.helpers({
    disabledSubmitButtons: () => {
        return (Template.instance().disabledSubmitBtn.get() || !!Template.instance().showPayModal.get());
    },
    invalid: () => {
        return Template.instance().loaded.get() && !Template.instance().regSettings.get();
    },
    currentStep: () => {
        return Template.instance().currentStep.get() || 1;
    },
    currentTemplate: () => {
        const step = Template.instance().currentStep.get() || 1;
        return 'registrationFlowStep' + step;
    },
    showBack: () => {
        const isModifyingChildFamily = Template.instance().addingChild.get() || Template.instance().editingChild.get();
        const step = Template.instance().currentStep.get() || 1;
        return  step > 1 || isModifyingChildFamily;
    },
    showNext: () => {
        const step = Template.instance().currentStep.get() || 1;
        return step < maxSteps;
    },
    showSubmit: () => {
        const step = Template.instance().currentStep.get() || 1;
        return step === maxSteps;
    },
    owesNothing: () => {
        const savedData = Session.get(registrationDataSessionName);
        return savedData && Template.instance().totalCharges.get() === 0;
    },
    settings: () => {
        return Template.instance().regSettings.get();
    },
    questions: () => {
        const settings = Template.instance().regSettings.get();
        return settings?.questions || [];
    },
    data: () => {
        const settings = Template.instance().regSettings.get();
        return {
            contactsSummaryView: Template.instance().contactsSummaryView.get(),
            currentChildIndex: Template.instance().currentChildIndex.get(),
            currentContactIndex: Template.instance().currentContactIndex.get(),
            addingContact: Template.instance().addingContact.get(),
            editingContact: Template.instance().editingContact.get(),
            currentStep: Template.instance().currentStep.get(),
            questions: settings?.questions || [],
            regSettings: settings,
            orgHasEmployeeIdCustomization: Template.instance().orgHasEmployeeIdCustomization.get(),
            requiredContactsCount: settings?.requiredContactsCount || 0,
            savedData: Session.get(registrationDataSessionName),
            showPayModal: Template.instance().showPayModal,
            disabledSubmitBtn: Template.instance().disabledSubmitBtn
        }
    },
    nextDisabled: () => {
        return !validNext.get() ? 'disabled' : '';
    },
    isLoading: () => {
        return !Template.instance().loaded.get();
    },
    showPayLater: () => {
        return Template.instance().personIsAdmin.get();
    }
});

Template.registrationFlow.events({
    "blur .email": function (e, instance) {
        validNext.set(checkValidity(instance));
    },
    "input": function (e, instance) {
        validNext.set(checkValidity(instance));
    },
    'focus input': (event, instance) => {
        validNext.set(checkValidity(instance));
    },
    'change select': (event, instance) => {
        setTimeout(() => {
            // In cases where the last question in the initial registration flow
            // is a conditional we need to wait 100ms because we conditionally render
            // the subsequent path (yes or no).
            validNext.set(checkValidity(instance));
        }, 100);
    },
    'focus select': (event, instance) => {
        validNext.set(checkValidity(instance));
    },
    "click .plan-card": function (e, instance) {
        if (instance.currentStep.get() === 3) {
            validNext.set(checkValidity(instance));
        }
    },
    "click #registrationFlowNext": function (e, instance) {
        if (!validNext.get()) {
            focusAll(instance);
        }
        if (!checkValidity(instance)) {
            return;
        }
        e.target.blur();
        const contactsSummaryView = instance.contactsSummaryView.get();
        storeData(instance);
        if(instance.currentStep.get() === 1 && isParentEdit.get()) {
            // move directly to contact summary if being edited by parents.
            instance.contactsSummaryView.set(true);
        }

        if (!contactsSummaryView && instance.currentStep.get() === 2) {
            // Show contacts summary screen; don't go to next step
            return;
        }
        if (instance.currentStep.get() === 3) {
            const currentData = Session.get(registrationDataSessionName) || {};
            const childIdx = instance.currentChildIndex.get();
            currentData.plans = currentData.plans || [];
            currentData.plans[childIdx] = currentData.plans[childIdx] || [];
            const plansWithoutSelectiveWeeks = currentData.plans[childIdx].filter(p => p.type === PLAN_TYPE && (!p.details?.selectiveWeeks || p.details?.selectiveWeeks.length === 0));
            // If user doesn't choose any plan type or only plans with selective weeks, skip to next step
            if (!plansWithoutSelectiveWeeks.length) {
                instance.currentStep.set(5);
                instance.addingContact.set(false);
                instance.editingContact.set(false);
                return;
            }
        }
        instance.currentStep.set(Number(instance.currentStep.get()) + 1);
        if (instance.currentStep.get() === 5) {
            instance.addingProgram.set(false);
            instance.addingChild.set(false);
            instance.editingChild.set(false);
        }
        instance.addingContact.set(false);
        instance.editingContact.set(false);
        logger.info("registrationFlowNext | Continue button", {"Session Data": Session.get(registrationDataSessionName)});
        logger.info("Current step", {"Current step": instance.currentStep.get()});
    },
    "click #registrationFlowBack": function (e, instance) {
        if (instance.editingChild.get() && instance.currentStep.get() === 1) {
            instance.currentStep.set(5);
            instance.editingChild.set(false);
            return;
        }
        if (instance.addingChild.get() && instance.currentStep.get() === 1) {
            instance.currentStep.set(5);
            instance.addingChild.set(false);
            removeUnfinishedChildData(instance);
            return;
        }
        if (instance.currentStep.get() === 2 && (instance.editingContact.get() || instance.addingContact.get())) {
            const currentData = Session.get(registrationDataSessionName) || {}
            if (currentData.contacts && currentData.contacts.length) {
                RegistrationUtils.setPrimaryContact(currentData.contacts, primaryContact, instance.currentChildIndex.get());
            }
        }
        if (instance.currentStep.get() === 2 && instance.addingContact.get()) {
            instance.addingContact.set(false);
            instance.contactsSummaryView.set(true);
            instance.maxContactIndex.set(Number(instance.maxContactIndex.get()) - 1);
            instance.currentContactIndex.set(Number(instance.maxContactIndex.get()));
            return;
        }
        if (instance.currentStep.get() === 2 && instance.editingContact.get()) {
            instance.editingContact.set(false);
            instance.contactsSummaryView.set(true);
            return;
        }
        if (instance.addingProgram.get() && instance.currentStep.get() === 3) {
            instance.currentStep.set(5);
            instance.addingProgram.set(false);
            removeUnfinishedPlans(instance);
            return;
        }
        if (instance.currentStep.get() === 5) {
            const currentData = Session.get(registrationDataSessionName) || {};
            const childIdx = instance.currentChildIndex.get();
            currentData.plans = currentData.plans || [];
            currentData.plans[childIdx] = currentData.plans[childIdx] || [];
            const plansWithoutSelectiveWeeks = currentData.plans[childIdx].filter(p => p.type === PLAN_TYPE && (!p.details?.selectiveWeeks || p.details?.selectiveWeeks.length === 0));
            // If user doesn't choose any plan type
            if (!plansWithoutSelectiveWeeks.length) {
                instance.currentStep.set(3);
                return;
            }
        }
        instance.currentStep.set(Number(instance.currentStep.get()) - 1);
        logger.info("registrationFlowBack | Back button", {"Session Data": Session.get(registrationDataSessionName)});
        logger.info("Current step", {"Current step": instance.currentStep.get()});
    },
    'click #addContact': function (e, instance) {
        e.preventDefault();
        instance.maxContactIndex.set(Number(instance.maxContactIndex.get()) + 1);
        instance.currentContactIndex.set(Number(instance.maxContactIndex.get()));
        instance.addingContact.set(true);
        instance.contactsSummaryView.set(false);
    },
    'click .contact-summary-row': function (e, instance) {
        const contactIndex = e.target.getAttribute('data-id');
        instance.currentContactIndex.set(Number(contactIndex));
        instance.contactsSummaryView.set(false);
        instance.editingContact.set(true);
        setTimeout(() => {
            validNext.set(checkValidity(instance));
        }, 100);
    },
    'click #addChild': function (e, instance) {
        e.preventDefault();
        const currentChildrenNumber = Session.get(registrationDataSessionName).children.length - 1;
        instance.maxChildIndex.set(Number(currentChildrenNumber) + 1);
        instance.currentChildIndex.set(Number(instance.maxChildIndex.get()));
        instance.addingChild.set(true);
        instance.currentStep.set(1);
    },
    'click #editChild': function (e, instance) {
        e.preventDefault();
        const childIndex = (Number(e.target.getAttribute('data-id')));
        instance.currentChildIndex.set(childIndex);
        instance.editingChild.set(true);
        instance.currentStep.set(1);
    },
    'click #addProgram': function (e, instance) {
        e.preventDefault();
        instance.currentChildIndex.set(Number(e.target.getAttribute('data-id')));
        instance.addingProgram.set(true);
        instance.currentStep.set(3);
    },
    'click #registrationFlowSubmit': async function (e, instance) {
        e.preventDefault();
        if (instance.disabledSubmitBtn.get()) {
            return;
        }
        if (!validNext.get()) {
            focusAll(instance);
        }
        instance.disabledSubmitBtn.set(true)
        const savedData = Session.get(registrationDataSessionName);
        savedData.couponCodes = Session.get(couponCodes) || [];
        const owesNothing = savedData && await RegistrationUtils.totalCharges(savedData, FlowRouter.getQueryParam('orgId'), true, true) === 0;
        if (owesNothing) {
            // We want the update path in order to bypass email checking.
            if (isParentEdit.get()) {
                Meteor.callAsync('updateFamily', savedData).then((res) => {
                    mpSwal.fire({
                        text: 'Family updated!',
                        allowOutsideClick: false
                    }).then(result => { if (result.isConfirmed) window.close() });
                }).catch((err) => {
                    if (err.reason) {
                        mpSwal.fire("Error", `${err.reason} ${err.details}`, "error").then(result => { if (result.isConfirmed) window.close() });
                    } else {
                        mpSwal.fire("Error", err.message, "error").then(result => { if (result.isConfirmed) window.close() });
                    }
                }).finally(() => {
                    instance.disabledSubmitBtn.set(false)
                });
            } else {
                logger.info("registrationFlowSubmit > approveRegistration", {"Saved Data": savedData});
                Meteor.callAsync('approveRegistration', savedData, null, FlowRouter.getQueryParam('orgId'), true).then((result) => {
                    Meteor.callAsync('formatMessage', result).then((res) => {
                        for (const o of res) {
                            parent.postMessage({
                                name: o.action,
                                detail: o
                            }, '*');
                        }
                    }).catch((err) => {
                        console.log("error in formatMessage", err);
                    });
                    FlowRouter.go(`/registration-completed?orgId=${FlowRouter.getQueryParam('orgId')}`);
                }).catch((error) => {
                    if (error.reason) {
                        mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
                    } else {
                        mpSwal.fire("Error", `${error.message}`, "error");
                    }
                }).finally(() => {
                    instance.disabledSubmitBtn.set(false)
                });
            }
        } else {
            instance.showPayModal.set(true);
        }
    },
    'click #btnPayLater': function (e, instance) {
        const regData = Session.get(registrationDataSessionName) || {};
        Log.info(regData, 'regData')
        e.currentTarget.disabled = true;
        regData.couponCodes = Session.get(couponCodes) || [];
        regData.payLater = true;
        logger.info("registrationFlow > click #btnPayLater", {"Org": FlowRouter.getQueryParam('orgId'), "Saved Data": regData});
        Meteor.callAsync('approveRegistration',  regData, null, FlowRouter.getQueryParam('orgId'), true).then((result) => {
            Meteor.callAsync('formatMessage', result).then((res) => {
                for (const o of res) {
                    parent.postMessage({
                        name: o.action,
                        detail: o
                    }, '*');
                }
            }).catch((err) => {
                console.log("error in formatMessage", err);
            });
            FlowRouter.go(`/registration-completed?orgId=${FlowRouter.getQueryParam('orgId')}`);
        }).catch((error) => {
            if (error.reason) {
                mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
            } else {
                mpSwal.fire("Error", `${error.message}`, "error");
            }
        });
    },
})

export function checkValidity(instance, wrapperSelector = '.registration-flow-wrapper') {
    let valid = true;
    const isMainRegistrationFlow = wrapperSelector === '.registration-flow-wrapper';
    const questionNodes = document.querySelectorAll(`${wrapperSelector} .form-control`) || [];
    for (const node of questionNodes) {
        if (node.dataset && node.dataset.required && !node.value.trim()) {
            valid = false;
            break;
        }
    }
    const singleCheckboxes = document.querySelectorAll(`${wrapperSelector} .checkbox-single`);
    for (const node of singleCheckboxes) {
        if (node.dataset.required && !node.checked) {
            valid = false;
            break;
        }
    }
    const multiSelects = document.querySelectorAll(`${wrapperSelector} .select-multi-group`);
    for (const nodeGroup of multiSelects) {
        if (nodeGroup.dataset.required) {
            const checked = nodeGroup.querySelectorAll('.select-multi-option:checked');
            if (checked.length === 0) {
                valid = false;
                break;
            }
        }
    }
    const phoneInputs = document.querySelectorAll(`${wrapperSelector} .phone`);
    for (const node of phoneInputs) {
        if (node.value) {
            const ayt = new AsYouType('US');
            ayt.input(node.value);
            // use isPossible rather than isValid to allow for test data
            if (!ayt.getNumber() || !ayt.getNumber().isPossible()) {
                valid = false;
                break;
            }
        }
    }
    const emailInputs = document.querySelectorAll(`${wrapperSelector} .email`);
    for (const node of emailInputs) {
        if (node.value && node.classList.contains('is-invalid')) {
            valid = false;
            break;
        }

        // Get all contactEmails, and remove duplicates
        const contactEmails = [...new Set(Session.get(registrationDataSessionName)?.contacts?.map(contact => contact.profileEmailAddress))] || [];
        if (contactEmails) {
            contactEmails.forEach((email, index) => {
                if (email.trim().toLowerCase() === node.value.trim().toLowerCase() && index !== instance.currentContactIndex.get()) {
                    valid = false;
                }
            })
        }
    }
    const dateInputs = document.querySelectorAll(`${wrapperSelector} .date-picker`);
    for (const node of dateInputs) {
        const pattern = new RegExp(/^(0[1-9]|1[0-2])\/(0[1-9]|1\d|2\d|3[01])\/(19|20)\d{2}$/);
        if (!node.value.trim().length || !pattern.test(node.value.trim())) {
            valid = false;
            break;
        }
        if (node.value.trim().length > 0 && !pattern.test(node.value.trim())) {
            valid = false;
            break;
        }
        if (node.name === 'birthday') {
            const today = moment().startOf('day').valueOf();
            if (moment(node.value, 'MM/DD/YYYY').valueOf() > today) {
                valid = false;
                break;
            }
        }
    }

    if (isMainRegistrationFlow && instance.currentStep.get() === 2 && instance.contactsSummaryView.get()) {
        const currentData = Session.get(registrationDataSessionName) || {};
        let requiredContactsCount = parseInt(instance.regSettings.get()?.requiredContactsCount) ?? 0;
        const matchedContacts = (currentData.contacts || []).filter(contact => {
            return contact.emergencyContact.toLowerCase() === 'yes' ||
                contact.authorizedPickup.toLowerCase() === 'yes'
        });
        if (matchedContacts.length < requiredContactsCount) {
            valid = false;
        }

        if (!RegistrationUtils.doesSessionHavePrimaryContacts()) {
            valid = false;
        }
    }

    if (isMainRegistrationFlow && instance.currentStep.get() === 3 && !selectedPlans.get().length) {
        valid = false;
    }

    if (!isMainRegistrationFlow && instance.currentStep.get() === 1 && !getInstanceVariable(instance, 'selectedPlans').get().length) {
        valid = false;
    }
    if ((isMainRegistrationFlow && instance.currentStep.get() === 4) || (!isMainRegistrationFlow && instance.currentStep.get() === 2)) {
        const plans = isMainRegistrationFlow ? selectedPlans.get() : getInstanceVariable(instance, 'selectedPlans').get();
        for (const plan of plans.filter(plan => ![ITEM_TYPE, PUNCH_CARD_TYPE].includes(plan.type))) {
            const planErrorMessageNode = document.getElementById(`validate-days-${plan._id}`) ?? null;

            // If any node has an error message visible do not continue.
            if (planErrorMessageNode && planErrorMessageNode.style.display === 'block' && planErrorMessageNode.innerText !== "") {
                valid = false;
                break;
            }

            const validReactiveVar = isMainRegistrationFlow ? validNext : getInstanceVariable(instance, 'validNext');
            // Validation of the days and the value of validNext are calculated in the daySelection template before this check is run.
            valid = validReactiveVar.get();

            if (valid) {
                if (planErrorMessageNode) {
                    planErrorMessageNode.style.display = 'none';
                }
            } else {
                if (planErrorMessageNode) {
                    planErrorMessageNode.style.display = 'block';
                }
            }
        }
    }
    return valid;
}


function getInstanceVariable(instance, variableName) {
    if (instance[variableName]) {
        return instance[variableName];
    } else if (instance.data && instance.data[variableName]) {
        return instance.data[variableName];
    }
    return undefined;
}


function removeUnfinishedPlans(instance) {
    const currentData = Session.get(registrationDataSessionName) || {};
    const childIdx = instance.currentChildIndex.get();
    currentData.plans = currentData.plans || [];
    currentData.plans[childIdx] = currentData.plans[childIdx] || [];
    currentData.plans[childIdx] = currentData.plans[childIdx].filter(plan => plan.selectedDays && plan.selectedDays.length > 0 && (plan.startDate || plan.selectedWeeks.length));
    Session.set(registrationDataSessionName, currentData);
}

function removeUnfinishedChildData(instance) {
    const currentData = Session.get(registrationDataSessionName) || {};
    const childIdx = instance.currentChildIndex.get();
    currentData.children = currentData.children || [];
    currentData.children[childIdx] = currentData.children[childIdx] || {};
    if (!currentData.plans[childIdx] || !currentData.plans[childIdx].length) {
        currentData.children.splice(childIdx, 1);
        currentData.contacts = currentData.contacts.filter(contact => contact.childIndex !== childIdx);
        updateCorrectCurrentChildrenIndex(instance, currentData);
    }
    else {
        if (_.find(currentData.plans[childIdx], plan => !plan.selectedDays || !plan.selectedDays.length || !plan.startDate)) {
            currentData.children.splice(childIdx, 1);
            currentData.contacts = currentData.contacts.filter(contact => contact.childIndex !== childIdx);
            currentData.plans.splice(childIdx, 1);
            updateCorrectCurrentChildrenIndex(instance, currentData);
        }
    }

}

function updateCorrectCurrentChildrenIndex(instance, currentData) {
    Session.set(registrationDataSessionName, currentData);
    instance.maxChildIndex.set(Number(instance.maxChildIndex.get()) - 1);
    instance.currentChildIndex.set(Number(instance.maxChildIndex.get()));
}

function storeData(instance) {
    const currentData = Session.get(registrationDataSessionName) || {};

    const childIdx = instance.currentChildIndex.get();
    const contactIdx = instance.currentContactIndex.get();
    const currentStep = instance.currentStep.get();
    const contactsSummaryView = instance.contactsSummaryView.get();

    // This step adds the children data to the currentData object.
    if (currentStep === 1) {
        currentData.children = currentData.children || [];
        currentData.children[childIdx] = currentData.children[childIdx] || {};
        if (FlowRouter.getQueryParam('homeSchool')) {
            currentData.children[childIdx].homeSchool = FlowRouter.getQueryParam('homeSchool');
        }
        const questionNodes = document.querySelector('.registration-flow-wrapper')?.querySelectorAll('.form-control') || [];
        const resultingYesConditionals = [];
        const resultingNoConditionals = [];
        for (const node of questionNodes) {
            // See if the node has "conditional-select". If it does, we need
            // to see which answer was chosen. We will use that instead of the
            // "is-nested" name/value, which gets skipped.
            if (node.classList.contains('conditional-select')) {
                // We check for an existence of a form-control with a class of `${NAME_OF_THIS_NODE}-result`.
                // If this item exists, we get the name and value and put it on the child.
                // If it doesn't exist, we skip.
                // We may want to add an error if it doesn't exist, but that's the quick
                // way for us right now to work with "noFollowUp".
                // Note ... if a user selects "Yes", saves, goes back, changes to "No", saves,
                // they will have both "Yes" and "No" path values on the document.
                // TODO: See if this is expected? Remove the bad one here based on
                // what the expected "No" and "Yes" property mappings are?
                const resultNode = document.querySelector(`.registration-flow-wrapper .${node.name}-result`);
                if (resultNode) {
                    currentData.children[childIdx][resultNode.name] = resultNode.value.trim();
                }
                // TODO: Instead of the name should we have a data attribute here?
                if (node.value.trim().toLowerCase() === 'yes') {
                    resultingYesConditionals.push(node.name);
                } else if (node.value.trim().toLowerCase() === 'no') {
                    resultingNoConditionals.push(node.name);
                }
            } else if (node.classList.contains('is-nested')) {
                continue;
            } else {
                currentData.children[childIdx][node.name] = node.value.trim();
            }
        }
        const singleCheckboxes = document.querySelectorAll('.registration-flow-wrapper .checkbox-single');
        for (const node of singleCheckboxes) {
            currentData.children[childIdx][node.name] = node.checked ? 'Yes' : 'No';
        }
        const multiSelects = document.querySelectorAll('.registration-flow-wrapper .select-multi-group');
        for (const nodeGroup of multiSelects) {
            const checked = Array.from(nodeGroup.querySelectorAll('.select-multi-option:checked'), n => n.value);
            currentData.children[childIdx][nodeGroup.dataset.name] = checked.join(',');
        }
        // Save the "Yes" and "No" conditionals
        currentData.children[childIdx].registrationConditionals = {
            yes: resultingYesConditionals,
            no: resultingNoConditionals
        }
    }

    // This step adds the contact data to the currentData object.
    if (currentStep === 2 && !contactsSummaryView) {
        const questionNodes = document.querySelector('.registration-flow-wrapper')?.querySelectorAll('.form-control') || [];
        const singleCheckboxes = document.querySelectorAll('.registration-flow-wrapper .checkbox-single');
        let contactEmail = ''
        let stepTwoContactIdx = contactIdx;
        currentData.contacts = currentData.contacts || [];

        // Get contact email from the form
        for(const node of questionNodes){
            if(node.name === 'profileEmailAddress') {
                contactEmail = node.value
            }
        }

        if (instance.editingContact.get()) {
            stepTwoContactIdx = contactIdx;
        } else {
            const newContactIndex = RegistrationUtils.findIndexOfMatchingContactByEmailAndIndex(currentData.contacts, contactEmail, childIdx);
            if (newContactIndex !== -1) {
                // Found matching contact, change that contact rather than create a new one
                stepTwoContactIdx = newContactIndex
            } else if (currentData.contacts === undefined || currentData.contacts.length === 0) {
                // No contacts exist, create the first one
                stepTwoContactIdx = 0
            } else {
                // Contact exists but for another child, create new contact
                stepTwoContactIdx = currentData.contacts.length
            }
        }

        currentData.contacts[stepTwoContactIdx] = currentData.contacts[stepTwoContactIdx] || {};

        // Update every single contact that matches to have the same data
        for (const node of questionNodes) {
            currentData.contacts[stepTwoContactIdx][node.name] = node.value

            // Update every single contact that matches to have the same data
            currentData.contacts.forEach((contact, index) => {
                if (contact.profileEmailAddress === contactEmail) {
                    contact[node.name] = node.value;
                }
            });
        }

        for (const node of singleCheckboxes) {
            currentData.contacts[stepTwoContactIdx][node.name] = node.checked ? 'Yes' : 'No';
        }
        // Don't overwrite the child index on the contact
        if (!('childIndex' in currentData.contacts[stepTwoContactIdx])) {
            currentData.contacts[stepTwoContactIdx].childIndex = childIdx;
        }

        // Change all other contacts with the same email to false for value 'copyToChildren'
        currentData.contacts.forEach((contact, index) => {
            if (contact.profileEmailAddress === contactEmail && contact.childIndex !== childIdx) {
                contact['copyToChildren'] = 'No';
            }
        });

        // Set the view to summary view
        instance.contactsSummaryView.set(true);
    }

    // This is where we add / edit plans
    if (currentStep === 3) {
        currentData.plans = currentData.plans || [];
        currentData.plans[childIdx] = currentData.plans[childIdx] || [];
        const mappedSelectPlans = _.map(selectedPlans.get(), plan => plan._id);

        //remove plans that were deselected since last save
        currentData.plans[childIdx] = currentData.plans[childIdx].filter(p => mappedSelectPlans.indexOf(p._id) !== -1)

        //add plans that were selected since last save
        _.forEach(selectedPlans.get(), plan => {
            const foundPlan = currentData.plans[childIdx].find(p => p._id === plan._id);
            // Update weeks if plan is a selective weekly plan
            if (foundPlan && foundPlan.details?.selectiveWeeks?.length) {
                foundPlan.selectedWeeks = plan.selectedWeeks;
            }

            // If no plan found, add it to array.
            if (!foundPlan) {
                if (!plan.selectedDays && plan.type !== 'item') {
                    plan.selectedDays = RegistrationUtils.setDefaultSelectedDaysByPlanConfig(plan);

                    // Set the correct default scaled price when days length isn't the full week.
                    if (plan.scaledAmounts?.length && plan.scaledAmounts.length > plan.selectedDays.length) {
                        plan.amount = plan.scaledAmounts[plan.selectedDays.length - 1];
                    }
                }
                currentData.plans[childIdx].push(plan);
            }
        })

        if (!RegistrationUtils.getPlansExcludingSelectiveWeeks(currentData.plans[childIdx]).length) {
            applyExistingAllocationsToNewPlans(instance, currentData.plans);
            _.each(currentData.plans[childIdx], plan => {
                plan.planTotal = plan.amount;
            });
        }
        if (!currentData.registrationFee && instance.registrationFee.get()) {
            currentData.registrationFee = instance.registrationFee.get();
            currentData.registrationFee.planTotal = currentData.registrationFee.amount || 0;
            applyExistingAllocationsToRegistrationFee(currentData.registrationFee);
        }
    }

    // This is where we add any discounts from bundles
    if (currentStep === 4) {
        _.forEach(selectedPlans.get(), plan => {
            const foundPlan = _.find(currentData.plans[childIdx], p => p._id === plan._id);
            if (!foundPlan) {
                currentData.plans[childIdx].push(plan);
            } else {
                currentData.plans[childIdx] = currentData.plans[childIdx].map(p => {
                    if (p._id === plan._id) {
                        return plan;
                    }
                    return p;
                });
            }
        });

        applyExistingAllocationsToNewPlans(instance, currentData.plans);
        if (isParentEdit.get()) {
            findAllocations(currentData.plans);
        }
        const bundlesApplied = [];
        const plans = currentData.plans[childIdx].filter(plan => plan.type !== ITEM_TYPE && plan.type !== PUNCH_CARD_TYPE && (!plan.details?.selectiveWeeks || plan.details?.selectiveWeeks.length === 0));
        plans.forEach(plan => {
            RegistrationUtils.filterBundleDiscountsWithoutRequiredPlans(plans, plan, instance.bundles.get())

            const formData = $(`#${plan._id}`).serializeArray();
            const daysSelected = _.filter(formData, input => input.value === 'on').map(day => day.name);
            plan.selectedDays = daysSelected;
            plan.startDate = _.find(formData, input => input.name === 'startDate').value;
            // Handle bundle discount
            if (plan.bundlePlanId && (bundlesApplied.includes(plan.bundlePlanId) || bundlesApplied.length === 0)) {
                const bundle = instance.bundles.get().find(b => b._id === plan.bundlePlanId);
                if (((plan.allocations && !plan.allocations.find(a => a.discountType === DiscountTypes.BUNDLE)) || !plan.allocations)) {
                    plan.allocations = plan.allocations || [];
                    plan.allocations.push({
                        allocationType: "discount",
                        amount: (plan.regularPrice - plan.bundlePlanPrice) / 2,
                        amountType: 'dollars',
                        discountType: DiscountTypes.BUNDLE,
                        allocationDescription: bundle ? `Bundle: ${bundle.description}` : ''
                    })
                }
                if (plan.allocations && plan.allocations.find(a => a.discountType === DiscountTypes.BUNDLE)) {
                    plan.allocations = plan.allocations || [];
                    plan.allocations.map(a => {
                        if (a.discountType === DiscountTypes.BUNDLE) {
                            a.amount = (plan.regularPrice - plan.bundlePlanPrice) / 2;
                        }
                        return a;
                    })
                }
                bundlesApplied.push(plan.bundlePlanId);
            } else if (plan.scaledAmounts?.length) {
                plan.amount = plan.scaledAmounts[(plan.selectedDays?.length || 5) - 1];
            }
        });

        _.each(currentData.plans[childIdx], plan => {
            plan.planTotal = plan.amount;
        });
    }

    if (currentData.plans) {
        currentData.plans.forEach((plans, index) => {
            const startDates = plans.map(plan => {
                if (plan.details?.selectiveWeeks?.length && plan.selectedWeeks?.length) {
                    return plan.selectedWeeks.map(weekIndex =>
                        moment(plan.details.selectiveWeeks[weekIndex][0], "MM/DD/YYYY")
                    );
                } else if (plan.startDate) {
                    return [moment(plan.startDate, "MM/DD/YYYY")];
                }
                return [];
            }).flat();

            const earliestDate = moment.min(startDates);
            if (earliestDate.isValid()) {
                currentData.children[index]["enrollmentDate"] = earliestDate.format("MM/DD/YYYY");
            }
        });
    }


    Session.set(registrationDataSessionName, currentData);
}

export function applyExistingAllocationsToNewPlans(instance, currentPlans) {
    const cachedCoupons = Session.get(couponCodes) || [];
    const cachedEmployeeDiscount = Session.get(employeeDiscount);

    // Keeps track of if the sibling discount has been removed during the registration process, as to not be re-added again
    const shouldIncludeSiblingDiscount = Session.get(includeSiblingDiscount);
    if (!currentPlans || !currentPlans.length) {
        return;
    }

    // First, add sibling discounts
    let childIndex = 0;
    for (const childPlans of currentPlans) {
        for (const plan of childPlans) {
            // Check if plan is in a bundle, if so, override reg fee and sibling discount 
            for (const bundle of instance.bundles.get()) {
                if (plan.bundlePlanId === bundle._id) {
                    // Set plan registration fee and discount equal to bundle
                    plan.regFeeExempt = bundle.regFeeExempt
                    plan.siblingDiscountExempt = bundle.siblingDiscountExempt
                }
            }
            const siblingDiscountCondition = instance.siblingDiscount.get() && (instance.addingChild.get() || instance.addingProgram.get() || instance.editingChild.get()) && childIndex > 0 && !plan.siblingDiscountExempt && shouldIncludeSiblingDiscount

            // If first child, remove sibling discount from allocations
            if (childIndex === 0 && plan?.allocations?.length) {
                plan.allocations = plan.allocations.filter(allocation => allocation.discountType !== 'siblingDiscount');
            }

            //If no discount has been applied add everything that is satisfied
            if (!plan.allocations || !plan.allocations.length) {
                plan.allocations = [];
                if (siblingDiscountCondition) {
                    const siblingDiscount = instance.siblingDiscount.get();
                    plan.allocations.push({
                        id: Random.id(),
                        allocationType: "discount",
                        amount: siblingDiscount.amount,
                        amountType: siblingDiscount.amountType,
                        discountType: DiscountTypes.SIBLING,
                        allocationDescription: siblingDiscount.description || 'Sibling Discount'
                    });
                }
            }
            else {
                if (siblingDiscountCondition) {
                    // Only add sibling discount if they don't have one already
                    const siblingDiscount = instance.siblingDiscount.get();
                    if (!plan.allocations.find(a => a.discountType === DiscountTypes.SIBLING)) {
                        const sumOfDollarDiscounts = _.chain(plan.allocations)
                            .filter((a) => { return a.amountType === "dollars"; })
                            .reduce((memo, a) => { return memo + a.amount; }, 0.0)
                            .value();
                        const index = plan.allocations.findIndex(a => !!a.code && cachedCoupons.find(c => c.code === a.code && !c.usedWithDiscounts));
                        if (index > -1) {
                            let discountAmount = 0;
                            if (plan.allocations[index].amountType === "dollars") {
                                discountAmount = plan.allocations[index].amount;
                            }
                            else {
                                discountAmount = BillingUtils.roundToTwo((plan.amount - (instance.topLinePercentDiscounts.get() ? 0 : sumOfDollarDiscounts)) * plan.allocations[index].amount / 100.0);
                            }
                            if (discountAmount < 0) discountAmount = 0;

                            let siblingDiscountAmount = 0;
                            if (siblingDiscount.amountType === "dollars") {
                                siblingDiscountAmount = siblingDiscount.amount;
                            }
                            else {
                                siblingDiscountAmount = BillingUtils.roundToTwo((plan.amount - (instance.topLinePercentDiscounts.get() ? 0 : sumOfDollarDiscounts)) * siblingDiscount.amount / 100.0);
                            }
                            if (siblingDiscountAmount < 0) siblingDiscountAmount = 0;
                            if (siblingDiscountAmount >= discountAmount) {
                                plan.allocations[index] = {
                                    id: Random.id(),
                                    allocationType: "discount",
                                    amount: siblingDiscount.amount,
                                    amountType: siblingDiscount.amountType,
                                    discountType: DiscountTypes.SIBLING,
                                    allocationDescription: siblingDiscount.description || 'Sibling Discount'
                                }
                            }
                        }
                        else {
                            plan.allocations.push({
                                id: Random.id(),
                                allocationType: "discount",
                                amount: siblingDiscount.amount,
                                amountType: siblingDiscount.amountType,
                                discountType: DiscountTypes.SIBLING,
                                allocationDescription: siblingDiscount.description || 'Sibling Discount'
                            });
                        }
                    }
                }
            }
        }
        childIndex++;
    }

    // Now, add coupons
    for (const [indexChild, childPlans] of currentPlans.entries()) {
        for (const [indexPlan, plan] of childPlans.entries()) {
            cachedCoupons.forEach(coupon => {
                const allocation = RegistrationUtils.createAllocationObjectFromCoupon(coupon);
                const currentData = Session.get(registrationDataSessionName)
                currentPlans[indexChild][indexPlan] = RegistrationUtils.removeCouponAllocationsFromPlan(plan, coupon)
                RegistrationUtils.addAllocationCouponDiscount(currentData.children, currentPlans, coupon, allocation, true)
            })

            // Add employee discounts if applicable and no other discount is applied
            if (!plan.allocations || !plan.allocations.length) {
                plan.allocations = [];
                if (cachedEmployeeDiscount) {
                    const allocation = {
                        id: Random.id(),
                        allocationType: "discount",
                        amount: cachedEmployeeDiscount.amount,
                        amountType: cachedEmployeeDiscount.amountType,
                        discountType: cachedEmployeeDiscount.type,
                        allocationDescription: cachedEmployeeDiscount.description
                    };
                    plan.allocations.push(_.clone(allocation));
                }
            }
        }
    }
}

export function applyExistingAllocationsToRegistrationFee(registrationFee) {
    const cachedCoupons = Session.get(couponCodes) || [];
    if (!registrationFee.allocations || !registrationFee.allocations.length) {
        registrationFee.allocations = [];
        if (cachedCoupons && cachedCoupons.length) {
            for (const coupon of cachedCoupons) {
                const allocation = {
                    id: Random.id(),
                    allocationType: "discount",
                    amount: coupon.amount,
                    amountType: coupon.amountType,
                    discountType: DiscountTypes.COUPON,
                    code: coupon.code,
                    allocationDescription: coupon.description || `Discount: Coupon Code ${coupon.code}`
                };
                if (coupon.oneTimeCharges && coupon.oneTimeCharges.length) {
                    if (!coupon.oneTimeCharges.includes(registrationFee._id)) {
                        continue;
                    }
                }
                registrationFee.allocations.push(_.clone(allocation));
            }
        }
    }
}

export function findAllocations(currentPlans, isMainRegistration = true, instance = null) {
    const savedPlans = isMainRegistration ? selectedPlans.get() : instance.selectedPlans.get();
    const newChildPlans = savedPlans.filter(plan => !plan.createdAt && plan.type !== 'item');
    let existingPlans = [];
    // find existing allocations family-wide that everyone has, and add it to the plan.
    for (const plans of currentPlans) {
        existingPlans = [...existingPlans, ...plans.filter(plan => plan.createdAt && plan.type !== 'item')];
    }
    if (existingPlans.length > 0) {
        let sharedAllocations = [];
        let allocationsSet = false;
        for (const plan of existingPlans) {
            if (!plan.allocations) {
                break;
            }
            if (sharedAllocations.length === 0 && !allocationsSet) {
                sharedAllocations = _.clone(plan.allocations.filter(allocation => allocation.discountType !== DiscountTypes.BUNDLE && allocation.discountType !== DiscountTypes.COUPON && allocation.allocationType !== 'reimbursable' && allocation.allocationType !== 'reimbursable-with-copay'));
                allocationsSet = true;
                continue;
            }
            if (sharedAllocations.length === 0 && allocationsSet) {
                break;
            }
            for (const allocation of sharedAllocations) {
                let included = false;
                for (const pa of plan.allocations) {
                    if (pa.amount === allocation.amount && pa.allocationType === allocation.allocationType && pa.description === allocation.description) {
                        included = true;
                    }
                }
                if (!included) {
                    sharedAllocations.splice(sharedAllocations.indexOf(allocation), 1);
                }
            }
        }
        if (sharedAllocations.length) {
            for (const allocation of sharedAllocations) {
                let included = false;
                for (const plan of newChildPlans) {
                    for (const al of plan.allocations) {
                        if (al.amount === allocation.amount && al.allocationType === allocation.allocationType && al.description === allocation.description) {
                            included = true;
                        }
                    }
                    if (!included) {
                        plan.allocations.push(allocation);
                    }
                }
            }
        }
    }
}

export function checkForCoupons(data) {
    const foundCoupons = []
    const currentPlans = data.plans || [];
    const appliedCoupons = Session.get(couponCodes) || [];
    for (const childPlans of data.plans) {
        for (const plan of childPlans) {
            if (plan.createdAt) continue;
            for (const allocation of plan.allocations) {
                if (allocation.discountType === DiscountTypes.COUPON && !foundCoupons.includes(allocation.code)) {
                    foundCoupons.push(allocation.code);
                    const options = {
                        couponCode: allocation.code,
                        orgId: FlowRouter.getQueryParam('orgId') ?? Orgs.current()?._id,
                        cachedCoupons: appliedCoupons,
                        cartPlans: currentPlans,
                        employeeDiscount: !!Session.get(employeeDiscount)
                    }

                    if(FlowRouter.getQueryParam('personId')) {
                        options.parentId = FlowRouter.getQueryParam('personId');
                    }

                    Meteor.callAsync("couponValidation", options).then((result) => {
                        const currentCoupons = Session.get(couponCodes) || [];
                        if (!currentCoupons.find(c => c._id === result._id)) {
                            currentCoupons.push(result);
                        }
                        Session.set(couponCodes, currentCoupons);
                    }).catch((err) => {
                        console.log("error in couponValidation", err);
                    });
                }
            }
        }
    }
}

export function focusAll(instance, wrapperSelector = '.registration-flow-wrapper') {
    const currentStep = instance.currentStep.get();
    const questionNodes = document.querySelectorAll(`${wrapperSelector} .form-control`);
    const singleCheckboxes = document.querySelectorAll(`${wrapperSelector} .checkbox-single`);
    const multiSelects = document.querySelectorAll(`${wrapperSelector} .select-multi-group`);
    const nodes = [...questionNodes, ...singleCheckboxes, ...multiSelects];
    for (const node of nodes) {
        if (node.dataset && node.dataset.required) {
            if (wrapperSelector === '.registration-flow-wrapper' && (currentStep === 1 || currentStep === 2)) {
                const name = node.dataset.name || node.name;
                if (node.classList.contains('not-validated') && !node.value.trim() > 0) {
                    node.classList.add('is-invalid');
                    node.classList.remove('border-0', 'border-right-0');
                    $(`#validate${name}`).show();
                }
            }

            if (node.id === 'relationship-group' && node.querySelectorAll('.select-multi-option:checked').length === 0) {
                $('#relationship-group-validation').show();
            }

            if ((wrapperSelector === '.registration-flow-wrapper' && currentStep === 4) || currentStep === 2) {
                $(`#validate-${node.dataset.type}-${node.dataset.id}`).show();
            }
        }
    }
}

