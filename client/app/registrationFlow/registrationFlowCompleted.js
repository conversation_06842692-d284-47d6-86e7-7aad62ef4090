import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import './registrationFlowCompleted.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {Log} from "../../../lib/util/log";

Template.registrationFlowCompleted.created = function() {
    this.onHold = new ReactiveVar(!!FlowRouter.getQueryParam('onHold'));
    this.districtEmail = new ReactiveVar(!!FlowRouter.getQueryParam('districtEmail'));

    this.orgLongName = new ReactiveVar('');
    const orgId = FlowRouter.getQueryParam('orgId')
    Meteor.callAsync('getOrgLongName', orgId).then((result) => {
        if (result) {
            this.orgLongName.set(result);
        }
    }).catch((error) => {
        mpSwal.fire('Error', error.reason || error.message, 'error');
        Log.error(error);
    });
};

Template.registrationFlowCompleted.helpers({
    message() {
        return Template.instance().onHold.get() ?
            (Template.instance().districtEmail.get() ? 'Thank you for submitting your registration. Since you indicated that you are a district employee, we are verifying your email address. A member of our team will reach out within a few days to confirm your registration.' : 'Thank you for submitting your registration. Since you indicated that you receive assistance from a subsidy program, we are verifying your eligibility. A member of our team will reach out within a few days to confirm your registration.') :
            `Thank you for registering! You will receive an account activation email shortly. Please click the “Verify My Email” button in that email to finish setting up your ${Template.instance().orgLongName.get()} account.`;
    }
});
