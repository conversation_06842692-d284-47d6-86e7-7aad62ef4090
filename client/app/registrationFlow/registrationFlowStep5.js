import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import _ from '../../../lib/util/underscore';
import './registrationFlowStep5.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {
    registrationDataSessionName,
    couponCodes,
    employeeDiscount,
    isParentEdit,
    selectedPlans,
    includeSiblingDiscount
} from './registrationFlow';
import { DiscountTypes } from "../../../lib/discountTypes";
import { ITEM_TYPE, PLAN_TYPE, PUNCH_CARD_TYPE } from "../../../lib/constants/billingConstants";
import { AvailableCustomizations } from "../../../lib/customizations";
import { MiscUtils } from "../../../lib/util/miscUtils";
import { BillingUtils } from '../../../lib/util/billingUtils';
import { RegistrationUtils, RegistrationFlowType } from '../../../lib/util/registrationUtils';
import { Log } from '../../../lib/util/log';
import { cloneDeep } from 'lodash';
import { CouponUtils } from "../../../lib/util/couponUtils";
import logger from '../../../imports/winston/index'
import '../billing/_feeVerbiage';
import './registrationFlowStep5.html';
import './_planSummaryCard';
import '../components/addDiscountDropdown/addDiscountDropdown';
import './_planAllocation';
import '../../layout/loading';
import './_registrationPaymentMethods.html';
import { hideModal, showModal } from '../main';
import { RegistrationFlowUtils } from './registrationFlowUtils/registrationFlowUtils';

function disabledClick(e) {
    e.preventDefault();
}

Template.registrationFlowStep5.created = async function () {
    const self = this;
    self.availablePrograms = new ReactiveVar([]);
    self.availablePlans = new ReactiveVar([]);
    self.availableTimePeriods = new ReactiveVar([]);
    self.availableBillingFrequencies = new ReactiveVar([]);
    self.topLinePercentDiscounts = new ReactiveVar(false);
    self.dueTodayCharges = new ReactiveVar(0);
    self.futureCharges = new ReactiveVar(0);
    self.registrationFeeAmountDue = new ReactiveVar(0);
    self.orgLongName = new ReactiveVar(null);
    self.bundles = new ReactiveVar([]);
    self.orgTimezone = new ReactiveVar('America/New_York');
    self.designation = FlowRouter.getQueryParam('designation') || null;
    self.showEmployeeId = new ReactiveVar(false);

    const currentData = Session.get(registrationDataSessionName) || {};
    currentData.designation = self.designation;
    Session.set(registrationDataSessionName, currentData);

    const orgId = FlowRouter.getQueryParam('orgId');
    Meteor.callAsync('getOrgInformation', orgId, true, self.designation).then((result) => {
        if (result) {
            logger.info('registrationFlowStep5 > created > Org information retrieved', { orgData: result });
            const { programs, plans, timezone, bundles, longName, timePeriods, frequencies, showEmployeeId } = result;
            self.orgTimezone.set(timezone);
            self.availableTimePeriods.set(timePeriods);
            self.availableBillingFrequencies.set(frequencies);
            self.availablePrograms.set(programs);
            self.availablePlans.set(plans);
            self.bundles.set(bundles);
            self.orgLongName.set(longName);
            self.showEmployeeId.set(showEmployeeId);
        }
    }).catch((error) => {
        if (error.reason) {
            mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
        } else {
            mpSwal.fire("Error", error.message, "error");
        }
        logger.error('registrationFlowStep5 > created > Error retrieving org information', { error: error });
        Log.error(error);
    });

    Meteor.callAsync('getTopLinePercentDiscounts', orgId).then((result) => {
        if (result) {
            logger.info('registrationFlowStep5 > created > Top line percent discounts retrieved', { topLinePercentDiscounts: result });
            self.topLinePercentDiscounts.set(result);

            const currentData = Session.get(registrationDataSessionName) || {};
            calculateIndividualPlanDiscountAndTotal(currentData, result);
            logger.info('registrationFlowStep5 > created > Individual plan discounts calculated', { currentData: currentData });
            Session.set(registrationDataSessionName, currentData);
        }
    }).catch((err) => {
        logger.error('registrationFlowStep5 > created > Error retrieving top line percent discounts', { error: err });
        return; // Handle the error as needed
    });

    self.totalCharges = new ReactiveVar(0);
    Tracker.autorun(async () => {
        const registrationData = Session.get(registrationDataSessionName);
        const orgId = FlowRouter.getQueryParam('orgId');
        const totalCharges = await RegistrationUtils.totalCharges(registrationData, orgId, false);
        self.totalCharges.set(totalCharges);
    });
};

Template.registrationFlowStep5.onRendered(function () {
    const currentData = Session.get(registrationDataSessionName) || {};
    const toplinePercentDiscounts = this.topLinePercentDiscounts.get();

    calculateIndividualPlanDiscountAndTotal(currentData, toplinePercentDiscounts);
    logger.info('registrationFlowStep5 > onRendered > Individual plan discounts calculated', { currentData });

    Session.set(registrationDataSessionName, currentData);
    const orgId = FlowRouter.getQueryParam('orgId');
    Session.set('summaryLoaded', false);

    Meteor.callAsync('getRegistrationChargesDueToday', currentData, orgId).then((results) => {
        logger.info('registrationFlowStep5 > onRendered > getRegistrationChargesDueToday', { results });
        this.dueTodayCharges.set(results);
        Meteor.callAsync('getRegistrationFeesDue', currentData, orgId).then((res) => {
            this.registrationFeeAmountDue.set(res.fee);
            setRegistrationFeeAmountDueInSession(currentData, res); 
        }).catch((err) => {
            logger.error('registrationFlowStep5 > onRendered > getRegistrationFeesDue error', { error: err });
            if (err.reason) {
                mpSwal.fire("Error", `${err.reason} ${err.details}`, "error");
            } else {
                mpSwal.fire("Error", err.message, "error");
            }
        }).finally(() => {
            Session.set('summaryLoaded', true);
        });
    }).catch((error) => {
        logger.error('registrationFlowStep5 > onRendered > Error retrieving registration charges due today', { error });
        if (error.reason) {
            mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
        } else {
            mpSwal.fire("Error", error.message, "error");
        }
        Session.set('summaryLoaded', true);
    });
});

Template.registrationFlowStep5.helpers({
    isLoaded: () => {
        return Session.get('summaryLoaded');
    },
    children:() => {
        let children = Session.get(registrationDataSessionName).children || [];
        return children.map(child => ({
            ...children,
            initials: RegistrationUtils.childInitials(child),
            fullName: RegistrationUtils.childFullName(child)
        }))
    },
    programs: (childIndex) => {
        const programs = Object.keys(RegistrationUtils.childProgramPlans(childIndex, Session.get(registrationDataSessionName)));
        return (Template.instance().availablePrograms.get() ?? []).filter(p => programs.includes(p._id));
    },
    plans: (childIndex, program) => {
        return RegistrationUtils.childProgramPlans(childIndex, Session.get(registrationDataSessionName))[program];
    },
    showEmployeeId() {
        return Template.instance().showEmployeeId.get();
    },
    hasEmployeeDiscount() {
        const currentEmployeeDiscount = Session.get(employeeDiscount);
        return !!currentEmployeeDiscount;
    },
    employeeId() {
        return Session.get(employeeDiscount)?.employeeId || '';
    },
    getOrgLongName() {
        return Template.instance().orgLongName.get();
    },
    getRegistrationFeeAmount() {
        const totalAmount = Template.instance().registrationFeeAmountDue.get();
        logger.debug('registrationFlowStep5 > getRegistrationFeeAmount: Total Amount Retrieved', { totalAmount });

        if (!totalAmount) {
            logger.info('registrationFlowStep5 > getRegistrationFeeAmount: Total amount is not available, returning "N/A"');
            return 'N/A';
        }

        const currentData = Session.get(registrationDataSessionName);

        const normalAmount = currentData?.registrationFee?.amount ?? 0;
        const discountedAmount = currentData?.registrationFee?.planTotal ?? 0;

        const registrationFee = MiscUtils.formatCurrency(totalAmount / discountedAmount * normalAmount);
        logger.info('registrationFlowStep5 > getRegistrationFeeAmount: Calculated Registration Fee Amount', { registrationFee });

        return registrationFee;
    },
    showRegistrationFeeDiscountsAndTotal() {
        const currentData = Session.get(registrationDataSessionName);
        return currentData.registrationFee && currentData.registrationFee.allocations && currentData.registrationFee.allocations.length;
    },
    registrationAllocations() {
        const currentData = Session.get(registrationDataSessionName);
        return currentData.registrationFee && currentData.registrationFee.allocations && currentData.registrationFee.allocations.length ? currentData.registrationFee.allocations : [];
    },
    getRegistrationFeeTotal() {
        const amountDue = Template.instance().registrationFeeAmountDue.get();
        return amountDue ? MiscUtils.formatCurrency(amountDue) : 'N/A';
    },
    summaryChildrenPlans() {
        return Session.get(registrationDataSessionName).plans || []
    },
    summaryChildFullName(index) {
        const children = Session.get(registrationDataSessionName).children || [];
        return RegistrationUtils.childFullName(children[index]);
    },
    summaryProgramName(programId) {
        const availablePrograms = Template.instance().availablePrograms.get() ?? [];
        const currentProgram = availablePrograms.find(p => p._id === programId)
        if (currentProgram) {
            return currentProgram.name;
        }
        return '';
    },
    totalCharges() {
        const range = RegistrationUtils.totalRange(Session.get(registrationDataSessionName)); // Selective week price ranges if applicable.
        const amount = Template.instance().totalCharges.get();
        const regFee = Template.instance().registrationFeeAmountDue.get() ?? 0;

        if (range) {
            if (range.min === range.max) {
                const formattedRange = `${MiscUtils.formatCurrency(range.min + amount + regFee)}`;
                logger.info('registrationFlowStep5 > totalCharges > range.min === range.max', { formattedRange });
                return `${formattedRange}`;
            } else {
                const formattedRange = `${MiscUtils.formatCurrency(range.min + amount + regFee)} - ${MiscUtils.formatCurrency(range.max + amount + regFee)}`;
                logger.info('registrationFlowStep5 > totalCharges > range.min !== range.max', { formattedRange });
                return `${formattedRange}`;
            }
        } else {
            const formattedAmount = `${MiscUtils.formatCurrency(amount + regFee)}`;
            logger.info('registrationFlowStep5 > totalCharges > !range', { formattedAmount });
            return `${formattedAmount}`;
        }
    },
    showPayModal() {
        if (Template.instance().data.showPayModal.get()) {
            logger.info('registrationFlowStep5 > showPayModal: Showing pay modal');
            const showPayModal = Template.instance().data.showPayModal;
            const disabledSubmitBtn = Template.instance().data.disabledSubmitBtn;
            const currentData = Session.get(registrationDataSessionName);
            const amount = Template.instance().dueTodayCharges.get();

            logger.debug('registrationFlowStep5 > showPayModal: Current Data:', { currentData });
            logger.debug('registrationFlowStep5 > showPayModal: Due Today Charges Amount:', { amount });

            // Create a skeleton of the first guardian to use for Adyen
            if (!currentData.contacts[0]._id && !isParentEdit.get()) {
                logger.info('registrationFlowStep5 > showPayModal: Creating placeholder guardian');

                Meteor.callAsync('createPlaceholderGuardian', FlowRouter.getQueryParam('orgId'), currentData).then((results) => {
                    currentData.contacts[0]._id = results;
                    Session.set(registrationDataSessionName, currentData);
                    logger.info('registrationFlowStep5 > showPayModal: Placeholder guardian created', { 'New Placeholder Guardian': currentData.contacts[0] });

                    Meteor.callAsync('getOrgPaymentInformation', FlowRouter.getQueryParam('orgId')).then((r) => {
                        logger.info('registrationFlowStep5 > showPayModal: Successfully retrieved organization payment information', { orgData: r });

                        // Show payment modal
                        const data = {
                            personId: results,
                            orgData: Object.assign({ _id: FlowRouter.getQueryParam('orgId') }, r),
                            amount: amount,
                            showPayModal: showPayModal,
                            disabledSubmitBtn: disabledSubmitBtn
                        };
                        showModal('registrationFlowStep5PaymentModal', data, '#registrationFlowStep5PaymentModal');
                        logger.info('registrationFlowStep5 > showPayModal: Payment modal displayed', { data });
                    }).catch((e) => {
                        if (e.reason) {
                            mpSwal.fire("Error", `${e.reason} ${e.details}`, "error");
                        } else {
                            mpSwal.fire("Error", e.message, "error");
                        }
                        logger.error('registrationFlowStep5 > showPayModal: Error retrieving organization payment information after creating placeholder guardian', { error: e });
                    });
                }).catch((err) => {
                    if (err.reason) {
                        mpSwal.fire("Error", `${err.reason} ${err.details}`, "error");
                    } else {
                        mpSwal.fire("Error", err.message, "error");
                    }
                    logger.error('registrationFlowStep5 > showPayModal: Error creating placeholder guardian', { error: err });
                });
            } else {
                logger.info('registrationFlowStep5 > showPayModal: No placeholder guardian needed; retrieving organization payment information');

                Meteor.callAsync('getOrgPaymentInformation', FlowRouter.getQueryParam('orgId')).then((r) => {
                    logger.info('registrationFlowStep5 > showPayModal: Successfully retrieved organization payment information', { orgData: r });

                    // Show payment modal
                    const data = {
                        personId: FlowRouter.getQueryParam('personId') || currentData.contacts[0]._id,
                        orgData: Object.assign({ _id: FlowRouter.getQueryParam('orgId') }, r),
                        amount: amount,
                        showPayModal: showPayModal,
                        disabledSubmitBtn: disabledSubmitBtn
                    };
                    showModal('registrationFlowStep5PaymentModal', data, '#registrationFlowStep5PaymentModal');
                    logger.info('registrationFlowStep5 > showPayModal: Payment modal displayed', { data });
                }).catch((e) => {
                    if (e.reason) {
                        mpSwal.fire("Error", `${e.reason} ${e.details}`, "error");
                    } else {
                        mpSwal.fire("Error", e.message, "error");
                    }
                    logger.error('registrationFlowStep5 > showPayModal: Error retrieving organization payment information', { error: e });
                });
            }
        }
    },
    dueTodayCharges() {
        return Template.instance().dueTodayCharges.get();
    },
    getFutureDue() {
        return RegistrationUtils.dueLater(Session.get(registrationDataSessionName));
    },
    getPlanType(plan) {
        if (plan.type === ITEM_TYPE || plan.type === PUNCH_CARD_TYPE) {
            return 'Item';
        }
        return 'Plan';
    },
    whiteLabelLogoOverride: function () {
        const host = window.location.host.split(".")[0];
        const enabledSites = Meteor.settings.public.whitelabel && Meteor.settings.public.whitelabel.enabled_sites;
        const meteorUser = Meteor.user(), currentOrg = meteorUser && meteorUser.fetchOrg();
        if (currentOrg && _.deep(currentOrg, "whiteLabel.assets.appLogo")) {
            return {
                small: currentOrg.whiteLabel.assets.appLogoSmall,
                large: currentOrg.whiteLabel.assets.appLogo
            };
        } else if (enabledSites && enabledSites.indexOf(host) > -1) {
            return {
                small: Meteor.settings.public.whitelabel[host].small_logo,
                large: Meteor.settings.public.whitelabel[host].large_logo
            }
        }
    },
    parentData: function() {
        return {
            currentData: Session.get(registrationDataSessionName) || {},
            availablePlans: Template.instance().availablePlans.get(),
            availablePrograms: Template.instance().availablePrograms.get(),
            timePeriods: Template.instance().availableTimePeriods.get(),
            frequencies: Template.instance().availableBillingFrequencies.get(),
            orgTimezone: Template.instance().orgTimezone.get(),
            availableBundles: Template.instance().bundles.get(),
        }
    },
    getPlanOriginalPrice(plan) {
        return RegistrationUtils.getPlanAmount(plan)
    },
    getPlanDiscountedPrice(plan) {
        return RegistrationUtils.getPlanDiscountedAmount(plan)
    },
    getRegSettings() {
        return Template.instance().data?.regSettings ?? {};
    },
});

Template.registrationFlowStep5.events({
    "click #addCouponCode": function (e, i) {
        e.preventDefault();
        showModal("registrationFlowStep5AddCouponModal", {
            savedData: Session.get(registrationDataSessionName),
            topLinePercentDiscounts: Template.instance().topLinePercentDiscounts.get(),
            dueTodayCharges: Template.instance().dueTodayCharges,
            registrationFeeAmountDue: Template.instance().registrationFeeAmountDue
        }, "#registrationFlowStep5AddCouponModal");
    },
    "click #addEmployeeId": function (e, i) {
        e.preventDefault();
        showModal("registrationFlowStep5AddEditEmployeeIdModal", {
            savedData: Session.get(registrationDataSessionName),
            orgLongName: Template.instance().orgLongName.get(),
            topLinePercentDiscounts: Template.instance().topLinePercentDiscounts.get(),
            isEdit: false,
            dueTodayCharges: Template.instance().dueTodayCharges,
            registrationFeeAmountDue: Template.instance().registrationFeeAmountDue
        }, "#registrationFlowStep5AddEditEmployeeIdModal");
    },
    "click #employeeIdLink": function (e, i) {
        e.preventDefault();
        showModal("registrationFlowStep5AddEditEmployeeIdModal", {
            isEdit: true,
            dueTodayCharges: Template.instance().dueTodayCharges,
            registrationFeeAmountDue: Template.instance().registrationFeeAmountDue
        }, "#registrationFlowStep5AddEditEmployeeIdModal");
    },
    "click #deleteEmployeeDiscount": function (e, i) {
        e.preventDefault();
        const popUpOptions = {
            title: "You are about to waive your employee discount. Are you sure?",
            showCancelButton: true,
        };
        const currentData = Session.get(registrationDataSessionName);
        const topLinePercentDiscounts = Template.instance().data.topLinePercentDiscounts;
        mpSwal.fire(popUpOptions).then(result => {
            if (result.value) {
                Session.set(employeeDiscount, undefined);
                removeEmployeeDiscountFromAllocations(currentData);
                calculateIndividualPlanDiscountAndTotal(currentData, topLinePercentDiscounts);
                Session.set(registrationDataSessionName, currentData);
                // Re-calculate charges due after removing employee discount
                recalculateChargesDue(i, currentData, FlowRouter.getQueryParam('orgId'));
            }
        });
    },
    "click #addSubsidy": function (e, i) {
        e.preventDefault();
        RegistrationFlowUtils.addSubsidyModal(Session.get(registrationDataSessionName), Template.instance().topLinePercentDiscounts.get(), RegistrationFlowType.PLR, undefined)
    },
    "click #addDistrictEmail": function (e, i) {
        e.preventDefault();
        RegistrationFlowUtils.addDistrictEmployeeEmailModal(Session.get(registrationDataSessionName), Template.instance().topLinePercentDiscounts.get(), RegistrationFlowType.PLR, undefined)
    },
});

Template.registrationFlowStep5AddCouponModal.created = function () {
    this.disabledSubmitBtn = new ReactiveVar(true);
    this.bundles = new ReactiveVar([]);
    Meteor.callAsync('getOrgBundles', FlowRouter.getQueryParam('orgId')).then((result) => {
        this.bundles.set(result ?? []);
    }).catch((err) => {
        console.log("error in getOrgBundles", err);
    });
};

Template.registrationFlowStep5AddCouponModal.helpers({
    submitButtonDisabled() {
        return Template.instance().disabledSubmitBtn.get();
    },
});

Template.registrationFlowStep5AddCouponModal.events({
    "input input[name='code']": function (e, i) {
        i.disabledSubmitBtn.set(!e.target.value);
    },
    "click #applyCoupon": function (e, i) {
        const couponValue = $("input[name='code']").val().trim().toUpperCase();
        logger.debug('registrationFlowStep5 > click #applyCoupon: Coupon Value Retrieved', { couponValue });

        const appliedCoupons = Session.get(couponCodes) || [];
        const appliedCouponsSet = new Set(_.map(appliedCoupons, coupon => coupon.code));

        const currentData = Session.get(registrationDataSessionName) || {};
        const currentPlans = currentData.plans || [];
        const newCoupon = appliedCoupons.find(coupon => coupon.code === couponValue) || {};
        const hasSiblingDiscount = CouponUtils.cartHasSiblingDiscount(currentPlans);

        // If a user enters a coupon that does not stack with discounts, and a discount has been applied, then allow to pass without showing an error
        const isCouponAlreadyApplied = appliedCouponsSet.has(couponValue);
        const cannotUseWithDiscounts = !newCoupon.usedWithDiscounts && hasSiblingDiscount;
        const shouldShowError = isCouponAlreadyApplied && !cannotUseWithDiscounts;

        if (shouldShowError) {
            mpSwal.fire({ icon: "error", title: 'This coupon has already been applied', text: 'Please use another coupon.' });
            return;
        }

        const currentChildren = currentData.children || [];
        const topLinePercentDiscounts = Template.instance().data.topLinePercentDiscounts;

        const options = {
            couponCode: couponValue,
            orgId: FlowRouter.getQueryParam('orgId'),
            cachedCoupons: appliedCoupons,
            cartPlans: currentPlans,
            data: currentData,
            employeeDiscount: !!Session.get(employeeDiscount)
        };

        if (FlowRouter.getQueryParam('personId')) {
            options.parentId = FlowRouter.getQueryParam('personId');
        }

        logger.debug('registrationFlowStep5 > click #applyCoupon: Options Prepared for Coupon Validation', { options });

        Meteor.callAsync("couponValidation", options).then((result) => {
            const coupon = result;
            logger.info('registrationFlowStep5 > click #applyCoupon: Coupon Validation Successful', { coupon });

            const allocation = RegistrationUtils.createAllocationObjectFromCoupon(coupon);
            RegistrationUtils.addAllocationCouponDiscount(currentChildren, currentPlans, coupon, allocation);

            const registrationFee = currentData.registrationFee;
            if (registrationFee) {
                registrationFee.allocations = registrationFee.allocations || [];
                if (coupon.oneTimeCharges && coupon.oneTimeCharges.length) {
                    if (coupon.oneTimeCharges.includes(registrationFee._id)) {
                        registrationFee.allocations.push(_.clone(allocation));
                    }
                } else {
                    registrationFee.allocations.push(_.clone(allocation));
                }
            }
            // Create a shallow copy of appliedCoupons and add the allocation object
            const updatedAppliedCoupons = [...appliedCoupons, coupon];
            applyDiscountChanges(i, currentData, topLinePercentDiscounts, updatedAppliedCoupons);

            const wasApplied = RegistrationUtils.wasCouponApplied(currentData, coupon);
            if (wasApplied) {
                mpSwal.fire({ icon: "success", title: `Coupon ${ coupon.code } successfully applied.` });
            }

            hideModal('#registrationFlowStep5AddCouponModal');
        }).catch((error) => {
            const hasRegFeesAllocations = currentData.registrationFee?.allocations?.length > 0;

            if (["MR-000005", "MR-000006", "MR-000007"].includes(error?.details?.errorCode)) {
                logger.info('registrationFlowStep5 > click #applyCoupon: Specific Error Code Encountered', { errorCode: error.details.errorCode });
                hideModal('#registrationFlowStep5AddCouponModal');
                const errorReason = RegistrationUtils.getErrorText(error.details, hasRegFeesAllocations);
                const additionalInfo = error.details.additionalInfo
                const { newCoupon } = additionalInfo;
                logger.debug('registrationFlowStep5 > click #applyCoupon: New Coupon Details', { newCoupon });

                const allocation = {
                    id: Random.id(),
                    allocationType: "discount",
                    amount: newCoupon.amount,
                    amountType: newCoupon.amountType,
                    discountType: DiscountTypes.COUPON,
                    code: newCoupon.code,
                    allocationDescription: newCoupon.description || `Discount: Coupon Code ${newCoupon.code}`
                }
                logger.debug('registrationFlowStep5 > click #applyCoupon: Created Allocation', { allocation });

                const initialSiblingDiscountStatus = Session.get(includeSiblingDiscount);
                logger.debug('registrationFlowStep5 > click #applyCoupon: Initial Sibling Discount Status', { initialSiblingDiscountStatus });

                // Calculate potential new totals with the new coupon
                // Temporary copiedCartPlansList is used in case the user doesn't want to use their new coupon assuming conflictions
                const {
                    newGrandTotal,
                    newTotalSavings,
                    newCartPlansList
                } = applyNewCouponAndCalculateSavings(options.cartPlans, additionalInfo, currentChildren, allocation, error.details);
                logger.info('registrationFlowStep5 > click #applyCoupon: New Totals Calculated', { "New Grand Total": newGrandTotal, "New Total Savings": newTotalSavings });

                const {
                    currentGrandTotal,
                    currentTotalSavings,
                    currentCartPlansList
                } = currentPlansWithPotentialCouponsOrDiscounts(currentPlans, currentChildren, newCoupon, allocation, error.details);
                logger.info('registrationFlowStep5 > click #applyCoupon: Current Totals Calculated', { "Current Grand Total": currentGrandTotal, "Current Total Savings": currentTotalSavings });

                const currentAllocations = getUniqueAllocations(currentCartPlansList);
                const newAllocations = getUniqueAllocations(newCartPlansList);
                logger.debug('registrationFlowStep5 > click #applyCoupon: Allocations', { "Current allocations": currentAllocations, "New allocations": newAllocations });

                mpSwal.fire({
                    title: "Compare Discounts",
                    width: '45%',
                    maxWidth: '600px',
                    minWidth: '300px',
                    html: `
                        <div style="padding: 10px;">
                            <p style="margin-bottom: 20px;">${errorReason}</p>
                            <div style="display: flex; flex-direction: row; gap: 20px; align-items: flex-start;">
                                <!-- Keep Selected Section -->
                                <div id="keep-selected" style="cursor: pointer; flex: 1; border: 1px solid #ccc; border-radius: 8px; padding: 15px; background-color: #f9f9f9; transition: background-color 0.3s;">
                                    <h3 style="margin-top: 0; text-align: center;">Keep Selected</h3>
                                    <p><strong>Current Discount:</strong> $${currentTotalSavings} off</p>
                                    <p><strong>Price with Current Discount:</strong> $${currentGrandTotal}</p>
                                    <hr style="border: none; border-top: 1px solid #eee; margin: 15px 0;">
                                    <p style="text-align: left; margin-bottom: 5px;"><strong>Discounts:</strong></p>
                                    <ul style="list-style-type: disc; padding-left: 20px; margin: 0; text-align: left;">
                                        ${currentAllocations.map(code => `<li style="font-size: 0.9em; margin-bottom: 3px;">${code}</li>`).join('')}
                                    </ul>
                                </div>
                                <!-- Switch to New Section -->
                                <div id="switch-to-new" style="cursor: pointer; flex: 1; border: 1px solid #ccc; border-radius: 8px; padding: 15px; background-color: #f9f9f9; transition: background-color 0.3s;">
                                    <h3 style="margin-top: 0; text-align: center;">Switch to New</h3>
                                    <p><strong>New Discount:</strong> $${newTotalSavings} off</p>
                                    <p><strong>Price with New Discount:</strong> $${newGrandTotal}</p>
                                    <hr style="border: none; border-top: 1px solid #eee; margin: 15px 0;">
                                    <p style="text-align: left; margin-bottom: 5px;"><strong>Discounts:</strong></p>
                                    <ul style="list-style-type: disc; padding-left: 20px; margin: 0; text-align: left;">
                                        ${newAllocations.map(code => `<li style="font-size: 0.9em; margin-bottom: 3px;">${code}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `,
                    showCancelButton: false,
                    showConfirmButton: false,
                    showCloseButton: true,
                    didOpen: () => {
                        const keepSelected = document.getElementById('keep-selected');
                        const switchToNew = document.getElementById('switch-to-new');

                        // Find the close button and add the data-cy tag
                        const closeButton = document.querySelector('.swal2-close');
                        if (closeButton) {
                            closeButton.setAttribute('data-cy', 'compare-modal-close-button');
                        }

                        keepSelected.addEventListener('click', () => {
                            mpSwal.clickConfirm();  // Simulate a click on the confirm button
                        });

                        switchToNew.addEventListener('click', () => {
                            mpSwal.clickCancel();  // Simulate a click on the cancel button
                        });

                        // Add hover effect
                        keepSelected.addEventListener('mouseover', () => {
                            keepSelected.style.backgroundColor = '#e0e0e0';
                        });
                        keepSelected.addEventListener('mouseout', () => {
                            keepSelected.style.backgroundColor = '#f9f9f9';
                        });

                        switchToNew.addEventListener('mouseover', () => {
                            switchToNew.style.backgroundColor = '#e0e0e0';
                        });
                        switchToNew.addEventListener('mouseout', () => {
                            switchToNew.style.backgroundColor = '#f9f9f9';
                        });
                    }
                }).then((result) => {
                    logger.debug('registrationFlowStep5 > click #applyCoupon: Modal Result', { result });
                    if (['backdrop', 'esc', 'close'].includes(result.dismiss)) {
                        // User clicked outside the modal, hit escape, or closed the modal
                        logger.info('registrationFlowStep5 > click #applyCoupon: User Dismissed Modal');
                        Session.set(includeSiblingDiscount, initialSiblingDiscountStatus);
                        return;
                    }

                    if (result.isConfirmed) { // "Keep Selected" is chosen
                        logger.info('registrationFlowStep5 > click #applyCoupon: User Chose to Keep Selected Discounts');
                        if (error.details.errorCode === "MR-000005") {
                            // Update the appliedCoupons to only include the coupons in use
                            CouponUtils.updateAppliedCoupons(currentCartPlansList, appliedCoupons)

                            // Add the new coupon to appliedCoupons
                            appliedCoupons.push(newCoupon);
                            logger.debug('registrationFlowStep5 > click #applyCoupon: Updated Applied Coupons', { appliedCoupons });

                            // Apply the updated appliedCoupons to the registration fee
                            const registrationFee = currentData.registrationFee;
                            RegistrationUtils.applyNewCouponsToRegistrationFee(registrationFee, appliedCoupons);
                            logger.debug('registrationFlowStep5 > click #applyCoupon: Applied Coupons to Registration Fee');
                        }
                        Session.set(includeSiblingDiscount, true);
                    } else { // "Switch" is selected
                        logger.info('registrationFlowStep5 > click #applyCoupon: User Chose to Switch to New Discounts');
                        currentData.plans = newCartPlansList;

                        // Update the appliedCoupons to only include the coupons in use
                        CouponUtils.updateAppliedCoupons(newCartPlansList, appliedCoupons)

                        // Add the new coupon to appliedCoupons
                        appliedCoupons.push(newCoupon);
                        logger.debug('registrationFlowStep5 > click #applyCoupon: Updated Applied Coupons', { appliedCoupons });

                        // Apply the updated appliedCoupons to the registration fee
                        const registrationFee = currentData.registrationFee;
                        RegistrationUtils.applyNewCouponsToRegistrationFee(registrationFee, appliedCoupons);
                        logger.debug('registrationFlowStep5 > click #applyCoupon: Applied Coupons to Registration Fee');
                    }

                    applyDiscountChanges(i, currentData, topLinePercentDiscounts, appliedCoupons);
                    logger.info('registrationFlowStep5 > click #applyCoupon: Applied Discount Changes');
                });
            }
            else {
                logger.warning('registrationFlowStep5 > click #applyCoupon: Unhandled Error Type', { error });
                if (error.reason) {
                    mpSwal.fire("Error", `${error.reason}`, "error");
                } else {
                    mpSwal.fire("Error", error.message, "error");
                }
            }
        });
    },
    "submit #addCouponForm": function (e, i) {
        e.preventDefault();
    }
});

Template.registrationFlowStep5AddEditEmployeeIdModal.created = function () {
    const currentEmployeeId = Session.get(employeeDiscount)?.employeeId;
    this.disabledSubmitBtn = new ReactiveVar(!currentEmployeeId);
};

Template.registrationFlowStep5AddEditEmployeeIdModal.helpers({
    submitButtonDisabled() {
        return Template.instance().disabledSubmitBtn.get();
    },
    employeeIdValue() {
        return Session.get(employeeDiscount)?.employeeId ?? '';
    }
});

Template.registrationFlowStep5AddEditEmployeeIdModal.events({
    "input input[name='employee-id']": function (e, i) {
        i.disabledSubmitBtn.set(!e.target.value);
    },
    "click #validateEmployeeId": function (e, i) {
        const employeeIdValue = $("input[name='employee-id']").val().trim();
        logger.debug('registrationFlowStep5 > click #validateEmployeeId: Employee ID Retrieved', { employeeIdValue });

        const currentData = Session.get(registrationDataSessionName) || {};
        const topLinePercentDiscounts = Template.instance().data.topLinePercentDiscounts;

        if (!Template.instance().data.isEdit) {
            const cachedCoupons = Session.get(couponCodes) || [];
            const invalidCoupons = RegistrationUtils.validateCouponWithDiscounts(cachedCoupons);

            logger.info('registrationFlowStep5 > click #validateEmployeeId: Validating coupons', { cachedCoupons, invalidCoupons });

            if (invalidCoupons.length > 0) {
                mpSwal.fire({
                    text: `These coupons ${invalidCoupons} cannot be used with other discounts. If you choose to proceed, the employee discount will be applied without these coupons`,
                    showCancelButton: true,
                    confirmButtonText: "Proceed",
                    cancelButtonText: "Close"
                }).then(result => {
                    if (result.value) {
                        logger.info('registrationFlowStep5 > click #validateEmployeeId: User chose to proceed with invalid coupons', { invalidCoupons });
                        // remove coupons from session
                        const newCachedCoupons = cachedCoupons.filter(coupon => !invalidCoupons.includes(coupon.code));
                        Session.set(couponCodes, newCachedCoupons);
                        removeInvalidCouponsFromAllocations(currentData, invalidCoupons);
                        validateAndApplyEmployeeDiscount(i, employeeIdValue, currentData, topLinePercentDiscounts);
                    } else {
                        logger.info('registrationFlowStep5 > click #validateEmployeeId: User canceled coupon validation');
                    }
                });
                return;
            }
            validateAndApplyEmployeeDiscount(i, employeeIdValue, currentData, topLinePercentDiscounts);
        } else {
            logger.info('registrationFlowStep5 > click #validateEmployeeId: Editing mode detected, validating with employee discount');
            validateAndApplyEmployeeDiscount(i, employeeIdValue, currentData, topLinePercentDiscounts, true);
        }
    },
    "submit #employeeIdForm": function (e, i) {
        e.preventDefault();
    }
});

Template.registrationFlowStep5PaymentModal.created = function () {
    this.currentOrg = null;
    this.selectedPaymentView = new ReactiveVar('');
    this.showServiceChargeNotice = new ReactiveVar(false);
    this.achDisabled = new ReactiveVar(false);
    Meteor.callAsync('getOrgById', FlowRouter.getQueryParam('orgId')).then((result) => {
        this.showServiceChargeNotice.set(result.billing?.passthroughFees);
        this.currentOrg = result
    }).catch((error) => {
        console.log("error in getOrgById", error);
    });

    Meteor.callAsync('hasCustomizationByOrgId', FlowRouter.getQueryParam('orgId'), AvailableCustomizations.DISABLE_ACH).then((result) => {
        this.achDisabled.set(result);
    }).catch((err) => {
        if (err) {
            this.achDisabled.set(false);
        }
    });
};

let registrationFlowStep5PaymentModalAdyenStateCc = null;
let registrationFlowStep5PaymentModalAdyenIsValidCc = false;
let registrationFlowStep5PaymentModalAdyenStateAch = null;
let registrationFlowStep5PaymentModalAdyenIsValidAch = false;

Template.registrationFlowStep5PaymentModal.rendered = function () {
    this.paymentProcessing = new ReactiveVar(false);
    const instance = Template.instance();
    registrationFlowStep5PaymentModalAdyenStateCc = null;
    registrationFlowStep5PaymentModalAdyenIsValidCc = false;
    registrationFlowStep5PaymentModalAdyenStateAch = null;
    registrationFlowStep5PaymentModalAdyenIsValidAch = false;

    function handleOnChangeAch(state, component) {
        registrationFlowStep5PaymentModalAdyenStateAch = state.data;
        registrationFlowStep5PaymentModalAdyenIsValidAch = state.isValid;
    }

    function handleOnChangeCc(state, component) {
        registrationFlowStep5PaymentModalAdyenStateCc = state.data;
        registrationFlowStep5PaymentModalAdyenIsValidCc = state.isValid;
    }

    const configurationBank = {
        locale: 'en_US',
        environment: instance.data.orgData.environment,
        clientKey: instance.data.orgData.clientKey,
        onChange: handleOnChangeAch
    };
    const configurationCc = {
        locale: 'en_US',
        environment: instance.data.orgData.environment,
        clientKey: instance.data.orgData.clientKey,
    };

    const checkoutBank = new AdyenCheckout(configurationBank);
    const checkoutCc = new AdyenCheckout(configurationCc);

    instance.adyenAch = checkoutBank.create('ach').mount('#ach-component-container');
    const customCard = checkoutCc.create('securedfields', {
        // Optional configuration
        type: 'card',
        ariaLabels: {
            lang: 'en-US',
            encryptedCardNumber: {
                label: 'Credit or debit card number field'
            }
        },
        // Events
        onChange: handleOnChangeCc,
        onValid: function () { },
        onLoad: function () { },
        onConfigSuccess: function () { },
        onFieldValid: function () { },
        onBrand: function () { },
        onError: function () { },
        onFocus: function () { },
        onBinValue: function (bin) { }
    }).mount('#customCard-container');
};

Template.registrationFlowStep5PaymentModal.helpers({
    showBankForm() {
        return Template.instance().selectedPaymentView.get() === 'bank';
    },
    showCcForm() {
        return Template.instance().selectedPaymentView.get() === 'cc';
    },
    personId() {
        return Template.instance().data.personId;
    },
    orgData() {
        return Template.instance().data.orgData;
    },
    showServiceChargeNotice() {
        return Template.instance().showServiceChargeNotice.get();
    },
    showAutoPay() {
        return !FlowRouter.getQueryParam('personId');
    },
    currentOrg() {
        return Template.instance().currentOrg;
    },
    paymentAmount() {
        return Template.instance().data.amount;
    },
    showAchPayment() {
        return !Template.instance().achDisabled.get();
    }
});

Template.registrationFlowStep5PaymentModal.events({
    "change input[name='method']": function (e, i) {
        i.selectedPaymentView.set(e.target.value);
    },
    "click #payNow": function (e, i) {
        e.preventDefault();
        if (i.paymentProcessing.get()) {
            return;
        }
        i.paymentProcessing.set(true);
        const regData = Session.get(registrationDataSessionName) || {};
        regData.couponCodes = Session.get(couponCodes) || [];
        logger.debug('registrationFlowStep5 > click #payNow: Registration Data Retrieved', { regData });

        if (!i.selectedPaymentView.get()) {
            mpSwal.fire('Please choose a payment method');
            logger.info('registrationFlowStep5 > click #payNow: Payment method not selected');
            return;
        }

        const personId = Template.instance().data.personId;
        logger.debug('registrationFlowStep5 > click #payNow: Selected Payment View', { selectedPaymentView: i.selectedPaymentView.get() });

        if (i.selectedPaymentView.get() === 'bank') {
            if ($('#frmPayment :invalid').length > 0) {
                logger.warning('registrationFlowStep5 > click #payNow: Missing required details in bank form');
                return mpSwal.fire('Error', 'You are missing some required details.', 'error');
            }

            if (!($("input[name='is-checking']").is(":checked"))) {
                logger.warning('registrationFlowStep5 > click #payNow: User did not certify US-based checking account');
                return mpSwal.fire('Error', 'Please check the box certifying that the account information provided is for a US-based checking account.', 'error');
            }

            if (!registrationFlowStep5PaymentModalAdyenIsValidAch || !registrationFlowStep5PaymentModalAdyenStateAch) {
                logger.warning('registrationFlowStep5 > click #payNow: ACH details are missing');
                return mpSwal.fire('Error', 'You are missing some required details.', 'error');
            }

            $(e.target).html('Saving...').prop('disabled', true);
            logger.info('registrationFlowStep5 > click #payNow: Adding payment method for bank account', { personId });

            Meteor.callAsync('adyenAddPaymentMethod', {
                sourceType: 'bank_account',
                adyenComponentState: registrationFlowStep5PaymentModalAdyenStateAch,
                personId,
                orgId: FlowRouter.getQueryParam('orgId'),
                fromRegistration: true
            }).then((result) => {
                logger.info('registrationFlowStep5 > click #payNow: Bank account payment method added successfully', { result });
                regData.payment_source = 'bank_account';
                Meteor.callAsync('removePlaceholderGuardianField', regData.contacts[0]._id).then((result) => {
                }).catch((error) => {
                    closePaymentModal(i);
                    logger.error('registrationFlowStep5 > click #payNow: Error removing placeholder guardian field', { error });
                    if (error.reason) {
                        mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
                    } else {
                        mpSwal.fire("Error", error.message, "error");
                    }
                });

                if (!isParentEdit.get()) {
                    logger.info('registrationFlowStep5 > click #payNow > approveRegistration', { 'Saved Data': regData });
                    Meteor.callAsync('approveRegistration', regData, null, FlowRouter.getQueryParam('orgId'), true).then((result) => {
                        logger.info('registrationFlowStep5 > click #payNow > formatMessage', { 'result': result  });
                        Meteor.callAsync('formatMessage', result).then((res) => {
                            for (const o of res) {
                                parent.postMessage({
                                    name: o.action,
                                    detail: o
                                }, '*');
                            }
                        }).catch((err) => {
                            console.log("error in formatMessage", err);
                        });
                        closePaymentModal(i);
                        FlowRouter.go(`/registration-completed?orgId=${FlowRouter.getQueryParam('orgId')}`);
                    }).catch((error) => {
                        closePaymentModal(i);
                        logger.error('registrationFlowStep5 > click #payNow: Error approving registration', { error });
                        if (error.reason) {
                            mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
                        } else {
                            mpSwal.fire("Error", error.message, "error");
                        }
                    });
                } else {
                    // Make signed-in user the responsible party
                    regData.payer_id = FlowRouter.getQueryParam('personId');
                    logger.info('registrationFlowStep5 > click #payNow > updateFamily', { 'Saved Data': regData });
                    Meteor.callAsync('updateFamily', regData).then((res) => {
                        closePaymentModal(i);
                        mpSwal.fire({
                            text: 'Family updated!',
                            allowOutsideClick: false
                        }).then(result => { if (result.isConfirmed) window.close(); });
                    }).catch((err) => {
                        closePaymentModal(i);
                        logger.error('registrationFlowStep5 > click #payNow: Error updating family', { error: err });
                        if (err.reason) {
                            mpSwal.fire("Error", `${err.reason} ${err.details}`, "error").then(result => { if (result.isConfirmed) window.close(); });
                        } else {
                            mpSwal.fire("Error", err.message, "error").then(result => { if (result.isConfirmed) window.close(); });
                        }
                    });
                }
            }).catch((error) => {
                $(e.target).html('Save').prop('disabled', false);
                logger.error('registrationFlowStep5 > click #payNow: Error adding payment method for bank account', { error });
                if (error.reason) {
                    mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
                } else {
                    mpSwal.fire("Error", error.message, "error");
                }
            });


        } else {
            if (!registrationFlowStep5PaymentModalAdyenStateCc || !registrationFlowStep5PaymentModalAdyenIsValidCc) {
                logger.warning('registrationFlowStep5 > click #payNow: Credit card details are missing');
                return mpSwal.fire('Error', 'You are missing some required details.', 'error');
            }

            $(e.target).html('Saving...').prop('disabled', true);
            logger.info('registrationFlowStep5 > click #payNow: Adding payment method for credit card', { personId });

            Meteor.callAsync('adyenAddPaymentMethod', {
                sourceType: 'card',
                adyenComponentState: registrationFlowStep5PaymentModalAdyenStateCc,
                personId,
                orgId: FlowRouter.getQueryParam('orgId'),
                fromRegistration: true
            }).then((result) => {
                logger.info('registrationFlowStep5 > click #payNow: Credit card payment method added successfully', { result });
                regData.payment_source = 'card';
                Meteor.callAsync('removePlaceholderGuardianField', regData.contacts[0]._id).then((result) => {
                }).catch((error) => {
                    closePaymentModal(i);
                    logger.error('registrationFlowStep5 > click #payNow: Error removing placeholder guardian field', { error });
                    if (error.reason) {
                        mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
                    } else {
                        mpSwal.fire("Error", error.message, "error");
                    }
                });

                if (!FlowRouter.getQueryParam('personId')) {
                    logger.info('registrationFlowStep5 > click #payNow > approveRegistration', { 'Saved Data': regData });   
                    Meteor.callAsync('approveRegistration', regData, null, FlowRouter.getQueryParam('orgId'), true).then((result) => {
                        logger.info('registrationFlowStep5 > click #payNow > formatMessage', { 'result': result  });                 
                        Meteor.callAsync('formatMessage', result).then((res) => {
                            for (const o of res) {
                                parent.postMessage({
                                    name: o.action,
                                    detail: o
                                }, '*');
                            }
                        }).catch((err) => {
                            console.log("error in formatMessage", err);
                        });
                        closePaymentModal(i);
                        FlowRouter.go(`/registration-completed?orgId=${FlowRouter.getQueryParam('orgId')}`);
                    }).catch((error) => {
                        logger.error('registrationFlowStep5 > click #payNow: Error approving registration', { error });
                        if (error.reason) {
                            mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
                        } else {
                            mpSwal.fire("Error", error.message, "error");
                        }
                    });
                } else {
                    // Make signed-in user the responsible party
                    regData.payer_id = FlowRouter.getQueryParam('personId');
                    logger.info('registrationFlowStep5 > click #payNow > updateFamily', { 'Saved Data': regData });
                    Meteor.callAsync('updateFamily', regData).then((res) => {
                        closePaymentModal(i);
                        mpSwal.fire('Family updated!').then(result => { if (result.isConfirmed) window.close(); });
                    }).catch((err) => {
                        closePaymentModal(i);
                        logger.error('registrationFlowStep5 > click #payNow: Error updating family', { error: err });
                        if (err.reason) {
                            mpSwal.fire("Error", `${err.reason} ${err.details}`, "error").then(result => { if (result.isConfirmed) window.close(); });
                        } else {
                            mpSwal.fire("Error", `${err.message}`, "error").then(result => { if (result.isConfirmed) window.close(); });
                        }
                    });
                }
            }).catch((error) => {
                $(e.target).html('Save').prop('disabled', false);
                logger.error('registrationFlowStep5 > click #payNow: Error adding payment method for credit card', { error });
                if (error.reason) {
                    mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
                } else {
                    mpSwal.fire("Error", error.message, "error");
                }
            });
        }
    },
    'click .cancel-payment': function (e, i) {
        i.data.showPayModal.set(false);
        i.data.disabledSubmitBtn.set(false);
    }
});

function closePaymentModal(i) {
    $('#registrationFlowStep5PaymentModal').modal('hide');
    i.data.disabledSubmitBtn.set(false)
    i.data.showPayModal.set(false)
}

function validateAndApplyEmployeeDiscount(instance, employeeIdValue, currentData, topLinePercentDiscounts, isEdit = false) {
    Meteor.callAsync("validateEmployee", employeeIdValue, FlowRouter.getQueryParam('orgId')).then((result) => {
        mpSwal.fire({ icon: "success", title: `Employee discount successfully applied.` });
        const employeeDiscountObject = _.clone(result);
        employeeDiscountObject.employeeId = employeeIdValue
        Session.set(employeeDiscount, employeeDiscountObject);
        if (!isEdit) {
            applyAllocationEmployeeDiscount(currentData, result);
            calculateIndividualPlanDiscountAndTotal(currentData, topLinePercentDiscounts);
            Session.set(registrationDataSessionName, currentData);
            // Re-calculate charges due after applying employee discount
            recalculateChargesDue(instance, currentData, FlowRouter.getQueryParam('orgId'));
        }
        hideModal('#registrationFlowStep5AddEditEmployeeIdModal');
    }).catch((error) => {
        if (error.reason) {
            mpSwal.fire("Error", `${error.reason} ${error.details}`, "error");
        } else {
            mpSwal.fire("Error", error.message, "error");
        }
    });
}

export function applyAllocationEmployeeDiscount(currentData, employeeDiscount) {
    if (!currentData.plans || !currentData.plans.length || !employeeDiscount) {
        return;
    }
    const allocation = {
        allocationType: "discount",
        amount: employeeDiscount.amount,
        amountType: employeeDiscount.amountType,
        discountType: employeeDiscount.type,
        allocationDescription: employeeDiscount.description
    }
    for (const childPlans of currentData.plans) {
        for (const plan of childPlans) {
                // only add the district discount if it doesn't already exist
                allocation.id = Random.id();
                plan.allocations = plan.allocations || [];
                const alreadyHasDiscount = plan.allocations.some(a => a.discountType === employeeDiscount.type);
            if (!alreadyHasDiscount) {
                plan.allocations.push(_.clone(allocation));
            }
        }
    }
}

export function removeAllocationEmployeeDiscount(currentData, employeeDiscount) {
    if (!currentData.plans || !currentData.plans.length || !employeeDiscount) {
        return;
    }

    for (const childPlans of currentData.plans) {
        for (const plan of childPlans) {
            if (plan.allocations.some(a => a.discountType === employeeDiscount.type)) {
                // remove discount if it exists
                plan.allocations = plan.allocations.filter(a => a.discountType !== employeeDiscount.type);
            }
        }
    }
}

export function removeEmployeeDiscountFromAllocations(currentData) {
    if (!currentData.plans || !currentData.plans.length) {
        return;
    }

    for (const childPlans of currentData.plans) {
        for (const plan of childPlans) {
            plan.allocations = (plan.allocations || []).filter(allocation => allocation.discountType !== DiscountTypes.RAS_EMPLOYEE && allocation.discountType !== DiscountTypes.STAFF);
        }
    }
}

export function removeInvalidCouponsFromAllocations(currentData, invalidCoupons) {
    if (!currentData.plans || !currentData.plans.length || !invalidCoupons) {
        return;
    }

    for (const childPlans of currentData.plans) {
        for (const plan of childPlans) {
            plan.allocations = (plan.allocations || []).filter(allocation => !allocation.code || !invalidCoupons.includes(allocation.code));
        }
    }
    if (!currentData.registrationFee) {
        return;
    }
    currentData.registrationFee.allocations = (currentData.registrationFee.allocations || []).filter(allocation => !allocation.code || !invalidCoupons.includes(allocation.code));
}

/**
 * Calculates the discount amounts for all plans and the registration fee, updating their totals accordingly.
 *
 * @param {Object} currentData - The current data containing plans and possibly a registration fee.
 * @param {boolean} topLinePercentDiscounts - A flag indicating whether top line percent discounts should be applied.
 *
 * @example
 * const currentData = {
 *   plans: [
 *     [
 *       { amount: 1000, allocations: [{ amountType: 'dollars', amount: 100 }] },
 *       { amount: 2000, allocations: [{ amountType: 'percent', amount: 5 }] }
 *     ]
 *   ],
 *   registrationFee: { amount: 500, allocations: [{ amountType: 'dollars', amount: 50 }] }
 * };
 * calculateIndividualPlanDiscountAndTotal(currentData, false);
 * console.log(currentData.plans[0][0].planTotal); // 900
 * console.log(currentData.registrationFee.planTotal); // 450
 */
export function calculateIndividualPlanDiscountAndTotal(currentData, topLinePercentDiscounts) {
    for (const childPlans of currentData.plans) {
        for (const plan of childPlans) {
            plan.planTotal = plan.amount;

            const totalDiscounts = calculateTotalDiscounts(plan.amount, plan.allocations, topLinePercentDiscounts);

            if (totalDiscounts > plan.planTotal) {
                plan.planTotal = 0;
            } else {
                plan.planTotal -= totalDiscounts;
            }
        }
    }

    if (!currentData.registrationFee) {
        return;
    }
    const regFee = currentData.registrationFee;
    regFee.planTotal = regFee.amount;

    const totalDiscounts = calculateTotalDiscounts(regFee.amount, regFee.allocations, topLinePercentDiscounts);
    if (totalDiscounts > regFee.planTotal) {
        regFee.planTotal = 0;
    } else {
        regFee.planTotal -= totalDiscounts;
    }

    currentData.registrationFee = regFee;
}

/**
 * Calculates the total discount for a given plan or registration fee based on its allocations.
 *
 * @param {number} amount - The original amount for the plan or registration fee.
 * @param {Array<Object>} allocations - The list of allocations, which contain the discount details.
 * @param {boolean} topLinePercentDiscounts - A flag indicating whether top line percent discounts should be applied.
 * @returns {number} - The total discount amount, rounded to two decimal places.
 *
 * @example
 * const amount = 1000;
 * const allocations = [
 *   { amountType: 'dollars', amount: 100 },
 *   { amountType: 'percent', amount: 10 }
 * ];
 * const totalDiscounts = calculateTotalDiscounts(amount, allocations, false);
 * console.log(totalDiscounts); // 200
 */
export function calculateTotalDiscounts(amount, allocations, topLinePercentDiscounts) {
    let totalDiscounts = 0;

    if (!allocations || !allocations.length) {
        return totalDiscounts;
    }

    const sumOfDollarDiscounts = _.chain(allocations)
        .filter((a) => a.amountType === "dollars")
        .reduce((memo, a) => memo + a.amount, 0.0)
        .value();

    for (const allocation of allocations) {
        let discountAmount = 0;

        if (allocation.amountType === "dollars") {
            discountAmount = allocation.amount;
        } else {
            discountAmount = BillingUtils.roundToTwo((amount - (topLinePercentDiscounts ? 0 : sumOfDollarDiscounts)) * allocation.amount / 100.0);
        }

        if (discountAmount < 0) {
            discountAmount = 0
        }

        allocation.discountAmount = discountAmount;
        totalDiscounts += discountAmount;
    }

    return BillingUtils.roundToTwo(totalDiscounts);
}


export function recalculateChargesDue(instance, currentData, orgId, forceSetRegistrationFeeAmountDueInSession = false) {
    console.log(instance)
    const nonPurchasedPlansSessionData = RegistrationUtils.getNonPurchasedPlansFromSessionData(currentData);
    const dueTodayReactiveVar = instance.data.dueTodayCharges ?? instance.dueTodayCharges;
    const regFeeAmountDueReactiveVar = instance.data.registrationFeeAmountDue ?? instance.registrationFeeAmountDue;
    Meteor.callAsync('getRegistrationChargesDueToday', nonPurchasedPlansSessionData, orgId).then((results) => {
        dueTodayReactiveVar.set(results);
        Meteor.callAsync('getRegistrationFeesDue', nonPurchasedPlansSessionData, orgId).then((res) => {
            regFeeAmountDueReactiveVar.set(res.fee);
            setRegistrationFeeAmountDueInSession(currentData, res, forceSetRegistrationFeeAmountDueInSession);
        }).catch((error) => {
            console.error('Error getting registration fee charge', error);
        });
    }).catch((error) => {
        console.error('Error getting registration charges due today', error);
    });
}

/**
 * Set the registration fee amount due in the session.
 *
 * @param currentData
 * @param feeObject
 * @param forceSetRegistrationFeeAmountDueInSession
 */
function setRegistrationFeeAmountDueInSession(currentData, feeObject, forceSetRegistrationFeeAmountDueInSession = false) {
    if (FlowRouter.getQueryParam('personId') || forceSetRegistrationFeeAmountDueInSession) {
        // Only set the registration fee amount due if the flow is restricted to a single child (DIY or a logged in parent adding a child)
        // The amount due allows registration fee calculation to be bypassed during approval since the correct amount is already known for the single child
        if (currentData.children.length === 1) {
            currentData.registrationFeeAmountDue = feeObject.fee;
            currentData.registrationFeeAmountDueTimePeriodIds = feeObject.timePeriodIds;
            Session.set(registrationDataSessionName, currentData);
        } else if (currentData.children.length > 1 && (currentData.registrationFeeAmountDue || currentData.registrationFeeAmountDueTimePeriodIds)) {
            // Remove the registration fee amount due if the flow is not restricted to a single child
            delete currentData.registrationFeeAmountDue;
            delete currentData.registrationFeeAmountDueTimePeriodIds;
            Session.set(registrationDataSessionName, currentData);
        }
    }
}
/**
 * Applies a new coupon to a deep copy of the cart plans list, adjusting existing discounts or coupons as necessary.
 *
 * This function deep copies the original cart plans list and applies a new coupon, handling potential conflicts
 * with existing coupons. The function recalculates individual plan totals and overall savings for the cart.
 *
 * @param {Array<Object>} cartPlansList - The original list of cart plans to which the coupon will be applied.
 * @param {Object} coupons - An object containing details of the new and conflicting coupons.
 * @param {Object} coupons.newCoupon - The newly added coupon from the user.
 * @param {Object} coupons.conflictingCoupon - The coupon that conflicts with the new coupon (e.g., a coupon that can't be used with other coupons but has already been selected by the user).
 * @param {Array<Object>} currentChildren - The current list of child plans or items associated with the cart.
 * @param {Object} allocation - The allocation object that defines the discount details (amount, type, etc.) for the new coupon.
 * @param {Object} errorDetails - The details of the error, including the error code related to the coupon conflict.
 * @returns {Object} - An object containing the grand total, total savings, and the modified copy of the cart plans list.
 * @returns {number} return.grandTotal - The new grand total after applying the new coupon.
 * @returns {number} return.totalSavings - The total savings from discounts and coupons after the new coupon is applied.
 * @returns {Array<Object>} return.copiedCartPlansList - The deep copied and modified cart plans list with the applied coupon.
 */
function applyNewCouponAndCalculateSavings(cartPlansList, { newCoupon, conflictingCoupon }, currentChildren, allocation, errorDetails) {
    const copiedCartPlansList = deepCopyCartPlans(cartPlansList);
    handleDiscountsAndCouponsForErrorCode(copiedCartPlansList, newCoupon, conflictingCoupon, errorDetails.errorCode);
    RegistrationUtils.addAllocationCouponDiscount(currentChildren, copiedCartPlansList, newCoupon, allocation, false, { errorCode: errorDetails.errorCode });
    calculateIndividualPlanDiscountAndTotal({ plans: copiedCartPlansList }, false);
    return RegistrationUtils.calculateCartTotalsAndDiscountSavings(copiedCartPlansList, 'new');
}

function currentPlansWithPotentialCouponsOrDiscounts(currentPlans, currentChildren, newCoupon, allocation, errorDetails) {
    if (errorDetails.errorCode === "MR-000005") {
        // If you add a coupon that doesn't work with discounts, but does work with coupons, the coupon will be applied to plans with no discounts
        RegistrationUtils.addAllocationCouponDiscount(currentChildren, currentPlans, newCoupon, allocation, false, { errorCode: errorDetails.errorCode });
    }
    calculateIndividualPlanDiscountAndTotal({ plans: currentPlans }, false);
    return RegistrationUtils.calculateCartTotalsAndDiscountSavings(currentPlans, 'current');
}

/**
 * Creates a deep copy of the cart plans list to avoid modifying the original data.
 *
 * @param {Array} cartPlansList - The original list of cart plans.
 * @returns {Array} - A deep copy of the cart plans list.
 */
function deepCopyCartPlans(cartPlansList) {
    return cloneDeep(cartPlansList);
}

/**
 * Adjusts the plans in the cart based on the provided error code.
 * Removes certain discounts or coupons depending on the error code.
 *
 * @param {Array} cartPlansList - The list of cart plans to adjust.
 * @param {Object} newCoupon - The newly added coupon from the user.
 * @param {Object} conflictingCoupon - The coupon that conflicts with the new coupon.
 * @param {string} errorCode - The error code indicating how to adjust the discounts or coupons.
 */
function handleDiscountsAndCouponsForErrorCode(cartPlansList, newCoupon, conflictingCoupon, errorCode) {
    cartPlansList.forEach(childPlans => {
        childPlans.forEach(plan => {
            if (errorCode === 'MR-000005') {
                handleSiblingDiscountReplacement(plan);
            } else if (errorCode === 'MR-000006') {
                replaceConflictingCoupon(plan, newCoupon, conflictingCoupon);
            } else if (errorCode === 'MR-000007') {
                removeAllCouponsAndAddNew(plan, newCoupon);
            }
        });
    });
}

/**
 * Replaces a sibling discount in the plan with a new coupon.
 *
 * @param {Object} plan - The plan object containing allocations and other details.
 * @param {Object} newCoupon - The new coupon object to be added to the plan.
 */
function handleSiblingDiscountReplacement(plan) {
    plan.allocations = plan.allocations.filter(allocation => allocation.discountType !== 'siblingDiscount');
    Session.set(includeSiblingDiscount, false);
}

/**
 * Replaces a conflicting coupon in the plan with a new coupon.
 * If the new coupon doesn't allow discounts, removes sibling discounts as well.
 *
 * @param {Object} plan - The plan object to update.
 * @param {Object} newCoupon - The new coupon object to add.
 * @param {Object} conflictingCoupon - The conflicting coupon object to remove.
 */
function replaceConflictingCoupon(plan, newCoupon, conflictingCoupon) {
    // Remove the conflicting coupon
    plan.allocations = plan.allocations.filter(allocation => allocation.code !== conflictingCoupon.code);

    // Check if the new coupon doesn't allow discounts
    if (!newCoupon.usedWithDiscounts) {
        // Remove all sibling discounts
        plan.allocations = plan.allocations.filter(allocation => allocation.discountType !== 'siblingDiscount');
        Session.set(includeSiblingDiscount, false);
    }
}

/**
 * Removes all coupons from the plan and adds a new coupon.
 *
 * @param {Object} plan - The plan object containing allocations and other details.
 * @param {Object} newCoupon - The new coupon object to be added to the plan.
 */
function removeAllCouponsAndAddNew(plan, newCoupon) {
    // Remove all coupons
    plan.allocations = plan.allocations.filter(allocation => allocation.discountType !== 'coupon');

    // Check if the new coupon doesn't allow discounts
    if (!newCoupon.usedWithDiscounts) {
        // Remove all sibling discounts
        plan.allocations = plan.allocations.filter(allocation => allocation.discountType !== 'siblingDiscount');
        Session.set(includeSiblingDiscount, false);
    }
}

function applyDiscountChanges(i, currentData, topLinePercentDiscounts, appliedCoupons) {
    calculateIndividualPlanDiscountAndTotal(currentData, topLinePercentDiscounts);
    Session.set(couponCodes, appliedCoupons);
    Session.set(registrationDataSessionName, currentData);

    // This is so when a user goes backwards in the flow, the selected plans allocations are updated correctly
    selectedPlans.set(CouponUtils.updateSelectedPlansAllocations(selectedPlans.get(), currentData.plans));

    recalculateChargesDue(i, currentData, FlowRouter.getQueryParam('orgId'));
}

function getUniqueAllocations(cartPlansList) {
    const allocationDescriptions = new Set();

    cartPlansList.flat().forEach(plan => {
        plan.allocations.forEach(allocation => {
            const identifier = allocation.code || allocation.allocationDescription;
            if (identifier) {
                allocationDescriptions.add(identifier);
            }
        });
    });

    return Array.from(allocationDescriptions);
}
