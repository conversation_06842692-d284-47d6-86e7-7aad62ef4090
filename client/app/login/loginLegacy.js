import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Accounts } from 'meteor/accounts-base'
import { PasswordUtil } from "../../../lib/util/passwordUtil";
import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './loginLegacy.html';
import { showModal } from '../main';
import '../users/userTerms.html';

function swapLoginClasses(newClass) {
  $('#kt_login').removeClass('login-forgot-on');
  $('#kt_login').removeClass('login-signin-on');
  $('#kt_login').removeClass('login-signup-on');
  $('#kt_login').removeClass('login-payment-statement-on');

  $('#kt_login').addClass(newClass);
}

Template.loginLegacy.onCreated(function() {
  const codeLogin = FlowRouter.getQueryParam('codeLogin');
  if (!codeLogin && FlowRouter.getRouteName() !== 'register' && !Meteor.settings.public.allowLegacyLogin) {
    FlowRouter.go('login');
  }
	this.codeEntryActive = new ReactiveVar(false);
    this.isCodeLogin = false;
    this.loaded = new ReactiveVar(true);
    this.subscribe("userData");
});



Template.loginLegacy.onRendered(function() {
    const codeLogin = FlowRouter.getQueryParam('codeLogin');
    if (codeLogin) {
        swapLoginClasses('login-payment-statement-on');
        this.isCodeLogin = true;
    }
});

Template.loginLegacy.events({
  'click .termsLink': function(e) {
    showModal("userTermsModal", {}, "#userTermsModal");
  },
  'click #kt_login_payment_statements': function(e) {
    swapLoginClasses('login-payment-statement-on');
  },
  'click #kt_login_forgot': function(e) {
    swapLoginClasses('login-forgot-on');
  },
  'click #kt_login_forgot_cancel': function(e) {
    swapLoginClasses('login-signin-on');
  },
  'click #kt_login_payment_statement_cancel': function(e, i) {
      if (i.isCodeLogin) {
          FlowRouter.go('/login');
      }
    i.codeEntryActive.set(false);
    swapLoginClasses('login-signin-on');
  },
  'click #kt_login_signup': function(e) {
    swapLoginClasses('login-signup-on');
  },
  'click #kt_login_signup_cancel': function(e) {
    swapLoginClasses('login-signin-on');
  },
	'submit #kt_login_signin_form': function (e,t) {
		e.preventDefault();
		$("#kt_login_signin_submit").prop('disabled', true).text("Logging in...");

		var email = t.find('#login-email').value,
		password = t.find('#login-password').value;
		if (!Meteor.status().connected)
			mpSwal.fire({
				title:"No Internet connection detected",
				text:"MomentPath requires an Internet connection for many of its features. Please check and try again."
			});

		Meteor.callAsync("checkLoginInfo", email)
    .then((loginInfo) => {
      Meteor.loginWithPassword(email, password, function(err) {
        $("#kt_login_signin_submit").prop('disabled', false).text("Sign In");
        if (err) {
          console.log("error logging in:" + err);
          return mpSwal.fire({
            title: "Email or password incorrect",
            text: "Please try again",
            timer: 1700,
            showConfirmButton: false,
            icon: "error"
          });

        }
        else {
          const user = Meteor.user();
          if(user && !user.isServiceAccount){
            $("#kt_login_signin_submit").prop('disabled', false).text("Sign In");
            return FlowRouter.go('dashboardSite');
          }
          else{
            Meteor.logout();
            return mpSwal.fire({
              title: "Invalid user name or password",
              text: "Please try again",
              timer: 1700,
              showConfirmButton: false,
              icon: "error"
            });
          }
        }
      });
    })
    .catch(((err) =>{
      console.log(err);
      $("#kt_login_signin_submit").prop('disabled', false).text("Sign In");
    }));

	},
  "click #kt_login_payment_statement_request": function(event, i) {
    event.preventDefault();

    if (!Meteor.status().connected) {
      mpSwal.fire({
        title:"No Internet connection detected",
        text:"MomentPath requires an Internet connection for many of its features. Please check and try again."
      });
    }

    Meteor.callAsync("setAccountFromMail","LineLeader support <<EMAIL>>").then(()=>{
      Accounts.requestLoginTokenForUser({
        selector: $("#payment-statement-email").val(), 
        options: {
          userCreationDisabled: true,
        }}, (error) => {
          if (error) {
            mpSwal.fire(error.reason);
          }
          i.codeEntryActive.set(true)
        });
    });
    
  },
  "submit form#kt_login_payment_statement_form": function(event, i) {
    event.preventDefault();

    if (!Meteor.status().connected) {
      mpSwal.fire({
        title:"No Internet connection detected",
        text:"MomentPath requires an Internet connection for many of its features. Please check and try again."
      });
    }

    Meteor.passwordlessLoginWithToken($("#payment-statement-email").val(), $("#payment-statement-login-code").val(), (err) => {
      if (err) {
        mpSwal.fire(err.reason);
      } else {
        i.codeEntryActive.set(false);
        FlowRouter.go('paymentStatementDashboard');
      }
    });
  },
  "submit form#kt_login_forgot_form": function(event) {
  event.preventDefault();

  if (!Meteor.status().connected)
    mpSwal.fire({
      title:"No Internet connection detected",
      text:"MomentPath requires an Internet connection for many of its features. Please check and try again."
    });

  Meteor.callAsync("requestResetPassword", $("#forgot-email").val())
  .then((request)=>{
      mpSwal.fire("Check your email for instructions");
      $('#kt_login').removeClass('login-forgot-on');
      $('#kt_login').addClass('login-signin-on');
    })
  .catch((error)=>{
    mpSwal.fire(error.reason);
  });

},
"submit form#kt_login_reset_form": function(event) {
  event.preventDefault();

  if (!Meteor.status().connected)
    mpSwal.fire({
      title:"No Internet connection detected",
      text:"MomentPath requires an Internet connection for many of its features. Please check and try again."
    });

  var newPw = $("#reset-password").val();
  var checkPw = $("#reset-password-confirm").val();
  if (newPw != checkPw) { mpSwal.fire("Passwords do not match"); return false; }

  var requestData = {
    emailAddress: $("#reset-email").val(),
    resetToken: $("#reset-code").val(),
    password: newPw
  };

  Meteor.callAsync("processResetPassword", requestData)
  .then((request)=>{
      mpSwal.fire("Successful Password Reset")
      FlowRouter.go('/login/legacy');
    })
  .catch((error)=>{
    mpSwal.fire(error.reason);
  });

},
'click #registerCancel': function(e) {
  FlowRouter.go('/login/legacy');
},
'click #registerSubmit': function(e, i) {
  e.preventDefault();
    if (!Meteor.status().connected)
      mpSwal.fire({
        title:"No Internet connection detected",
        text:"MomentPath requires an Internet connection for many of its features. Please check and try again."
      });
    if (!$("#registerAgree").is(':checked'))  {
      mpSwal.fire({
        title:"Please review and agree to terms",
        text:"MomentPath requires you to agree to its terms before registering"
      });
      return;
    }
    var invitationToken = $("#registerToken").val();
    var password = $("input[name=registerPassword]").val();
    var cPassword = $("input[name=cRegisterPassword]").val();
    const passwordErrors = [];
    if (password !== cPassword) {
        passwordErrors.push('Passwords do not match.');
    }
    if (!PasswordUtil.isValid(password)) {
        passwordErrors.push('Password must be at least 8 characters long and contain at least one lower case letter, one upper case letter, and one number.');
    }
    if (passwordErrors.length) {
        mpSwal.fire(passwordErrors.join('<br><br>'));
        return false;
    }

    i.loaded.set(false);
    Meteor.callAsync('registerUser', invitationToken, password)
    .then((response)=>{
      i.loaded.set(true);
      FlowRouter.go("/login/legacy?loginCreated=yes");
    })
    .catch((error)=>{
        mpSwal.fire({
          title: "Error",
          text: error.reason,
          icon: "error"
        });
          });
}
});

Template.loginLegacy.helpers({
  "getWhiteLabelHeaderLogo": function() {
    return Session.get("whiteLabelHeaderLogo");
  },
  "getCode": function() {
    const code = FlowRouter.getQueryParam('code');
    return code;
  },
  "getInvitationToken": function() {
    const code = FlowRouter.getParam('invitationToken');
    return code;
  },
	"showLearnMore": function() {
		return !Meteor.isCordova;
	},
	"showLoginCreated": function() {
		return FlowRouter.getQueryParam('loginCreated') == "yes" ? true : false;
	},
  "codeEntryActive": function() {
    const instance = Template.instance();
		return instance.codeEntryActive.get();
  },
    "isLoading": function() {
        return !Template.instance().loaded.get();
    }
});
