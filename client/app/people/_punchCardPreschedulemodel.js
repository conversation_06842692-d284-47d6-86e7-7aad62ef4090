import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_punchCardPrescheduleModel.html';
import moment from "moment-timezone";
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from "../../../lib/collections/people";
import { Orgs } from '../../../lib/collections/orgs';
import { hideModal } from '../main';
import _ from '../../../lib/util/underscore';

Template.punchCardPrescheduleModal.rendered = function() {
    // parents can only schedule for tomorrow but admins can schedule for today
    const timezone = Orgs.current().getTimezone();
    const currentUserPerson = Meteor.user()?.fetchPerson();
    const userIsAdmin = currentUserPerson?.superAdmin || currentUserPerson?.type === "admin";
    const tomorrowStart = new moment.tz(timezone).add(1, 'days').startOf("day").format('MM/DD/YYYY');
    const today = new moment.tz(timezone).startOf("day").format('MM/DD/YYYY');

    $("#scheduleReservationDate").datepicker({
        autoclose: true,
        todayHighlight: true,
        startDate: userIsAdmin ? today : tomorrowStart,
    })

};

Template.punchCardPrescheduleModal.events({
    'click .createReservation': function(event, template) {
        event.preventDefault();
        const selectedPersonId = template.data.selectedReservationPersonId;
        const person = People.findOne({_id: selectedPersonId});
        const currentOrg = Orgs.current();
        const selectedDate = $("#scheduleReservationDate").val();
        const startDate = moment.tz(selectedDate, "MM/DD/YYYY", currentOrg.getTimezone()).startOf('day');
        const todayDate = new moment.tz(currentOrg.getTimezone()).startOf("day");
        const scheduleType = $("#scheduleType").val();
        const currentUserPerson = Meteor.user().fetchPerson();
        const isAdmin = currentUserPerson?.superAdmin || currentUserPerson?.type === "admin";
        const {firstName, lastName} = currentUserPerson;
        const user = {
            personId: currentUserPerson._id,
            orgId: currentOrg._id,
            userId: template.data.selectedReservationPersonId,
        };
        // if not an admin, only future days are allowed
        if (!selectedDate || (startDate.diff(todayDate, 'days') === 0 && !isAdmin)) {
            mpSwal.fire("Error", "Please select a future date.", "error");
            return;
        }
        if (!scheduleType) {
            mpSwal.fire("Error", "Please select a schedule type.", "error");
            return;
        }
        const scheduleTypeObj = currentOrg.getScheduleTypes().find((type) => type._id === scheduleType);

        const currentReservation = {
            scheduledDate: startDate.valueOf(),
            scheduleType: scheduleType,
            scheduledTime: scheduleTypeObj.startTime,
            scheduledEndTime: scheduleTypeObj.endTime,
            selectedPerson: selectedPersonId,
            orgId: currentOrg._id,
            createdBy: currentUserPerson._id,
        };

        Meteor.callAsync('insertReservation', currentReservation, null, true).then((result) => {
            if (result && result.responseMessage) {
                if (result.responseMessage.includes( "already exists")) {
                    mpSwal.fire("Already Exist!", `A reservation already exist for ${person.firstName} ${person.lastName} on ${selectedDate}`, "warning");
                } else {
                    mpSwal.fire("Result", `New reservation created for ${person.firstName} ${person.lastName} on ${selectedDate}`, "success");
                    let reservationId = result.newReservationId;
                    if(reservationId) {
                        Meteor.callAsync('markPunchCardAsUsed', person._id, selectedDate).then(()=>{
                        }).catch((err)=>{
                            if (err) {
                                mpSwal.fire('Error',err.reason || err.message, 'error');
                            }
                        })
                        Meteor.callAsync('auditPreschedulePunchCard', user,`${firstName} ${lastName}`,
                            {
                                personId: selectedPersonId,
                                selectedDate,
                                reason: reservationId
                            }).then(()=>{
                            }).catch((error)=>{
                                if (error) return mpSwal.fire("Error", "Failed to update punch card audit trail", "error");
                            })
                    }
                }
                hideModal("#punchCardPrescheduleModal");
                FlowRouter.go('/people/' + template.data.selectedReservationPersonId + (isAdmin ? '#reservations' : '#programs'));
            }
        }).catch((error) => {
            if (error) {
                mpSwal.fire("Error", error.reason, "error");
            }
            hideModal("#punchCardPrescheduleModal");
            FlowRouter.go('/people/' + template.data.selectedReservationPersonId + (isAdmin ? '#reservations' : '#programs'));
        });
    },
});


Template.punchCardPrescheduleModal.helpers({
    scheduleTypes() {
        return _.sortBy(Orgs.current().getScheduleTypes(), "type");
    },
});
