<template name="_punchCards">
    {{#if isAdminOrSuperAdmin}}
        {{#if isPunchCardFull}}
            <p href="#" class="font-weight-bold mb-4 pl-4">No unused punch cards</p>
        {{else}}
            <a href="#" class="addPastPunchCardDate font-weight-bolder mb-4 pl-4">Log past punch card date</a>
        {{/if}}
    {{/if}}
    <div class="container d-flex flex-wrap pt-0 mt-0">
        {{#if punchCards }}
            {{#if needsRefresh }}
                {{ refreshView }}
            {{ else }}
                {{#each punchCards}}
                    <div class="d-flex flex-column align-items-center font-weight-bolder mt-0 mx-2 mb-3"
                         style="width: 100px;">
                        {{#if trueIfEq this 0}}
                            <i class="fad fa-circle check-box-enlarged my-2" style="--fa-primary-color: #AC52DB;"></i>
                            <div class="mx-1">Available</div>
                            <a href="#" class="preschedule text-center" data-id="{{this}}">
                                Preschedule
                            </a>
                        {{else}}
                            <i class="fad fa-check-circle check-box-enlarged my-2" style="--fa-primary-color: #AC52DB;"></i>
                            <div class="mx-1">{{this}}</div>
                            {{#if isAdminOrSuperAdmin}}
                                <a href="#" class="markAsUnread text-center" data-id="{{this}}">Mark as unused</a>
<!--                            {{else}}-->
<!--                                {{#if prescheduled}}-->
<!--                                    <a href="#" class="cancelPreschedule text-center" data-id="{{this}}">Cancel Preschedule</a>-->
<!--                                {{/if}}-->
                            {{/if}}
                        {{/if}}
                    </div>
                {{/each}}
            {{/ if }}
        {{else}}
            <p class="mt-4">No punch cards found.</p>
        {{/if}}
    </div>
</template>
