<template name="activations">
  <div class="container flex-row-fluid ml-lg-8">
    <!--begin::Card-->
    <div class="card card-custom gutter-bs">
      <!--Begin::Header-->
      <div class="card-header card-header-tabs-line">
        <div class="card-toolbar">
          <ul class="nav nav-tabs nav-tabs-space-lg nav-tabs-line nav-tabs-bold nav-tabs-line-3x" role="tablist">
            <li class="nav-item mr-3">
              <a class="nav-link active" data-toggle="tab" href="#tnaf">
                <span data-cy="children-no-activated-families" class="nav-text font-weight-bold">{{ getEntityTypePeople }} w/ no activated families</span>
              </a>
            </li>
            <li class="nav-item mr-3">
              <a class="nav-link" data-toggle="tab" href="#tufm">
                <span data-cy="all-unactivated-families" class="nav-text font-weight-bold">All unactivated families</span>
              </a>
            </li>
            <li class="nav-item mr-3">
              <a class="nav-link" data-toggle="tab" href="#tfmi">
                <span data-cy="children-no-relationships" class="nav-text font-weight-bold">{{ getEntityTypePeople }} w/ no relationships</span>
              </a>
            </li>
          </ul>
        </div>
        <div class="d-flex flex-row align-items-center justify-content-center">
          <div data-cy="send-all-invitations" class="btn btn-primary font-weight-bolder text-white" id="btnSendAll">
            Send All Invitations
          </div>
        </div>
      </div>
      <!--end::Header-->
      <!--Begin::Body-->
      <div class="card-body px-0">
        <div class="tab-content pt-5">
          <!--begin::Tab Content-->
          <div class="tab-pane active" id="tnaf" role="tabpanel">
            {{> _activationsData type="noActivations" }}
          </div>      
          <div class="tab-pane" id="tufm" role="tabpanel">
            {{> _activationsData type="anyActivations" }}
          </div>
          <div class="tab-pane" id="tfmi" role="tabpanel">
            {{> _activationsData type="noRelationships" }}
          </div>
          <!--end::Tab Content-->
        </div>
      </div>
      <!--end::Body-->
    </div>
    <!--end::Card-->
  </div>
</template>
