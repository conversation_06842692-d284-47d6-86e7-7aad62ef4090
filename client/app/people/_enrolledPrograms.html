<template name="_enrolledPrograms">
    <div class="d-flex flex-column-fluid">
        <div class="container">
            <div class="d-flex flex-column">
                {{#if hasResults}}
                    {{#each program in sortedProgramsAndItems}}
                        {{#if program.isItem}}
                            {{> _reservationCard reservation=program type='item' itemData=program.itemData}}
                        {{else}}
                            {{> _reservationCard reservation=program enrolledPrograms=enrolledPrograms enrolledSelectiveWeekPlans=enrolledSelectiveWeekPlans parentData=parentData type='single'}}
                        {{/if}}
                    {{/each}}
                    {{#each group in selectiveWeekReservations}}
                        {{> _reservationCard reservation=group type='group'}}
                    {{/each}}
                {{else}}
                    <p>No reservations found.</p>
                {{/if}}
            </div>
        </div>
    </div>
</template>
