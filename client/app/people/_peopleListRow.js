import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_peopleListRow.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
import { PeopleUtils } from '../../../lib/util/peopleUtils';
import { showModal, hideModal } from '../main';
import { processPermissions } from '../../../lib/permissions';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import _ from '../../../lib/util/underscore';
import '../moments/partials/_checkinFormModal';
import '../moments/partials/_checkoutFormModal';
import { Log } from '../../../lib/util/log';
import { HistoryAuditRecordTypes, HistoryAuditChangeTypes } from '../../../lib/constants/historyAuditConstants'
import './components/updateRelationships/updateRelationships';

function pendingFamilyCheckinCheck(org, person) {
	return org && org.hasCustomization("people/familyCheckin/enabled") &&
		!person.checkedIn && person.currentFamilyCheckin();
}

Template._peopleListRow.helpers({
	getPersonName(person) {
		return PeopleUtils.getNamePreferredLast(person);
	},
	'getDropDownSelected': function () {
		return Template.instance().showDropdownTemplate.get();
	},
	'hasSeparator': function () {
		return this.person.type == "person";
	},
	'showEngagement': function () {
		const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
		var currentUserType = currentPerson && currentPerson.type;

		if (this.person.hasEngagement && (currentUserType == "admin" || currentUserType == "staff")) {

			return true; // switch to true
		}

	},
	'getCheckButtonIcon': function () {
		if (this.person.checkedIn) return "fa-check-circle";
		if (this.person.type == "person" && (this.person.reservationCancellationToday() || this.person.absent())) return "fa-user-times"
		if (pendingFamilyCheckinCheck(Orgs.current(), this.person)) return "fa-user-clock";
		return "fa-minus-hexagon";
	},
	'getCheckButtonText': function () {
		if (this.person.checkedIn) return "Checked In";
		if (this.person.type == "person" && (this.person.reservationCancellationToday() || this.person.absent())) return "Absent"
		if (pendingFamilyCheckinCheck(Orgs.current(), this.person)) return "Informed Arrival";
		return "Not Checked In";
	},
	'getCheckButtonColor': function () {
		if (this.person.checkedIn) return "btn-success";
		if (this.person.type == "person" && (this.person.reservationCancellationToday() || this.person.absent())) return "btn-primary"
		if (pendingFamilyCheckinCheck(Orgs.current(), this.person)) return "btn-warning";
		return "btn-secondary";
	},
	'lastMomentData': function () {
		if (this.person.type == "person") {
			if (this.person.lastMoment && this.person.lastMoment.momentTypePretty) {
				var x = moment(this.person.lastMoment.sortStamp), y = moment(), z = moment.duration(y - x).humanize();
				return {
					type: this.person.lastMoment.momentTypePretty,
					timeDescriptionText: z
				}
			} else
				return {
					type: "N/A",
					timeDescriptionText: ""
				};
		}
	},
	recentMomentData() {
		if (this.person.type != "person") return;
		const group = Groups.findOne({ _id: this.person.defaultGroupId || this.person.checkInGroupId });
		if (!group) return;

		const recentMomentTypes = ["potty", "food", "sleep"];
		let recentMomentData = [];
		_.each(recentMomentTypes, (mt) => {
			if (this.person.lastMomentByType && this.person.lastMomentByType[mt]) {

				const thisMoment = this.person.lastMomentByType[mt];
				const x = moment(thisMoment.sortStamp), y = moment(), z = moment.duration(y - x);
				let i18NValue = '';
				if(mt === 'potty') {
					i18NValue = orgLanguageTranformationUtil.getMomentTypesPotty('prettyName');
				} else if(mt === 'food') {
					i18NValue = 'Food';
				} else if (mt === 'sleep') {
					i18NValue = 'Sleep'
				}

				const gsRule = group && _.find(group.groupSuggestionRules, (gsr) => { return gsr.momentType == mt; });

				recentMomentData.push({
					momentType: thisMoment.momentType,
					type: i18NValue,
					recency: z.days() > 0 ? z.humanize() + " ago" : z.hours() + ":" + (z.minutes()).pad(2),
					overLimit: gsRule && gsRule.thresholdHours && (z.asMinutes() >= (gsRule.thresholdHours * 60))
				});
			}
		});
		return recentMomentData;
	},
	'engagementWidth': function (source) {
		//grab baselines

		var familyMax = this.person.engagementFamilyMax;
		var providerMax = this.person.engagementProviderMax;

		if (source == "family" && familyMax > 0)
			return parseInt(this.person.engagementFamilyCount / familyMax * 100) / 2;
		else if (source == "provider" && providerMax > 0)
			return parseInt(this.person.engagementProviderCount / providerMax * 100) / 2;
		else
			return 0;
	},
	'peopleLinkQuery': function () {
		return "r=" + Iron.Location.get().path;
	},
	'pendingFamilyCheckin': function () {
		const org = Orgs.current();
		return org && org.hasCustomization("people/familyCheckin/enabled") &&
			!this.person.checkedIn && this.person.currentFamilyCheckin();
	},
	'personAbsentText': function (person) {
		if (person.absentComment && person.absentReason) {
			return `${person.absentReason}: ${person.absentComment}`
		} else {
			return person.absentReason
		}
	},
	getAllergies: function(person){
		return getStandardOutlookData(person, 'allergies');
	},
	hasImportantNotesOrSpecialNeeds: function (person){
		return !!getStandardOutlookData(person, 'importantNotes') || !!getStandardOutlookData(person, 'specialNeeds');
	},
	importantNotesAndSpecialNeedsTooltipText: function(person){
		const importantNotes = getStandardOutlookData(person, 'importantNotes');
		const specialNeeds = getStandardOutlookData(person, 'specialNeeds');
		let textArray = [];
		if(importantNotes)  textArray.push(`Important Notes: ${importantNotes}`);
		if(specialNeeds)  textArray.push(`Special Needs: ${specialNeeds}`);

		return textArray.join(" <br> ");
	},
	'isGreyedOut': function () {
		return Template.instance().data.greyedOut === true;
	},
	'isClickable': function (person, source) {
		return (person.clickable || source !== 'people')
	},
	'hasFamilyRelationshipsCustomization': function () {
		return  Orgs.current().hasCustomization("people/showFamilyRelationships")
	},
	'loggedInUserIsAParent': function () {
		const meteorUser = Meteor.user()
		const currentPerson = meteorUser && meteorUser.fetchPerson();
		return currentPerson.type === 'family'
	}
});

Template._peopleListRow.events({
	'click #updateRelationshipsBtn': function(event, template) {
		event.preventDefault();
		event.stopImmediatePropagation();
		showModal("updateRelationships", {
			children: template.children.get(),
			personName: `${template.data.person.firstName} ${template.data.person.lastName}`,
			handleSave: async (data) => {
				const meteorUser = Meteor.user()
				const currentPerson = meteorUser && meteorUser.fetchPerson();
				const loggedInUserFullName = `${currentPerson.firstName} ${currentPerson.lastName}`
				const userBeingEdited = `${template.data.person.firstName} ${template.data.person.lastName}`
				const currentOrg = Orgs.current();
				
				await Meteor.callAsync('getOrgFamilyRegistrationSettings', currentOrg._id).then(async (results) => {
					const minimumContacts = parseInt(results.requiredContactsCount) ?? 0;
					
					// Map<childId, Set<contactPersonId>>
					const childContactsMap = new Map();
					
					Object.values(data).forEach(child => {
						// Initialize a Set for this child
						if (!childContactsMap.has(child._id)) {
							childContactsMap.set(child._id, new Set());
						}
						
						const contactsSet = childContactsMap.get(child._id);
						
						// Add the current person (template.data.person._id) as a contact if they have
						// any of the qualifying relationship types in the current changes
						if (
							child.isPrimaryCaregiver === true || 
							child.isAuthorizedPickup === true || 
							child.isEmergencyContact === true
						) {
							// Add the current person (who is being edited) as a contact
							contactsSet.add(template.data.person._id);
						}
					});
					
					// Check existing relationships for each child
					const childrenWithInsufficientContacts = [];
					await Promise.all(Object.values(data).map(async (child) => {
						try {
							// Get all existing relationships for this child
							const relationships = await Meteor.callAsync('getTargetRelationships', child._id);
							
							// Get the Set for this child's contacts (already includes changes from data)
							const contactsSet = childContactsMap.get(child._id);
							
							// Process each person's relationships
							Object.entries(relationships).forEach(([personId, personData]) => {
								// Skip the current person being edited (already handled from data)
								if (personId === template.data.person._id) {
									return;
								}
								
								// Check if this person has any qualifying relationship with the child
								const hasQualifyingRelationship = personData.relationships.some(rel => 
									rel.primaryCaregiver === true || 
									rel.relationshipType === 'authorizedPickup' || 
									rel.relationshipType === 'emergencyContact'
								);
								
								if (hasQualifyingRelationship) {
									contactsSet.add(personId);
								}
							});
							
							// Check if this child has enough contacts
							if (contactsSet.size < minimumContacts) {
								childrenWithInsufficientContacts.push({
									name: child.fullName,
									contactCount: contactsSet.size
								});
							}
						} catch (error) {
							Log.error("Error checking relationships for child", error);
							throw error; // Re-throw to be caught by the outer catch
						}
					})).catch(async (error) => {
						mpSwal.fire({
							title: "Error",
							text: "There was an error validating emergency contacts. Please try again.",
							icon: "error",
							confirmButtonText: "OK"
						});
						return; // Exit early on error
					});
					
					// Check if any children have insufficient contacts
					if (childrenWithInsufficientContacts.length > 0) {
						mpSwal.fire({
							title: "Insufficient Emergency Contacts",
							html: `You are required to keep ${minimumContacts} contacts on file. Please add a new contact or update an existing relationship to continue`,
							icon: "error",
							confirmButtonText: "OK"
						});
						return; // Exit early if validation fails
					}
					Object.values(data).forEach(child => {
						// Find the original child data to get previous state
						const originalChild = template.children.get().find(c => c._id === child._id);
						const previousRelationships = originalChild?.relationshipType || [];
						const currentRelationships = PeopleUtils.formatRelationshipListWithConjunction(child);
						const currentRelationshipString = PeopleUtils.mapChildRolesToRelationships(currentRelationships);
						const detailsString = `${loggedInUserFullName} changed ${userBeingEdited}'s relationship types to ${child.fullName} to ${currentRelationshipString}`;
	
						const historyOptions = {
							personId: child._id,
							orgId: Orgs.current()._id,
							callbackString: HistoryAuditRecordTypes.RELATIONSHIP,
							performedByUser: currentPerson,
							performedByName: `${currentPerson.firstName} ${currentPerson.lastName}`,
							previousState: { relationshipType: previousRelationships, _id: child._id, orgId: Orgs.current()._id },
							currentState: { relationshipType: currentRelationships, _id: child._id, orgId: Orgs.current()._id},
							details: detailsString
						};
	
						Meteor.callAsync('logHistory', historyOptions).then((result) => {
							Log.info("History record created successfully:", result);
						}).catch((error) => {
							Log.info("Error creating history record for person insert", error);
						});
					});
	
					await Meteor.callAsync('editMultipleRelationships', data, template.data.person._id);
					$("#updateRelationships").modal("hide");
	
					// Refresh children data
					const updatedChildren = await Meteor.callAsync('getChildrenForUpdateRelationshipsModal', template.data.person._id);
					template.children.set(updatedChildren);
				})
			}
		}, "#updateRelationships");
	},
	'click .on-hold-profile': function(event) {
		event.preventDefault();
		mpSwal.fire('Thank you for submitting your registration. Our team is verifying your information, which typically takes 24-48 hours. We will reach out once everything is confirmed or additional information is needed. Your patience is greatly appreciated.');
	},
	'click #checkin-btn': function (event, template) {
		if (!processPermissions({
			assertions: [{ context: "people/movement", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff"
		})) return false;

		Session.set("currentId", this.person._id);
		if (this.person.checkedIn) {
			showModal("_checkoutFormModal", { personId: this.person._id }, "#_checkoutFormModal")
		} else {
			Session.set("currentGroupId", this.person.defaultGroupId);
			showModal("_checkinFormModal", { personId: this.person._id }, "#_checkinFormModal")
		}
	},
	'click .card-custom': function (event, template) {
		var rowDropdown = $(`#checkInDropdown-${template.data.person._id}`);
		if (!$(event.target).closest('.click-dropdown').length && !rowDropdown.hasClass("show")) {
			if (this.person.clickable) {
				FlowRouter.go('person', { "_id": $(event.currentTarget).attr("data-id") });
			}
			if (template.data.source == "_rosterModal") {
				hideModal("#_rosterModal");
			}
		}
	}
});

Template._peopleListRow.onCreated(function () {
	var self = this;
	self.showDropdownTemplate = new ReactiveVar(false);
	//TODO: setup form to modify checkouts;
	this.children = new ReactiveVar([]);
	if (this.data.person.type === 'family'){
		Meteor.callAsync('getChildrenForUpdateRelationshipsModal', this.data.person._id)
			.then((res) => {
				this.children.set(res);
			})
			.catch(error => {
				Log.error('Error getting people from relationships:', error);
			});
	}
});

Template._peopleListRow.onRendered(function () {
	var template = Template.instance();
	$('[data-toggle="tooltip"]').tooltip();
	var self = this;
	$(`#checkInDropdown-${this.data.person._id}`).on('show.bs.dropdown', function (event) {
		template.showDropdownTemplate.set(true);
	});

	$(`#checkInDropdown-${this.data.person._id}`).on('hide.bs.dropdown', function (event) {
		template.showDropdownTemplate.set(false);
	});
});

/**
 * There are two ways a person object can exist, this checks for both versions
 * @param person
 * @param variable
 */
function getStandardOutlookData(person, variable){
	return person?.standardOutlook?.[variable] || person?.profileData?.standardOutlook?.[variable]
}