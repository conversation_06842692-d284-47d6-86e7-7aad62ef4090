import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_medications.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { showModal } from '../main';
import './_addMedicationModal';

Template._medications.helpers({
  canManageMedications: function() {
    const meteorUser = Meteor.user(), currentPerson = meteorUser && meteorUser.fetchPerson();
    return (currentPerson && currentPerson.type=="admin");
  },
});

Template._medications.events({
  'click #btnAddMedication': function(e) {
    e.preventDefault();
    var personId = FlowRouter.current().params._id;
    showModal("_addMedicationModal", { personId }, "#_addMedicationModal");
  },
  'click .btnDeleteMedication': function(e) {
    e.preventDefault();
    const personId = Template.parentData()._id;
    const medicationId = $(e.currentTarget).attr("data-id");

    mpSwal.fire({
      title: "Are you sure?",
      text: "This will remove the selected medication from the profile",
      icon: "warning",
      showCancelButton: true
    }).then( async (result) => {
      if (result.value) {
        await Meteor.callAsync("removeMedication", {
          personId: personId,
          medicationId: medicationId
        });
      }
    });
  },
  'click .btnEditMedication': function(e) {
    const medicationId = $(e.currentTarget).attr("data-id");
    var personId = FlowRouter.current().params._id;
    const m = this;
    showModal("_addMedicationModal", { personId, m }, "#_addMedicationModal");
  },
})
