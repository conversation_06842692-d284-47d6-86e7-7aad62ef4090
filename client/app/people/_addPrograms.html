<template name="_addPrograms">
    <div class="container px-4">
        <div class="row d-flex justify-content-center mx-4">
            <div class="btn-group-toggle" data-toggle="buttons">
                {{#each program in programs}}
                    <label data-cy="programs" class="btn btn-lg program-button {{activeButton @index}}" data-id="{{@index}}">
                        <input type="radio" name="{{program.name}}" />
                        {{program.name}}
                    </label>
                {{/each}}
            </div>
        </div>
        <div class="row mt-5 d-flex flex-column align-items-center mx-4">
            {{#each plan in plans}}
                <div data-cy="plans" class="col my-2">
                    {{> planCard plan=plan parentData=parentData selectedPlans=selectedPlans}}
                </div>
            {{/each}}
        </div>
    </div>
</template>