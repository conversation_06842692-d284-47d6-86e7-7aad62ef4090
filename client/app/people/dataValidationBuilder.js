import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './dataValidationBuilder.html';
import _ from '../../../lib/util/underscore';
var dot = require('dot-object');
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {getPeopleData} from "../../services/peopleMeteorService";
import { People } from '../../../lib/collections/people';
import { Orgs } from '../../../lib/collections/orgs';
import '../../layout/loading';
import { PeopleDataValidations } from '../../../lib/collections/peopleDataValidation';

async function getProfileValues(personType, allAvailable = false) {
  const collapsedProfileFieldLis = await People.getCollapsedProfileFieldList(personType);
  return  _.chain((collapsedProfileFieldLis))
				.filter((f) => {
          if (!allAvailable && f.visibleOnlyToRoles && _.contains(f.visibleOnlyToRoles, "admin") && !_.contains(f.visibleOnlyToRoles, personType)) return false;
          if (f.name == "defaultGroupName" || f.name == "attachedEmail") return false;
          return f.type != "attachments" && f.type !="file"; 
        })
				.sortBy("prefixedLabel")
				.map( (f) => { return {id: f.prefixedName, label:f.prefixedLabel, type: f.type, description: f.description};})
				.value();
}

async function updateValues(personType) {

	const values = await getProfileValues(personType),
    allValues = await getProfileValues(personType, true)
		curControl = $("#validation-filter")[0],
    reqControl = $('#required-filter')[0],
    secControl = $("#validation-report-only-fields")[0];
	
  console.log(values);
	if (curControl && curControl.selectize)  
		curControl.selectize.destroy();
  
  if (secControl && secControl.selectize)
    secControl.selectize.destroy();
  
  if (reqControl && reqControl.selectize)
    reqControl.selectize.destroy();

	$("#validation-filter").selectize({ 
    maxItems: null,
		valueField: 'id',
		labelField: 'label',
		searchField: 'label',
	  options: values,
  });

  $("#required-filter").selectize({ 
    maxItems: null,
		valueField: 'id',
		labelField: 'label',
		searchField: 'label',
	  options: values,
  });
  
  $("#validation-report-only-fields").selectize({ 
    maxItems: null,
    valueField: 'id',
    labelField: 'label',
    searchField: 'label',
    options:allValues
  });
	
}

Template.dataValidationBuilder.helpers({
  isNew() {
    const dvId = FlowRouter.current().params._id;
    return !dvId;
  },
  dataValidationRecord() {
    const dvId = FlowRouter.current().params._id,
      record = dvId && PeopleDataValidations.findOne(dvId);
    
    return record;
  },
  showReportChildrenFields() {
    const org = Orgs.current();
    if (org && org.valueOverrides && org.valueOverrides.reportChildDataValidationFields) return true;
    return false;
  },
  
})

Template.dataValidationBuilder.events({
  "click #btn-create-validation": (e,i) => {
    e.preventDefault();
    const selectedProfileFields = $("#validation-filter").val();
    
    const childReportProfileFields = $("#checkReportChildFields").prop("checked");
    const ongoing = $("#checkOngoingValidation").prop("checked");
    
    Meteor.callAsync("insertPeopleDataValidation", {
      title: $("#validation-title").val(),
      description: $("#validation-description").val(),
      profileFields: $("#validation-filter").val(),
      requiredFields: $("#required-filter").val(),
      reportProfileFields: $("#validation-report-only-fields").val() || [],
      audience: $("#validation-audience").val(),
      childReportProfileFields,
      ongoing,
    }).then((result) => {
      FlowRouter.redirect("/people/data-validation/" + result);
    }).catch((error) => {
      mpSwal.fire("Error", error.reason, "error");
    });
  },
  "change #validation-audience": async (e) => {
    e.preventDefault();
    await updateValues($("#validation-audience").val());
  }
});

Template.dataValidationBuilder.rendered = async function() {
	await updateValues("family");
}


Template.dataValidationBuilderUpdatePanel.onCreated( function() {
	this.profileValues = new ReactiveVar([]);
  this.allProfileValues = new ReactiveVar([]);
  this.childProfileValues = new ReactiveVar([]);
  this.hasChildReportFields = new ReactiveVar(false);
  this.hasOngoingProperty = new ReactiveVar(false);
  this.peopleRes = new ReactiveVar([])

  const personIds = this.data.record?.responses?.map((r)=>{ return r.personId })
  this.autorun(() => {
    getPeopleData({_id:{$in:personIds}}, {"fields": { _id: 1, lastName: 1, firstName: 1 }}).then((res) => {
      if (res) {
        this.peopleRes.set(res);
      }
    }).catch(err => {
      console.log(err);
    });
  })
});

Template.dataValidationBuilderUpdatePanel.rendered = async function() {
  if(this.data && this.data.record) {
    var values = await getProfileValues(this.data.record.audience);
    var allValues = await getProfileValues(this.data.record.audience, true);
    var childValues = await getProfileValues("person", true);
    this.profileValues.set(values);
    this.allProfileValues.set(allValues);
    this.childProfileValues.set(childValues);
    
    if (this.data.record.childReportProfileFields) this.hasChildReportFields.set(true);
    if (this.data.record.ongoing) this.hasOngoingProperty.set(true);
    
    var selectedFields = this.data.record.profileFields || [];
    let requiredFields = this.data.record.requiredFields || [];
    var selectedReportFields = this.data.record.reportProfileFields || [];
    var curControl = $("#static-validation-filter")[0];
    if (curControl && curControl.selectize)  
      curControl.selectize.destroy();
    
    var secControl = $("#static-validation-report-only-fields")[0];
    if (secControl && secControl.selectize)  
      secControl.selectize.destroy();

    var reqControl = $("#static-required-filter")[0];
    if (reqControl && reqControl.selectize)  
      reqControl.selectize.destroy();
    
    $("#static-validation-filter").selectize({ 
      valueField: 'id',
  		labelField: 'label',
  		searchField: 'label',
  	  options: values,
      create: false,
      persist: false,
      items: selectedFields
    });

    $("#static-required-filter").selectize({ 
      valueField: 'id',
  		labelField: 'label',
  		searchField: 'label',
  	  options: values,
      create: false,
      persist: false,
      items: requiredFields
    })
    
    $("#static-validation-report-only-fields").selectize({ 
      valueField: 'id',
      labelField: 'label',
      searchField: 'label',
      options: allValues,
      create: false,
      persist: false,
      items: selectedReportFields
    })
  }
}

Template.dataValidationBuilderUpdatePanel.events({
  "click #btnExportCsv": function() {
    var outputFile = 'export.csv'
      
    // CSV
    exportTableToCSV.apply(this, [$('#dv-data > table'), outputFile]);
    
  },
  "click #btn-update-validation": (e) => {
    e.preventDefault();
    const dvId = FlowRouter.current().params._id;
    
    Meteor.callAsync("updatePeopleDataValidation", {
      _id: dvId,
      title: $("#dv-update-validation-title").val(),
      description: $("#dv-update-validation-description").val(),
    }).then((result) => {
      mpSwal.fire({icon:"success", title:"Save Succsessful"});
    }).catch((error) => {
      mpSwal.fire("Error", error.reason, "error");
    });
  },
});

Template.dataValidationBuilderUpdatePanel.helpers({
  "getPersonName": (response) => {
    const peopleRes = Template.instance().peopleRes.get();
    if(peopleRes.length){
      const p =  peopleRes[peopleRes.findIndex(p => p._id === response.personId)];
      return `${p.firstName} ${p.lastName}`;
    }
      
  },
  "getFieldDescription": (field) => {
    var profileValueDef = Template.instance().profileValues.get();
    var currentDef = _.find(profileValueDef, (obj) => { return obj.id == field });
    if (currentDef && currentDef.description) return currentDef.description;
    return field;
  },
  "formatResponseData": (field, response) => {
    var profileValueDef = Template.instance().profileValues.get();
    var currentDef = _.find(profileValueDef, (obj) => { return obj.id == field });
    let f = dot.pick(field, response);
    if (currentDef && currentDef.type == "date" && f) {
      return new moment(f).format("MM/DD/YYYY");
    }    
    return f;
  },
  "getChildReports": (report) => {
    console.log(report);
    if (report && report.childReportProfileFields) return report.childReportProfileFields;
    return []
  },
  "showReportChildFields": () => {
    const org = Orgs.current();
    if (org && org.valueOverrides && org.valueOverrides.reportChildDataValidationFields) return true;
    return false;
  },
  "hasReportChildFields": () => {
    return Template.instance().hasChildReportFields.get();
  },
  "hasOngoingProperty": () => {
    return Template.instance().hasOngoingProperty.get()
  },
  "formatResponseDataChild": (field, response) => {
    var profileValueDef = Template.instance().childProfileValues.get();
    var currentDef = _.find(profileValueDef, (obj) => { return obj.id == field });
    let f = dot.pick(field, response);
    if (currentDef && currentDef.type == "date" && f) {
      return new moment(f).format("MM/DD/YYYY");
    }    
    return f;
  },
  "getChildFieldDescription": (field) => {
    var profileValueDef = Template.instance().childProfileValues.get();
    var currentDef = _.find(profileValueDef, (obj) => { return obj.id == field });
    if (currentDef && currentDef.description) return currentDef.description;
    return field;
  },
});
