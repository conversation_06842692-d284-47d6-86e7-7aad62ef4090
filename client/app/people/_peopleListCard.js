import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import './_peopleListCard.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Orgs } from '../../../lib/collections/orgs';
import { hideModal, showModal } from '../main';
import { processPermissions } from '../../../lib/permissions';
import { Session } from 'meteor/session';
import '../moments/partials/_checkinFormModal';
import '../moments/partials/_checkoutFormModal';

function pendingFamilyCheckinCheck(org, person) {
  return org && org.hasCustomization("people/familyCheckin/enabled") &&
    !person.checkedIn && person.currentFamilyCheckin();
}

Template._peopleListCard.helpers({
  'getCheckButtonIcon': function () {
    if (this.person.checkedIn) return "fa-check-circle";
    if (this.person.type == "person" && (this.person.reservationCancellationToday() || this.person.absent())) return "fa-user-times"
    if (pendingFamilyCheckinCheck(Orgs.current(), this.person)) return "fa-user-clock";
    return "fa-minus-hexagon";
  },
  'getCheckButtonText': function () {
    if (this.person.checkedIn) return "Checked In";
    if (this.person.type == "person" && (this.person.reservationCancellationToday() || this.person.absent())) return "Absent"
    if (pendingFamilyCheckinCheck(Orgs.current(), this.person)) return "Informed Arrival";
    return "Not Checked In";
  },
  'getCheckButtonColor': function () {
    if (this.person.checkedIn) return "btn-success";
    if (this.person.type == "person" && (this.person.reservationCancellationToday() || this.person.absent())) return "btn-primary"
    if (pendingFamilyCheckinCheck(Orgs.current(), this.person)) return "btn-warning";
    return "btn-secondary";
  },
  "isSelected": function () {
    return Template.instance().selected.get();
  },
  "onlyGridViewActive": function () {
    return this.gridViewActive && (!this.selectMultiple && !this.nameToFace);
  },
  'personAbsentText': function (person) {
    if (person.absentComment && person.absentReason) {
      return `${person.absentReason}: ${person.absentComment}`
    } else {
      return person.absentReason
    }
  }
});

Template._peopleListCard.events({
  'click .card-custom': function (e, i) {
    if (this.gridViewActive && (!this.selectMultiple && !this.nameToFace)) {
      if (!$(event.target).closest('.checkin-wrapper').length) {
        FlowRouter.go(`/people/${this.person._id}#profile`)
        hideModal("#_rosterModal");
      }
    } else {
      if ($(`#${this.person._id}-list-card`).hasClass("mp-selected-card")) {
        $(`#${this.person._id}-list-card`).removeClass("mp-selected-card");
        $(`#${this.person._id}-list-card`).removeClass("bg-success-o-80");
      } else {
        $(`#${this.person._id}-list-card`).addClass("mp-selected-card");
        $(`#${this.person._id}-list-card`).addClass("bg-success-o-80");
      }
    }
  },
  'click #card-checkin-button': function (event, template) {
    if (this.gridViewActive && (!this.selectMultiple && !this.nameToFace)) {
      if (!processPermissions({
        assertions: [{ context: "people/movement", action: "edit" }],
        evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff"
      })) return false;

      Session.set("currentId", this.person._id);
      if (this.person.checkedIn) {
        showModal("_checkoutFormModal", { personId: this.person._id }, "#_checkoutFormModal")
      } else {
        Session.set("currentGroupId", this.person.defaultGroupId);
        showModal("_checkinFormModal", { personId: this.person._id }, "#_checkinFormModal")
      }
    }
  },

});

Template._peopleListCard.onCreated(function () {
  var self = this;
  self.selected = new ReactiveVar(false);
});
