<template name="_addRelationshipModal">
  <div id="_addRelationshipModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">{{#unless isEditing}}New{{else}}Edit{{/unless}} Relationship</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="frmAddRelationship">
            {{#if isEditing}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Name</label>
                <div class="col-lg-9 col-xl-6">
                  <input type="text" class="form-control form-control-lg form-control-solid" value="{{getFullName}}" disabled >
                </div>
              </div>
            {{else}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Find Existing Person</label>
                <div data-cy="existing-person-select" class="col-lg-9 col-xl-6">
                  <select data-cy="existing-person-search" class="form-control form-control-lg form-control-solid" id="existingPersonSearch">
                    {{#each people}}
                      <option value="{{_id}}">{{firstName}} {{lastName}} {{associatedEmail}}</option>
                    {{/each}}
                  </select>
                </div>
              </div>
            {{/if}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Type</label>
              <div class="col-lg-9 col-xl-6">
                <select class="form-control form-control-lg form-control-solid" name="relationshipType" id="inputRelationshipType" data-cy="add-relationship-type" multiple>
                  <option value="family" {{selectedIfContains relationships "family"}}>Family</option>
                  <option value="authorizedPickup" {{selectedIfContains relationships "authorizedPickup"}}>Authorized Pickup</option>
                  <option value="emergencyContact" {{selectedIfContains relationships "emergencyContact"}}>Emergency Contact</option>
                </select>
              </div>
            </div>
            {{#if isFamilyRelationship}}
                <div class="form-group row">
                    <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
                    <div class="col-lg-9 col-xl-6">
                        <div class="checkbox-inline">
                            <label class="checkbox checkbox-primary">
                              <input data-cy="chk-primary-caregiver" type="checkbox" id="primary_caregiver" name="primary_caregiver" {{checkedIfEq true isPrimaryCaregiver}}>
                              <span></span>
                              Primary Caregiver
                            </label>
                        </div>
                    </div>
                </div>
            {{/if}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Description (e.g. grandparent, etc.)</label>
              <div class="col-lg-9 col-xl-6">
                <input data-cy="relationship-desc" type="text" class="form-control form-control-lg form-control-solid" name="relationshipDescription" id="inputRelationshipDescription" value="{{getRelationshipDescription}}">
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <!-- NOTE: the save button should execute on the yielded _personAccount JS -->
          <div data-cy="add-relationship-save-btn" class="btn btn-primary font-weight-bolder mr-2" id="btnSaveRelationship">Save</div>
          <div class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</div>
        </div>
  		</div>
  	</div>
  </div>
</template>
