<template name="_peopleListRow">
  <!--begin::Card-->
    {{#unless isGreyedOut}}
	<div data-cy="people-item" class="card card-custom clickable-row gutter-b my-2" data-id="{{person._id}}">
		<div class="card-body py-3">
			<!--begin::Top-->
			<div class="d-flex">
				<!--begin::Pic-->
				<div class="flex-shrink-0 mr-7">
          
          {{#unless person.hasAvatar}}
              <div class="d-flex avatar-circle align-items-center justify-content-center"
                   style="background-color:{{getAvatarBackground person.personInitials}}">
                  <span data-cy="initials" class="initials">{{person.personInitials}}</span>
              </div>
          {{else}}
            <div class="people-list-user-img" style="background-image:url({{person.getAvatarUrl}})"></div>
          {{/unless}}
				</div>
				<!--end::Pic-->
				<!--begin: Info-->
				<div class="flex-grow-1">
					<!--begin::Title-->
					<div class="d-flex align-items-center justify-content-between flex-wrap mt-2">
						<!--begin::User-->
						<div class="mr-3">
							<!--begin::Name-->
                            <div class="flex flex-row">
                                {{#if isClickable person source}}
                                    <a href="/people/{{person._id}}" class="text-dark text-hover-primary font-size-h5 font-weight-bold" data-cy="person-name">
                                        {{ getPersonName person }}
                                    </a>
                                {{else}}
                                    <span class="text-dark font-size-h5 font-weight-bold" data-cy="person-name">
                                        {{ getPersonName person }}
                                    </span>
                                {{/if}}
                                {{#if getAllergies person}}
                                    <i class="fa fa-solid fa-plus fa-lg ml-2" style="color: #C50400;" data-html="true"
                                       data-toggle="tooltip" data-placement="right" title="Allergy: {{getAllergies person}}"></i>
                                {{/if}}
                                {{#if hasImportantNotesOrSpecialNeeds person}}
                                     <i class="fad-regular fad fa-info-circle fa-lg ml-2" style="color: #77767b;" data-html="true"
                                        data-toggle="tooltip" data-placement="right" title="{{importantNotesAndSpecialNeedsTooltipText person}}"></i>
                                {{/if}}
                            </div>
							<!--end::Name-->
							<!--begin::Contacts-->
							<div class="d-flex flex-wrap my-2">
                <i class="icon text-bright-blue fad fa-user-circle mr-2"></i>
                <span class="text-muted font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2" data-cy="person-type">{{translationForEntityType person.type}}</span>
                {{#if person.inActive}}
                  <span class="text-danger font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">deactivated</span>
                {{/if}}
                {{#if person.isCheckInAble}}
                  {{#if person.checkedIn}}
                    <i class="icon text-bright-blue fad fa-users mr-2"></i>
                    <span class="text-muted font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">{{person.findCheckedInGroup.name}}</span>
                  {{/if}}
                  <i class="icon text-bright-blue fad fa-calendar mr-2"></i>
                  <span class="text-muted font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">
                    
                    <span class="text-muted font-weight-bold mr-2">{{person.recurringDays}}</span>
                  </span>
                {{/if}}
							</div>
							<!--end::Contacts-->
                            {{#if person.isAbsentToday}}
                            <div style="max-width: 700px;">
                                <span style="word-wrap: break-word">{{personAbsentText person}}</span>
                            </div>
                            {{/if}}
						</div>
						<!--begin::User-->
						<!--begin::Actions-->
						<div class="my-lg-0 my-1">
              {{#if person.isCheckInAble}}
              <div class="click-dropdown" id="checkInDropdown-{{person._id}}">
                <div class="btn {{getCheckButtonColor}} font-weight-bolder btn-text-white min-w-175px" id="checkin-btn" data-id="{{person._id}}" data-cy="checkin-btn">
                  <i class="fad-regular fad {{getCheckButtonIcon}} fa-swap-opacity mr-2" style="color:#fff"></i>{{getCheckButtonText}}
                </div>
              </div>
              {{/if}}
              {{#if loggedInUserIsAParent}}
                {{#if hasFamilyRelationshipsCustomization}}
                  {{#if trueIfEq person.type "family"}}
                    <button data-cy="edit-relationship-btn" class="btn btn-light-primary font-weight-bolder px-6 min-w-150px shadow-sm hover:shadow-md transition-shadow" id="updateRelationshipsBtn">
                      <i class="fad fa-users fa-lg mr-3 text-primary-600"></i>Edit Relationships
                    </button>
                  {{/if}}
                {{/if}}
              {{/if}}
						</div>
						<!--end::Actions-->
					</div>
					<!--end::Title-->
					<!--begin::Content-->
					<!--end::Content-->
				</div>
				<!--end::Info-->
			</div>
			<!--end::Top-->
			<!--begin::Separator-->
      {{#if hasSeparator}}
        <div class="separator separator-solid my-4"></div>
        <!--end::Separator-->
        <!--begin::Bottom-->
        <div class="d-flex">
          {{#if recentMomentData}}
            <div class="d-flex flex-grow-1 align-items-center flex-wrap">
              {{#each recentMoment in recentMomentData}}
              <!--begin: Item-->
              <div class="d-flex align-items-center mr-10 my-1">
                <span class="mr-4">
                  <i class="icon-2x {{#if recentMoment.overLimit}}text-danger{{else}}text-bright-blue{{/if}} fad {{iconForType recentMoment.momentType}}"></i>
                </span>
                <!-- <span class="mr-4">
                  <i class="flaticon-piggy-bank icon-2x text-muted font-weight-bold"></i>
                </span> -->
                <div class="d-flex flex-column text-dark-75">
                  <span class="font-weight-bolder font-size-sm {{#if recentMoment.overLimit}}text-danger{{/if}}">{{recentMoment.type}}</span>
                  <span class="font-weight-bolder font-size-h5 {{#if recentMoment.overLimit}}text-danger{{/if}}">{{recentMoment.recency}}</span>
                </div>
              </div>
              <!--end: Item-->
              {{/each}}
            </div>
          {{/if}}
          <div class="d-flex flex-grow-1 align-items-center justify-content-end flex-wrap">
            {{#if showEngagement}}
              <div class="d-flex align-items-center flex-wrap justify-content-between">
                <!--begin::Progress-->
                <div class="d-flex flex-column mt-4 mt-sm-0">
                  <span class="font-weight-bolder font-size-sm">Engagement</span>
                  <div class="progress" style="width:200px">
                      <div class="progress-bar bg-primary" role="progressbar" style="width:{{engagementWidth "provider"}}%;" title="provider" data-toggle="tooltip"></div>
                      <div class="progress-bar bg-info" role="progressbar" style="width:{{engagementWidth "family"}}%;" title="family" data-toggle="tooltip"></div>
                  </div>
                </div>
                <!--end::Progress-->
              </div>
            {{/if}}
          </div>
        </div>
        <!--end::Bottom-->
      {{/if}}
		</div>
	</div>
    {{else}}
        <div class="card card-custom on-hold-profile clickable-row gutter-b my-2">
            <div class="card-body py-3">
                <!--begin::Top-->
                <div class="d-flex">
                    <!--begin::Pic-->
                    <div class="flex-shrink-0 mr-7">
                        <div class="d-flex avatar-circle align-items-center justify-content-center"
                             style="background-color: grey">
                            <span class="initials">{{person.personInitials}}</span>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center justify-content-between flex-wrap mt-2">
                            <div class="mr-3">
                                <div class="flex flex-row">
                                    <span class="text-dark text-hover-primary font-size-h5 font-weight-bold">{{ getPersonName person }}</span>
                                </div>
                                <div class="d-flex flex-wrap my-2">
                                    <i class="icon text-bright-blue fad fa-user-circle mr-2"></i>
                                    <span class="text-muted font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">{{translationForEntityType person.type}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="separator separator-solid my-4"></div>
            </div>
        </div>
    {{/unless}}
	<!--end::Card-->
</template>
