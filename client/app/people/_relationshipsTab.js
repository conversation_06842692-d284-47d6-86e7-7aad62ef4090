import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_relationshipsTab.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
import { CustomizationsService } from '../../../lib/customizations';
import { AvailableActionTypes, AvailablePermissions } from '../../../lib/constants/permissionsConstants';
import { Log } from '../../../lib/util/log';
import { processPermissions } from '../../../lib/permissions';
import { showModal } from '../main';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';
import { Person, People } from '../../../lib/collections/people';
import './_addRelationshipModal';
import './_addRelationshipNewPerson';
import { Relationships, Relationship } from '../../../lib/collections/relationships';
import { offlinePreventCheck } from '../../../mpweb';
import { StringUtils } from '../../../lib/util/stringUtils';
import { MethodDataManager } from '../../../lib/methodDataManager';

Template._relationshipsTab.onCreated(function () {
  this.selectedRelationships = new ReactiveVar({});
  this.refreshTrigger = new ReactiveVar(0);
  this.targetPerson = new ReactiveVar();
  this.inheritedRelationships = new ReactiveVar();

  this.autorun(async () => {
    const targetId = this.data._id;
    this.refreshTrigger.get();

    const person = await People.find(targetId).fetchAsync();
    const relationships = await person[0].findInheritedRelationships().fetchAsync();
    this.inheritedRelationships.set(relationships);

    Meteor.callAsync('getTargetRelationships', targetId)
      .then((result) => {
        this.selectedRelationships.set(result);
      })
      .catch((error) => {
        Log.error("Couldn't get selected relationships", error);
      });

    MethodDataManager.fetchRelationships();
  });
});

Template._relationshipsTab.events({
  'click #newRelationshipLink': function (event, template) {
    if (offlinePreventCheck()) return false;
    const personId = this._id;
    const i18NValue = orgLanguageTranformationUtil.getEntityType('person');

    // Check if the user has the 'people/add/family' permission
    const canCreateNew = processPermissions({
      assertions: [{ context: AvailablePermissions.PEOPLE_ADD_FAMILY, action: AvailableActionTypes.EDIT }],
      evaluator: (person) => person.type === 'admin'
    });

    mpSwal
      .fire({
        title: 'Add new relationship',
        text:
          'You can either create a new person or associate an existing person. If you create a new person, they will automatically be associated with this ' +
          i18NValue +
          '.',
        showCancelButton: canCreateNew,
        showCloseButton: true,
        confirmButtonText: canCreateNew ? 'Create New' : 'Associate Existing',
        cancelButtonText: canCreateNew ? 'Associate Existing' : undefined,
        confirmButtonColor: 'var(--primary)',
        cancelButtonColor: 'var(--primary)',
        focusConfirm: false,
        focusCancel: false
      })
      .then((result) => {
        if (result.value) {
          if (canCreateNew) {
            showModal('_addRelationshipNewPerson', {
              onSave: () => {
                template.refreshTrigger.set(template.refreshTrigger.get() + 1);
              }
            }, '#_addRelationshipNewPerson');
          } else {
            showModal(
              '_addRelationshipModal',
              {
                personId,
                alreadySelectedRelationships: template.selectedRelationships.get() || [],
                onSave: () => {
                  template.refreshTrigger.set(template.refreshTrigger.get() + 1);
                }
              },
              '#_addRelationshipModal'
            );
          }
        } else if (result.dismiss && result.dismiss === 'cancel' && canCreateNew) {
          showModal(
            '_addRelationshipModal',
            {
              personId,
              alreadySelectedRelationships: template.selectedRelationships.get() || [],
              onSave: () => {
                template.refreshTrigger.set(template.refreshTrigger.get() + 1);
              }
            },
            '#_addRelationshipModal'
          );
        }
      });
  },
  'click .editRelationshipLink': function (event, template) {
    const relationshipId = $(event.currentTarget).attr('data-id');
    const childId = Template.instance().data._id;

    try {
      showModal(
        '_addRelationshipModal',
        {
          personId: childId,
          relationshipId,
          onSave: () => {
            template.refreshTrigger.set(template.refreshTrigger.get() + 1);
          }
        },
        '#_addRelationshipModal'
      );
    } catch (error) {
      template.refreshTrigger.set(template.refreshTrigger.get() + 1);
    }
  },

  'click .deleteRelationshipLink': function (event, template) {
    var relationshipId = $(event.currentTarget).attr('data-id');

    mpSwal
      .fire({
        title: 'Are you sure you want to delete this relationship?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        closeOnConfirm: true
      })
      .then(async (result) => {
        if (result.value) {
          try {
            await Meteor.callAsync('deleteRelationship', relationshipId);
            template.refreshTrigger.set(template.refreshTrigger.get() + 1);
          } catch (error) {
            template.refreshTrigger.set(template.refreshTrigger.get() + 1);
          }
        }
      });
  },
  "change input[name='inputDaily']": async function (e) {
    var status = $(e.target).prop('checked');
    await Meteor.callAsync('updateSubscription', {
      id: this._id,
      momentType: 'dailySummary',
      method: 'email',
      status
    });
  },
  "change input[name='familyMessagesOptOut']": async function (e) {
    var status = $(e.target).prop('checked');
    await Meteor.callAsync('updateSubscription', {
      id: this._id,
      momentType: 'familyMessagesOptOut',
      method: 'email',
      status
    });
  },
  'change .real-time-preferences-list input:checkbox': async function (e) {
    var box = $(e.target),
      status = box.prop('checked'),
      boxName = box.val();

    var momentType = boxName.split('|')[0],
      method = boxName.split('|')[1];
    var subscriptionData = {
      id: this._id,
      status: status,
      momentType: momentType,
      method: method
    };
    await Meteor.callAsync('updateSubscription', subscriptionData);
  }
});

Template._relationshipsTab.helpers({
  isRelationshipSource: function () {
    return (
      this.type == 'person' &&
      processPermissions({
        assertions: [{ context: 'people/relationships', action: 'edit' }],
        evaluator: (thisPerson) => thisPerson.type == 'admin'
      })
    );
  },
  checkedIfSubscribed: function (momentType, method) {
    return this.subscriptions && this.subscriptions[momentType] && this.subscriptions[momentType][method]
      ? true
      : false;
  },
  checkedIfReceiveDaily: function () {
    return !this.suspendDailySummaryEmails;
  },
  showSubscriptions: function () {
    return this.relationshipTypes.includes('family');
  },
  canManageRelationships: function () {
    return processPermissions({
      assertions: [{ context: 'people/relationships', action: 'edit' }],
      evaluator: (thisPerson) => thisPerson.type == 'admin'
    });
  },
  canViewContactInfo() {
    return CustomizationsService.canViewContactInfo();
  },
  showFirst(index) {
    return index === 0;
  },
  showPrimaryCaregiver: function (relationshipType, primaryCaregiver) {
    return relationshipType === 'family' && primaryCaregiver;
  },
  showEmailAndPhoneNumber: function () {
    return Orgs.current().hasCustomization('people/showFamilyRelationships');
  },
  collapsedOwnedRelationships: function () {
    if (Orgs.current().hasCustomization('people/showFamilyRelationships')) {
      const selectedRelationships = Template.instance().selectedRelationships.get();

      if (!selectedRelationships) {
        return [];
      }

      const currentUser = Meteor.user();
      const currentPerson = currentUser?.fetchPerson();
      const currentPersonId = currentPerson?._id;

      const relationships = _.values(selectedRelationships || {});
      const currentUserRelationship = relationships.find((rel) => rel.person?._id === currentPersonId);
      const otherRelationships = relationships.filter((rel) => rel.person?._id !== currentPersonId);

      if (currentUserRelationship?.person) {
        currentUserRelationship.person = new Person(currentUserRelationship.person);
      }

      const sortedOtherRelationships = _.sortBy(otherRelationships, (rel) => {
        if (!rel || !rel.person) return '';
        rel.person = new Person(rel.person);
        return ((rel.person.lastName || '') + ', ' + (rel.person.firstName || '')).trim();
      });

      return currentUserRelationship
        ? [currentUserRelationship, ...sortedOtherRelationships]
        : sortedOtherRelationships;
    }

    const collapsedList = {};
    _.each(this.findOwnedRelationships(), (r) => {
      if (!collapsedList[r.personId])
        collapsedList[r.personId] = {
          person: r.fetchPerson(),
          relationships: []
        };
      collapsedList[r.personId].relationships.push(r);
    });

    // Show 'family' relationship type first
    for (const temp in collapsedList) {
      collapsedList[temp].relationships.sort(
        (a, b) => (b.relationshipType === 'family') - (a.relationshipType === 'family')
      );
    }
    Template.instance().selectedRelationships.set(collapsedList);
    return _.sortBy(_.values(collapsedList), (rel) => {
      return rel.person.lastName + ', ' + rel.person.firstName;
    });
  },
  isCurrentUser(personId) {
    const currentUser = Meteor.user();
    const currentPerson = currentUser?.fetchPerson();

    if (currentPerson.type === 'admin') {
      return true;
    }

    return currentPerson && currentPerson._id === personId;
  },
  subscriptionTypes: function () {
    const currentOrg = Orgs.current(),
      typesList = [
        { label: 'Comment', type: 'comment' },
        { label: 'Food', type: 'food' },
        { label: 'Sleep', type: 'sleep' },
        { label: 'Potty', type: 'potty' }
      ];
    if (!currentOrg) return;
    if (currentOrg.hasCustomization('moments/activity/enabled'))
      typesList.push({ label: 'Activity', type: 'activity' });
    if (currentOrg.hasCustomization('moments/medical/enabled')) typesList.push({ label: 'Medical', type: 'medical' });
    if (currentOrg.hasCustomization('moments/learning/enabled'))
      typesList.push({ label: 'Learning', type: 'learning' });
    if (currentOrg.hasCustomization('moments/mood/enabled')) typesList.push({ label: 'Mood', type: 'mood' });
    _.chain(currentOrg.availableDynamicMomentTypes())
      .filter((cmd) => cmd.availableForRealtime)
      .each((cmd) => typesList.push({ label: cmd.momentTypePretty, type: cmd.momentType }));

    return typesList;
  },
  isPrintable() {
    return FlowRouter.getQueryParam('printable');
  },
  checkedIfFamilyMessagesOptOut: function () {
    return !!this.familyMessagesOptOut;
  },
  showFamilyMessagesOptOut: function () {
    return Relationships.find({ relationshipType: 'family', targetId: this.targetId }).fetch().length > 1;
  },
  'groupedRelationships'() {
    const relationships = Template.instance().inheritedRelationships.get();
    const grouped = _.groupBy(relationships, 'targetId');

    return Object.values(grouped).map((rels) => {
      const familyRel = rels.find((r) => r.relationshipType === 'family');
      const transformed = {
        _id: familyRel ? familyRel._id : rels[0]._id,
        personId: rels[0].personId,
        targetId: rels[0].targetId,
        relationshipTypes: rels.map((r) => r.relationshipType),
        primaryCaregiver: rels[0].primaryCaregiver,
        orgId: rels[0].orgId,
        subscriptions: familyRel ? familyRel.subscriptions : rels[0].subscriptions,
        familyMessagesOptOut: familyRel ? familyRel.familyMessagesOptOut : rels[0].familyMessagesOptOut,
        suspendDailySummaryEmails: familyRel ? familyRel.suspendDailySummaryEmails : rels[0].suspendDailySummaryEmails,
        relationshipDescription: familyRel ? familyRel.relationshipDescription : rels[0].relationshipDescription
      };
      return new Relationship(transformed);
    });
  },
  formatRelationshipTypes(types) {
    return StringUtils.formatRelationshipTypesForRelationshipTab(types);
  }
});
