import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_addMedicationModal.html';
import { hideModal } from '../main';

Template._addMedicationModal.events({
  'click #btnSaveMedication': function(e) {
    e.preventDefault();
    const personId = Template.instance().data.personId;
    const m = Template.instance().data.m;
    const editingMedicationId = $("#frmAddMedication input[name=medicationId]").val();
    const frequencyType = $("#frmAddMedication input[name=medication-frequency]:checked").val();
    let frequencyAmount;
    if (frequencyType == "everyhours") frequencyAmount = $("#frmAddMedication input[name=everyhours-amt]").val();
    else if (frequencyType == "timesperday") frequencyAmount = $("#frmAddMedication input[name=timesperday-amt]").val();
    const durationType = $("#frmAddMedication input[name=medication-duration]:checked").val();
    const medicationData = {
      personId: personId,
      name: $("#frmAddMedication input[name=medication-name]").val(),
      dosage: $("#frmAddMedication input[name=medication-dosage]").val(),
      frequencyType: frequencyType,
      durationType: durationType,
      notes: $("#frmAddMedication textarea[name=medication-notes]").val()
    };
    if (frequencyAmount) medicationData.frequencyAmount = parseInt(frequencyAmount);
    if (durationType == "end-date") {
      const durationEnd = new moment($("#frmAddMedication input[name=medication-end-date]").val(), "MM/DD/YYYY");
      medicationData.durationEnd = durationEnd.valueOf();
    }
    if (m && m._id) medicationData.medicationId = m._id;
    
    Meteor.callAsync("insertMedication", medicationData).then((result)=>{
      mpSwal.fire("Success", "Medication saved successfully", "success");
      hideModal("#_addMedicationModal");
    }).catch((error)=>{
      mpSwal.fire("Error saving medication", error.reason, "error");
    })
  },
});

Template._addMedicationModal.helpers({
  getTitle: function() {
    var m = Template.instance().data.m;
    if (m && m._id) return "Edit";
    return "Add";
  }
})

Template._addMedicationModal.rendered = function() {
  var m = Template.instance().data.m;
  if (m) {
    $("#frmAddMedication input[name=medication-name").val(m.name);
    $("#frmAddMedication input[name=medication-dosage").val(m.dosage);
    if (m.frequencyType=="everyhours") {
      $("#frmAddMedication input[name=medication-frequency][value=everyhours]").prop("checked",true);
      $("#frmAddMedication input[name=everyhours-amt]").val(m.frequencyAmount);
    } else if (m.frequencyType=="timesperday") {
      $("#frmAddMedication input[name=timesperday-amt]").val(m.frequencyAmount);
      $("#frmAddMedication input[name=medication-frequency][value=timesperday]").prop("checked",true);
    } else $("#frmAddMedication input[name=medication-frequency][value=other]").prop("checked",true);
    if (m.durationType == "end-date") {
      $("#frmAddMedication input[name=medication-duration][value=end-date]").prop("checked",true);
      $("#frmAddMedication input[name=medication-end-date]").val(new moment(m.durationEnd).format("MM/DD/YYYY"));
    } else $("#frmAddMedication input[name=medication-duration][value=ongoing]").prop("checked",true);
    $("#frmAddMedication textarea[name=medication-notes]").val(m.notes);
    $("#frmAddMedication input[name=medicationId]").val(m._id);
  }
  $("#frmAddMedication input[name=medication-end-date]").datepicker({ autoclose: true, todayHighlight: true })
}
