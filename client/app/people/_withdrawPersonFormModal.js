import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_withdrawPersonFormModal.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { hideModal } from '../main';

Template._withdrawPersonFormModal.rendered = function() {
	$("#withdrawPersonDate").datepicker({
		autoclose: true, todayHighlight: true
	});
}

Template._withdrawPersonFormModal.events({
	'click #btnSubmitWithdraw': function(event){
		event.preventDefault();
		var personId = FlowRouter.current().params._id
		var withdrawDate = moment($("#withdrawPersonDate").val(), "MM/DD/YYYY").valueOf()

		const options = {personId, withdrawDate};
		Meteor.callAsync('withdrawPerson', options).then(() => {
			hideModal("#_withdrawPersonFormModal");	
		}).catch((error) => {
			mpSwal.fire('Error', error.reason, 'error');
		});
	}
});

Template._withdrawPersonFormModal.helpers({
	"withdrawPersonDateVal": () => {
    return Template.instance().data.date;
	}
})
