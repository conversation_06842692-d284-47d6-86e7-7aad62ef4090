<template name="_relationshipsTab">
  <div class="d-flex flex-column-fluid">
    <div class="container">
      {{#if isRelationshipSource}}
        <div class="d-flex flex-row justify-content-end mb-4">
          <div data-cy="new-relationship-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="newRelationshipLink">
            <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add Relationship
          </div>
        </div>
      {{/if}}
      {{#each collapsedOwnedRelationships}}
      <div class="card shadow-sm mb-4" data-id="{{_id}}">
        <div class="card-body p-4">
          <!-- Header Section with Avatar and Basic Info -->
          <div class="d-flex align-items-start">
            <!-- Avatar Section -->
              <div class="flex-shrink-0 mr-7">
                {{#unless person.hasAvatar}}
                    <div class="d-flex avatar-circle align-items-center justify-content-center"
                         style="background-color:{{getAvatarBackground person.personInitials}}">
                        <span class="initials">{{person.personInitials}}</span>
                    </div>
                {{else}}
                  <div class="people-list-user-img" style="background-image:url({{person.getAvatarUrl}})"></div>
                {{/unless}}
              </div>

            <!-- Person Info Section -->
            <div class="flex-grow-1">
              <div class="d-flex flex-wrap align-items-center mb-2">
                {{#if isCurrentUser person._id}}
                  <a data-cy="relationship-full-name"
                     href="/people/{{person._id}}"
                     target="{{#if isPrintable}}_blank{{/if}}"
                     class="text-decoration-none h5 mb-0 me-3">
                    {{person.firstName}} {{person.lastName}}
                  </a>
                {{else}}
                  <span class="h5 mb-0 me-3">{{person.firstName}} {{person.lastName}}</span>
                {{/if}}

                {{#if person.inActive}}
                  <span class="badge bg-danger">Deactivated</span>
                {{/if}}
              </div>

              {{#if canViewContactInfo}}
                {{#unless showEmailAndPhoneNumber}}
                  <div class="mt-2">
                    {{#if person.getEmailAddress}}
                    <div class="d-flex align-items-center mb-2">
                      <span>{{person.getEmailAddress}}</span>
                    </div>
                    {{/if}}

                    {{#each person.availableContactMethods}}
                    <div class="d-flex align-items-center mb-2">
                      <span class="text-muted me-2">{{description}}:</span>
                      <span class="ml-1">{{value}}</span>
                    </div>
                    {{/each}}
                  </div>
                {{/unless}}
              {{/if}}
            </div>
          </div>

          <!-- Relationships Section -->
          {{#each relationships}}
            <div class="d-flex align-items-center py-2 mt-1">
              {{#if canManageRelationships}}
              <div class="col-1 dropdown">
                {{#if showFirst @index}}
                <div class="btn btn-icon btn-clean" data-toggle="dropdown">
                  <span class="svg-icon svg-icon-primary fad-regular fad-primary fad fa-ellipsis-h text-dark"></span>
                </div>
                <div class="dropdown-menu dropdown-menu-left" aria-labelledby="dropdownMenuButton">
                  <span class="dropdown-item clickable-row editRelationshipLink" data-id="{{_id}}">Edit</span>
                  <span class="dropdown-item clickable-row deleteRelationshipLink" data-id="{{_id}}">Delete</span>
                </div>
                {{/if}}
              </div>
              {{/if}}

              <div class="d-flex align-items-center flex-grow-1">

                <!-- Primary Caregiver Tag -->
                {{#if showPrimaryCaregiver relationshipType primaryCaregiver}}
                  <div class="d-flex align-items-center me-4 mr-4">
                    <span data-cy="primary-caregiver" class="badge bg-purple text-white" style="background-color: #6f42c1;">Primary Caregiver</span>
                  </div>
                {{/if}}

                <!-- Relationship Type -->
                <span data-cy="relationship-type" class="badge bg-light text-dark border me-4">
                  {{relationshipType.capitalizeFirstLetter}}
                </span>

                <!-- Relationship Description -->
                {{#if relationshipDescription}}
                  <span data-cy="relationship-description" class="text-muted ml-4">
                    {{relationshipDescription}}
                  </span>
                {{/if}}

              </div>
            </div>
          {{/each}}
        </div>
      </div>
      {{/each}}
      {{#each groupedRelationships}}
        <div class="card card-custom gutter-b" data-id="{{_id}}">
          <div class="card-body">
            <div class="d-flex flex-row">
              <div class="flex-shrink-0 mr-7">
                {{#unless fetchTargetPerson.hasAvatar}}
                    <div class="d-flex avatar-circle align-items-center justify-content-center"
                         style="background-color:{{getAvatarBackground fetchTargetPerson.personInitials}}">
                        <span class="initials">{{fetchTargetPerson.personInitials}}</span>
                    </div>
                {{else}}
                  <div class="people-list-user-img" style="background-image:url({{fetchTargetPerson.getAvatarUrl}})"></div>
                {{/unless}}
              </div>
              <div class="d-flex flex-column">
                <!--begin::Title-->
                <div class="d-flex flex-row flex-wrap mt-2">
                  <!--begin::User-->
                  <div class="mr-3">
                    <!--begin::Name-->
                    <a href="/people/{{fetchTargetPerson._id}}" class="d-flex align-items-center text-dark text-hover-primary font-size-h5 font-weight-bold mr-3">{{fetchTargetPerson.firstName}} {{fetchTargetPerson.lastName}}</a>
                    <!--end::Name-->
                    <!--begin::Contacts-->
                    <div class="d-flex flex-wrap my-2">
                      {{#if person.inActive}}
                        <span class="text-danger font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">deactivated</span>
                      {{/if}}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="d-flex flex-column flex-grow-1">
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Types</label>
                <label class="col-lg-9 col-xl-6 text-left col-form-label">
                  {{formatRelationshipTypes relationshipTypes}}
                </label>
              </div>
              {{#if relationshipDescription}}
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right col-form-label">Description</label>
                  <label class="col-lg-9 col-xl-6 text-left col-form-label">{{relationshipDescription}}</label>
                </div>
              {{/if}}
              {{#if primaryCaregiver}}
  							<div class="form-group row">
  								<label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
  								<div class="col-lg-9 col-xl-6 text-left col-form-label">
  									<span><i class="fad fad-primary fa-check-circle mr-4"></i>Primary Caregiver</span>
  								</div>
  							</div>
						  {{/if}}
              {{#if showSubscriptions}}
                <div class="form-group row">
                  <label class="col-xl-3 col-lg-3 text-right col-form-label" style="padding-left:0px;">Daily Summary Emails</label>
                  <div class="col-lg-9 col-xl-6">
                    <span class="switch switch-outline switch-primary">
                      <label>
                        <input type="checkbox" checked={{checkedIfReceiveDaily}} name="inputDaily"/>
                        <span></span>
                      </label>
                    </span>
                  </div>
                </div>
                {{# if showFamilyMessagesOptOut }}
                  <div class="form-group row">
                    <label class="col-xl-3 col-lg-3 text-right col-form-label" style="padding-left:0px;">Don’t copy me when messaging other family members</label>
                    <div class="col-lg-9 col-xl-6">
                      <span class="switch switch-outline switch-primary">
                        <label>
                          <input type="checkbox" checked={{ checkedIfFamilyMessagesOptOut }} name="familyMessagesOptOut" />
                          <span></span>
                        </label>
                      </span>
                    </div>
                  </div>
                {{/ if }}
              <div class="container py-4">
                <h4 class="mb-4 display-6">Real-time Preferences</h4>
                <div class="real-time-preferences-list ml-10">
                  {{#each st in subscriptionTypes}}
                  <div class="row g-4 mb-3 align-items-center py-2" style="border-left: 4px solid #e9ecef;">
                    <div class="col-lg-3">
                      <label class="form-label display-6 font-size-h6-xl font-weight-bold ml-2 mb-0">{{st.label}}</label>
                    </div>

                    <div class="col-lg-9">
                      <div class="d-flex" style="gap: 6rem;">
                        <div class="d-flex align-items-center">
                          <span class="me-5 mr-2">Email</span>
                          <span class="switch switch-outline switch-primary">
                            <label>
                              <input type="checkbox" name="input{{st.label}}Email" checked={{checkedIfSubscribed st.type 'email'}} value="{{st.type}}|email">
                              <span></span>
                            </label>
                          </span>
                        </div>

                        <div class="d-flex align-items-center">
                          <span class="me-5 mr-2">Push</span>
                          <span class="switch switch-outline switch-primary">
                            <label>
                              <input type="checkbox" name="input{{st.label}}Push" checked={{checkedIfSubscribed st.type 'push'}} value="{{st.type}}|push">
                              <span></span>
                            </label>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  {{/each}}
                </div>
              </div>
              {{/if}}
            </div>
          </div>
        </div>
      {{/each}}
    </div>
  </div>
</template>
