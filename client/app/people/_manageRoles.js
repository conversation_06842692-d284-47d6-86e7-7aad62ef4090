import { Template } from 'meteor/templating';
import './_manageRoles.html';
import { processPermissions } from '../../../lib/permissions';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';
Template.manageRoles.helpers({
	availableRoles() {
		return _.sortBy(Orgs.current() && Orgs.current().enabledRolesDetails(), rd => rd.label + "|" + rd.id);
	},
	checkedIfRoleEnabled(role) {
		const currentPerson = this.currentPerson;
		return currentPerson && _.contains(currentPerson.roles, role) ? "checked": false;
	},
	disabledIfRoleDisabled(role) {
		const	roleDef = _.find(Orgs.current().enabledRolesDetails(), rd => rd.id == role);
		
		const allowed =	processPermissions({
			assertions: [{ context: "people/manageAllRoles", action: "edit"  }],
			evaluator: (person) => person.masterAdmin || person.superAdmin
		});
			
		return !(allowed) && !roleDef.localAdminCanAssign;
	}
});
