<template name="_activationsData">
  <table class="table">
    <tbody>
      <tr>
        <th>{{getPeople<PERSON>abe<PERSON>}} Name</th>
        <th>Family Member</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
      {{#each person in people}}
      <tr>
        <td data-cy="child-name">{{person.fullName}}</td>
        <td data-cy="family-member">{{person.familyName}}</td>
        <td data-cy="invitation-status">
          {{person.status}}

        </td>
        <td class="form-inline">
          <select data-cy="select-action" class="form-control activations-action" data-type="{{getType}}">
            <option data-cy="actions"> Actions...</option>
            {{#if selectedIfContains person.actionsAvailable "resendInvitation"}}
              <option value="activations-resend-invitation" data-id="{{person.familyId}}" data-type="{{getType}}">Resend Invitation</option>
            {{/if}}
            {{#if selectedIfContains person.actionsAvailable "createInvitation"}}
              <option value="activations-create-invitation" data-id="{{person.familyId}}" data-type="{{getType}}">Create Invitation</option>
            {{/if}}
            {{#if selectedIfContains person.actionsAvailable "associatePerson"}}
              <option value="view-profile" data-id="{{person._id}}" data-type="{{getType}}">Add Relationship to Profile</option>
            {{/if}}
          </select>
        </td>
      </tr>

      {{/each}}
    </tbody>
  </table>
</template>
