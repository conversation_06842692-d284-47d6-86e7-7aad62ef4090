<template name="_programDays">
    <div class="row justify-content-between align-items-center">
        <h2>What days would you like to attend?</h2>
    </div>
    <div class="mt-5">
        {{# if showLinkBundle }}
            <div data-cy="add-missing-plan-btn" class="btn btn-primary font-weight-bolder" id="btnAddMissingPlan" style="width: 100%;">
                Save on {{getPlanToAdd.otherPlanDescription}} by adding {{ getPlanToAdd.description }} -- Tap to explore!
            </div>
        {{/ if }}
        {{# if planAdded }}
            <div data-cy="remove-plan-btn" class="btn btn-secondary font-weight-bolder" id="btnRemovePlan" style="width: 100%;">
                Tap to remove {{ getPlanToAdd.description }}
            </div>
        {{/ if }}
        {{#each plan in plans}}
            <form id="{{plan._id}}" class="card registration-card day-card my-2" data-id="{{plan._id}}">
                {{> _programDaysPlanInfo plan=plan plans=plans currentBundle=currentBundle programs=programs validNext=validNext plansWithScaledAmounts=plansWithScaledAmounts }}
            </form>
        {{/each}}
        {{#if showSavings}}
            <div class="card registration-card day-card h3 my-2 py-4">
                <div class="row ml-1 font-weight-bold">
                    <div class="col-auto text-right">
                        <div class="row">
                            <div data-cy="regular-price-amount" class="col">
                                {{ formatCurrency regularPrice }}
                            </div>
                        </div>
                        <div class="row">
                            <div data-cy="bundled-price-amount" class="col">
                                {{ formatCurrency bundledPrice }}
                            </div>
                        </div>
                        <div class="row">
                            <div data-cy="your-savings-amount" class="col" style="color: var(--primary);">
                                {{ formatCurrency bundleSavings }}
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="row">
                            <div class="col">
                                Cost of plans if purchased separately
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                Bundled Price
                            </div>
                        </div>
                        <div class="row">
                            <div class="col" style="color: var(--primary);">
                                Your savings!
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {{/if}}
    </div>
</template>