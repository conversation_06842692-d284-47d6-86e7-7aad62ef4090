import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './_billingTab.html';
import { BillingUtils } from '../../../lib/util/billingUtils';
import { People } from '../../../lib/collections/people';
import _ from '../../../lib/util/underscore';
import { Orgs } from '../../../lib/collections/orgs';
const html2pdf = require('html2pdf.js');
const { FlowRouter } = require('meteor/ostrio:flow-router-extra');
const { SCALED_WEEKLY_PLAN, SCALED_BIWEEKLY_PLAN, SCALED_MONTHLY_PLAN } = require("../../../lib/constants/billingConstants");
const {AvailableCustomizations} = require("../../../lib/customizations");
import moment from "moment-timezone";
import {BillingFrequencies, PUNCH_CARD_TYPE} from "../../../lib/constants/billingConstants";
import { CouponUtils } from '../../../lib/util/couponUtils';
import { MiscUtils } from "../../../lib/util/miscUtils";
import logger from '../../../imports/winston';
import $ from 'jquery';
import { hideModal, showModal } from '../main';
import { processPermissions } from '../../../lib/permissions';
import './_personPaymentInfo';
import '../components/datePicker/datePicker';
import '../components/dateRangePicker/dateRangePicker';
import { AuditTrails } from '../../../lib/collections/auditTrail';
import { Invoice, Invoices } from '../../../lib/collections/invoices';
import { Relationships } from '../../../lib/collections/relationships';
import { Reservations } from '../../../lib/collections/reservations';
import '../simpleModal/simpleModal';
import '../billing/_billingPayment';
import { Groups } from '../../../lib/collections/groups';
import { getPeopleById } from '../../services/peopleMeteorService';
import { PersonBillingFamilySplitModalService } from './billing/personBillingFamilySplitModalService';
import { BillingTabService } from '../../services/people/billingTabService';
import { BillingTabAuditTrailsMethodsList } from '../../../lib/constants/peopleConstants';

function triggerBillingRefresh(instance) {
	if (instance && instance.refreshTrigger) {
		instance.refreshTrigger.set(instance.refreshTrigger.get() + 1);
		
		const personId = instance.data && instance.data._id;
		if (personId) {
			// Force re-rendering of pendingCharges
			if (instance.pendingCharges) {
				instance.pendingCharges.set([]);
			}
			
			// Force collection update
			if (People._collection && People.findOne(personId)) {
				try {
					// Clear local cache first to ensure we get fresh data
					People._collection.update(personId, { $set: { "billing.pendingCharges": [] } });
				} catch (e) {
					console.log("Error updating local collection", e);
				}
			}
			
			[100, 500, 1500, 3000].forEach(delay => {
				Meteor.setTimeout(() => {
					instance.refreshTrigger.set(instance.refreshTrigger.get() + 1);
					Meteor.subscribe('people', [personId], {
						onReady: () => {
							const person = People.findOne(personId);
							if (person && instance.pendingCharges) {
								const charges = _.deep(person, 'billing.pendingCharges') || [];
								instance.pendingCharges.set(charges);
							}
							instance.refreshTrigger.set(instance.refreshTrigger.get() + 1);
						}
					});
				}, delay);
			});
		}
	}
}

Template.billingTab.onCreated( async function() {
	const self = this;
	self.showExpiredEnrollments = new ReactiveVar(false);
	self.showAllCreditMemos = new ReactiveVar(false);
	this.personNames = new ReactiveVar({});
	this.pendingCalls = new Set();
	self.overPayments = new ReactiveVar([]);
	this.pendingCharges = new ReactiveVar([]);
	this.refreshTrigger = new ReactiveVar(0);
	this.auditTrails = new ReactiveVar([]);
	this.auditTrailsLoading = new ReactiveVar(false);

	// Commented out due to a reactivity issue in RAS-168
	// this.autorun(() => {
	// 	this.subscribe("auditTrailByTargetId", {targetId: self.data._id});
	// })

	self.securityDeposits = new ReactiveVar(null);
	const sd = await this.data.openSecurityDeposits();
	self.securityDeposits.set(sd);
	const currentOrg = Orgs.current();
	const personId = FlowRouter.getParam('_id')
	const options = {
		orgId: currentOrg._id,
		personId: personId
	}
	const overPayment = await this.data.openUnappliedCash(options);
	self.overPayments.set(overPayment);

	self.auditTrailsLoading.set(true);
	getAuditTrailHistory(personId, self);
});

Template.billingTab.onRendered(async function() {
	const self = this;
	const personId = FlowRouter.getParam('_id')
	self.auditTrailsLoading.set(true);
	getAuditTrailHistory(personId, self);
})

Template.billingTab.events({
	"click #chkShowAllCreditMemos": (e, instance) => {
		const showAllState = $(e.currentTarget).prop("checked");
		instance.showAllCreditMemos.set(showAllState);
	},
	"click #btnAddPlan": (e, instance) => {
		const personId = instance.data._id;
		const currentPerson = People.findOne(personId);
		const currentPlans = _.deep(currentPerson, "billing.enrolledPlans");
		showModal("simpleModal", {
			title:"Add Billing Plan",
			template: "personBillingEnrollPlanModal",
			data: {
				availableBillingPlans: Orgs.current().availableBillingPlans(false),
				enrollmentDate: new moment.tz(Orgs.current().getTimezone()).format("MM/DD/YYYY"),
				personId: personId
			},
			onSave: (e, i, formFieldData) => {
				formFieldData.personId = personId;
				formFieldData.allocations = ((formFieldData.allocations || "") != "") ? JSON.parse(formFieldData.allocations) : null;
				if (currentPlans?.find(p => p._id === formFieldData.plan)) {
					mpSwal.fire({
						title: "Warning",
						text: "This child has previously been enrolled in this billing plan. Are you sure you want to enroll them again?",
						showCancelButton: true,
						confirmButtonText: "Proceed"
					}).then ((result) => {
						if (result.value) {
							Meteor.callAsync("enrollBillingPlan", formFieldData).then(() => {
								$("#simpleModal").modal('hide');
							}).catch((error) => {
								$(e.target).html('Save').prop("disabled", false);
								mpSwal.fire("Error", error.reason, "error");
							});
						}
					});
				}
				else {
					Meteor.callAsync("enrollBillingPlan", formFieldData).then(() => {
						$("#simpleModal").modal('hide');
					}).catch((error) => {
						$(e.target).html('Save').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				}
			}
		})
	},
	"click .btnEditPlan": (e, instance) => {
		e.preventDefault();
		const personId = instance.data._id;
		const planId = $(e.target).data("id");
		const createdAt = $(e.target).data("created-at");
		const existingPlan = _.find(instance.data.billing.enrolledPlans, (p) => { return p._id === planId && p.createdAt === createdAt; });
		const planInfo = _.find(Orgs.current().billing.plansAndItems, (p)=>{return p._id===existingPlan._id;});
		const timezone = Orgs.current().getTimezone();
		const currentPerson = People.findOne(personId);
		const currentPlans = _.deep(currentPerson, "billing.enrolledPlans");

		showModal("simpleModal", {
			title:"Edit Billing Plan",
			template: "personBillingEnrollPlanModal",
			data: {
				enrollmentDate: new moment.tz(existingPlan.enrollmentDate, timezone).format("MM/DD/YYYY"),
				expirationDate: existingPlan.expirationDate && new moment.tz(existingPlan.expirationDate, timezone).format("MM/DD/YYYY"),
				enrollmentForecastStartDate: existingPlan.enrollmentForecastStartDate && new moment.tz(existingPlan.enrollmentForecastStartDate, timezone).format("MM/DD/YYYY"),
				enrollmentForecastEndDate: existingPlan.enrollmentForecastEndDate && new moment.tz(existingPlan.enrollmentForecastEndDate, timezone).format("MM/DD/YYYY"),
				personId: personId,
				existingPlan: existingPlan,
				overrideRate: existingPlan.overrideRate,
				existingPlanAmount: existingPlan.overrideRate || planInfo.amount,
				reservationId: existingPlan.reservationId
			},
			onSave: (e, i, formFieldData) => {
				const initialAllocations = currentPlans.find((plan) => planId === plan._id).allocations || [];
				const allCoupons = Orgs.current().availableCouponCodes();
				formFieldData.personId = personId;
				formFieldData.allocations = ((formFieldData.allocations || "") !== "") ? JSON.parse(formFieldData.allocations) : null;
				formFieldData.existingPlanId = existingPlan._id;
				formFieldData.createdAt = existingPlan.createdAt;

				const orgId = Orgs.current()._id;
				const newlyAddedCoupons = CouponUtils.addedCoupons(initialAllocations, formFieldData.allocations) || [];
				const removedCoupons = CouponUtils.removedCoupons(initialAllocations, formFieldData.allocations) || [];
				CouponUtils.updateCurrentNumberOfRegistrations(newlyAddedCoupons || [], orgId, 'add');
				CouponUtils.updateCurrentNumberOfRegistrations(removedCoupons || [], orgId, 'subtract');
				CouponUtils.updateSingleInstallmentCoupon(newlyAddedCoupons, removedCoupons, allCoupons, orgId, personId, existingPlan);

				Meteor.callAsync("modifyBillingPlan", formFieldData).then(() => {
					$("#simpleModal").modal('hide');
				}).catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnAddFamilySplit": (e, instance) => {
		e.preventDefault();
		const personId = instance.data._id;
		const currentPerson = People.findOne(personId);
		const existingSplits = currentPerson.billingFamilySplits() || {};

		showModal("simpleModal", {
			title:"Manage Family Split",
			template: "personBillingFamilySplitModal",
			data: {
				relationships: _.chain(currentPerson.findOwnedRelationships())
					.filter( (r) => {return r.relationshipType=="family";})
					.map( (r) => {
						let p = People.findOne(r.personId);
						p.existingSplit = existingSplits[r.personId] || 0.00;
						return p;
					})
					.sortBy( (r) => { return r.lastName + "|" + r.firstName;})
					.value(),
				personId: personId
			},
			onSave: (e, i, formFieldData) => {
				let familySplitData = {
					personId : personId,
					allocations: {}
				};
				let newTotal = 0;
				let valid = true;
				$("input[name=split-amount]").each( function(index) {
					const value = parseInt(this.value);
					if (isNaN(value)) {
						valid = false;
						$(e.target).html('Save').prop("disabled", false);
						return mpSwal.fire("Error", "Your split amounts must be numbers.");
					}
					newTotal = newTotal + value;
					const relationshipId = $(this).data("id");
					familySplitData.allocations[relationshipId] = parseInt(this.value);
				});
				if (!valid) {
					return;
				}
				if (newTotal < 100) {
					$(e.target).html('Save').prop("disabled", false);
					return mpSwal.fire("Error", "Your split amounts must add up to 100.");
				} else if (newTotal > 100) {
					$(e.target).html('Save').prop("disabled", false);
					return mpSwal.fire("Error", "Your split amounts cannot exceed 100.");
				}
				Meteor.callAsync("updateBillingFamilySplit", familySplitData).then(() => {
					$("#simpleModal").modal('hide');
				}).catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click #btnAddCharge": (e, instance) => {
		const personId = instance.data._id,
			todayDate = new moment.tz(Orgs.current().getTimezone()).startOf("day").valueOf();

		showModal("simpleModal", {
			title:"Add Charge",
			template: "personBillingAddCharge",
			data: {
				availableItems: _.chain(_.deep(Orgs.current(), "billing.plansAndItems"))
					.filter((p) => {
						if (Orgs.current().hasCustomization(AvailableCustomizations.PUNCH_CARDS)) {
							if ((p.type === "item" || p.type === PUNCH_CARD_TYPE) && !p.archived && (!p.expires || (p.expires > todayDate))) {
								return true;
							}
						} else  {
							if (p.type === "item" && !p.archived && (!p.expires || (p.expires > todayDate))) {
								return true;
							}
						}
						return false;
					})
					.sortBy("description")
					.value()
			},
			onSave: (e, i, formFieldData) => {
				formFieldData.personId = personId;
				Meteor.callAsync("insertBillingCharge", formFieldData).then(() => {
					hideModal("#simpleModal")

					triggerBillingRefresh(Template.instance());

					// Add multiple refresh attempts to ensure the UI updates after server changes
					let refreshCount = 0;
					const refreshInterval = setInterval(() => {
						triggerBillingRefresh(Template.instance());
						refreshCount++;
						if (refreshCount >= 3) {
							clearInterval(refreshInterval);
						}
					}, 5000);
				}).catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		})
	},
	"click #btnGenerateInvoice": (e, instance) => {
		const personId = instance.data._id;
		mpSwal.fire({
			title: "Generate Manual Invoice",
			html: `<div class="row mx-2">This will create an invoice for any outstanding charges and send immediately. Are you sure?</div><div class="row mx-2 mt-4">You can enter an optional due date. If left blank, the invoice will respect your standard grace period.</div><div class="form-group row mx-2"><input type="text" class="form-control" id="manualInvoiceDueDate"></div>`,
			showCancelButton: true,
			confirmButtonText: "Generate Invoice",
			willOpen: () => {
				const swalPopup = document.querySelector('.swal2-popup');
				if (swalPopup) {
					swalPopup.style.cssText = ''; // Clear out SweetAlert2's CSS styles.
				}
			},
			didRender: function() {
				$("#manualInvoiceDueDate").datepicker({ autoclose: true, todayHighlight: true });
			},
		}).then(async (result) => {
			if (result.value) {
				const invoiceDueDate = $('#manualInvoiceDueDate').val().length > 0 ? new moment.tz($('#manualInvoiceDueDate').val(), "MM/DD/YYYY", Orgs.current().getTimezone()).format("MM/DD/YYYY") : null;

				try {
					// Show loading indicator
					mpSwal.fire({
						title: 'Generating Invoice',
						text: 'Please wait...',
						allowOutsideClick: false,
						didOpen: () => {
							mpSwal.showLoading();
						}
					});
					
					await BillingTabService.generateManualInvoice(personId, invoiceDueDate, Orgs.current());
					
					// Close the loading dialog first
					mpSwal.close();
					
					Meteor.subscribe('people', [personId], {
						onReady: function() {
							const person = People.findOne(personId);
							if (person) {
								People._collection.update(personId, { $set: { "billing.pendingCharges": [] } });
							}
							
							Meteor.subscribe('people', [personId], () => {
								triggerBillingRefresh(Template.instance());
								mpSwal.fire({
									icon: 'success',
									title: 'Invoice Generated',
									text: 'The invoice has been generated successfully.',
									timer: 2000
								});
							});
						}
					});
					
					const refreshIntervals = [500, 1500, 3000, 5000];
					refreshIntervals.forEach((interval) => {
						Meteor.setTimeout(() => {
							triggerBillingRefresh(Template.instance());
						}, interval);
					});
				} catch (error) {
					console.error("Error generating invoice:", error);
					mpSwal.fire({
						icon: 'error',
						title: 'Error',
						text: 'There was an error generating the invoice. Please try again.'
					});
				}
			}
		});
	},
	"click #btnAddCreditMemo": (e, instance) => {
		const personId = instance.data._id;

		showModal("simpleModal", {
			title:"Add Credit Memo",
			template: "personBillingAddCreditMemo",
			data: {
				availableCreditMemoTypes: Orgs.current().availableCreditMemoTypes()
			},
			onSave: (e, i, formFieldData) => {
				formFieldData.personId = personId;
				Meteor.callAsync("insertBillingCreditMemo", formFieldData).then(() => {
					$("#simpleModal").modal('hide');
				}).catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnSuspendBilling": (e, instance) => {
		e.preventDefault();
		let options = {
			personId: instance.data._id
		};
		mpSwal.fire({
			title:"Suspend Billing",
			html:"<p>Enter date through which billing will be suspended.  No invoices will be created until after this date.</p>" +
				"<input type='text' class='form-control' id='billingSuspensionDate'>",
			showCancelButton: true,
			confirmButtonText: "Suspend Billing",
			didRender: function() {
				$("#billingSuspensionDate").datepicker({
					autoclose: true, todayHighlight: true
				})
			}
		}).then ((result) =>
		{
			if (result.value) {
				options.suspendUntil = new moment.tz($("#billingSuspensionDate").val(), "MM/DD/YYYY", Orgs.current().getTimezone()).startOf("day").valueOf();
				Meteor.callAsync("suspendPersonBilling", options).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnBillingDueDateRemove": (e, instance) => {
		// Remove billing due date from user
		e.preventDefault();
		const personId = instance.data._id
		Meteor.callAsync("removeInvoiceDueDate", {personId}).then(() => {
		}).catch((err) => {
			mpSwal.fire("Error: " + err.reason);
		});
	},
	"click .btnBillingDueDateEdit": (e, instance) => {
		e.preventDefault();
		const personId = instance.data._id
		const person = People.findOne(personId);
		const monthlyDay = person.billing.invoiceDateOverride?.monthlyPlanDueDay || ''
		const weeklyDay = person.billing.invoiceDateOverride?.weeklyPlanDueDay || ''
		const monthlyDayDefaultValue = `value="${monthlyDay}"`
		const weeklyDayDefaultValue = (day) => day === weeklyDay ? 'selected' : ''

		mpSwal.fire({
			title:`<h1><b>Adjust Invoice Due Date</b></h1>`,
			width: 550,
			html:`
				<div style="text-align:left" class="mt-3 ml-3 mr-3">
					<p>Adjust the due date on this user's invoices to help handle exceptions to your normal due date settings. Please define which date invoices should be due for this user:</p>

					<p>Monthly Invoices: Due on the next occurring <input ${monthlyDayDefaultValue} type="number" style="width: 5rem" class="form-control d-inline ml-2 mr-2" id="monthly-day-input" required min="0" max="31"> of the month </p>

					<p>Weekly Invoices: Due on the next occurring <select id="weekly-day-options" class="form-control d-inline ml-2" style="width: 10rem">
							<option value="monday" ${weeklyDayDefaultValue('monday')}>Monday</option>
							<option value="tuesday" ${weeklyDayDefaultValue('tuesday')}>Tuesday</option>
							<option value="wednesday" ${weeklyDayDefaultValue('wednesday')}>Wednesday</option>
							<option value="thursday" ${weeklyDayDefaultValue('thursday')}>Thursday</option>
							<option value="friday" ${weeklyDayDefaultValue('friday')}>Friday</option>
							<option value="saturday" ${weeklyDayDefaultValue('saturday')}>Saturday</option>
							<option value="sunday" ${weeklyDayDefaultValue('sunday')}>Sunday</option>
						</select>
					</p>
				</div>
			`,
			showCancelButton: true,
			confirmButtonText: "Adjust Due Date",
		}).then ((result) =>
		{
			const monthlyDay = document.getElementById('monthly-day-input').value
			const weeklyDay = document.getElementById('weekly-day-options').value;
			if (result.value) {
				Meteor.callAsync("adjustInvoiceDueDate", {monthlyDay, weeklyDay, personId}).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnRemoveBillingSuspension": (e, instance) => {
		const personId = instance.data._id;
		mpSwal.fire({
			title:"Remove Billing Suspension",
			text:"This will resume all billing for this person.",
			showCancelButton: true,
			confirmButtonText: "Remove Billing Suspension"
		}).then ((result) => {
			if (result.value) {
				Meteor.callAsync("suspendPersonBilling", {personId: personId}).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnRemovePlan": (e, instance) => {
		e.preventDefault();
		const options = {
			personId: instance.data._id,
			planId: $(e.target).data("id"),
			createdAt:  $(e.target).data("created-at")
		};
		mpSwal.fire({
			title:"Are you sure?",
			text:"Removing plan will prevent new invoices, charges, and transfers.",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, remove billing"
		}).then((result) =>
		{
			if (result.value) {
				Meteor.callAsync("removeBillingPlan", options).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click #btnRemoveFamilySplit": (e, instance) => {
		const options = {
			personId: instance.data._id
		};
		mpSwal.fire({
			title:"Are you sure?",
			text:"This will permanently remove the family split.",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, remove split"
		}).then((result) =>
		{
			if (result.value) {
				Meteor.callAsync("removeBillingFamilySplit", options).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnRemoveCharge": (e, instance) => {
		const options = {
			personId: instance.data._id,
			chargeId: $(e.target).data("id")
		};
		mpSwal.fire({
			title:"Are you sure?",
			text:"This charge will be removed and will not be invoiced.",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, remove charge"
		}).then((result) =>
		{
			if (result.value) {
				Meteor.callAsync("removeBillingCharge", options).then(() => {
					// Add multiple refresh attempts to ensure the UI updates after server changes
					let refreshCount = 0;
					const refreshInterval = setInterval(() => {
						triggerBillingRefresh(Template.instance());
						refreshCount++;
						if (refreshCount >= 3) {
							clearInterval(refreshInterval);
						}
					}, 1000);
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnPayInvoice": async (e, instance) => {
		const invoiceId = $(e.target).data("id");
		const personId = instance.data._id;

		Meteor.call("getInvoiceById", invoiceId, (err, invoice) => {
			if (err) {
				console.error("Error fetching invoice:", err);
				mpSwal.fire("Error", err.reason, "error");
				return;
			}
			invoice = new Invoice(invoice);
			if (invoice) {
				showModal("billingPayment", {
					paymentAmount: invoice.amountDueForFamilyMember?.(personId),
					invoiceNumber: invoice.invoiceNumber
				});
			}
		});
	},
	"click #btnEditBillingNotes": (e, instance) => {
		const personId = instance.data._id,
			currentPerson = People.findOne(personId);
		mpSwal.fire({
			title:"Edit Billing Notes",
			input:"textarea",
			customClass: {
				"popup":"profile-billing-pop-up",
				"confirmButton":"swal2-confirm btn btn-primary font-weight-bolder",
				"cancelButton":"swal2-cancel btn btn-secondary font-weight-bolder"
			},
			inputValue: _.deep(currentPerson, "billing.billingNotes") || "",
			showCancelButton: true,
			confirmButtonText: "Save"
		}).then((result) =>
		{
			if (result.hasOwnProperty("value")) {
				Meteor.callAsync("updateBillingNotes", {personId: personId, notes: result.value || ""}).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnVoidCreditMemo": (e, instance) => {
		e.preventDefault();
		const personId = instance.data._id;
		const creditMemoId = $(e.target).data("id");
		mpSwal.fire({
			title:"Are you sure?",
			text:"This will void the credit memo and reverse any credits applied to invoices.",
			type:"warning",
			showCancelButton: true,
			confirmButtonText: "Yes, void and reverse"
		}).then((result) =>
		{
			if (result.value) {
				Meteor.callAsync("voidCreditMemo", {creditMemoId: creditMemoId, personId: personId}).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnManualRefundCreditMemo": (e, instance) => {
		e.preventDefault();
		const personId = instance.data._id;
		const creditMemoId = $(e.target).data("id");
		mpSwal.fire({
			title:"Manual Refund Credit Memo",
			text:"This will close the credit memo balance and mark the credit memo as refunded manually (via check or otherwise). You may add notes below:",
			type:"warning",
			input:"text",
			showCancelButton: true,
			confirmButtonText: "Yes, mark as refunded"
		}).then((result) =>
		{
			if (result.isConfirmed) {
				Meteor.callAsync("manualRefundCreditMemo", {creditMemoId: creditMemoId, personId: personId, notes: result.value}).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click .btnShowCreditMemoDetails": (e, instance) => {
		e.preventDefault();
		const personId = instance.data._id;
		const creditMemoId = $(e.target).data("id");
		Meteor.callAsync("getCreditMemoDetails", {personId, creditMemoId}).then((result) => {
			showModal("simpleModal", {
				title: "Credit Memo Details",
				template: "personBillingCreditMemoDetails",
				data: {
					personId,
					lineItems: result.lineItems,
				},
				hideSave: true,
				cancelButtonLabel: "Close",
				onSave: (e, i, formFieldData) => {
					$("#simpleModal").modal('hide');
				}
			});
		})
	},
	"click .btnOnlineRefundCreditMemo": (e, instance) => {
		e.preventDefault();
		const personId = instance.data._id,
			creditMemoId = $(e.target).data("id"),
			creditMemo = instance.data.billing.creditMemos.find(cm => cm._id == creditMemoId);
		Meteor.callAsync("onlineRefundCreditMemo", {personId, creditMemoId}).then((result) => {
			showModal("simpleModal", {
				title: "Online Refund Security Deposit",
				template: "personBillingOnlineRefundDeposit",
				data: {
					personId,
					creditMemo,
					invoice: result.paymentInvoice,
					refundableCharges: result.refundableCharges
				},
				actionButtonLabel: "Send Refund Now",
				onSave: (e, i, formFieldData) => {
					formFieldData.personId = personId;
					formFieldData.creditMemoId = creditMemoId;

					Meteor.callAsync("onlineRefundCreditMemo", formFieldData).then(() => {
						$("#simpleModal").modal('hide');
					}).catch((error) => {
						$(e.target).html('Submit').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});

				}
			});
		});
	},
	"click #btnManualPlanInvoice": (e, instance) => {
		e.preventDefault();
		const todayDate = new moment.tz(Orgs.current().getTimezone()).startOf("day"),
			plans = _.filter(instance.data.billing.enrolledPlans, ep => !ep.expirationDate || ep.expirationDate > todayDate.valueOf());

		_.each(plans, plan => {
			const planInfo = _.find(Orgs.current().billing.plansAndItems, (p)=>{return p._id==plan._id;});
			plan.masterPlanInfo = planInfo;
		});
		showModal("simpleModal", {
			title: "Send Manual Plan Invoice",
			template: "personBillingSendManualPlanInvoice",
			data: {
				plans,
				todayDate: todayDate.format('MM/DD/YYYY'),
			},
			actionButtonLabel: "Send Invoice Now",
			onSave: (e, i, formFieldData) => {
				const selectedPeriodStartDate = new moment.tz(formFieldData.period_start_date, "MM/DD/YYYY", Orgs.current().getTimezone()).startOf("day"),
					todayDate = new moment.tz(Orgs.current().getTimezone()).startOf("day");
				if (Orgs.current().hasCustomization("billing/configuration/postUsingPeriodStart") &&
					Orgs.current().hasCustomization("billing/configuration/preventManualInvoicePriorPeriod") &&
					selectedPeriodStartDate.valueOf() < todayDate.valueOf()) {
					$(e.target).html('Submit').prop("disabled", false);
					return mpSwal.fire("Error", "Deferred revenue reporting is enabled, so you must choose a period start date of today or later for a manual invoice.");
				}
				formFieldData.planIds = $("input[name='plan_ids']:checked").map(function () { return this.value; }).get();
				// save created timestamp to use because multiple same plans can be selected
				formFieldData.createdAts = $("input[name='plan_ids']:checked").map(function () { return parseInt(this.dataset.created); }).get();
				formFieldData.reservationIds = $("input[name='plan_ids']:checked").map(function () { return this.dataset.reservationid; }).get();
				formFieldData.includePendingItems = $("input[name='pending_charges']").prop("checked");
				formFieldData.personId = instance.data._id;
				formFieldData.periodStartDate = selectedPeriodStartDate.format("MM/DD/YYYY");
				formFieldData.invoiceDueDate = new moment.tz(formFieldData.invoice_due_date, "MM/DD/YYYY", Orgs.current().getTimezone()).format("MM/DD/YYYY");
				Meteor.callAsync("sendManualPlanInvoice", formFieldData).then(() => {
					$("#simpleModal").modal('hide');
					triggerBillingRefresh(Template.instance());

					// Add multiple refresh attempts to ensure the UI updates after server changes
					let refreshCount = 0;
					const refreshInterval = setInterval(() => {
						triggerBillingRefresh(Template.instance());
						refreshCount++;
						if (refreshCount >= 3) {
							clearInterval(refreshInterval);
						}
					}, 1000);
				}).catch((error) => {
					$(e.target).html('Submit').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"change #showExpiredEnrollments": (e, instance) => {
		instance.showExpiredEnrollments.set($(e.currentTarget).prop("checked"));
	},
	"click .btnDepositApply": async (e, instance) => {
		const personId = instance.data._id;
		const person = People.findOne(personId);
		const creditMemoId = $(e.currentTarget).data("id");

		const creditMemo = _.find(await person.openSecurityDeposits(), item => item.creditMemoId == creditMemoId);

		Meteor.call("getInvoices", {personId, openAmount:{"$gt": 0}}, (err, invoices) => {
			if (err) {
				console.error("Error fetching invoices:", err);
				mpSwal.fire("Error", err.reason, "error");
				return;
			}
			const updatedInvoices = invoices.map((i) => new Invoice(i));

			showModal("simpleModal", {
				title: "Apply Security Deposit",
				template: "personBillingApplyDeposit",
				data: {
					invoices: updatedInvoices,
					creditMemo
				},
				onSave: (e, i, formFieldData) => {
					formFieldData.personId = personId;
					formFieldData.creditMemoId = creditMemo.creditMemoId;

					Meteor.callAsync("applySecurityDeposit", formFieldData)
						.then(() => {
							hideModal("#simpleModal");
							mpSwal.fire("Success", "Security deposit successfully applied.", "success");
						}).catch((error) => {
						$(e.target).html('Save').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				}
			});
		});
	},
	"click .btnDepositResolve": (e, instance) => {
		const personId = instance.data._id,
			invoiceId = $(e.currentTarget).data("id"),
			creditMemoId = $(e.currentTarget).data("cm-id");

		mpSwal.fire({
			title: "Confirm Resolve Deposit",
			text: "This will mark the security deposit invoice as resolved, removing it from the security deposit list. You should only do this if you have handled the security deposit manually.",
			icon: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, resolve"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("resolveSecurityDeposit", {
					invoiceId,
					personId: personId,
					manualResolveCreditMemoId: creditMemoId
				}).then(() => {
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});

	},
	"click .btnSecuritySeize": async (e, instance) => {
		const personId = instance.data._id,
			person = People.findOne(personId),
			creditMemoId = $(e.currentTarget).data("id"),
			creditMemo = _.find(await person.openSecurityDeposits(), item => item.creditMemoId == creditMemoId),
			currentUser = Meteor.user(),
			currentPerson = currentUser?.fetchPerson()

		if (currentPerson) {
			creditMemo.seizedBy = currentPerson._id
		}

		mpSwal.fire({
			title: "Confirm Seize Deposit",
			text: "You are about to seize a security deposit. Once a security deposit is seized, parents will no longer be able to use this balance. Would you like to proceed?",
			icon: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("seizeSecurityDeposit", creditMemo).then(() => {
					mpSwal.fire("Success", "Security deposit successfully seized.", "success");
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		})
	},
	"click .btnSecurityReinstate": async (e, instance) => {
		const personId = instance.data._id,
			person = People.findOne(personId),
			creditMemoId = $(e.currentTarget).data("id"),
			creditMemo = _.find(await person.openSecurityDeposits(), item => item.creditMemoId == creditMemoId),
			currentUser = Meteor.user(),
			currentPerson = currentUser?.fetchPerson()

		if (currentPerson) {
			creditMemo.seizeReinstatedBy = currentPerson._id
		}

		mpSwal.fire({
			title: "Confirm Reinstate Deposit",
			text: `You are about to reinstate a security deposit. This action will reopen the security deposit ($${creditMemo.seizedAmount}) for the previously held amount for future use. Would you like to proceed?`,
			icon: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("reinstateSeizedSecurityDeposit", creditMemo).then(() => {
					mpSwal.fire("Success", "Security deposit successfully reinstated.", "success");
				}).catch((error) => {
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		})
	},
	"click .btnOverpaymentApply": (e, instance) => {
		const invoiceId = e.target.getAttribute('data-invoice-id');
		const creditIndex = e.target.getAttribute('data-credit-index');
		const heldAmount = parseInt(e.target.getAttribute('data-heldAmount'));
		const personId = instance.data._id;

		Meteor.call("getInvoiceById", invoiceId, async (err, invoice) => {
			if (err) {
				console.error("Error fetching invoice:", err);
				mpSwal.fire("Error", err.reason, "error");
				return;
			}
			invoice = new Invoice(invoice);
			const creditMemo = invoice.credits[creditIndex];
			creditMemo.heldAmount = heldAmount;

			Meteor.call("getInvoices", {personId, openAmount: {"$gt": 0}}, (err, invoices) => {
				if (err) {
					console.error("Error fetching invoices:", err);
					mpSwal.fire("Error", err.reason, "error");
					return;
				}

				const updatedInvoices = invoices.map((i) => new Invoice(i));
				showModal("simpleModal", {
					title: "Apply Unapplied Cash",
					template: "personBillingApplyUnappliedCash",
					data: {
						invoices: updatedInvoices,
						creditMemo
					},
					onSave: (e, i, formFieldData) => {
						formFieldData.personId = personId;
						formFieldData.creditIndex = creditIndex;
						formFieldData.creditInvoiceId = invoiceId;
						formFieldData.creditPayerSource = creditMemo.creditPayerSource;

						Meteor.callAsync("applyUnappliedCash", formFieldData)
							.then(() => {
								hideModal("#simpleModal");
								mpSwal.fire("Success", "Unapplied cash successfully applied.", "success");
							})
							.catch((error) => {
								$(e.target).html('Save').prop("disabled", false);
								mpSwal.fire("Error", error.reason, "error");
							});
					}
				});
			});
		});
	}
});

Template.billingTab.helpers({
	enabledBillingPlans() {
		const todayDate = new moment.tz(Orgs.current().getTimezone()).startOf("day").valueOf(),
			showExpired = Template.instance().showExpiredEnrollments.get();
		return this.enabledBillingPlans(todayDate, showExpired);
	},
	charges() {
		const instance = Template.instance();
		if (instance && instance.refreshTrigger) {
			instance.refreshTrigger.get();
		}
		
		const personId = this._id;
		if (personId) {
			Meteor.subscribe('people', [personId]);
		}
		
		const person = People.findOne(this._id, {reactive: true});
		if (!person || !person.billing || !person.billing.pendingCharges) {
			return [];
		}
		
		return _.map(person.billing.pendingCharges, (c) => {
			c.amount = c.quantity * c.price;
			return c;
		});
	},
	creditMemos() {
		const showAll = Template.instance().showAllCreditMemos.get()
		return this.availableCreditMemos(showAll);
	},
	securityDeposits() {
		return Template.instance().securityDeposits.get();
	},
	shouldShowPlans() {
		return (this.type == "person");
	},
	overPayments() {
		return Template.instance().overPayments.get();
	},
	shouldShowPaymentInfo() {
		return  processPermissions({
				assertions: [{ context: "billing/invoices", action: "edit"}],
				evaluator: (person) => person.type=="admin" || person.type=="family"
			}) &&
			Orgs.current().billingStatus().status == "active" && this.type == "family";
	},
	shouldShowAddPlan() {
		return processPermissions({
			assertions: [{ context: "billing/invoices/planAssignments", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowOtherActions() {
		return processPermissions({
			assertions: [{ context: "billing/invoices/planAssignments", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		}) && (!this.billingFamilySplits() || !this.activeBillingSuspension());
	},
	shouldShowEditAndRemovePlan() {
		return processPermissions({
			assertions: [{ context: "billing/invoices/planAssignments", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowManualPlanInvoice() {
		return processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowPendingCharges() {

		return this.type == "person" && processPermissions({
			assertions: [{ context: "billing/invoices", action: "read"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowSecurityDeposits() {
		return this.type == "person" && processPermissions({
			assertions: [{ context: "billing/invoices", action: "read"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowHeldFunds() {
		return this.type == "person" && Orgs.current().hasCustomization(AvailableCustomizations.HELDFUNDS_ENABLED) && processPermissions({
			assertions: [{ context: "billing/invoices", action: "read"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowAuditTrail() {
		return this.type == "person" && processPermissions({
			assertions: [{ context: "billing/invoices", action: "read"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowAddCharges() {
		return processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit"}, {context: "billing/invoices/itemCharges", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	},
	shouldShowBillingNotes() {
		const currentPerson = Template.parentData(1);
		return processPermissions({
			assertions: [{ context: "billing/invoices", action: "read"}],
			evaluator: (person) => person.type=="admin"
		}) && _.contains(["active", "invoice_only"],Orgs.current().billingStatus().status) && Template.instance().data.type == "person";
	},
	shouldShowEditBillingNotes() {
		return  processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit"}, {context: "billing/invoices/billingNotes", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		}) && _.contains(["active", "invoice_only"],Orgs.current().billingStatus().status) && Template.instance().data.type == "person";
	},
	shouldShowCreditMemos() {
		const currentUser = Meteor.user(), currentPerson = currentUser && currentUser.fetchPerson();
		return processPermissions({
				assertions: [{ context: "billing/invoices", action: "read"}],
				evaluator: (person) => person.type=="admin" || person.type == "family"
			}) && _.contains(["active", "invoice_only"],Orgs.current().billingStatus().status)
			&& currentPerson
			&& (this.type != "person" || this._id == currentPerson._id) ;
	},
	shouldShowAddCreditMemo() {
		return  processPermissions({
			assertions: [{ context: "billing/payments", action: "edit"}, { context: "billing/creditMemos/create", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		}) && _.contains(["active", "invoice_only"],Orgs.current().billingStatus().status) && Template.instance().data.type == "family";
	},
	shouldShowVoidCreditMemo() {
		return  processPermissions({
				assertions: [{ context: "billing/payments", action: "edit"}, { context: "billing/creditMemos/create", action: "edit"}],
				evaluator: (person) => person.type=="admin"
			}) && _.contains(["active", "invoice_only"],Orgs.current().billingStatus().status)
			&& Template.instance().data.type == "family";
	},
	shouldShowOnlineRefund() {
		return  processPermissions({
			assertions: [{ context: "billing/payments", action: "edit"}, { context: "billing/creditMemos/create", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		}) && this.type == "securityDepositAuto" && this.paidForInvoiceId && this.openAmount > 0;
	},
	billingFamilySplitsDisplay() {
		return _.map(this.billingFamilySplits(), (v, k) => {
			const p = People.findOne(k);
			if (p) {
				return {
					firstName: p.firstName,
					lastName: p.lastName,
					amount: v
				};
			}
		});
	},
	creditMemoDescriptionForType(t) {
		const cmType = _.find(Orgs.current().availableCreditMemoTypes(), (cmt) => {
			return cmt.type == t;
		});
		return cmType ? cmType.description : t;
	},
	alternateServiceChargeFeeDescription() {
		return Orgs.current().alternateServiceChargeFeeDescription();
	},
	getPersonFromId(billingAuditTrail) {
		if (billingAuditTrail.personId === 'SYSTEM') {
			return 'System';
		}

		const template = Template.instance();
		const personNames = template.personNames.get();
		const personId = billingAuditTrail.personId;

		if (!personNames[personId] && !template.pendingCalls.has(personId)) {
			template.pendingCalls.add(personId);

			Meteor.callAsync('getNameFromId', personId).then((result) => {
				const updatedNames = {...template.personNames.get()};
				updatedNames[personId] = result;
				template.personNames.set(updatedNames);
			}).catch((error) => {
				logger.error('getNameFromId meteor call', { 'Error': error });
			}).finally(() => {
				template.pendingCalls.delete(personId);
			})
			return billingAuditTrail.performedBy || '';
		}

		return personNames[personId];
	},
	showInvoiceDetailLink() {
		return !FlowRouter.getQueryParam("printable") && processPermissions({
			assertions: [{context: "billing/invoices", action: "read"}],
			evaluator: (person) => person.type === "admin"
		});
	},
	getInvoiceIdFromAuditArgs(args) {
		return args.invoiceId;
	},
	getInvoiceNumberFromAuditArgs(args) {
		if (Array.isArray(args)) {
			return args[0].invoiceNumber;
		}
		return args.invoiceNumber;
	},
	billingAuditTrails() {
		return Template.instance().auditTrails.get();
	},

	formatBillingAction(action, args) {
		switch (action) {
			case 'generateManualInvoice':
				return 'Generated a manual invoice'
			case 'sendBalanceNotice':
				return 'Sent balance notice'
			case 'sendManualPlanInvoice':
				return 'Sent a manual plan invoice'
			case 'insertBillingCharge':
				return 'Created a new billing charge'
			case 'removeBillingCharge':
				return 'Removed a billing charge'
			case 'updateBillingNotes':
				return 'Updated billing notes'
			case 'enrollBillingPlan':
				return 'Enrolled into a new billing plan'
			case 'modifyBillingPlan':
				return 'Modified existing billing plan'
			case 'suspendPersonBilling':
				return 'Added or removed a billing suspension'
			case 'removeBillingPlan':
				return 'Removed an existing billing plan'
			case 'updateBillingFamilySplit':
				return 'Updated a billing family split'
			case 'removeBillingFamilySplit':
				return 'Removed existing billing family split'
			case 'voidCreditMemo':
				return 'Voided a credit memo'
			case 'resolveSecurityDeposit':
				return 'Resolved a security deposit'
			case 'applySecurityDeposit':
				return 'Applied a security deposit'
			case 'resendInvoice':
				return 'Re-sent invoice'
			case 'voidInvoice':
				return 'Voided invoice'
			case 'updateInvoiceNotes':
				return 'Updated invoice notes'
			case 'creditInvoice':
				return 'Added a credit or payment'
			case 'voidCreditLine':
				return 'Voided a credit or payment'
			case 'modifyDiscount':
				return 'Modified a discount or reimbursement'
			case 'addDiscount':
				return 'Added a discount or reimbursement'
			case 'manualRefundCreditLine':
				return 'Issued a refund'
			case 'applyUnappliedCash':
				return 'Applied unapplied cash'
			case 'reallocatePayer':
				return 'Reallocated reimbursement'
			case 'manualReverseReallocation':
				return 'Reversed reallocation'
			case 'seizeSecurityDeposit':
				return 'Seized a security deposit'
			case 'reinstateSeizedSecurityDeposit':
				return 'Reinstated a security deposit'
			case 'markAsUnreadPunchCard':
				return `Reinstated punch card day originally used on ${args.dateSelected} - ${args.reason}`
			case 'preschedulePunchCard':
				return `Punch card day prescheduled for ${args.selectedDate}`
			case 'pastSchedulePunchCard':
				return `Punch card day manually marked as used on ${args.selectedDate}`
			default:
				return action ? action : ''

		}
	},
	isBundled(plan) {
		return !!plan.bundlePlanId;
	},
	bundlePlanNames(plan) {
		const org = Orgs.current();
		if (!org) {
			return '';
		}
		const plans = org.availableBillingPlans(true);
		const bundles = org.availableBundles();
		const bundle = bundles?.find(b => b._id === plan.bundlePlanId);
		if (!bundle) {
			return '';
		}
		return plans.filter(p => bundle.plans.includes(p._id))
			.map(p => p.description)
			.join('/');
	},
	activeAdjustedBillingDates() {
		const personId = FlowRouter.getParam('_id')
		const person = People.findOne(personId);
		return person.billing?.invoiceDateOverride
	},
	getMonthlyDay(){
		const personId = FlowRouter.getParam('_id')
		const person = People.findOne(personId);
		return moment.localeData().ordinal(person.billing.invoiceDateOverride.monthlyPlanDueDay)
	},
	getWeeklyDay(){
		const personId = FlowRouter.getParam('_id')
		const person = People.findOne(personId);
		const weeklyDay = person.billing.invoiceDateOverride.weeklyPlanDueDay
		return weeklyDay[0].toUpperCase() + weeklyDay.substring(1)
	},
	isAuditTrailsLoading() {
		return Template.instance().auditTrailsLoading.get()
	}
});

Template.personBillingAddCharge.events({
	"change select[name=item]": (e, i) => {
		const planId =$(e.target).val();
		if (!planId || planId == "") return;
		const planInfo = _.find(Orgs.current().billing.plansAndItems, (p)=>{return p._id==planId;});
		$("input[name=price]").val(numeral(planInfo.amount).format('0.00'));
		$("input[name=quantity]").val(1);
		$("input[name=amount]").val(numeral(planInfo.amount).format('$0.00'));
	},
	"change input[name=price],input[name=quantity]": (e,i) => {
		const price = $("input[name=price]").val();
		const qty = $("input[name=quantity]").val();
		if (!isNaN(price) && !isNaN(qty)) $("input[name=amount]").val(numeral(price * qty).format('$0.00'));
	}
});
Template.personBillingAddCharge.helpers({
	"availableDiscountTypes": () => {
		return Orgs.current() && _.sortBy(Orgs.current().availableDiscountTypes(),  (d) => d.description );
	}
});

Template.personBillingFamilySplitModal.onCreated(function () {
	this.personBillingFamilySplitModalService = new PersonBillingFamilySplitModalService(this?.data?.relationships);
});

Template.personBillingFamilySplitModal.helpers({
	splitTotal() {
		return Template.instance().personBillingFamilySplitModalService.getSplitTotal();
	}
});

Template.personBillingFamilySplitModal.events({
	"change input[name=split-amount]": (e, i) => {
		const values = $("input[name=split-amount]").map(function () {
			return $(this).val();
		}).get();
		i.personBillingFamilySplitModalService.setTotalFromSplitAmounts(values);
	}
});

Template.personBillingEnrollPlanModal.onCreated(function() {
	this.selectedPlan = new ReactiveVar();
	this.overrideRate = new ReactiveVar();
	this.allocationDateField = new ReactiveVar(false);
	this.allocationPayerStartDateField = new ReactiveVar(false);
	this.allocationPayerEndDateField = new ReactiveVar(false);
	this.allocations = new ReactiveVar([]);
	this.selectedCategory = new ReactiveVar("none");
	this.allocationBuilder = new ReactiveDict();
	this.scheduleTypeToPlans = new ReactiveVar({});
	this.scheduleTypes = new ReactiveVar(Orgs.current().getScheduleTypes());
	this.selectedScheduleType = new ReactiveVar();
	this.availableBillingPlans = new ReactiveVar([]);
	this.selectedGroup = new ReactiveVar();

	const defaultGroupId = People.findOne(this.data.personId, {
		fields: { defaultGroupId: 1 }
	})?.defaultGroupId;

	this.allGroups = new ReactiveVar(Groups.find({}, {
		fields: {
			_id: 1,
			name: 1,
			scheduleTypeToPlans: 1
		},
		sort: { name: 1 }
	}).fetch());

	const updateBillingPlans = () => {
		const groupId = this.selectedGroup.get();
		const effectiveGroupId = (groupId === 'default') ? defaultGroupId : groupId;
		const scheduleType = this.selectedScheduleType.get();

		const allPlans = Orgs.current().availableBillingPlans();

		if (!this.data.filterBillingPlans) {
			this.availableBillingPlans.set(allPlans);
			return;
		}


		if (!effectiveGroupId || effectiveGroupId === "") {
			this.scheduleTypeToPlans.set({});
			this.availableBillingPlans.set([]);
			return;
		}

		const group = Groups.findOne(effectiveGroupId);
		this.scheduleTypeToPlans.set(group?.scheduleTypeToPlans || {});

		const scheduleTypeToPlans = this.scheduleTypeToPlans.get();
		const allowedPlanIds = scheduleTypeToPlans[scheduleType] || [];
		this.availableBillingPlans.set(
			allPlans.filter(plan => allowedPlanIds.includes(plan._id))
		);
	};

	this.autorun(() => {
		this.selectedGroup.get();
		this.selectedScheduleType.get();
		updateBillingPlans();
	});

	$(document).on('change', '#scheduleType', (e) => {
		this.selectedScheduleType.set($(e.target).val());
	});

	Tracker.afterFlush(() => {
		const initialGroupId = $('#targetGroup').val();
		this.selectedGroup.set(initialGroupId);

		const effectiveGroupId = (initialGroupId === 'default') ? defaultGroupId : initialGroupId;
		if (effectiveGroupId) {
			const foundGroup = this.allGroups.get().find(group => group._id === effectiveGroupId);
			const scheduleKeys = foundGroup?.scheduleTypeToPlans ? Object.keys(foundGroup.scheduleTypeToPlans) : [];
			const filteredTypes = this.scheduleTypes.get().filter(type => scheduleKeys.includes(type._id));

			const scheduleTypeInput = document.getElementById('scheduleType');
			if (filteredTypes.length > 0 && !scheduleTypeInput?.value) {
				const firstScheduleType = _.sortBy(filteredTypes, "type")[0]._id;
				this.selectedScheduleType.set(firstScheduleType);
				$('#scheduleType').val(firstScheduleType);
			} else if (filteredTypes.length) {
				this.selectedScheduleType.set(scheduleTypeInput?.value ?? '');
			}
		}
	});

	this.onDestroyed = () => {
		$(document).off('change', '#scheduleType');
		$('#targetGroup').off('change');
	};
});
Template.personBillingEnrollPlanModal.onDestroyed(function() {
	$(document).off('change', '#scheduleType');
});
Template.personBillingEnrollPlanModal.onRendered(function() {
	const instance = this;
	const existingPlan = Template.instance().data.existingPlan;

	if (existingPlan) {
		if (existingPlan.allocations && existingPlan.allocations.length > 0) {
			// Cheap and easy deep copy to avoid mutating plan data.
			const allocations = JSON.parse(JSON.stringify(existingPlan.allocations));
			instance.allocations.set(allocations);
		}

		if (existingPlan.planDetails && existingPlan.planDetails.category) {
			instance.selectedCategory.set(existingPlan.planDetails.category);
		}
	}

	$("input[name='allocations']").val(JSON.stringify(instance.allocations.get()));
	$("select[name='plan']").select2({
		allowClear: true,
		placeholder: "Select a plan",
	});

	$("select[name='plan']").on("change", function(event){
		const planId = $(event.currentTarget).val();
		const plan = _.find(_.deep( Orgs.current(), "billing.plansAndItems"), p => p._id == planId);
		if (plan) {
			if (plan.expires) {
				$("input[name='expiration_date']").val(new moment.tz(plan.expires, Orgs.current().getTimezone()).format("MM/DD/YYYY"));
			}

			instance.selectedCategory.set(plan.category || "none");
			instance.selectedPlan.set(plan);

			if (plan.frequency === "charged_daily_invoiced_monthly") {
				instance.allocationBuilder.clear();
				instance.allocationBuilder.set("building", true);
				instance.allocationDateField.set(false);
				instance.allocationPayerStartDateField.set(false);
				instance.allocationPayerEndDateField.set(false);
				instance.allocationBuilder.set("type", "reimbursable-with-copay");
			} else {
				instance.allocationBuilder.clear();
				instance.allocationDateField.set(false);
				instance.allocationPayerStartDateField.set(false);
				instance.allocationPayerEndDateField.set(false);
			}
		}
	})

	$("#enrollmentDateField").datepicker({
		autoclose: true, todayHighlight: true
	});

	$("#expirationDateField").datepicker({
		autoclose: true, todayHighlight: true
	});

	$("#enrollmentForecastStartField").datepicker({
		autoclose: true, todayHighlight: true
	});

	$("#enrollmentForecastEndField").datepicker({
		autoclose: true, todayHighlight: true
	})

	$('#targetGroup').on('change', (e) => {
		const groupId = $(e.target).val();
		this.selectedGroup.set(groupId);
		const effectiveGroupId = (groupId === 'default') ? defaultGroupId : groupId;

		if (effectiveGroupId) {
			const foundGroup = this.allGroups.get().find(group => group._id === effectiveGroupId);
			const scheduleKeys = foundGroup?.scheduleTypeToPlans ? Object.keys(foundGroup.scheduleTypeToPlans) : [];
			const filteredTypes = this.scheduleTypes.get().filter(type => scheduleKeys.includes(type._id));

			if (filteredTypes.length > 0) {
				const firstScheduleType = _.sortBy(filteredTypes, "type")[0]._id;
				this.selectedScheduleType.set(firstScheduleType);
				$('#scheduleType').val(firstScheduleType);
			} else {
				this.selectedScheduleType.set('');
				$('#scheduleType').val('');
			}
		}
	});

});
Template.personBillingEnrollPlanModal.events({
	"click #btnAddAllocation": (event, instance) => {
		event.preventDefault();
		instance.allocationBuilder.clear();
		instance.allocationBuilder.set("building", true);
		instance.allocationDateField.set(false);
		instance.allocationPayerStartDateField.set(false);
		instance.allocationPayerEndDateField.set(false);
	},
	"click #btnCancelAllocation": (event, instance) => {
		event.preventDefault();
		instance.allocationBuilder.clear();
		instance.allocationDateField.set(false);
		instance.allocationPayerStartDateField.set(false);
		instance.allocationPayerEndDateField.set(false);
	},
	"click #btnApplyAllocation": (event, instance) => {
		event.preventDefault();
		const allocationType = instance.allocationBuilder.get("type");
		const timezone = Orgs.current().getTimezone();

		if (!allocationType || allocationType === "") {
			return mpSwal.fire("Please specify allocation type");
		}

		let allocation = {};
		allocation.allocationType = allocationType;
		const discountValue = allocationType === 'coupon' ? instance.allocationBuilder.get("amount") : $("input[name='discount_amount']").val();

		if (isNaN(parseFloat(discountValue))) {
			return mpSwal.fire("Please specify an appropriate discount amount");
		}

		allocation.amount = parseFloat(discountValue);
		allocation.amountType = $("input[name='amount_type']:checked").val();

		function applyAllocation() {
			const allocationEditId = instance.allocationBuilder.get("editId");
			let currentAllocations = instance.allocations.get();

			if (allocationEditId) {
				const allocationIndex = currentAllocations.findIndex( a => a.id === allocationEditId);
				allocation.id = allocationEditId;
				currentAllocations[allocationIndex] = allocation;
			} else {
				allocation.id = Random.id();
				currentAllocations.push(allocation);
			}

			instance.allocations.set(currentAllocations);
			$("input[name='allocations']").val(JSON.stringify(currentAllocations));
			instance.allocationBuilder.clear();
		}

		switch(allocationType) {
			case "family":
				const selectedFamilyId = $("select[name='allocation_family_member']").val();

				if (!selectedFamilyId || selectedFamilyId === "") {
					return mpSwal.fire("Please specify family member");
				}

				allocation.familyPersonId = selectedFamilyId;
				allocation.allocationDescription = "Family member: " + $("select[name='allocation_family_member'] option:selected").text();
				break;
			case "reimbursable":
			case "reimbursable-with-copay":
				const selectedReimbursementType = $("select[name='allocation_reimbursement_type']").val();

				if (!selectedReimbursementType || selectedReimbursementType === "") {
					return mpSwal.fire("Please specify reimbursement type");
				}

				allocation.reimbursementType = selectedReimbursementType;
				if (allocationType === "reimbursable-with-copay") {
					allocation.selectedResponsiblePartyId = $("select[name='allocation_family_member']").val();

					if (!allocation.selectedResponsiblePartyId) {
						return mpSwal.fire("Please specify a responsible person");
					}

					const plan = getPlanDetails(instance);

					if (!plan) {
						return mpSwal.fire("Please specify a plan in order to apply this type of discount");
					}

					const planFrequency = plan.adjustedFrequency ?? plan.masterPlanInfo?.frequency;
					allocation.allocationDescription = "Reimbursable: " + $("select[name='allocation_reimbursement_type'] option:selected").text()
						+ " " + planFrequency;
				} else {
					allocation.allocationDescription = "Reimbursable: " + $("select[name='allocation_reimbursement_type'] option:selected").text();
				}
				const voucherStartsDateValue = instance.allocationPayerStartDateField.get();
				const voucherStarts = voucherStartsDateValue && new moment.tz(voucherStartsDateValue, timezone);
				const valueOfStarts = voucherStarts && voucherStarts.isValid() && voucherStarts.valueOf();
				const voucherEndsDateValue = instance.allocationPayerEndDateField.get();
				const voucherEnds = voucherEndsDateValue && new moment.tz(voucherEndsDateValue, timezone);
				const valueOfEnds = voucherEnds && voucherEnds.isValid() && voucherEnds.valueOf();

				if (valueOfStarts) {
					allocation.payerStartDate = valueOfStarts;
				} else if(!valueOfStarts && allocation.payerStartDate) {
					// Remove on edit if no value is set (or deleted, in this case)
					delete allocation.payerStartDate;
				}

				if (valueOfEnds) {
					allocation.payerEndDate = valueOfEnds;
				} else if(!valueOfEnds && allocation.payerEndDate) {
					// Remove on edit if no value is set (or deleted, in this case)
					delete allocation.payerEndDate;
				}

				let conflicts = [];
				const currentPerson = People.findOne({_id: instance.data.personId});
				const todayDate = new moment.tz(timezone).startOf("day").valueOf();
				const personPlans = (currentPerson.billing?.enrolledPlans || []).filter( ep => !ep.expirationDate || ep.expirationDate > todayDate);

				for (personPlan of personPlans) {
					_.each( personPlan.allocations, allocation => {
						if (allocation.allocationType === "reimbursable" || allocation.allocationType === "reimbursable-with-copay") {
							const voucherStart = valueOfStarts || todayDate;
							const allocationStart = allocation.payerStartDate || 0;
							const voucherEnd = valueOfEnds || Number.MAX_SAFE_INTEGER;
							const allocationEnd = allocation.payerEndDate || Number.MAX_SAFE_INTEGER;
							if ( voucherStart <= allocationEnd && voucherEnd >= allocationStart )  {
								let conflictMessage = "This allocation overlaps with an existing plan: ";
								conflictMessage += personPlan.planDetails.description + ". ";
								conflictMessage += "Type: " + allocation.allocationDescription + ". ";
								conflicts.push(conflictMessage);
							}
						}
					});
				}

				if (conflicts.length > 0)
					return mpSwal.fire({
						title: "There are existing plans with overlapping vouchers.",
						text: conflicts + " Do you wish to continue?",
						icon: "warning",
						showCancelButton: true
					}).then(result => {
						if (result.value) {
							applyAllocation();
						}
					});
				break;
			case "discount":
				const selectedDiscountType = $("select[name='allocation_discount_type']").val();

				if (!selectedDiscountType || selectedDiscountType === "") {
					return mpSwal.fire("Please specify discount type");
				}

				allocation.discountType = selectedDiscountType;
				allocation.allocationDescription = "Discount: " + $("select[name='allocation_discount_type'] option:selected").text();
				const dateFieldValue = instance.allocationDateField.get();
				const discountExpires = dateFieldValue && new moment.tz(dateFieldValue, timezone);
				const valueOfExpires = discountExpires && discountExpires.isValid() && discountExpires?.valueOf();

				if (valueOfExpires) {
					allocation.discountExpires = valueOfExpires;
				}

				break;
			case "coupon":
				const selectedCouponType = $("select[name='allocation_coupon_code']").val();

				if (!selectedCouponType || selectedCouponType === "") {
					return mpSwal.fire("Please specify coupon type");
				}

				const couponDateFieldValue = instance.allocationDateField.get();
				const couponDiscountExpires = couponDateFieldValue && new moment.tz(couponDateFieldValue, timezone);
				const couponValueOfExpires = couponDiscountExpires && couponDiscountExpires.isValid() && couponDiscountExpires?.valueOf();

				if (couponValueOfExpires) {
					allocation.discountExpires = couponValueOfExpires;
				}

				allocation.amount = instance.allocationBuilder.get("amount");
				allocation.amountType = instance.allocationBuilder.get("amountType");
				allocation.discountType = selectedCouponType;
				allocation.allocationDescription = "Discount: " + $("select[name='allocation_coupon_code'] option:selected").text();
				break;
		}
		applyAllocation();
	},
	"change select[name='allocation_type']": (event, instance) => {
		const allocationType = $(event.currentTarget).val();
		instance.allocationBuilder.set("type", allocationType);
	},
	"change select[name='allocation_coupon_code']": (event, instance) => {
		const couponCode = $(event.currentTarget).val();
		const coupon = _.find(Orgs.current().availableCouponCodes(), (d) => d.code === couponCode);

		// Populate coupon expiration date
		if (coupon?.expirationDate) instance.allocationDateField.set(coupon.expirationDate);
		else instance.allocationDateField.set(false);

		// Populate coupon amount
		if (coupon?.amountType === 'dollars') {
			$("input[name='discount_amount']").val(`$${coupon.amount}`);
			instance.allocationBuilder.set("amount", coupon.amount);
			instance.allocationBuilder.set("amountType", coupon.amountType);
		}
		else if (coupon?.amountType === 'percent') {
			$("input[name='discount_amount']").val(`${coupon.amount}%`);
			instance.allocationBuilder.set("amount", coupon.amount);
			instance.allocationBuilder.set("amountType", coupon.amountType);
		}
		else $("input[name='discount_amount']").val("");
	},
	"change select[name='allocation_discount_type']": (event, instance) => {
		const discountType = $(event.currentTarget).val();
		const discount = _.find(Orgs.current().availableDiscountTypes(), (d) => d.type === discountType);
		if(!processPermissions({
			assertions: [{ context: "billing/configuration/discounts", action: "edit"}],
			evaluator: (person) => person.type=="admin" || person.type=="staff"
		}) && discount.amountType !== "percent") {
			$("input[name='amount_type'][value='percent']").attr("disabled", true);
		} else if (!processPermissions({
			assertions: [{ context: "billing/configuration/discounts", action: "edit"}],
			evaluator: (person) => person.type=="admin" || person.type=="staff"
		}) && discount.amountType == "percent") {
			$("input[name='amount_type'][value!='percent']").attr("disabled", true);
		}
		if (discount && discount.amount) {
			$("input[name='discount_amount']").val(discount.amount);
			$("input[name='amount_type'][value='" + discount.amountType + "']").removeAttr("disabled");
			$("input[name='amount_type'][value='" + discount.amountType + "']").prop("checked", true);
		}
	},
	"change input[name='override_rate']": (event, instance) => {
		const val = $(event.currentTarget).val();

		instance.overrideRate.set(( val && !isNaN(val)) ? parseFloat(val) : null);
	},
	"click .btnRemoveAllocation": (event, instance) => {
		event.preventDefault();
		const editId = $(event.currentTarget).data("id");
		const currentAllocation = _.find(instance.allocations.get(), (a) => a.id === editId );
		const payerAllocations = ["reimbursable", "reimbursable-with-copay"]
		if (currentAllocation && payerAllocations.includes(currentAllocation.allocationType)){
			mpSwal.fire({
				title: "Warning",
				text: "Removing this payer will increase the parent's balance owed on future invoices. It will also impact the General Attendance Report when filtered by subsidy agencies.",
				icon: "warning",
				showCancelButton: true,
				showCloseButton: true,
				cancelButtonText: "Remove",
				confirmButtonText: "Set Voucher End Date",
			}).then((result) => {
				if (result.value) {
					editAllocation(instance, currentAllocation);
				} else if (result.dismiss === "cancel") {
					removeAllocation(event, instance);
				}
			});
		} else {
			removeAllocation(event, instance);
		}
	},
	"click .btnEditAllocation": (event, instance) => {
		event.preventDefault();
		const editId = $(event.currentTarget).data("id");
		const currentAllocation = _.find(instance.allocations.get(), (a) => a.id === editId );
		editAllocation(instance, currentAllocation);
	},
});
Template.personBillingEnrollPlanModal.helpers({
	"availableBillingPlans": () => {
		return Template.instance().availableBillingPlans.get();
	},
	"getAllocationDateField": (selection) => {
		return {
			selectedDate: (selection === "payerStart" && Template.instance().allocationPayerStartDateField) ||
				(selection === "payerEnd" && Template.instance().allocationPayerEndDateField) ||
				Template.instance().allocationDateField,
			datePickerId: selection
		}
	},
	"getCouponDateField": () => {
		const dateFieldValue = Template.instance().allocationDateField.get();
		return dateFieldValue && new moment.tz(dateFieldValue, Orgs.current().getTimezone()).format('MM/DD/YYYY');
	},
	"allocations": () => {
		return Template.instance().allocations.get();
	},
	"planType":() => {
		const instance = Template.instance();
		const selectedPlan = instance.selectedPlan.get();
		const editId = instance.allocationBuilder.get("editId");
		const currentAllocation = _.find(instance.allocations.get(), (a) => a.id === editId);
		if (selectedPlan?.frequency === "charged_daily_invoiced_monthly" && instance.allocations.get().length === 0) {
			return true;
		} else if (currentAllocation?.allocationType === "reimbursable-with-copay" && currentAllocation.allocationDescription?.includes("charged_daily_invoiced_monthly")) {
			return true;
		}
	},
	"allocationBuilder": (prop) => {
		return Template.instance().allocationBuilder.get(prop);
	},
	"allocationEditField"(prop) {
		const instance = Template.instance(),
			editId = instance.allocationBuilder.get("editId");
		if (!editId) return;
		const currentAllocation = _.find(instance.allocations.get(), (a) => a.id === editId);
		switch(prop) {
			case "type":
				return currentAllocation.allocationType;
			case "couponCode":
				return currentAllocation.discountType;
			case "amount":
				const isCoupon = (currentAllocation.allocationType === 'coupon');
				if (isCoupon && currentAllocation?.amountType === 'dollars')  return `$${currentAllocation.amount}`;
				else if (isCoupon && currentAllocation?.amountType === 'percent') return `${currentAllocation.amount}%`;
				else return currentAllocation[prop];
			default:
				return currentAllocation[prop];
		}
	},
	checkedIfEqualOrBlank(val1, val2) {
		const result = !val1 || val1 === "" || val1 === val2;
		return result ? "checked" : "";
	},
	cantModifyAmount() {
		const allocationType = Template.instance().allocationBuilder.get('type');

		switch(allocationType){
			case 'discount':
				return allocationType && !(processPermissions({
					assertions: [{ context: "billing/configuration/discounts", action: "edit"}],
					evaluator: (person) => person.type === "admin" || person.type === "staff"
				}))
			case 'coupon':
				// Don't allow modification of amount for coupons
				return true;
		}
	},
	"availableFamilyMembers": () => {
		const query= {targetId: Template.instance().data.personId, relationshipType:"family"};
		const availableFamily = Relationships.find(query).fetch();
		return _.map(availableFamily,
			(r) => { return People.findOne({_id: r.personId}); });
	},
	"availableDiscountTypes": () => {
		return Orgs.current().availableDiscountTypes();
	},
	"availableCouponCodes": () => {
		const coupons = Orgs.current().availableCouponCodes();
		const now = moment.tz(Orgs.current().getTimezone()).valueOf();
		const plan = getPlanDetails(Template.instance());
		const filteredCoupons = [];
		const couponBeingEdited = Template.instance().allocationBuilder.get("code") || '';
		const isEditing = Template.instance().allocationBuilder.get("editing");
		const familyRelationships = Relationships.find({orgId: Orgs.current()._id, targetId: Template.instance().data.personId, relationshipType: "family"}).map(rel => rel.personId);
		const props = {
			now,
			plan,
			allocations: Template.instance().allocations.get(),
			isEditing,
			couponBeingEdited,
			familyRelationships,
			orgId: Orgs.current()._id
		}
		coupons.forEach((coupon) =>  {
			if (CouponUtils.showCouponInBillingTab({...props, currentCoupon: coupon})) {
				filteredCoupons.push(coupon);
			}
		})

		return filteredCoupons;
	},
	"availableReimbursementTypes": () => {
		return Orgs.current().availablePayerSources();
	},
	"copayFrequencyDescription": () => {
		const plan = getPlanDetails(Template.instance());
		if (plan && plan.adjustedFrequency) return "(paid " + plan.adjustedFrequency + ")";
	},
	"hasClassListAndTuitionPlanSelected": () => {
		if (Orgs.current().hasCustomization("report/classList/enabled") !== true) return false;

		const currentSelection = Template.instance().selectedCategory.get();
		return (currentSelection === "tuition") ? true : false;
	},
	"remainingAmounts": () => {
		const overrideRate = Template.instance().overrideRate.get();
		const selectedPlan = Template.instance().selectedPlan.get();
		const existingPlan = Template.instance().data.existingPlan;
		const personId = Template.instance().data.personId;
		const currentPlan = existingPlan || selectedPlan;
		const org = Orgs.current();

		if (!org || (!overrideRate && !currentPlan)) return;
		const planAmount = overrideRate || existingPlan?.calculatedAmount || currentPlan.amount;
		const planType = existingPlan?.masterPlanInfo.frequency || currentPlan.frequency;
		const allAllocations = Template.instance().allocations.get();
		const todayDate = moment().startOf('day').valueOf();
		const coversPlanStartDate = moment(todayDate).startOf("day");
		const coversPlanEndDate = coversPlanStartDate.clone().endOf("month");
		const coveredDays = Reservations.findWithRecurrence({startDateValue:coversPlanStartDate.valueOf(),
			endDateValue:coversPlanEndDate.clone().add(1,'day').valueOf(),
			query:{orgId:org._id,selectedPerson:personId}}).map( d => new moment(d.scheduledDate).format("YYYY-MM-DD"));
		const coveredDaysCount= coveredDays.length;
		if(planType=== "charged_daily_invoiced_monthly"){
			return BillingUtils.getDailySubsidyAmount(planAmount, allAllocations, org, coveredDaysCount );
		} else {
			return BillingUtils.getDiscountedAmount(planAmount, allAllocations, org);
		}
	},
	"estimatedFirstInvoiceDate"() { //This helper function is not in use also not returning anything so not changing Invoices.getPlanDetailsBySchedule method to async
		const instance = Template.instance(),
			startDate = instance.data.startDate,
			endDate = instance.data.endDate,
			selectedPlan = instance.selectedPlan.get();
		if (startDate && selectedPlan) {
			const planDetailsForSchedule = Invoices.getPlanDetailsBySchedule({
				plan: selectedPlan,
				reservation: {
					scheduledDate: startDate,
					scheduledEndDate: endDate,
				}
			});
		}
	},
	shouldShowBillingRateOverride() {
		return processPermissions({
			assertions: [{ context: "billing/configuration/override", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		});
	}	,
	disabledLinkedPlan() {
		return !!Template.instance().data.reservationId;
	},
	shouldShowFields() {
		return !Template.instance().data.registrationReview;
	}
});

function getPlanDetails(instance) {
	let currentPlan = instance.data.existingPlan;

	if (!currentPlan) {
		const selectedPlanId = $("select[name=plan]").val();
		if (selectedPlanId) currentPlan = _.find(instance.data.availableBillingPlans, p => p._id == selectedPlanId);
	}
	if (currentPlan) {
		currentPlan.adjustedFrequency = currentPlan?.masterPlanInfo?.frequency?.startsWith("weekly") ? "weekly" : currentPlan.frequency;
		return currentPlan;
	}
}

Template.personBillingYearEndStatement.onCreated( function()  {
	this.formData = new ReactiveVar();
	this.filterStartDate = new ReactiveVar(new moment.tz(this.timezone).subtract(30, 'days'));
	this.filterEndDate = new ReactiveVar(new moment.tz(this.timezone));
	this.person = new ReactiveVar();
	getPeopleById(this.data?.personId).then((person) => {
		this.person.set(person);
	})
});

Template.personBillingYearEndStatement.helpers({
	"dateFields": () => {
		console.log("dateFields helper===>")
		return {
			filterStartDate: Template.instance().filterStartDate,
			filterEndDate: Template.instance().filterEndDate
		}
	},
	"legalEntities": () => {
		const data =  Template.instance().formData.get();
		return data && data.legalEntities;
	},
	"childBirthday": (person) => {
		const orgs = Orgs.current();
		const prefix = orgs.profileDataPrefix();
		return prefix ? (person[prefix] && person[prefix].birthday) : person.birthday;
	},
	"hasStatementData": () => {
		const data =  Template.instance().formData.get();
		return data && data.generated;
	},
	"isEmptyReport": () => {
		const data =  Template.instance().formData.get();
		return data && data.generated && data.legalEntities.length === 0;
	},
	"isSummary": () => {
		const data =  Template.instance().formData.get();
		return data && data.reportType && data.reportType == "summary";
	},
	"dateInfo": () => {
		const data =  Template.instance().formData.get();
		return data && data.dateInfo;
	},
	taxYears() {
		return MiscUtils.numberRange(2018, new Date().getFullYear() - 1).reverse();
	},
	"entities": () => {
		const data =  Template.instance().formData.get();
		return data && data.entities;
	},
	"parentData": () => {
		return Template.instance().person.get()
	}
});

Template.personBillingYearEndStatement.events({
	"click #btnSave": async () => {
    const html2pdf = await import('html2pdf.js').then(module => module.default);
		var element = document.getElementById('yearEndStatementBody');
		html2pdf(element);
	},
	"click #btnLoad": (e, i) => {
		const templateData = Template.instance().data;
		const timezone = this.timezone;
		const options = {
			dateType: $("input[name='date_type']:checked").val(),
			reportType: $("input[name='reportType']:checked").val(),
			personId: templateData.personId,
		};

		if (templateData.deactivatedUser) {
			options.orgId = templateData.orgId;
			options.deactivatedUser = templateData.deactivatedUser;
		}

		if (options.dateType == "custom") {
			const startDate = new moment(i.filterStartDate.get());
			const endDate = new moment(i.filterEndDate.get());
			if (!startDate.isValid() || !endDate.isValid())
				mpSwal.fire("Error", "Please make sure you've chosen valid dates for your custom date range.", "error");
			options.startDate = i.filterStartDate.get().format("MM/DD/YYYY");
			options.endDate = i.filterEndDate.get().format("MM/DD/YYYY");
		} else {
			options.taxYear = $("#taxYear").val();
		}
		Meteor.callAsync("getYearEndStatementData", options).then(result => {
			i.formData.set(result);
		}).catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	},
	"change input[name='date_type']": (e,i) => {
		const newValue = e.currentTarget.value;
		if (newValue == "custom") {
			$(".billing-date-range").toggleClass("d-none", false); $(".tax-year").toggleClass("d-none", true);
		} else {
			$(".billing-date-range").toggleClass("d-none", true); $(".tax-year").toggleClass("d-none", false);
		}
	}
});

Template.personBillingSendManualPlanInvoice.events({
	"change input[name='prorate_invoice']": (event, instance) => {
		const isChecked = $(event.currentTarget).prop("checked"),
			currentMethod = $("select[name='prorate_type']").val() ;
		instance.showProrate.set((isChecked && currentMethod) || (Orgs.current().hasCustomization("billing/autoProrateByDate/enabled") && "start-date") || (isChecked && "percentage"));
	},
	"change select[name='prorate_type']": (event, instance) => {
		const currentMethod = $("select[name='prorate_type']").val();
		instance.showProrate.set(currentMethod);
	}
});

Template.personBillingSendManualPlanInvoice.helpers({
	"showProrate": (method) => {
		const self = Template.instance();
		return (!method && self.showProrate.get()) || (method && method == self.showProrate.get());
	},
	"showPlan": (plan) => {
		return _.contains(
			[
				BillingFrequencies.DAILY,
				BillingFrequencies.WEEKLY,
				BillingFrequencies.WEEKLY_SCHEDULED_DAILY,
				BillingFrequencies.MONTHLY,
				BillingFrequencies.SEMIMONTHLY,
				BillingFrequencies.BIMONTHLY,
				BillingFrequencies.BIWEEKLY,
				BillingFrequencies.SCALED_WEEKLY,
				BillingFrequencies.SCALED_BIWEEKLY,
				BillingFrequencies.SCALED_MONTHLY,
				BillingFrequencies.DAILY_CHARGED_MONTHLY,
			],
			plan.planDetails.frequency
		);
	}
});

Template.personBillingSendManualPlanInvoice.onCreated( function() {
	this.showProrate = new ReactiveVar();
});

Template.personBillingSendManualPlanInvoice.rendered = function() {
	$("#manualPlanInvoicePeriodStart").datepicker({ autoclose: true, todayHighlight: true });
	$("#manualPlanInvoiceDue").datepicker({ autoclose: true, todayHighlight: true });
}

function removeAllocation(event, instance){
	const removeIndex = $(event.currentTarget).data("index");
	const currentAllocations = instance.allocations.get();
	currentAllocations.splice(removeIndex, 1);
	instance.allocations.set(currentAllocations);
	$("input[name='allocations']").val(JSON.stringify(instance.allocations.get()));
}

function editAllocation(instance, currentAllocation){
	instance.allocationBuilder.set("code", currentAllocation.discountType);
	instance.allocationBuilder.set("type", currentAllocation.allocationType);
	instance.allocationBuilder.set("editId", currentAllocation.id);
	instance.allocationBuilder.set("amount", currentAllocation.amount);
	instance.allocationBuilder.set("amountType", currentAllocation.amountType);
	// I reset to trigger re-filter of coupon options
	instance.allocationBuilder.set("editing", false);
	instance.allocationBuilder.set("editing", true);

	instance.allocationDateField.set(currentAllocation.discountExpires);
	instance.allocationPayerStartDateField.set(currentAllocation.payerStartDate);
	instance.allocationPayerEndDateField.set(currentAllocation.payerEndDate);
	instance.allocationBuilder.set("building", true);
}

function getAuditTrailHistory(personId, instance) {
	Meteor.callAsync('getBillingHistoryAuditTrailForPerson', personId, BillingTabAuditTrailsMethodsList).then((auditTrails) => {
		instance.auditTrailsLoading.set(false);
		instance.auditTrails.set(auditTrails);
	}).catch((error) => {
		console.error("Error getting billing history audit trail:", error);
		instance.auditTrailsLoading.set(false);
	});
}
