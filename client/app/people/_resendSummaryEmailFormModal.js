import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './_resendSummaryEmailFormModal.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { hideModal } from '../main';

Template._resendSummaryEmailFormModal.events({
	"click #btnResendEmail": function(e) {
		e.preventDefault();
		Meteor.callAsync("resendSummaryEmail", FlowRouter.current().params._id, $("#inputSendCopy").prop('checked')).then((result) => {
			hideModal("#_resendSummaryEmailFormModal");
		}).catch((error) => {
			mpSwal.fire("Error", error.reason, "error");
		});
	}
});
