<template name="_addRelationship">
    <div class="container mt-4">
        <div class="card">
            <div class="card-header text-white" style="background-color: var(--primary);">
                <h2 class="mb-0">Add a contact</h2>
            </div>
            <div class="card-body">
                <form>
                    {{> _relationshipFormFields useFullWidthColumns=false rowClass="mb-3"}}
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 gy-8">
                        {{#each child in children}}
                        <div class="col mb-6">
                            {{> _childRelationshipCheckboxes
                                child=child
                                childName=child.fullName
                                updateChildData=updateChildData
                            }}
                        </div>
                        {{/each}}
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-secondary backBtn me-2 text-" style="color: crimson;">Go Back</button>
                        <button type="submit" class="btn text-white" style="background-color: var(--primary);" {{saveButtonAttributes}}>
                            {{saveButtonText}}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>