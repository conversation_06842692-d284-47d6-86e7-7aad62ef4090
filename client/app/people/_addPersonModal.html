<template name="_addPersonModal">
  <div id="_addPersonModal" class="modal fade" data-cy="add-new-person-form">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel" data-cy="modal-title">Add New Person</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" data-cy="dismiss-modal">
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body bg-white">
          <form id="newPersonForm">
            {{> _personAccount hideSave=true isCreate=true}}
          </form>
        </div>
        <div class="modal-footer">
          <!-- NOTE: the save button should execute on the yielded _personAccount JS -->
          <button data-cy="modal-save-button" type="button" class="btn btn-primary font-weight-bolder mr-2" id="add-person-modal-save">Save</button>
          <button data-cy="modal-close-button" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
        </div>
  		</div>
  	</div>
  </div>
</template>
