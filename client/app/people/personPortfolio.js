import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import './personPortfolio.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { People } from '../../../lib/collections/people';
import { Orgs } from '../../../lib/collections/orgs';
import { Curriculums } from '../../../lib/collections/curriculum';
import { Groups } from '../../../lib/collections/groups';
import { showModal } from '../main';
import '../simpleModal/simpleModal';
import './_assessmentAdjustment';

Template.personPortfolio.onCreated( function() {
	var self = this;
	console.log("FlowRouter", FlowRouter.current());
	self.portfolioSections = new ReactiveVar();
	self.filterStartDate = new ReactiveVar(new moment().startOf("day"));
	self.filterEndDate = new ReactiveVar(new moment().add(1, "days").startOf("day"));
});

Template.personPortfolio.onRendered( function() {
	var instance = this;

	getPortfolioData(instance);
});


Template.personPortfolio.helpers({
	whiteLabeling() {
		const org = Orgs.current();
		if(org.whiteLabel?.ROOT_URL && org.whiteLabel?.ROOT_URL.includes("lightbridge")) {
			return "https://assets.momentpath.com/customers/lightbridge/summaryemailv2019/logo.png"
		} else {
			return "/media/svg/icons/icon.svg"
		}
	},
	whiteLabelLogoWidth() {
		const org = Orgs.current()
		if(org.whiteLabel.ROOT_URL && org.whiteLabel.ROOT_URL.includes("lightbridge")) {
			return "w-120px"
		} else {
			return "w-50px"
		}
	},
	dateFields() {
		return {
			filterStartDate: Template.instance().filterStartDate,
			filterEndDate: Template.instance().filterEndDate,
		}
	},
	person() {
		const currentPerson = People.findOne({_id: FlowRouter.current().params._id}),
			defaultGroup = currentPerson?.defaultGroupId && Groups.findOne(currentPerson.defaultGroupId);

		if (currentPerson) 
			currentPerson.defaultGroupName = defaultGroup?.name;
		return currentPerson;
	},
	reportGeneratedLabel() {
		return "Report generated " + new moment().format("MMMM Do, YYYY");
	},
	reportCoversLabel() {
		const dates = FlowRouter.current().queryParams["date-range"].split(" - "),
			startDate = new moment(dates[0], "MM/DD/YYYY"),
			endDate = new moment(dates[1], "MM/DD/YYYY");
		return "From " + startDate.format("MMMM Do") + " - " + endDate.format("MMMM Do");
	},
	portfolioSections() {
		return Template.instance().portfolioSections.get();
	},
	assessmentLevels() {
		return Orgs.current().getAvailableAssessmentLevels();
	},
	widthForStandard(standard) {
		if (!Orgs.current()) return;
		return (((standard.useValue + 0.5) / (Orgs.current().getAvailableAssessmentLevels().length - 0.5)) * 100).toString() + "%";
	},
	ageGroupFilters() {
		const targetPerson = People.findOne({_id: FlowRouter.current().params._id});
		let ageGroups = Curriculums.activitiesAgeGroups();
		const currentDefaultGroupId = targetPerson && targetPerson.defaultGroupId,
			defaultGroup = currentDefaultGroupId && Groups.findOne(currentDefaultGroupId);
		if (defaultGroup && defaultGroup.activitiesAgeGroup) {
			const group = ageGroups.find( g => g.label == defaultGroup.activitiesAgeGroup);
			
			if (group) group.selected = true;
		}
		return ageGroups;
	},
	photoCount(media) {
		return media.length;
	},
	printView() {
		return FlowRouter.current().queryParams["view"] == "print";
	},
	categoryAnchorId(category) {
		return category.replaceAll(" ", "-");
	}
});

Template.personPortfolio.events({
	"click #btnPrint"() {
		window.print();		
	},
	"click .highlight-btn"(e, i) {
		const tgt = $(e.currentTarget).data("location");
		//console.log("tgt", tgt);
		//$(tgt).get(0).scrollIntoView();

		const id = tgt;
		const yOffset = -100; 
		const element = document.getElementById(id);
		const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;

		window.scrollTo({top: y, behavior: 'smooth'});
	},
	"click .adjust-measurement"(e, i) {
		const tgt = $(e.currentTarget).data("location"),
			standards = this,
			personId = FlowRouter.current().params._id,
			selectedStandard = standards.outputStandards.find( os => os.standard.standardId == tgt);

		if (tgt) {
			showModal("simpleModal", {
				title: "Adjust Observation",
				template: "_assessmentAdjustment",
				data: {  targetAdjustment: tgt, standards, selectedStandard },
				onSave: (saveEvent, formInstance, formFieldData) => {
					const selectedDocument = formFieldData["source-document"] && sourceDocuments.find( sd => sd.name == formFieldData["source-document"]);
					formFieldData.selectedDocumentIdx = selectedDocument && selectedDocument.location.match(/documents\[(\d+)\]/)[1];
	
					Meteor.callAsync("adjustAssessment", {...formFieldData, targetAdjustment: tgt, personId}).then( result => {
						mpSwal.fire("Adjustment added successfully.");
						$("#simpleModal").modal("hide");
						getPortfolioData(i);
					}).catch( error => {
						$(saveEvent.target).html('Save').prop("disabled", false);
						return mpSwal.fire("Error adding adjustment", error.reason, "error");
					})
				}
			});
		}
	}
});

function getPortfolioData(instance) {
	const dates = FlowRouter.current().queryParams["date-range"].split(" - "),
		startDate = new moment(dates[0], "MM/DD/YYYY").valueOf(),
		endDate = new moment(dates[1], "MM/DD/YYYY").add(1, "days").startOf("day").valueOf(),
		ageGroup = FlowRouter.current().queryParams["age-group"];

	const options = {
		startDate, 
		endDate,
		personId: FlowRouter.current().params._id,
		ageGroup
	};

	Meteor.callAsync("generatePortfolio", options).then( response => {
		instance.portfolioSections.set(response);
	}).catch( error => {
		mpSwal.fire("Error", "Problem generating portfolio: " + error.reason, "error");
	});
}