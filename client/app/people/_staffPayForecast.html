<template name="_staffPayForecast">
  <div class="row">
    <div class="col-lg-9 col-xl-6 offset-xl-3">
      <span class="font-size-h3 mb-5">Payroll Forecast</span>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label pl-4">Values for the Week (Total time per day in hours)</label>
    <div class="col-lg-9 col-xl-6 d-flex flex-row align-items-center">
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="staffForecastMondayVal" step=".25" min="0" max="24" value={{getStaffForecastDayValue "M"}}>
        <span>Mon</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="staffForecastTuesdayVal" step=".25" min="0" max="24" value={{getStaffForecastDayValue "T"}}>
        <span>Tue</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="staffForecastWednesdayVal" step=".25" min="0" max="24" value={{getStaffForecastDayValue "W"}}>
        <span>Wed</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="staffForecastThursdayVal" step=".25" min="0" max="24" value={{getStaffForecastDayValue "R"}}>
        <span>Thu</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="staffForecastFridayVal" step=".25" min="0" max="24" value={{getStaffForecastDayValue "F"}}>
        <span>Fri</span>
      </div>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label pl-4">Extra Values (Total time per day in hours)</label>
    <div class="col-lg-9 col-xl-6 d-flex flex-row align-items-center">
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="staffForecastBreakTime" step=".25" min="0" max="24" value={{getStaffForecastDayValue "BREAK"}}>
        <span>Break Time</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="staffForecastPTO" step=".25" min="0" max="24" value={{getStaffForecastDayValue "PTO"}}>
        <span>Holiday / PTO</span>
      </div>
      <div class="d-flex flex-column mr-4 align-items-center">
        <input type="number" class="form-control form-control-solid w-auto" id="staffForecastEvent" step=".25" min="0" max="24" value={{getStaffForecastDayValue "EVENT"}}>
        <span>Events</span>
      </div>
    </div>
  </div>
  <div class="form-group row">
    <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
    <div class="col-lg-9 col-xl-6 d-flex flex-row">
      <div class="btn btn-primary font-weight-bolder mr-4" id="btnSaveStaffForecast">Save Payroll Forecast</div>
    </div>
  </div>
</template>
