import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {CustomizationsService} from "../../../lib/customizations";
import {AsYouType, parsePhoneNumber} from "libphonenumber-js";
import { base64StringToBlob } from "blob-util";
import $ from 'jquery';
import './_profileBoxFieldGroup.html';
import { processPermissions } from '../../../lib/permissions';
import _ from '../../../lib/util/underscore';
import { Orgs } from '../../../lib/collections/orgs';
import { remapDownloadUrl } from '../../../mpweb';
import { Person } from '../../../lib/collections/people';

Template._profileBoxFieldGroup.onRendered(function() {
	$(".profile-date-input").datepicker({
		format: 'mm/dd/yyyy',
		keepEmptyValues: false,
		autoclose: true,
		todayHighlight: true,
		clearBtn: true
	});
});

Template._profileBoxFieldGroup.helpers({
	isDateField: function(field) {
		return (field.type == "date") ? true : false;
	},
	queryFieldValues: function(field) {
		if (field.type == "query") {
			const instanceData = Template.instance().data, person = instanceData && instanceData.person;
			return Orgs.current().queryData(field.source, {personId: person._id});
		}
	},
	showField: function(field) {
		const person = Template.instance().data && Template.instance().data.person;
		return !(!CustomizationsService.canViewContactInfo(person._id) && field.name && field.name.startsWith('phone'));
	},
	getProfileFieldValue: function(fieldName) {

		const instanceData = Template.instance().data;
		const person = new Person(instanceData.person);
		const fieldDef = _.find(instanceData.allowableProfileFields, (f) => {return fieldName == f.name;});

		//lookup to return value
		let dataValue;
		if (fieldName == "pinCode") {
			dataValue = person.pinCode;
		}

		else if (fieldName == "pinCodeSupplemental") {
			dataValue = person.pinCodeSupplemental;
		}
		else if (instanceData.path && instanceData.path.length > 0) {
			let path = instanceData.path;
			let key = (person.profileData ? "profileData" : "" ) + path.replaceAll("/", ".");

			if ($.isNumeric(instanceData.index)) key = key + "." + instanceData.index;

			key = key + "." + fieldName;
			dataValue = _.deep(person,key);
		} else  {
			dataValue = (person.profileData && person.profileData[fieldName]) || person[fieldName];
		}

		//now return either the value or a query result
		if (fieldDef && fieldDef.type == "query") {
			const qd = Orgs.current().queryData(fieldDef.source, {personId: person._id});
			const selectedRecord = _.find(qd, (d) => { return d.id == dataValue});

			if (selectedRecord && selectedRecord.id) return selectedRecord.id;
			if (selectedRecord && selectedRecord.value) return selectedRecord.value;
		}
		if (fieldName === "phonePrimary" && dataValue) {
			dataValue = parsePhoneNumber(dataValue, 'US').formatNational();
		}
		return dataValue;
	},
	getHoursBlocks: function() {
		const instanceData = Template.instance().data;
		const person = instanceData.person;
		const hoursData = person.profileData && person.profileData.hours;
		return _.map( hoursData, (v, k) => {
			return {
				dayLabel: k.capitalizeFirstLetter(),
				hoursDescription: (v.start ? new moment(v.start, "H").format("h a")  : "?" )
					+ " - "
					+ (v.end ? new moment(v.end, "H").format("h a") : "?" )
			};
		});
	},
	pathConcat(addPath, i) {
		let outPath = this.path + "/" + addPath;
		return outPath;
	},
	collapseIdForGroup() {
		return this.group && this.group.name + (this.index ? this.index : "");
	},
	dataIndexesForPath(path) {
		const instanceData = Template.instance().data;
		const person = instanceData.person;
		const key = (person.profileData ? "profileData" : "") + path.replace("/", ".");

		const data = _.deep(person, key);

		return _.times(data ? data.length : 0, (i) => { return i; });
	},
	displayIndex() {
		if (parseInt(this.index) >= 0) return parseInt(this.index) + 1;
	},
	isEditableField(field) {
		const instanceData = Template.instance().data;
		const person = instanceData.person,
			userPerson = Meteor.user() && Meteor.user().fetchPerson(),
			adpReadonlyFields = [
				'phonePrimary',
				'staffAddress',
				'staffAddressCity',
				'staffAddressState',
				'staffAddressZip',
				'payRate',
				'employeeClassification'
			];
		return field.type != "hoursV1" && userPerson &&
			processPermissions({
				assertions: [{ context: "people/profile", action: "edit" }],
				evaluator: (thisPerson) => thisPerson.type=="admin" || (thisPerson.type=="staff" && thisPerson._id == person._id) || field.isFamilyEditable
			}) &&
			(
				!['staff', 'admin'].includes(person.type) ||
				processPermissions({
					assertions: [{ context: 'people/manageAdpStaffAndAdmins', action: 'edit' }],
					evaluator: (thisPerson) => thisPerson.type === 'admin' || (thisPerson.type === 'staff' && thisPerson._id === person._id) || field.isFamilyEditable
				}) ||
				!adpReadonlyFields.includes(field.fieldPath)
			);
	},
	disabledIfNotEditable(truthyVal) {
		return (!truthyVal) ? "disabled" : "";
	},
	addMultiple(field) {
		 if(field.multi) {
			 $(`#select-${field.name}`).select2({
				 multiple: true,
			 })
			return 'multiple'
		}

		 return '';
	},
});

Template._profileBoxFieldGroup.events({
	'click .crm-document': function (e, i) {
		e.preventDefault();
		e.stopPropagation();
		const docId = Number(e.currentTarget.dataset?.docId);
		const docInfo = i.data.group?.fields?.find(f => f.crmId === docId);
		if (docInfo) {
			Meteor.callAsync('getCrmDocumentBlob', docInfo.crmId, docInfo.familyId).then(res => {
				if (res) {
					openFile(res, docInfo.filename, docInfo.mimeType);
				}
			});
		}
	},
	'input .phone': function (e, i) {
        const ayt = new AsYouType('US');
        ayt.input(e.target.value);
        e.target.value = ayt.getNumber() ? ayt.getNumber().formatNational() : '';
    },
	"click .edit-field": function(e) {
		$(e.currentTarget).closest(".row").find(".hidden").removeClass("hidden");
		$(e.currentTarget).closest(".row").find(".edit-field,.fieldValue").addClass("hidden");
	},
	"click .field-save": function(e) {
		var saveTarget = $(e.currentTarget);
		const fieldPath = {
			fieldId: saveTarget.closest(".row").find(".field-data-id").val(),
			fieldType: saveTarget.closest(".row").find(".field-data-type").val(),
			fieldPath: saveTarget.closest(".row").find(".field-data-path").val(),
			fieldIndex: saveTarget.closest(".row").find(".field-data-index").val()
		};
		var newFieldValue = (fieldPath.fieldType == "buttons") ?
			saveTarget.closest(".row").find(".btn-group button.active").text() :
			saveTarget.closest(".row").find(".form-control").val();

		saveTarget.closest(".row").find(".btn-group,.form-control,.field-save,.field-help,.file-maintenance,.remove-date-field").addClass("hidden");
		saveTarget.closest(".row").find(".edit-field,.fieldValue").removeClass("hidden");

		if (fieldPath.fieldType == "attachments" || fieldPath.fieldType== "file") return;

		var personId = FlowRouter.current().params._id;
		if (personId)
			Meteor.callAsync("updateProfileFieldValue", personId, fieldPath, newFieldValue).then(() => {
			}).catch((error) => {	
				mpSwal.fire("Error", error.reason, "error");
			});
		else
			saveTarget.closest(".row").find(".fieldValue").text(newFieldValue);
	},
	"click .remove-date-field": function(e) {
		var saveTarget = $(e.currentTarget);
		const fieldPath = {
			fieldId: saveTarget.closest(".row").find(".field-data-id").val(),
			fieldType: saveTarget.closest(".row").find(".field-data-type").val(),
			fieldPath: saveTarget.closest(".row").find(".field-data-path").val(),
			fieldIndex: saveTarget.closest(".row").find(".field-data-index").val()
		};

		saveTarget.closest(".row").find(".btn-group,.form-control,.field-save,.field-help,.file-maintenance,.remove-date-field").addClass("hidden");
		saveTarget.closest(".row").find(".edit-field,.fieldValue").removeClass("hidden");
		var personId = FlowRouter.current().params._id;
		if (personId) {
			Meteor.callAsync("clearDateProfileFieldValue", personId, fieldPath).then(() => {
			}).catch((error) => {
				mpSwal.fire("Error", error.reason, "error");
			});
		} else {
			saveTarget.closest(".row").find(".fieldValue").text("");
		}
	},
	"click .btn-group": function(e) {
		var clickTarget = $(e.target);
		clickTarget.closest(".btn-group").find(".btn").removeClass("active");
		clickTarget.addClass("active");
	},
	"click .remove-attachment": function(e) {
		e.preventDefault();
		mpSwal.fire({
			title:"Are you sure?",
			text:"This will permanently remove the attachment.",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, remove attachment"
		}).then(async (result) =>
		{
			if (result.value) {
				var removeLink = $(e.currentTarget);
				var fieldId = removeLink.attr("data-fieldid");
				const fieldPath = {
					fieldId: fieldId,
					fieldType: removeLink.closest(".row").find(".field-data-type").val(),
					fieldPath: removeLink.closest(".row").find(".field-data-path").val(),
				}

				var tokenId = removeLink.attr("data-token");
				var personId = FlowRouter.current().params._id;

				var removalData = {
					action:"remove",
					mediaToken: tokenId
				};
				if (personId)
					await Meteor.callAsync("updateProfileFieldValue", personId, fieldPath, removalData);
			}
		});
	},
	'change .profileInputFile': function(event) {
		event.preventDefault();
		event.stopImmediatePropagation();
		var uploadFiles = $(event.target);
    console.log(uploadFiles);
		var fieldId = uploadFiles.first().data("fieldid");
		const fieldPath = {
			fieldId: fieldId,
			fieldPath: uploadFiles.closest(".row").find(".field-data-path").val(),
			fieldIndex: uploadFiles.closest(".row").find(".field-data-index").val(),
		}
    console.log(fieldPath);
		var uploadFile = uploadFiles[0];
		if (uploadFile && uploadFile.files.length > 0 ) {
			var metaContext = {tokenId: tokenString()};

			var uploader = new Slingshot.Upload("myDocumentUploads", metaContext);
			uploader.send(uploadFile.files[0], async function (error, downloadUrl) {
			  if (error) {
			    alert (error);
			  }
			  else {
				downloadUrl = remapDownloadUrl(downloadUrl);
			  	console.log("download = " + downloadUrl);
			  	var mediaType = uploadFile.files[0].type;

			  	var uploadedFile = {
			  		name: uploadFile.files[0].name,
			  		mediaUrl: downloadUrl,
			  		mediaToken: metaContext.tokenId,
			  		mediaFileType: mediaType,
			  		mediaPath: Meteor.user().orgId + "/" + Meteor.user()._id + "/" + metaContext.tokenId
			  	};

			  	var personId = FlowRouter.current().params._id;
				if (personId)
					 await Meteor.callAsync("updateProfileFieldValue", personId, fieldPath, uploadedFile);
			  }
			});
		}
	}
});


var tokenString = function() {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i=0; i<string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum,rnum+1);
	}
	return randomstring;
};

export function downloadFile(fileUrl, filename) {
	const link = document.createElement('a');
	link.href = fileUrl;
	link.download = filename;
	// Open the link for the user
	link.click();
	return link.href;
}

export function openFile(fileContents, filename, mimeType) {
	const blobContents = base64StringToBlob(fileContents);
	const fileBlob = new Blob([blobContents], { type: mimeType });
	const fileUrl = URL.createObjectURL(fileBlob);
	const href = downloadFile(fileUrl, filename);
	URL.revokeObjectURL(href);
}
