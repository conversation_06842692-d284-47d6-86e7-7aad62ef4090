<template name="_childRelationshipCheckboxes">
  <div class="mb-4 child-relationship-container" data-child-id="{{child._id}}">
    <h3 class="mb-3 ps-5 pl-3 bold">{{childName}}</h3>
    <div class="row mb-4">
      <div class="col d-flex align-items-center">
        <label class="checkbox checkbox-primary text-black {{#if disablePrimaryCaregiver}}text-muted opacity-75{{/if}}" data-cy="primary-caregiver-checkbox">
          <input type="checkbox" id="isPrimaryCaregiver_{{child._id}}" checked="{{isPrimaryCaregiver}}" disabled="{{disablePrimaryCaregiver}}">
          <span class="mr-3"></span>
          Primary Caregiver
        </label>
      </div>
    </div>
    <div class="row mb-4">
      <div class="col d-flex align-items-center">
        <label class="checkbox checkbox-primary text-black secondary-checkbox {{#if isDisabled}}text-muted opacity-75{{/if}}" data-cy="authorized-pickup-checkbox">
          <input type="checkbox" id="isAuthorizedPickup_{{child._id}}" checked="{{isAuthorizedPickup}}" disabled="{{isDisabled}}">
          <span class="mr-3"></span>
          Authorized Pickup
        </label>
      </div>
    </div>
    <div class="row mb-4">
      <div class="col d-flex align-items-center">
        <label class="checkbox checkbox-primary text-black secondary-checkbox {{#if isDisabled}}text-muted opacity-75{{/if}}" data-cy="emergency-contact-checkbox">
          <input type="checkbox" id="isEmergencyContact_{{child._id}}" checked="{{isEmergencyContact}}" disabled="{{isDisabled}}">
          <span class="mr-3"></span>
          Emergency Contact
        </label>
      </div>
    </div>
  </div>
</template>