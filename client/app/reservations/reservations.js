import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import '../calendar/calendar.js';
import './reservationsList.js';
import './reservations.html';
import '../calendar/calendar';
import './reservationsList';
import './staffSchedule';

Template.reservations.onCreated(function() {
	var self = this;
	self.currentTab = new ReactiveVar();
});

Template.reservations.events({
	"click .reservations-tabs"(e, i) {
		const target = $(e.currentTarget).data("destination");
		i.currentTab.set(target);
	}
});

Template.reservations.helpers({
	activeIfSelected(id) {
		const currentTab = Template.instance().currentTab.get() || "calendar";
		return currentTab == id ? "active" : "";
	}
});
