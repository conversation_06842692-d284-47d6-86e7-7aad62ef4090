<template name="reservationFormModal">
  <div id="reservationFormModal" class="modal fade">
  	<div class="modal-dialog modal-dialog-scrollable modal-lg" >
  		<div class="modal-content" style="height:auto;min-height: 100%;">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Schedule Entry</h5>
          <div class="d-flex align-items-center">
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div data-cy="schedule-entry-body" class="modal-body bg-white">
          {{#if hasRecurrenceParent}}
            <div class="separator separator-dashed mt-2 mb-8"></div>
            <div class="d-flex flex-row align-items-center justify-content-center font-weight-bolder mb-4">
              This Reservation was created from a recurring series
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
              <div class="col-lg-9 col-xl-6">
                <div class="btn btn-primary font-weight-bolder mr-2" id="editEventSeries">
                  Edit Recurring Series
                </div>
              </div>
            </div>
            <div class="separator separator-dashed my-8"></div>
          {{/if}}
          <form id="reservationForm">
            {{#if reservationTypeEnabled}}
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Type</label>
                <div class="col-lg-9 col-xl-6">
                  {{#if isNew}}
                    <select class="form-control form-control-lg form-control-solid" id="selectedType">
                      <option value="person">Person</option>
                      <option value="staff">Staff</option>
                    </select>
                  {{else}}
                    <input type="text" class="form-control form-control-lg form-control-solid" value="{{reservation.reservationType.capitalizeFirstLetter}}" disabled />
                  {{/if}}
                </div>
              </div>
            {{/if}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Date</label>
              <div class="col-lg-9 col-xl-6">
                <input data-cy="schedule-reservation-date" class="form-control form-control-lg form-control-solid" type="text" id="scheduleReservationDate" value="{{formattedScheduledDate}}"/>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Group</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="target-group" class="form-control form-control-lg form-control-solid" id="targetGroup">
                  <option value="default">Default</option>
  								{{#each group in availableGroups}}
  								  <option value="{{group._id}}" {{selectedIfEqual reservation.groupId group._id}}>{{group.name}}</option>
  								{{/each}}
                </select>
              </div>
            </div>
            {{#if scheduleTypes}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Schedule Type</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="schedule-type" class="form-control form-control-lg form-control-solid" id="scheduleType">
                  {{#each scheduleTypes}}
                    <option value="{{_id}}" {{selectedIfEqual reservation.scheduleType _id}}>{{type}}</option>
                  {{/each}}
                </select>
              </div>
            </div>
            {{/if}}
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Recurrence</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="recurrence-select" class="form-control form-control-lg form-control-solid" id="repeatSelect">
                  <option value="dnr">Does not repeat</option>
                  <option value="repeats" {{selectedIfEqual reservation.recurringType "weekly"}}>Repeats</option>
                </select>
              </div>
            </div>
            <div style="{{#unless isRepeatingReservation}}display:none;{{/unless}}">
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label"></label>
                <label>Repeats every <input type="text" class="form-control form-control-solid" name="frequency" value="{{reservation.recurringFrequency}}" style="width:100px;display:inline"> week(s)</label>
              </div>
                <div class="row d-flex select-multi-group form-group">
                    <div class="col-3 d-flex flex-column align-items-center"></div>
                    <div class="d-flex flex-column align-items-center mr-4">
                        <label class="checkbox checkbox-primary">
                            <input
                                    data-cy="sunday"
                                    type="checkbox"
                                    id="recursSun"
                                    name="recurs"
                                {{ checkedForDay "sun" }}
                                {{ disabledIfUnavailable 0 }}
                            >
                            <span class="mr-3"></span>
                            Sun
                        </label>
                        <div data-type="unavailable-note" class="{{ unavailableNoteClass 0 }}">Unavailable:<br>At Capacity</div>
                    </div>
                    <div class="d-flex flex-column align-items-center mr-4">
                        <label class="checkbox checkbox-primary">
                            <input
                                    data-cy="monday"
                                    type="checkbox"
                                    id="recursMon"
                                    name="recurs"
                                {{ checkedForDay "mon" }}
                                {{ disabledIfUnavailable 1 }}
                            >
                            <span class="mr-3"></span>
                            Mon
                        </label>
                        <div data-type="unavailable-note" class="{{ unavailableNoteClass 1 }}">Unavailable:<br>At Capacity</div>
                    </div>
                    <div class="d-flex flex-column align-items-center mr-4">
                        <label class="checkbox checkbox-primary">
                            <input
                                    data-cy="tuesday"
                                    type="checkbox"
                                    id="recursTue"
                                    name="recurs"
                                {{ checkedForDay "tue" }}
                                {{ disabledIfUnavailable 2 }}
                            >
                            <span class="mr-3"></span>
                            Tue
                        </label>
                        <div data-type="unavailable-note" class="{{ unavailableNoteClass 2 }}">Unavailable:<br>At Capacity</div>
                    </div>
                    <div class="d-flex flex-column align-items-center mr-4">
                        <label class="checkbox checkbox-primary">
                            <input
                                    data-cy="wednesday"
                                    type="checkbox"
                                    id="recursWed"
                                    name="recurs"
                                {{ checkedForDay "wed" }}
                                {{ disabledIfUnavailable 3 }}
                            >
                            <span class="mr-3"></span>
                            Wed
                        </label>
                        <div data-type="unavailable-note" class="{{ unavailableNoteClass 3 }}">Unavailable:<br>At Capacity</div>
                    </div>
                    <div class="d-flex flex-column align-items-center mr-4">
                        <label class="checkbox checkbox-primary">
                            <input
                                    data-cy="thursday"
                                    type="checkbox"
                                    id="recursThu"
                                    name="recurs"
                                {{ checkedForDay "thu" }}
                                {{ disabledIfUnavailable 4 }}
                            >
                            <span class="mr-3"></span>
                            Thu
                        </label>
                        <div data-type="unavailable-note" class="{{ unavailableNoteClass 4 }}">Unavailable:<br>At Capacity</div>
                    </div>
                    <div class="d-flex flex-column align-items-center mr-4">
                        <label class="checkbox checkbox-primary">
                            <input
                                    data-cy="friday"
                                    type="checkbox"
                                    id="recursFri"
                                    name="recurs"
                                {{ checkedForDay "fri" }}
                                {{ disabledIfUnavailable 5 }}
                            >
                            <span class="mr-3"></span>
                            Fri
                        </label>
                        <div data-type="unavailable-note" class="{{ unavailableNoteClass 5 }}">Unavailable:<br>At Capacity</div>
                    </div>
                    <div class="d-flex flex-column align-items-center mr-4">
                        <label class="checkbox checkbox-primary">
                            <input
                                    data-cy="saturday"
                                    type="checkbox"
                                    id="recursSat"
                                    name="recurs"
                                {{ checkedForDay "sat" }}
                                {{ disabledIfUnavailable 6 }}
                            >
                            <span class="mr-3"></span>
                            Sat
                        </label>
                        <div data-type="unavailable-note" class="{{ unavailableNoteClass 6 }}">Unavailable:<br>At Capacity</div>
                    </div>
                </div>
              <div class="form-group row">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Set End Date</label>
                <div class="d-flex flex-row align-items-center justify-content-center">
                  <div class="checkbox-list">
                    <label class="checkbox checkbox-primary">
                      <input data-cy="set-end-date" type="checkbox" id="setEndDate" {{checkedIfEq scheduledEndDateActive true}}> 
                      <span></span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="form-group row" style="{{#unless scheduledEndDateActive}}display:none;{{/unless}}">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">End Date</label>
                <div class="col-lg-9 col-xl-6">
                  <input data-cy="end-date-input" class="form-control form-control-lg form-control-solid" type="text" id="scheduleReservationEndDate" value="{{formattedScheduledEndDate}}"/>
                </div>
              </div>
              <div class="form-group row" style="{{#unless cancellationReasonActive}}display:none;{{/unless}}">
                <label class="col-xl-3 col-lg-3 text-right col-form-label">Cancellation Reason</label>
                <div class="col-lg-9 col-xl-6">
                  <select data-cy="cancellation-reason-input" class="form-control form-control-lg form-control-solid" id="cancellationReasonId">
                    <option value=""></option>
                    {{#each reason in cancellationReasons}}
                      <option value="{{reason._id}}" {{selectedIfEqual reservation.cancellationReasonId reason._id}}>{{reason.reason}}</option>
                    {{/each}}
                  </select>
                </div>
              </div>
            </div>
            <div class="form-group row">
              <div class="col-xl-3 col-lg-3"></div>
              {{#unless trueIfEq totalHours ''}}
              <label class="col-lg-9 col-xl-6 font-weight-bolder {{#if overtime}}text-danger{{/if}}">
                Total Hours Scheduled: {{totalHours}}
                <a href="#" data-toggle="tooltip" data-placement="right" title="This total is based on actual hours worked plus all remaining scheduled hours (from all schedules, if there are more than one) for this employee for the week selected"><i class="fad fad-primary fa-info-circle"></i></a>
              </label>
              {{/unless}}
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Arrival Time</label>
              <div class="col-lg-9 col-xl-6">
                <input class="form-control form-control-lg form-control-solid" type="time" name="timePicker" id="scheduledTime" value="{{timeFormatted}}"/>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">End Time</label>
              <div class="col-lg-9 col-xl-6">
                <input class="form-control form-control-lg form-control-solid" type="time" name="timeEndPicker" id="scheduledEndTime" value="{{endTimeFormatted}}"/>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-xl-3 col-lg-3 text-right col-form-label">Person</label>
              <div class="col-lg-9 col-xl-6">
                <select data-cy="select-person" class="form-control form-control-lg form-control-solid" id="selectedPerson" {{isMultipleSelect}}>
                  <option value="">None selected</option>
                  {{#each people}}
                    <option value="{{_id}}" {{selectedIfEqual _id reservation.selectedPerson}}>{{firstName}} {{lastName}}</option>
                  {{/each}}
                </select>
              </div>
            </div>
          </form>
          {{#if showLinkedBillingPlanForm}}
          <div class="p-4 position-relative mt-3 border-1 border border-dark rounded">
            {{#if showCloseLinkedPlanButton}}
              <button type="button" class="btn btn-icon btn-sm position-absolute"
                      style="top: 10px; right: 10px;"
                      id="btnCloseLinkedPlan">
                <i class="fa fa-times text-danger"></i>
              </button>
            {{/if}}

            {{> personBillingEnrollPlanModal
            availableBillingPlans=linkedPlanDetails.availableBillingPlans
            personId=linkedPlanDetails.personId
            enrollmentDate=linkedPlanDetails.enrollmentDate
            isScheduleLinked=true
            startDate=linkedPlanDetails.startDate
            endDate=linkedPlanDetails.endDate
            filterBillingPlans=true }}
          </div>
          {{/if}}
          {{#if showLinkedBillingPlanButton}}
            <div class="form-group row">
              <label  class="col-xl-3 col-lg-3 text-right col-form-label"></label>
              <div class="col-lg-9 col-xl-6">
                <button class="btn btn-primary" id="btnAddLinkedBillingPlan" data-cy="add-linked-billing-plan">Add Linked Billing Plan</button>
              </div>
            </div>
          {{/if}}
        </div>
        <div class="modal-footer">
          {{#if isNew}}
            <button type="button" class="btn btn-primary font-weight-bolder mr-2 createReservation" id="createReservationAndStayOpen" data-stayopen="true">Save &amp; Create Another</button>
          {{/if}}
          <button data-cy="save-schedule-entry" type="button" class="btn btn-primary font-weight-bolder mr-2 createReservation" id="createReservationSave" data-stayopen="false">Save</button>
          <button data-cy="close-schedule-entry" type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal" {{isDisabled}}>Close</button>
        </div>
  		</div>
  	</div>
  </div>
</template>
