import moment from 'moment-timezone';
import { Meteor } from 'meteor/meteor';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { CALENDAR_EVENTS, CALENDAR_VIEWS, EVENT_COLORS } from '../../../lib/calendar/calendarConstants';
import { Orgs } from '../../../lib/collections/orgs';
import { getPeopleData } from '../../services/peopleMeteorService';
import { formatGroups, getShowSections, matchesQuery } from '../../../lib/calendar/calendarUtils';
import { Curriculum } from '../../../lib/collections/curriculum';
import _ from '../../../lib/util/underscore';
import { CurriculumTheme } from '../../../lib/collections/curriculumThemes';
import { Announcements } from '../../../lib/collections/announcements';
import { Foods } from '../../../lib/collections/food';
import { Reservations } from '../../../lib/collections/reservations';
import { Person } from '../../../lib/collections/people';
import { Groups } from '../../../lib/collections/groups';
import { USER_TYPES } from '../../../lib/constants/profileConstants';
import { AvailableCustomizations } from '../../../lib/customizations';
import { HolidayUtils } from '../../../lib/util/holidayUtils';
import { Log } from '../../../lib/util/log';
import { HOLIDAY_SCHEDULE_TYPES } from '../../../lib/constants/holidayConstants';


export class CalendarService {
    constructor({ timezone, showSections, filterOptions, currentPerson, data = {} }) {
        this.timezone = timezone;
        this.currentOrg = Orgs.current();
        this.showSections = showSections;
        this.filterOptions = filterOptions;
        this.currentPerson = currentPerson;
        this.data = data;

        this.rangeStart = new ReactiveVar(this._getRangeStart());
        this.rangeEnd = new ReactiveVar(this._getRangeEnd());
        this.viewStyle = new ReactiveVar(this._getViewStyle());
        this.selectedGroup = new ReactiveVar(this._getSelectedGroup());
        this.reservationsSort = new ReactiveVar(
            filterOptions.reservationsSort === "" ? "" : filterOptions.reservationsSort || "schedule-type"
        );

        this.curriculumData = new ReactiveVar([]);
        this.curriculumThemesData = new ReactiveVar([]);
        this.showContent = new ReactiveVar(false);
        this.allReservationsPeople = new ReactiveVar([]);
        this.allReservationsPeopleLoader = new ReactiveVar(true);
    }

    /**
     * Gets the initial range start value from query parameters or current time.
     *
     * @private
     * @returns {number} Range start timestamp in milliseconds
     */
    _getRangeStart() {
        return parseInt(FlowRouter.getQueryParam("rangeStart")) || moment.tz(this.timezone).startOf("day").valueOf();
    }

    /**
     * Gets the initial range end value from query parameters or current time.
     *
     * @private
     * @returns {number} Range end timestamp in milliseconds
     */
    _getRangeEnd() {
        return parseInt(FlowRouter.getQueryParam("rangeEnd")) || moment.tz(this.timezone).endOf("day").valueOf();
    }


    /**
     * Gets the initial view style from filter options or query parameters.
     *
     * @private
     * @returns {string} View style ('day', 'week', 'month', etc.)
     */
    _getViewStyle() {
        if (this.showSections?.includes("reservations")) {
            return this.filterOptions.reservationsViewStyle || "week";
        }
        return FlowRouter.getQueryParam("viewStyle") ?? "day";
    }

    /**
     * Gets the initial selected group based on user type and permissions.
     *
     * @private
     * @returns {string} Selected group ID
     */
    _getSelectedGroup() {
        const person = this.currentPerson;

        if (!person || !["staff", "admin"].includes(person.type) || FlowRouter.getQueryParam("view") === "print") {
            return FlowRouter.getQueryParam("group") || "";
        }
        return person.checkInGroupId || person.defaultGroupId || FlowRouter.getQueryParam("group") || "";
    }

    async loadData() {
        const start = moment.tz(this.rangeStart.get(), this.timezone);
        const end = moment.tz(this.rangeEnd.get(), this.timezone).add(1, "day");

        Meteor.subscribe("theFood", {
            startDate: start.format("MM/DD/YYYY"),
            endDate: end.format("MM/DD/YYYY"),
        });

        Meteor.subscribe("theReservations", {
            startDate: start.format("MM/DD/YYYY"),
            endDate: end.format("MM/DD/YYYY"),
        });

        Meteor.subscribe("theAnnouncements", {
            rangeStart: this.rangeStart.get(),
            rangeEnd: this.rangeEnd.get(),
            expand: true,
        });

        const [curriculum, themes] = await Promise.all([
            Meteor.callAsync("getCurriculumData", {
                startDate: start.format("MM/DD/YYYY"),
                endDate: end.format("MM/DD/YYYY"),
            }),
            Meteor.callAsync("getCurriculumThemesData", {
                startDate: start.format("MM/DD/YYYY"),
                endDate: end.format("MM/DD/YYYY"),
            }),
        ]);

        this.curriculumData.set(curriculum);
        this.curriculumThemesData.set(themes);
    }

    async getPeopleForReservations() {
        this.showContent.set(false);
        this.allReservationsPeopleLoader.set(true);

        try {
            const start = moment.tz(this.rangeStart.get(), this.timezone);
            const end = moment.tz(this.rangeEnd.get(), this.timezone).add(1, "day");

            const allReservations = Reservations.findWithRecurrence({
                startDate: start.format("MM/DD/YYYY"),
                endDate: end.format("MM/DD/YYYY"),
            });

            const reservationPersonIds = allReservations.map((r) => r.selectedPerson);
            const relIds = this.currentPerson?.findInheritedRelationships().map((rel) => rel.targetId) || [];
            const uniqueIds = [...new Set([...reservationPersonIds, ...relIds])];

            const peopleList = await getPeopleData({ _id: { $in: uniqueIds } }, {});
            this.allReservationsPeople.set(peopleList);
        } catch (error) {
            Log.info('Failed to load people for reservations:', error.reason || error.message);
            throw error;
        } finally {
            // Ensure these always reset, even on error
            this.allReservationsPeopleLoader.set(false);
            this.showContent.set(true);
        }
    }

    initDatepicker(elementId) {
        const initializePicker = () => {
            const element = $(`#${elementId}`);
            if (element.length === 0) {
                // Element doesn't exist yet, try again
                setTimeout(initializePicker, 100);
                return;
            }

            // Destroy existing datepicker if it exists
            if (element.data('datepicker')) {
                element.datepicker('destroy');
            }

            // Initialize new datepicker
            element.datepicker({
                zIndexOffset: 9999
            }).on("changeDate", (event) => {
                const newDate = moment.tz(event.date, this.timezone);
                this.rangeStart.set(newDate.startOf("day").valueOf());
                this.recalculateRange(0);
            });
        };

        setTimeout(initializePicker, 200);
    }

    recalculateRange(addMultiplier) {
        const timezone = this.timezone
        const viewStyle = this.viewStyle.get();
        const rangeStart = this.rangeStart.get();
        let newStart;
        let newEnd;

        switch (viewStyle) {
            case CALENDAR_VIEWS.DAY:
                newStart = new moment.tz(rangeStart, timezone).add(addMultiplier, "day");
                newEnd = newStart.clone().endOf('day');
                break;
            case CALENDAR_VIEWS.THREE_DAY:
                newStart = new moment.tz(rangeStart, timezone).add(addMultiplier * 3, "days");
                newEnd = newStart.clone().add(2, "days").endOf('day');
                break;
            case CALENDAR_VIEWS.WEEK:
                newStart = new moment.tz(rangeStart, timezone).add(addMultiplier * 7, "days").startOf("week").add(1, "days");
                newEnd = newStart.clone().endOf("week").subtract(1, 'days');
                break;
            case CALENDAR_VIEWS.MONTH:
                newStart = new moment.tz(rangeStart, timezone).add(addMultiplier, "month").startOf("month");
                newEnd = newStart.clone().endOf("month");
                break;
        }

        this.rangeStart.set(newStart.valueOf());
        this.rangeEnd.set(newEnd.valueOf());
    }

    async updateFilterOptionsState() {
        const newState = {
            reservationsViewStyle: this.viewStyle.get(),
            reservationsSort: this.reservationsSort.get(),
            selectedGroup: this.selectedGroup.get(),
        };
        await Meteor.callAsync("setUiOption", "calendarFilters", newState);
    }

    shouldShow(string) {
        const showSections = getShowSections(this.data);
        return showSections?.includes(string);
    }

    getEvents(rangeStart, rangeEnd) {
        const allEvents = [];

        if (!this.currentPerson) {
            return [];
        }

        const reservationsSort = this.reservationsSort.get();
        const showReservationsByGroup = ['group', 'schedule-type'].includes(reservationsSort);

        if (this.shouldShow(CALENDAR_EVENTS.ACTIVITIES)) {
            allEvents.push(...this.getCurriculumEvents(rangeStart, rangeEnd));
            allEvents.push(...this.getCurriculumThemeEvents(rangeStart, rangeEnd));
        }

        if (this.shouldShow(CALENDAR_EVENTS.ANNOUNCEMENTS)) {
            allEvents.push(...this.getAnnouncementEvents());
        }

        if (this.shouldShow(CALENDAR_EVENTS.FOOD)) {
            allEvents.push(...this.getFoodEvents(rangeStart, rangeEnd));
        }

        if (this.shouldShow(CALENDAR_EVENTS.RESERVATIONS) && this.currentOrg.hasCustomization(AvailableCustomizations.RESERVATIONS_ENABLED)) {
            allEvents.push(...this.getReservationEvents(rangeStart, rangeEnd, showReservationsByGroup, reservationsSort));
        }

        return allEvents;
    }

    generateBaseQuery(rangeStart, rangeEnd, currentPerson) {
        const groupIds = [];
        const query = {
            scheduledDate: {
                $gte: rangeStart,
                $lte: rangeEnd
            }
        };

        const selectedGroup = this.selectedGroup.get();

        if (currentPerson?.type === USER_TYPES.FAMILY) {
            const people = this.allReservationsPeople.get();
            currentPerson.findInheritedRelationships().forEach((rel) => {
                const person = people.find((p) => p._id === rel.targetId);
                if (person?.defaultGroupId) {
                    groupIds.push(person.defaultGroupId);
                }
            });
            query["$or"] = [{ selectedGroups: [] }, { selectedGroups: { $in: groupIds } }];
        } else if (currentPerson?.type === USER_TYPES.STAFF) {
            const fallbackGroup = selectedGroup || currentPerson.checkInGroupId;
            if (fallbackGroup) {
                query["$or"] = [{ selectedGroups: [] }, { selectedGroups: { $in: [fallbackGroup] } }];
            } else if (currentPerson.defaultGroupId) {
                query["$or"] = [{ selectedGroups: [] }, { selectedGroups: { $in: [currentPerson.defaultGroupId] } }];
            } else {
                query["selectedGroups"] = [];
            }
        } else {
            if (selectedGroup) {
                query["$or"] = [{ selectedGroups: [] }, { selectedGroups: { $in: [selectedGroup] } }];
            }
        }

        return query;
    }

    getCurriculumEvents(rangeStart, rangeEnd) {
        const events = [];
        const currentPerson = this.currentPerson;
        const query = this.generateBaseQuery(rangeStart, rangeEnd, currentPerson);
        const filteredCurriculum = (this.curriculumData.get() || []).filter(curriculum => matchesQuery(curriculum, query));

        filteredCurriculum.forEach(curriculum => {
            curriculum = new Curriculum(curriculum);
            const curriculumEvent = {
                title: curriculum.headline,
                subheader: (curriculum.selectedTypes || []).length > 0 ? curriculum.selectedTypes[0] : "",
                start: curriculum.scheduledDate,
                color: EVENT_COLORS.ACTIVITIES,
                textColor: EVENT_COLORS.TEXT,
                eventType: CALENDAR_EVENTS.ACTIVITIES,
                eventDetail: curriculum.message,
                eventSubDetail: curriculum.teacherNotes,
                eventHeader: "Activities",
                eventTargets: curriculum.sendToAll ? null : formatGroups(curriculum.findRecipientGroups?.().map(d => d.name) || []),
                eventDestination: CALENDAR_EVENTS.ACTIVITIES,
                eventAttachments: curriculum.mediaFiles,
                eventItemId: curriculum._id,
                hasCurriculumThemeId: curriculum.curriculumThemeId
            };

            if ([USER_TYPES.STAFF, USER_TYPES.ADMIN].includes(currentPerson.type)) {
                if (curriculum.internalNotes) {
                    curriculumEvent.eventInternalNotes = curriculum.internalNotes;
                }

                if (curriculum.internalLink) {
                    curriculumEvent.eventInternalLink = curriculum.internalLink;
                }
            }

            if (!curriculum.curriculumThemeId) {
                events.push(curriculumEvent);
            }
        });

        return events;
    }

    getCurriculumThemeEvents(rangeStart, rangeEnd) {
        const events = [];

        const themesQuery = this.generateBaseQuery(rangeStart, rangeEnd, this.currentPerson);
        delete themesQuery.scheduledDate;
        themesQuery["selectedDays"] = { $elemMatch: { $gte: rangeStart, $lte: rangeEnd } };

        const filteredThemes = (this.curriculumThemesData.get() || []).filter(theme =>
            matchesQuery(theme, themesQuery)
        );

        filteredThemes.forEach(theme => {
            theme = new CurriculumTheme(theme);
            theme.selectedDays
                .filter(day => day >= rangeStart && day < rangeEnd)
                .forEach(themeDay => {
                    const groupLabel = formatGroups((theme.findRecipientGroups() || []).map(d => d.name));

                    // Push the theme header
                    events.push({
                        title: "Theme: " + theme.name,
                        start: themeDay,
                        eventType: CALENDAR_EVENTS.ACTIVITIES,
                        eventSubType: "theme",
                        eventDetail: theme.description,
                        eventHeader: "Activities",
                        eventDestination: CALENDAR_EVENTS.ACTIVITIES,
                        eventTargets: groupLabel,
                        eventItemId: theme._id,
                        isCurriculumTheme: true
                    });

                    // Find and push child curriculum events associated with this theme + day
                    (this.curriculumData.get() || [])
                        .filter(c =>
                            c.curriculumThemeId === theme._id &&
                            c.scheduledDate === themeDay
                        )
                        .forEach(c => {
                            const curriculum = new Curriculum(c);
                            const event = {
                                title: curriculum.headline,
                                subheader: (curriculum.selectedTypes || [])[0] || "",
                                start: curriculum.scheduledDate,
                                color: EVENT_COLORS.ACTIVITIES,
                                textColor: EVENT_COLORS.TEXT,
                                eventType: CALENDAR_EVENTS.ACTIVITIES,
                                eventDetail: curriculum.message,
                                eventSubDetail: curriculum.teacherNotes,
                                eventHeader: "Activities",
                                eventTargets: groupLabel,
                                eventDestination: CALENDAR_EVENTS.ACTIVITIES,
                                eventAttachments: curriculum.mediaFiles,
                                eventItemId: curriculum._id,
                                hasCurriculumThemeId: curriculum.curriculumThemeId
                            };

                            if ([USER_TYPES.STAFF, USER_TYPES.ADMIN].includes(this.currentPerson.type)) {
                                if (curriculum.internalNotes) {
                                    event.eventInternalNotes = curriculum.internalNotes;
                                }

                                if (curriculum.internalLink) {
                                    event.eventInternalLink = curriculum.internalLink;
                                }
                            }

                            events.push(event);
                        });
                });
        });

        return events;
    }
    getAnnouncementEvents() {
        const events = [];
        // Add filter based on group filter
        const selectedGroup = this.selectedGroup.get();
        let query = {};

        if (selectedGroup && selectedGroup.length > 0) {
            query = {
                $or: [
                    { "selectedGroups": selectedGroup },
                    { "selectedGroups": { $size: 0 } }
                ]
            };
        }

        Announcements.find(query).forEach(announcement => {
            events.push({
                title: announcement.headline,
                start: announcement.scheduledDate,
                end: announcement.scheduledEndDate,
                color: EVENT_COLORS.ANNOUNCEMENTS,
                textColor: EVENT_COLORS.TEXT,
                eventType: CALENDAR_EVENTS.ANNOUNCEMENTS,
                eventDetail: announcement.message,
                eventHeader: "Announcement",
                eventTargets: announcement.sendToAll() ? null : formatGroups((announcement.findRecipientGroups() || []).map(function (d) {
                    return d.name;
                })),
                eventDestination: CALENDAR_EVENTS.ANNOUNCEMENTS,
                eventAttachments: announcement.mediaFiles,
                eventItemId: announcement._id
            });
        });

        return events;
    }

    getFoodEvents(rangeStart, rangeEnd) {
        const events = [];
        let foodQuery = this.generateBaseQuery(rangeStart, rangeEnd, this.currentPerson);
        delete foodQuery['scheduledDate'];

        const fullFoodQuery = {
            startDate: new moment.tz(rangeStart, this.timezone).format('MM/DD/YYYY'),
            endDate: new moment.tz(rangeEnd, this.timezone).add(1, "days").format('MM/DD/YYYY'),
            query: foodQuery
        };
        Foods
            .findWithRecurrence(fullFoodQuery)
            .forEach(
                function (food) {
                    events.push(
                        {
                            title: `${food.meal}: ${food.processedDescription()}`,
                            start: food.scheduledDate,
                            color: '#E0C7EC',
                            textColor: '#333',
                            eventType: 'food',
                            eventDetail: food.processedDescription(),
                            eventHeader: 'Food',
                            eventTargets: food.sendToAll()
                                ? null
                                : formatGroups(
                                    food
                                        .findRecipientGroups()
                                        .map(
                                            function (d) {
                                                return d.name;
                                            }
                                        )
                                ),
                            eventDestination: 'food',
                            eventItemId: food.originalId || food._id
                        }
                    );
                }
            );

        return events;
    }

    /**
     * Generates calendar reservation events for the given date range.
     *
     * This method retrieves reservations (including recurring ones), associates them with people and groups,
     * computes stats like check-in counts and group-level capacity, and formats everything into calendar event objects.
     *
     * The output includes:
     * - Individual reservationSlot events per person
     * - Summary headers for group reservation stats
     * - Optional nested group/schedule-type headers depending on `reservationsSort`
     *
     * @param {number} rangeStart - Start of the calendar range (inclusive), in milliseconds since epoch.
     * @param {number} rangeEnd - End of the calendar range (exclusive), in milliseconds since epoch.
     * @param {boolean} showReservationsByGroup - Whether to group and summarize reservations by default group.
     * @param {string} reservationsSort - Sort behavior: "", "group", or "schedule-type".
     * @returns {Array<Object>} An array of event objects formatted for the calendar UI.
     *
     * Event Object Structure (examples):
     * {
     *   title: "John Smith",
     *   allDay: true,
     *   start: 1749013200000,
     *   color: "#F4CDE2",
     *   textColor: "#333",
     *   eventType: "reservationSlot",
     *   eventHeader: "Scheduling",
     *   eventTargets: "Group A",
     *   eventDestination: "scheduling",
     *   eventDataId: "child123",
     *   eventItemId: "res456",
     *   checkInOrder: 3,
     *   originalReservation: { ... }
     * }
     *
     * Group header example:
     * {
     *   title: "Group A",
     *   allDay: true,
     *   start: 1749013200000,
     *   eventType: "reservationCapacityByGroup",
     *   eventHeader: "Scheduling",
     *   eventStats: [
     *     { name: "Coming", value: 10 },
     *     { name: "Capacity", value: 20 },
     *     { name: "Ratio", value: "1:10" }
     *   ]
     * }
     */
    getReservationEvents(rangeStart, rangeEnd, showReservationsByGroup, reservationsSort) {
        // Get holidays in the date range
        const holidayMap = HolidayUtils.getHolidaysInRange(rangeStart, rangeEnd, this.currentOrg);

        const reservations = this.fetchReservations(rangeStart, rangeEnd);

        const { reservationEvents, groupReservations, todayStats } = this.processReservationsWithHolidays(
            reservations,
            rangeStart,
            rangeEnd,
            showReservationsByGroup,
            holidayMap
        );

        const calendarEvents = this.buildCalendarEvents(
            reservationEvents,
            groupReservations,
            todayStats,
            rangeStart,
            rangeEnd,
            reservationsSort
        );

        // Add holiday event items
        const holidayEvents = this.createHolidayEventItems(holidayMap);

        return [...holidayEvents, ...calendarEvents];
    }


    /**
     * Fetches reservations for the given date range using the base query.
     *
     * @param {number} rangeStart - Start of date range in milliseconds
     * @param {number} rangeEnd - End of date range in milliseconds
     * @returns {Array<Object>} Array of reservation objects
     *
     * @example
     * const reservations = service.fetchReservations(startTime, endTime);
     */
    fetchReservations(rangeStart, rangeEnd) {
        const reservationsQuery = this.generateBaseQuery(rangeStart, rangeEnd, this.currentPerson);
        delete reservationsQuery["$or"];
        delete reservationsQuery['scheduledDate'];
        reservationsQuery.reservationType = USER_TYPES.PERSON;
        reservationsQuery.cancellationReason = null;

        return Reservations.findWithRecurrence({
            startDate: new moment.tz(rangeStart, this.timezone).format('MM/DD/YYYY'),
            endDate: new moment.tz(rangeEnd, this.timezone).add(1, "days").format('MM/DD/YYYY'),
            query: reservationsQuery
        });
    }

    /**
     * Processes reservations and builds reservation events and group data with holiday support.
     *
     * @param {Array<Object>} reservations - Array of reservation objects
     * @param {number} rangeStart - Start of date range in milliseconds
     * @param {number} rangeEnd - End of date range in milliseconds
     * @param {boolean} showReservationsByGroup - Whether to group reservations
     * @param {Object} holidayMap - Map of dates to holiday data
     * @returns {Object} Object containing reservationEvents, groupReservations, and todayStats
     *
     * @example
     * const { reservationEvents, groupReservations, todayStats } =
     *   service.processReservationsWithHolidays(reservations, start, end, true, holidays);
     */
    processReservationsWithHolidays(reservations, rangeStart, rangeEnd, showReservationsByGroup, holidayMap) {
        const reservationEvents = [];
        const groupReservations = {};
        let reservationsTodayCount = 0;
        let checkInsTodayCount = 0;

        for (const reservation of reservations) {
            const reservationPerson = this.getReservationPerson(reservation);
            if (!reservationPerson) {
                continue;
            }

            const reservationGroup = this.getReservationGroup(reservation, reservationPerson);

            // Check if reservation date has a holiday
            const reservationDateKey = moment.tz(reservation.scheduledDate, this.timezone).format('YYYY-MM-DD');
            const holidayOnDate = holidayMap[reservationDateKey];

            // Check if this reservation's schedule type is permitted on the holiday
            let isPermittedOnHoliday = true;
            if (holidayOnDate) {
                isPermittedOnHoliday = HolidayUtils.isScheduleTypePermitted(reservation.scheduleType, holidayOnDate);
            }

            const { checkInOrderString, checkInOrder, useGroupReservations } = this.processReservationCheckinWithHolidays(
                reservation,
                reservationPerson,
                groupReservations,
                showReservationsByGroup,
                reservationGroup,
                isPermittedOnHoliday
            );

            const isToday = this.isReservationToday(reservation);
            if (isToday) {
                // Only count if permitted on holiday (or no holiday)
                if (isPermittedOnHoliday) {
                    reservationsTodayCount++;
                    if (checkInOrderString !== "") {
                        checkInsTodayCount++;
                    }
                }
            }

            const reservationEvent = this.createReservationEvent(
                reservation,
                reservationPerson,
                checkInOrderString,
                checkInOrder,
                reservationGroup
            );

            reservationEvents.push(reservationEvent);

            if (useGroupReservations) {
                groupReservations[reservation.scheduledDate][reservationGroup._id].reservationEvents.push(reservationEvent);
            }
        }

        this.sortReservationEvents(reservationEvents);

        return {
            reservationEvents,
            groupReservations,
            todayStats: { reservationsTodayCount, checkInsTodayCount }
        };
    }

    /**
     * Gets the person associated with a reservation from the loaded people data.
     *
     * @param {Object} reservation - The reservation object
     * @returns {Person|null} Person instance or null if not found
     *
     * @example
     * const person = service.getReservationPerson(reservation);
     */
    getReservationPerson(reservation) {
        const person = this.allReservationsPeople.get().find(p => p._id === reservation.selectedPerson);
        return person ? new Person(person) : null;
    }

    /**
     * Processes check-in information for a reservation with holiday awareness.
     *
     * @param {Object} reservation - The reservation object
     * @param {Person} reservationPerson - The person making the reservation
     * @param {Object} groupReservations - Group reservations tracking object
     * @param {boolean} showReservationsByGroup - Whether grouping is enabled
     * @param {Object} defaultGroup - The default group for this reservation
     * @param {boolean} isPermittedOnHoliday - Whether this reservation type is permitted on holiday
     * @returns {Object} Object with checkInOrderString, checkInOrder, and useGroupReservations
     *
     * @example
     * const { checkInOrderString, checkInOrder, useGroupReservations } =
     *   service.processReservationCheckinWithHolidays(reservation, person, groups, true, group, true);
     */
    processReservationCheckinWithHolidays(reservation, reservationPerson, groupReservations, showReservationsByGroup, defaultGroup, isPermittedOnHoliday) {
        let checkInOrderString = "";
        let checkInOrder;
        let useGroupReservations = false;

        if (showReservationsByGroup || this.currentOrg.hasCustomization(AvailableCustomizations.MOMENTS_CHECKIN_ORDER)) {
            const reservationIsToday = this.isReservationToday(reservation);

            if (defaultGroup) {
                this.initializeGroupReservationWithHolidays(groupReservations, reservation, defaultGroup);

                // Always increment total reservations count
                groupReservations[reservation.scheduledDate][defaultGroup._id].totalReservations++;

                // Only increment "coming" count if permitted on holiday
                if (isPermittedOnHoliday) {
                    groupReservations[reservation.scheduledDate][defaultGroup._id].reservations++;
                }

                // Track schedule type counts
                const scheduleTypeId = reservation.scheduleType || '';
                const groupData = groupReservations[reservation.scheduledDate][defaultGroup._id];

                if (!groupData.scheduleTypeCounts[scheduleTypeId]) {
                    groupData.scheduleTypeCounts[scheduleTypeId] = {
                        total: 0,
                        permitted: 0,
                        isPermittedOnHoliday: isPermittedOnHoliday
                    };
                }

                groupData.scheduleTypeCounts[scheduleTypeId].total++;
                if (isPermittedOnHoliday) {
                    groupData.scheduleTypeCounts[scheduleTypeId].permitted++;
                }

                useGroupReservations = true;
            }

            if (this.isPersonCheckedInToday(reservationPerson, reservationIsToday)) {
                if (reservationPerson.checkInOrder) {
                    checkInOrderString = " #" + reservationPerson.checkInOrder;
                    checkInOrder = reservationPerson.checkInOrder;
                }

                if (defaultGroup && isPermittedOnHoliday) {
                    groupReservations[reservation.scheduledDate][defaultGroup._id].checkins++;
                }
            }
        }

        return { checkInOrderString, checkInOrder, useGroupReservations };
    }

    /**
     * Checks if a reservation is for today's date.
     *
     * @param {Object} reservation - The reservation object with scheduledDate
     * @returns {boolean} True if the reservation is for today
     *
     * @example
     * if (service.isReservationToday(reservation)) {
     *   // Handle today's reservation
     * }
     */
    isReservationToday(reservation) {
        return moment(reservation.scheduledDate).tz(this.timezone).isSame(moment().tz(this.timezone), 'day');
    }

    /**
     * Gets the group associated with a reservation, either from reservation.groupId or person's default.
     *
     * @param {Object} reservation - The reservation object
     * @param {Person} reservationPerson - The person making the reservation
     * @returns {Object|null} Group object or null if not found
     *
     * @example
     * const group = service.getReservationGroup(reservation, person);
     */
    getReservationGroup(reservation, reservationPerson) {
        return reservation.groupId ? Groups.findOne(reservation.groupId) : reservationPerson.findDefaultGroup();
    }

    /**
     * Initializes group reservation data structure if it doesn't exist, with holiday tracking.
     *
     * @param {Object} groupReservations - Group reservations tracking object
     * @param {Object} reservation - The reservation object
     * @param {Object} defaultGroup - The default group
     *
     * @example
     * service.initializeGroupReservationWithHolidays(groupReservations, reservation, group);
     */
    initializeGroupReservationWithHolidays(groupReservations, reservation, defaultGroup) {
        if (!groupReservations[reservation.scheduledDate]) {
            groupReservations[reservation.scheduledDate] = {};
        }

        if (!groupReservations[reservation.scheduledDate][defaultGroup._id]) {
            groupReservations[reservation.scheduledDate][defaultGroup._id] = {
                start: reservation.scheduledDate,
                name: defaultGroup.name,
                reservations: 0, // Coming count (holiday-filtered)
                totalReservations: 0, // Total count (unfiltered)
                checkins: 0,
                capacity: defaultGroup.capacity,
                ratio: defaultGroup.ratio,
                reservationEvents: [],
                sortOrder: defaultGroup.sortOrder,
                scheduleTypeCounts: {}
            };
        }
    }

    /**
     * Checks if a person is checked in today based on check-in time and reservation date.
     *
     * @param {Person} reservationPerson - The person to check
     * @param {boolean} reservationIsToday - Whether the reservation is for today
     * @returns {boolean} True if the person is checked in today
     *
     * @example
     * if (service.isPersonCheckedInToday(person, true)) {
     *   // Person is checked in for today's reservation
     * }
     */
    isPersonCheckedInToday(reservationPerson, reservationIsToday) {
        return reservationPerson.checkedIn &&
            reservationPerson.checkedInOutTime >= new Date().setHours(0, 0, 0, 0) &&
            reservationIsToday;
    }

    /**
     * Creates a reservation event object for the calendar display.
     *
     * @param {Object} reservation - The reservation object
     * @param {Person} reservationPerson - The person making the reservation
     * @param {string} checkInOrderString - String representation of check-in order
     * @param {number} checkInOrder - Numeric check-in order
     * @param {Object} reservationGroup - The group for this reservation
     * @returns {Object} Calendar event object
     *
     * @example
     * const event = service.createReservationEvent(reservation, person, "#1", 1, group);
     */
    createReservationEvent(reservation, reservationPerson, checkInOrderString, checkInOrder, reservationGroup) {

        const event = {
            title: reservationPerson.firstName + " " + reservationPerson.lastName + checkInOrderString,
            allDay: true,
            start: reservation.scheduledDate,
            color: (checkInOrderString === "") ? EVENT_COLORS.RESERVATION : EVENT_COLORS.RESERVATION_CHECKED_IN,
            textColor: EVENT_COLORS.RESERVATION_TEXT,
            eventType: "reservationSlot",
            eventHeader: "Scheduling",
            eventTargets: reservationGroup && reservationGroup.name,
            eventDestination: "scheduling",
            eventDataId: reservation.selectedPerson,
            eventItemId: reservation.originalId || reservation._id,
            checkInOrder: checkInOrder,
            originalReservation: reservation
        };

        const orgScheduleTypes = this.currentOrg.getScheduleTypes();
        const matchedScheduleType = orgScheduleTypes.find(mst => mst._id === reservation.scheduleType);
        event.eventTags = matchedScheduleType && [matchedScheduleType.type];

        return event;
    }

    /**
     * Sorts reservation events by check-in order first, then alphabetically by name.
     *
     * @param {Array<Object>} reservationEvents - Array of reservation event objects to sort
     *
     * @example
     * service.sortReservationEvents(events);
     */
    sortReservationEvents(reservationEvents) {
        reservationEvents.sort((a, b) => {
            if (a.checkInOrder && b.checkInOrder) {
                return a.checkInOrder - b.checkInOrder;
            } else if (a.checkInOrder && !b.checkInOrder) {
                return -1;
            } else if (!a.checkInOrder && b.checkInOrder) {
                return 1;
            } else {
                return a.title.localeCompare(b.title, undefined, { sensitivity: 'base' });
            }
        });
    }

    /**
     * Builds the complete calendar events array from processed reservation data.
     *
     * @param {Array<Object>} reservationEvents - Individual reservation events
     * @param {Object} groupReservations - Grouped reservation data
     * @param {Object} todayStats - Statistics for today's reservations
     * @param {number} rangeStart - Start of date range
     * @param {number} rangeEnd - End of date range
     * @param {string} reservationsSort - Sort type for reservations
     * @returns {Array<Object>} Complete array of calendar events
     *
     * @example
     * const events = service.buildCalendarEvents(reservationEvents, groups, stats, start, end, 'group');
     */
    buildCalendarEvents(reservationEvents, groupReservations, todayStats, rangeStart, rangeEnd, reservationsSort) {
        let events = [];

        // Add today's check-in summary if today is in range
        const todayEvent = this.createTodayCheckInEvent(todayStats, rangeStart, rangeEnd);
        if (todayEvent) {
            events.push(todayEvent);
        }

        // Add group reservation events
        const groupEvents = this.createGroupReservationEvents(groupReservations, reservationsSort);
        events = events.concat(groupEvents);

        // Add individual reservation events if not grouped
        if (reservationsSort === "") {
            events = events.concat(reservationEvents);
        }

        return events;
    }

    /**
     * Creates today's check-in summary event if today falls within the date range.
     *
     * @param {Object} todayStats - Object with reservationsTodayCount and checkInsTodayCount
     * @param {number} rangeStart - Start of date range
     * @param {number} rangeEnd - End of date range
     * @returns {Object|null} Today's summary event or null if not applicable
     *
     * @example
     * const todayEvent = service.createTodayCheckInEvent(stats, start, end);
     */
    createTodayCheckInEvent(todayStats, rangeStart, rangeEnd) {
        const todayKey = moment.tz(this.timezone).startOf('day').valueOf();

        if (todayKey >= rangeStart && todayKey < rangeEnd) {
            return {
                title: "Check-ins: " + todayStats.checkInsTodayCount + "/" + todayStats.reservationsTodayCount,
                allDay: true,
                start: new Date().setHours(0, 0, 0, 0),
                color: EVENT_COLORS.CAPACITY,
                textColor: EVENT_COLORS.TEXT,
                eventType: "reservationCapacity",
                eventDetail: "",
                eventHeader: "Scheduling",
                eventTargets: [],
                eventDestination: "scheduling"
            };
        }

        return null;
    }

    /**
     * Creates group reservation events with optional sorting and nesting.
     *
     * @param {Object} groupReservations - Grouped reservation data
     * @param {string} reservationsSort - Sort type ('', 'group', 'schedule-type')
     * @returns {Array<Object>} Array of group and nested reservation events
     *
     * @example
     * const groupEvents = service.createGroupReservationEvents(groups, 'schedule-type');
     */
    createGroupReservationEvents(groupReservations, reservationsSort) {
        const events = [];
        const selectedGroup = this.selectedGroup.get();
        const todayKey = moment.tz(this.timezone).startOf('day').valueOf();

        _.each(groupReservations, (reservationDayValue, reservationDayKey) => {
            const sortedGroups = this.sortGroupsByOrder(reservationDayValue);

            _.each(sortedGroups, (groupData) => {
                if (selectedGroup && groupData.key !== selectedGroup) return;

                const groupHeaderEvent = this.createGroupHeaderEvent(groupData, reservationDayKey, todayKey);
                events.push(groupHeaderEvent);

                // Add nested events based on sort type
                if (reservationsSort === "group") {
                    const nestedEvents = this.createGroupSortedEvents(groupData);
                    events.push(...nestedEvents);
                } else if (reservationsSort === "schedule-type") {
                    const scheduleTypeEvents = this.createScheduleTypeSortedEvents(groupData, reservationDayKey);
                    events.push(...scheduleTypeEvents);
                }
            });
        });

        return events;
    }

    /**
     * Sorts groups by their sort order and name.
     *
     * @param {Object} reservationDayValue - Object mapping group IDs to group data
     * @returns {Array<Object>} Sorted array of group data with keys
     *
     * @example
     * const sortedGroups = service.sortGroupsByOrder(dayGroups);
     */
    sortGroupsByOrder(reservationDayValue) {
        return _.chain(reservationDayValue)
            .map((rdv, key) => {
                rdv.key = key;
                return rdv;
            })
            .sortBy(rdv => rdv.sortOrder + "-" + rdv.name)
            .value();
    }

    /**
     * Creates a group header event with statistics and capacity information.
     *
     * @param {Object} groupData - Group data including reservations, checkins, capacity
     * @param {string} reservationDayKey - Date key for this day's reservations
     * @param {string} todayKey - Today's date key for comparison
     * @returns {Object} Group header calendar event
     *
     * @example
     * const header = service.createGroupHeaderEvent(groupData, dayKey, todayKey);
     */
    createGroupHeaderEvent(groupData, reservationDayKey, todayKey) {
        const eventStats = [];

        if (reservationDayKey === todayKey) {
            eventStats.push({ name: "Here", value: groupData.checkins });
        }

        eventStats.push(
            { name: "Coming", value: groupData.reservations },
            { name: "Capacity", value: groupData.capacity }
        );

        if (groupData.ratio) {
            eventStats.push(
                { name: "Ratio", value: "1:" + groupData.ratio },
                { name: "Staff Req", value: Math.ceil(groupData.reservations / groupData.ratio) }
            );
        }

        const primaryCollapsibleKey = "scheduling-" + reservationDayKey + "-" + groupData.name;

        return {
            title: groupData.name,
            allDay: true,
            start: parseInt(reservationDayKey),
            color: EVENT_COLORS.CAPACITY,
            textColor: EVENT_COLORS.TEXT,
            eventType: "reservationCapacityByGroup",
            eventDetail: "",
            eventHeader: "Scheduling",
            eventTargets: [groupData.name],
            eventDestination: "scheduling",
            additionalStyle: "main-header-bg",
            collapsibleDestinationKey: primaryCollapsibleKey,
            eventStats
        };
    }

    /**
     * Creates events sorted by group with nested styling.
     *
     * @param {Object} groupData - Group data containing reservation events
     * @returns {Array<Object>} Array of group-sorted events with nested styling
     *
     * @example
     * const groupSortedEvents = service.createGroupSortedEvents(groupData);
     */
    createGroupSortedEvents(groupData) {
        return _.chain(groupData.reservationEvents)
            .map(r => {
                r.additionalStyle = "nested";
                return r;
            })
            .sortBy(r => r.title)
            .value();
    }

    /**
     * Creates events sorted by schedule type with holiday awareness.
     *
     * @param groupData
     * @param reservationDayKey
     * @return {*[]}
     */
    createScheduleTypeSortedEvents(groupData, reservationDayKey) {
        const events = [];
        const scheduleTypes = [...this.currentOrg.getScheduleTypes(), { _id: "", type: "Other" }];
        const primaryCollapsibleKey = "scheduling-" + reservationDayKey + "-" + groupData.name;

        _.chain(scheduleTypes)
            .sortBy(st => st.sortStart)
            .each(scheduleType => {
                const secondaryCollapsibleKey = "schedule-type-" + groupData.name + "-" + scheduleType.type + "-" + reservationDayKey;

                // Get the count for this schedule type, accounting for holidays
                let eventCount = 0;
                if (groupData.scheduleTypeCounts && groupData.scheduleTypeCounts[scheduleType._id]) {
                    const scheduleTypeData = groupData.scheduleTypeCounts[scheduleType._id];
                    // Use permitted count (which accounts for holidays)
                    eventCount = scheduleTypeData.permitted;
                }

                // Get matched events for display (but count might be different due to holidays)
                const matchedEvents = this.getMatchedScheduleTypeEvents(groupData.reservationEvents, scheduleType, secondaryCollapsibleKey);

                // Show the schedule type if it has events OR if it's a defined schedule type
                if (scheduleType._id !== "" || eventCount > 0 || matchedEvents.length > 0) {
                    const scheduleTypeHeader = this.createScheduleTypeHeader(
                        scheduleType,
                        reservationDayKey,
                        primaryCollapsibleKey,
                        secondaryCollapsibleKey,
                        eventCount, // Use the holiday-adjusted count
                        groupData.capacity
                    );

                    events.push(scheduleTypeHeader);

                    // Only add individual reservation events if there are permitted attendees
                    // Note: We still show the events even if count is 0 so users can see what's restricted
                    if (matchedEvents.length > 0) {
                        // Filter events to only show those that are actually permitted
                        const permittedEvents = matchedEvents.filter(event => {
                            const reservation = event.originalReservation;
                            if (!reservation) return true; // Safety check

                            // Check if this specific reservation is permitted
                            const reservationDateKey = moment.tz(reservation.scheduledDate, this.timezone).format('YYYY-MM-DD');
                            const holidayOnDate = HolidayUtils.getHolidaysInRange(reservation.scheduledDate, reservation.scheduledDate, this.currentOrg)[reservationDateKey];

                            if (!holidayOnDate) return true; // No holiday, so permitted

                            return HolidayUtils.isScheduleTypePermitted(reservation.scheduleType, holidayOnDate);
                        });

                        events.push(...JSON.parse(JSON.stringify(permittedEvents)));
                    }
                }
            });

        return events;
    }

    /**
     * Gets events that match a specific schedule type, with holiday filtering.
     *
     * @param {Array<Object>} reservationEvents - Array of reservation events
     * @param {Object} scheduleType - Schedule type object with _id
     * @param {string} secondaryCollapsibleKey - Key for collapsible functionality
     * @returns {Array<Object>} Filtered and styled events for this schedule type
     *
     * @example
     * const matchedEvents = service.getMatchedScheduleTypeEvents(events, scheduleType, key);
     */
    getMatchedScheduleTypeEvents(reservationEvents, scheduleType, secondaryCollapsibleKey) {
        return _.chain(reservationEvents)
            .filter(r => {
                return r.originalReservation?.scheduleType === scheduleType._id ||
                    (scheduleType._id === "" && !r.originalReservation.scheduleType);
            })
            .map(r => {
                r.additionalStyle = "nested-double";
                r.collapsibleSourceKey = secondaryCollapsibleKey;
                delete r.eventTargets;
                return r;
            })
            .sortBy(r => r.title)
            .value();
    }

    /**
     * Creates a schedule type header event with timing and capacity information.
     *
     * @param {Object} scheduleType - Schedule type object
     * @param {string} reservationDayKey - Date key for this day
     * @param {string} primaryCollapsibleKey - Primary collapsible key
     * @param {string} secondaryCollapsibleKey - Secondary collapsible key
     * @param {number} eventCount - Number of events for this schedule type
     * @param {number} capacity - Group capacity
     * @returns {Object} Schedule type header event
     *
     * @example
     * const header = service.createScheduleTypeHeader(scheduleType, dayKey, primary, secondary, count, capacity);
     */
    createScheduleTypeHeader(scheduleType, reservationDayKey, primaryCollapsibleKey, secondaryCollapsibleKey, eventCount, capacity) {
        return {
            title: scheduleType.type,
            allDay: true,
            start: parseInt(reservationDayKey),
            color: EVENT_COLORS.CAPACITY,
            textColor: EVENT_COLORS.TEXT,
            eventType: "reservationCapacityByScheduleType",
            eventDetail: "",
            eventHeader: "Scheduling",
            eventTargets: scheduleType.startTime && (scheduleType.startTime + " - " + scheduleType.endTime),
            eventDestination: "scheduling",
            additionalStyle: "nested sub-header-bg",
            collapsibleSourceKey: primaryCollapsibleKey,
            collapsibleDestinationKey: secondaryCollapsibleKey,
            eventStats: [
                { name: "Coming", value: eventCount },
                { name: "Capacity", value: capacity }
            ]
        };
    }

    /**
     * Creates holiday event items for the calendar display.
     *
     * @param {Object} holidayMap - Map of date strings to holiday objects
     * @returns {Array<Object>} Array of holiday calendar events
     *
     * @example
     * const holidayEvents = service.createHolidayEventItems(holidayMap);
     */
    createHolidayEventItems(holidayMap) {
        const events = [];

        Object.entries(holidayMap).forEach(([dateKey, holiday]) => {
            const holidayDate = moment.tz(dateKey, this.timezone).valueOf();
            const permittedTypes = HolidayUtils.getPermittedScheduleTypes(holiday);

            // Get operating schedule types for display
            let operatingSchedules = [];
            if (permittedTypes === HOLIDAY_SCHEDULE_TYPES.ALL) {
                operatingSchedules = this.currentOrg.getScheduleTypes().map(st => st.type);
            } else if (permittedTypes === HOLIDAY_SCHEDULE_TYPES.NONE) {
                operatingSchedules = [HOLIDAY_SCHEDULE_TYPES.NONE];
            } else {
                const orgScheduleTypes = this.currentOrg.getScheduleTypes();
                operatingSchedules = permittedTypes
                    .map(id => {
                        const scheduleType = orgScheduleTypes.find(st => st._id === id);
                        return scheduleType ? scheduleType.type : null;
                    })
                    .filter(Boolean);
            }

            // Format operating schedules text
            let operatingText = HOLIDAY_SCHEDULE_TYPES.NONE;
            if (operatingSchedules.length > 0) {
                operatingText = operatingSchedules.map(schedule => `• ${schedule}`).join('\n');
            }

            events.push({
                title: `Holiday: ${holiday.name}`,
                allDay: true,
                start: holidayDate,
                color: EVENT_COLORS.HOLIDAYS || '#D7BDE4',
                textColor: EVENT_COLORS.HOLIDAY_TEXT || '#333',
                eventType: 'holiday',
                eventDetail: `Operating Schedule Types:\n${operatingText}`,
                eventHeader: 'Holiday',
                eventDestination: 'scheduling',
                eventItemId: `holiday-${holiday._id || holiday.name}-${dateKey}`,
                isHoliday: true,
                holidayData: holiday,
                operatingSchedules: operatingSchedules
            });
        });

        return events;
    }
}