<template name="_calendarWeekView">
    <div class="list-table">
        <div class="list-table-row">
            <div class="list-table-column week">
                <table data-cy="calendar-week-table" class="calendar-grid-table week">
                    <tr data-cy="calendar-grid-row" class="calendar-grid-row" style="page-break-after: avoid; page-break-before: avoid;">
                        {{#each label in weekviewDayLabels}}
                            <th data-cy="calendar-grid-col" class="calendar-grid-col">{{formatDate label "ddd MMM Do"}}</th>
                        {{/each}}
                    </tr>
                    {{#each weekSection in weekSections}}
                        <tr class="calendar-grid-row" style="page-break-after: avoid; page-break-before: avoid;">
                            <td class="calendar-grid-col section-header" colspan="5">{{weekSection.title.capitalizeFirstLetter}}</td>
                        </tr>
                        <tr class="calendar-grid-row" style="page-break-inside: avoid; page-break-before: auto;">
                            {{#each day in weekSection.days}}
                                <td class="calendar-grid-col">
                                    {{#each item in day.items}}
                                        {{> _calendarEventItem classType='week' item=item showDetail=false showIcon=false}}
                                    {{/each}}
                                </td>
                            {{/each}}
                        </tr>
                    {{/each}}
                </table>
            </div>
        </div>
    </div>
</template>