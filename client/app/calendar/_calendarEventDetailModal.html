<template name="_calendarEventDetailModal">
  <div data-cy="calendar-detail-modal" id="_calendarEventDetailModal" class="modal fade">
    <div class="modal-dialog modal-dialog-scrollable modal-lg" style="width: 100%;">
      <div class="modal-content" style="height:auto;min-height: 100%;">
        {{#with eventData}}
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel">{{headerName}} Detail</h5>
            <div class="d-flex align-items-center">
              <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
                <span data-cy="close-modal" class="fad-regular fad-primary fad fa-times"></span>
              </div>
            </div>
          </div>
          <div class="modal-body">
            <div>
              <h4 data-cy="modal-detail-title">{{title}}</h4>
            </div>
            {{#if detail}}
            <div class="form-group row">
              <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Detail</span>
              <span class="col-lg-6 col-md-9 col-sm-12">{{detail}}</span>
            </div>
            {{/if}}
            {{#if subDetail}}
              <div class="form-group row">
                <span class="col-xl-3 col-lg-3 text-right font-weight-bold"></span>
                <span class="col-lg-6 col-md-9 col-sm-12">{{subDetail}}</span>
              </div>
            {{/if}}
            <div class="form-group row">
              <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Scheduled</span>
              <span class="col-lg-6 col-md-9 col-sm-12">{{formatDate eventItemDate "M/DD/YYYY"}}</span>
            </div>
            {{#if eventTargets}}
              <div class="form-group row">
                <span class="col-xl-3 col-lg-3 text-right font-weight-bold">Target(s)</span>
                <span data-cy="detail-target" class="col-lg-6 col-md-9 col-sm-12">{{eventTargets}}</span>
              </div>
            {{/if}}
            {{#if showAttachments}}
              {{#each attachment in eventAttachments}}
                {{#if startsWith attachment.mediaFileType "image"}}
                  <div class="form-group row">
                    <img src="{{attachment.mediaUrl}}" style="width:100%;object-fit:cover">
                  </div>
                {{else}}
                  <div class="form-group row">
                    <span class="col-xl-3 col-lg-3 text-right font-weight-bold">{{attachment.mediaName}}</span>
                    <span class="col-lg-6 col-md-9 col-sm-12"><a href="#" onclick="window.open('{{attachmentHandler attachment.mediaUrl}}', '_blank');" class="btn btn-primary font-weight-bolder">View</a></span>
                  </div>
                {{/if}}
              {{/each}}
            {{/if}}
          </div>
          <div data-cy="calendar-modal-footer" class="modal-footer">
            {{#if showCheckInButton}}
              <button type="button" class="btn btn-primary font-weight-bolder" id="checkIn" data-target="{{eventDataId}}">Check In</button>
            {{/if}}
            {{#if showCheckOutButton}}
              <button type="button" class="btn btn-primary font-weight-bolder" id="checkOut" data-target="{{eventDataId}}">Check Out</button>
            {{/if}}
            {{#if showEditButton}}
  					  <button type="button" class="btn btn-primary font-weight-bolder" id="edit" data-target="{{eventDataId}}">Edit</button>
  					{{/if}}
            {{#if showCancelButton}}
              <button type="button" class="btn btn-primary font-weight-bolder" id="cancelReservation" data-target="{{eventDataId}}">Cancel</button>
            {{/if}}
            {{#if showGoToButton}}
              <button type="button" data-cy="detail-go-to-btn" class="btn btn-primary font-weight-bolder" id="gotoDetail" data-target="{{destination}}">Go to</button>
            {{/if}}
            <button type="button" data-cy="detail-close" class="btn btn-secondary font-weight-bolder" data-dismiss="modal">Close</button>
          </div>
        {{/with}}
      </div>
    </div>
  </div>
</template>
