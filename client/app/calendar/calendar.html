<template name="calendar">
	{{#if logicalOr showLoaderPeopleList (isReadyToShow)}}
		{{> loading}}
	{{else}}
		<div class="calendar container">
			{{#if isPrintMode}}
				{{#if whiteLabelLogoOverride}}
					<div class="row d-flex justify-content-center">
						<img alt="Logo" src="{{whiteLabelLogoOverride.logo}}" class="max-h-100px" />
					</div>
				{{/if}}
				<div class="d-flex flex-row h1 align-content-center" style="page-break-after: avoid; page-break-before: avoid;">
					{{pageTitle}}
				</div>
			{{/if}}

			{{> _calendarHeader}}

			{{#if logicalOr showLoaderPeopleList (showLoading Template.subscriptionsReady)}}
				{{> loading}}
			{{/if}}

			<div data-cy="calendar-detail-table" class="calendar-detail">
				{{#if (trueIfEq (viewStyle.layoutStyle) "list")}}
					{{> _calendarDayView}}
				{{/if}}
				{{#if trueIfEq viewStyle.layoutStyle "week"}}
					{{> _calendarWeekView}}
				{{/if}}
				{{#if trueIfEq viewStyle.style "month"}}
					{{> _calendarMonthView}}
				{{/if}}
			</div>
		</div>
		{{#if isPrintMode}}
			<script type="text/javascript">
				setTimeout(function () { window.print(); }, 500);
				window.onfocus = function () { setTimeout(function () { window.close(); }, 500); }
			</script>
		{{/if}}
	{{/if}}
</template>
