import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';
import { Relationships } from '../../../lib/collections/relationships';
import { Groups } from '../../../lib/collections/groups.js';
import _ from '../../../lib/util/underscore';
import './messages.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './messageList.js'

Template.messages.helpers({
	canView() {
		return Template.instance().canView.get();
	},
	showAdministrativeVisibility() {
		return (Orgs.current() && Orgs.current().hasCustomization("messages/administrativeVisibility/enabled")
			&& Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type == "admin");
	},
	currentView() {
		const messageId = FlowRouter.getParam("id");
		return messageId ? "message" : "list";
	},
	currentFolder() {
		return Template.instance().messageView.get();
	},
	recipients() {
		return Session.get("recipentPeoplelist");
	},
});

Template.messages.events({
	"click #btnComposeSend": function (e, i) {
		e.preventDefault();
		Meteor.callAsync("insertMessage", {
			recipients: $("#recipients").val(),
			subject: $("input[name='compose_subject']").val(),
			message: $("textarea[name='compose_message']").val()
		})
		.then((result)=>{
			mpSwal.fire("Message sent!");
			$("#kt_inbox_compose").modal('hide');
		})
		.catch((error)=>{
			mpSwal.fire(error.reason);
		});
	},
	"click .navi-link": function (e, i) {
		e.preventDefault();
		const targetFolder = $(e.currentTarget).data("folder");
		Session.set("messageTargetFolder", targetFolder);
		i.messageView.set(targetFolder);
	},
	"click #new_message": function (e, i) {
		$("#recipients").selectize({
			render: {
				option: formatRecipient,
				item: formatTaggedRecipient
			},
			plugins: ['remove_button']
		});
		$("#kt_inbox_compose").on('shown.bs.modal', function () {
			$("#recipients")[0].selectize.clear();
			$("input[name='compose_subject']").val("");
			$("textarea[name='compose_message']").val("");

		});
	}
});

Template.messages.onCreated(function () {
	this.canView = new ReactiveVar(false);

	this.peoplelist = new ReactiveVar([]);

	const currentPerson = Meteor.user().fetchPerson();
	const org = Orgs.current();
	const designations = org?.valueOverrides?.designations ?? [];
	const hideStaff = org && org.hasCustomization('messages/disableStaffMessages/enabled');

	let query = { orgId: org._id, inActive: { $ne: true } };

	const include = ['admin'];

	if (_.contains(["staff", "admin"], currentPerson.type)) {
		if (!hideStaff) {
			include.push('staff');
		}
		include.push('person', 'family');
		query['type'] = { '$in': include };
	} else if (currentPerson.designations && currentPerson.designations.length > 0 && designations.length > 0) {
		query['type'] = { '$in': include };
	} else {
		if (!hideStaff) {
			include.push('staff');
		}
		query['type'] = { '$in': include };
	}

	Meteor.callAsync("getPeopleByQueryAndOptions",
		query,
		{
			"sort": { lastName: 1, firstName: 1 },
			"fields": { _id: 1, type: 1, lastName: 1, firstName: 1 }
		})
		.then((resPeopleList)=>{
			if (resPeopleList) {

				let results = resPeopleList.map((p) => ({ _id: p._id, type: p.type, label: p.lastName + ", " + p.firstName }))

				if (currentPerson.type == "staff" || currentPerson.type == "admin") {
					const familyAndStaffLabel = !hideStaff ? ' families + staff' : ' families';
					Groups.find({}, { sort: { name: -1 } }).fetch().forEach((g) => { results.unshift({ _id: "group:" + g._id, type: "group", label: "All " + g.name + familyAndStaffLabel }) });


					if (currentPerson.type === 'admin') {
						results.unshift(
							{ _id: 'group:missingScheduled', type: 'group', label: 'Scheduled But Not Present' }
						);
					}
					results.unshift(
						{ _id: "group:alladmins", type: "group", label: "All Admins" }
					);
					if (!hideStaff) {
						results.unshift(
							{ _id: "group:allstaff", type: "group", label: "All Staff" }
						);
					}
				} else if (currentPerson.type === 'family') {
					let childIds = Relationships.find({ orgId: currentPerson.orgId, relationshipType: 'family', personId: currentPerson._id }).fetch()
						.map((r) => r.targetId);

					Meteor.callAsync(
						'getPeopleByQueryAndOptions',
						{ _id: { $in: childIds } },
						{})
						.then((childrens)=>{
							for (const child of childrens) {
								results.unshift(
									{
										_id: `group:allteachers:${child._id}`,
										type: "group",
										label: `All of ${child.firstName}'s teachers`
									}
								);
							}

							results.unshift(
								{ _id: "group:alladmins", type: "group", label: "All Admins" }
							);
							Session.set("recipentPeoplelist", results);
						})
						.catch((error)=>{
							console.log("Error while fetching a people infor on messages.js - ",error);
						});

				}

				if (currentPerson.type == "admin" && designations.length > 0) {
					for (const d of designations) {
						let containedString = d.replace(/\s/g, '')
						results.unshift({ _id: `designation:${containedString}`, type: "designation", label: d });
					}
				}
				if (currentPerson.type == "admin") {
					results.splice(3, 0, { _id: `group:allfamilies`, type: "group", label: "All Families" });
				}
				
				Session.set("recipentPeoplelist", results);

			}
		})
		.catch((err)=>{
			console.log("Error while fetching recipients -", err);
		});

	if (this.data.id) {
		Meteor.callAsync("canViewMessage", { messageId: this.data.id })
		.then((result)=>{
			if (!result) {
				FlowRouter.redirect('/my-site');
			}else {
				this.canView.set(true);
			}
		})
		.catch((error)=>{
			FlowRouter.redirect('/my-site');
		});
	} else {
		this.canView.set(true);
	}
	this.messageView = new ReactiveVar("inbox");
	Session.set("messageTargetFolder", "inbox");
});


function formatRecipient(item, escape) {
	const recipientType = item.text.split("|")[1], recipientName = item.text.split("|")[0];
	return "<div><span class='name-label'>" + escape(recipientName) + "</span><span class='name-caption'>" + escape(recipientType) + "</span></div>";

}

function formatTaggedRecipient(item, escape) {
	const recipientType = item.text.split("|")[1], recipientName = item.text.split("|")[0];
	return "<div class='item'><span>" + escape(recipientName) + "</span></div>";
}
