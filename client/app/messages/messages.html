<template name="messages">
    {{# if canView }}
    <div class="container">
        <!--begin::Inbox-->
        <div class="d-flex flex-row">
            <!--begin::Aside-->
            <div class="flex-row-auto offcanvas-mobile w-200px w-xxl-275px" id="kt_inbox_aside">
                <!--begin::Card-->
                <div class="card card-custom card-stretch">
                    <!--begin::Body-->
                    <div class="card-body px-5">
                        <!--begin::Compose-->
                        <div class="px-4 mt-4 mb-10">
                            <a data-cy="new-message-btn" href="#"
                                class="btn btn-block btn-primary font-weight-bold text-uppercase py-4 px-6 text-center"
                                data-toggle="modal" data-target="#kt_inbox_compose" id="new_message">New Message
                            </a>
                        </div>
                        <!--end::Compose-->
                        <!--begin::Navigations-->
                        <div
                            class="navi navi-hover navi-active navi-link-rounded navi-bold navi-icon-center navi-light-icon">
                            <!--begin::Item-->
                            <div class="navi-item my-2">
                                <a href="#" class="navi-link {{#if trueIfEq currentFolder 'inbox'}}active{{/if}}"
                                    data-folder="inbox">
                                    <span class="navi-icon mr-4">
                                        <span class="svg-icon svg-icon-lg">
                                            <!--begin::Svg Icon | path:/metronic/theme/html/demo13/dist/assets/media/svg/icons/Communication/Mail-heart.svg-->
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px"
                                                viewBox="0 0 24 24" version="1.1">
                                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                    <rect x="0" y="0" width="24" height="24"></rect>
                                                    <path
                                                        d="M6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,13 C19,13.5522847 18.5522847,14 18,14 L6,14 C5.44771525,14 5,13.5522847 5,13 L5,3 C5,2.44771525 5.44771525,2 6,2 Z M13.8,4 C13.1562,4 12.4033,4.72985286 12,5.2 C11.5967,4.72985286 10.8438,4 10.2,4 C9.0604,4 8.4,4.88887193 8.4,6.02016349 C8.4,7.27338783 9.6,8.6 12,10 C14.4,8.6 15.6,7.3 15.6,6.1 C15.6,4.96870845 14.9396,4 13.8,4 Z"
                                                        fill="#000000" opacity="0.3"></path>
                                                    <path
                                                        d="M3.79274528,6.57253826 L12,12.5 L20.2072547,6.57253826 C20.4311176,6.4108595 20.7436609,6.46126971 20.9053396,6.68513259 C20.9668779,6.77033951 21,6.87277228 21,6.97787787 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,6.97787787 C3,6.70173549 3.22385763,6.47787787 3.5,6.47787787 C3.60510559,6.47787787 3.70753836,6.51099993 3.79274528,6.57253826 Z"
                                                        fill="#000000"></path>
                                                </g>
                                            </svg>
                                            <!--end::Svg Icon-->
                                        </span>
                                    </span>
                                    <span data-cy="inbox-section"
                                        class="navi-text font-weight-bolder font-size-lg">Inbox</span>
                                    <!--<span class="navi-label">
                                          <span class="label label-rounded label-light-success font-weight-bolder">3</span>
                                        </span>-->
                                </a>
                            </div>
                            <!--end::Item-->
                            <!--begin::Item-->
                            <div class="navi-item my-2">
                                <a href="#" class="navi-link {{#if trueIfEq currentFolder 'all'}}active{{/if}}"
                                    data-folder="all">
                                    <span class="navi-icon mr-4">
                                        <span class="svg-icon svg-icon-lg">
                                            <!--begin::Svg Icon | path:/metronic/theme/html/demo13/dist/assets/media/svg/icons/General/Half-star.svg-->
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px"
                                                viewBox="0 0 24 24" version="1.1">
                                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                    <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                                    <path
                                                        d="M12,4.25932872 C12.1488635,4.25921584 12.3000368,4.29247316 12.4425657,4.36281539 C12.6397783,4.46014562 12.7994058,4.61977315 12.8967361,4.81698575 L14.9389263,8.95491503 L19.5054023,9.61846284 C20.0519472,9.69788046 20.4306287,10.2053233 20.351211,10.7518682 C20.3195865,10.9695052 20.2170993,11.1706476 20.0596157,11.3241562 L16.7552826,14.545085 L17.5353298,19.0931094 C17.6286908,19.6374458 17.263103,20.1544017 16.7187666,20.2477627 C16.5020089,20.2849396 16.2790408,20.2496249 16.0843804,20.1472858 L12,18 L12,4.25932872 Z"
                                                        fill="#000000" opacity="0.3"></path>
                                                    <path
                                                        d="M12,4.25932872 L12,18 L7.91561963,20.1472858 C7.42677504,20.4042866 6.82214789,20.2163401 6.56514708,19.7274955 C6.46280801,19.5328351 6.42749334,19.309867 6.46467018,19.0931094 L7.24471742,14.545085 L3.94038429,11.3241562 C3.54490071,10.938655 3.5368084,10.3055417 3.92230962,9.91005817 C4.07581822,9.75257453 4.27696063,9.65008735 4.49459766,9.61846284 L9.06107374,8.95491503 L11.1032639,4.81698575 C11.277344,4.464261 11.6315987,4.25960807 12,4.25932872 Z"
                                                        fill="#000000"></path>
                                                </g>
                                            </svg>
                                            <!--end::Svg Icon-->
                                        </span>
                                    </span>
                                    <span data-cy="all-mail-section"
                                        class="navi-text font-weight-bolder font-size-lg">All Mail</span>
                                </a>
                            </div>
                            <!--end::Item-->

                            <!--begin::Item-->
                            <div class="navi-item my-2">
                                <a href="#" class="navi-link {{#if trueIfEq currentFolder 'sent'}}active{{/if}}"
                                    data-folder="sent">
                                    <span class="navi-icon mr-4">
                                        <span class="svg-icon svg-icon-lg">
                                            <!--begin::Svg Icon | path:/metronic/theme/html/demo13/dist/assets/media/svg/icons/Communication/Sending.svg-->
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px"
                                                viewBox="0 0 24 24" version="1.1">
                                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                    <rect x="0" y="0" width="24" height="24"></rect>
                                                    <path
                                                        d="M8,13.1668961 L20.4470385,11.9999863 L8,10.8330764 L8,5.77181995 C8,5.70108058 8.01501031,5.63114635 8.04403925,5.56663761 C8.15735832,5.31481744 8.45336217,5.20254012 8.70518234,5.31585919 L22.545552,11.5440255 C22.6569791,11.5941677 22.7461882,11.6833768 22.7963304,11.794804 C22.9096495,12.0466241 22.7973722,12.342628 22.545552,12.455947 L8.70518234,18.6841134 C8.64067359,18.7131423 8.57073936,18.7281526 8.5,18.7281526 C8.22385763,18.7281526 8,18.504295 8,18.2281526 L8,13.1668961 Z"
                                                        fill="#000000"></path>
                                                    <path
                                                        d="M4,16 L5,16 C5.55228475,16 6,16.4477153 6,17 C6,17.5522847 5.55228475,18 5,18 L4,18 C3.44771525,18 3,17.5522847 3,17 C3,16.4477153 3.44771525,16 4,16 Z M1,11 L5,11 C5.55228475,11 6,11.4477153 6,12 C6,12.5522847 5.55228475,13 5,13 L1,13 C0.44771525,13 6.76353751e-17,12.5522847 0,12 C-6.76353751e-17,11.4477153 0.44771525,11 1,11 Z M4,6 L5,6 C5.55228475,6 6,6.44771525 6,7 C6,7.55228475 5.55228475,8 5,8 L4,8 C3.44771525,8 3,7.55228475 3,7 C3,6.44771525 3.44771525,6 4,6 Z"
                                                        fill="#000000" opacity="0.3"></path>
                                                </g>
                                            </svg>
                                            <!--end::Svg Icon-->
                                        </span>
                                    </span>
                                    <span data-cy="sent-section"
                                        class="navi-text font-weight-bolder font-size-lg">Sent</span>
                                </a>
                            </div>
                            <!--end::Item-->
                            {{#if showAdministrativeVisibility}}
                            <!--begin::Item-->
                            <div class="navi-item my-2">
                                <a href="#" class="navi-link {{#if trueIfEq currentFolder 'admin'}}active{{/if}}"
                                    data-folder="admin">
                                    <span class="navi-icon mr-4">
                                        <span class="svg-icon svg-icon-lg">
                                            <!--begin::Svg Icon | path:/metronic/theme/html/demo13/dist/assets/media/svg/icons/Design/PenAndRuller.svg-->
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px"
                                                viewBox="0 0 24 24" version="1.1">
                                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                    <rect x="0" y="0" width="24" height="24"></rect>
                                                    <path
                                                        d="M3,16 L5,16 C5.55228475,16 6,15.5522847 6,15 C6,14.4477153 5.55228475,14 5,14 L3,14 L3,12 L5,12 C5.55228475,12 6,11.5522847 6,11 C6,10.4477153 5.55228475,10 5,10 L3,10 L3,8 L5,8 C5.55228475,8 6,7.55228475 6,7 C6,6.44771525 5.55228475,6 5,6 L3,6 L3,4 C3,3.44771525 3.44771525,3 4,3 L10,3 C10.5522847,3 11,3.44771525 11,4 L11,19 C11,19.5522847 10.5522847,20 10,20 L4,20 C3.44771525,20 3,19.5522847 3,19 L3,16 Z"
                                                        fill="#000000" opacity="0.3"></path>
                                                    <path
                                                        d="M16,3 L19,3 C20.1045695,3 21,3.8954305 21,5 L21,15.2485298 C21,15.7329761 20.8241635,16.200956 20.5051534,16.565539 L17.8762883,19.5699562 C17.6944473,19.7777745 17.378566,19.7988332 17.1707477,19.6169922 C17.1540423,19.602375 17.1383289,19.5866616 17.1237117,19.5699562 L14.4948466,16.565539 C14.1758365,16.200956 14,15.7329761 14,15.2485298 L14,5 C14,3.8954305 14.8954305,3 16,3 Z"
                                                        fill="#000000"></path>
                                                </g>
                                            </svg>
                                            <!--end::Svg Icon-->
                                        </span>
                                    </span>
                                    <span data-cy="admin-view-section"
                                        class="navi-text font-weight-bolder font-size-lg">Admin View</span>
                                    <!--<span class="navi-label">
                                              <span class="label label-rounded label-light-warning font-weight-bolder">5</span>
                                            </span>-->
                                </a>
                            </div>
                            <!--end::Item-->
                            {{/if}}
                        </div>
                        <!--end::Navigations-->
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Card-->
            </div>
            <!--end::Aside-->
            {{#if trueIfEq currentView "message"}} 
                {{> message}} 
            {{else}} 
                {{> messageList currentFolder=currentFolder}} 
            {{/if}}
        </div>
        <!--end::Inbox-->
        <!--begin::Compose-->
        <div class="modal modal-sticky modal-sticky-lg modal-sticky-bottom-right" id="kt_inbox_compose"
            data-backdrop="false" aria-hidden="true" style="display: none">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <!--begin::Form-->
                    <form id="kt_inbox_compose_form">
                        <!--begin::Header-->
                        <div class="d-flex align-items-center justify-content-between py-5 pl-8 pr-5 border-bottom">
                            <h5 class="font-weight-bold m-0">Compose</h5>
                            <div class="d-flex ml-2">
                                <span data-cy="expand-modal" class="btn btn-clean btn-sm btn-icon mr-2">
                                    <i class="flaticon2-arrow-1 icon-1x"></i>
                                </span>
                                <span data-cy="close-modal" class="btn btn-clean btn-sm btn-icon" data-dismiss="modal">
                                    <i class="ki ki-close icon-1x"></i>
                                </span>
                            </div>
                        </div>
                        <!--end::Header-->
                        <!--begin::Body-->
                        <div class="d-block">
                            <!--begin::To-->
                            <div class="d-flex align-items-center border-bottom inbox-to px-8 min-h-45px">
                                <div class="text-dark-50 w-75px">To:</div>
                                <div data-cy="recipient-list" class="d-flex align-items-center flex-grow-1">
                                    <select data-cy="recipient-selector" class="form-control border-0"
                                        multiple name="recipients-list" id="recipients">
                                        {{#each recipients}}
                                        <option value="{{_id}}">{{label}}|{{type}}</option>
                                        {{/each}}
                                    </select>
                                </div>
                            </div>
                            <!--end::To-->
                            <!--begin::Subject-->
                            <div class="border-bottom">
                                <input data-cy="message-subject" class="form-control border-0 px-8 min-h-45px"
                                    name="compose_subject" placeholder="Subject" />
                            </div>
                            <!--end::Subject-->
                            <!--begin::Message-->
                            <div id="kt_inbox_reply_editor" class="border-0 ql-container ql-snow px-3">
                                <textarea data-cy="message-body" class="form-control border-0" name="compose_message"
                                    placeholder="Type message here..." rows="6"></textarea>
                            </div>
                            <!--end::Message-->
                        </div>
                        <!--end::Body-->
                        <!--begin::Footer-->
                        <div class="d-flex align-items-center justify-content-between py-5 pl-8 pr-5 border-top">
                            <!--begin::Actions-->
                            <div class="d-flex align-items-center mr-3">
                                <!--begin::Send-->
                                <div>
                                    <span data-cy="send-message-btn" class="btn btn-primary font-weight-bold px-6"
                                        id="btnComposeSend">Send</span>
                                </div>
                                <!--end::Send-->
                            </div>
                            <!--end::Actions-->
                        </div>
                        <!--end::Footer-->
                    </form>
                    <!--end::Form-->
                </div>
            </div>
        </div>
        <!--end::Compose-->
    </div>
    {{/ if }}
</template>