import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Orgs } from '../../../lib/collections/orgs';
import { Relationships } from '../../../lib/collections/relationships';
import _ from '../../../lib/util/underscore';
import './message.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import DOMPurify from 'dompurify';

Template.message.events({

	"click #btnCancel": (e, i)=> {
		e.preventDefault();
		$("#reply-area").hide();
		$("#btnReply").show();
	},
	"click #btnReplySend": (e, i) => {
		e.preventDefault();
		const threadId = FlowRouter.getParam("id");
		Meteor.callAsync("insertMessage", {
			threadId: threadId,
			message: $("textarea[name='reply-message']").val(),
			includeAllFamily: $('#includeAllFamily').is(':checked')
		})
		.then((result)=>{
			if (result.newId) {
				FlowRouter.go("messages", { id: result.newId });
			} else {
				FlowRouter.redirect("/messages");
			}
		
			$("#reply-area").hide();
			$("#btnReply").show();
		})
		.catch((error)=>{
			mySwal.fire(error.reason);
		});
	},
	"click #btnArchive": async (e, i) => { 
		e.preventDefault();
		const messageId = FlowRouter.getParam("id");
		console.log("to archive", messageId);
		try {
			await Meteor.callAsync("archiveMessage", {messageId: messageId});
		} finally {
			FlowRouter.go("/messages");
		}
	},
	"click #btnMoveToInbox": async (e,i) => {
		e.preventDefault();
		const messageId = FlowRouter.getParam("id");
		await Meteor.callAsync("unarchiveMessage", {messageId: messageId});
	},
	"click #btn-messages-back": (e,i) => {
		FlowRouter.go("messages");
	}
});

Template.message.helpers({
	"isArchived": function () {
		return Meteor.user() && Meteor.user().fetchPerson() && _.contains(this.markedArchived, Meteor.user().personId);
	},
	"isInbox": function() {
		const currentPerson = Meteor.user() && Meteor.user().fetchPerson();
		let qualifiedRecipients = [currentPerson._id];
		qualifiedRecipients = qualifiedRecipients.concat(
		  _.map(
			Relationships.find({orgId: currentPerson.orgId, personId: currentPerson._id, relationshipType:"family"}).fetch(),
			(r) => { return r.targetId;}
		  )
		);
		
		return currentPerson && !(_.contains(this.markedArchived, currentPerson._id)) && 
				( this.personId == currentPerson._id || _.intersection(this.currentRecipients, qualifiedRecipients).length > 0);
	},
	"showBackBtn": function() {
		return !Meteor.isCordova;
	},
	"recipientIncluded": function() {
		const currentUser = Meteor.user(), currentPerson = currentUser && currentUser.fetchPerson();
		return (this.personId == currentPerson._id) || (_.include(this.currentRecipients, currentPerson._id));
	},
	"thread": function() {
		const m = Template.instance().message.get();
		return m;
	},
	'includeAllFamily': function() {
		// This should not be reactive, it should be the state of the message when it was first displayed.
		const m = Template.instance().message.get();
		return !!m.includeAllFamily;
	},
	'showIncludeAllFamilyCheckbox': function() {
		if (!Meteor.user()) {
			return false;
		}
		const person = Meteor.user().fetchPerson();
		if (!person) {
			return false;
		}
		return person.type === 'family';
	},
	'checkboxCursor': function() {
		const m = Template.instance().message.get();
		return !!m.includeAllFamily ? 'cursor: default' : '';
	},
	getMessageContent(threadMessage) {
		return DOMPurify.sanitize(threadMessage);
	}
})
Template.message.onCreated( function() {
	
	const idParam = FlowRouter.getParam("id");
	const instance = this;
	this.message = new ReactiveVar(null);
	instance.autorun( function() {
		instance.subscribe("theSingleMessage", {messageId: idParam});
	});
	Meteor.callAsync('getSingleMessage', idParam)
	.then((res)=>{
		instance.message.set(res);
	})
	.catch((err)=>{
		mpSwal.fire('Error viewing the message');
	});
});

Template.message.onRendered( async () => {
	const messageId = FlowRouter.getParam("id");
	await Meteor.callAsync("markMessageRead", {messageId});
});
