<template name="simpleModal">
	<div id="simpleModal" class="modal fade">
		<div class="modal-dialog {{#unless nonScrollable}} modal-dialog-scrollable{{/unless}} modal-{{modalSize}}" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">{{title}}</h5>
					<div class="d-flex align-items-center">
						<div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close">
							<span class="fad-regular fad-primary fad fa-times"></span>
						</div>
					</div>
				</div>

				<div class="modal-body bg-gray-100">
					{{> Template.dynamic template=template data=data}}
				</div>

				<div class="modal-footer {{justifyFooter}}">
					<div class="mr-auto text-danger font-weight-bolder" id="errorMessage">
						{{errorMessage}}
					</div>
					{{#if showCustomButton}}
						<button type="button" class="btn {{customButtonStyle}} font-weight-bolder" id="customBtn" disabled={{saveDisabled}}>{{customButtonLabel}}</button>
					{{/if}}
					{{#unless hideSave}}
						<button data-cy="save-edits" type="button" class="btn btn-primary font-weight-bolder" id="btnSave" disabled={{saveDisabled}}>{{actionButtonLabel}}</button>
					{{/unless}}
					{{#unless hideCancelButton}}
						<button data-cy="cancel-edits" type="button" class="btn btn-secondary font-weight-bolder"
								data-dismiss="modal">{{#if cancelButtonLabel}}{{cancelButtonLabel}}{{else}}
							Cancel{{/if}}</button>
					{{/unless}}
				</div>
			</div>
		</div>
	</div>
</template>
