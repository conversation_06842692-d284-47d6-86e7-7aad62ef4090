import './simpleModal.html';
import _ from '../../../lib/util/underscore';

Template.simpleModal.events({
	"click #btnSave": (event, template) => {
		if (!template.$("form")[0].checkValidity()) {
			mpSwal.fire("Please correct one or more issues with your form before submitting.");
			return false;
		}
		$(event.target).html('Saving').prop("disabled", true);
		const formFieldData = getFormData(template);
		template.data.onSave(event, template, formFieldData);
	},
	"click #customBtn": (event, template) => {
		if (!template.$("form")[0].checkValidity()) {
			mpSwal.fire("Please correct one or more issues with your form before submitting.");
			return false;
		}
		$(event.target).prop("disabled", true);
		const formFieldData = getFormData(template);
		template.data.customButtonHandler(event, template, formFieldData);
	}
});
Template.simpleModal.helpers({
	actionButtonLabel() {
		const actionButtonLabel = Template.instance().data.actionButtonLabel || Template.instance().data.data?.actionButtonLabel
		return actionButtonLabel || "Save";
	},
	justifyFooter() {
		const justify = Template.instance().data.justifyFooter || Template.instance().data.data?.justifyFooter || "end";
		return `d-flex justify-content-${justify}`;
	},
	saveDisabled() {
		const customValidation = Template.instance().data.customValidation ?? false;
		if (customValidation) {
			return customValidation();
		} else {
			return false;
		}
	},
	modalSize() {
		return Template.instance().data.modalSize || Template.instance().data.data?.modalSize || "xl";
	},
});
Template.simpleModal.rendered = function () {
	this.$(":input").blur(function() { $(this).addClass("visited")});

	if (Template.instance().data && Template.instance().data.onRendered) Template.instance().data.onRendered();
};

function getFormData(template) {
	let formFieldData = {};
	_.each(template.$("form").serializeArray(), (d) => {
		if (template.data.multiValues && d.name.match(/\[]$/)) {
			// avoid any possible interference with existing usage of this modal
			const fieldName = d.name.substring(0, d.name.length - 2);
			if (!formFieldData[fieldName]) {
				formFieldData[fieldName] = [];
			}
			formFieldData[fieldName].push(d.value);
		} else {
			formFieldData[d.name] = d.value;
		}
	});
	return formFieldData;
}
