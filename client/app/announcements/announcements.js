import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { Groups } from '../../../lib/collections/groups';
import { Announcements } from '../../../lib/collections/announcements';
import { processPermissions } from '../../../lib/permissions';
import { showModal } from "../main";
import './announcementFormModal.js';
import './announcements.html';
import { offlinePreventCheck } from '../../../mpweb.js';
const moment = require('moment-timezone');

Template.announcements.onCreated(function() {
	const timezone = Orgs.current().getTimezone();
	this.filterGroup = new ReactiveVar("");
	this.filterRole = new ReactiveVar("");
	this.filterStartDate = new ReactiveVar(new moment.tz(timezone).format("MM/DD/YYYY"));
	this.filterEndDate = new ReactiveVar(new moment.tz(timezone).add(7,"days").format("MM/DD/YYYY"));
});

Template.announcements.onRendered( function() {
	const timezone = Orgs.current().getTimezone();
	var self = this;
	$("#announcementsStartDate").datepicker({ autoclose: true, todayHighlight: true });
	$("#announcementsEndDate").datepicker({ autoclose: true, todayHighlight: true });
	self.autorun( function() {
		const rangeStart = new moment.tz(self.filterStartDate.get(), "MM/DD/YYYY", timezone).startOf('day').valueOf(),
			rangeEnd = new moment.tz(self.filterEndDate.get(), "MM/DD/YYYY", timezone).startOf('day').valueOf();

		self.subscribe("theAnnouncements", {
			expand: true,
			rangeStart,
			rangeEnd
		});
	});
});

Template.announcements.events({
	'click #newAnnouncementLink': function(event, template) {
		if (offlinePreventCheck()) return false;
		showModal("announcementFormModal", {}, "#announcementFormModal");
	},
	'click .editAnnouncementLink': function(event) {
		if (offlinePreventCheck()) return false;
		const announcementId = $(event.target).attr("data-id");
		showModal("announcementFormModal", { announcementId }, "#announcementFormModal")
	},
	'click .deleteAnnouncementLink': function(event) {
		if (offlinePreventCheck()) return false;
		var announcementId = $(event.target).attr("data-id");
		var swalOptions = {  
			title: "Are you sure?",   
			text: "You will not be able to recover this announcement once deleted!",   
			icon: "warning",   
			showCancelButton: true,   
			confirmButtonText: "Yes, delete it!"
		};
		
		var user = Meteor.user();
		var userPerson = (user) ? user.fetchPerson() : {};
		if (userPerson.isMasterAdmin()) {
			swalOptions.input = "checkbox"
			swalOptions.inputPlaceholder = " Delete Associated Announcements in other Orgs"
		};
		return mpSwal.fire(swalOptions)
			.then(async result => {
				if (result.value === true) {
					await Meteor.callAsync("deleteAnnouncement", announcementId);
				} else if (result.value === 1 || result.value === 0) {
					var deleteAll = (result.value === 1) ? true : false;
					await Meteor.callAsync("deleteMasterAnnouncement", announcementId, deleteAll);
				}
			});
	},
    'click .headlineLink': function(event) {
    	if (offlinePreventCheck()) return false;
    	Session.set("announcementId", this._id);
		$("#announcementFormModal").modal();
	},
	'click #update-filters': function(event, instance) {
		const startDate = $("#announcementsStartDate").val(),  endDate = $("#announcementsEndDate").val();
		instance.filterStartDate.set( startDate );
		instance.filterEndDate.set( endDate );
		instance.filterGroup.set( $("#filterGroup").val() );
		instance.filterRole.set( $("#filterRole").val() );
	}
});

Template.announcements.helpers({
  'getRecipientGroupList': function(recipients) {
    const arr = [];
    _.each(recipients, (r) => {
      arr.push(r.name)
    });
    return arr.join(', ');
  },
  'getRecipientRoleList': function(recipients) {
    const arr = [];
    _.each(recipients, (r) => {
      arr.push(r.capitalizeFirstLetter())
    });
    return arr.join(', ');
  },
	'showOptions': function(masterAdminEditId) {
		var currentUser = Meteor.user();
		var currentPerson = (currentUser) ? currentUser.fetchPerson() : null;
		if (masterAdminEditId) {
			if (currentPerson && currentPerson.isMasterAdmin()) {
				return true;
			}
			return false;
		}
		return true;
	},
	'userCanAddAnnouncement': function() {
		return processPermissions({
			assertions: [{ context: "announcements", action: "edit"}],
			evaluator: (person) => person.type=="admin" || person.type=="staff"
		});
	},
	'announcements': function() {
		const self = Template.instance();
		/*const rangeStart = new moment(self.filterStartDate, "MM/DD/YYYY").startOf('day').valueOf(),
			rangeEnd = new moment(self.filterEndDate, "MM/DD/YYYY").startOf('day').valueOf();
		var query = {scheduledDate: {$gte: rangeStart, $lte: rangeEnd} }; */
		var query = {};
		var andItems = [];
		var filterGroup = Template.instance().filterGroup.get();
		if (filterGroup != "") andItems.push({selectedGroups : filterGroup});
		var filterRole = Template.instance().filterRole.get();
		if (filterRole != "") andItems.push({"selectedRoles": filterRole});
		if (andItems.length > 0) {
			query =  { $or: [
				{selectedGroups:{$in: [ [], null]}, selectedRoles: {$in: [ [], null]}}
			]};
			query["$or"].push({"$and" : andItems});
		}

		return Announcements.find(query, {sort: {scheduledDate : 1}}).fetch();
	},
	'scheduledDateFormatted': function() {
		const timezone = Orgs.current().getTimezone();
		let output = moment.tz(this.scheduledDate, timezone).format("MM/DD/YYYY");
		if (this.scheduledEndDate && this.scheduledEndDate > this.scheduledDate) output += " - " + moment.tz(this.scheduledEndDate, timezone).format("MM/DD/YYYY");
		return output;
	},
	'nextReminderFormatted': function() {
		const timezone = Orgs.current().getTimezone();
		let now = new moment.tz(timezone);
		let nextReminder = null;
		if (this.reminders) {
			_.each(this.reminders, (r) => {
				let reminderMoment = new moment.tz(`${r.date} ${r.time}`, "YYYY-MM-DD hh:mm", timezone);
				if (r.status != "sent" && reminderMoment > now) {
					if (nextReminder && reminderMoment < nextReminder) {
						nextReminder = reminderMoment
					} else if (!nextReminder) {
						nextReminder = reminderMoment;
					}
				}
			})
		}

		return (nextReminder) ? nextReminder.format("MMM Do h:mma") : "None Scheduled";
	},
	"groups": function() {
         return Groups.find({}, {sort: {name: 1}});
	},
	formattedAnnouncementStartDate: function() {
		return new Template.instance().filterStartDate.get();
	},
	formattedAnnouncementEndDate: function() {
		return new Template.instance().filterEndDate.get();
	},
});
