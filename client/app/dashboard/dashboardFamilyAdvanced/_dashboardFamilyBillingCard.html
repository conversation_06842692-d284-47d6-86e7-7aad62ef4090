
<template name="dashboardFamilyBillingCard">
	<!-- BEGIN billing card -->
	<div class="card card-body card-custom mb-6" data-cy="billing-widget">
		<!-- BEGIN billing card header -->
		<div class="d-flex align-items-center justify-content-between">
			<div class="icon-dashboard-family-billing">
		
			</div>
			<div class="pl-5 font-size-h3 font-weight-bold" data-cy="billing-widget-title">
				Billing
			</div>
			<div class="ml-auto">
				
			</div>
		</div>
		<!-- END billing card header -->

		<!-- BEGIN billing card button switcher -->

		<div class="row mt-3 p-1" style="background-color:#F5F7FA;border-radius:0.42em;">
			<div class="col-6 text-center p-1" style="background-color: #FFFFFF; border-radius:0.42em;" data-cy="outstanding-tab">
				Outstanding
			</div>
			
			<div class="col-6 text-center p-1">
				<a href="/people/{{currentUser.personId}}#transactions" class="btn p-0" data-cy="view-invoices-link">
				View Invoices
				</a>
			</div>
		</div>
		<!-- END billing card button switcher -->

		<!-- BEGIN billing card body -->
		<div style="border:2px solid #F5F7FA;border-radius:0.42em;" class="row mt-3">
			<div class="col-12">
				<div class="row">				
					<div class="col-12 pt-3 pb-3">
						
						<span class="font-size-h6" data-cy="balance-label">Your Balance</span><br/>
						<span class="font-size-h1 font-weight-bolder" data-cy="balance-amount">{{formatCurrency currentPerson.billingStatus.currentAmountDue}}</span><br/>
						
					</div>
				</div>
				<div class="d-flex align-items-center justify-content-between pb-3">
					<div style="font-size:1.1rem;" data-cy="autopay-status">
						{{#if autopayStatus.HAS_AMOUNT_DUE}}
							<span style="color:var(--success);">Autopayment of {{formatCurrency currentPerson.billingStatus.autoPayAmountFuture}} on {{formatDate currentPerson.billingStatus.autoPaymentDateFuture "M/DD/YYYY"}}</span>
						{{/if}}
						{{#if autopayStatus.NO_PAYMENT_DUE}}
							<span style="color:var(--success);">Autopay enabled</span>
						{{/if}}
						{{#if autopayStatus.AUTOPAY_NOT_SETUP}}
							<a href="/people/{{currentUser.personId}}#billing" data-cy="enroll-autopay-link">Enroll in autopay</a>
						{{/if}}
					</div>
					<div class="ml-auto">
						{{#if currentPerson.billingStatus.hasAmountDue}}
							<a href="/people/{{currentUser.personId}}#transactions" class="btn btn-primary" data-cy="pay-now-button">Pay Now</a>
						{{/if}}
					</div>
				</div>
			</div>
		</div>
		<!-- END billing card body -->
	</div>
	<!-- END billing card -->
</template>