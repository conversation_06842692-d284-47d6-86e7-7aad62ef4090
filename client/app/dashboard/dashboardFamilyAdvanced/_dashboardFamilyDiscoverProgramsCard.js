import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { People } from "../../../../lib/collections/people";
import { Orgs } from "../../../../lib/collections/orgs";
import { Relationships } from "../../../../lib/collections/relationships";
import { showModal, hideModal } from "../../../../client/app/main";
import { Log } from 'meteor/logging';
import "./_dashboardFamilyAddProgram";
import './_dashboardFamilyDiscoverProgramsCard.html';

Template.dashboardFamilyDiscoverProgramsCard.onCreated(async function() {
	const instance = this;
	instance.showMore = new ReactiveVar(false);
	instance.programFilter = new ReactiveVar("Near Me");
	instance.availablePrograms = ReactiveVar([]);
	const currentPersonId = Meteor.user().personId;
	const currentOrgId = Orgs.current()._id;
	instance.autorun(async function() {
		if (instance.subscriptionsReady()) {
			const childIds = _.uniq(Relationships.find({ personId: currentPersonId, relationshipType: "family" }).map((rel) => rel.targetId));
			await Meteor.callAsync("getMultiOrgProgramsAndPlans", currentOrgId, childIds)
			.then((result) => {
				instance.availablePrograms.set( result );
			}).catch((error) => {
				mpSwal.fire('Error', error.reason || error.message, 'error');
				Log.error(error);
			});
		}
	});
});

Template.dashboardFamilyDiscoverProgramsCard.helpers({
	programs() {
		const allPrograms = Template.instance().availablePrograms.get();
		const showMore = Template.instance().showMore.get();
		const programFilter = Template.instance().programFilter.get();
		const currentOrgId = Orgs.current()._id;
		const searchTypePrograms = programFilter == "Near Me" ? 
			allPrograms.filter((program) => program.orgId !== currentOrgId)
				: 
			allPrograms.filter((program) => program.orgId === currentOrgId);
		const programsLimited = showMore ? searchTypePrograms : searchTypePrograms.slice(0, 5);
		return programsLimited;
	},
	avatarGroup(program) {
		const avatarGroupData = { people: [], more: false };
		for (let i = 0; i < (program.childIds || []).length && i < 2; i++) {
			const person = People.findOne(program.childIds[i]);
			avatarGroupData.people.push( person );
		}
		if (program.childIds.length > 2) {
			avatarGroupData.more = program.childIds.length - 2;
		}
		return avatarGroupData;
	},
	showMore() {
		return Template.instance().showMore.get();
	},
	searchTypeLabel() {
		return Template.instance().programFilter.get();
	}
});

Template.dashboardFamilyDiscoverProgramsCard.events({
	"click #discoverMoreProgramsShowMore"(event, instance) {
		instance.showMore.set(true);
	},
	"click .discover-programs-search-type"(event, instance) {
		const targetType = event.currentTarget.getAttribute("data-value");
		instance.programFilter.set(targetType);
	},
	"click .discover-programs-link"(event, instance) {
		const programIndex = event.currentTarget.getAttribute('data-program-id');
		const programFilter = Template.instance().programFilter.get();
		const allPrograms = Template.instance().availablePrograms.get();
		const currentOrgId = Orgs.current()._id;
		const searchTypePrograms = programFilter === "Near Me" ? 
			allPrograms.filter((program) => program.orgId != currentOrgId)
				: 
			allPrograms.filter((program) => program.orgId == currentOrgId);
		const program = searchTypePrograms[programIndex];
		
		const availableChildren = People.find({
			_id: { $in: program.childIds }
		}).fetch();

		showModal("simpleModal", {
            title: "Explore New Program",
            template: "dashboardFamilyAddProgram",
            hideCancelButton: false,
            actionButtonLabel: "Begin Registration",
            justifyFooter: 'center',
			showCustomButton: true,
            data: {
                program: program,
				availableChildren: availableChildren,
            },
			onSave: async (e, i, formFieldData) =>  {
				const selectedChildId = $("select[name='child-id']").val();
				if ((selectedChildId || "") === "") {
					mpSwal.fire("Error", "Please select a child to register", "error");
					$(e.target).html('Begin Registration').prop("disabled", false);
					return;
				}
				// if destination program in current org, simply redirect
				if (program.orgId == Orgs.current()._id) {
					console.log("program in current org, redirecting to", program);
					hideModal("#simpleModal");
					//location.replace(`/people/${selectedChildId}?program-id=${program._id}#programs`);
					//FlowRouter.go(`/people/${selectedChildId}?program=${JSON.stringify(program)}`);
					const localDestUrl = "/registration?orgId=" + currentOrgId + "&reregistrationfamilyid=" + Meteor.user().personId + "&reregistrationchildids=" + selectedChildId + "&reregistrationplanid=" + program.plan._id;
					console.log("localDestUrl", localDestUrl);
					location.replace(localDestUrl);
					setTimeout(function() {
					  document.location = '#programs';
					}, 0);
					return;
				}
				// if destination program in another org, setup registration if necessary and wait for enterprise sync
				const registrationData = {
					childId: selectedChildId,
					familyMemberId: Meteor.user().personId,
					orgId: program.orgId,
				};
				
				let readyToRegister = false;
				let retryReadyToRegister = 0;
				let registrationResult;
				while (!readyToRegister && retryReadyToRegister < 30) {
					retryReadyToRegister++;
					registrationResult = await Meteor.callAsync("ensureRegisterChildAndFamilyInOrg", registrationData);
					console.log("ensureRegisterChildAndFamilyInOrg", registrationResult);
					if (registrationResult && registrationResult.canRegister) {
						readyToRegister = true;
					} else {
						await new Promise(r => setTimeout(r, 5000));
						retryReadyToRegister++;
					}
				}
				if (!readyToRegister) {
					mpSwal.fire("Error", "Registration in other site is taking longer than expected, please try again later", "error");
					$(e.target).html('Begin Registration').prop("disabled", false);
					return;
				} else {
					console.log("calling switchUserMembership with", program);
					Meteor.callAsync('switchUserMembership', program.orgId).then((result) => {
						//location.replace("/loading?setLoc=redirect&desturl=" + "/people/" + registrationResult.personInOrg.personId + "?program=" + JSON.stringify(program) + "#programs");
						const redirectUrl = "registration?orgId=" + program.orgId + "&reregistrationfamilyid=" + registrationResult.familyPersonInOrg + "&reregistrationchildids=" + registrationResult.personInOrg + "&reregistrationplanid=" + program.plan._id;
					}).catch((error) => {
						console.log("switching error", error);
						mpSwal.fire("Error switching membership", error.reason + " " + "Destination: " + program.orgId, "error");
					});
				}

			}
		});
	}
});