<template name="dashboardFamilyAdvancedHeader">
	<ul class="menu-nav">

		<li class="menu-item {{isActivePath path='/my-site' class='menu-item-active' }}" aria-haspopup="true">
			{{ #if showSwitchableDropdown }}
			<div class="dropdown">
				<button type="button" class="btn btn-outline-primary dropdown-toggle" id="discoverMoreProgramsSearchType" data-toggle="dropdown" aria-expanded="false">
					{{ currentOrgName }}
				</button>
				<div class="dropdown-menu">
					{{#each membership in memberships}}
						<a class="dropdown-item switch-membership-entry" href="#" data-value="{{membership._id}}">{{membership.name}}</a>
					{{/each}}
				</div>
			</div>
			{{ else }}
				<button type="button" class="btn btn-outline-primary" >
					{{ currentOrgName }}
				</button>
			{{/if}}
		</li>
	</ul>			
</template>