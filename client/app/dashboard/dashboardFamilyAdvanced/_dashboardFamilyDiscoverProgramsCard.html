<template name="dashboardFamilyDiscoverProgramsCard">
	<!-- <PERSON><PERSON><PERSON> discover programs card -->
	<div class="card card-body card-custom">
		<!-- BEGIN discover card header -->
		<div class="d-flex align-items-center justify-content-between">
			<div>
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#000" d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2m.366 11.366l-3.469 6.01a8.06 8.06 0 0 0 4.459.51a9.94 9.94 0 0 1 .784-5.494zm3.518 2.031a7.96 7.96 0 0 0-.587 3.894a8 8 0 0 0 3.078-2.456zm-7.025-4.055a9.95 9.95 0 0 1-4.365 3.428a8 8 0 0 0 2.671 3.605l3.469-6.009zm11.103-.13l-.258.12a7.95 7.95 0 0 0-2.82 2.333l2.492 1.439a8 8 0 0 0 .586-3.893M4 12q0 .4.038.789a7.95 7.95 0 0 0 3.078-2.453L4.624 8.897A8 8 0 0 0 4 12m12.835-6.374l-3.469 6.008l1.775 1.025a9.95 9.95 0 0 1 4.366-3.43a8 8 0 0 0-2.419-3.402zM12 4q-.695.001-1.357.115a9.93 9.93 0 0 1-.784 5.494l1.775 1.025l3.469-6.01A8 8 0 0 0 12 4m-3.297.71l-.191.088a8 8 0 0 0-2.886 2.367l2.49 1.438a7.96 7.96 0 0 0 .587-3.893"/></svg>
			</div>
			<div class="pl-5 font-size-h3 font-weight-bold">
				Discover More Programs
			</div>
			<div class="ml-auto">
				<div class="dropdown">
					<button type="button" class="btn btn-outline-primary dropdown-toggle" id="discoverMoreProgramsSearchType" data-toggle="dropdown" aria-expanded="false">
						{{searchTypeLabel}}
					</button>
					<div class="dropdown-menu">
						<a class="dropdown-item discover-programs-search-type" href="#" data-value="Near Me">Near Me</a>
						<a class="dropdown-item discover-programs-search-type" href="#" data-value="This Site">This Site</a>
					</div>
				</div>
			</div>
		</div>
		<!-- END discover card header -->

		<!-- BEGIN discover card body -->
		<div class="mt-6">
			<p class="font-size-h3">
				Did you know your child can explore new interests at nearby locations? &#128161;
			</p>
			<p class="text-muted">
				See what programs are available at our sister sites.
			</p>
			<hr noshade style="background-color:#ccc" />
			<!-- BEGIN programs list -->
			{{#each program in programs}}
			<a href="#" style="color:unset" class="discover-programs-link" data-program-id="{{@index}}">
				<div class="d-flex p-3 rounded mb-3" style="background-color: var(--lighter-primary);">
					<div class="flex-shrink-0" style="width:100px">
						<div class="symbol-group symbol-hover">
							{{#with avatarGroup program}}
								{{#each child in this.people}}
									
									{{#unless child.hasAvatar}}
										<div class="symbol symbol-35 symbol-circle" data-toggle="tooltip" title="" data-original-title="Ana Fox">
											<div class="people-card-user-img people-card-user-small"
													style="background-color:{{getAvatarBackground child.personInitials}}; height: 35px !important; width: 35px !important;">
												<span class="initials" style="font-size:23px;">{{child.personInitials}}</span>
											</div>
										</div>
									{{else}}
										<div class="symbol symbol-35 symbol-circle" data-toggle="tooltip" title="" data-original-title="Ana Fox">
											<div class="">
												<img class="people-card-center-cropped people-card-user-small" style="display:none; height: 35px !important; width: 35px !important;" src="{{child.getAvatarUrl}}" onload="showme(this);" onerror="imgError(this);" alt="User profile picture">
												<i class="icon-2x fad fa-spinner fa-spin timelinePhotoSpinner text-primary" style="display:none;margin-top:20px;"></i>
											</div>
										</div>
									{{/unless}}
								{{/each}}
							
								{{#if this.more}}
								<div class="symbol symbol-35 symbol-circle symbol-light-success" data-toggle="tooltip" title="" data-original-title="Invite someone">
									<span class="symbol-label font-weight-bold">+{{this.more}}</span>
								</div>
								{{/if}}
							{{/with}}
						</div>
					</div>
					<div class="flex-grow-1">
						<span class="font-size-h6 font-weight-bold">
							{{program.plan.description}}
						</span>
						<br/>
						{{{program.plan.programDetails}}}
						<br/>
						<p class="mt-3">
							{{program.orgName}}
							<!--<span class="text-muted">(1.2 miles from school)</span>-->
						</p>
					</div>
				</div>
			</a>
			{{/each}}

			{{#unless showMore}}
			<div class="text-center">
				<button type="button" class="btn btn-outline-primary" id="discoverMoreProgramsShowMore">
					Show More
				</button>
			</div>
			{{/unless}}
			<!-- END programs list -->
		</div>
		<!-- END discover card body -->

	</div>
	<!-- END discover programs card -->
</template>