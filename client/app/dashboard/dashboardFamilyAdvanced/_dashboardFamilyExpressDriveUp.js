import { Template } from 'meteor/templating';
import { People } from '../../../../lib/collections/people';
import './_dashboardFamilyExpressDriveUp.html';

Template.dashboardFamilyExpressDriveUp.onCreated(function() {
	const instance = this;
	instance.attending = new ReactiveVar(true);
	instance.currentEstimatedArrival = new ReactiveVar();

	instance.autorun(() => {
		const childId = Template.currentData()._id;
		instance.subscribe('childById', childId);
	});
});

Template.dashboardFamilyExpressDriveUp.helpers({
	isArriving() {
		const person = People.findOne({ _id: Template.instance().data._id });
		return person && !person.checkedIn;
	},
	attending() {
		return Template.instance().attending.get();
	},
	currentEstimatedArrival() {
		return Template.instance().currentEstimatedArrival.get();
	}
});

Template.dashboardFamilyExpressDriveUp.events({
	'change #expressDriveUpAttendingToday': function(event, template) {
		const newValue = event.target.value;
		template.attending.set(newValue === 'Yes');
	},
	'click .estimated-arrival-btn': function(event, template) {
		const newValue = event.currentTarget.getAttribute('data-value');
		template.currentEstimatedArrival.set(newValue);
	}
});