import { Tracker } from 'meteor/tracker';
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import _ from '../../../../lib/util/underscore.js';
import { getRevenuePerformanceWidgetData,
	getAmountInvoiced,
	getAging,
	getPayerStatus,
	getPastDueAccounts,
	getRecentManualPayments,
	getACHReturns,
	getCreditCardRefusal,
	getRecentRefunds,
	getRegistrationEmployee,
	getDistrictEmployeeReview,
	getUpcomingWithdrawals,
	getNextAutoInvoicing,
	getInProgressPaymentsAndRefunds,
	getUpcomingBillingAdjustments } from "../../../app/billing/overview/overview.js";
import { processPermissions } from "../../../../lib/permissions.js";
import { Orgs } from '../../../../lib/collections/orgs.js';
import { User } from '../../../../lib/collections/users.js';
import './_dashboardLayout.html';
import '../../../app/billing/overview/_ACHReturns.js';
import '../../../app/billing/overview/_aging.js';
import '../../../app/billing/overview/_amountInvoiced.js';
import '../../../app/billing/overview/_creditCardRefusal.js';
import '../../../app/billing/overview/_districtEmployeeReview.js';
import '../../../app/billing/overview/_inProgressPaymentsAndRefunds.js';
import '../../../app/billing/overview/_nextAutoInvoicing.js';
import '../../../app/billing/overview/_pastDueAccounts.js';
import '../../../app/billing/overview/_payerStatus.js';
import '../../../app/billing/overview/_recentManualPayments.js';
import '../../../app/billing/overview/_recentRefund.js';
import '../../../app/billing/overview/_registrationStatus.js';
import '../../../app/billing/overview/_revenuePerformance.js';
import '../../../app/billing/overview/_upcomingBillingAdjustments.js';
import '../../../app/billing/overview/_upcomingWithdrawals.js';

import '../dashboardBuilder/widgets/_widgetPayersDueSummary.js';

Template.dashboardLayout.helpers({
	"dashboardSectionItems"(section) {
		const data = Template.instance().dashboardData.get();
		return data && data[section].items;
	},
	"dashboardSections"() {
		const data = Template.instance().dashboardData.get();
		if (processPermissions({
			assertions: [{ context: "billing/reports", action: "read" }],
			evaluator: (person) => person.type == "admin"
		})) {
			return data && data.sections
		} else {
			const filteredSections = data.sections.filter(section => section.title !== 'Billing')
			return data && filteredSections;
		}
	},
	"dashboardSection"(section, prop) {
		const data = Template.instance().dashboardData.get();
		return data && data[section] && (prop ? data[section][prop] : data["section"]);
	},
	"dashboardData"() {
		return Template.instance().dashboardData;
	},
	"hasDashboardSectionItems"(section) {
		const data = Template.instance().dashboardData.get();
		return data && data[section].items && data[section].items.length > 0;
	},
	"requery"() {
		const instance = Template.instance();
		return instance.requeryActive.get();
	},
	"widgetParameters"() {
		return Template.instance().widgetParameters;
	},
	"handler"() {
		const instance = Template.instance();
		return {
			changeParameter(destination, item, val) {
				const currentState = instance.widgetParameters.get(destination) || {};
				currentState[item] = val;
				instance.widgetParameters.set(destination, currentState);
			},
			handleDropdownChange(options) {
				Meteor.callAsync("getDashboardItem", {
					timespan: options.selectedValue,
					name: options.itemId,
					section: options.sectionTitle,
					orgIds: instance.data.orgs && instance.data.orgs.get()
				}).then((result)=>{
					const data = instance.dashboardData.get(),
					section = _.find(data.sections, s => s.title === options.sectionTitle),
					itemIndex = section && section.items.findIndex(i => i.itemId === options.itemId);

					if (itemIndex >= 0) {
						section.items[itemIndex] = result;
						instance.dashboardData.set(data);
					}
				});
			}
		}
	},
	"parentData"() {
		return Template.instance().data
	},
	"equals"(a, b) {
		return a === b;
	},
	"showRecentManualPayments"() {
		if (processPermissions({
			assertions: [{ context: "billing/payments/manageBankDeposits", action: "read" }],
			evaluator: (person) => person.type == "admin"
		})) {
			return true;
		} else {
			return false;
		}
	},
	"showRegistration"() {
		return Orgs.current().hasCustomization("integrations/airslate/enabled") || Orgs.current().hasCustomization("registrationFlow")
	},
	"showDistricEmployeeReview"() {
		return Orgs.current().hasCustomization("registrationFlow")
	},
	"showACHReturnsAndCreditCardRefusal"() {
		const currentUser = Meteor.user();
		if (currentUser.fetchPerson().type == "admin" &&
			_.deep(Orgs.current(), "billing.enabled") &&
			(Orgs.current().billing.stripeAccountId || _.deep(Orgs.current(), "billing.adyenInfo"))
		) {
			if (processPermissions({
				assertions: [{ context: "billing/payments/manageChargebacks", action: "read" }],
				evaluator: (person) => person.type == "admin"
			})) {
				return true;
			}
		}
		return false;
	},
	"showRecentRefund"() {
		const currentUser = Meteor.user();
		if (currentUser.fetchPerson().type == "admin" && _.deep(Orgs.current(), "billing.enabled") &&
			(Orgs.current().billing.stripeAccountId || _.deep(Orgs.current(), "billing.adyenInfo"))
		) {
			return true;
		}
		return false;
	},
	"showPayerStatus"() {
		if (processPermissions({
			assertions: [{ context: "billing/payments", action: "read" }],
			evaluator: (person) => person.type == "admin",
		})) {
			return true;
		}
		return false
	},
	"revenuePerformanceData"() {
		return Template.instance().revenuePerformanceData.get();
	},
	"amountInvoicedData"() {
		return Template.instance().amountInvoicedData.get();
	},
	"agingData"() {
		return Template.instance().agingData.get();
	},
	"payerStatusData"() {
		return Template.instance().payerStatusData.get();
	},
	"pastDueAccountsData"() {
		return Template.instance().pastDueAccountsData.get();
	},
	"recentManualPaymentsData"() {
		return Template.instance().recentManualPaymentsData.get()
	},
	"achReturnsData"() {
		return Template.instance().achReturnsData.get();
	},
	"creditCardRefusalData"() {
		return Template.instance().creditCardRefusalData.get();
	},
	"recentRefundsData"() {
		return Template.instance().recentRefundsData.get();
	},
	"registrationStatusData"() {
		return Template.instance().registrationStatusData.get();
	},
	"districtEmployeeData"() {
		return Template.instance().districtEmployeeData.get();
	},
	"upcomingBillingAdjustmentsData"() {
		return Template.instance().upcomingBillingAdjustmentsData.get();
	},
	"upcomingWithdrawalsData"() {
		return Template.instance().upcomingWithdrawalsData.get();
	},
	"nextAutoInvoicingData"() {
		return Template.instance().nextAutoInvoicingData.get();
	},
	"inProgressPaymentsAndRefundsData"() {
		return Template.instance().inProgressPaymentsAndRefundsData.get();
	}
});

Template.dashboardLayout.onCreated(function () {
	var self = this;
	self.dashboardData = new ReactiveVar();
	self.requeryActive = new ReactiveVar();
	self.widgetParameters = new ReactiveDict();
	//reloadDashboard(self);

	//Billing overview related reactive variable
	this.revenuePerformanceData = new ReactiveVar(null);
	this.amountInvoicedData = new ReactiveVar(null);
	this.agingData = new ReactiveVar(null);
	this.payerStatusData = new ReactiveVar(null);
	this.pastDueAccountsData = new ReactiveVar(null);
	this.recentManualPaymentsData = new ReactiveVar(null);
	this.achReturnsData = new ReactiveVar(null);
	this.creditCardRefusalData = new ReactiveVar(null);
	this.recentRefundsData = new ReactiveVar(null);
	this.registrationStatusData = new ReactiveVar(null);
	this.districtEmployeeData = new ReactiveVar(null);	
	this.upcomingBillingAdjustmentsData = new ReactiveVar(null);	
	this.upcomingWithdrawalsData = new ReactiveVar(null);
	this.nextAutoInvoicingData = new ReactiveVar(null);
	this.inProgressPaymentsAndRefundsData = new ReactiveVar(null);
});

Template.dashboardLayout.onRendered(function () {
	var self = this;

	Tracker.autorun(function () {
		const paramsOrgs = self.data.orgs && self.data.orgs.get();
		if (self.data.name !== "billing") {
			reloadDashboard(self);
		}
	});

	if (self.data.name == "billing") {
		Promise.all([
			getRevenuePerformanceWidgetData(this),
			getAmountInvoiced(this),
			getAging(this),
			getPayerStatus(this),
			getPastDueAccounts(this),
			getRecentManualPayments(this),
			getACHReturns(this),
			getCreditCardRefusal(this),
			getRecentRefunds(this),
			getRegistrationEmployee(this),
			getDistrictEmployeeReview(this),
			getUpcomingWithdrawals(this),
			getInProgressPaymentsAndRefunds(this),
			getUpcomingBillingAdjustments(this),
			getNextAutoInvoicing(this)]
		).then((results) => {
			return true;
		}).catch((error) => {
			throw new Meteor.Error(error.error, error.reason);
		});
	}
});

function reloadDashboard(instance) {
	const options = {
		name: instance.data.name,
		orgIds: instance.data.orgs && instance.data.orgs.get(),
		widgetParameters: instance.widgetParameters.all()
	};

	instance.requeryActive.set(true);
	Meteor.callAsync("getDashboardOverviewData", options)
	.then((result)=>{
		instance.dashboardData.set(result);
	})
	.catch((error)=>{
		console.log("dashboard load error", error);
	})
	.finally(()=>{
		instance.requeryActive.set(false);
	});
}