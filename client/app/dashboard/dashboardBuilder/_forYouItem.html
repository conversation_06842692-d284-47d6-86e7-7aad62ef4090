<template name="forYouItem">
	{{#if (trueIfEq item.style "alert")}}
		<div class="d-flex align-items-center bg-light-secondary rounded p-5 mb-9">
			<i class="fa fa-comment-alt mr-5">
			</i>
			<div class="d-flex flex-column flex-grow-1 mr-2">
				<a href="#" class="font-weight-bold text-dark-75 text-hover-primary font-size-lg mb-1">{{item.title}}</a>
				<span class="text-muted font-weight-bold">{{item.subTitle}}</span>
			</div>
			<div class="d-flex flex-column">
				{{#if item.link}}
				<a href="{{item.link}}" class="btn btn-primary">Go</a>
				{{/if}}
			</div>
		</div>
	{{else if (trueIfEq item.style "info")}}
		<div class="for-you-info-card pt-5">
			{{#if item.image}}
				
				<img src="{{item.image}}">
				
			{{/if}}

			<b>{{item.title}}</b><br/>
			<h1>{{item.metric}}</h1>
			<p>{{item.subTitle}}</p>
			<h3>{{item.descriptiveTitle}}</h3>
			<p>{{item.descriptiveText}}</p>
			{{#if item.link}}
			<a href="{{item.link}}" class="btn btn-primary">{{item.linkTitle}}</a>
			{{/if}}
	
		</div>
	{{else if (trueIfEq item.style "chart")}}
		<div class="row">
			<div class="col-lg-8">
				<div id="{{chartId}}"></div>
			</div>
			<div class="col-lg-4 d-flex flex-column">
				<!--begin::Engage Widget 2-->
				<div class="flex-grow-1 bg-danger p-8 rounded-xl flex-grow-1 bgi-no-repeat" style="background-position: calc(100% + 0.5rem) bottom; background-size: auto 70%; background-image: url(/img/person-sitting.svg)">
					<h4 class="text-inverse-danger mt-2 font-weight-bolder">User Confidence</h4>
					<p class="text-inverse-danger my-6">Boost marketing &amp; sales 
					<br />through product confidence.</p>
					<a href="#" class="btn btn-warning font-weight-bold py-2 px-6">Learn</a>
				</div>
				<!--end::Engage Widget 2-->
			</div>
		</div>
	{{/if}}
</template>