import { People } from "../../../../../lib/collections/people";
import { User  } from "../../../../../lib/collections/users";
import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { processPermissions } from "../../../../../lib/permissions";
import { showModal } from "../../../main";
import './_widgetExpressDriveUp.html';
import '../../../moments/partials/_checkinFormModal';
import '../../../moments/partials/_checkoutFormModal';
import moment from 'moment-timezone';
import $ from 'jquery';

Template.widgetExpressDriveUp.onCreated(function() {
    this.autorun(() => {
        const user = Meteor.user();
        if (user) {
            this.subscribe('expressDriveUpPeople');
        }
    });
});

Template.widgetExpressDriveUp.helpers({
    'checkLoading': function () {
        return Session.get('isLoading');
    },
    'peopleHere': function () {
        const user = Meteor.user();
        const q = {
            orgId: user.orgId,
            inActive: { $ne: true },
            $or: [
                { 'familyCheckIn.dropOffArrival': true },
                { 'familyCheckOut.pickUpArrival': true }
            ]
        };

        return People.find(q, { sort: { lastName: 1, firstName: 1 } }).fetch();
    },
    'arrivals': function () {
        const user = Meteor.user();
        const q = {
            orgId: user.orgId,
            inActive: { $ne: true },
            $or: [
                { 'familyCheckIn.dropOffTimeEstimate': { $exists: true } },
                { 'familyCheckOut.pickUpTimeEstimate': { $exists: true } }
            ]
        };


        return People.find(
            q,
            {
                sort: {
                    'familyCheckIn.dropOffTimeEstimate': 1,
                    'familyCheckIn.pickUpTimeEstimate': 1
                }
            }
        ).fetch();
    },
    'getCheckButtonIcon': function (person) {
        return person.checkedIn ? 'fa-sign-out text-black' : 'fa-user-clock text-white';
    },
    'getCheckButtonText': function (person) {
        return person.checkedIn ? 'Check Out' : 'Waiting for Checkin';
    },
    'getCheckButtonColor': function (person) {
        return person.checkedIn ? 'btn-secondary btn-text-black' : 'btn-info btn-text-white';
    },
    'getEstimatedTime': function (person) {
        const time = person?.familyCheckIn?.dropOffTimeEstimate || person?.familyCheckOut?.pickUpTimeEstimate;
        return `Arrival: ~${moment(time).format("h:mm a")}`;
    },
}
);

Template.widgetExpressDriveUp.events(
    {
        'click .site-check-in-out-btn': function (e, i) {
            if (
                !processPermissions({
                    assertions: [{ context: 'people/movement', action: 'edit' }],
                    evaluator: (thisPerson) => thisPerson.type === 'admin' || thisPerson.type === 'staff'
                }
                )
            ) {
                return false;
            }

            const personId = $(e.currentTarget).attr('data-id');
            const person = People.findOne(personId);
            if (!person) {
                return;
            }

            Session.set('currentId', personId);

            if (person.checkedIn) {
                showModal('_checkoutFormModal', { personId: person._id }, '#_checkoutFormModal');
            } else {
                Session.set('currentGroupId', person.defaultGroupId);
                showModal('_checkinFormModal', { personId: person._id }, '#_checkinFormModal');
            }
        }
    }
);
