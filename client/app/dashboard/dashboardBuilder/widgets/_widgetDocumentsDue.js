import { Template } from 'meteor/templating';
import _ from 'underscore';
import './_widgetDocumentsDue.html';
import '../_dashboardItem';

Template.widgetDocumentsDue.helpers({
  getItem() {
    const people = Template.instance().data?.widgetData;
    
    const formattedRows = _.map( people, person => {
      const label = `${person.personDetail.firstName} ${person.personDetail.lastName}`;
      return {
        label,
        value: `${person.missingDocumentsCount}`,
        link: `/people/${person.personId}#profile`,
        personId: person.personId
      }
    })
    const subtitle = (formattedRows && formattedRows.length > 0) ? "" : "No documents currently outstanding";
    const isLoading = Template.instance().data?.isLoading
    
    return {
      style: "list",
      title: "Documents Outstanding",
      subTitle: subtitle,
      isLoading: isLoading,
      dataItems: _.first(formattedRows, 5),
      footer: (formattedRows && formattedRows.length > 5),
      modalAction: {
        btnText: "Show All",
        title: "All Documents Outstanding",
        items: formattedRows
      },
    }
  }
})
