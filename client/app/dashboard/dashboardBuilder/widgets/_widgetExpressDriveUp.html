<template name="widgetExpressDriveUp">
  <div class="card card-custom gutter-b">
    {{#if checkLoading}}
        {{> loading fontSize="50px"}}
    {{else}}
    <div class="card-header align-items-center justify-content-between border-0 mt-4">
      <div class="d-flex flex-column">
        <span data-cy="express-drive-up" class="font-weight-bolder text-dark card-title">{{getEduTitle}}</span>
        <span class="text-dark-25">Who's on their way right now</span>
      </div>
      <a data-cy="express-drive-up-view-all-button" href="/express-drive-up" class="btn btn-primary font-weight-bolder btn-text-white btnExpressDriveUp">
        View All
      </a>
    </div>
    <div class="card-body table-responsive no-padding">
      <table class="table">
        <tbody>
          <tr>
            <th style="width:40%">People</th>
            <th style="width:30%">Status</th>
            <th style="width:30%">Action</th>
          </tr>
          {{#each person in peopleHere}}
            <tr>
              <td style="width:40% vertical-align: middle;">
                <div class="d-flex">
                  <div class="flex-shrink-0 mr-7">
                    
                    {{#unless person.hasAvatar}}
                        <div class="d-flex avatar-circle align-items-center justify-content-center"
                             style="background-color:{{getAvatarBackground person.personInitials}}">
                            <span class="initials">{{person.personInitials}}</span>
                        </div>
                    {{else}}
                      <div class="people-list-user-img" style="background-image:url({{person.getAvatarUrl}})"></div>
                    {{/unless}}
                  </div>
                  <div class="flex-grow-1">
                    <div class="d-flex align-items-center justify-content-between flex-wrap mt-2">
                      <div class="mr-3">
                        <a data-cy="people-express-drive-up-item-{{person._id}}" href="/people/{{person._id}}" class="d-flex align-items-center text-dark text-hover-primary font-size-h5 font-weight-bold mr-3">{{person.firstName}} {{person.lastName}}</a>
                        <div class="d-flex flex-wrap my-2">
                          <i class="icon text-bright-blue fad fa-user-circle mr-2"></i>
                          <span class="text-muted font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">{{translationForEntityType person.type}}</span>
                          {{#if person.isCheckInAble}}
                            {{#if person.checkedIn}}
                              <i class="icon text-bright-blue fad fa-users mr-2"></i>
                              <span class="text-muted font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">{{person.findCheckedInGroup.name}}</span>
                            {{/if}}
                          {{/if}}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </td>
              <td style="width:30%; vertical-align: middle;" >
                <div class="align-items-center">
                  <span data-cy="im-here" class="text-primary bg-light-primary px-4 py-4 font-weight-bolder">I'm Here</span>
                </div>
              </td>
              <td style="width:30%; vertical-align: middle;">
                <div data-cy="check-in-button" class="btn {{getCheckButtonColor person}} font-weight-bolder min-w-175px site-check-in-out-btn" data-id="{{person._id}}">
                  <i class="fad-regular fad {{getCheckButtonIcon person}} fa-swap-opacity mr-2"></i>{{getCheckButtonText person}}
                </div>
              </td>
            </tr>
          {{/each}}
          {{#each person in arrivals}}
            <tr>
              <td style="width:40% vertical-align: middle;">
                <div class="d-flex">
                  <div class="flex-shrink-0 mr-7">
                    
                    {{#unless person.hasAvatar}}
                        <div class="d-flex avatar-circle align-items-center justify-content-center"
                             style="background-color:{{getAvatarBackground person.personInitials}}">
                            <span class="initials">{{person.personInitials}}</span>
                        </div>
                    {{else}}
                      <div class="people-list-user-img" style="background-image:url({{person.getAvatarUrl}})"></div>
                    {{/unless}}
                  </div>
                  <div class="flex-grow-1">
                    <div class="d-flex align-items-center justify-content-between flex-wrap mt-2">
                      <div class="mr-3">
                        <a data-cy="people-express-drive-up-item-{{person._id}}" href="/people/{{person._id}}" class="d-flex align-items-center text-dark text-hover-primary font-size-h5 font-weight-bold mr-3">{{person.firstName}} {{person.lastName}}</a>
                        <div class="d-flex flex-wrap my-2">
                          <i class="icon text-bright-blue fad fa-user-circle mr-2"></i>
                          <span class="text-muted font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">{{translationForEntityType person.type}}</span>
                          {{#if person.isCheckInAble}}
                            {{#if person.checkedIn}}
                              <i class="icon text-bright-blue fad fa-users mr-2"></i>
                              <span class="text-muted font-weight-bold mr-lg-8 mr-5 mb-lg-0 mb-2">{{person.findCheckedInGroup.name}}</span>
                            {{/if}}
                          {{/if}}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </td>
              <td style="width:30%; vertical-align: middle;" >
                <div data-cy="estimated-arrival-time" class="btn btn-light-info text-info font-weight-bold" style="pointer-events: none;">
                  {{getEstimatedTime person}}
                </div>
              </td>
              <td style="width:30%; vertical-align: middle;">
                <div data-cy="check-in-button" class="btn {{getCheckButtonColor person}} font-weight-bolder min-w-175px site-check-in-out-btn" data-id="{{person._id}}">
                  <i class="fad-regular fad {{getCheckButtonIcon person}} fa-swap-opacity mr-2"></i>{{getCheckButtonText person}}
                </div>
              </td>
            </tr>
          {{/each}}
        </tbody>
      </table>
    </div>
    {{/if}}
  </div>
</template>
