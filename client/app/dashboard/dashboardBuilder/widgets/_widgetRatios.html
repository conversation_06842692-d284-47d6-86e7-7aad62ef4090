<template name="widgetRatios">
	<div class="card card-custom gutter-b">
		<!--begin::Header-->
		<div class="card-header align-items-center border-0 mt-4">
			<h3 class="card-title align-items-start flex-column">
				<span data-cy="check-in-ratios" class="font-weight-bolder text-dark">Check-in Ratios</span>
				<!--<span class="text-muted mt-3 font-weight-bold font-size-sm"></span>-->
			</h3>
		</div>
		<!--end::Header-->
		<!--begin::Body-->
		<div class="card-body pt-4">
			{{#if checkLoading}}
				<div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
					{{>loading fontSize="30px"}}
				</div>
			{{else}}
				{{#if ratioGroups}}
					<div class="ratio-table">
						{{#each ratioGroups}}
							<div class="ratio-group-row">
								<div style="display:flex;flex-direction:column;padding-bottom:8px;margin-bottom:8px;">
									<div style="display:flex;flex-direction:row">
										<span data-cy="group-name-checkin-ratio" style="font-size:18px;cursor:pointer;" class="text-primary group-roster-link" data-groupid={{groupId}}>{{name}}</span>
										{{#if hasCustomization "report/classList/enabled"}}
											<div class="ratio-label-container">
												<span data-cy="ratio-level" class="ratio-label" style="background-color:{{getColorFromCompliance inCompliance}}">1:{{ratio}}</span>
											</div>
										{{/if}}
										<span ></span>
									</div>
									<div style="display:flex;flex-direction:row;">
										<div class="ratio-group-ratio">
											<span data-cy="children-count" class="ratio-number">{{checkinCount}}</span> {{ getEntityTypePeople }}<hr/><span data-cy="staff-count" class="ratio-number">{{staffCount}}</span> staff
										</div>
										<div class="ratio-group-bars" >
											<div class="ratio-bar-bg">
												<div class="ratio-bar-people" style="width:{{checkinLength}}%"></div>
												<div class="ratio-bar-capacity" style="width:{{capacityLength}}%"></div>
											</div>
											<div class="ratio-bar-bg">
												<div class="ratio-bar-staff" style="width:{{staffLength}}%"></div>
											</div>
										</div>
										<div class="ratio-group-extra-info">
											{{#if childrenAbsentCount absenceCount}}
												<span data-cy="absence-count" class="ratio-number">{{absenceCount}}</span> absent<hr/>
											{{else}}
												<a class="group-roster-link" data-groupid={{groupId}} style="cursor: pointer;">
													<span data-cy="absence-count" class="ratio-number" >{{absenceCount}}</span> absent<hr/>
												</a>
											{{/if}}
											<span data-cy="remaining-count" class="ratio-number">{{remainingCount}}</span> remaining
										</div>
									</div>
									{{#if noScheduleMessage}}
									<div style="display:flex;flex-direction:row">
										<span style="font-size:12px;font-weight:bolder;color:#ff0000">{{noScheduleMessage}}</span>
									</div>
									{{/if}}
								</div>
							</div>
						{{/each}}
						<!-- In the future we may want to show "No Group Assigned" -->
						<!-- Commenting this out so that it's quick to add this back in one day -->
						<!--
						<div class="ratio-group-row">
							<div style="display:flex;flex-direction:column;padding-bottom:8px;margin-bottom:8px;">
								<div style="display:flex;flex-direction:row">
									<span style="font-size:18px;">No Group Assigned</span>
								</div>
								<div style="display:flex;flex-direction:row;">
									<div class="ratio-group-ratio">
										<span class="ratio-number">{{noGroupAssigned.childrenCount}}</span> {{formatLowercase (_ "entityTypes.people")}}<hr/><span class="ratio-number">{{noGroupAssigned.staffCount}}</span> staff
									</div>
									<div class="ratio-group-bars" >
										<div class="ratio-bar-bg">
											<div class="ratio-bar-people" style="width:{{noGroupAssigned.checkinLength}}%"></div>
											<div class="ratio-bar-capacity" style="width:{{noGroupAssigned.capacityLength}}%"></div>
										</div>
										<div class="ratio-bar-bg">
											<div class="ratio-bar-staff" style="width:{{noGroupAssigned.staffLength}}%"></div>
										</div>
									</div>
									<div class="ratio-group-extra-info">
										<span class="ratio-number">{{noGroupAssigned.absenceCount}}</span> absent<hr/>
										<span class="ratio-number">{{noGroupAssigned.remainingCount}}</span> remaining
									</div>
								</div>
							</div>
						</div>
						-->
					</div>
				{{else}}
					<div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
						<div class="text-center">
							<div class="mb-2"><i class="fa fa-info-circle fa-2x text-muted"></i></div>
							<p class="text-muted">No check-in data available</p>
						</div>
					</div>
				{{/if}}
			{{/if}}
		</div>
	</div>
</template>
