<template name="dashboardFamily">
  <div class="d-flex flex-column justify-content-center">
    {{#if showBlock "billing"}}
      <div class="d-flex flex-column flex-grow-1 container mb-8">
        <div class="card card-custom">
          <div class="card-header flex-wrap border-0 pt-6 pb-0">
            <div class="card-title">
              <h3 class="card-label text-primary">Billing
              <span class="text-muted pt-2 font-size-sm d-block">Current and Past Due Amount</span></h3>
            </div>        
          </div>
          <div class="card-body">
            <div class="d-flex flex-column justify-content-center align-items-center">
              {{#if currentPerson.billingStatus.hasAmountDue}}
                <span class="text-primary font-weight-bolder font-size-h1">{{formatCurrency currentPerson.billingStatus.currentAmountDue}}</span>
                {{#if currentPerson.billingStatus.showPastDueAmount}} {{currentPerson.billingStatus.pastDueAmount}} {{/if}}
                {{currentPerson.billingStatus.dueStatus}}
                <a href="/people/{{currentPerson._id}}#transactions" class="btn btn-primary font-weight-bolder" style="margin-top:10px">View and Pay Now</a>
              {{else}}
                <p>It looks like your account is current.  Thank you!</p>
              {{/if}}
            </div>
          </div>
        </div>
      </div>
    {{/if}}
    {{#each children}}
    {{#if showDocs this}}
    <div data-cy="child-document-item" class="d-flex flex-column flex-grow-1 container mb-8">
      <div class="card card-custom">
        <div class="card-header flex-wrap border-0 pt-6 pb-0">
          <div class="card-title">
            <h3 class="card-label text-primary">Pending documents
              <span class="text-muted pt-2 font-size-sm d-block">Current documents available for your child {{fullName this.firstName this.lastName}}</span></h3>
          </div>
        </div>
        <div class="card-body d-flex flex-center align-items-center">
          <div class="w-900px text-center">
            <h2 data-cy="child-name-item" class="mb-10 text-primary">{{fullName this.firstName this.lastName}}</h2>
            {{> _documents child=this}}
          </div>
        </div>
      </div>
    </div>
    {{/if}}
    {{/each}}
    {{#if hasCustomization "wmg/enabled"}}
      <div class="d-flex flex-column flex-grow-1 container mb-8">
        <div class="card card-custom">
          <div class="card-body">
            <div class="d-flex flex-row justify-content-center align-items-center">
              <a href="#" class="btn btn-primary font-weight-bolder px-8 py-8 wmgButton">{{getWmgLabel}}</a>
            </div>
          </div>
        </div>
      </div>
    {{/if}}
    {{> momentList }}
  </div>
</template>
