<template name="dashboardSite">
	<div class="container-fluid" id="dashboard">
		<div class="d-flex flex-row align-items-center justify-content-between">
			<div class="col-md-6">
				{{#if locationName}}
					<h1 data-cy="organization-name" class="mr-6">{{locationName}}</h1>
				{{else}}
					{{>loading fontSize="50px"}}
				{{/if}}
			</div>
			<div class="col-md-6">
				{{> widgetCheckinsStat}}
			</div>
		</div>
		<div class="d-flex flex-row mt-12">
			<div class="col-md-6">
				{{#if hasExpressDriveUp}}
					{{ > widgetExpressDriveUp }}
				{{/if}}
				{{#if hasPastdueAcct}}
				<div data-cy="past-due-account" class="card card-custom gutter-b">
					{{> pastDueAccounts pastDueAccountsData}}
				</div>
				{{/if}}
				{{ > widgetActivity}}
				{{ > widgetActivations  activationsData}}
				{{ > widgetDocumentsDue widgetData=documentsOutstandingData isLoading=documentsOutstandingIsloadingFlag}}
				{{ > widgetMediaRequirement widgetData=mediaRequirementsData isLoading=mediaRequirementsIsloadingFlag}}
			</div>
			<div class="col-md-6">
				{{ > widgetRatios isLoading=checkInRatiosIsloadingFlag}}
				{{#if showRoomEnrollments}}
					{{ > widgetScheduleEnrollments widgetData=weeksScheduleRegistrationFlowData isLoading=weeksScheduleRegistrationFlowIsloadingFlag}}
				{{else}}
					{{ > widgetEnrollments widgetData=weeksScheduleData isLoading=weeksScheduleIsloadingFlag}}
				{{/if}}
			</div>
		</div>
	</div>
</template>
