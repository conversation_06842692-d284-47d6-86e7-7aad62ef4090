import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { CheckGetSwitchableSites } from '../../api/v2/services/checkSwitchableSites.js';

import { Orgs } from '../../lib/collections/orgs.js';

import '../../client/layout/application/application.html';
import '../../client/layout/application/partials/_headermobile.js';
import '../../client/layout/application/partials/_aside.js';
import '../../client/layout/application/partials/_header.js';
import '../../client/layout/application/partials/_quickpaneluser.js';
import { setupRouteTracking } from '../lib/performanceMonitoringSetup';

import { KTApp, KTUtil, KTLayoutQuickPanel, 
	KTLayoutQuickUser, KTLayoutHeader, KTLayoutHeaderMenu,
	KTLayoutHeaderTopbar, KTLayoutBrand, KTLayoutAside, 
	KTLayoutAsideToggle, KTLayoutAsideMenu
} from './bundles/KTlibs.js';
import '../layout/_offlineToast.js';
import fs from 'fs';

if (Meteor.isDevelopment) {
	window.FlowRouter = FlowRouter;
  }

Template.application.onRendered(function() {
  KTApp.init();
  KTUtil.init();
  setupRouteTracking();
  KTLayoutQuickPanel.init('kt_quick_panel');
  KTLayoutQuickUser.init('kt_quick_user');
  // KTLayoutAside.init('kt_aside');
  // KTLayoutHeader.init('kt_header', 'kt_header_mobile');
  // KTLayoutHeaderMenu.init('kt_header_menu', 'kt_header_menu_wrapper');
  // KTLayoutAsideMenu.init('kt_aside_menu');

  KTLayoutHeader.init('kt_header', 'kt_header_mobile');

  // Init Header Menu
  KTLayoutHeaderMenu.init('kt_header_menu', 'kt_header_menu_wrapper');

  // Init Header Topbar For Mobile Mode
  KTLayoutHeaderTopbar.init('kt_header_mobile_topbar_toggle');

  // Init Brand Panel For Logo
  KTLayoutBrand.init('kt_brand');

  // Init Aside
  KTLayoutAside.init('kt_aside');

  // Init Aside Menu Toggle
  KTLayoutAsideToggle.init('kt_aside_toggle');

  // Init Aside Menu
  KTLayoutAsideMenu.init('kt_aside_menu');

  $.getScript("https://checkoutshopper-live-us.adyen.com/checkoutshopper/sdk/3.12.1/adyen.js");
  Tracker.autorun( async function() {

	if (Meteor.user()) {
		// console.log("setting up Intercom");
		Meteor.callAsync('getIntercomHash')
		.then((result)=>{
			setupIntercom(Meteor.user()._id, result);
		})
		.catch((error)=>{
			setupIntercom(Meteor.user()._id, undefined);
		});

		if (!Session.get('switchableSites')) {
			const person = Meteor.user()?.fetchPerson();
      const org = Orgs.current();

      const canGetSwitchableSites = CheckGetSwitchableSites.canGetSwitchableSites(person, org);

			if (canGetSwitchableSites) {
				Meteor.callAsync('getSwitchableSites')
				.then((result)=>{
					Session.set('switchableSites', result);
				});
			}
		}
	}
  });

});

handleTabHistory = function() {
	if (location.hash !== '') $('a[href="' + location.hash + '"]').tab('show');

	$('a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
		if(history.pushState) {
			 history.pushState(null, null, '#'+$(e.target).attr('href').substr(1));
		} else {
			 location.hash = '#'+$(e.target).attr('href').substr(1);
		}
	});
}

export const showModal = function (templateName, data, templateId) {
	if (!templateId) {
		templateId = "#" + templateName;
	}
	let parentNode = document.body;
	let view = Blaze.renderWithData(Template[templateName], data, parentNode);

	$(templateId).on('shown.bs.modal', function (event) {
		console.log("show view");
		$(templateId).find('[autofocus]').focus()
	});
	$(templateId).on('hidden.bs.modal', function (event) {
		console.log("remove view");
		Blaze.remove(view)
	});
	$(templateId).modal('show');
}

export const hideModal = function(templateId) {
  $(templateId).modal('hide');
}

exportFormattedCSV = function(rows, filename) {
 var tmpColDelim = String.fromCharCode(11) // vertical tab character
 ,tmpRowDelim = String.fromCharCode(0) // null character

 // actual delimiter characters for CSV format
 ,colDelim = '","'
 ,rowDelim = '"\r\n"';

 var csv = '"';
 csv += formatRows(rows) + '"';
 var csvFile;
 var downloadLink;

 // CSV file
 csvFile = new Blob([csv], {type: "text/csv"});

 // Download link
 downloadLink = document.createElement("a");

 // File name
 downloadLink.download = filename;

 // Create a link to the file
 downloadLink.href = window.URL.createObjectURL(csvFile);

 // Hide download link
 downloadLink.style.display = "none";

 // Add the link to DOM
 document.body.appendChild(downloadLink);

 // Click download link
 downloadLink.click();

function formatRows(rows){
	return rows.join(tmpRowDelim)
		.split(tmpRowDelim).join(rowDelim)
		.split(tmpColDelim).join(colDelim);
}
}

exportTableToCSV = function ($table, filename) {

	var $headers = $table.find('tr:has(th)')
		,$rows = $table.find('tr:has(td)')

		// Temporary delimiter characters unlikely to be typed by keyboard
		// This is to avoid accidentally splitting the actual contents
		,tmpColDelim = String.fromCharCode(11) // vertical tab character
		,tmpRowDelim = String.fromCharCode(0) // null character

		// actual delimiter characters for CSV format
		,colDelim = '","'
		,rowDelim = '"\r\n"';

		// Grab text from table into CSV formatted string
		var csv = '"';
		csv += formatRows($headers.map(grabRow));
		csv += rowDelim;
		csv += formatRows($rows.map(grabRow)) + '"';

		var csvFile;
		var downloadLink;

		// CSV file
		csvFile = new Blob([csv], {type: "text/csv"});

		// Download link
		downloadLink = document.createElement("a");

		// File name
		downloadLink.download = filename;

		// Create a link to the file
		downloadLink.href = window.URL.createObjectURL(csvFile);

		// Hide download link
		downloadLink.style.display = "none";

		// Add the link to DOM
		document.body.appendChild(downloadLink);

		// Click download link
		downloadLink.click();

	//------------------------------------------------------------
	// Helper Functions
	//------------------------------------------------------------
	// Format the output so it has the appropriate delimiters
	function formatRows(rows){
		return rows.get().join(tmpRowDelim)
			.split(tmpRowDelim).join(rowDelim)
			.split(tmpColDelim).join(colDelim);
	}
	// Grab and format a row from the table
	function grabRow(i,row){

		var $row = $(row);
		//for some reason $cols = $row.find('td') || $row.find('th') won't work...
		var $cols = $row.find('td');
		if(!$cols.length) $cols = $row.find('th');

		return $cols.map(grabCol)
					.get().join(tmpColDelim);
	}
	// Grab and format a column from the table
	function grabCol(j,col){
		var $col = $(col),
			$text = $col.text();
		if (col?.dataset?.exportValue) {
			return col.dataset.exportValue;
		}

		return $text.replace(/"/g, '""').replace(/\n/g, "; "); // escape double quotes
	}
}

exportTableToBlob = function ($table) {

	var $headers = $table.find('tr:has(th)')
		,$rows = $table.find('tr:has(td)')

		// Temporary delimiter characters unlikely to be typed by keyboard
		// This is to avoid accidentally splitting the actual contents
		,tmpColDelim = String.fromCharCode(11) // vertical tab character
		,tmpRowDelim = String.fromCharCode(0) // null character

		// actual delimiter characters for CSV format
		,colDelim = '","'
		,rowDelim = '"\r\n"';

		// Grab text from table into CSV formatted string
		var csv = '"';
		csv += formatRows($headers.map(grabRow));
		csv += rowDelim;
		csv += formatRows($rows.map(grabRow)) + '"';

		return csv;

	//------------------------------------------------------------
	// Helper Functions
	//------------------------------------------------------------
	// Format the output so it has the appropriate delimiters
	function formatRows(rows){
		return rows.get().join(tmpRowDelim)
			.split(tmpRowDelim).join(rowDelim)
			.split(tmpColDelim).join(colDelim);
	}
	// Grab and format a row from the table
	function grabRow(i,row){

		var $row = $(row);
		//for some reason $cols = $row.find('td') || $row.find('th') won't work...
		var $cols = $row.find('td');
		if(!$cols.length) $cols = $row.find('th');

		return $cols.map(grabCol)
					.get().join(tmpColDelim);
	}
	// Grab and format a column from the table
	function grabCol(j,col){
		var $col = $(col),
			$text = $col.text();

		return $text.replace('"', '""').replace(/\n/g,"; "); // escape double quotes

	}
}


Template.application.onCreated(function() {
  this.autorun(() => {
	// commenting temporary to check if does affect anything. 
	// If it does not affect anything then we will remove it permanently
    this.subscribe("userData");
    this.subscribe("theOrg");
	this.subscribe("theLoggedinPeople");
    // this.subscribe("thePeople");
    // this.subscribe("theGroups");
    // this.subscribe("theMomentDefinitions");
    // this.subscribe("thePeopleDirectory");
  });
});

Template.application.helpers({
  'showConnectionStatus': function() {
    if (Meteor.status().status === "offline" || Meteor.status().status === "failed") return true;
    if (Meteor.status().status === "waiting" && Meteor.status().retryCount >= 4) return true;
    return false;
  },
})
