<template name="reportMeals">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Meals Report
				<span class="text-muted pt-2 font-size-sm d-block">View overall meal consumption data.</span></h3>
			</div>
			<div class="card-toolbar">
				<div data-cy="export-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div data-cy="update-btn" class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="box">

				<div class="box-body">
				  <div class="row">
					<div class="col-lg-3">
						  <div class="form-group">
							<label>Start Date</label>
							<div class="input-group">
							
								<input data-cy="start-date-input" type="text" class="form-control pull-right" id="attendanceStartDate" value="{{formattedStartDate}}">
							</div>
						</div>
					</div>
					<div class="col-lg-3">
						  <div class="form-group">
							<label>End Date</label>
							<div class="input-group">
								
								<input data-cy="end-date-input" type="text" class="form-control pull-right" id="attendanceEndDate" value="{{formattedEndDate}}">
							</div>
						</div>
					</div>
					
				  </div>
				  
				</div><!-- /.box-header -->
				<div class="box-body no-padding">
				  <table class="table">
					<tbody><tr>
					  
					  <th>Date</th>
					  <th class="text-right">Breakfast</th>
					  <th class="text-right">Snack AM</th>
					  <th class="text-right">Lunch</th>
					  <th class="text-right">Snack PM</th>
					</tr>
					
					{{# each meals}}
					<tr>
					  <td data-cy="meal-date">{{date}}</td>
					  <td data-cy="breakfast-count" class="text-right">{{breakfast}}</td>
					  <td data-cy="snack-am-count" class="text-right">{{snackAm}}</td>
					  <td data-cy="lunch-count" class="text-right">{{lunch}}</td>
					  <td data-cy="snack-pm-count" class="text-right">{{snackPm}}</td>
					</tr>
					{{/each}}

				  </tbody></table>
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
