import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { CustomReportUtils } from "../../../lib/customReports/customReportUtils";
import { AVAILABLE_REPORTS } from "../../../lib/constants/reportConstants";
import './reportsCustomBuilderContainer.html';
import { hideModal, showModal } from '../main';
import './reportOrgsField';
import './customReports/customReportFilters';
import './customReports/reportCustomReportFlex';
import { Orgs } from '../../../lib/collections/orgs';

Template.reportsCustomBuilderContainer.onCreated(function () {
	const reportName = FlowRouter.getParam("reportName") || "";
	const savedReportId = FlowRouter.getParam("savedReportId") || "";
	const matchedReport = savedReportId ? CustomReportUtils.getReportObjectByIndex(reportName) : CustomReportUtils.getReportObject(reportName);
	this.reportId = matchedReport?._id;
	this.savedReportId = savedReportId;
	this.fields = new ReactiveVar([]);
	this.filters = new ReactiveVar([]);
	this.savedReport = new ReactiveVar(null);
	if (!reportName) {
		sessionStorage.setItem('flexReportOrgIds', '');
		sessionStorage.setItem('flexReportHash', '');
	}
	Meteor.callAsync('getOrgHierarchyForReports', Orgs.current()._id).then(result => {
		const orgLevelNames = CustomReportUtils.getOrgLevelNames(result);
		sessionStorage.setItem('flexReportOrgLevels', orgLevelNames.join(','));
	});
})

Template.reportsCustomBuilderContainer.onRendered(function () {
	if (this.reportId && !this.fields.length) {
		showModal('customReportFilters', { reportId: this.reportId, savedReportId: this.savedReportId, dataCallback: (fields, filters, savedReport) => {
			setData(this, fields, filters, savedReport);
		} }, '#customReportFilters');
	}
})

Template.reportsCustomBuilderContainer.helpers({
	availableReports() {
		const enabledReports = [...AVAILABLE_REPORTS];
		enabledReports.sort((a, b) => a.name.localeCompare(b.name));
		return enabledReports;
	},
	reportId() {
		return Template.instance().reportId;
	},
	fields() {
		return Template.instance().fields.get();
	},
	filters() {
		return Template.instance().filters.get();
	},
	savedReport() {
		return Template.instance().savedReport.get();
	},
	fieldsSelected() {
		return Template.instance().fields?.get()?.length;
	},
	orgsFieldOptions() {
		return {
			onChange: (values) => {
				sessionStorage.setItem('flexReportOrgIds', values.join(','));
			}
		}
	}
});

function setData(i, fields, filters, savedReport) {
	i.fields.set(fields);
	i.filters.set(filters);
	i.savedReport.set(savedReport);
	hideModal("#customReportFilters");
}