<template name="reportCaliforniaRoster">
  <div class="row">
    <div class="col-md-12">
      <div class="box">
        <div class="box-header">
          <h3 class="box-title">California Roster Report</h3>
        </div>
        <div class="box-body">
          <div class="row">
            <div class="col-lg-12">
              <div class="form-group">
                <label>The roster report shows  within 3 years of the report run date.</label>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-lg-4">
              <div class="form-group">
                <button type="button" class="btn btn-primary" id="update">Update</button>
                <button type="button" class="btn" id="btnExportCsv"><i class="fa fa-download"></i> Export CSV</button>
              </div>
            </div>
          </div>
        </div>
        {{#if ShowResults}}
        <div class="row">
          <div class="col-md-12">
            <h3 class="text-center">California Roster Report</h3>
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th colspan="5"><span class="text-muted">Facility Name:</span> {{legalFacilityName}}</th>
                  <th><span class="text-muted">Facility License Number:</span> {{facilityLicenseNumber}}</th>
                  <th><span class="text-muted">Date/Update:</span> {{getToDay}}</th>
                </tr>
              </thead>
            </table>
            <div class="table-responsive">
              <table class="table table-bordered table-striped" id="californiaRosterTable">
                <thead>
                  <tr>
                    <th>Child's Name</th>
                    <th>Birthday</th>
                    <th>Address</th>
                    <th>Parent/Guardian Name(s)</th>
                    <th>Daytime phone of Parent/Guardian</th>
                    <th>Physician name and phone</th>
                    <th>Date Enrolled</th>
                    <th>Date Left</th>
                  </tr>
                </thead>
                <tbody>
                  {{#if DataReport}} {{#each person in DataReport}}
                  <tr>
                    <td>{{person.lastName}}, {{person.firstName}}</td>
                    <td>{{parseDate person.birthday}}</td>
                    <td>{{person.address}}</td>
                    <td>
                      {{#each parent in person.parents}} {{getParents parent}}<br />
                      {{/each}}
                    </td>
                    <td>
                      {{#each parent in person.parents}} {{getParentsPhone parent}}<br />
                      {{/each}}
                    </td>
                    <td>
                      {{#if person.physicianName}} {{getPhysician person.physicianName}} {{getPhysicianNumber person.physicianPhone}}
                      {{/if}}
                    </td>
                    <td>{{parseDate person.enrollmentDate}}</td>
                    <td>{{parseDate person.withdrawDate}}</td>
                  </tr>
                  {{/each}} {{else}}
                  <tr>
                    <td colspan="8" class="text-center">No Records Found.</td>
                  </tr>
                  {{/if}}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        {{/if}}
      </div>
    </div>
  </div>
</template>
