import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { orgLanguageTranformationUtil } from '../../../lib/util/orgLanguageTranformationUtil';
const moment = require('moment-timezone');
import './reportEnrollmentStatusV2.html';
import { User } from '../../../lib/collections/users';
import './reportQueueTemplate';
import { reportQueuedSwal, upperFirst } from './queueReportsUtil';

Template.reportEnrollmentStatusV2.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar();
	this.dateRangeLabel = new ReactiveVar();
	this.showWithdrawnChildrenOnly = new ReactiveVar(false);
	this.showDetails = new ReactiveVar(false);
	this.queueMode = new ReactiveVar(false);
	this.savedMode = new ReactiveVar(false);
	this.savedHeader = new ReactiveVar();
	this.isUpdating = new ReactiveVar(false);
	const queueId = FlowRouter.getQueryParam('queueId');
	if (queueId) {
		Meteor.callAsync('retrieveQueuedReport', { queueId }).then((response) => {
			if (!response) {
				return;
			}
			this.savedMode.set(true);
			this.reportData.set(response.data);
			const reportArgs = response.args;
			const startDate = new moment(reportArgs.startDate).format("MM/DD/YYYY");
			const endDate = new moment(reportArgs.endDate).format("MM/DD/YYYY");
			this.dateRangeLabel.set(  startDate + " - " + endDate);
			this.showDetails.set(reportArgs.orgs.length <= 1 );
			let header;
			Meteor.callAsync('retrieveOrgNames', { orgs: reportArgs.orgs }).then((resp) => {
				header = 'Orgs: ' + resp;
			}).catch((err) => {
				header = 'Orgs: ';
			}).finally(() => {
				let personTypesLabel = 'None selected';
				if (reportArgs.personTypes) {
					personTypesLabel = reportArgs.personTypes.map(type => { return orgLanguageTranformationUtil.getEntityType(type)}).join(', ');
				}
				header += '<br>Person Types: ' + personTypesLabel;
				let includeLabel = 'All';
				if (reportArgs.includeOnly === 'withdrawn') {
					includeLabel = 'Withdrawn';
				} else if (reportArgs.includeOnly) {
					includeLabel = 'New' + upperFirst(reportArgs.includeOnly);
				}
				header += '<br>Include Only: ' + includeLabel;
				this.savedHeader.set(header);
			});
		}).catch((err) => {
			return;
		});
	}
}

Template.reportEnrollmentStatusV2.rendered = function() {
	$('#reportStartDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
	$('#reportEndDate').datepicker({autoclose:true}).datepicker("setDate", new Date());
	$("#reportPersonType").selectpicker();
	$("#reportIncludeOnly").selectpicker();
}

Template.reportEnrollmentStatusV2.events({
	"click #chkQueue": function(e, i) {
		i.queueMode.set(document.getElementById('chkQueue').checked);
	},
	"click #btnUpdate": async function(e, i) {
		$("#btnUpdate").prop("disabled", true);
		i.isUpdating.set(true);

		const startDate = $("#reportStartDate").val(),
			endDate = $("#reportEndDate").val();
			
		if (startDate == "" || endDate == "") return;		
		const startDateNum =  new moment(startDate, "MM/DD/YYYY").valueOf(),
			endDateNum = new moment(endDate, "MM/DD/YYYY").valueOf();
		
		var orgs = $("#announcementOrgs").val() || [];
		var personTypes = $("#reportPersonType").val() || [];
		var includeOnly = $("#reportIncludeOnly").val();

		i.showWithdrawnChildrenOnly.set( includeOnly == "withdrawn");
		
		if (includeOnly) {
			personTypes = ["person"];
		}
		if (i.queueMode.get()) {
			i.isUpdating.set(false);
			$("#btnUpdate").prop('disabled', false);
			await Meteor.callAsync('queueReportDefer', {
				reportFunction: 'enrollmentStatusV2',
				reportName: 'Enrollment Status',
				reportArgs: { startDate: startDateNum, endDate: endDateNum, orgs, personTypes, includeOnly },
				userId: Meteor.userId(),
				reportRoute: 'reports/reportEnrollmentStatusV2'
			});
			await Meteor.callAsync('trackClientActivity', {
				label: "report-queued",
				reportType: "enrollmentStatusV2"
			});
			reportQueuedSwal();
			return;
		}

		Meteor.callAsync("enrollmentStatusV2", { startDate: startDateNum, endDate: endDateNum, orgs, personTypes, includeOnly }).then((response) => {
			i.isUpdating.set(false);
			$("#btnUpdate").prop('disabled', false);
			console.log("response", response)
			i.reportData.set(response);		
			i.dateRangeLabel.set(  startDate + " - " + endDate);
			i.showDetails.set( orgs.length <= 1 );
		}).catch((err) => {
			i.isUpdating.set(false);
			$("#btnUpdate").prop('disabled', false);
			i.reportData.set(undefined);		
			i.dateRangeLabel.set(  startDate + " - " + endDate);
			i.showDetails.set( orgs.length <= 1 );
		});
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "enrollmentStatusV2"
		});
	},
	"click #btnPrint": async function (e,i) {
		var outputFile = 'export.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#dvData table'), outputFile]);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "enrollmentStatusV2"
		});
	},
	"change #withdrawnChildrenOnly": (e, instance) => {
		instance.showWithdrawnChildrenOnly.set($(e.currentTarget).prop("checked"));
	},
	"click #btnShowDetail"(e, instance) {
		instance.showDetails.set(true);
	}
});

Template.reportEnrollmentStatusV2.helpers({
	showOrgSelection() {
		const user = Meteor.user();
		const userPerson = (user) ? user.fetchPerson() : {};
		return userPerson.isMasterAdmin();
	},
	reportRows() {
		const data = Template.instance().reportData.get();
		return data && data.rows;
	},
	dateRangeLabel() {
		return Template.instance().dateRangeLabel.get();
	},
	updateLabel() {
		return Template.instance().isUpdating.get() ? 'Updating' : (Template.instance().queueMode.get() ? 'Queue' : 'Update');
	},
	isQueueMode() {
		return Template.instance().queueMode.get();
	},
	isSavedMode() {
		return Template.instance().savedMode.get();
	},
	savedHeader() {
		return Template.instance().savedHeader.get();
	},
	showWithdrawnChildrenOnly() {
		return Template.instance().showWithdrawnChildrenOnly.get();
	},
	withdrawnFteSummary() {
		const data = Template.instance().reportData.get();
		return data && data.withdrawnFteSummary;
	},
	withdrawalBreakdownLabels() {
		const data = Template.instance().reportData.get(),
			breakdown = _.map(data?.breakdowns?.All?.withdrawalReasons, (v, k) => k);
		return breakdown;
	},
	breakdowns() {
		const data = Template.instance().reportData.get(),
			mapped = _.map( data.breakdowns, (breakdown, orgName) => ({
				locationName: orgName,
				...breakdown
			}));
		return mapped;
	},
	amountFor(breakdownLabel, breakdown) {
		return breakdown?.withdrawalReasons?.[breakdownLabel] || 0;
	},
	showDetails() {
		return Template.instance().showDetails.get();
	},
	showTuition() {
		return !(Template.instance().showWithdrawnChildrenOnly.get());
	},
	'getEntityTypePerson': function () {
		return orgLanguageTranformationUtil.getEntityType('person');
	},
    'getEntityTypeFamily': function () {
		return orgLanguageTranformationUtil.getEntityType('family');
	},
    'getEntityTypeStaff': function () {
		return orgLanguageTranformationUtil.getEntityType('staff');
	},
    'getEntityTypeAdmin': function () {
		return orgLanguageTranformationUtil.getEntityType('admin');
	},
});
