<template name="reportCurriculum">

	<div class="card card-custom" data-cy="view-activities-report">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Activities Report
				<span class="text-muted pt-2 font-size-sm d-block">View activity history and filter by type, tags, and date.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExportCsv" data-cy="export-btn">
					<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
				</div>
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="update" data-cy="update-btn">
					Update
				</div>
				<!--end::Button-->
			</div>
		</div>
		<div class="card-body">
			<div class="box">
				<div class="box-body">
				  <div class="row">
					<div class="col-lg-3">
						  <div class="form-group">
							<label>Start Date</label>
							<div class="input-group">
								
								<input type="text" class="form-control pull-right" id="scheduledStartDate" data-cy="start-date-input">
							</div>
						</div>
					</div>
					<div class="col-lg-3">
						  <div class="form-group">
							<label>End Date</label>
							<div class="input-group">
								
								<input type="text" class="form-control pull-right" id="scheduledEndDate" data-cy="end-date-input">
							</div>
						</div>
										</div>
										<div class="col-lg-6">
											<div class="form-group">
												<label>Tag(s)</label>
												<br/>
												<select class="form-control" multiple name="curriculumTypes" id='inputCurriculumTypes' data-cy="select-moment-type">
													{{#each availableCurriculumTypes}}
													<option value="{{value}}">{{name}}</option>
													{{/each}}
												</select>
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-sm-12">
						  <div class="form-group">
							<label>Standards</label>
							<br/>
												<select class="form-control" multiple name="curriculumStandards" id="inputCurriculumStandards" data-cy="curriculum-standards">
													{{#each availableCurriculumStandards}}
													<option label="{{source}}|{{standardId}}" value="{{source}}|{{standardId}}">{{source}}: {{benchmark}}</option>
													{{/each}}
												</select>
											
						</div>
					</div>
									</div>
									<div class="row">
										<div class="col-lg-3">
											<div class="form-group">
												<label>Group</label>
												<select class="form-control" id="selectedGroup" data-cy="select-moment-group">
																		<option value=""></option>
																		{{#each groups}}
																			<option value="{{_id}}">{{name}}</option>
																		{{/each}}
																	</select>
											</div>
															</div>
															<div class="col-lg-6">
																	<div class="form-group">
																		<label>Search Text</label>
													
														<input type="text" class="form-control" id="searchText">
													
												</div>
											</div>
									</div>
					
				</div><!-- /.box-header -->
				<div class="box-body no-padding">
				  <table class="table" data-cy="activities-report-table">
					<tbody><tr>
					  <th>Date</th>
					  <th>Headline</th>
					  <th>Tag(s)</th>
					  <th>Standards</th>
					  <th>Groups</th>
					</tr>
					
					{{# each curriculums}}
					<tr>
					  <td>{{formattedScheduledDate}}</td>
					  <td>{{headline}}</td>
					  <td>{{typesFormatted}}</td>
					  <td>{{standardsFormatted}}</td>
					  <td>{{groupsFormatted}}</td>
					</tr>
					{{/each}}

				  </tbody></table>
				</div><!-- /.box-body -->
			  </div>

		</div>
	</div>
</template>
