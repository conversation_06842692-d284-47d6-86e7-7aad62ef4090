<template name="reportSignInGrid">

	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Attendance Grid
				<span class="text-muted pt-2 font-size-sm d-block">Generate sign-in/sign-out grid for groups.</span></h3>
			</div>
			{{#unless shouldHideHeaderForPrint}}
				<div class="card-toolbar">
					<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnPrint" data-cy="print-btn">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Print View
					</div>
					<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="update-btn">
						Update
					</div>
				</div>
			{{/unless}}
		</div>
		<div class="card-body">
			<div class="box">

				<div class="box-body attendance-grid">
					  
					<div class="row" id="dvData">
						<div class="col-12">
							{{#if fullLayout}}
							<div class="row">
								<div class="col-sm-4">
									<label>Group:</label><br/>
									<select id="selectedGroup" class="form-control" data-cy="select-group">
										<option value="">All</option>
										{{#each group in groups}}
										<option value="{{group._id}}">{{group.name}}</option>
										{{/each}}
									</select>
								</div>
								<div class="col-sm-4 pt-7 pl-7">
									<div class="form-check">
										<input type="checkbox" name="showScheduleAndDateOfBirth" class="form-check-input" data-cy="include-dob-checkbox">
										<label class="form-check-label mr-4">Show Schedule and Date of Birth</label>
									</div>
									<div class="form-check">
										<input type="checkbox" name="includeNextWeekInQuery" class="form-check-input" data-cy="include-following-week-checkbox">
										<label class="form-check-label">Include following week schedules</label>
									</div>
								</div>
							</div>
							<br/>
							{{/if}}

							{{#if reportRows}}
							<div id="reportData" data-cy="check-in-out-ratio-table">
								{{#if reportTitle}}<h3 style="padding-left:5px">{{reportTitle}}</h3>{{/if}}
								<table class="table">
									<tbody>
									<tr>
										<th rowspan="2" style="vertical-align: bottom" data-cy="person-name">Name</th>
										{{#if showScheduleAndDateOfBirth}}
											<th rowspan="2" style="vertical-align: bottom" data-cy="date-of-birth">Date of Birth</th>
											<th rowspan="2" style="vertical-align: bottom; white-space: nowrap; width: max-content;" data-cy="moment-type">Schedule</th>
										{{/if}}
										{{#each day in days}}
										<th colspan="2">{{day}}</th>
										{{/each}}
									</tr>
									<tr>
										{{#each day in days}}
										<th>Time In</th>
										<th>Time Out</th>
										{{/each}}
									</tr>
									{{#each person in reportRows.people}}
									<tr>
										<td style="text-align: left">{{person.lastName}}, {{person.firstName}}</td>
										{{#if showScheduleAndDateOfBirth}}
											<td>{{formatDate (getBirthday person) 'MM/DD/YYYY'}}</td>
											<td>
												<div class="d-flex flex-wrap align-content-start justify-content-between">
													<ul class="list-unstyled text-left mb-0">
														{{#each reservation in userReservations person._id}}
														<li class="{{getLiClass @index}}">
															<p class="font-weight-bolder mb-0">{{reservation.scheduleTypeName}}</p>
															<span class="day-list">{{formatDays reservation.recurringDays}}</span>
														</li>
														{{/each}}
													</ul>
												</div>
											</td>
										{{/if}}
										{{#each day in days}}
										<td></td>
										<td></td>
										{{/each}}
									</tr>
									{{/each}}
									</tbody>
								</table>
							</div>
							{{/if}}
						</div>
					</div>
							
				</div>
			</div>
		</div>
	</div>


</template>
