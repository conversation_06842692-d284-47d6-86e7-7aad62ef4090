import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {getPeopleById, getPeopleData} from "../../services/peopleMeteorService";
import { People } from '../../../lib/collections/people';
import './reportMoments.html';
import { Groups } from '../../../lib/collections/groups';
import { Orgs } from '../../../lib/collections/orgs';

Template.reportMoments.created = function() {
	var instance = this;
	this.reportStartDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.reportEndDate = new ReactiveVar(moment().format("MM/DD/YYYY"));
	this.momentsReportAggregates = new ReactiveVar();
	this.person = new ReactiveVar("");
	this.momentType = new ReactiveVar("");
	this.group = new ReactiveVar("");
	this.momentGroup = new ReactiveVar("");
	this.includeDetail = new ReactiveVar(false);
	this.personList = new ReactiveVar([]);
	this.resPeopleList = new ReactiveVar([]);

	getPeopleData({"type":"person", inActive:{$ne:true}}, {sort: {lastName:1, firstName:1}, fields:{_id: 1, lastName:1, firstName:1, inActive: 1}}).then(res => {
		this.personList.set(res)
	}).catch(err => {
		console.log(err);
	});
};

Template.reportMoments.rendered = function() {
	$('#momentsStartDate').datepicker({autoclose:true});
	$('#momentsEndDate').datepicker({autoclose:true});

	var self = Template.instance();

}

Template.reportMoments.events({
	"click #btnUpdate": async function(event) {
		event.preventDefault();
		$("btnUpdate").text("Updating").prop('disabled', true);

		var instance = Template.instance();
		instance.reportStartDate.set($("#momentsStartDate").val());
		instance.reportEndDate.set($("#momentsEndDate").val());
		instance.person.set($("#filterPerson").val());
		instance.momentType.set($("#filterType").val());
		instance.group.set($("#filterGroup").val());
		instance.momentGroup.set($("#filterMomentGroup").val());
		instance.includeDetail.set( $("#chkIncludeDetail").prop("checked") );

		fetchMomentsReportAggregates(Template.instance());
		
		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "moments"
		});
	},
	"click #btnExportCsv": async function() {
		var outputFile = 'export.csv'
			
		// CSV
		exportTableToCSV.apply(this, [$('#dvData > table'), outputFile]);
		
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "moments"
		});
	},
	"click .linkViewMomentDetail": function(e) {
		var momentId = $(e.target).data("id");
		var m = _.find(Template.instance().momentsReportAggregates.get(), function(m) { return m._id == momentId; });
		console.log("Found", m);
		if (m) {
			
			mpSwal.fire({
				title: "Moment Data",
				html: "<b>Created Time:</b> " + moment(m.createdAt).format("M/D/YY h:mm a") +
					"<br/><b>Moment Time:</b> " + moment(m.sortStamp).format("M/D/YY h:mm a") + 
					"<br/><b>Description:</b> " + 
					"<span style='white-space: pre-wrap'>" + (m.formattedDescriptionString || "") + "<br/>" + (m.comment || "") + 
					(m.momentType == 'nameToFace' ? "<br/>" + m.nameToFaceDescription : "") + "</span>"
			});
		}
	},
	"click .linkGoToMoment": function(e) {
		e.preventDefault();
		var momentId = $(e.target).data("id");
		console.log(momentId);
		FlowRouter.go("/moments/" + momentId);
	}
});

Template.reportMoments.helpers({
	"formattedStartDate": function(){
		return Template.instance().reportStartDate.get();
	},
	"formattedEndDate": function(){
		return Template.instance().reportEndDate.get();
	},
	"formatTime": function(m) {
		if (m) 
			return m.time ? m.time : moment(m.createdAt).format("hh:mm a");
	},
	"moments": function() {
		return Template.instance().momentsReportAggregates.get();
	},
	"people": function() {
		return Template.instance().personList.get();
	},
	"createdByPersonName": function() {
		const resPeopleList = Template.instance().resPeopleList.get();
		
		var person = resPeopleList[resPeopleList.findIndex(p => p._id === this.createdByPersonId)];
		// var person = People.findOne({ _id: this.createdByPersonId });
		return (person) ? `${person.firstName} ${person.lastName}` : "";
	},
	"groups": function(onlyDesignated) {
		const allGroups =  Groups.find({ }, {sort: {name: 1}}).fetch();
		if (onlyDesignated && allGroups.filter(g => g.includeClassList).length > 0)
			return allGroups.filter(g => g.includeClassList);
		else 
			return allGroups;
	},
	"availableMomentTypes": function() {
		
		let types = Orgs.current().availableMomentTypes();
		types.push({momentType: 'nameToFace', prettyName: "Name To Face"});
		types.push({momentType: 'checkin', prettyName: "Check In" })
		types.push({momentType: 'move', prettyName: 'Move'})
		
		return _.sortBy(types, "prettyName");
	},
	"showDetail": function() {
		return Template.instance().includeDetail.get();
	},
	"showGroup": function() {
		return !Template.instance().momentGroup.get();
	},
	"groupName": function() {
		let groupId;
		if (this.momentType === "nameToFace") {
			groupId = this.nameToFaceGroupId;
		} else if (this.momentType === "move") {
			groupId = this.checkOutGroupId;
		} else {
			groupId = this.createdByPersonGroupId;
		}
		return Groups.findOne(groupId)?.name;
	},
	"detailString": function() {
		const m = this;
		if (m.momentType == "checkin") {
			let desc = "";
			_.map(this.checkInHealthChecks, function(value, key) {
				desc += `${key}: ${value}, `;
			});
			desc += "\n";
			_.each(Orgs.current() && Orgs.current().pinCodeCheckinFields(), (field) => {
				if (m[field.dataId]) desc += `${field.label} ${m[field.dataId]}\n`;
			})
			if (m.checkInHealthCheckCovid19Symptoms) {
				desc += "COVID-19 Health Check:\n";
				desc += `Symptoms: ${m.checkInHealthCheckCovid19Symptoms}\n`;
				desc += `Contact: ${m.checkInHealthCheckCovid19ProlongedContact}\n`;
				desc += `Temperature over 100.4: ${m.checkInHealthCheckCovid19TemperatureAboveLimit}\n`;
				desc += `Temperature: ${m.checkInHealthCheckCovid19Temperature}\n`;	
			}
			return desc;
		}
		
		return ("" + m.formattedDescriptionString?.replace(/\<br\/\>/g, "\n") + "\n" + 
					(m.momentType == 'nameToFace' ? "\n" + m.nameToFaceDescription + (m.nameToFaceCompletedByPersonName ? "\nCompleted by: " + m.nameToFaceCompletedByPersonName + "\n" : "") : "") +
					( (m.momentType == 'sleep' && m.sleepChecks) ? 
						"\nSleep Checks:\n" + m.sleepChecks.map( (sc) => { 
							// const createdByPerson = People.findOne(sc.createdBy);
							const resPeopleList = Template.instance().resPeopleList.get();
							const createdByPerson = resPeopleList[resPeopleList.findIndex(p => p._id === sc.createdBy)];
							const scMoment = new moment(sc.createdAt); 
							const sleepPosition = (sc.sleepPosition) ? `Sleep Position: ${sc.sleepPosition}` : "";
							const distressedCheck = (sc.distressedSleep) ? `\nSigns of Distress: ${sc.distressedSleep}` : "";
							return `${createdByPerson.firstName} ${createdByPerson.lastName} at ${scMoment.format("hh:mm a")} ${sleepPosition}${distressedCheck}`;
						}).join("\n") : "") 
				).trim();
	},
	"highlightInactives": function(s) {
		return s?.replace("(i)", "<span style='color:#ff0000'>(i)</span>");
	}
});

var fetchMomentsReportAggregates = function (instance) {
	Meteor.callAsync("momentsReportAggregates", {
		startDate: instance.reportStartDate.get(), 
		endDate: instance.reportEndDate.get(),
		momentType: instance.momentType.get(),
		person: instance.person.get(),
		group: instance.group.get(),
		momentGroup: instance.momentGroup.get()
	}).then((response) => {
		$("btnUpdate").text("Update").prop('disabled', false);
		const momentPeopleIds = response.flatMap((r)=>{
			return [r.createdByPersonId, r.sleepChecks?.map((sc)=> sc.createdBy)]
		})
		const peopleIds = [... new Set(momentPeopleIds.flat())]

		getPeopleData({"_id":{"$in": peopleIds }}, {sort: {lastName:1, firstName:1}, fields: {_id: 1, lastName:1, firstName:1, inActive: 1}}).then(res => {
			instance.momentsReportAggregates.set(response);
			instance.resPeopleList.set(res)
		}).catch(err => {
			console.log(err);
		});
	}).catch((error) => {
		$("btnUpdate").text("Update").prop('disabled', false);
		alert(error);
	});
};
