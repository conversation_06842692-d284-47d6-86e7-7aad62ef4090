import './reportWaitList.html';
import _ from '../../../lib/util/underscore';

Template.reportWaitList.created = function() {
	this.waitListResults = new ReactiveVar({groups: [], totals: []});
};

Template.reportWaitList.rendered = function() {
	const slider = document.querySelector('#dvData');
	let isDown = false;
	let startX;
	let scrollLeft;
	
	slider.addEventListener('mousedown', (e) => {
	  isDown = true;
	  slider.classList.add('active');
	  startX = e.pageX - slider.offsetLeft;
	  scrollLeft = slider.scrollLeft;
	  slider.style.cursor = 'grabbing';
      slider.style.userSelect = 'none';
	});
	slider.addEventListener('mouseleave', () => {
	  isDown = false;
	  slider.classList.remove('active');
	});
	slider.addEventListener('mouseup', () => {
	  isDown = false;
	  slider.classList.remove('active');
	  slider.style.cursor = 'grab';
      slider.style.removeProperty('user-select');
	});
	slider.addEventListener('mousemove', (e) => {
	  if(!isDown) return;
	  e.preventDefault();
	  const x = e.pageX - slider.offsetLeft;
	  const walk = (x - startX) * 3; //scroll-fast
	  slider.scrollLeft = scrollLeft - walk;
	});
};

Template.reportWaitList.events({
	"click #btnUpdate": async function(event) {
		event.preventDefault();
		$("#btnUpdate").text("Updating").prop('disabled', true);

		fetchClassListReportAggregates(Template.instance());

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "classList"
		});
	},
	"click #btnExportCsv": async function(e, i) {
   		var tmpColDelim = String.fromCharCode(11) // vertical tab character
			,tmpRowDelim = String.fromCharCode(0) // null character
			// actual delimiter characters for CSV format
			,colDelim = '","'
			,rowDelim = '"\r\n"';

		function grabRow(i,row){
			
			var $row = $(row);
			//for some reason $cols = $row.find('td') || $row.find('th') won't work...
			var $cols = $row.find('td'); 
			if(!$cols.length) $cols = $row.find('th');  
		
			return $cols.map(grabCol)
					.get().join(tmpColDelim);
		}
		// Grab and format a column from the table 
		function grabCol(j,col){
			var $col = $(col),
				$text = $col.text();
		
			return $text.replace('"', '""').replace(/\n/g,"; "); // escape double quotes
		
		}

		var outputFile = 'export.csv'
			
		
		
		const groups = i.waitListResults.get().groups;
		const combinedArr = [];

		_.each(["group-header", "group-active", "group-preselected", "group-totals", "group-unassigned"], currentGroup => {
			if (currentGroup == "group-totals") {
				combinedArr.push($("#group-totals").find('tr:eq(0)').map(grabRow).get());
				combinedArr.push($("#group-totals").find('tr:eq(1)').map(grabRow).get());
			} else {
				const rowsArr = [];
				let length =0;

				if (currentGroup == "group-unassigned") {
					const $table = $("#group-unassigned");
					const rowLength = $table.find('tr').length;
					if (rowLength > length) length = rowLength;
					rowsArr.push($table.find('tr').map(grabRow).get());
					combinedArr.push(tmpColDelim);
					combinedArr.push("Unassigned");
				} else {
					for (const g of groups) {
						const $table = $(`#${currentGroup}-${g._id}`);
						const rowLength = $table.find('tr').length;
						if (rowLength > length) length = rowLength;
						rowsArr.push($table.find('tr').map(grabRow).get())
					}
				}
				
				for (let x=0; x<length; x++) {
					let combinedRow = "";
					for (let y=0; y< rowsArr.length; y++) {
						const elem = rowsArr?.[y]?.[x];
						
						if (currentGroup == "group-header" && x == 1) {
							combinedRow += tmpColDelim;
						}
						
						if (elem != undefined) {
							combinedRow += elem + tmpColDelim;
						} else {
							combinedRow += `${tmpColDelim}${tmpColDelim}${tmpColDelim}${tmpColDelim}${tmpColDelim}${tmpColDelim}${tmpColDelim}${tmpColDelim}`;
						}

						if (currentGroup == "group-preselected" && x == 0) {
							combinedRow += tmpColDelim.repeat(7);
						}
					}
					combinedArr.push(combinedRow);
				}
				if (currentGroup!="group-header")
					combinedArr.push(tmpColDelim);
			}
		});

		

			// CSV
			// exportTableToCSV.apply(this, [$('#dvData'), outputFile]);
		exportFormattedCSV.apply(this, [combinedArr, outputFile])
		
		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "waitList"
		});
	}
});

Template.reportWaitList.helpers({
  'groupTotals': function() {
		return Template.instance().waitListResults.get().waitListTotals;
  }, 
  'profileFieldValue': function(person, path) {
		let key = (person.profileData ? "profileData" : "" ) + "." + path.replace("/", ".");
		
		let result = _.deep(self, key);
		if (key.endsWith("Date") && result) {
			const momentValue = new moment(result);
			if (momentValue.isValid())
				return momentValue.format("M/DD/YYYY");
		} 
		
		return result;
	},
	"hasNumberValue": function(val) {
		return !isNaN(val);
	},
	"formatCurrency": function(num) {
		// Create our number formatter.
		var formatter = new Intl.NumberFormat('en-US', {
		  style: 'currency',
		  currency: 'USD',
		});
		if (num) return formatter.format(num);
	},
	"groups": function() {
		return Template.instance().waitListResults.get().groups;
	},
	"getUnassignedPeople": function() {
		const wlt = Template.instance().waitListResults.get().waitListTotals;
		console.log(wlt.unassignedPeople);
		return (wlt && wlt.unassignedPeople) ? wlt.unassignedPeople : [];
	},
	"reformatScheduleLabel": function(str) {
		return (str || "").replaceAll("Mon", "M").replaceAll("Tue", "T").replaceAll("Wed", "W").replaceAll("Thu", "H").replaceAll("Fri", "F");
	}
});

var fetchClassListReportAggregates = function (instance) {
	Meteor.callAsync("waitListReport").then((response) => {
		$("#btnUpdate").text("Update").prop('disabled', false);
		instance.waitListResults.set(response);
		console.log("response", response)
	}).catch((error) => {
		$("#btnUpdate").text("Update").prop('disabled', false);
		alert(error);
	});
};
