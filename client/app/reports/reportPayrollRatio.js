import './reportPayrollRatio.html';

Template.reportPayrollRatio.created = function() {
	var instance = this;
	this.reportData = new ReactiveVar(false);
	this.dateRangeLabel = new ReactiveVar();
	this.showGroup = new ReactiveVar(false);
	this.groupStaffDetails = new ReactiveVar(false);
}

Template.reportPayrollRatio.rendered = function() {
	var momentDate = new moment();
	$('#reportStartDate').val(momentDate.format("MM/DD/YYYY"));
	$("#reportEndDate").val(momentDate.add(6, 'days').format("MM/DD/YYYY"));
	$("#reportStartDate").datepicker({
		autoclose: true, todayHighlight: true,
	}).on("changeDate", function(e) {
		var momentEndDate = new moment(e.date).add(6, 'days');
		$("#reportEndDate").val(momentEndDate.format("MM/DD/YYYY"))
	})
}

Template.reportPayrollRatio.events({
	"click #showGroup": function(e, i) {
		var checked = $(e.currentTarget).prop("checked");
		if (!checked) i.groupStaffDetails.set(false);
		i.showGroup.set(checked);
	},
	"click #addStaffToGroup": function(e, i) {
		i.groupStaffDetails.set($(e.currentTarget).prop("checked"));
	},
	"click #btnUpdate": async function(e, i) {
		const startDate = new moment($("#reportStartDate").val()).toDate().valueOf();
		$("#btnUpdate").prop('disabled', true);
		
		// var orgs = $("#announcementOrgs").val() || [];
		// var personTypes = $("#reportPersonType").val() || [];

		Meteor.callAsync("reportPayrollRatio", { startDate }).then((response) => {
			$("#btnUpdate").prop('disabled', false);
			i.reportData.set(response);
			var startDateMoment = new moment($("#reportStartDate").val());
			i.dateRangeLabel.set(`${startDateMoment.format('MM/DD/YYYY')} - ${startDateMoment.add(6,'days').format("MM/DD/YYYY")}`);
		}).catch((err) => {
			$("#btnUpdate").prop('disabled', false);
			i.reportData.set(undefined);
			var startDateMoment = new moment($("#reportStartDate").val());
			i.dateRangeLabel.set(`${startDateMoment.format('MM/DD/YYYY')} - ${startDateMoment.add(6,'days').format("MM/DD/YYYY")}`);
		});

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "payrollRatio"
		});
	},
	"click #btnPrint": async function (e,i) {
		var outputFile = 'export.csv'
		// CSV
		exportTableToCSV.apply(this, [$('#dvData table'), outputFile]);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "payrollRatio"
		});
	}
});

Template.reportPayrollRatio.helpers({
	reportRows() {
		const data = Template.instance().reportData.get();
		return data;
	},
	dateRangeLabel() {
		return Template.instance().dateRangeLabel.get();
	},
	totalActiveLabel() {
		const data = Template.instance().reportData.get();
		return data && data.total;
	},
	groupFilterActive() {
		return Template.instance().showGroup.get();
	},
	groupStaffDetails() {
		return Template.instance().groupStaffDetails.get();
	},
	staffInGroup(groupId) {
		const data = Template.instance().reportData.get();
		if (groupId == "center") return _.filter(data.calculatedStaffPayroll, (s) => s.isCenterGroup == true);
		return _.filter(data.calculatedStaffPayroll, (s) => s.defaultGroupId == groupId);
	}
});
