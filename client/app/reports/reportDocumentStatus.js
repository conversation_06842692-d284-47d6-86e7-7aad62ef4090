import { Groups } from '../../../lib/collections/groups';
import { Orgs } from '../../../lib/collections/orgs';
import './reportDocumentStatus.html';

Template.reportDocumentStatus.onCreated( function() {
	const instance = this;

	instance.selectedGroup = new ReactiveVar();
	instance.showIncompleteOnly = new ReactiveVar();
	instance.reportData = new ReactiveVar();
	instance.selectedSort = new ReactiveVar();
	instance.includeInactive = new ReactiveVar(false);
	instance.includeWaitList = new ReactiveVar(true);
});

Template.reportDocumentStatus.onCreated( function() {
	Tracker.afterFlush( function() {
		//if (Router.current().params.query.printable) window.print();
	})
});
Template.reportDocumentStatus.events( {
	"click #btnPrint": async function(e, instance) {
		var outputFile = 'export.csv'
		exportTableToCSV.apply(this, [$('#dvData table'), outputFile]);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "documentStatus"
		});
	},
	"click #btnUpdate": async function() {
		const instance = Template.instance(),
			selectedGroup = $("#selectedGroup").val(),
			selectedSort = $("#selectedSort").val(),
			showIncompleteOnly = $("input[name='show-incomplete-only']").prop('checked'),
			includeInactive = $("#chkIncludeInactive").prop("checked"),
			includeWaitList = $("#chkIncludeWaitList").prop("checked")
		;
		
		instance.selectedGroup.set(selectedGroup);
		instance.showIncompleteOnly.set(showIncompleteOnly);
		instance.selectedSort.set(selectedSort);
		instance.includeInactive.set(includeInactive);
		instance.includeWaitList.set(includeWaitList);

		const options = { selectedGroupId: selectedGroup, includeDetail: true, includeInactive, includeWaitList };

		if (!showIncompleteOnly) options.showAll = true;

		Meteor.callAsync("documentsDueSummary", options).then((result) => {
			instance.reportData.set(result);
		}).catch((err) => {	
			instance.reportData.set(result);
		});

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "documentStatus"
		});
	}
});

Template.reportDocumentStatus.helpers( {
	"reportRows"() {
		const outRows = [], reportData = Template.instance().reportData.get(), sortBy = Template.instance().selectedSort.get();
		
		_.each( reportData, (row) => {
			row.documentsDetail.forEach( (dd) => {
				dd.personDetail = row.personDetail;
				outRows.push(dd);
			});
		});	
		
		return _.sortBy( outRows, (row) => {
			switch( sortBy) {
				case "approved_date": return row.matchedItem && row.matchedItem.approvedAt;
				case "submitted_date": return row.matchedItem && row.matchedItem.createdAt;
				case "rejected_date": return row.matchedItem && row.matchedItem.rejectedAt;
				case "document_name": return row.definition && row.definition.name;
				case "person_name": return row.personDetail && (row.personDetail.lastName + "|" + row.personDetail.firstName);
			}
		});
	},
	"groups"() {
		return Groups.find({}, {sort:{name:1}});
	},
	"showIncludeWaitList"() {
		return Orgs.current() && Orgs.current().hasWaitListDesignation();
	},
	"isWaitList"(document) {
		if (document.personDetail.designations && document.personDetail.designations.length) {
			return document.personDetail.designations.includes('Wait List');
		}
		return false;
	}
});