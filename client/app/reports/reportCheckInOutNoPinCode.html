<template name="reportCheckInOutNoPinCode">
  <div class="row">
		<div class="col-md-12">
			<div class="box">
				<div class="box-header">
				  <h3 class="box-title">Check In/Out without PIN Code</h3>
				</div>
                <div class="box-body">
                    {{#if isSavedMode}}
                        <div class="row font-weight-bolder text-dark my-4">
                            <div class="col">
                                {{{ savedHeader }}}
                            </div>
                        </div>
                    {{/if}}
                    {{#unless isSavedMode}}
                        <div class="row">
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Start Date</label>
                                    <div class="input-group">
                                        <input data-cy="start-date-input" type="text" class="form-control pull-right" id="reportStartDate">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>End Date</label>
                                    <div class="input-group">
                                        <input data-cy="end-date-input" type="text" class="form-control pull-right" id="reportEndDate">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Type</label>
                                    <select data-cy="select-person-type" id="aggregationSelect" name="template_options"
                                            class="form-control form-control-solid">
                                        <option value="all" selected>All</option>
                                        <option value="person">Child</option>
                                        <option value="staff">Staff</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="form-group">
                                    <label>Org(s):</label><br/>
                                    {{> reportOrgsField }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <br/>
                                    <button data-cy="update-btn" type="button" class="btn btn-primary" id="update">Update</button>
                                </div>
                            </div>
                            <div class="col-lg-7 d-flex">
                                {{> reportQueueCheckbox }}
                            </div>
                        </div>
                    {{/unless}}
                </div>
        
        {{#if moments}}
          <h3>Check In/Out Ratio</h3>
          <div class="box-body no-padding">
            <table data-cy="check-in-out-ratio-table" class="table" id="checkWithoutPinTable" style="overflow-x:auto;display:block">
              <tbody>
                <tr>  
                  {{#each title in totalColumnTitles}}
                    <th>
                      {{title}}
                    </th>
                  {{/each}}
                </tr>

                {{#each row in totals}}
                <tr>
                  {{#each col in row}}
                    <td>
                      {{col}}
                    </td>
                  {{/each}}
                </tr>
                {{/each}}
                
              </tbody>
            </table>
          </div>
				{{/if}}
        <br />
				{{#if moments}}
          <h3>Check In/Out Information</h3>
          <div class="box-body no-padding">
            <table data-cy="check-in-out-information-table" class="table" id="checkWithoutPinTable" style="overflow-x:auto;display:block">
              <tbody>
                <tr>  
                  {{#each title in columnTitles}}
                    <th>
                      {{title}}
                    </th>
                  {{/each}}
                </tr>

                {{#each row in moments}}
                <tr>
                  {{#each col in row}}
                    <td>
                      {{#if isLink @index}}
                        <a href="/people/{{col.id}}" class="destination-link">{{col.name}}</a>
                      {{else}}
                        {{col}}
                      {{/if}}
                    </td>
                  {{/each}}
                </tr>
                {{/each}}
                
              </tbody>
            </table>
          </div>
				{{/if}}
			</div>
		</div>
	</div>
</template>