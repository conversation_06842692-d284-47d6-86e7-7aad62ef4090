import { Orgs } from '../../../../lib/collections/orgs';
import '../reportOrgsField';
import './multiSiteReportingOrg.html';

var orgHierarchy = null;
Template.multiSiteOrgReport.helpers({
  availableOrgs() {
    return Template?.instance()?.orgs?.get();
  },
  isLoading() {
    return !Template?.instance()?.loaded?.get();
  },
  orgName() {
    return Orgs?.current() && Orgs?.current()?.name;
  },
});

Template.multiSiteOrgReportDropdown.helpers({
  "orgsFieldOpts": function() {
    return {
      onChange(options) {
        const orgIds = $('#reportOrgs').val();
        console.log({orgIds});
        let selectedNames = $('#reportOrgs option:selected')?.map(function(){
          return $(this).text()
        }).get();
        sessionStorage.setItem('selectedOrgNames', selectedNames);
        sessionStorage.setItem('orgIds', orgIds);
      }
    }
  },
});

Template.multiSiteOrgReport.onCreated(function () {
  var self = this;
  self.orgs = new ReactiveVar();
  self.loaded = new ReactiveVar();
  self.orgnizationData = new ReactiveVar();
  if (!orgHierarchy) {
    Meteor.callAsync(
      "getAllOrgnizationData",
      "orgs").then(async (result) => {
        self.orgnizationData.set(result);
        const a = self.orgnizationData.get();
        const orgsRelation = [];
        orgsRelation.push({
          name: Orgs?.current()?.name,
          id: Orgs?.current()?._id,
          children: buildOrgHierarchy(a, Orgs?.current()?.name),
        });
        orgHierarchy = orgsRelation;
        self.orgs.set(orgsRelation);
        self.loaded.set(true);
        const parentHierarchy = findHierarchy(a, Orgs.current()._id);
        sessionStorage.setItem('parentChildHierarchy',parentHierarchy);
        await Meteor.callAsync('updateDataRedshift', parentHierarchy, Orgs.current()._id);
        const topOrg = a?.filter(item => item.name === (parentHierarchy[0]?.split('/'))[0]);
        sessionStorage.setItem('corporateOrg',topOrg[0]?._id);
        const orgLevels = ((topOrg[0]?.orgLevelNames)?.split(','))?.slice(1);
        sessionStorage.setItem('orgnizationLevels', orgLevels);
    });
  } else {
    self.orgs.set(orgHierarchy);
    self.loaded.set(true);
  }
  $("#orgsSelection").multiselect("rebuild");
});


function findHierarchy(data,id){
  function findParents(data, id){
    const currentItem = data?.find(item => item?._id === id);
    if(!currentItem){
      return null
    }
    
    if(!currentItem?.parentOrgName){
      return null;
    }
    
    const parentItem = data?.find(item => item?.name === currentItem?.parentOrgName);
    if(!parentItem){
      return currentItem?.name;
    }
    
    const parentHierarchy = findParents(data, parentItem?._id);
    if(parentHierarchy){
      return `${parentHierarchy}/${parentItem?.name}`;
    }
    else{
      return parentItem?.name;
    }
  }

  const parents = [];
  parents?.push(findParents(data, id));
  
  const orgReportData = [];
  function findTopmostChildOrg(org ,path=[]){
    if(org?.children?.length === 0){
      orgReportData?.push(path?.join('/'));
    }
    else{
      org?.children?.forEach(child => {
        findTopmostChildOrg(child, [...path,child?.name]);
      })
    }
  }
  findTopmostChildOrg({children: orgHierarchy});
  const r = !parents.includes(null) ? orgReportData?.map(item => `${parents[0]}/${item}`) : orgReportData;
  return r;
}


Template.multiSiteOrgReportDropdown.onRendered(function () {
  var self = this;
  $("#orgsSelection").multiselect({
    enableFiltering: true,
    maxHeight: 300,
    nonSelectedText: Orgs?.current() && Orgs?.current()?.name,
    onChange: function (option, checked) {
      const orgs1 = self?.data?.orgs;
      checkAllBelowOptions($(option).val(), orgs1, checked);
      let orgIds = $('#orgsSelection').val();
      const cOrg = sessionStorage.getItem('corporateOrg');
      orgIds = orgIds?.filter(item => item !== cOrg);
      orgIds = orgIds?.join(',');	

      let selectedNames = $('#orgsSelection option:selected')?.map(function(){
        return $(this).text()
      }).get();
      sessionStorage.setItem('selectedOrgNames', selectedNames);
      sessionStorage.setItem('orgIds', orgIds);
    }
  });
});

function buildOrgHierarchy(tableData, parentOrg = null) {
  const orgWithParent = tableData?.filter(
    (org) => org?.parentOrgName === parentOrg
  );
  const orgWithChildren = orgWithParent?.map((org) => {
    return {
      name: org?.name,
      id: org?.orgId,
      children: buildOrgHierarchy(tableData, org?.name),
    };
  });
  return orgWithChildren;
}

function findSelectedOrgObject(nodes, orgId, isSelected) {
  for (let i = 0; i < nodes?.length; i++) {
    const node = nodes[i];
    if (node?.id === orgId) {
      autoSelectChildOrg(node, isSelected);
      return true;
    } else if (
      node?.children &&
      findSelectedOrgObject(node?.children, orgId, isSelected)
    ) {
      return true;
    }
  }
  return false;
}

function autoSelectChildOrg(node, isSelected) {
  $("#orgsSelection").multiselect(isSelected, node?.id);

  if (node?.children) {
    node?.children?.forEach((child) => autoSelectChildOrg(child, isSelected));
  }
}

function checkAllBelowOptions(orgId, orgs, checked) {
  const isSelected = checked ? "select" : "deselect";
  findSelectedOrgObject(orgs, orgId, isSelected);
}

function buildParentToChildHierarchyPath(data, currentOrgId){
  const org = data?.find(item => item?._id === currentOrgId);
  if(!org){
    return [];
  }

  let paths = [];
  function buildPaths(orgId, currentPath){
    const org1 = data?.find(item => item?._id === orgId);
    if(!org1 || !org1.parentOrgName){
      paths.push(currentPath);
      return;
    }

    currentPath = `${org1.parentOrgName}/${currentPath}`;
    buildPaths(org1.parentOrgName, currentPath)
  }

  buildPaths(org?.parentOrgName,org?.name);
  return paths;
}