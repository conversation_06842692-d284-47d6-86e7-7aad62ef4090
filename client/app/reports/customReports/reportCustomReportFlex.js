import { BLANK, DateFormats, FLEX_TABLE_NAME } from '../../../../lib/constants/reportConstants';
import { getReportFieldMapping, getReportsUrl } from './customReportService/customReportsOrgLevels';
import { CustomReportUtils } from "../../../../lib/customReports/customReportUtils";
import Flexmonster from 'flexmonster';
import './reportCustomReportFlex.html';
import { Orgs } from '../../../../lib/collections/orgs';
import { getWhitelabel, getWhiteLabelSettings } from "../../../../lib/whitelabelUtils";
import { showModal } from '../../main';
import './modalReportSave';

Template.reportCustomReportFlex.onCreated(function () {
    this.reportId = this.data.reportId;
    this.reportObject = CustomReportUtils.getReportObject(this.reportId);
});

Template.reportCustomReportFlex.onRendered(async function () {
    let reportName = this.reportObject.name;
    let saveName = 'My ' + reportName + ' Report';
    const location = new URL(window.location).host;
    const host = getWhitelabel(location);
    const wlSettings = getWhiteLabelSettings(host);
    const flexKey = wlSettings?.flex_key || Meteor.settings.public.flexmonster.key;
    const savedReportObject = this.data.savedReport?.reportObject;
    console.log(`Using Flexmonster key: ${flexKey.substring(flexKey.length - 10)}`);
    const reportsUrl = await getReportsUrl(this.reportObject.index, this.data.filters);
    const generatedSlice = CustomReportUtils.generateSlice(this.data.fields)
    const reportData = {
        dataSource: {
            type: 'json',
            filename: reportsUrl,
            mapping: await getReportFieldMapping(this.reportObject.index),
            useStreamLoader: true
        },
        options: {
            grid: {
                type: 'flat',
                title: reportName,
                showGrandTotals: 'off'
            },
            dateTimePattern: DateFormats.DATE_TIME_PATTERN,
            datePattern: DateFormats.DATE_PATTERN
        },
        formats: CustomReportUtils.generateFormat(this.data.reportId),
        slice: generatedSlice
    }
    if (savedReportObject) {
        reportData.formats = savedReportObject.formats;
        reportData.slice = savedReportObject.slice;
        reportData.options = savedReportObject.options;
        reportData.slice.rows = CustomReportUtils.meldSavedRows(savedReportObject, generatedSlice);
        reportData.options.grid.title = this.data.savedReport.name;
        saveName = this.data.savedReport.name;
    }
    this.pivot = new Flexmonster({
        container: '#pivot-container-flexmonster',
        componentFolder: Meteor.settings.public.flexmonster.componentFolderUrl,
        toolbar: true,
        licenseKey: flexKey,
        beforetoolbarcreated: customizeToolbar,
        height: '100%',
        filters: false,
        configuratorButton: false,
        report: reportData,
        global: {
            localization: {
                grid: {
                    'blankMember': BLANK,
                    'dateInvalidCaption': BLANK
                }
            }
        }
    });
    /**
     * Customize the tabs toolbar of flexmonster
     * @param {object} toolbar 
     */
    async function customizeToolbar(toolbar) {
        let tabs = toolbar.getTabs();
        toolbar.getTabs = function () {
            const tabs1 = tabs.filter(tab => tab.id !== 'fm-tab-connect');
            tabs = tabs1.filter(tab => tab.id !== 'fm-tab-open');
            const saveTab = tabs[tabs.map(function(e) {
                return e.id;
            }).indexOf('fm-tab-save')];
            saveTab.title = 'Save Filter';
            saveTab.handler = openSaveModal;
            return tabs;
        }
    }
    const openSaveModal = () => {
        const reportData = this.pivot.getReport();
        showModal('modalReportSave', { reportData, reportName: saveName, index: this.reportObject.index }, '#modalReportSave');
    }
    window.addEventListener('resize', () => {
        this.pivot?.refresh();
    });
});

Template.reportCustomReportFlex.helpers({
    reportTitle: function () {
        return Template.instance().reportObject?.name + ' Report';
    }
})

Template.reportCustomReportFlex.onDestroyed(function() {
    if(this.pivot && this.pivot?.dispose){
        this.pivot?.dispose();
        this.pivot = null;
    }    
});