import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import {getPeopleById, getPeopleData} from "../../services/peopleMeteorService"
import './reportBusRoster.html';
import { Orgs } from '../../../lib/collections/orgs';
import { Person } from '../../../lib/collections/people';

Template.reportBusRoster.onCreated( function() {
	var instance = this;
	const isPrintableView = FlowRouter.getQueryParam('printable');
	const reportStartMonday = FlowRouter.getQueryParam('selectedStartMonday');
	const busRosterAmPm = FlowRouter.getQueryParam('selectedAmPm');
	//this.reportStartMonday = new ReactiveVar(moment().format("MM/DD/YYYY")); //report can only be run in weekly views, so this is the Monday of the week - not just any start date.
	//this.busRosterAmPm = new ReactiveVar("busRosterAmPm");

	instance.dataLoaded = new ReactiveVar(false);
	instance.selectedReportStartMonday= new ReactiveVar(reportStartMonday);
	instance.selectedBusRosterAmPm= new ReactiveVar(busRosterAmPm);
	instance.selectionMade = new ReactiveVar(isPrintableView ? true : false);
	instance.peopleList = new ReactiveVar([]);
	this.autorun(() => {
		let amArray = [];
		let pmArray = [];
		if(this.selectedBusRosterAmPm.get() == "am") {
			amArray =  _.filter(Orgs.current().busRoutes, (r) => r.am && r._id).map((fr)=> fr._id);
		} else {
			pmArray =   _.filter(Orgs.current().busRoutes, (r) => r.pm && r._id).map((fr)=> fr._id);
		}
		const startDate = moment(this.selectedReportStartMonday.get()).valueOf();
		let query = {};
		if(Orgs.current().profileDataPrefix()) {
			if(this.selectedBusRosterAmPm.get() == "am") {
				query = {type:"person", $or: [{withdrawDate: {$gte: startDate}}, {withdrawDate: null}], "profileData.amBusRoute": {$in: amArray},  inActive:{"$ne":true}, designations: { $nin: ["Wait List"] }};
			} else {
				query = {type:"person", $or: [{withdrawDate: {$gte: startDate}}, {withdrawDate: null}], "profileData.pmBusRoute": {$in: pmArray},  inActive:{"$ne":true}, designations: { $nin: ["Wait List"] }};
			}
		} else {
			if(this.selectedBusRosterAmPm.get() == "am") {
				query = {type:"person", $or: [{withdrawDate: {$gte: startDate}}, {withdrawDate: null}], "amBusRoute": {$in: amArray},  inActive:{"$ne":true}, designations: { $nin: ["Wait List"] }};
			} else {
				query = {type:"person", $or: [{withdrawDate: {$gte: startDate}}, {withdrawDate: null}], "pmBusRoute": {$in: pmArray},  inActive:{"$ne":true}, designations: { $nin: ["Wait List"] }};
			}
		}
		getPeopleData(query, {sort: {lastName:1, firstName:1}, fields: {_id:1, lastName:1, firstName:1, inActive: 1, "profileData.amBusRoute": 1, amBusRoute: 1, "profileData.pmBusRoute": 1, pmBusRoute: 1, "profileData.birthday": 1, birthday:1, "profileData.school": 1, school: 1 }}).then(peopleRes => {
			const tempPeople = peopleRes.map(item => {
				return new Person(item); // Convert to collection document to get access to Person extended methods.
				});
				this.peopleList.set(tempPeople)
				this.dataLoaded.set(true);
		}).catch(err => {
			console.log(err);
		});
	})
});

Template.reportBusRoster.rendered = function() {
	$('#startMonday').datepicker({autoclose:true});
}

Template.reportBusRoster.helpers({
	"openPrintableView": function() {
		return Template.instance().dataLoaded.get() && FlowRouter.getQueryParam('printable');
	},
    "fullLayout"() {
		return FlowRouter.getQueryParam('printable') != "true";
	},
	"days"() {
		return ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];
	},
	"signatureItems"() {
		return ["Handing off count & Sign",
				"Receiving count & Sign",
				"Driver Vehicle check - Sign",
				"Management Vehicle check - Sign"];
	},
	"org"() {
		return Orgs.current();
	},
	"reportRoutes"() {
		if (!Template.instance().selectionMade.get()) return;

		if(Template.instance().selectedBusRosterAmPm.get() == "am") {
			return _.filter(Orgs.current().busRoutes, (r) => r.am);
		} else {
			return _.filter(Orgs.current().busRoutes, (r) => r.pm);
		}
	},
	"children"() {
		if (!Template.instance().selectionMade.get()) return;
		const peopleList =  Template.instance().peopleList.get()
		if(Orgs.current().profileDataPrefix()) {
			if(Template.instance().selectedBusRosterAmPm.get() == "am") {
				return peopleList.filter((p)=> p["profileData"]["amBusRoute"] === this._id && p)
			} else {
				return peopleList.filter((p)=> p["profileData"]["pmBusRoute"] === this._id && p)
			}
		} else {
			if(Template.instance().selectedBusRosterAmPm.get() == "am") {
				return peopleList.filter((p)=> p["amBusRoute"] === this._id && p)
			} else {
				return peopleList.filter((p)=> p["pmBusRoute"] === this._id && p)
			}
		}
	},
	'repeat'(max) {
		return _.range(max);
	},
	'count'(array) {
		return array.length;
	},
	'reportStartMonday'() {
		return Template.instance().selectedReportStartMonday.get();
	},
	'endReportCheck'(index, array) {
		return index + 1 == array.length;
	},
	'reportAmPmDisplay'() {
		return Template.instance().selectedBusRosterAmPm.get() == "am" ? "AM" : "PM";
	},
	'currentMonday'() {
		return moment().startOf('isoWeek').format("MM/DD/YYYY");
	},
	'getChildDob'(person) {
		const peopleList =  Template.instance().peopleList.get()
		if(Orgs.current().profileDataPrefix()) {
			return new moment(peopleList.find((p)=> p._id === person._id).profileData.birthday).format("MM/DD/YYYY")
		} else {
			return new moment(peopleList.find((p)=> p._id === person._id).birthday).format("MM/DD/YYYY")
		}
	},
	'getSchool'(person) {
		const peopleList =  Template.instance().peopleList.get()
		if(Orgs.current().profileDataPrefix()) {
			return peopleList.find((p)=> p._id === person._id).profileData.school
		} else {
			return peopleList.find((p)=> p._id === person._id).school
		}
	}
});

Template.reportBusRoster.events({
	"click #btnUpdate": async function() {
		let instance = Template.instance();
		if($("#startMonday").val())
		{
			instance.selectedReportStartMonday.set(moment($("#startMonday").val()).startOf('isoWeek').format("MM/DD/YYYY"));
		} else {
			instance.selectedReportStartMonday.set(moment().startOf('isoWeek').format("MM/DD/YYYY"));
		}

		instance.selectedBusRosterAmPm.set($('input[name="busRosterAmPm"]:checked').val());
		instance.selectionMade.set(true);

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "busRoster"
		});
	},
	"click #btnPrint": async function(e, instance) {
		const url = "/reports/reportBusRoster?printable=true&selectedAmPm=" + instance.selectedBusRosterAmPm.get() + "&selectedStartMonday=" + instance.selectedReportStartMonday.get();
		window.open( url, '_blank');

		await Meteor.callAsync('trackClientActivity', {
			label: "report-exported",
			reportType: "busRoster"
		});
	},
});