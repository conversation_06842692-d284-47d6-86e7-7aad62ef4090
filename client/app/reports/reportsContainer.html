<template name="reportsContainer">
  {{#if isLoading}}
  <div class="d-flex justify-content-center align-items-center" style="height: 200px">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  {{else}} {{#if displayReportName}}
  <div class="container">{{> Template.dynamic template=displayReportName}}</div>
  {{else}}
  <div class="container-fluid" id="dashboard">
    {{#if searchIsEnabled}}
    <div class="row mb-8">
      <div class="col-xl-4 col-lg-6 col-md-6 col-sm-6 offset-xl-4 offset-lg-3 offset-md-3 offset-sm-3">
        <input type="text" class="form-control" placeholder="Search for a report..." id="searchReports" />
      </div>
    </div>
    {{/if}}
    <div class="row">
      {{#each reports in availableReports}}
      <div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
        <div class="card card-custom gutter-b card-stretch">
          <div class="d-flex flex-column card-body pt-4 justify-content-between">
            <div class="d-flex align-items-center mb-7">
              <div class="flex-shrink-0 mr-4">
                <i class="fa fa-chart-bar fa-lg"></i>
              </div>
              <div class="d-flex flex-column">
                {{#if checkTitle reports.name}}
                <a href="/pdf/nametofaceprintout.pdf" target="_blank" class="text-dark font-weight-bold text-hover-primary font-size-h4 mb-0"
                  >{{reports.name}}</a
                >
                {{else}}
                <a href="/reports/{{reports._id}}" class="text-dark font-weight-bold text-hover-primary font-size-h4 mb-0">{{reports.name}}</a>
                {{/if}}
              </div>
            </div>
            <p class="mb-7">{{reports.description}}</p>
            {{#if checkTitle reports.name}}
            <a href="/pdf/nametofaceprintout.pdf" target="_blank" class="btn btn-block btn-sm btn-light-primary font-weight-bolder text-uppercase py-4"
              >View {{reports.name}}</a
            >
            {{else}}
            <a href="/reports/{{reports._id}}" class="btn btn-block btn-sm btn-light-primary font-weight-bolder text-uppercase py-4">View {{reports.name}}</a>
            {{/if}}
          </div>
        </div>
      </div>
      {{/each}}
    </div>
  </div>
  {{/if}} {{/if}}
</template>
