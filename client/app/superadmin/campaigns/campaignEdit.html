<template name="campaignEdit">
	<a href="/superadmin/campaigns">&lt; Campaigns List</a><br/>
	<br/>
	<form class="form">
		<div class="row">
			<div class="col-12">
				<label class="font-weight-bold">Campaign Title</label>
				<input class="form-control" name="campaign-title" value="{{campaignData.title}}"><br/>

				<div class="checkbox-list">
					<label class="checkbox checkbox-primary">
						<input type="checkbox" class="form-check-input" name="campaign-active" {{checkedIfEq campaignData.active true}}>
					<span></span>
					Active (campaign will only display to recipients if checked)
					</label>
				</div>
				<hr class="mt-2 mb-3" style="border-top-width:3px;"/>
				<label class="font-weight-bolder">Campaign Type</label>

				<hr class="mt-2 mb-3"/>

				<div class="form-check">
					<input class="form-check-input" type="radio" name="campaign-type" id="campaign-type-simple-survey" value="simple-survey" {{checkedIfEq campaignData.campaignType "simple-survey"}}>
					<label class="form-check-label font-weight-bold" for="flexRadioDefault1">
						Simple 1-10 Survey
					</label>
				</div>
				<div class="pl-10 mt-6">
					<label class="font-weight-bold">Question for User</label>
					<input type="text" name="announcement" class="form-control" value="{{campaignData.announcement}}">
				</div>

				<div class="form-check mt-6">
					<input class="form-check-input" type="radio" name="campaign-type" id="campaign-type-resource-card" value="resource-card" {{checkedIfEq campaignData.campaignType "resource-card"}}>
					<label class="form-check-label font-weight-bold" for="flexRadioDefault1">
						Resource Link Card
					</label>
				</div>
				<div class="pl-10 mt-6">
					<label class="font-weight-bold">Title</label>
					<input type="text" name="resource-title" class="form-control" value="{{campaignData.resourceTitle}}">
					<label class="font-weight-bold">Description</label>
					<input type="text" name="resource-description" class="form-control" value="{{campaignData.resourceDescription}}">
					<label class="font-weight-bold">Link</label>
					<input type="text" name="resource-link" class="form-control" value="{{campaignData.resourceLink}}">
					
				</div>

				<hr class="mt-2 mb-3" style="border-top-width:3px;"/>
				<label class="font-weight-bolder">Campaign Targeting</label>

				<hr class="mt-2 mb-3"/>
				<div class="row">
					<div class="col-6" >
						<label class="font-weight-bold">Add Org Targets</label>
						<div class="input-group mb-3">
							<div class="input-group-prepend">
							  <span class="input-group-text" id="basic-addon1"><i class="fa fa-search"></i></span>
							</div>
							<input id="txtSearch" type="text" class="form-control" placeholder="Search available orgs..." aria-label="Org" aria-describedby="basic-addon1">
						</div>
						<div style="height:250px;overflow:auto">
							<table class="table">
								<tr>
									<th style="width:50px"><input type="checkbox" id="chkSelectAll"></th>
									<th>Name</th>
								</tr>
								{{#each org in availableOrgs}}
								<tr>
									<td  style="width:50px"><input type="checkbox" class="chkAddOrg" data-id="{{org._id}}"></td>
									<td>{{org.name}}</td>
								</tr>
								{{/each}}
							</table>
						</div>
						<div class="text-center">
							<button class="btn btn-primary" id="btnAddOrg">Add Org(s)</button>
						</div>
					</div>

					<div class="col-6">
						<div style="height:250px;overflow:auto">
							<table class="table">
								<tr>
									<th>Target Org Name</th>
									<th>Action</th>
								</tr>
								{{#each org in selectedOrgs}}
								<tr>
									<td>{{org.name}}</td>
									<td><a href="#" class="btnRemoveOrg" data-id="{{org._id}}">remove</a></td>
								</tr>
								{{/each}}
							</table>
						</div>
						<div class="text-center">
							{{selectedOrgsCount}} Org(s) Selected
						</div>
					</div>
				</div>

				<hr class="mt-2 mb-3"/>
				<label class="font-weight-bold">Targeted Types</label>
				<div class="checkbox-list">
					<label class="checkbox checkbox-primary">
						<input type="checkbox" class="form-check-input" name="target-types" {{checkedIfEq (selectedTypesContain "family") true}} value="family">
					<span></span>
					Family
					</label>
					<label class="checkbox checkbox-primary">
						<input type="checkbox" class="form-check-input" name="target-types" {{checkedIfEq (selectedTypesContain "staff") true}} value="staff">
					<span></span>
					Staff
					</label>
					<label class="checkbox checkbox-primary">
						<input type="checkbox" class="form-check-input" name="target-types" {{checkedIfEq (selectedTypesContain "admin") true}} value="admin">
					<span></span>
					Admins
					</label>
				</div>
				<hr class="mt-2 mb-3"/>
				<div class="text-center">
					<button class="btn btn-primary" id="btnSave">Save</button>
					<a href="/superadmin/campaigns" class="btn btn-secondary">Cancel</a>
				</div>
			</div>
		</div>
	</form>
</template>
