import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import _ from '../../../lib/util/underscore';
import './superAdminFixLineItemsWithoutVoid.html';
import moment from "moment-timezone";
import $ from 'jquery';

Template.superAdminFixLineItemsWithoutVoid.onCreated(function () {
	const self = this;
	self.haveQueried = new ReactiveVar(false);
	self.effectedInvoices = new ReactiveVar([]);
	self.availableOrgs = new ReactiveVar([]);
	self.loadingInvoices = new ReactiveVar(false);
	self.selectableParentOrg = new ReactiveVar(null);
	self.numberOfSelectableChildren = new ReactiveVar(0);
	self.confirmingFixAll = new ReactiveVar(false);
	self.fixingAll = new ReactiveVar(false);
	self.csvData = new ReactiveVar();
	Tracker.autorun(function () {
		Meteor.callAsync("superAdminList", {
			showAll: true,
			includeParentOrg: true,
			includeChildrenCount: true,
		}).then((availableOrgs) => {
			self.availableOrgs.set(availableOrgs);
		});
	});
});

Template.superAdminFixLineItemsWithoutVoid.onRendered(function () {
	const templateInstance = this;
	$("#orgsList").select2({
		selectionCssClass: "form-control form-control-lg form-control-solid",
		matcher: function (params, data) {
			if (!params.term) {
				return data;
			}
			const term = params.term.toLowerCase();
			const name = data.text.toLowerCase();
			const id = data.id.toLowerCase(); // Not _id, because select2 transforms it to id
			if (name.indexOf(term) > -1 || id.indexOf(term) > -1) {
				return data;
			}
			return null;
		},
	}).on("change", () => {
		const selectedOrgId = $("#orgsList").val();
		const selectedOrg = _.find(this.availableOrgs.get(), function (o) {
			return o._id === selectedOrgId;
		});
		// Set selectableParentOrg to the parent org of the selected org
		if (selectedOrg && selectedOrg.parentOrg) {
			templateInstance.selectableParentOrg.set(selectedOrg.parentOrg);
			templateInstance.numberOfSelectableChildren.set(selectedOrg.childrenCount);
		} else {
			templateInstance.selectableParentOrg.set(null);
			templateInstance.numberOfSelectableChildren.set(0);
		}
	});
})

Template.superAdminFixLineItemsWithoutVoid.helpers({
	getNumberOfSelectableChildren: function () {
		return Template.instance().numberOfSelectableChildren.get();
	},
	getConfirmingFixAll: function () {
		return Template.instance().confirmingFixAll.get();
	},
	formatTimestamp: function (timestamp) {
		const timestampMoment = moment.tz(timestamp, 'America/New_York');
		const formattedMoment = timestampMoment.format("MM/DD/YYYY hh:mm:ss a");
		const timeSinceNow = timestampMoment.fromNow();
		return `${formattedMoment} EST (${timeSinceNow})`;
	},
	getLineItemAllocationType: function (lineItem) {
		// lineItem.enrolledPlan.allocations[0].allocationType
		const allocations = lineItem.enrolledPlan?.allocations || [];
		if (allocations.length === 0) {
			return "None"; // Should never happen, as this component queries for invoices with allocations
		}
		const allocationTypes = _.map(allocations, function (allocation) {
			return allocation.allocationType;
		});
		return allocationTypes.join(", "); // It's an array to it seems possible to have more than 1, but seems uncommon
	},
	haveQueried: function () {
		return Template.instance().haveQueried.get();
	},
	availableOrgs: function () {
		const orgs = Template.instance().availableOrgs.get();
		return _.map(orgs, function (o) {
			return {
				_id: o._id,
				name: `${o.name} (org ID - ${o._id})`,
			};
		});
	},
	effectedInvoices: function () {
		return Template.instance().effectedInvoices.get();
	},
	currentParentOrg() {
		return Template.instance().selectableParentOrg.get();
	}
});

Template.superAdminFixLineItemsWithoutVoid.events({
	'click .btn-fix-all-invoices': function (event, templateInstance) {
		templateInstance.confirmingFixAll.set(true);
	},
	'click .btn-fix-all-invoices-cancel': function (event, templateInstance) {
		templateInstance.confirmingFixAll.set(false);
	},
	'click .btn-fix-all-invoices-confirm': function (event, templateInstance) {
		if (templateInstance.fixingAll.get()) {
			return;
		}
		templateInstance.fixingAll.set(true);
		$(".btn-fix-all-invoices-confirm").text("Fixing...");
		$(".btn-fix-all-invoices-cancel").prop("disabled", true);
		$(".btn-fix-all-invoices-confirm").prop("disabled", true);
		// For now, it's not efficient but we just iterate over all effected
		// invoices and fix them one by one.
		const effectedInvoices = templateInstance.effectedInvoices.get();
		const effectedInvoiceIds = _.map(effectedInvoices, function (invoice) {
			return invoice._id;
		});
		Meteor.callAsync("superAdminFixLineItemsForVoidedInvoices", {
			invoiceIds: effectedInvoiceIds
		}).then((result) => {
			templateInstance.fixingAll.set(false);
			$(".btn-fix-all-invoices-confirm").text("Fix All");
			$(".btn-fix-all-invoices-cancel").prop("disabled", false);
			$(".btn-fix-all-invoices-confirm").prop("disabled", false);
			mpSwal.fire("Success", `Invoices fixed.`, "success");
			// Remove the row from the table
			templateInstance.effectedInvoices.set([]);
		}).catch((error) => {
			templateInstance.fixingAll.set(false);
			$(".btn-fix-all-invoices-confirm").text("Fix All");
			$(".btn-fix-all-invoices-cancel").prop("disabled", false);
			$(".btn-fix-all-invoices-confirm").prop("disabled", false);
			mpSwal.fire("Error", `Error fixing invoices: ${error}`, "error");
		});
	},
	'submit form': function (event, templateInstance) {
		event.preventDefault();
		// If fixing all, stop for now.
		if (templateInstance.fixingAll.get()) {
			return;
		}
		// See if useParentOrg is checked:
		const useParentOrg = $("#useParentOrg").is(":checked");
		// useChildrenOrgs:
		const useChildrenOrgs = $("#useChildrenOrgs").is(":checked");
		if (templateInstance.loadingInvoices.get()) {
			return;
		}
		$("#btn-find-invoices").text("Finding...");
		templateInstance.loadingInvoices.set(true);
		const selectedOrgId = $("#orgsList").val();
		Meteor.callAsync("superadminGetLineItemsForVoidedInvoices", {
			orgId: selectedOrgId,
			useParentOrg,
			useChildrenOrgs
		}).then((result) => {
			templateInstance.loadingInvoices.set(false);
			$("#btn-find-invoices").text("Find Invoices");
			templateInstance.confirmingFixAll.set(false);
			templateInstance.haveQueried.set(true);
			templateInstance.effectedInvoices.set(result.effectedInvoices.map(invoice => {
				// In case we need to do more here...
				return invoice;
			}));
		}).catch((error) => {
			templateInstance.loadingInvoices.set(false);
			$("#btn-find-invoices").text("Find Invoices");
			mpSwal.fire("Error", `Error getting voided invoices with subsidy allocations: ${error}`, "error");
		});
	},
	'click .btn-fix-invoice': function (event, templateInstance) {
		// If fixing all, stop for now.
		if (templateInstance.fixingAll.get()) {
			return;
		}
		const buttonTarget = $(event.currentTarget);
		const invoiceId = buttonTarget.data("invoice-id");
		buttonTarget.text("Fixing...");
		buttonTarget.prop("disabled", true);
		Meteor.callAsync("superAdminFixLineItemsForVoidedInvoices", {
			invoiceIds: [invoiceId]
		}).then((result) => {
			buttonTarget.text("Fix");
			buttonTarget.prop("disabled", false);
			mpSwal.fire("Success", `Invoice fixed.`, "success");
			// Remove the row from the table
			const effectedInvoices = templateInstance.effectedInvoices.get();
			const newEffectedInvoices = _.reject(effectedInvoices, function (invoice) {
				return invoice._id === invoiceId;
			});
			templateInstance.effectedInvoices.set(newEffectedInvoices);
		}).catch((error) => {
			buttonTarget.text("Fix");
			buttonTarget.prop("disabled", false);
			mpSwal.fire("Error", `Error fixing invoice: ${error}`, "error");
		});
	},
	"click .btn-export": function (event, templateInstance) {
		var outputFile = "invoicesToFix.csv";
		const self = templateInstance;
		let customizedTable = $('<table>');
		let headers = ["Invoice Number", "Invoice ID", "Invoice Date", "Voided At", "Person Name", "Description", "Line Items", "Debits", "Credits"];
		let headerRow = $('<tr>');
		headers.forEach(function (header) {
			let th = $('<th>').text(header);
			headerRow.append(th);
		});
		customizedTable.append(headerRow);

		const invoices = self.effectedInvoices.get();
		Meteor.callAsync("fixLineItemsWithoutVoidReport", invoices).then((result) => {
			self.csvData.set(result);
			const data = self.csvData.get();
			data.forEach(function (rowData) {
				let row = $('<tr>');
				rowData.forEach(function (cellData) {
					let cell = $('<td>').text(cellData);
					row.append(cell);
				});
				customizedTable.append(row);
			});
			exportTableToCSV.apply(this, [customizedTable, outputFile]);
		}).catch((error) => {
			console.log(error);
			mpSwal.fire("Error", `Error when exporting invoices: ${error.message}`, "error");
		});
	}
});