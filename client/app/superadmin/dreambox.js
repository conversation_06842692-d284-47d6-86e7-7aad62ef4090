import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import _ from '../../../lib/util/underscore';
import './dreambox.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import <PERSON> from "papaparse";
import { hideModal, showModal } from '../main';
import '../simpleModal/simpleModal';
import './_newReleaseUpdateModal';

Template.dreamboxIntegrationModal.helpers({
	"rootOrgs": function() {
		return Orgs.find({'parentOrgId': {$eq: null}, 'dreambox': {$eq: null}});
	},
	'staffFields': function() {
		const orgId = Template.instance().data.editOrgId;
		const org = Orgs.findOne({_id: orgId});
		return org ? getFields(org, 'staffProfileFields') : [];
	},
	'childFields': function() {
		const orgId = Template.instance().data.editOrgId;
		const org = Orgs.findOne({_id: orgId});
		return org ? getFields(org, 'profileFields') : [];
	},
	'dreamboxConfig': function() {
		if (Template.instance().data.editOrgId) {
			return Orgs.findOne({_id: Template.instance().data.editOrgId})?.dreambox || {};
		}
		return {};
	}
});
Template.dreambox.helpers({
	"integratedOrgs": function () {
		return Orgs.find({'dreambox': {$ne: null}});
	},
	"dreamboxEnabled": function(orgId) {
		const org = Orgs.findOne({_id: orgId});
		return org.hasCustomization('integrations/dreambox/enabled');
	},
});

Template.dreambox.events({
	"click .toggle-dreambox": async function(event, template) {
		const curId = $(event.currentTarget).attr("data-id");
		await Meteor.callAsync('toggleDreambox', curId);
	},
	"click .delete-dreambox": async function(event, template) {
		const curId = $(event.currentTarget).attr("data-id");
		await Meteor.callAsync('deleteDreambox', curId);
	},
	"click .manual-upload-dreambox": async function(event, template) {
		const curId = $(event.currentTarget).attr("data-id");
		await Meteor.callAsync('manualUploadDreambox', curId);
	},
	"click .get-dreambox-file": function(event, template) {
		const curId = $(event.currentTarget).attr("data-id");
		Meteor.callAsync('getDreamboxFile', curId).then((data) => {
			const csv = Papa.unparse(data);
			DownloadFile(csv, 'dreambox.csv');
		});
	},
	"click .edit-dreambox": function(event) {
		const curId = $(event.currentTarget).attr("data-id");
		const org = Orgs.findOne({_id: curId});
		if (!org) {
			return;
		}
		let host = '';
		let dir = '';
		let username = '';
		let password = '';
		if (org.dreambox) {
			host = org.dreambox.host;
			dir = org.dreambox.dir;
			username = org.dreambox.username;
			password = org.dreambox.password;
		}
		showModal("simpleModal", {
			title:"Edit Dreambox Integration",
			template: "dreamboxIntegrationModal",
			data: {editOrgId: curId, host, dir, username, password},
			onSave: (e, i, formFieldData) => {
				Meteor.callAsync("updateDreamboxIntegration", formFieldData).then(() => {
					hideModal("#simpleModal");
				}).catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		}, "#simpleModal");
	},
	"click #btnAddDreamboxIntegration": function() {
		showModal("simpleModal", {
			title:"Create New Dreambox Integration",
			template: "dreamboxIntegrationModal",
			data: {},
			onSave: (e, i, formFieldData) => {
				Meteor.callAsync("updateDreamboxIntegration", formFieldData).then(() => {
					hideModal("#simpleModal");
				}).catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		}, "#simpleModal");
	}
});

function getFields(org, type) {
	const allOrgIds = org.findAllRelatedOrgIds();
	const allOrgs = Orgs.find({_id: {$in: allOrgIds}}).fetch();
	let fields = [];
	for (const iterOrg of allOrgs) {
		if (iterOrg.valueOverrides && iterOrg.valueOverrides[type]) {
			fields = fields.concat(iterOrg.valueOverrides[type].map(f => f.name));
		}
	}
	fields.sort();
	fields = fields.filter((v,i,a)=>a.indexOf(v) === i);
	fields.unshift('');
	return fields;
}

