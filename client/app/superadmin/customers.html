<template name="customers">
  <div class="d-flex flex-row justify-content-between mb-4 mx-8">
    <h3>Customers</h3>
    <button class="btn btn-primary pull-right" id="btnAddCustomer"><i class="fa fa-plus"></i> New Customer</button>
  </div>

	<div class="card card-custom mx-8">
    {{#unless showInactive}}
    <a href="/superadmin/customers?showinactive=true">Show Inactive</a><br/>
    {{/unless}}
		<div class="box-body table-responsive no-padding">
			<table class="table table-hover">
				<tbody>
					<tr>
						<th>Name</th>
						<th class="text-center">Last Engagement Date</th>
						<th class="text-center">Last Engagement Amount</th>
						<th>Action</th>
					</tr>

					{{#each customers}}
					<tr>
						<td><a href="/superadmin/customers/{{_id}}">{{name}}</a></td>
						<td class="text-center">{{lastEngagementDateInfo "date"}}</td>
						<td class="text-center">{{lastEngagementDateInfo "count"}}</td>
						<td><a href="/superadmin/customers/{{_id}}">Edit</a></td>
					</tr>
					{{/each}}
				</tbody>
			</table>
		</div>
	</div>
</template>

<template name="newCustomerModal">
	<form id="formNewCustomer">
		<div class="row">
			<div class="col-md-12">
				<label>Company Name:</label>
				<input type="text" class="form-control" name="orgname"><br/>
				<label for="timezone">Timezone (required):</label>
				<select class="form-control mb-4" name="timezone" id="timezone">
					<option value="">Select a timezone</option>
					<option value="America/New_York">Eastern Time (America/New_York)</option>
					<option value="America/Chicago">Central Time (America/Chicago)</option>
					<option value="America/Denver">Mountain Time (America/Denver)</option>
					<option value="America/Phoenix">Mountain Time (Arizona)</option>
					<option value="America/Los_Angeles">Pacific Time (America/Los_Angeles)</option>
					<option value="Pacific/Honolulu">Hawaii Time (Pacific/Honolulu)</option>
				</select>
				<label>Industry:</label>
				<select class="form-control" name="industry">
					<option value="childcare">Childcare</option>
					<option value="schools">Schools</option>
					<option value="senior">Adult/Senior</option>
				</select><br/>

				<div class="checkbox">
					<label>
						<input type="checkbox" id="chkDisableTrial" name="disableTrial" checked> Disable trial
					</label>
				</div>

				<div class="checkbox">
					<label>
						<input type="checkbox" id="chkProvisionMetacx" name="provisionMetacx" checked> Provision MetaCX
					</label>
				</div>
			</div>
		</div>
	</form>
</template>
