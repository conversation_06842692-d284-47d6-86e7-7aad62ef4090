<template name="benchmarkStats">
    <div class="d-flex flex-row justify-content-between mb-4 mx-8">
        <h3>Select an Org To Get Stats For</h3>
    </div>

    <div class="card card-custom mx-8">
        <div class="box-body table-responsive no-padding">
            <table class="table table-hover">
                <tbody>
                    <tr>
                        <td>
                            <p>
                                <select id="statsOrg">
                                    {{#each orgs}}
                                        <option value="{{ rootOrgId }}">{{ name }} </option>
                                    {{/each}}
                                </select>
                            </p>
                            <button class="btn btn-primary stats-button">Get Stats</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="stats-results row">
        <div class="col offset-1">
            {{#if hasStats }}
                <table class="table">
                    {{#each stats}}
                        <tr>
                            <td>{{ label }}</td>
                            <td>{{ value }}</td>
                        </tr>
                    {{/each}}
                </table>
                <div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4 mt-10" id="btnExport">
                    <i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
                </div>
            {{/if }}
            {{#if isLoading }}
                {{> loading }}
            {{/if}}
        </div>
    </div>
</template>
