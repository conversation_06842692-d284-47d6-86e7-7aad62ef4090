import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './superAdminReportsFailedInvoicing.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Log } from '../../../../lib/util/log';

Template.superAdminReportsFailedInvoicing.events({
    'click #reportsLink': async function (e, i) {
        e.preventDefault();
        FlowRouter.go('superAdminReports');
    },
    'click #btnViewReport': async function (e, i) {
        const timezone = document.getElementById('timezone').value;

        if (!timezone) {
            mpSwal.fire({
                text: 'Please select a timezone.',
                icon: 'error'
            });
            return;
        }
        Meteor.callAsync('viewFailedInvoicingReport', { timezone }).then((result) => {
            mpSwal.fire('Your report is being generated. You will receive an email when it is ready if there is any failed invoicing and you are on the distribution list.');
        }).catch((err) => {
            Log.error({ err });
            mpSwal.fire({
                text: 'There was an error generating the report.',
                icon: 'error'
            });
        });
    }
});
