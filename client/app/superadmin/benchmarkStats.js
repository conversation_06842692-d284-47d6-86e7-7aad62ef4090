import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import './benchmarkStats.html';

Template.benchmarkStats.created = function () {
    this.loading = new ReactiveVar(false);
    this.stats = new ReactiveVar([]);
    this.orgs = [
        {
            name: 'CDS',
            rootOrgId: 'tJ9aLXAJBhMr8gxz9'
        },
        {
            name: 'Right At School',
            rootOrgId: 'm3yycdFev2CeLPmT9'
        },
        {
            name: 'Sunshine House',
            rootOrgId: 'zT4NtQk32ooZ6Q6kA',
        },
        {
            name: 'Lightbridge Academy',
            rootOrgId: 'wXZeDbmLnTNEDzL3K',
        },
        {
            name: 'Little Sunshine\'s Playhouse',
            rootOrgId: 'xiL88wfjv59oJ8G2w'
        },
        {
            name: 'Building Kidz',
            rootOrgId: 'FfkDjcS59Eg76FJqM'
        },
        {
            name: 'Early Learning Academies',
            rootOrgId: 'qPp44WfCMqTj6kdJF'
        },
        {
            name: 'New Creations',
            rootOrgId: 'G3YmXyBMyrfrk4ys9'
        },
        {
            name: 'KLA',
            rootOrgId: 'wG6EEHZ89rN2xkSmT'
        },
        {
            name: 'Early Learning Indiana',
            rootOrgId: 'g8PFB4qWj2yqR9Jsn'
        },
        {
            name: 'Mariposa',
            rootOrgId: 'GtAoTHqGeLk9BR8iw'
        }
    ]
}

Template.benchmarkStats.helpers({
    "orgs": function () {
        return Template.instance().orgs;
    },
    'isLoading': function() {
        return Template.instance().loading.get();
    },
    hasStats: function() {
        return Template.instance().stats.get().length > 0;
    },
    stats: function() {
        return Template.instance().stats.get();
    }
})

Template.benchmarkStats.events({
    'click .stats-button': async function(e, i) {
        e.preventDefault();
        const orgId = document.getElementById('statsOrg').value;
        i.loading.set(true);
        i.stats.set([]);
        Meteor.callAsync('getBenchmarkStats', orgId).then((result) => {
            i.loading.set(false);
            const statsArray = Object.entries(result).map(([key, value]) => ({
                label: key,
                value
            }));
            i.stats.set(statsArray);
        });
    },
    'click #btnExport': async function(e, i) {
        const orgSelect = document.getElementById('statsOrg');
        const label = orgSelect.options[orgSelect.selectedIndex].text;
        exportTableToCSV.apply(this, [$('.stats-results table'), 'export-' + label + '.csv']);
    }
})