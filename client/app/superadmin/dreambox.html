<template name="dreambox">
	<div class="d-flex flex-row justify-content-between mb-4 mx-8">
		<h3>Dreambox Integrations</h3>
		<button class="btn btn-primary pull-right" id="btnAddDreamboxIntegration"><i class="fa fa-plus"></i> New
			Integration</button>
	</div>

	<div class="card card-custom mx-8">
		<div class="box-body table-responsive no-padding">
			<table class="table table-hover">
				<tbody>
					<tr>
						<th>Name</th>
						<th>Action</th>
					</tr>

					{{#each org in integratedOrgs}}
						<tr>
							<td>{{org.name}}</td>
							<td>
								<a class="edit-dreambox" style="cursor: pointer" data-id="{{org._id}}">Edit</a> |
								<a class="toggle-dreambox" style="cursor: pointer" data-id="{{org._id}}">{{# if dreamboxEnabled org._id}}Disable{{else}}Enable{{/if}}</a> |
								<a class="get-dreambox-file" style="cursor: pointer;" data-id="{{org._id}}">Get File</a> |
								<a class="manual-upload-dreambox" style="cursor: pointer;" data-id="{{org._id}}">Trigger Upload</a> |
								<a class="delete-dreambox" style="cursor: pointer;" data-id="{{org._id}}">Delete Integration</a>
							</td>
						</tr>
					{{/each}}
				</tbody>
			</table>
		</div>
	</div>
</template>

<template name="dreamboxIntegrationModal">
	<form id="formDreamboxIntegration">
		<div class="row">
			<div class="col-md-12">
				{{#if editOrgId}}
				<input type="hidden" name="orgId" value="{{editOrgId}}" />
				{{else}}
				<label>Root Org</label>
				<br/>
				<select name="orgId">
					{{#each org in rootOrgs}}
						<option value="{{org._id}}">{{org.name}}</option>
					{{/each}}
				</select>
				<br />
				{{/if}}

				<label>SFTP Host</label>
				<input type="text" class="form-control" name="host" value="{{host}}"><br />

				<label>SFTP Directory</label>
				<input type="text" class="form-control" name="dir" value="{{dir}}"><br />

				<label>SFTP User</label>
				<input type="text" class="form-control" name="username" value="{{username}}"><br />

				<label>SFTP Password</label>
				<input type="text" class="form-control" name="password" value="{{password}}"><br />

				{{#if editOrgId}}
				<h3>Staff Custom Fields</h3>
				<label>Title</label>
				<br/>
				<select name="staffTitle">
					{{#each field in staffFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.staffTitle field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />

				<label>School Student Id</label>
				<br/>
				<select name="staffSchoolId">
					{{#each field in staffFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.staffSchoolId field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />

				<label>Language</label>
				<br/>
				<select name="staffLanguage">
					{{#each field in staffFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.staffLanguage field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />

				<label>School Administrator</label>
				<br/>
				<select name="staffSchoolAdmin">
					{{#each field in staffFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.staffSchoolAdmin field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />

				<label>District Administrator</label>
				<br/>
				<select name="staffDistrictAdmin">
					{{#each field in staffFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.staffDistrictAdmin field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />
				<br />
				<br />
				<h3>Child Custom Fields</h3>
				<label>Gender</label>
				<br/>
				<select name="childGender">
					{{#each field in childFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.childGender field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />

				<label>Grade</label>
				<br/>
				<select name="childGrade">
					{{#each field in childFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.childGrade field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />

				<label>School Student Id</label>
				<br/>
				<select name="childSchoolId">
					{{#each field in childFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.childSchoolId field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />

				<label>Language</label>
				<br/>
				<select name="childLanguage">
					{{#each field in childFields}}
					<option value="{{field}}" {{selectedIfEqual dreamboxConfig.childLanguage field}} >{{field}}</option>
					{{/each}}
				</select>
				<br />
				{{/if}}
			</div>
		</div>
	</form>
</template>