import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import './fixWithdrawn.html';

Template.fixWithdrawn.created = function () {
    this.crmOrgs = new ReactiveVar([]);
    Meteor.callAsync('getCrmOrgs').then((result) => {
        console.log({ result });
        result.sort((a, b) => {
            return a.name.localeCompare(b.name);
        });
        this.crmOrgs.set(result);
    });
}

Template.fixWithdrawn.helpers({
    "crmOrgs": function () {
        return Template.instance().crmOrgs.get();
    },
});

Template.fixWithdrawn.events({
    'click #btnFixAll': async function(e, i) {
        const confirm = await mpSwal.fire({
            text: 'This will trigger fixing all withdrawn children with no schedules for all integrated sites. Are you sure?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes, fix all!'
        });
        if (confirm.isConfirmed) {
            mpSwal.fire('Processing. This will probably take a while.');
            Meteor.callAsync('fixAllWithdrawn').then((result) => {
                console.log({ result });
                mpSwal.fire({
                    text: 'Done!',
                    icon: 'success'
                });
            });
        }
    }
});