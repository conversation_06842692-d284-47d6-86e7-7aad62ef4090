import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import _ from '../../../lib/util/underscore';
import './permission.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { AvailablePermissions } from '../../../lib/constants/permissionsConstants';

Template.permission.onCreated( function() {
	var self=this;
	self.roleDefinition = new ReactiveVar();
	Meteor.callAsync("getRoles", { roleId: FlowRouter.current().params.roleId }).then((result) => {
		self.roleDefinition.set(result && result.length > 0 && result[0]);
	});
});

Template.permission.helpers({
	role() {
		return Template.instance().roleDefinition.get();
	},
	currentRules() {
		const currentRole = Template.instance().roleDefinition.get();
		
		return _.sortBy(currentRole?.rules, r => r.context);
	},
	availableContexts() {
		const currentRole = Template.instance().roleDefinition.get();
		const currentRoleContexts = currentRole && currentRole.rules.map(rule => rule.context);
		const defaultContextList = Object.values(AvailablePermissions);
		const availableContexts = currentRole && defaultContextList.filter(context => !currentRoleContexts.includes(context));
		return availableContexts;
	}
});

Template.permission.events({
	"click #btnAddContext"() {
		const currentRole = Template.instance().roleDefinition.get(),
			newContexts = $("#available-contexts").val(),
			newAction = $("#context-action").val();

		if (_.isEmpty(newContexts) || !newAction) return;
		if (!currentRole.rules) currentRole.rules = [];
		_.each(newContexts, newContext => {
			currentRole.rules.push({
				context: newContext,
				action: newAction
			});
	}	);
		Template.instance().roleDefinition.set(currentRole);
	},
	"click .btnRemoveContext"(e, i) {
		e.preventDefault();
		const currentRole = Template.instance().roleDefinition.get(),
			removeContext = $(e.currentTarget).data("context");
		
		currentRole.rules = currentRole.rules.filter( r=> r.context != removeContext);
		Template.instance().roleDefinition.set(currentRole);
	},
	"click #btnCancel"() {
		window.history.back();
	},
	"click #btnSave"() {
		const currentRole = Template.instance().roleDefinition.get();
		currentRole.label = $("input[name='role-description']").val();
		currentRole.passthroughWithoutMatchingRules = $("#checkbox-passthrough").is(":checked");
		currentRole.localAdminCanAssign = $("#checkbox-localassign").is(":checked");
		Meteor.callAsync("savePermissionsRole", currentRole).then((result) => {
			mpSwal.fire("Success", "Role successfully saved.", "success");
		}).catch((error) => {
			mpSwal.fire("Error", error.reason, "error");
		});
	}
});