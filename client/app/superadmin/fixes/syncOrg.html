<template name="syncOrg">
  <div id="syncModal" class="modal {{#if shouldFade}}fade{{/if}}" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Sync Org Status</h5>
          <div>
          <button
              id="syncAllOrgsButton"
              type="button"
              class="btn btn-outline-secondary font-weight-bold w-10 {{#if isSyncing}}disabled{{/if}}"
              disabled="{{isSyncing}}"
            >
              {{#if isSyncing}}
              <span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>
              {{/if}} Sync All Orgs Status
            </button>
            <div class="btn btn-icon btn-clean btn-lg" data-dismiss="modal" aria-label="Close" >
              <span class="fad-regular fad-primary fad fa-times"></span>
            </div>
          </div>
        </div>
        <div class="modal-body text-center">
          <div class="mb-4 mx-auto" style="width: 80%">

            <!-- Dropdown for selecting Org ID -->
            <select id="orgIdDropdown" class="form-control form-control-lg mb-3 text-center">
              <option value="">Select an Org</option>
              {{#each allCenters}}
              <option value="{{orgId}}">{{name}}</option>
              {{/each}}
            </select>

            <!-- Sync Single Org Button -->
            <button
              id="syncSingleOrgButton"
              type="button"
              class="btn btn-primary font-weight-bold w-100 {{#if isSyncing}}disabled{{/if}}"
              disabled="{{isSyncing}}"
            >
              {{#if isSyncing}}
              <span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>
              {{/if}} Sync Single Org Status
            </button>

            <!-- Divider -->
            <div class="my-3 border-top"></div>

            <!-- Sync All Orgs Button -->
            
            <!-- Sync result message area -->
            <div
              id="syncResultMessage"
              class="mt-3 
              {{#if syncResultType}}
                {{#if (eq syncResultType 'success')}}text-success
                {{else if (eq syncResultType 'error')}}text-danger
                {{else}}text-muted
                {{/if}}
              {{else}}text-muted
              {{/if}}"
            style="max-height: 150px; overflow-y: auto; padding: 10px; border-radius: 5px; white-space: pre-line;"
            >
              {{syncResult}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
