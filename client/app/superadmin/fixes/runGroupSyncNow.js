import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './runGroupSyncNow.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Log } from '../../../../lib/util/log';

Template.runGroupSyncNow.helpers({

});

Template.runGroupSyncNow.events({
    'click #fixesLink': async function (e) {
        e.preventDefault();
        FlowRouter.go('superAdminFixes');
    },
    'click #btnRunGroupSyncNow': async function (e, i) {
        const confirm = await mpSwal.fire({
            text: 'This will run the weekly Default Group sync job for the current week. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes'
        });
        if (confirm.isConfirmed) {
            Meteor.callAsync('runGroupSyncJob').then((response) => {
                Log.info('Group sync job completed');
                mpSwal.fire('Success', 'Group sync job completed', 'success');
            }).catch((error) => {
                Log.error(`Error running group sync job: ${error.message || error.reason}`);
                mpSwal.fire('Error', `Error running group sync job: ${error.message || error.reason}`, 'error');
            });
        }
    }
});
