import { Template } from 'meteor/templating';
import { Meteor } from 'meteor/meteor';
import './generateChargebackInvoices.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import moment from 'moment-timezone';

Template.generateChargebackInvoices.helpers({
    dateString() {
        return new moment().add(-1,"months").format('MMMM YYYY');
    }
});

Template.generateChargebackInvoices.events({
    'click #fixesLink': async function (e) {
        e.preventDefault();
        FlowRouter.go('superAdminFixes');
    },
    'click #btnGenerate': async function (e, i) {
        const confirm = await mpSwal.fire({
            text: 'This will generate chargeback invoices for all customers that failed to have invoices generated. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Yes'
        });
        if (confirm.isConfirmed) {
            Meteor.callAsync('runChargebacksInvoicing', {}).then((result) => {
                console.log({ result });
                mpSwal.fire('Alright. Invoices are being generated. Any failures will be returned in an email to the billing errors email.');
            });
        }
    }
});
