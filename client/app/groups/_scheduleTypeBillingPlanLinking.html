<template name="_scheduleTypeBillingPlanLinking">
  <div class="container">
    <div class="row mb-2">
      <div class="col-5">
        <label class="col-form-label">Schedule Type</label>
      </div>
      <div class="col-1"></div>
      <div class="col-5">
        <label class="col-form-label">Available Billing Plan</label>
      </div>
      <div class="col-1"></div>
    </div>
    {{#each row in rows}}
    <div class="row mb-4" data-row-id="{{row.id}}">
        <div class="col-5 d-flex align-items-center justify-content-center">
          <select class="form-control schedule-type text-center" data-row-id="{{row.id}}" data-cy="schedule-type-single-select">
            <option value="">Schedule Type</option>
            {{#each getScheduleTypes}}
            <option
              value="{{_id}}"
              {{disabledIfSelected _id}}
              {{isSelected _id row.scheduleType}}
            >
              {{type}}
            </option>
            {{/each}}
          </select>
        </div>

        <div class="col-1 d-flex align-items-center justify-content-center">
          <span class="fad fa-arrow-right" style="font-size: 1rem;"></span>
        </div>

      <div class="col-5">
        <select multiple data-cy="billing-plans-multi-select" id="billingPlanMultiSelect-{{row.id}}" name="billingPlanMultiSelect">
          {{#each billingPlans}}
          <option value="{{_id}}">{{description}}</option>
          {{/each}}
        </select>
        {{initializeMultiselect row.id}}
      </div>

      <div class="col-1 d-flex align-items-center">
        <button type="button"
                class="btn btn-icon btn-clean btn-sm"
                data-cy="delete-billing-scheduletype"
                data-action="delete-row"
                data-row-id="{{row.id}}">
          <span class="fad-regular fad-primary fad fa-times"></span>
        </button>
      </div>
    </div>
    {{/each}}
    <button class="btn border-0 shadow-none text-primary font-weight-bolder px-6 min-w-150px" id="addScheduleType" data-cy="add-schedule-type">
      <i class="fad fa-plus fa-lg mr-3 text-primary"></i>Add Schedule Type
    </button>
  </div>
</template>