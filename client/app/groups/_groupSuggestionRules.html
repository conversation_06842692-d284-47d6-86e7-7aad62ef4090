<template name="_groupSuggestionRules">
  <!-- Modal-->
  <div class="modal fade" id="_groupSuggestionRules" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg" role="document">
      <div class="modal-content min-h-400px">
        <div class="modal-header">
          <h5 data-cy="suggestion-rules" class="modal-title">{{group.name}} Suggestion Rules</h5>
          <button class="btn btn-primary font-weight-bolder btn-text-white mr-4" id="btnShowAddRule">
            <i class="fad-regular fad fa-plus fa-swap-opacity mr-2" style="color:#fff"></i>Add Rule
          </button>
        </div>
        <div class="modal-body">
          <div class="card-body">
            <div id="addGroupRuleForm" class="d-none">
              
              
              <form class="px-8 py-8 d-flex flex-row flex-wrap" id="add-rule-form">
                <div class="mr-12">
                  <div class="form-group">
                    <label for="rule-moment-type">Rule Type</label>
                    <select class="form-control" id="rule-type">
                        <option value="moment">Moment</option>
                        <option value="media">Media</option>
                    </select>
                  </div>
                </div>
                <div class="mr-12">
                  <div class="form-group" id="frm-moment-type">
                    <label for="rule-moment-type">Moment Type</label>
                    <select class="form-control" id="rule-moment-type">
                      {{#each mt in availableMomentTypeOptions}}
                        <option value="{{mt.momentType}}">{{mt.prettyName}}</option>
                      {{/each}}
                    </select>
                  </div>
                </div>
                <div class="mr-12">
                  <div class="form-group" id= "frm-threshold-hours">
                      <label for="rule-threshold-hours">Time</label>
                      <input type="text" class="form-control" id="rule-threshold" placeholder="#">
                      <select class="form-control" id="rule-frequency-type">
                        <option value="hours">Hours</option>
                        <option value="days">Days</option>
                      </select>
                  </div>
                </div>
                <div class="mr-12">
                  <div class="form-group d-none" id="frm-threshold-days">
                      <label for="rule-threshold-days"># Days for Required Media</label>
                      <input type="text" class="form-control" id="rule-threshold-days"> 
                  </div>
                </div>
                <div class="mr-12">
                  <div class="form-group d-none" id="frm-media-required">
                    <label for="rule-media-requireed">Number of Media Required</label>
                    <input type="text" class="form-control" id="rule-media-required">
                  </div>
                </div>
                <div class="mr-12">
                  <div class="form-group d-none" id="frm-media-absence-exclusion">
                    <label for="rule-media-number-days">Absence Exclusion (Days)</label>
                    <input type="text" class="form-control" id="rule-media-absence-days">
                  </div>
                </div>
                <div class="mr-12">
                  <button type="button" class="btn btn-primary" id="save-add-rule">Save</button>
                  <button type="button" class="btn btn-secondary" id="cancel-add-rule">Cancel</button>
                </div>
              </form>
            
            </div>
            {{#if group.mediaRequirement}}
            <div class="row vertical-align mb-4">
              <div class="col-3">
                <span class="h2 text-muted font-weight-bold">
                  Media Requirement
                </span>
              </div>
              <div class="col-4">
                <span class="h2 text-muted font-weight-bold">
                  {{group.mediaRequirement.mediaNumber}} media in {{group.mediaRequirement.mediaDays}} day(s) with a {{group.mediaRequirement.mediaAbsenceDays}} day absence exclusion
                </span>
              </div>
              <div class="col-3">
                <a href="#" class="removeMediaRequirementLink" data-activity-type="{{ group._id }}"><i class="h2 fad fa-times fad-primary"></i></a>
              </div>
            </div>
            {{/if}}
            {{#each gsr in group.groupSuggestionRules}}
              <div class="row vertical-align mb-4">
                <div class="col-3">
                  <span class="h2 text-muted font-weight-bold">
                    {{ prettyLabelFor gsr.momentType }}
                  </span>
                </div>
                <div class="col-4">
                  <span class="h2 text-muted font-weight-bold">
                    Threshold: {{ gsr.thresholdHours }} hours
                  </span>
                </div>
                <div class="col-3">
                  <a href="#" class="removeSuggestionRuleLink" data-activity-type="{{ gsr.momentType }}"><i class="h2 fad fa-times fad-primary"></i></a>
                </div>
              </div>
            {{/each}}
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary font-weight-bolder" data-dismiss="modal" data-cy="close-suggestion-rules">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>
