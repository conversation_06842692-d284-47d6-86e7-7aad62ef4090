import { Template } from 'meteor/templating';
import { Groups} from '../../../lib/collections/groups.js';
import '../moments/momentList.js';
import './_groupActivityModal.html';

Template._groupActivityModal.helpers({
  "group": function() {
    var groupId = Template.instance().data.groupId;
    var group = Groups.findOne({_id: groupId});
    return group;
  },
});

Template._groupActivityModal.events({
});

Template._groupActivityModal.onCreated( function() {
	const self=this;
})
