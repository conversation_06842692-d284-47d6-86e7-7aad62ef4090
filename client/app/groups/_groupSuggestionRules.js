import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Groups} from '../../../lib/collections/groups.js';
import { Orgs } from "../../../lib/collections/orgs.js";
import _ from '../../../lib/util/underscore';
import './_groupSuggestionRules.html';

Template._groupSuggestionRules.helpers({
  "group": function() {
    var groupId = Template.instance().data.groupId;
    var group = Groups.findOne({_id: groupId});
    return group;
  },
  "prettyLabelFor": function(momentType) {
    const org = Orgs.current();
    let availableTypes = [{momentType: "nameToFace", prettyName: "Name to Face"}];
    if (org && org.availableMomentTypes()) {
      return _.chain(availableTypes.concat(org.availableMomentTypes()))
        .find(function(md) { return md.momentType == momentType;})
        .result('prettyName')
        .value();
    }
  },
  "availableMomentTypeOptions": function() {
    const org = Orgs.current();
    let availableTypes = [{momentType: "nameToFace", prettyName: "Name to Face"}];
    if (org && org.availableMomentTypes()) {
      return availableTypes.concat(org.availableMomentTypes());
    }
  },
});

Template._groupSuggestionRules.events({
  "click #save-add-rule": function(event, template) {
    event.preventDefault();
    const groupId = Template.instance().data.groupId;
    const resultValue = {
      ruleType: $("#rule-type").val(),
      momentType: $("#rule-moment-type").val(),
      threshold: $("#rule-threshold").val(),
      thresholdType: $("#rule-frequency-type").val(),
      thresholdDays: $("#rule-threshold-days").val(),
      mediaNumber: $("#rule-media-required").val(),
      mediaAbsenceDays: $("#rule-media-absence-days").val()
    }

        Meteor.callAsync("insertGroupSuggestionRule", groupId, resultValue)
            .then(() => {
                resetRuleForm();
                $("#addGroupRuleForm").addClass("d-none");

                mpSwal.fire({
                    title: "Success",
                    text: "Your rule was created.",
                    icon: "success"
                })
            })
            .catch((error) => {
                mpSwal.fire({
                    title: "Error",
                    text: error.reason,
                    icon: "error"
                });
            });
  },
  "click .removeSuggestionRuleLink": function(event, template) {
		const groupId = Template.instance().data.groupId;
		const ruleType = $(event.currentTarget).data("activity-type");

		return mpSwal.fire({  
			title: "Are you sure?",   
			text: "This will permanently remove this suggestion rule from this group.",   
			icon: "warning",   
			showCancelButton: true,   
			confirmButtonText: "Yes, delete it!"
		}).then( async result => {   
			if (result.value) await Meteor.callAsync("deleteGroupSuggestionRule", groupId, ruleType);
		});
	},
  "click .removeMediaRequirementLink": function(event, template) {
		const groupId = Template.instance().data.groupId;
		return mpSwal.fire({  
			title: "Are you sure?",   
			text: "This will permanently remove this media requirement rule from this group.",   
			icon: "warning",   
			showCancelButton: true,   
			confirmButtonText: "Yes, delete it!"
		}).then( async result => {   
			if (result.value) await Meteor.callAsync("deleteGroupSuggestionRule", groupId, "mediaRequirement");
		});
	},
  "click #btnShowAddRule"() {
    $("#addGroupRuleForm").removeClass("d-none");
  },
  "change #rule-type"() {
    const val = $("#rule-type").val();
    if (val == "moment") {
      $("#frm-moment-type").removeClass("d-none");
      $("#frm-threshold-hours").removeClass("d-none");
      $("#frm-threshold-days").addClass("d-none");
      $("#frm-media-required").addClass("d-none");
      $("#frm-media-absence-exclusion").addClass("d-none");
    } else {
      $("#frm-moment-type").addClass("d-none");
      $("#frm-threshold-hours").addClass("d-none");
      $("#frm-media-required").removeClass("d-none");
      $("#frm-media-absence-exclusion").removeClass("d-none");
      $("#frm-threshold-days").removeClass("d-none");
    }
  },
  "click #cancel-add-rule"() {
    $("#addGroupRuleForm").addClass("d-none");
    resetRuleForm();
  }
});

Template._groupSuggestionRules.onCreated( function() {
	const self=this;

	// self.autorun(function() {
	// 	self.subscribe("thePeople", {invoiceId: self.data && self.data.groupId});
	// });
});

function resetRuleForm() {
  $('#addGroupRuleForm :input[type=text]','#addGroupRuleForm')
  .val('');
}