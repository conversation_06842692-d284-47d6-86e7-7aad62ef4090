import { Invoice, Invoices } from '../../../lib/collections/invoices';
import { Orgs } from '../../../lib/collections/orgs';
import { InvoiceUtils } from '../../../lib/util/invoiceUtils';
import './_invoiceDetailModal.html';

Template.invoiceDetailModal.onCreated(function () {
	this.targetOrg = new ReactiveVar();
	this.targetPerson = new ReactiveVar({});
	this.invoice = new ReactiveVar(null);
	const invoiceId = Template.currentData().invoiceId;
	Meteor.call("getInvoiceById", invoiceId, (err, invoice) => {
		if (err) {
			console.error("Error fetching invoice:", err);
			return;
		}
		this.invoice.set(new Invoice(invoice));

		const orgs = Orgs.findOne({_id: invoice.orgId}, {fields: {name: 1}});

		Meteor.callAsync("orgBusinessTaxId", {orgId: invoice.orgId})
			.then((result) => {
				this.targetOrg.set({
					name: orgs?.name,
					businessTaxId: result
				});
			})
			.catch((error) => {
				mpSwal.fire(error.reason);
			});

		Meteor.callAsync("getPeopleByQueryAndOptions",
			{_id: invoice.personId},
			{})
			.then((resPeopleList) => {
				this.targetPerson.set(resPeopleList.length && resPeopleList[0]);
			})
			.catch((err) => {
				console.log("Error while fetching invoiced person -", err);
			});
	});
});


Template.invoiceDetailModal.helpers({
	invoice() {
		return Template.instance().invoice.get();
	},
	targetPerson() {
		return Template.instance().targetPerson.get();
	},
	targetOrg() {
		return Template.instance().targetOrg.get();
	},
	originalAmountWithPayers() {
		return this.originalAmount + _.reduce(
			this.totalAmountsForPayers(),
			(memo, payer) => memo + payer.amount,
			0);
	},
	getBirthday(person) {
		const prefix = Orgs.current().profileDataPrefix();
		return prefix ? (person[prefix] && person[prefix].birthday) : person.birthday;
	},
	getTotalAmount(lineItem) {
		return InvoiceUtils.getPlanAmountTotal(lineItem);
	}
});

Template.invoiceDetailModal.events({
	"click #btnClose": () => {
		$("#invoiceDetailModal").modal("hide");
	}
});