<template name="invoiceDetailModal">
	<div id="invoiceDetailModal" class="modal">
		<div class="modal-dialog modal-dialog-scrollable modal-xl">
			<div class="modal-content">
			
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">×</span>
						</button>
						<h4 class="modal-title">Invoice Detail</h4>
				</div>
				<div class="modal-body">
					<b>Center Name:</b> {{targetOrg.name}}<br/>
					<b>EIN:</b> {{targetOrg.businessTaxId}}<br/>
					<br/>
					<b>Invoice Number:</b> {{invoice.invoiceNumber}}<br/>
					<b>Invoice Date:</b> {{invoice.invoiceDate}}<br/>
					<b>Due Date:</b> {{formatDate invoice.dueDate "M/DD/YYYY"}}<br/>
					<br/>
					<b>Invoice For:</b> {{targetPerson.lastName}}, {{targetPerson.firstName}}<br/>
					<b>Child's Date of Birth:</b> {{formatDate (getBirthday targetPerson) "MM/DD/YYYY"}}<br/>
					<br/>
					<b>Invoice Details:</b><br/>
					<br/>
					<table style="width:100%">
						<tr style="border-bottom:1px solid #aaa">
							<th>Description</th>
							<th>Qty</th>
							<th>Price</th>
							<th style="text-align:right">Amount</th>
						</tr>
						{{#each lineItem in invoice.lineItems}}
							{{#with lineItem}}

								{{#if trueIfEq type "item"}}
									<tr  style="border-bottom:1px solid #aaa">
										<td>
											{{originalItem.description}}{{#if notes}}<br/> - {{notes}}{{/if}} 
										</td>
										<td>
											{{quantity}}
										</td>
										<td>
											{{formatCurrency price}}
										</td>
										<td style="text-align:right">
											{{formatCurrency amount}}
										</td>
									</tr>
								{{else}}
									<tr>
										<td>amount
											{{description}} {{frequency}}<br/>
											{{#if coversPeriodDesc}} - {{coversPeriodDesc}}{{/if}}
										</td>
										<td></td> 
										<td>
											{{formatCurrency amount}} 	
										</td>
										<td></td>
									</tr>
								{{/if}}

								{{#if appliedDiscounts}}
									{{#each appliedDiscounts}}
									<tr>
										<td>
											- {{#if trueIfEq originalAllocation.discountType "coupon"}}{{originalAllocation.code}} Coupon Code{{else}}{{#if trueIfEq lineItem.type "item"}}{{originalAllocation.description}}{{else}}{{originalAllocation.allocationDescription}}{{/if}}{{/if}}
											{{#if voidedAt}} <i style="color:var(--primary)">voided</i>{{/if}}
										</td>
										<td></td>
										<td>({{formatCurrency amount}})</td>
										<td></td>
									</tr>
									{{/each}}
								{{/if}}
								<tr style="border-bottom:1px solid #aaa">
								    <td colspan=3>{{#if trueIfEq type "item"}}Item{{else}}Plan{{/if}} Total:</td>
								    <td style="text-align:right">{{formatCurrency (getTotalAmount this) }}</td>
								</tr>
							{{/with}}
						{{/each}}
						<tr>
							<td colspan=3><b>Invoice Total:</b></td>
							<td style="text-align:right"><b>{{formatCurrency invoice.originalAmount}}</b></td>
						</tr>
					</table>
				</div>
				<div class="modal-footer">
					
					<button class="btn btn-primary font-weight-bolder btn-md" id="btnClose">Close</button>

				</div>

			</div>
		</div>
	</div>
</template>
