<template name="billingReportInProgress">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">In-progress Payments and Refunds
				<span class="text-muted pt-2 font-size-sm d-block">Payments and refunds that have not yet settled.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				{{#if entries}}
					<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>				
				{{/if}}
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
					Update
				</div>
			</div>
		</div>
		<div class="card-body">
			<div class="row">

				<div class="col-md-3">
					
					
					<label class="control-label">Date:</label><br/>
					<div class="input-group date">
						<input type="text" class="form-control" name="date" value="{{todayDate}}">
						<div class="input-group-addon">
							<span class="glyphicon glyphicon-th"></span>
						</div>
					</div>
				
					
				</div>

				<div class="col-md-3">
					
						
					
				</div>
			</div>
			
			
			<br/>
			
			{{#if reportData}}
			<div class="row">
				<div class="col-md-12">
				
					<table class="table">
						<tr>
							<th>Type</th>
							<th>Transaction Date</th>
							<th>Invoice</th>
							<th>Reference</th>
							<th>Amount</th>
						</tr>
						{{#each reportData.refundsInProgress}}
						<tr>
							<td>Refund</td>
							<td>{{formatDate createdAt "MM/DD/YYYY"}}</td>
							<td><a href="/billing/invoices/{{invoiceId}}">{{invoiceNumber}}</a></td>
							<td>{{adyenInfo.pspReference}}</td>
							<td>{{formatCurrency amount}}</td>
						</tr>
						{{/each}}
						{{#each reportData.paymentsInProgress}}
						<tr>
							<td>Payment</td>
							<td>{{formatDate createdAt "MM/DD/YYYY"}}</td>
							<td><a href="/billing/invoices/{{invoiceId}}" target="_blank">{{invoiceNumber}}</a></td>
							<td>{{adyenInfo.pspReference}}</td>
							<td>{{formatCurrency amount}}</td>
						</tr>
						{{/each}}
						<tr>
							<td colspan=4 class="font-weight-bold">Total Refunds</td>
							<td>{{formatCurrency totals.refunds}}</td>
						</tr>
						<tr>
							<td colspan=4 class="font-weight-bold">Total Payments</td>
							<td>{{formatCurrency totals.payments}}</td>
						</tr>
						<tr>
							<td colspan=4 class="font-weight-bold">Net Total</td>
							<td>{{formatCurrency totals.net}}</td>
						</tr>
					</table>

				</div>
			</div>
			{{/if}}
		</div>
	</div>
</template>