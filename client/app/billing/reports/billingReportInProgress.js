import './billingReportInProgress.html';

Template.billingReportInProgress.events({
	"click #btnUpdate"(e,instance) {
		const options = {name:"inProgressPaymentsAndRefunds"};
		options.date = $("input[name='date']").val();
		$("#btnUpdate").html("<i class='fa fa-spin fa-spinner'></i> Updating").prop('disabled', true);
		Meteor.callAsync("getDashboardItem", options)
		.then((result) => {
			$("#btnUpdate").html("Update").prop('disabled', false);
			console.log("result", result);
			instance.reportData.set(result);
		})
		.catch((error) => {
			$("#btnUpdate").html("Update").prop('disabled', false);
			mpSwal.fire("Error", error.reason, "error");
		});
	}
});

Template.billingReportInProgress.helpers({
	todayDate() {
		return new moment().format("MM/DD/YYYY");
	},
	reportData() {
		return Template.instance().reportData.get();
	},
	totals() {
		const data = Template.instance().reportData.get();
		const totals = {
			refunds: _.reduce(data.refundsInProgress, (memo, refund) => memo + refund.amount, 0),
			payments: _.reduce(data.paymentsInProgress, (memo, payment) => memo + payment.amount, 0),
		};
		totals["net"] = totals.payments - totals.refunds;
		return totals;
	}
});

Template.billingReportInProgress.onRendered( function() {
	$(".input-group.date").datepicker({
		zIndexOffset:9999
	});
});

Template.billingReportInProgress.onCreated( function() {
	const self = this;
	self.reportData = new ReactiveVar();
});