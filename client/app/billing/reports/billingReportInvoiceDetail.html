<template name="billingReportInvoiceDetail">
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Invoice Detail Report
				<span class="text-muted pt-2 font-size-sm d-block">View and search invoices by payment status, payer, name, and date range.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				{{#if transactions}}
					<div data-cy="export-btn" class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>				
				{{/if}}
				{{#unless applyMode}}
					<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="update-btn">
						Update
					</div>				
				{{/unless}}
				<!--end::Button-->
			</div>
		</div>
		<div class="card-body">
			<form id="invoiceSearch">
				
				<div class="row">
					<div class="col-sm-3">
						
						<div class="form-group">
							<label class="control-label">Family Payment Status: </label><br/>
							<select data-cy="family-payment-status" name="filter_status" class="form-control">
								<option value="open">Open</option>
								<option value="pastdue">Past Due</option>
								<option value="all">All</option>
							</select> 
						</div>
					</div>
					<div class="col-sm-3 ">
						<div class="form-group">
							<label class="control-label">Start Date:</label><br/>
							<div class="input-group date">
								<input type="text" class="form-control" name="start_date" value="{{todayDate}}" data-cy="start-date-input">
								<div class="input-group-addon">
									<span class="glyphicon glyphicon-th"></span>
								</div>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label class="control-label">End Date:</label><br/>
							<div class="input-group date">
								<input data-cy="end-date-input" type="text" class="form-control" name="end_date" value="{{todayDate}}">
								<div class="input-group-addon">
									<span class="glyphicon glyphicon-th"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-3 col-xs-12">
						<div class="form-group">
							<label class="control-label">Filter by payer:</label><br/>
							<select data-cy="filter-by-payer" name="balance_by_payer" class="form-control">
								<option value=""></option>
								
								{{#each payerSources}}
								<option value="{{type}}">{{description}}</option>
								{{/each}}
							</select>
						</div>
						<div class="checkbox">
							<label>
								<input data-cy="open-payer-balances-only" type="checkbox" id="chkOpenPayerOnly"> Open payer balances only
							</label>
						</div>
					</div>
					<div class="col-sm-7 col-xs-12">
						<div class="form-group">
							<label class="control-label">Filter by name:</label><br/>
							<input data-cy="search-by-name" type="text" class="form-control" name="search_by_name">
						</div>
					</div>
				</div>
				
				<div class="row">
					<div class="col-sm-3">
						<div class="form-group">
							<label class="control-label">Sort by:</label><br/>
							<select data-cy="select-sort" name="sort" class="form-control">
								<option value="invoiceNumberDesc" selected>Invoice # (desc)</option>
								<option value="invoiceNumberAsc">Invoice # (asc)</option>
								<option value="nameAsc">Name</option>
								<option value="amountDesc">Open Amount (desc)</option>
								<option value="amountAsc">Open Amount (asc)</option>
								<option value="originalAmountDesc">Original Amount (desc)</option>
								<option value="originalAmountAsc">Original Amount (asc)</option>
							</select>
						</div>
					</div>
				</div>
				
				<div class="row">
					<div class="col-xs-12">

						{{#if transactions}}
						
						<br/><br/>
							{{#if showApplyMode}}
								{{#if applyMode}}
								<button type="button" class="btn btn-default btn-sm" id="btnApplyCancel">Close apply payments...</button><br/>
								<div class="row">
									<div class="col-xs-9 text-right">
										<b>Amount Expected: </b>
									</div>
									<div class="col-xs-2 text-right">
								 		{{formatCurrency applyPaymentsInfo.amountExpected}}
									</div>
								</div>
								<div class="row">
									<div class="col-xs-9 text-right">
										<b>Amount Posted: </b>
									</div>
									<div class="col-xs-2 text-right">
											({{formatCurrency applyPaymentsInfo.amountPosted}})
									</div>
								</div>
								<div class="row">
									<div class="col-xs-9 text-right">
										<b>Amount Open: </b>
									</div>
									<div class="col-xs-2 text-right">
											{{formatCurrency applyPaymentsInfo.openAmount}}
									</div>
								</div>
								{{else}}
								<button type="button" class="btn btn-default btn-sm" id="btnApplyMultiple">Apply Payments...</button>
								{{/if}}
							{{/if}}
						{{/if}}
					</div>
				</div>
			</form>
			<br/>
			{{#if subscriptionWaiting}}
			<div class="row">
				<div class="col-xs-12" style="text-align:center"><img src="/img/circle_spinner.gif"><br/></div>
			</div>
			{{/if}}
			{{#if transactions}}
			<div class="row">
				<div class="col-md-12">
					
					<h3 class='text-center'>Invoice Detail Report</h3>
					<h4 class='text-center'>Org: {{orgName}}</h4>
					
					<table class="table table-striped" id="dvData">
						<tbody>
							<tr>
								<th>#</th>
								<th>Date</th>
								<th>Description</th>
								<th>Amount</th>
								<th>Balance(s)</th>
								<th>Actions</th>
							</tr>

							{{#each transactions}}
							<tr>
								<td data-cy="invoice-number" style="width: 15px;padding-right:10px">
									{{#if showInvoiceDetailLink}}
										<a href="/billing/invoices/{{_id}}" target="_blank">{{invoiceNumber}}</a>
									{{else}}
										{{invoiceNumber}}
									{{/if}}
								</td>
								<td data-cy="invoice-date">{{invoiceDate}}</td>
								<td data-cy="invoice-description">{{personName}}<br/>
									<b>Type:</b> Plan Invoice (<a href="#" class="btnViewInvoiceDetail" data-id="{{_id}}">quick view</a>)
									{{#if coversPeriodDescription}}<br/><b>Covers:</b> {{coversPeriodDescription}}{{/if}}
									{{#if invoiceNotes}}<br/><b>Notes:</b> {{invoiceNotes}}{{/if}}
								</td>
								<td data-cy="invoice-amount">{{formatCurrency originalAmount}}<br/>
									{{#if trueIfEq invoiceStatus "open"}}
									<span class="collapse-label">Status:</span> Open 
									{{else if trueIfEq invoiceStatus "void"}}
									<span class="collapse-label">Status:</span> Void 
								{{else}}
									<span class="collapse-label">Status:</span> Paid (<a href="#" class="btnViewReceiptDetail" data-id="{{_id}}">view receipt</a>)
								{{/if}}

								</td>
								<td data-cy="invoice-balance">
									Family: {{formatCurrency openAmount}}<br/>
									{{#each payerInfo}}
										{{payerName}}: {{formatCurrency openAmount}} <br/>
									{{/each}}
								</td>
								<td>
									{{#unless applyMode}}
									<select class="form-control selectInvoiceAction" size=1 data-id="{{_id}}" data-cy="select-invoice-action">
									<option value="">Action...</option>
									{{#if showIssueCredit}}<option value="credit">Issue Credit</option>{{/if}}
									<option value="ledger">Invoice Ledger</option>
									{{#if showResendInvoice}}<option value="resend">Resend Invoice</option>{{/if}}
									</select>
									{{else}}
									<input type="text" class="applyAmount" data-id="{{_id}}" style="vertical-align:middle" value="{{formatNumber applyAmount '0.00'}}"><button class="btn btn-sm btn-primary btnApplyAmount"  data-id="{{_id}}">Apply</button>
									{{/unless}}
								</td>
							</tr>
							{{/each}}
						</tbody>
					</table>
				</div>
			</div>
			{{/if}}
		</div>
	</div>
</template>
