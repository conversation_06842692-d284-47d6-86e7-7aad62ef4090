<template name="billingReportPayouts">
    {{#if payoutsEnabled}}
    <div class="card card-custom">
        <div class="card-header flex-wrap border-0 pt-6 pb-0">
            <div class="card-title">
                <h3 class="card-label">Payouts Report
                    <span class="text-muted pt-2 font-size-sm d-block">View detailed payout information for credit card and ACH transactions processed through Lineleader.</span>
                </h3>
            </div>
            <div class="card-toolbar">
                <!--begin::Button-->
                {{#if transactions}}
                <div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport">
                    <i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
                </div>
                {{/if}}
                {{#unless isSavedMode}}
                <div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate">
                    Update
                </div>
                {{/unless}}
                <!--end::Button-->
            </div>
        </div>
        <div class="card-body">
            {{#if isSavedMode}}
            <div class="row font-weight-bolder text-dark my-4">
                <div class="col">
                    {{{ savedHeader }}}
                </div>
            </div>
            {{/if}}
            {{#unless isSavedMode}}
            <div class="row">
                <div class="col-md-3">
                    <form class="form">
                        <div class="form-group" style="margin-left:10px">
                            <label class="control-label">Date Start:</label><br/>
                            <div class="input-group date">
                                <input type="text" class="form-control" name="start_date" value="{{todayDate}}">
                                <div class="input-group-addon">
                                    <span class="glyphicon glyphicon-th"></span>
                                </div>
                            </div>
                        </div>
                    </form>
                    {{> reportQueueCheckbox }}
                </div>
                <div class="col-md-3">
                    <form class="form">
                        <div class="form-group" style="margin-left:10px">
                            <label class="control-label">Date End:</label><br/>
                            <div class="input-group date">
                                <input type="text" class="form-control" name="end_date" value="{{todayDate}}">
                                <div class="input-group-addon">
                                    <span class="glyphicon glyphicon-th"></span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-3">
                    <form class="form">
                        <div class="form-group" style="margin-left:10px">
                            <label>Org(s):</label><br/>
                            {{> reportOrgsField }}
                        </div>
                    </form>
                </div>
                <!--				<div class="col-md-3">-->
                <!--					<form class="form-inline">-->
                <!--						<div class="form-group" style="margin-left:10px">-->
                <!--							<label class="control-label">Type:</label><br/>-->
                <!--							<select name="type" class="form-control">-->
                <!--								<option value="">All</option>-->
                <!--								<option value="charge.succeeded">Charge Succeeded</option>-->
                <!--								<option value="charge.failed">Charge Failed</option>-->
                <!--							</select>-->
                <!--						</div>-->
                <!--					</form>-->
                <!--				</div>-->
            </div>
            <div class="row">
                <div class="col-md-12">
                    {{#if stripeLegacyEnabled}}
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" id="chkStripeLegacy"> View Stripe Legacy Data
                        </label>
                    </div>
                    {{/if}}

                </div>
            </div>
            {{/unless}}
            <br/>
            {{#if transactions}}

            <div class="row">
                <div class="col-md-12">

                    <h3 class='text-center'>Payouts Report</h3>
                    <h4 class='text-center'>Org: {{orgName}}</h4>

                    <table class="table table-striped" id="dvData">
                        <tbody>
                        <tr>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Ref #</th>
                            <th>Org</th>

                        </tr>

                        {{#each transactions}}
                        <tr>
                            <td>{{formatDate arrival_date "MM/DD/YYYY"}}</td>
                            <td>{{formatCurrency amount}}</td>
                            <td>{{id}}
                                <br/>
                                <a href="{{getViewDetailLink id ref type}}" data-id="{{id}}"
                                   data-org-id="{{orgId}}"
                                   data-ref-id="{{ref}}"
                                   class="btnViewDetail">view detail
                                </a>
                            </td>
                            <td>{{org}}</td>
                        </tr>
                        {{/each}}

                        </tbody>
                    </table>
                </div>
            </div>
            {{/if}}
        </div>
    </div>
    {{else}}
    <div class="box box-solid content-box">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12" style="text-align:center">
                    <h3>Payouts will display here when you add a bank account to billing settings.</h3>
                </div>
            </div>
        </div>
    </div>
    {{/if}}
</template>
