import { isMoment } from "moment";
import './billingReportUpcomingInvoices.html';
import { Orgs } from "../../../../lib/collections/orgs";

Template.billingReportUpcomingInvoices.onCreated( function () {
	this.results = ReactiveVar();
});

Template.billingReportUpcomingInvoices.onRendered( function () {
	$(".input-group.date").datepicker({
		zIndexOffset:9999
	});

});
Template.billingReportUpcomingInvoices.events({
	"click #btnUpdate": async function(e,i) {
		$(e.target).html('<i class="fa fa-spinner fa-spin"></i> Updating').prop("disabled", true);
		options = {};
		var self = Template.instance();
		options.startDate = $("input[name=start_date]").val();
		options.endDate = $("input[name=end_date]").val();
		options.includePendingItems = $("input[name='include-pending-items']").prop("checked");

		Meteor.callAsync("billingUpcomingBillingsReport", options)
		.then((result) => {
			$(e.target).html('Update').prop("disabled", false);
			self.results.set(result);
		})
		.catch((error) => {
			$(e.target).html('Update').prop("disabled", false);
			mpSwal.fire("Error", error.reason, "error");
		});

		await Meteor.callAsync('trackClientActivity', {
			label: "report-created",
			reportType: "billingUpcomingInvoices"
		});
		
	},

});

Template.billingReportUpcomingInvoices.helpers({
	defaultDates(option) {
		return option=="start" ? new moment().add(1,"day").format("MM/DD/YYYY") : new moment().add(2,"days").format("MM/DD/YYYY");
	},

	invoices() {
		return Template.instance().results.get();
	},
	
	noResults() {
		const results = Template.instance().results.get();
		return typeof results !== 'undefined' && results.length == 0;
	},
	orgName() {
		return Orgs.current() && Orgs.current().name;
	}
});
