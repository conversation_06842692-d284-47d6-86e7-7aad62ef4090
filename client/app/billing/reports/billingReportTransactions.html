<template name="billingReportTransactions">
	{{#if transactionsEnabled}}
	<div class="card card-custom">
		<div class="card-header flex-wrap border-0 pt-6 pb-0">
			<div class="card-title">
				<h3 class="card-label">Online Payments Transaction Report
				<span class="text-muted pt-2 font-size-sm d-block">View all processed credit card and ACH transaction details including charges, credits, and refunds.</span></h3>
			</div>
			<div class="card-toolbar">
				<!--begin::Button-->
				{{#if transactions}}
					<div class="btn btn-secondary font-weight-bolder btn-text-secondary mr-4" id="btnExport" data-cy="transaction-export-btn">
						<i class="fad-regular fad fa-download fa-swap-opacity mr-2 text-secondary"></i>Export CSV
					</div>				
				{{/if}}
				<div class="btn btn-primary font-weight-bolder btn-text-white" id="btnUpdate" data-cy="transaction-update-btn">
					Update
				</div>				
				<!--end::Button-->
			</div>
		</div>
		<div class="card-body" data-cy="transactions-card-body">
			<div class="row">
				<div class="col-md-3">
					<form class="form-inline">
						<div class="form-group" style="margin-left:10px">
							<label class="control-label">Date Start:</label>
							<div class="input-group date">
								<input type="text" class="form-control" name="start_date" value="{{todayDate}}" data-cy="transactions-start-date-inp">
								<div class="input-group-addon">
									<span class="glyphicon glyphicon-th"></span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="col-md-3">
					<form class="form-inline">
						<div class="form-group" style="margin-left:10px">
							<label class="control-label">Date End:</label>
							<div class="input-group date">
								<input type="text" class="form-control" name="end_date" value="{{todayDate}}" data-cy="transactions-end-date-inp">
								<div class="input-group-addon">
									<span class="glyphicon glyphicon-th"></span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="col-md-3">
					<form class="form-inline">
						<div class="form-group" style="margin-left:10px">
							<label class="control-label">Type:</label><br/>
							<select name="type" class="form-control" data-cy="transactions-type-select">
								<option value="">All</option>
								<option value="charge.succeeded">Charge Succeeded</option>
								<option value="charge.failed">Charge Failed</option>
								<option value="charge.refunded">Charge Refunded</option>
							</select>
						</div>
					</form>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					{{#if stripeLegacyEnabled}}
					<div class="checkbox">
						<label>
							<input type="checkbox" id="chkStripeLegacy"> View Stripe Legacy Data
						</label>
					</div>
					{{/if}}
					
				</div>
			</div>
			
			<br/>
			{{#if transactions}}
			<div class="row" data-cy="transaction-table-row">
				<div class="col-md-12">
					<h3 class='text-center'>Online Payments Transaction Report</h3>
					<h4 class='text-center'>Org: {{orgName}}</h4>
					<table class="table table-striped" id="dvData" data-cy="transactions-report-table">
						<tbody>
							<tr>
								<th>Date/Time</th>
								<th>Type</th>
								<th>Invoice</th>
								<th>Paid by</th>
								<th>Description</th>
								<th>Applied Amount</th>
								<th>Overpayment Amount</th>
								<th>Net Amount</th>
								
							</tr>

							{{#each transactions}}
							<tr>
								<td>{{formatDate created "MM/DD/YYYY hh:mm:ss a"}}</td>
								<td>{{type}}</td>
								<td><a href="/billing/invoices/{{invoiceId}}" target="_blank">{{invoiceNumber}}</a></td>
								<td>{{paidByDesc}}</td>
								<td>{{{desc}}}<br/>ID: {{id}}</td>
								<td>{{formatCurrency originalAmount}}</td>
								<td>{{formatCurrency overpaymentAmount}}</td>
								<td>{{formatCurrency amount}}</td>
							</tr>
							{{/each}}

							{{#if totalAmount}}
							<tr>
								<td colspan=7>Total</td>
								<td>{{formatCurrency totalAmount}}</td>
							</tr>
							{{else}}
							<tr>
								<td  colspan=8 style="text-align:center">To display a total, please select a transaction type</td>
							</tr>
							{{/if}}
						</tbody>
					</table>
				</div>
			</div>
			{{/if}}
		</div>
	</div>
	{{else}}
	<div class="box box-solid content-box">
		<div class="box-body">
			<div class="row">
				<div class="col-md-12" style="text-align:center">
					<h3>Transactions will display here when you add a bank account to billing settings.</h3>
				</div>
			</div>
		</div>
	</div>
	{{/if}}
</template>
