import { Invoice } from "../../../lib/collections/invoices";
import { processPermissions } from "../../../lib/permissions";
import { hideModal } from "../main";
import { BillingHelpers } from "./_billingHelpers";
import './_invoiceLedgerModal.html';

Template.invoiceLedgerModal.onCreated(function () {
	const self = this;
	this.targetPerson = new ReactiveVar({});
	this.invoice = new ReactiveVar(null);

	const invoiceId = self.data && self.data.invoiceId;

	Meteor.call("getInvoiceById", invoiceId, (err, invoice) => {
		if (err) {
			console.error("Error fetching invoice:", err);
			return;
		}
		self.invoice.set(new Invoice(invoice));

		Meteor.callAsync("getPeopleByQueryAndOptions",
			{ _id: invoice.personId },
			{ fields: { _id: 1, lastName: 1, firstName: 1 } })
			.then((resPeopleList) => {
				self.targetPerson.set(resPeopleList.length && resPeopleList[0]);
			})
			.catch((err) => {
				console.log("Error while fetching invoiced person -", err);
			});
	});
});

Template.invoiceLedgerModal.helpers({
	invoice() {
		return Template.instance().invoice.get();
	},
	targetPerson() {
		return Template.instance().targetPerson.get();
	},
	lineItems() {
		const invoice = Template.instance().invoice.get();
		if (invoice && invoice.lineItemDetail) {
			return invoice.lineItemDetail();
		}
	},
	showEdit() {
		return (this.type == "manual_payment");
	},
	showVoid() {
		return processPermissions({
			assertions: [{ context: "billing/payments/void", action: "edit" }],
			evaluator: (person) => person.type=="admin"
		}) && 
		 (this.type == "manual_payment" || this.payment_type == "credit_memo" || this.type == "reimbursable" || this.type == "other" || this.type == "payroll_deduction") && !this.voidedReason && !this.voidedAt && !this.refundedAt;
	},
	showManualRefund() {
		return processPermissions({
			assertions: [{ context: "billing/payments/refund", action: "edit" }],
			evaluator: (person) => person.type=="admin"
		}) && 
		this.type == "manual_payment" && !this.voidedReason && !this.voidedAt && !this.refundedAt;
	},
	showReverse() {
		return processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type ==="admin"
		}) && ['bad_debt', 'agency_write_off', 'reallocation_to_payer', 'reallocation_to_family', 'reallocation_to_collections', 'reallocation_to_bad-debt', 'reallocation_to_agency', 'collections_write_off'].includes(this.type) && !this.reversedAt;
	}
});

Template.invoiceLedgerModal.events({
	"click #btnClose": () => {
		hideModal("#invoiceLedgerModal");
	},
	"click .btnEditLineItem": async (e, i) => {
		const creditIndex = $(e.currentTarget).data("id");
		const invoice = Template.instance().invoice.get();
		hideModal("#invoiceLedgerModal");
		await BillingHelpers.showCreditModal(invoice._id, invoice, null, creditIndex);
	},
	"click .btnVoidLineItem": (e, i) => {
		const creditIndex = $(e.currentTarget).data("id");
		const invoice = Template.instance().invoice.get();
		hideModal("#invoiceLedgerModal");
		BillingHelpers.showVoidCreditLineModal(invoice._id, creditIndex);
	},
	"click .btnManualRefundLineItem"(e, i) {
		const creditIndex = $(e.currentTarget).data("id");
		const invoice = Template.instance().invoice.get();
		BillingHelpers.showManualRefundCreditLineModal(invoice._id, creditIndex);
	},
	"click .btnReverseLineItem"(e, i) {
		e.preventDefault();
		const invoice = Template.instance().invoice.get();
		const options = {
			invoice,
			creditIndex: this.i ?? null,
			allocationIndex: this.ai ?? null,
			createdInvoice: this.createdInvoice ?? null
		};
		BillingHelpers.showReverseLineItemModal(options);
	},
});