import './queuedPayments.html'
import { showModal } from "../main";
import '../simpleModal/simpleModal';

Template.queuedPayments.onCreated( function() {
	var self = this;
	self.batchData = new ReactiveVar();
	getData( self );
});

Template.queuedPayments.helpers({
	batchData() {
		return Template.instance().batchData.get();
	},
	sumTotalInDollars(amountInCents) {
		return amountInCents / 100;
	}
});

Template.queuedPayments.events({
	"click #btnGenerateFile"() {
		Meteor.callAsync("getOrgAdditionalData", {path: "achGeneration"})
		.then((dataResult) => {
			showModal("simpleModal", {
				title: "Generate Payments File",
				template: "queuedPaymentsGenerateFileModal",
				data: dataResult,
				onSave: (saveEvent, formInstance, formFieldData) => {
					formFieldData.orgIds = $("#reportOrgs").val();
					Meteor.callAsync("generateAchFile", {...formFieldData})
					.then((result) => {
						mpSwal.fire("File generation requested. It will be emailed to you once it's complete.");
						$("#simpleModal").modal("hide");
					})
					.catch((error) => {
						$(saveEvent.target).html('Save').prop("disabled", false);
						mpSwal.fire("Error requesting ACH file", error.reason, "error");
					});
				}
			});
		})
		.catch((dataError) => {
			mpSwal.fire("Error", dataError.reason, "error");
		});
	}
	
});

function getData(instance) {
	Meteor.callAsync("getQueuedPaymentsInfo")
	.then((result) => {
		instance.batchData.set(result);
	})
	.catch((error) => {
		mpSwal.fire("Error", "There was an issue retrieving batch payment data: " + error.reason, "error");
	});
}

Template.queuedPaymentsGenerateFileModal.onCreated( function() {

	var self = this;
	self.emailStatus = new ReactiveVar( "Not sent" );
});

Template.queuedPaymentsGenerateFileModal.events({
	"click #btn-send-auth-code"(event) {
		event.preventDefault();
		const i = Template.instance();
		i.emailStatus.set("Sending email");
		Meteor.callAsync("sendTwoFactorCode", null)
		.then((result) => {
			i.emailStatus.set("Email sent!");
		})
		.catch((error) => {
			mpSwal.fire("Error", error.reason, "error");
		});
	}
});

Template.queuedPaymentsGenerateFileModal.helpers({
	emailStatus() {
		return Template.instance().emailStatus.get();
	}
})