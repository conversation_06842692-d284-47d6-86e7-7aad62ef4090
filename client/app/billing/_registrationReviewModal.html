<template name="registrationReviewModal">
    <div id="registrationReviewModal" class="modal">
        <div class="modal-dialog modal-dialog-scrollable modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Registration Review</h4>
                    <div class="btn btn-icon btn-clean btn-lg" id="btnClose">
                        <span class="fad-regular fad-primary fad fa-times"></span>
                    </div>
                </div>
                <div class="modal-body">
                    <h3>Contacts</h3>
                    {{# each contact in contacts }}
                        <div class="row">
                            <div class="col">
                                <label>First Name</label>
                                <input
                                        type="text"
                                        value="{{contact.firstName}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <label>Last Name</label>
                                <input
                                        type="text"
                                        value="{{contact.lastName}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <label>Relationship</label>
                                <input
                                        type="text"
                                        value="{{contact.relationshipDescription}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 col-md-6">
                                <label>Primary Phone</label>
                                <input
                                        type="text"
                                        value="{{contact.phonePrimary}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                            <div class="col-sm-12 col-md-6">
                                <label>Alternative Phone</label>
                                <input
                                        type="text"
                                        value="{{contact.phoneAlt}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <label>Email</label>
                                <input
                                        type="text"
                                        value="{{contact.profileEmailAddress}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <label>Address</label>
                                <input
                                        type="text"
                                        value="{{contact.address}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 col-md-6">
                                <label>City</label>
                                <input
                                        type="text"
                                        value="{{contact.city}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                            <div class="col-sm-12 col-md-6">
                                <label>State</label>
                                <input
                                        type="text"
                                        value="{{contact.state}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 col-md-6">
                                <label>Zip</label>
                                <input
                                        type="text"
                                        value="{{contact.zip}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <strong>Primary Caregiver: </strong>{{contact.primaryCaregiver}}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <strong>Authorized Pickup: </strong>{{contact.authorizedPickup}}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <strong>Emergency Contact: </strong>{{contact.emergencyContact}}
                            </div>
                        </div>
                        <hr>
                    {{/ each }}
                    {{#if email}}
                        <div class="row pb-4" style="border:1px solid var(--primary); border-radius: 5px; padding:5px">
                            <div class="col-6">
                                <label>District Employee Email</label>
                                <input
                                        id="districtEmail"
                                        type="text"
                                        value="{{email}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                            <div class="col-6 d-flex align-content-end justify-content-center pt-8">
                                <button data-cy="approve-discount-district" id="validateEmail" class="btn btn-primary font-weight-bolder mx-2">Approve Discount</button>
                                <button data-cy="deny-discount-district" id="denyEmail" class="btn btn-secondary font-weight-bolder mx-2">Deny Discount</button>
                            </div>
                        </div>
                    {{/if}}
                    <hr>
                    <h2>Children</h2>
                    {{#each child in children}}
                        <div class="row">
                            <div class="col">
                                <label>First Name</label>
                                <input
                                        type="text"
                                        value="{{child.firstName}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <label>Last Name</label>
                                <input
                                        type="text"
                                        value="{{child.lastName}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <label>Birthday</label>
                                <input
                                        type="text"
                                        class="form-control rounded"
                                        value="{{formatDate child.birthday 'MM/DD/YYYY'}}"
                                        readonly
                                >
                            </div>
                            <div class="col">
                                <label>Grade</label>
                                <input
                                        type="text"
                                        value="{{child.studentGrade}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <label>Gender</label>
                                <input
                                        type="text"
                                        value="{{child.gender}}"
                                        class="form-control rounded"
                                        readonly
                                />
                            </div>
                        </div>
                        <h3 class="mt-5">Programs</h3>
                        {{# each program in programs @index}}
                            <div class="ml-5 mt-5">
                                <h4>{{program.name}}</h4>
                                {{#each plan in program.plans}}
                                    {{#unless checkIfItemOrPunchcard plan.type}}
                                        <div class="row">
                                            <div class="col">
                                                <strong>Plan: </strong>{{plan.description}}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col">
                                                <strong>Frequency: </strong>{{getFrequencyLabel plan.frequency}}
                                            </div>
                                        </div>
                                        {{# if isSelectiveWeeksPlan plan }}
                                            <div class="row">
                                                <div class="col">
                                                    <strong>Desired Weeks: </strong>
                                                    <div class="row">
                                                        <div class="col">
                                                            <ul>
                                                                {{# each week in plan.selectedWeeks }}
                                                                    <li>
                                                                        Week {{ incremented week }}: {{ getWeekDateRange plan week }}
                                                                    </li>
                                                                {{/ each }}
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {{ else }}
                                            <div class="row">
                                                <div class="col">
                                                    <strong>Desired Start Date: </strong>{{plan.startDate}}
                                                </div>
                                            </div>
                                        {{/ if }}
                                        <div class="row">
                                            <div class="col">
                                                <strong>Selected Days: </strong>{{selectedDaysText plan.selectedDays}}
                                            </div>
                                        </div>
                                        {{# unless isSelectiveWeeksPlan plan }}
                                            <div class="row">
                                                <div class="col-3">
                                                    <button data-id="{{child.childIdx}}-{{@index}}"
                                                            class="edit-plan-btn btn btn-block btn-primary font-weight-bolder my-2">
                                                        Edit
                                                    </button>
                                                </div>
                                            </div>
                                        {{/ unless }}
                                    {{/unless}}
                                {{/each}}
                            </div>
                        {{/each}}
                        {{#if childHasVoucher child @index}}
                            <h3 class="mt-5">Voucher information</h3>
                            <form id="formVoucher{{@index}}"
                                 style="border:1px solid var(--primary); border-radius: 5px; padding:5px">
                                <div class="form-group row mt-5">
                                    <label class="col-xl-3 col-lg-3 text-right col-form-label">Voucher Name</label>
                                    <div class="col-lg-6 col-md-9 col-sm-12">
                                        <input type="text" value="{{child.agencyIdentifier}}" class="form-control rounded voucher-name-{{@index}}"/>
                                    </div>
                                </div>
                                <div class="form-group row mt-5">
                                    <label class="col-xl-3 col-lg-3 text-right col-form-label">Voucher Number</label>
                                    <div class="col-lg-6 col-md-9 col-sm-12">
                                        <input type="text" value="{{child.subsidyCode}}" class="form-control rounded voucher-number-{{@index}}"/>
                                    </div>
                                </div>
                                <div class="form-group row mt-5">
                                    <label class="col-xl-3 col-lg-3 text-right col-form-label">Payer/Discount
                                        Type</label>
                                    <div class="col-lg-6 col-md-9 col-sm-12">
                                        <select name="allocation_type" class="form-control" data-id={{@index}}>
                                            <option value="reimbursable" selected="{{trueIfEq (getPrefill 'sub-type' child) 'reimbursable'}}">Other Payer - Specify Subsidy Amount</option>
                                            <option value="reimbursable-with-copay" selected="{{trueIfEq (getPrefill 'sub-type' child) 'reimbursable-with-copay'}}">Other Payer - Specify Family Copay
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                {{#if isCopay @index}}
                                    <div class="form-group row">
                                        <label class="col-xl-3 col-lg-3 text-right col-form-label">Responsible Family
                                            Member</label>
                                        <div class="col-lg-6 col-md-9 col-sm-12">
                                            <select name="allocation_family_member" class="form-control"
                                                    data-id={{@index}}>
                                                <option></option>
                                                {{#each member in availableFamilyMembers @index}}
                                                    <option selected="{{trueIfEq (getPrefill 'responsible-party' child) @index}}">{{member.firstName}} {{member.lastName}}</option>
                                                {{/each}}
                                            </select>
                                        </div>
                                    </div>
                                {{/if}}
                                    <div class="form-group row">
                                        <label class="col-xl-3 col-lg-3 text-right col-form-label">Voucher Starts (blank
                                            for none):</label>
                                        <div class="col-lg-6 col-md-9 col-sm-12">
                                            <input type="text" name="startDate" class="form-control date-picker" value="{{getPrefill 'start-date' child}}">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-xl-3 col-lg-3 text-right col-form-label">Voucher Ends (blank
                                            for none):</label>
                                        <div class="col-lg-6 col-md-9 col-sm-12">
                                            <input type="text" name="endDate" class="form-control date-picker" value="{{getPrefill 'end-date' child}}">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-xl-3 col-lg-3 text-right col-form-label">{{#if isCopay @index}}Copay{{/if}} Amount</label>
                                        <div class="col-lg-6 col-md-9 col-sm-12">
                                            <input data-cy="amount-voucher" type="text" class="form-control" name="discount_amount" value="{{getPrefill 'amount' child}}"
                                                   pattern="[0-9]+(\.[0-9][0-9]?)?"><br/>
                                            <div class="radio-inline">
                                                <label class="radio radio-primary">
                                                    <input data-cy="dollar-amount" type="radio" name="amount_type" value="dollars" checked="{{trueIfEq (getPrefill 'amount-type' child) 'dollars'}}"/>
                                                    <span></span>
                                                    Dollars
                                                </label>
                                                <label class="radio">
                                                    <input data-cy="percent-amount" type="radio" name="amount_type" value="percent" checked="{{trueIfEq (getPrefill 'amount-type' child) 'percent'}}"/>
                                                    <span></span>
                                                    Percent
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-xl-3 col-lg-3 text-right col-form-label">{{#unless isCopay @index}}Other {{/unless}}Payer</label>
                                        <div class="col-lg-6 col-md-9 col-sm-12">
                                            <select data-cy="reimbursement-type-select" name="allocation_reimbursement_type" class="form-control">
                                                <option value=""></option>
                                                {{#each availableReimbursementTypes}}
                                                    <option value="{{type}}" selected="{{trueIfEq (getPrefill 'payer' child) type}}">{{description}}</option>
                                                {{/each}}
                                            </select>
                                        </div>
                                    </div>
                            </form>
                        {{/if}}
                        <hr>
                    {{/each}}
                    {{#unless email }}
                        <hr>
                        <div class="row">
                            <div class="col-auto offset-1">
                                <div class="checkbox-list">
                                    <label class="checkbox checkbox-primary">
                                        <input type="checkbox" class="form-check-input" id="chkRegFee" checked>
                                        <span></span>
                                        Charge Registration Fee (if Applicable)
                                    </label>
                                </div>
                            </div>
                        </div>
                    {{/unless}}
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button data-cy="approve-btn" class="btn btn-primary font-weight-bolder btn-md" id="btnAccept">Approve</button>
                    <button data-cy="deny-btn" class="btn btn-secondary font-weight-bolder btn-md" id="btnDeny">Deny</button>
                </div>
            </div>
        </div>
    </div>
</template>
