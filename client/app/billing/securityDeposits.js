import { getPeopleById } from "../../services/peopleMeteorService";
import { hideModal, showModal } from "../main";
import './securityDeposits.html';
import '../simpleModal/simpleModal';

Template.securityDeposits.onCreated(function() {
	var self = this;
	self.securityDepositsData = new ReactiveVar();
	self.showResolved = new ReactiveVar();
});

Template.securityDeposits.onRendered(function() {
	$('#withdrawalsStartDate').datepicker({autoclose:true});
	$('#withdrawalsEndDate').datepicker({autoclose:true});
	var self = this;
	callGetSecurityDeposits(self, moment().format("MM/DD/YYYY"), moment().add(30, "days").format("MM/DD/YYYY"), false);
});

function callGetSecurityDeposits(instance, passedStartDate, passedEndDate, passedShowResolved) {
	const startDate = passedStartDate || $("#withdrawalsStartDate").val(),
		endDate = passedEndDate || $("#withdrawalsEndDate").val(),
		showResolved = passedShowResolved || $("#showResolved").prop("checked"),
		filterBy = $("input[name='filter-by']:checked").val();

	instance.showResolved.set(showResolved);
	$("#btnUpdate").html("Updating...").prop("disabled", true);
	Meteor.callAsync("billingSecurityDepositsReport", {
		startDate,
		endDate,
		showResolved,
		showAll: filterBy == "all"
	})
	.then((result) => {
		$("#btnUpdate").html("Update").prop("disabled", false);
		instance.securityDepositsData.set(result);
	})
	.catch((error) => {
		$("#btnUpdate").html("Update").prop("disabled", false);
		instance.securityDepositsData.set(undefined);
	});
}

Template.securityDeposits.helpers({
	formattedStartDate() {
		return moment().format("MM/DD/YYYY");
	},
	formattedEndDate() {
		return moment().add(30, "days").format("MM/DD/YYYY");
	},
	withdrawals() {
		return Template.instance().securityDepositsData.get();
	},
	showResolved() {
		return Template.instance().showResolved.get();
	},
	showResolveAction() {
		return !this.resolvedAmount || this.resolvedAmount == 0;
	},
	sumOf(field) {
		const data = Template.instance().securityDepositsData.get();
			amount = data && _.reduce(data, (memo, wd) => memo + (wd[field] || 0), 0);
		return amount;
	}
});

Template.securityDeposits.events({
	async "click .btnApplyTo"(e, instance) {
		const self = this,
			personId = self._id,
			creditMemo = self;
		
		showModal("simpleModal", {
			title:"Apply Security Deposit",
			template: "personBillingApplyDeposit",
			data: {
				invoices: await self.openInvoices,
				creditMemo
			},
			onSave: (e, i, formFieldData) => {
				formFieldData.personId = personId;
				formFieldData.creditMemoId = creditMemo.creditMemoId;
				Meteor.callAsync("applySecurityDeposit", formFieldData)
				.then((result) => {
					hideModal("#simpleModal");
					mpSwal.fire("Success", "Security deposit successfully applied.", "success");
					callGetSecurityDeposits(instance);
				})
				.catch((error) => {
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		})
	},
	"click .btnMarkResolved"(e, instance) {
		const self = this,
			personId = self._id,
			invoiceId = self.invoiceId,
			creditMemoId = self.isPhantomDeposit && self.creditMemoId;

		console.log("self", self);

		getPeopleById(personId).then(person => {
			Meteor.callAsync("resolveSecurityDeposit", {personId, invoiceId, checkStatus: true, manualResolveCreditMemoId: creditMemoId})
			.then((result) => {
				console.log("result", result)
				showModal("simpleModal", {
					title: "Resolve Security Deposit",
					template: "billingResolveSecurityDeposit",
					data: {
						openCreditMemos: result.openSecurityDepositCredits,
						person
					},
					onSave: (e, i, formFieldData) => {
						Meteor.callAsync("resolveSecurityDeposit", {personId, invoiceId, manualResolveCreditMemoId: creditMemoId, action: formFieldData.action, notes: formFieldData.notes})
						.then((result) => {
							$("#simpleModal").modal("hide");
							callGetSecurityDeposits(instance);
						})
						.catch((error) => {
							$(e.target).html('Submit').prop("disabled", false);
							mpSwal.fire("Error", error.reason, "error");
						});
					}
				});
			})
			.catch((error) => {
				mpSwal.fire("Error", error.reason, "error");
			})
		}).catch(err => {
			console.log(err);
		});
	},
	"click #btnUpdate"(e, instance) {
		callGetSecurityDeposits(instance);
	}
});