import { Orgs } from "../../../lib/collections/orgs";
import { AvailableCustomizations } from "../../../lib/customizations";
const moment = require('moment-timezone');
import './billingAdminSettings.html';
import _ from "../../../lib/util/underscore";
import './_billingAdminDiscountsAndPayers';
import './_billingAdminPaymentSettings';
import './_billingRegFee';
import './_dropInFeeBuffer';
import { showModal } from "../main";
import { processPermissions } from "../../../lib/permissions";
import '../simpleModal/simpleModal';

Template.billingSettingsModalSchedule.rendered = function() {
	$("#generateBiWeeklyDateField").datepicker({
		autoclose: true,
		todayHighlight: true,
		// daysOfWeekDisabled: [0, 2, 3, 4, 5, 6],
		endDate: moment.tz(Orgs.current().getTimezone()).format("MM/DD/YYYY")
	});
}
Template.billingSettingsModalSchedule.helpers({
	"monthdays"() {
		return _.range(1,32);
	},
	getRegistrationFlowCustomizationName() {
		return AvailableCustomizations.REGISTRATION_FLOW;
	},
});

Template.billingSettingsModalOptions.rendered = function() {
	$("#registrationDiscountTypes").selectize({
		placeholder: 'Choose which discount types can be applied at check out.',
		plugins: ['remove_button']
	});
}

Template.billingSettingsModalOptions.helpers({
	showEmployeeId() {
		return Orgs.current().hasCustomization(AvailableCustomizations.SHOW_EMPLOYEE_ID_PLR);
	},
	"discountsAllowed"() {
		return Orgs.current().familyRegistrationSettings.discountsAllowed
	},
})

Template.billingAdminSettings.created = function() {
	this.showTimePeriods = new ReactiveVar(false);
}

Template.billingAdminSettings.helpers({
	showTimePeriodsConfig() {
		return Template.instance().showTimePeriods.get();
	},
	billingInfo() {
		if (!Orgs.current()) return;
		const orgBillingData = Orgs.current().billing;
		let messageColor = "#AA0000";
		switch(Orgs.current().billingStatus().status) {
			case "active": case "invoice_only": messageColor = "#00AA00"; break;
			case "bank_issue": case "missing_account": messageColor = "#f39c12"; break;
		}
		const todayStamp = new moment().startOf("day").valueOf();
		const result =  {
			legalEntity: _.deep(orgBillingData, "legalEntity"),
			billingStatus: Orgs.current().billingStatus(),
			messageColor: messageColor,
			generateWhenLabel: "In " + (_.deep(orgBillingData, "scheduling.generateWhen") || "").capitalizeFirstLetter(),
			generateDayLabel: (_.deep(orgBillingData, "scheduling.generateDay") || "").capitalizeFirstLetter(),
			generateMonthDay: _.deep(orgBillingData, "scheduling.generateMonthDay"),
			generateBiWeeklyDate: _.deep(orgBillingData, "scheduling.generateBiWeeklyDate"),
			nextBiWeeklyDate: _.deep(orgBillingData, "scheduling.generateBiWeeklyDate") && Orgs.current().nextBiWeeklyBillingTimestamp(),
			gracePeriodDays: _.deep(orgBillingData, "scheduling.gracePeriodDays"),
			lateFee: _.deep(orgBillingData, "scheduling.lateFee"),
			missedInvoiceInterval: _.deep(orgBillingData, "scheduling.missedInvoiceInterval"),
			toplinePercentDiscounts: _.deep(orgBillingData, "toplinePercentDiscounts"),
			allowDepositReportEdit: _.deep(orgBillingData, "allowDepositReportEdit"),
			suspendInvoicing: _.deep(orgBillingData, "suspendInvoicingIndefinitely") || (_.deep(orgBillingData, "suspendInvoicingUntilDate") && _.deep(orgBillingData, "suspendInvoicingUntilDate") > todayStamp),
			suspendInvoicingUntilDate: _.deep(orgBillingData, "suspendInvoicingUntilDate"),
			allowPaymentsBeforeDueDate: !(_.deep(orgBillingData, "scheduling.disablePaymentsBeforeDueDate")) ? "Yes" : "No",
			generateWeeklyDescription: "Invoices generated on " + (_.deep(orgBillingData, "scheduling.generateDay") || "").capitalizeFirstLetter() + "s and due " + 
				(_.deep(orgBillingData, "scheduling.weeklyPlanDueDay") ? 
					"the next occuring " + _.deep(orgBillingData, "scheduling.weeklyPlanDueDay").capitalizeFirstLetter() :
					"after " + _.deep(orgBillingData, "scheduling.gracePeriodDays") + " days"),
			generateMonthlyDescription: "Invoices generated on the " + getNumberWithOrdinal(_.deep(orgBillingData, "scheduling.generateMonthDay") || "") + " and due " + 
				(_.deep(orgBillingData, "scheduling.monthlyPlanDueDay") ? 
					"the next occuring " + getNumberWithOrdinal(_.deep(orgBillingData, "scheduling.monthlyPlanDueDay")) :
					"after " + _.deep(orgBillingData, "scheduling.gracePeriodDays") + " days")
		};
		result.automaticLateFees = {
			active: _.deep(orgBillingData, "scheduling.assessLateFee"),
		}
		if (result.automaticLateFees.active) {
			const lateFeeItem = _.find(orgBillingData.plansAndItems, pi => pi._id == _.deep(orgBillingData, "scheduling.assessLateFeeItemId"));
			if (lateFeeItem) result.automaticLateFees.lateFeeItem = lateFeeItem.description;
			result.automaticLateFees.lateFeeDays = _.deep(orgBillingData, "scheduling.assessLateFeeDays");
		}		
		return result;
	},
	showDisableButton() {
		if (!Orgs.current()) 
			return false;
		else 
			return (
				_.contains(["active", "bank_issue", "missing_account", "invoice_only"], Orgs.current().billingStatus().status) &&
				processPermissions({
					assertions: [{ context: "billing/configuration/system", action: "edit"}],
					evaluator: (person) => person.type=="admin"
				})
			);

	},
	showRunInvoices() {
		return Meteor.user() && Meteor.user().fetchPerson().superAdmin;
	},
	availableBillingMaps() {
		const o = Orgs.current();
		return o.billingMaps() && _.map( o.billingMaps(), (bm, k) => {
			bm.code = k;
			return bm;
		});
	},
	showEditOptions() {
		return processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit"}],
			evaluator: (person) => person.type=="admin"
		})
	},
	getRegistrationFlowCustomizationName() {
		return AvailableCustomizations.REGISTRATION_FLOW;
	},
	availablePrograms() {
		return (Orgs.current() && Orgs.current().availablePrograms(true));
	},
	availableTimePeriods() {
		return (Orgs.current() && Orgs.current().availableTimePeriods());
	},
	getProgramStatus(isActive) {
		return isActive ? 'Active' : 'Inactive';
	},
	getProgramRequiresAdvanceNotice(isRequiredAdvanceNotice) {
		return isRequiredAdvanceNotice ? 'X' : '';
	},
	formatDateToString(date) {
		const timezone = Orgs.current() && Orgs.current().getTimezone();
		return moment.tz(date, timezone).format('YYYY-MM-DD');
	}
});


Template.billingAdminSettings.events({
	"click #btnConfigureTimePeriods": (e, i) => {
		i.showTimePeriods.set(!i.showTimePeriods.get());
	},
	"click #btnEditLegalEntity": () => {
		const data = _.deep(Orgs.current(), "billing.legalEntity") || {};
		Meteor.callAsync("getBusinessTaxId")
		.then((result)=>{
			data.business_tax_id = result;
			showModal("simpleModal", { 
				title:"Legal Entity", 
				template: "billingSettingsLegalEntity",
				data,
				onSave: (e, i, formFieldData) => {
					Meteor.callAsync("updateBillingLegalEntity", formFieldData)
					.then((result)=>{
						$("#simpleModal").modal("hide");
					})
					.catch((error)=>{
						$(e.target).html('Save').prop("disabled", false);
						mpSwal.fire("Error", error.reason, "error");
					});
				}
			},"#simpleModal");
		})
		.catch((error)=>{
			mpSwal.fire(error.reason);
		});
		
	},
	"click .btnEditSchedule": () => {
		const org = Orgs.current(), 
			scheduleData = _.deep(org, "billing.scheduling") || {};
		scheduleData.generateBiWeeklyDateFormatted = scheduleData && scheduleData.generateBiWeeklyDate && new moment(scheduleData.generateBiWeeklyDate, "MM/DD/YYYY").format("MM/DD/YYYY");
		scheduleData.useWeeklyPlanDue = scheduleData?.weeklyPlanDueDay ? "fixed" : "graceperiod";
		scheduleData.useMonthlyPlanDue = scheduleData?.monthlyPlanDueDay ? "fixed" : "graceperiod";
		scheduleData.availableItems = _.chain(org?.billing?.plansAndItems)
			.filter( pi => pi.type == "item" && !pi.archived)
			.sortBy( pi => pi.description)
			.value();
		console.log("scheduleData", scheduleData);
		showModal("simpleModal", {
			title:"Billing Schedule",
			template: "billingSettingsModalSchedule",
			data: scheduleData,
			onSave: (e, i, formFieldData) => {
				formFieldData.use_late_fee = formFieldData.automatic_late_fee == "on";
				if (formFieldData.use_late_fee) {
					let errorMessages = [];
					if (!formFieldData.late_fee_item || formFieldData.late_fee_item == "")
						errorMessages.push("You must select an item to use for assessing late fees.");
					if (!formFieldData.late_fee_days || isNaN(formFieldData.late_fee_days))
						errorMessages.push("You must select a number of days for assessing late fees.");
					if (errorMessages.length > 0) {
						$(e.target).html('Save').prop("disabled", false);
						return mpSwal.fire("Error", errorMessages.join(" "), "error");
					}
				}
				formFieldData.generate_biweeklydate = formFieldData.generate_biweeklydate && new moment(formFieldData.generate_biweeklydate, "MM/DD/YYYY").format("MM/DD/YYYY");
				Meteor.callAsync("updateBillingSettings", formFieldData)
				.then((result)=>{
					$("#simpleModal").modal("hide");
				})
				.catch((error)=>{
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		},"#simpleModal");
	},
	"click .btnEditOptions": () => {
		const discountCalculation = _.deep(Orgs.current(), "billing.toplinePercentDiscounts") ? "topline" : "afterDollars";
		const allowDepositReportEdit = _.deep(Orgs.current(), "billing.allowDepositReportEdit");
		showModal("simpleModal", {
			title:"Billing Options",
			template: "billingSettingsModalOptions",
			nonScrollable: true,
			multiValues: true,
			data: {discountCalculation, allowDepositReportEdit},
			onSave: (e, i, formFieldData) => {
				// we need to flag this specially because there's a common save function and it's impossible to differentiate
				// none submitted vs one of the other forms
				formFieldData.isOptionsModal = true;
				Meteor.callAsync("updateBillingSettings", formFieldData)
				.then((result)=>{
					$("#simpleModal").modal("hide");
				})
				.catch((error)=>{
					$(e.target).html('Save').prop("disabled", false);
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		}, "#simpleModal");
	},
	"click #btnDisableBilling": () => {
		mpSwal.fire({
			title: "Are you sure?",
			text:"Disabling billing will prevent new invoices, charges, and transfers.",
			type: "warning",
			showCancelButton: true,
			confirmButtonText: "Yes, disable billing"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("disableBilling")
				.catch((error)=>{
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click #btnSuspendInvoicing": () => {
		mpSwal.fire({
			title: "Confirm invoicing suspension",
			html: "<p>You may optionally include a date to resume invoicing. Otherwise, invoicing will be suspended indefinitely.</p><div><input type='text' class='form-control' placeholder='mm/dd/yyyy' id='invoicing-suspension-date'></div>",
			showCancelButton: true,
			confirmButtonText: "Confirm invoicing suspension",
			didRender: function() {
				$("#invoicing-suspension-date").datepicker({ autoclose: true, todayHighlight: true });
			}
		}).then((result) => {
			if (result.value) {
				const dateValue = $("#invoicing-suspension-date").val(),
					dateStamp = dateValue && new moment(dateValue, "MM/DD/YYYY").valueOf(),
					options = {
						suspendInvoicing: true
					};
				if (dateStamp) options.suspendDateStamp = dateStamp;
				
				Meteor.callAsync("manageSuspendInvoicing", options)
				.catch((error)=>{
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click #btnResumeInvoicing": () => {
		mpSwal.fire({
			title: "Confirm resume invoicing",
			text: "This will cancel the current invoice suspension and resume normal invoicing processes.",
			showCancelButton: true,
			confirmButtonText: "Yes"
		}).then((result) => {
			if (result.value) {
				Meteor.callAsync("manageSuspendInvoicing", {suspendInvoicing: false})
				.catch((error)=>{
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	"click #btnRunInvoices": () => {
		console.log("calling run invoices");
		Meteor.callAsync("runInvoicesNow")
		.then((result)=>{
			mpSwal.fire("Success", "Invoicing queued.", "success");
		})
		.catch((error)=>{
			mpSwal.fire("Error", error.reason, "error");
		});
	},
	"click #btnRunAutopay": () => {
		const today = new moment().format("MM/DD/YYYY");
		mpSwal.fire({
			title: "Run Auto Pay",
			html: `<div class="row mx-2">This will run auto pay for the selected date. Are you sure?</div><div class="row mx-2 mt-4">Please choose a date to run auto payments for.</div><div class="form-group row mx-2"><input type="text" class="form-control" id="manualAutoPayRunDate" value="${ today }"></div>`,
			showCancelButton: true,
			confirmButtonText: "Run Auto Pay",
			didRender: function () {
				$("#manualAutoPayRunDate").datepicker({
					autoclose: true,
					todayHighlight: true,
					endDate: today
				});
			},
		}).then((result) => {
			if (result.value) {
				const runDate = $('#manualAutoPayRunDate').val().length > 0 ? new moment.tz($('#manualAutoPayRunDate').val(), "MM/DD/YYYY", Orgs.current().getTimezone()).format("MM/DD/YYYY") : today;
				console.log('calling run auto pay');
				Meteor.callAsync('runAutoPaymentsNow', { runDate })
				.then((result)=>{
					mpSwal.fire("Success", "Autopay queued.", "success");
				})
				.catch((error)=>{
					mpSwal.fire("Error", error.reason, "error");
				});
			}
		});
	},
	// Delete after BUGS-2915 passes QA
	"click #btnRunPrecomputeForOrg": () => {
		mpSwal.fire({
			title: "Run Precompute Now",
			html: `<div class="row mx-2">This will run the nightly cron-job called 'Precompute'</div>`,
			showCancelButton: true,
			confirmButtonText: "Run Job Now",
		}).then((result) => {
			if (result.value) {
				const orgId = Orgs.current()._id;
				Meteor.callAsync('runPrecomputeNow', orgId)
					.then((result)=>{
						mpSwal.fire("Success", "Precompute finished.", "success");
					})
					.catch((error)=>{
						mpSwal.fire("Error", error.reason, "error");
					});
			}
		});
	},
	"click #btnShowBillingMaps": e => {
		$("#billingMapsPanel").show();
		$("#btnShowBillingMaps").hide();
	},
	"click #btnHideBillingMaps": e => {
		$("#billingMapsPanel").hide();
		$("#btnShowBillingMaps").show();
	},
	"click .btnEditBillingMap": (e) => {
		e.preventDefault();
		const code = $(e.currentTarget).data("id"),
			existingMap = Orgs.current().billingMaps()[code];
		mpSwal.fire({
			title: "Add/edit ledger code",
			text: "Description: " + code,
			input: "text",
			inputValue: existingMap.accountName,
			showCancelButton: true,
			confirmButtonText: "Save Ledger Code"
		}).then((result) => {
			if (result.value != null) {
				Meteor.callAsync("updateBillingMap", {code, accountName: result.value})
				.then((result)=>{
					mpSwal.fire("Success", "Billing map updated.", "success");
				})
				.catch((error)=>{
					mpSwal.fire(error.reason);
				});
			}
		})
	},
	"click #btnShowPrograms": () => {
		$("#programsPanel").show();
		$("#btnShowPrograms").hide();
	},
	"click #btnHidePrograms": () => {
		$("#programsPanel").hide();
		$("#btnShowPrograms").show();
		$("#btnAddNewProgram").show();
		$("#rowAddProgram").hide();
	},
	"click #btnAddNewProgram": () => {
		$("#btnAddNewProgram").hide();
		$("#rowAddProgram").show();
		$("#frmAddProgram")[0].reset();
	},
	"click .btnEditProgram": (e) => {
		e.preventDefault();
		const programId = $(e.currentTarget).data("id");
		$(".programEditRow[data-id='" + programId + "']").show();
		$(".programDisplayRow[data-id='" + programId + "']").hide();
	},
	"click #btnSaveProgram": (e) => {
		e.preventDefault();
		Meteor.callAsync('insertProgram', {
			name: $('#txtProgramName').val(),
			isActive: $('#selectProgramStatus').val() === 'active',
			isRequiredAdvanceNotice: $('#chkProgramRequiresAdvanceNotice').is(':checked')
		})
		.then((result)=>{
			$('#btnAddNewProgram').show();
			$('#rowAddProgram').hide();
		})
		.catch((error)=>{
			mpSwal.fire(error.reason);
		});
	},
	"click .btnSaveEditProgram": (e) => {
		e.preventDefault();
		const programId = $(e.currentTarget).data('id'),
			isActive = $(".frmProgramEdit[data-id='" + programId + "'] .selectProgramStatus").val() === 'active',
			currentProgram = _.find(Orgs.current().availablePrograms(true), p => p._id === programId),
			args = {
				'_id': programId,
				name: $(".frmProgramEdit[data-id='" + programId + "'] .txtProgramName").val(),
				isActive: isActive,
				isRequiredAdvanceNotice: $(".frmProgramEdit[data-id='" + programId + "'] .chkProgramRequiresAdvanceNotice").is(':checked')
			};

		if (currentProgram && currentProgram.isActive !== isActive) {
			const messageText = isActive ? 'This program will appear as a selection in billing.'
				: 'An inactive program will not appear as a selection in billing.',
				confirmButtonText = isActive ? 'Yes, activate program' : 'Yes, deactivate program';
			mpSwal.fire({
				title: 'Are you sure?',
				text: messageText,
				type: 'warning',
				showCancelButton: true,
				confirmButtonText: confirmButtonText
			}).then((result) => {
				if (result.value) {
					Meteor.callAsync('updateProgram', args)
					.then((result)=>{
						$(".programEditRow[data-id='" + programId + "']").hide();
						$(".programDisplayRow[data-id='" + programId + "']").show();
					})
					.catch((error)=>{
						mpSwal.fire(error.reason);
					});
				}
			});
		} else {
			Meteor.callAsync('updateProgram', args)
			.then((result)=>{
				$(".programEditRow[data-id='" + programId + "']").hide();
				$(".programDisplayRow[data-id='" + programId + "']").show();
			})
			.catch((error)=>{
				mpSwal.fire(error.reason);
			});
		}
	},
	"click .btnCancelEditProgram": (e) => {
		e.preventDefault();
		const programId = $(e.currentTarget).data("id");
		$(".programEditRow[data-id='" + programId + "']").hide();
		$(".programDisplayRow[data-id='" + programId + "']").show();
	},
	"click #btnAddNewTimePeriod": () => {
		$("#btnAddNewTimePeriod").prop('disabled', true);
		$("#rowAddTimePeriod").show();
		$("#frmAddTimePeriod")[0].reset();
	},
	"click #btnSaveTimePeriod": (e) => {
		e.preventDefault();
		Meteor.callAsync('insertTimePeriod', {
			name: $('#txtTimePeriodName').val(),
			startDate: $('#dateTimePeriodStart').val(),
			endDate: $('#dateTimePeriodEnd').val()
		})
		.then((result)=>{
			$("#btnAddNewTimePeriod").prop('disabled', false);
			$("#rowAddTimePeriod").hide();
		})
		.catch((error)=>{
			mpSwal.fire(error.reason);
		});
	},
	"click #btnCancelAddTimePeriod": (e) => {
		e.preventDefault();
		$("#btnAddNewTimePeriod").prop('disabled', false);
		$("#rowAddTimePeriod").hide();
	},
	"click .btnEditTimePeriod": (e) => {
		e.preventDefault();
		const timePeriodId = $(e.currentTarget).data("id");
		$(".timePeriodEditRow[data-id='" + timePeriodId + "']").show();
		$(".timePeriodDisplayRow[data-id='" + timePeriodId + "']").hide();
	},
	"click .btnCancelEditTimePeriod": (e) => {
		e.preventDefault();
		const timePeriodId = $(e.currentTarget).data("id");
		$(".timePeriodEditRow[data-id='" + timePeriodId + "']").hide();
		$(".timePeriodDisplayRow[data-id='" + timePeriodId + "']").show();
	},
	"click .btnSaveEditTimePeriod": (e) => {
		e.preventDefault();
		const timePeriodId = $(e.currentTarget).data('id')
		const args = {
			'_id': timePeriodId,
			name: $(".frmTimePeriodEdit[data-id='" + timePeriodId + "'] .txtTimePeriodName").val(),
			startDate: $(".frmTimePeriodEdit[data-id='" + timePeriodId + "'] .dateTimePeriodStart").val(),
			endDate: $(".frmTimePeriodEdit[data-id='" + timePeriodId + "'] .dateTimePeriodEnd").val()
		};
		Meteor.callAsync('updateTimePeriod', args)
		.then((result)=>{
			$(".timePeriodEditRow[data-id='" + timePeriodId + "']").hide();
			$(".timePeriodDisplayRow[data-id='" + timePeriodId + "']").show();
		})
		.catch((error)=>{
			mpSwal.fire(error.reason);
		});
	},
});

function getNumberWithOrdinal(n) {
	var s = ["th", "st", "nd", "rd"],
		v = n % 100;
	return n + (s[(v - 20) % 10] || s[v] || s[0]);
}