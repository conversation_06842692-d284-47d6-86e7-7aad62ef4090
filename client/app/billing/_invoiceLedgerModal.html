<template name="invoiceLedgerModal">
	<div id="invoiceLedgerModal" class="modal fade">
		<div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
			<div class="modal-content">
			
				<div class="modal-header">
						<h5 class="modal-title">Invoice Ledger</h5>
				</div>
				<div class="modal-body">
					<b data-cy="invoice-number-label">Invoice Number:</b> {{invoice.invoiceNumber}}<br/>
					<b data-cy="invoice-date-label">Invoice Date:</b> {{invoice.invoiceDate}}<br/>
					<b data-cy="due-date-label">Due Date:</b> {{formatDate invoice.dueDate "M/DD/YYYY"}}<br/>
					<br/>
					<b data-cy="invoice-for-label">Invoice For:</b> {{targetPerson.lastName}}, {{targetPerson.firstName}}<br/>
					{{#if invoice.coversPeriodDescription}}
					<b data-cy="invoice-covers-label">Covers:</b> {{invoice.coversPeriodDescription}}<br/>
					{{/if}}
					<br/>
					<b>Invoice Ledger:</b><br/>
					
					<br/>
					<table style="width:100%" class="invoice-ledger-table">
						<tr style="border-bottom:1px solid #aaa">
							<th>Date</th>
							<th>Description</th>
							<th style="text-align:right">Debit</th>
							<th style="text-align:right">Credit</th>
							<th style="text-align:right">Balance</th>
						</tr>
						{{#each lineItems}}
						<tr>
							<td data-cy="invoice-date-ledger">{{formatDate date "MM/DD/YYYY"}}</td>
							<td data-cy="invoice-date-description-ledger">{{description}}{{#if showVoid}} <a href="#" class="btnVoidLineItem" data-id="{{i}}">void</a>{{/if}}
								{{#if showManualRefund}} <a href="#" class="btnManualRefundLineItem" data-id="{{i}}">refund</a>{{/if}}
								{{#if showReverse}} <a href="#" class="btnReverseLineItem">reverse</a>{{/if}}
							{{#if voidedReason}}<br/>Voided: {{voidedReason}} {{#if voidedNote}} - {{voidedNote}}{{/if}}{{/if}}
							{{#if voidedAt}}<br/>Voided: {{formatDate voidedAt "MM/DD/YYYY"}}{{/if}}
							{{#if refundedAt}}<br/>Refunded: {{formatDate refundedAt "MM/DD/YYYY"}} {{#if refundedNote}} - {{refundedNote}}{{/if}}{{/if}}
								{{#if reversedAt}}<br/>Reversed: {{formatDate reversedAt "MM/DD/YYYY"}} {{#if reversedNote}} - {{reversedNote}}{{/if}}{{/if}}
							</td>
							<td data-cy="invoice-ledger-debit" style="text-align:right">{{#if debitAmount}}{{formatCurrency debitAmount}}{{/if}}</td>
							<td data-cy="invoice-ledger-credit" style="text-align:right">{{#if creditAmount}}{{formatCurrency creditAmount}}{{/if}}</td>
							<td data-cy="invoice-ledger-balance" style="text-align:right">{{formatCurrency balance}}</td>
						</tr>
						{{/each}}
					</table>
				</div>
				<div class="modal-footer">
					
						<button data-cy="close-btn" class="btn btn-primary font-weight-bolder btn-md" id="btnClose">Close</button>

					</div>

				</div>
			</div>
		</div>
</template>
