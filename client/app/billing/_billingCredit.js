import './_billingCredit.html';

Template.billingCredit.onCreated( function() {
	var self = this,
		passedCreditReason = self.data && self.data.existingCredit.creditReason;
	
	self.creditReason = new ReactiveVar(passedCreditReason);
});

Template.billingCredit.events({
	"change select[name='credit_reason']": function(e, i) {
		const reason = $(e.currentTarget).val();
		i.creditReason.set(reason);
	}
});

Template.billingCredit.helpers({
	showLineItem() {
		return Template.instance().creditReason.get() == "other";
	},
	showCheckNumber() {
		return Template.instance().creditReason.get().search(/check/i) !== -1;
	},
	lineItemDescription(idx) {
		return "[" + idx + "] " + (this.type == "item" ?
			this.originalItem.description + " $" + numeral(this.amount).format("0.00") :
			this.description + " $" + numeral(this.amount).format("0.00"));
	},
	isSelected(value, compareValue) {
		return value === compareValue ? 'selected' : '';
	}
});