import { Orgs } from '../../../lib/collections/orgs';
import './billingAdyenOnboarding.html';

Template.billingAdyenOnboarding.events({
	"submit #frmAdyenBasic"(event, instance) {
		event.preventDefault();
		const options = {
			companyName: $("input[name=company-name]").val(),
			firstName: $("input[name=signer-first-name]").val(),
			lastName: $("input[name=signer-last-name]").val(),
			gender: $("select[name=signer-gender]").val(),
			email: $("input[name=signer-email]").val(),
		}

		Meteor.callAsync("billingAdyenOnboarding", options)
		.catch(error => {
			mpSwal.fire("Error", error.reason, "error");
		});
	},

	"click #btnRedirectHop"(event, instance) {
		event.preventDefault();

		Meteor.callAsync("billingAdyenGenerateHopUrl")
		.then((result) => {
			console.log("result", result);
			if (result.destinationUrl) {
				window.open(result.destinationUrl, '_blank');
			} else {
				mpSwal.fire("Error", "No onboarding portal link was available. Please try again later.", "error");
			}
		})
		.catch(error => {
			console.log("error", error);
			mpSwal.fire("Error", "There was an issue generating the link to the onboarding portal.", "error");
		});
	}
});
Template.billingAdyenOnboarding.helpers({
	"onboardingStage"() {
		const currentOrg = Orgs.current();
		if (currentOrg && currentOrg.billing && currentOrg.billing.adyenOnboarding)
			return "hosted-onboarding";
		else 
			return "start";
	}
});

Template.billingAdyenOnboardingStatus.onCreated(function() {
	var self=this;
	self.onboardingData = new ReactiveVar();
	self.showOnboardingData = new ReactiveVar();
	Meteor.callAsync("billingAdyenOnboardingStatus")
	.then((result) => {
		self.showOnboardingData.set(true);
		self.onboardingData.set(result && result.onboardingData);
	});
});

Template.billingAdyenOnboardingStatus.helpers({
	showOnboardingData() {
		return Template.instance().showOnboardingData.get();
	},
	status() {
		const status = Template.instance().onboardingData.get();
		if (status) {
			return {
				status: status.status,
				processingStateDescription: status.processingState.disabled ? "Inactive" : "Active",
				payoutStateDescription: status.payoutState.allowPayout ? "Active" : "Inactive"
			}
		} else 
			return {
				status: "Inactive",
				processingStateDescription: "Inactive",
				payoutStateDescription: "Inactive"
			}
	}
})
