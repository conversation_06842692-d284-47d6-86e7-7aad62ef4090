<template name="savedReconciliations">
	<div class="payer-reconciliation-section pb-3">
		<h4>Saved Reconciliations</h4>
		
		<div class="filter-section mb-3">
			<div class="row">
				<div class="col-md-6">
					<label for="saved-reconciliations-date-range">Date Range:</label>
					<input type="text" id="saved-reconciliations-date-range" name="saved-reconciliations-date-range" class="form-control">
				</div>
				<div class="col-md-6 d-flex align-items-end">
					<button id="btn-update-saved-filters" class="btn btn-primary">Update</button>
				</div>
			</div>
		</div>
		
		{{#if loadingSavedReconciliations}}
			<div class="text-center p-4">
				<i class="fa fa-spinner fa-spin fa-2x"></i>
				<p class="mt-2">Loading saved reconciliations...</p>
			</div>
		{{else}}
			{{#if requestSavedReceived}}
				<div class="table-responsive">
					{{#if savedReconciliations.length}}
						<table class="table table-striped">
							<thead>
								<tr>
									<th>Date</th>
									<th>Amount</th>
									<th>Check Number</th>
									<th>Actions</th>
								</tr>
							</thead>
							<tbody>
								{{#each item in savedReconciliations}}
									<tr>
										<td>{{formatDate item.date}}</td>
										<td>{{formatCurrency item.amount}}</td>
										<td>{{item.checkNumber}}</td>
										<td>
											<a href="#" 
											   class="download-csv" 
											   data-batch-id="{{item._id}}" 
											   data-csv-url="{{item.csvUrl}}"
											   title="Download CSV">
												Download CSV
											</a>
											<a href="#"
												class="download-document ml-3 {{#unless item.documentUrl}}text-muted disabled-link{{/unless}}" 
												data-document-url="{{item.documentUrl}}" 
												data-file-name="{{item.fileName}}" 
												data-file-type="{{item.fileType}}"
												title="{{#if item.documentUrl}}Download Supporting Document{{else}}No document available{{/if}}">
												Download Supporting Document
											</a>
										</td>
									</tr>
								{{/each}}
							</tbody>
						</table>
					{{else}}
						<div class="alert alert-info">
							No saved reconciliations found for the selected date range.
						</div>
					{{/if}}
				</div>
			{{else}}
				<div class="alert alert-info">
					Select a date range and click "Update" to view saved reconciliations.
				</div>
			{{/if}}
		{{/if}}
	</div>
</template> 