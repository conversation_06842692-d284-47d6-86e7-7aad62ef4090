import './_amountInvoiced.html';

Template.amountInvoiced.helpers({
	"amountInvoicedItem"() {
		return Template.instance().amountInvoicedItem.get();
	},
	"section"() {
		return Template.instance().section.get();
	},
	"dashboardData"() {
		return Template.instance().dashboardData;
	},
	"amountInvoicedDataAvailable"() {
		Template.instance().dashboardData.set(Template.instance().data);
		Template.instance().section.set(Template.instance().data?.sections[0]);
		Template.instance().amountInvoicedItem.set(Template.instance().data?.sections[0]?.items[0]);
		return Template.instance().data ? true : false;
	}
});

Template.amountInvoiced.onCreated(function () {
	this.dashboardData = new ReactiveVar(null);
	this.section = new ReactiveVar(null);
	this.amountInvoicedItem = new ReactiveVar(null);
});