import './_upcomingBillingAdjustments.html';

Template.upcomingBillingAdjustments.helpers({
    "upcomingBillingAdjustmentsItem"(){
		return Template.instance().upcomingBillingAdjustmentsItem.get();
	},
	"section"(){
		return Template.instance().section.get();
	},
	"dashboardData"() {
		return Template.instance().dashboardData;
	},
	"upcomingBillingAdjustmentsData"(){
		Template.instance().dashboardData.set(Template.instance().data);
		Template.instance().section.set(Template.instance().data?.sections[0]);
		Template.instance().upcomingBillingAdjustmentsItem.set(Template.instance().data?.sections[0]?.items[0]);
		return Template.instance().data ? true : false;
	}
});

Template.upcomingBillingAdjustments.onCreated(function() {
	this.section = new ReactiveVar(null);
	this.dashboardData = new ReactiveVar(null);
	this.upcomingBillingAdjustmentsItem = new ReactiveVar(null);
});