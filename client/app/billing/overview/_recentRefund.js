import './_recentRefund.html';

Template.recentRefund.helpers({
    "recentRefundItem"(){
		return Template.instance().recentRefundItem.get();
	},
	"section"(){
		return Template.instance().section.get();
	},
	"dashboardData"(){
		return Template.instance().dashboardData;
	},
	"recentRefundData"(){
		Template.instance().dashboardData.set(Template.instance().data);
		Template.instance().section.set(Template.instance().data?.sections[0]);
		Template.instance().recentRefundItem.set(Template.instance().data?.sections[0]?.items[0]);
		return Template.instance().data ? true : false;
	}
});

Template.recentRefund.onCreated(function() {
	this.dashboardData = new ReactiveVar(null);
	this.section = new ReactiveVar(null);
	this.recentRefundItem = new ReactiveVar(null);
});