import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import { TimeCards } from '../../../lib/collections/timeCards';
import { processPermissions } from '../../../lib/permissions';
import { showModal } from "../main";
import './_timeCardHistoryModal.js';
import './_timeCardFormModal.js';
import '../components/dateRangePicker/dateRangePicker.js';
import './timeCards.html';
import { offlinePreventCheck } from '../../../mpweb.js';
const moment = require("moment-timezone");
const {TimeCardUtils} = require("../../../lib/util/timeCardUtils");
const { getPeopleData } = require("../../services/peopleMeteorService");
import _ from '../../../lib/util/underscore.js';

Template.timeCards.onCreated(function () {
  this.filterStartDate = new ReactiveVar(new moment().startOf('week'));
  this.filterEndDate = new ReactiveVar(new moment().endOf('week'));
  this.filterPeople = new ReactiveVar([]);
  this.confirmedFilter = new ReactiveVar(false);
  this.autoCheckoutFilter = new ReactiveVar(false);
  this.isEditedFilter = new ReactiveVar(false);
  this.timeCardData = new ReactiveVar([]);
  this.loading = new ReactiveVar(true);
  this.timeCardsData =  new ReactiveVar([]);
  this.peopleData = new ReactiveVar([]);
  var instance = this;

  this.getAttributes = () => {
    return {
      startDate: new moment(instance.filterStartDate.get()).format("MM/DD/YYYY"),
      endDate: new moment(instance.filterEndDate.get()).format("MM/DD/YYYY"),
      peopleIds: instance.filterPeople.get(),
      confirmedFilter: instance.confirmedFilter.get(),
      autoCheckoutFilter: instance.autoCheckoutFilter.get(),
      isEditedFilter: instance.isEditedFilter.get()
    }
  };

  this.autorun(() => {
    this.subscribe('theTimeCards', this.getAttributes());
  });

  this.autorun(() => {
    if (this.timeCardsData.get()?.length) {
      const fields = {_id: 1, firstName: 1, lastName: 1};
      const query = {_id: {$in: this.timeCardsData.get().map(tc => tc.personId)}};
      getPeopleData(query, {fields}).then(res => {
        this.peopleData.set(res);
      }).catch(err => {
        console.log(err);
      })
    }
  });
});

Template.timeCards.rendered = function () {
  const org = Orgs.current();
  const hasChildFilter = org.hasCustomization("integration/kinderConnect/enabled");
  const peopleQuery = hasChildFilter ? {} : { type: { $in: ["admin", "staff"] }, orgId: org['_id'] };
  getPeopleData(peopleQuery, { sort: { lastName: 1, firstName: 1 }, fields: { _id: 1, firstName: 1, lastName: 1 } }).then(res => {
    const values = res.map(p => { return { id: p._id, label: `${p.firstName} ${p.lastName}` } });
    const curControl = $("#timeCardsPeopleFilter")[0];

    if (curControl && curControl.selectize)
      curControl.selectize.destroy();
  
    var self = this;
    $("#timeCardsPeopleFilter").selectize({
      maxItems: null,
      valueField: 'id',
      labelField: 'label',
      searchField: 'label',
      options: values
    });
  
    $("#timeCardsPeopleFilter").on("change", function () {
      if (offlinePreventCheck()) return false;
      self.filterPeople.set($("#timeCardsPeopleFilter").val());
    })
  }).catch(err => {
    console.log(err);
  });
};

Template.timeCards.events({
  'click .voidTimeCardLink': function (event, template) {
    event.preventDefault();
    if (offlinePreventCheck()) return false;
    const timeCardId = $(event.target).attr("data-id");
    mpSwal.fire({
      title: "Void Time Card",
      html: "<p>Voiding this time card will remove it from reports. You may provide a reason in the following text area</p><div><textarea rows='4' id='void-timecard-reason'></textarea></div>",
      showCancelButton: true,
      icon: "warning",
      confirmButtonText: "Yes, void time card"
    }).then(result => {
      if (result.value) {
        const voidReason = $("#void-timecard-reason").val();
        Meteor.callAsync("voidTimeCard", { timeCardId, voidReason }).then(result => {}).catch(error => {
          mpSwal.fire({ icon: "error", title: "Could not void time card", text: error.reason });
        });
      }
    })
  },
  'click .editTimeCardLink': function (event, template) {
    event.preventDefault();
    if (offlinePreventCheck()) return false;
    const startDate = $("#timeCardsStartDate").val();
    const endDate = $("#timeCardsEndDate").val();
    const peopleIds = $("#timeCardsPeopleFilter").val() || [];

    showModal("_timeCardFormModal", { timeCardId: $(event.target).attr("data-id") }, "#_timeCardFormModal");
  },
  'click .viewTimeCardLink': function (event, template) {
    event.preventDefault();
    if (offlinePreventCheck()) return false;

    showModal("_timeCardHistoryModal", { timeCardId: $(event.target).attr("data-id") }, "#_timeCardHistoryModal");
  },
  'click .sendToKinderConnect': function (event, template) {
    if (offlinePreventCheck()) return false;
    const timeCardId = $(event.target).attr("data-id");
    mpSwal.fire({
      title: "Send to Kinder Connect",
      text: "Confirm time card sync with KinderConnect",
      showCancelButton: true,
      icon: "warning",
      confirmButtonText: "Yes, Sync"
    }).then(result => {
      if (result.value) {
        Meteor.callAsync("timeCardToKinderConnect", { timeCardId }).then(result => {}).catch(error => {
          mpSwal.fire({ icon: "error", title: "Could not sync time card", text: error.reason });
        });
      }
    })
  },
  "change #timeCardsStartDate": function (event, template) {
    if (offlinePreventCheck()) return false;
    template.filterStartDate.set($("#timeCardsStartDate").val());
  },
  "change #timeCardsEndDate": function (event, template) {
    if (offlinePreventCheck()) return false;
    template.filterEndDate.set($("#timeCardsEndDate").val());
  },
  "click #btnExportCsv": function (event, template) {
    var outputFile = 'export.csv'

    // CSV
    exportTableToCSV.apply(this, [$('#timeCardTable > table'), outputFile]);
  },
  'change #confirmedFilter': function (event, template) {
    template.confirmedFilter.set(event.target.checked);
  },
  'change #autoCheckoutFilter': function (event, template) {
    template.autoCheckoutFilter.set(event.target.checked);
  },
  'change #isEditedFilter': function (event, template) {
    template.isEditedFilter.set(event.target.checked);
  },
});

Template.timeCards.helpers({
  "hasKinderConnect": function () {
    const org = Orgs.current();
    return org.hasCustomization("integration/kinderConnect/enabled");
  },
  "dateFields": function () {
    return {
      filterStartDate: Template.instance().filterStartDate,
      filterEndDate: Template.instance().filterEndDate,
    }
  },
  "timeCardData": function () {
    // see theTimeCards in /server/publications.js
    const timeCardsDataRes = TimeCards.find({});
    Template.instance().timeCardsData.set(timeCardsDataRes.fetch());
    return timeCardsDataRes;
  },
  "timeCardConfirmed": function (tc) {
    return (tc.timeCardConfirmed ? "Yes" : "No");
  },
  "formattedStartDate": function () {
    return Template.instance().filterStartDate.get();
  },
  "formattedEndDate": function () {
    return Template.instance().filterEndDate.get();
  },
  "formatTimecardTime": function (timecard) {
    return TimeCardUtils.getTimeCardTotalHours(timecard).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  },
  "isAutoCheckout": function (timecard) {
    return TimeCardUtils.isTimeCardAutoCheckout(timecard);
  },
  "isEdited": function (timecard) {
    return TimeCardUtils.isTimeCardEdited(timecard);
  },
  "formatName": function (tc) {
    if (Template.instance().peopleData.get()?.length) {
      const p = Template.instance().peopleData.get()?.filter(p => tc.personId === p._id);
      if (p) return `${p[0].firstName} ${p[0].lastName}`;
    }
    return "";
  },
  "formatVoidReason": function (tc) {
    if (tc.void) {
      return (tc.voidReason) ? tc.voidReason : "*VOID*"
    }
    return "";
  },
  "payTypeLabel": function (selectedPayTypeId) {
    const org = Orgs.current();
    const payTypes = org && org.customStaffPaySettings && org.customStaffPaySettings.types;
    const selected = _.find(payTypes || [], (t) => t._id == selectedPayTypeId);
    if (selected && selected.type) return selected.type;
    return "Standard";
  },
  formatCheckOutTime: function (t) {
    if (!t) return "";
    return moment(t, ["h:mm a", "h:mm:ss A"]).format("h:mm a");
  },
  canEdit: function (tc) {
    const org = Orgs.current();
    // If time cards aren't locked in config, meaning that everyone can edit it
    if (!org.hasCustomization("people/timeCardsLocked/enabled")) {
      return true;
    }
    const timezone = org?.getTimezone()
    const lockedDate = org?.valueOverrides?.timeCardsLockedDate;
    // 9:00 AM is the hard-coded default time to check against
    const lockedTime = "9:00 am";
    const lockedDateTime = new moment.tz(lockedDate + " " + lockedTime, timezone);
    const tcDateTime = new moment.tz(tc.checkOutDate + " " + tc.checkOutTime, timezone);
    const now = new moment.tz(timezone);
    const passedLockedDate = now.isSameOrAfter(lockedDateTime);
    if (passedLockedDate) {
      // if the locked data has passed, locked all time cards prior and  check the permission to see if the user can edit or not
      const isTimeCardBeforeLockedDate = tcDateTime.isBefore(lockedDateTime);
      const canEditLockedTimeCards = processPermissions({
        assertions: [{ context: "people/editLockedTimeCards", action: "edit" }],
        evaluator: (person) => person.type === "admin" || person.type === "staff"
      });
      return canEditLockedTimeCards || !isTimeCardBeforeLockedDate;
    }
    return true;
  },
});

