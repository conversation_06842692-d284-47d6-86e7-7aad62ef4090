import { Template } from 'meteor/templating';
import { ReactiveVar } from 'meteor/reactive-var';
import { Meteor } from 'meteor/meteor';
import { Orgs } from '../../../lib/collections/orgs';
import './_timeCardHistoryModal.html';
import {Log} from "../../../lib/util/log";
import moment from 'moment-timezone';
import { getPeopleById } from "../../services/peopleMeteorService";

Template._timeCardHistoryModal.onCreated(function() {
  this.timeCard = new ReactiveVar({});
  this.loaded = new ReactiveVar(false);
  this.formatName = new ReactiveVar("");
  this.originalCheckInCreator = new ReactiveVar();
  this.originalCheckOutCreator = new ReactiveVar();
  this.personModifiedId = new ReactiveVar();
  this.personModifiedIdCheck = new ReactiveVar();
  this.personModifiedName = new ReactiveVar();
  this.autorun(() => {
    let timeCard = this.timeCard.get();
    if (timeCard && timeCard.personId) {
      // const fields = {_id: 1, firstName: 1, lastName: 1};
      getPeopleById(timeCard.personId).then(res => {
        this.formatName.set(`${res.firstName} ${res.lastName}`);
      }).catch(err => {
        console.log(err);
      });
    }
    if (timeCard && timeCard.originalCheckInMoment && timeCard.originalCheckInMoment.createdByPersonId) {
      getPeopleById(timeCard.originalCheckInMoment.createdByPersonId).then(res => {
        if (res) {
          this.originalCheckInCreator.set(`${res.firstName} ${res.lastName}`);
        }
        else {
          this.originalCheckInCreator.set("*Creator not found");
        }
      }).catch(err => {
        console.log(err);
      });
    }

    if (timeCard && timeCard.originalCheckOutMoment && timeCard.originalCheckOutMoment.createdByPersonId) {
      getPeopleById(timeCard.originalCheckOutMoment.createdByPersonId).then(res => {
        if (res) {
          this.originalCheckOutCreator.set(`${ res.firstName } ${ res.lastName }`);
        }
      }).catch(err => {
        console.log(err);
      });
    } else if (timeCard && timeCard.originalCheckOutMoment && timeCard.originalCheckOutMoment.createdBy == "SYSTEM") {
      this.originalCheckOutCreator.set("[CREATED BY SYSTEM]");
    } else {
      this.originalCheckOutCreator.set("*Creator not found");
    }
  });

  this.autorun(() => {
    const personModifiedId = this.personModifiedId.get();
    if (personModifiedId) {
      if (personModifiedId !== this.personModifiedIdCheck.get()) {
        this.personModifiedIdCheck.set(personModifiedId);
        getPeopleById(personModifiedId).then(res => {
          if (res) {
            this.personModifiedName.set(res);
          }
        }).catch(err => {
          console.log(err);
        });
      }
    }
  });
});

Template._timeCardHistoryModal.rendered = function() {	

	var self = this;
  const timeCardId = Template.instance().data.timeCardId;

  Meteor.callAsync('getTimeCard', { timeCardId }).then(result => {
    if (!result.voidedAt && result.void) {
      Meteor.callAsync('getAuditTrailForVoidedTimeCard', timeCardId, result.voidedByUser).then(auditResult => {
        if (auditResult.length > 0 && auditResult[0].date) {
          self.timeCard.set({
            ...result,
            voidedAt: auditResult[0].date
          });
        } else {
          self.timeCard.set(result);
        }
      }).catch(error => {
        Log.error(error);
      });
    } else {
      self.timeCard.set(result);
    }
    self.loaded.set(true);
  }).catch(error => {});
};

Template._timeCardHistoryModal.helpers({
  "isVoided": function() {
    const tc = Template.instance().timeCard.get();
    return tc && tc.void;
  },
  "isVoidedBy": function() {
    const tc = Template.instance().timeCard.get();
    return tc && tc.voidedByPersonName;
  },
  "getVoidReason": function() {
    const tc = Template.instance().timeCard.get();
    return tc && (tc.voidReason || "Reason not provided");
  },
  "getVoidedByPersonName": function() {
    const tc = Template.instance().timeCard.get();
    return tc && tc.voidedByPersonName;
  },
  "getVoidTimeAndDate": function() {
    const timeCard = Template.instance().timeCard.get();
    if (!timeCard || !timeCard.voidedAt) {
      return "";
    }
    return moment.tz(timeCard.voidedAt, Orgs.current().getTimezone()).format("MM/DD/YYYY h:mm a");
  },
  "getUpdateLogs": function() {
      var tc = Template.instance().timeCard.get();
      var updateLogs = _.filter(tc.updateLog || [], function(ulog) {
          if (ulog.modifiedDoc == "TimeCard") return false;
          return true;
      });
      return updateLogs || [];
  },
  "getOriginalCheckInCreateDateTime": function() {
    var tc = Template.instance().timeCard.get();
    if (tc && tc.originalCheckInMoment && tc.originalCheckInMoment.createdAt) {
      return new moment(tc.originalCheckInMoment.createdAt).format("MM/DD/YYYY h:mm a")
    }
  },
  "getOriginalCheckInCreator": function() {
    return Template.instance().originalCheckInCreator.get();
  },
  "getOriginalCheckOutCreator": function() {
    return Template.instance().originalCheckOutCreator.get();
  },
  "getOriginalCheckOutCreateDateTime": function() {
    var tc = Template.instance().timeCard.get();
    if (tc && tc.originalCheckOutMoment && tc.originalCheckOutMoment.createdAt) {
      return new moment(tc.originalCheckOutMoment.createdAt).format("MM/DD/YYYY h:mm a")
    }
  },
  "hasUpdateLog": function() {
    var tc = Template.instance().timeCard.get();
    return tc && tc.updateLog && tc.updateLog.length > 0;
  },
  "hasOriginalCheckInMoment": function() {
    var tc = Template.instance().timeCard.get();
    return (tc && tc.originalCheckInMoment) ? true : false;
  },
  "hasOriginalCheckOutMoment": function() {
    var tc = Template.instance().timeCard.get();
    return (tc && tc.originalCheckOutMoment) ? true : false;
  },
  "getModifiedElement": function(ulog) {
    var personModified = (ulog.updatedFields && ulog.updatedFields.modifiedByName) ? ulog.updatedFields.modifiedByName : null;
    if (personModified == null && ulog.modifiedBy) {
      Template.instance().personModifiedId.set(ulog.modifiedBy);
      var person = Template.instance().personModifiedName.get();
      if (person) {
        personModified = `${person.firstName} ${person.lastName}`;
      }
    }
    
    var dt = new moment(ulog.processedTime).format("MM/DD/YYYY h:mm a");
    var doc = ulog.modifiedDoc;
    if (ulog.modifiedDoc == "checkout") doc = "Check Out";
    if (ulog.modifiedDoc == "checkin") doc = "Check In";
    if (ulog.modifiedDoc == "TimeCard") doc = "Time Card";
    
    return (personModified) ? `${personModified} modified ${doc} at ${dt}` : `${doc} at ${dt}`;
  },
  "getDateUpdate": function(ulog) {
    var dateUpdate = ulog.updatedFields && ulog.updatedFields.date;
    return (dateUpdate) ? dateUpdate : false;
  },
  "getTimeUpdate": function(ulog) {
    var timeUpdate = ulog.updatedFields && ulog.updatedFields.time;
    return (timeUpdate) ? timeUpdate : false;
  },
  "getSelectedPayUpdate": function(ulog) {
    var selectedPayTypeId = ulog.updatedFields && ulog.updatedFields.selectedPayTypeId;
    if (!selectedPayTypeId) return false;
    
    if (selectedPayTypeId == "standard") return "Standard";
    
    const org = Orgs.current();
    var types = org && org.customStaffPaySettings && org.customStaffPaySettings.types;
    if (!types) types = [];
    var selectedType = _.find(types, function(t) { return t._id == selectedPayTypeId});
    return (selectedType) ? selectedType.type : `type not found for ${selectedPayTypeId}`;
  },
  "checkInSelectedPay": function() {
    var tc = Template.instance().timeCard.get();
    var selectedPayTypeId = tc && tc.originalCheckInMoment && tc.originalCheckInMoment.selectedPayTypeId;
    
    const org = Orgs.current();
    const payTypes = org && org.customStaffPaySettings && org.customStaffPaySettings.types;
    const selected = _.find(payTypes || [], (t) => t._id == selectedPayTypeId);
    if (selected && selected.type) return selected.type;
    return "Standard";
  },
  "loaded": function() {
    return Template.instance().loaded.get();
  },
  "timeCard": function() {
    return Template.instance().timeCard.get();
  },
	"formatName": function() {
    return Template.instance().formatName.get();
	},
});
