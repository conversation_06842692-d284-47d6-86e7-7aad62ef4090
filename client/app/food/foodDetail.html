<template name="foodDetail">
  <div class="container">
    <div class="flex-column-fluid ml-lg-8">
      <div class="card card-custom gutter-bs">
        <div class="card-body">
          {{#if showManageFood}}
            <div class="btn-group pull-right">
              <button data-cy="edit-food-entry-btn" type="button" class="btn btn-primary editFoodLink font-weight-bolder">Edit Food Entry</button>
              <button data-cy="toggle-dropdown-food-entry" type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="sr-only">Toggle Dropdown</span>
              </button>
              <div class="dropdown-menu dropdown-menu-right">
                <span data-cy="edit-food-entry-opt" class="dropdown-item clickable-row editFoodLink">Edit Food Entry</span>
                <span data-cy="delete-food-entry-opt" class="dropdown-item clickable-row deleteFoodLink">Delete Food Entry</span>
              </div>
            </div>
          {{/if}}

          <h3 data-cy="meal-type-label" class="text-primary">{{meal}}</h3>

          {{#if foodItems}}
            <b>Food Items:</b><br/>
            <ol>
              {{#each item in foodItems}}
                <li data-cy="food-item-entry">{{item}}</li>
              {{/each}}
            </ol>
          {{/if}}
          <b>Scheduled Date:</b> <span data-cy="scheduled-date">{{formattedDate}}</span><br/>
          <b>Description:</b> <span data-cy="description">{{description}}</span><br/>
          <b>Recurring:</b> <span data-cy="recurring-description">{{recurringDescription}}</span><br/>
          <b>Selected Groups:</b> <span data-cy="selected-group-names">{{selectedGroupNames}}</span><br/>
        </div>
      </div>
    </div>
  </div>
</template>
