:root {
  --people-ratio-template-color: #aaaaff;
  --people-ratio-staff-template-color: #E7A4C7;
  --dark-primary: #533F58;
  --light-primary: #d7bde4;
  --lighter-primary: #f9f5fb;
  --aside-hover-icon: rgba(255, 255, 255, .7)
}

.inlineJsonEditor {
  position: relative;
  width: calc(100%);
  height: 600px;
}

.menu-icon-parent:not(:hover) .menu-icon-aside {
  color: var(--white) !important;
}

.menu-icon-parent:hover .menu-icon-aside {
  color: var(--aside-hover-icon) !important;
}

.menu-icon-parent:not(:hover) .menu-text-aside {
  color: var(--white) !important;
}

.menu-icon-parent:hover .menu-text-aside {
  color: var(--aside-hover-icon) !important;
}

.menu-item-dd-parent:not(:hover) .menu-item-aside {
  color: var(--white) !important;
}

.menu-item-dd-parent:not(:hover) .menu-item-aside {
  color: var(--white) !important;
}

.menu-item-dd-parent:hover .menu-item-aside {
  color: var(--aside-hover-icon) !important;
}


.report-cp-br {
  border-right: 1px solid #ccc;
} 


.header-box-shadow {
   box-shadow: 0px 10px 30px 0px rgba(82, 63, 105, 0.08) !important;
}

.aside-bg {
  background-color: var(--primary);
}

.aside-bg-transparent {
  background-color: transparent;
}

.aside-menu .menu-nav > .menu-item.menu-item-active > .menu-heading, .aside-menu .menu-nav > .menu-item.menu-item-active > .menu-link {
  background-color: rgba(0,0,0,0.4);
}
.aside-menu .menu-nav > .menu-item .menu-submenu .menu-item.menu-item-active > .menu-heading, .aside-menu .menu-nav > .menu-item .menu-submenu .menu-item.menu-item-active > .menu-link {
  background-color: rgba(0,0,0,0.4);
  border-radius:0.43em;
}

/* if we have sidebar enabled then this needs adjustment */
.header-fixed[data-header-scroll="on"] .header {
  padding-right:40px; }

.header-fixed[data-header-scroll="on"] .header .header-wrapper {
  background-color: #EBF9FD; }

.text-dark-primary {
  color: var(--dark-primary) !important; }
  
/*COLORS*/
.bg-bright-blue {
  background-color: #3AC6EF !important;
}

.text-bright-blue {
  color: #3AC6EF !important;
}

/*ICONS*/
.fad-regular {
  font-size: 24px;
}

.fad-danger {
  color: var(--danger) !important;
}

.fad-warning {
  color: var(--warning) !important;
}

.fad-success {
  color: var(--success) !important;
}

.fad-primary {
  color: var(--primary) !important;
}

.fad-primary.fa-times:after {
  opacity: 1;
}

.fad-primary-h:hover, .fad-primary-h:focus {
  color: var(--primary) !important;
}

@media (min-width: 992px) {
  .mobile-only {
    display: none !important;
  }
}

@media (max-width: 991.98px) {
  .mobile-only {
  }
}

/* dashboard */
.qb-audit-message {
  white-space: pre-wrap;
  font-family:'Courier New', Courier, monospace;
}

.for-you-item {
  /*border-bottom:1px solid #E5E5EA; */
}
.for-you-item:last-child {
  border-bottom:none;
}
.for-you-info-card  img {
  float:right;
  height:200px;
}
.na-label {
    height: 100%;
    position: absolute;
    width: 100%;
    background-color: rgba(255, 255,255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}
.na-label-text {

}
.dashboard-overlay {
  width: 100%;
  height: 100%;
  background-color: rgba(255,255,255,0.7);
  position: absolute;
  z-index: 10;
}

/*SWAL2 */

.swal2-icon.swal2-warning {
  font-size: 1.1rem !important;
}
  
/*DROPDOWN*/
.dropdown-item.clickable-row {
  cursor:pointer;
}

/* CARDS */
.card-custom.clickable-row {
  cursor:pointer;
} 

/*Messages*/

.message-body p {
  white-space: pre;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

/* PEOPLE */
.avatar-circle {
  width: 60px;
  height: 60px;
  text-align: center;
  border-radius: 10%;
  -webkit-border-radius: 10%;
  -moz-border-radius: 10%;
  float: left;
}

.header-avatar-circle {
  width: 32px;
  height: 32px;
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  background-color: #AC52DB;
  text-align: center;
  border-radius: 10%;
  -webkit-border-radius: 10%;
  -moz-border-radius: 10%;
  float: left;
}

.card-avatar-circle {
  width: 200px;
  height: 200px;
  text-align: center;
  border-radius: 10%;
  -webkit-border-radius: 10%;
  -moz-border-radius: 10%;
}

.people-list-user-img {
  width: 60px;
  height: 60px;
  -webkit-border-radius: 10%;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10%;
  -moz-background-clip: padding;
  border-radius: 10%;
  background-clip: padding-box;
  float:left;
  background-size: cover;
  background-position: center center;
  cursor:pointer;
}

.avatar-small {
  width: 71.25px;
  height: 71.25px;
}

.people-card-center-cropped {
  object-fit: cover;
  object-position: center; /* Center the image within the element */
  height: 200px;
  width: 200px;
  -webkit-border-radius: 10%;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10%;
  -moz-background-clip: padding;
  border-radius: 10%;
  text-align: center;
  background-clip: padding-box;
  background-position: center center;
}

.people-card-user-img {
  width: 200px;
  height: 200px;
  -webkit-border-radius: 10%;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10%;
  -moz-background-clip: padding;
  border-radius: 10%;
  text-align: center;
  background-clip: padding-box;
  background-size: cover;
  background-position: center center;
  cursor:pointer;
}

.people-card-user-med {
  width: 120px !important;
  height: 120px !important;
}

.people-card-user-small {
  width: 80px !important;
  height: 80px !important;
}

.people-card-user-xs-small {
  width: 60px !important;
  height: 60px !important;
}

.crm-status {
  text-overflow: ellipsis;
  overflow: hidden;
  width: 80px !important;
}

.crm-status-child {
  width: 60px !important;
}

.people-days {
  padding-top:6px;
  padding-bottom:6px;
  padding-left:8px;
  padding-right:8px;
  margin-left:4px;
  margin-right:4px;
  border-radius:4px;
  background-color:var(--secondary);
  color:#ffffff;
  font-size:14px;
}

.initials-xs {
  position: relative;
  font-size: 14px; /* 50% of parent */
  color: #fff;
  /*font-family: "Courier New", monospace;*/
  font-weight: bold;
}

.initials {
  position: relative;
  font-size: 27px; /* 50% of parent */
  color: #fff;
  /*font-family: "Courier New", monospace;*/
  font-weight: bold;
  text-transform: uppercase;
}
.large-initials {
  position: relative;
  font-size: 50px; /* 50% of parent */
  color: #fff;
  /*font-family: "Courier New", monospace;*/
  font-weight: bold;
}
.initials-search {
  font-size: 16px;
  font-weight: 700;
}

/* MOMENTS */
#momentForm input[type="file"] {
    display: none;
}

.customSuggestionsList > div {
  max-height: 300px;
  border-radius: 0.42rem;
  border: 1px solid var(--primary);
  overflow: auto;
}

/* MOMENTS::TAGIFY */

.customSuggestionsList > div{
  max-height: 200px;
  margin-top: 8px;
  border: 1px solid var(--primary);
  overflow: auto;
}

.tagify__dropdown.mp-fixed {

}

/*LOADING TEMPLATE*/
.loading-overlay {
  position:fixed;
  top:53px;
  bottom:0;
  width: 100%;
  z-index:999;
  background-color:#f9f9f9;
  opacity:1;
  margin-left:-15px;
}

.loading-overlay-centered {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align:center
}

.loading-overlay-fixed-centered {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* profile field group box */
.row.profile-box-field-group {
  /* margin-left:10px !important; */
  margin-top:10px;
  margin-bottom:0;
  padding-top:0;
  padding-bottom:0;
}
.profile-box-field-group h4 {
  margin-left: 24px
}
.profile-box-field-group input[type="file"] {
    display: none;
}

.profile-box .profile-box-field-group .row {
  border-bottom: none;
  border-top: none;
}
.profile-box .row.field-group-data-row {
  border-bottom:1px solid #ccc;
}
.profile-box .row.field-group-data-row:last-of-type {
  border-bottom:none;
}

.profile-box-field-group-header .accordion-toggle:after {
  /* symbol for "opening" panels */
  font-family: 'FontAwesome';  /* essential for enabling glyphicon */
  content: "\f078";    /* adjust as needed, taken from bootstrap.css */
  color: var(--primary);         /* adjust as needed */
  display: inline-block;
  padding-left: 10px;
  vertical-align: middle;
}
.profile-box-field-group-header .accordion-toggle.collapsed:after {
  /* symbol for "collapsed" panels */
  content: "\f054";    /* adjust as needed, taken from bootstrap.css */
}

/*scheduling profile*/
.recurring-schedule-current-label {
  text-transform: uppercase;
  color: var(--primary);
  font-size:12px;
}
/*Immunizations */
.immunization-date {
  float:left;
  margin-right: 20px; 
  border-radius: 4px; 
  border: 1px solid var(--secondary); 
  padding: 5px;
  margin-right:20px;
  margin-bottom:5px;
}

/*reactions*/
.reaction-item {
  display: inline-block;
  margin-right:6px;
}

.reaction-item img {
  width:16px;
  vertical-align: text-bottom;
  margin-right:6px;
 /* filter: invert(91%) sepia(17%) saturate(4798%) hue-rotate(198deg) brightness(86%) contrast(93%);*/
}
.reaction-item.reaction-item-block {
  display:block
}

.reaction-buttons {
  display:none;
}

.reaction-buttons a {
  margin-right:8px;
  display:inline;
  padding:10px;
}

.reaction-buttons img {
  width:24px;
  /*filter: invert(91%) sepia(17%) saturate(4798%) hue-rotate(198deg) brightness(86%) contrast(93%);*/
}

a.likeMoment {
  border: 1px solid var(--primary);
  background-color: #fafafa;
  border-radius:2px;
  padding:5px;
}

@media print {
  body {-webkit-print-color-adjust: exact;}
  .roster-report.page-break .contact-container {page-break-after: always;}
  .portfolio-bg-color { background-color: #808080 !important; }
  table { page-break-before: auto; }
  tr    { page-break-inside:avoid; page-break-after:auto }
  td    { page-break-inside:avoid; page-break-after:auto }
  thead { display:table-header-group }
  tfoot { display:table-footer-group }
  @page {
      size:  auto;   /* auto is the initial value */
      margin: 0mm;  /* this affects the margin in the printer settings */
  }
  div.header {
    display: none;
  }
  div.aside {
    display: none;
  }
  div.header-mobile {
    display: none;
  }
}

.row-eq-height {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display:         flex;
}

/*PIN CODE*/

.box.box-pin-code-checkin {
  border-top: none;
}
/* pin code checkin stuff */
.pinCodeAnnouncements li {
  list-style-type:none;
  padding-left:5px;
  border-left: 2px solid var(--primary);
  margin-left:30px;
}
.pinCodeAnnouncements {
  margin-left:0px;
  padding-left:0px;
}
.box.box-pin-code-checkin h4 {
  font-weight:600;
}
.box.box-pin-code-checkin textarea {
  height:24px;
}
.box-pin-code-checkin .reminder-block {
  margin-left:30px;
  font-size:16px;
  margin-bottom:10px;
  border-left: 2px solid var(--primary);
  padding-left:5px;
}
.box-pin-code-checkin .reminder-block b {
  font-weight:600;
}
.box-pin-code-checkin #momentForm label {
  font-size:115%;
  padding-left:25px;
}
.pin-code-box::-webkit-outer-spin-button,
.pin-code-box::-webkit-inner-spin-button  {
  -webkit-appearance: none;
  margin: 0;
}

/*empty Layout */
.fullPageCentered {
  text-align:center;
}
.fullPageWhite {
  background-color:#fff;
  width:100%;
  height:100%;
  overflow-y:scroll;
  overflow-x:hidden;
  padding:0;
  margin:0;
  top:0;
  left:0;
  position:fixed;
}
/*Registration */
.fullPageCentered .registration-form {
  text-align:left;
}
/* captive manage payments */
.captive-manage-payments { 
  max-width:480px;
  margin: 0 auto;
}
.captive-manage-payments .method-block {
  border:1px solid #aaa;
  margin-bottom:10px;
  padding:5px;
  border-radius:5px;
}
/* adyen styling */
#customCard-container label {
  width:100%
}
#customCard-container span.customCard-field {
  width:100%;
  display:block;
  height:32px;
  padding:5px;
  border: 1px solid #ccc;
  margin-top:5px;
  margin-bottom:10px;
}
/*Kiosk*/

.fullPageGray {
  background-color:var(--gray);
  position:fixed;
  padding:0;
  margin:0;
  top:0;
  left:0;
  width:100%;
  height:100%;
  overflow-y:scroll;
  overflow-x:hidden;
}

/* Data Validation */
.dv-container-column {
  display: flex;
  flex-direction: column;
}
.dv-container-row {
  display: flex;
  flex-direction: row;
}
.dv-center-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.dv-options {
  display:flex;
  flex:1;
  justify-content:flex-end;
  align-items:center;
  cursor:pointer;
}
.dv-options-item {
  font-size:16px;
  margin-top:8px;
  margin-bottom:8px;
  padding-left:16px;
}
.dv-details-container {
  display:flex;
  align-items:center;
  justify-content:center;
  cursor:pointer;
}
.dv-details-text {
  font-size:18px;
  color: var(--primary);
  text-align:center;
}

/* dropdown tagger */
.selectize-dropdown-content .name-label {
  display:block;
  font-size:16px;
  padding:3px;
  font-weight:500
}
.selectize-dropdown-content .name-caption {
  display:block;
  font-size:13px;
  padding:3px;
}
.selectize-control.multi .selectize-input > div {
  padding:5px;
  background-color:var(--primary);
  color:#fff;
}
.selectize-control.plugin-remove_button [data-value] .remove {
  font-size:18px;
  padding:5px 0 0 0;
}

/*reports*/

#subsidy-report-table td.no-wrap {
  white-space: nowrap;
  padding: 0 16px;
}

#subsidy-report-table th.no-wrap {
  white-space: nowrap;
  padding: 10 16px;
}

.total-column {
  border-top:1px solid #666;
  text-align:right
}

/* billing report */
.table > tbody > tr.no-border > td,
.table > tbody > tr.no-border > th,
.table > tbody > tr.no-border > td > div,
.table > tbody > tr.no-border > th > div {
  border-top: none !important;
}

.table>tbody>tr.bottom-border-only>td,
.table>tbody>tr.no-border-only>th {
  border-top:none;
  border-bottom: 1px solid #ccc;
}

.attendance-grid .table tbody tr td, .attendance-grid .table tbody tr th {
  border: 1px solid #aaa;
  text-align: center
}
.attendance-grid .table>tbody>tr>th {
  border-top: 1px solid #aaa;

}

/* family balance report stuff */
.fb-collapsable.collapsed {
  display:none
}
.fb-expandable i.fa-chevron-down {
  display:none
}
.fb-expandable.expanded i.fa-chevron-right {
  display:none
}
.fb-expandable.expanded i.fa-chevron-down {
  display:inline
}
.fb-expandable {
  background-color:#efefef
}
.fb-expandable.expanded .fb-collapsed-balance span {
  display:none
}

/* california attendance register */
#californiaAttendanceRegisterTable th {
  vertical-align: bottom;
}
#californiaAttendanceRegisterTable th.centered {
  text-align: center;
}
#californiaAttendanceRegisterTable td.centered {
  text-align: center;
}

/* dashboard widget activity timeline */
.widget-activity .timeline.timeline-6:before {
  left: 105.5px;
}
.widget-activity .timeline.timeline-6 .timeline-item .timeline-label {
  width: 105px;
}
/* dashboard widget classroom ratios */

.ratio-table {
  display:table;
  width:100%
}
.ratio-group-row {
  width:100%;
  display:table-row;
}
.ratio-group-row:last-of-type > div {
  border-bottom: none
}
.ratio-group-row > div {
  display: table-cell;
  border-bottom:1px solid #ccc;
}

.ratio-group-row > div.ratio-group-name {
  width:100px;
  font-size:115%;
  vertical-align:middle;
}
.ratio-group-row > div.ratio-group-ratio {
  width:80px;
  text-align:center;
}
.ratio-group-row > div.ratio-group-extra-info {
  width:100px;
  text-align:center;
}
.ratio-group-row > div hr {
  margin:0;
}

.ratio-bar-bg {
  width:100%;
}
.ratio-group-bars {
  display:flex;
  flex-direction:column;
  flex:1;
  align-items:center;
  justify-content:center;
  margin-left:8px;
  margin-right:8px;
  vertical-align:middle;
}
.ratio-bar-people {
  background-color: var(--people-ratio-template-color);
  height:12px;
  margin-bottom:4px;
  display:inline-block;
}
.ratio-bar-staff {
  background-color: var(--people-ratio-staff-template-color);
  height:12px;
}
.ratio-bar-capacity {
  background-color:#eee;
  height:12px;
  display:inline-block;
  margin-bottom:4px;
}
.ratio-bar-bg > div {
  margin-right:-3px;
}
.ratio-number {
  font-size:110%;
  font-weight:600;
}
.ratio-label-container {
  display:flex;
  flex:1;
  align-items:center;
  justify-content:flex-end;
}
.ratio-label {
  color:#ffffff;
  font-weight:bold;
  min-width:100px;
  border-radius:24px;
  text-align:center;
}

.room-enrollments-container {
  display:flex;
  flex-direction:column;
  margin:8px 16px 8px 16px;
  padding-bottom:8px;
  border-bottom:1px solid #ccc;
}
.room-enrollments-label {
  color:var(--primary);
  font-size:18px;
  font-weight:400;
}

/* FOOD */
.food-list-item {
  border-bottom: 1px solid #ccc;
  margin-bottom: 3px;
  font-size: 18px;
}

/* TOAST */
.connection-status-wrapper {
  position:relative;
  background-color: var(--danger);
  color:#FFFFFF;
  font-weight:bolder;
  text-align:center;
  padding: 3px 15px 3px 15px;
}

/*Calendar and Lists*/
.list-table {
  display: table;
  width: 100%;
  border-collapse: collapse;
}

.list-table-row {
  background-color: #fff;
  margin-bottom: 10px;
  margin-left: 0px;
  margin-right: 0px;
  border: 1px solid #ececec;
  border-radius: 2px;
  height: 70px;
  display: table-row;
}

.list-table-row.selected {
  background-color: #E0FFE0;
}

.list-table.people-grid .list-table-row.selected .people-list-column-name a {
  background-color: #E0FFE0;
}

.list-table-row.clickable-row {
  cursor: pointer;
}

.list-table-spacer-row {
  display: table-row;
  height: 10px;
}

.list-table-column {
  vertical-align: middle;
  display: table-cell;
  font-size: 16px;
  line-height: 18px;
}

@media (max-width: 768px) {
  .list-table-column {
    display: block;
    padding-left: 10px;
  }

  .list-table-column.first-column {
    margin-top: 10px;
  }

  .list-table-column.last-column {
    margin-bottom: 10px;
  }
}

.list-table-column.first-column {
  padding: 0 10px 0 10px;
}

.list-table-column.last-column {
  padding: 0 10px 0 10px;
}

.list-table-column a {
  font-size: 18px;
  line-height: 20px;
  font-weight: bolder;
}

#group-listing .list-table-column {
  padding: 0 10px 0 10px;
}

#announcements-listing .list-table-column {
  padding: 0 10px 0 10px;
}

#curriculum-listing .list-table-column {
  padding: 0 10px 0 10px;
}

#food-listing .list-table-column {
  padding: 0 10px 0 10px;
}

/* calendar */
.calendar .filter-row {
  display: flex;
  flex-direction: row;
}

.calendar .filter-item {
  flex-grow: 1;
}

.calendar .filter-item:last-child * {
  float: right;
}

.calendar .filter-item .form-control {
  width: unset;
}

.calendar .filter-row .date-adjustment a {
  line-height: 36px;
  font-size: 24px;
  margin: 5px;
  font-weight: 400;
  color: #888;
}

@media (max-width: 768px) {
  .calendar .filter-row .date-adjustment .move-date {
    display: none
  }

  .calendar .filter-item select {
    font-size: 20px;
    height: 36px;
    margin-top: 12px;
    padding: 4px 10px;
  }

  .checkbox label {
    font-size: 16px;
  }
}

.calendar .filter-row .date-range-display {
  color: #888;
  font-size: 24px;
  font-weight: 600;
}

.calendar .date-display div {
  display: inline-block;
}

.calendar-detail {
  overflow-x: scroll;
}

.calendar-detail .list-table {
  /* margin-bottom: 15px; */
}

.calendar-detail .list-table-row {
  background-color: unset;
  border: none;
  /* border-top: 1px solid #ccc; */
}

.calendar-detail .section-title {
  color: #333;
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
  margin-bottom: 10px;
}

.calendar-detail .section-title .fa {
  color: #666;
}

@media (max-width: 768px) {
  .calendar-detail .section-title .fa {
    position: absolute;
    top: 0;
    right: 10px;
    font-size: 12px;
  }
}

.calendar-detail .calendar-day-item {
  border-left: 5px solid var(--primary);
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: #fefefe;
  -webkit-box-shadow: 0px 0px 30px 0px rgba(82, 63, 105, 0.05);
  box-shadow: 0px 0px 30px 0px rgba(82, 63, 105, 0.05);
}

.calendar-detail .calendar-day-item p {
  line-height: 24px;
  margin: 0;
}

.calendar-day-item.theme-header {
  background-color: var(--lighter-primary);
}

.calendar-day-item.theme-item {
  margin-left: 10px;
  border-left: none;
}

.calendar-grid-table .calendar-item.theme-header {
  background-color: var(--lighter-primary);
}

.calendar-grid-table .calendar-item.theme-item {
  margin-left: 10px;
  border-left: none;
}

.calendar-detail .list-table-column {
  padding: 5px;
}

.calendar-detail .list-table-column .col-md-10 {
  padding-top: 5px;
}

.calendar-detail .calendar-item.clickable {
  cursor: pointer;
}

.calendar-day-item .title {
  font-size: 18px;
  font-weight: 600;
}

.calendar-day-item .sub-title {
  font-size: 12px;
  text-transform: uppercase
}

.calendar-day-item .sub-header {
  font-size: 12px;
  color: var(--primary)
}

.calendar-grid-table {
  width: 100%
}

/*
.calendar-grid-col:first-of-type {
  width:10%;
  font-size:11px;
  text-transform: uppercase;
  font-weight:600;
}
*/
.calendar-grid-col {
  width: 33%
}

.calendar-grid-table.week .calendar-grid-col {
  width: 20%
}

.calendar-staff-card {
  background: #fff
}

.calendar-grid-table.week.staff .calendar-grid-col {
  width: 12%
}

.calendar-grid-table.week.staff .calendar-grid-col {
  width: 12%
}

.calendar-grid-table.week.staff .calendar-grid-row th,
.calendar-grid-table.week.staff .calendar-grid-row td
{
  padding-left: 10px;
  text-align: left;
  border-left: 0;
  border-right: 0;
}

.calendar-grid-table.week.staff .calendar-grid-row th {
  border-bottom: 2px solid;
}

.calendar-grid-table.week.staff .calendar-grid-row td:first-child {
  padding-left: 20px;
}

.calendar-grid-table.week.staff .calendar-grid-row td:first-child.section-header {
  background-color: #fff;
  text-align: left;
  padding-left: 10px;
}

.calendar-grid-table.week.staff .calendar-grid-row.child-schedule-row td {
  vertical-align: middle;
  border-bottom: 2px solid;
}

.calendar-grid-table.week.staff td.person-link {
  cursor: pointer;
  color: var(--primary);
}

.print-only {
  display: none;
}

@media print {
  .print-only {
    display: initial;
  }
}

/*
.calendar-grid-table.week .calendar-grid-col:first-of-type {
  width:10%
}
*/
.list-table-column.week, .list-table-column.month {
  padding: 0;
}

.calendar-grid-row .section-header {
  text-align: center;
  font-weight: 600;
  background-color: #fafafa;
  padding: 10px 0;
}

th.calendar-grid-col {
  text-align: center;
  border-bottom: 1px solid #aaa;
  padding: 10px 0;
}

td.calendar-grid-col {
  vertical-align: top;
  padding: 5px;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}

td.calendar-grid-col:last-of-type {
  border-right: none;
}

.calendar-grid-table tr:last-of-type td.calendar-grid-col {
  border-bottom: none;
}

.calendar-grid-table .calendar-item {
  background-color: #fefefe;
  font-size: 13px;
  border-radius: 3px;
  border-left: 2px solid var(--primary);
  margin-bottom: 3px;
  padding: 5px;
  font-weight: 600;
  -webkit-box-shadow: 0px 0px 30px 0px rgba(82, 63, 105, 0.05);
  box-shadow: 0px 0px 30px 0px rgba(82, 63, 105, 0.05);
}

.calendar-grid-table .calendar-item .sub-title {
  font-weight: normal;
  font-size: 11px;
}

.calendar-grid-table .calendar-item .sub-header {
  font-weight: normal;
  font-size: 12px;
  color: var(--primary)
}

.calendar-grid-table.month .calendar-grid-col {
  width: 14%;
  border-right: 1px solid #ccc
}

.calendar-grid-table.month .calendar-grid-col:last-of-type {
  width: 14%;
  border-right: none
}

.calendar-grid-table.month .calendar-day-block {
  min-height: 130px;
  position: relative;
}

.calendar-grid-table.month .calendar-grid-col:first-of-type {
  width: 14%;
  font-size: unset;
  text-transform: unset;
}

.calendar-grid-table.month .calendar-grid-col .day-label {
  font-size: 10px;
  font-weight: 600;
  text-align: right;
}

.calendar-grid-table.month .calendar-items {
  display: inline-block;
  margin-top: 5px;
  width: 100%
}

.calendar-grid-table.month .calendar-item .fa {
  font-weight: 300;
  font-size: 10px;
}

.calendar .nested {
  padding-left:8px;
  margin-top:16px;
}
.calendar .nested-double {
  padding-left:16px;
}
.calendar .main-header-bg {
  background-color:var(--primary);
  color: #fff;
}
.calendar .sub-header-bg {
  background-color:var(--dark-primary);
  color: #fff;
}

.calendar .event-tags .event-tag {
  background-color: var(--dark-primary);
  padding:2px 4px;
  font-size:11px;
  display:inline-block;
  border-radius:4px;
  color: #fff !important;
  font-weight:bolder;
  text-transform:uppercase;
  margin-bottom:4px;
}
.calendar-grid-table.week .event-tags .event-tag {
  font-size:9px;
  padding:2px;
  line-height:9px;
  border-radius:2px;
}
.calendar-grid-table.month .event-tags .event-tag  {
  font-size:8px;
  padding:1px;
  line-height:8px;
  border-radius:1px;
}

.calendar-item .event-stats {
  margin: 4px 0 4px 0;
}

.calendar-item .event-stats .event-stat {
  float:left;
  text-align:center;
  margin-right:6px;
  font-size:14px;
  font-weight:bolder;
  line-height:14px;
}
.calendar-item .event-stats .event-stat .event-stat-name {
  font-size:9px;
  font-weight:bold;
  line-height:9px;
}

/* ORG settings*/
.switchRow {
  display: table-row;
}
.switchRow > span {
  display: table-cell;
  vertical-align: middle;
}
.switchRow > span.switchHolder {
  padding-left:50px;
}

.switchRow .switchContainer {
  display:flex;
  flex-direction:row;
  align-items:center;
  margin-left:48px;
}

.override-switchHolder{
	padding-left:20px !important;
}

/* reportOrgsField */
.report-orgs-field.fullwidth .btn-group {
  width:100%;
}

/* css animations (fade-in) */
.fade-in {
  -webkit-animation: fadein 3s; /* Safari, Chrome and Opera > 12.1 */
     -moz-animation: fadein 3s; /* Firefox < 16 */
      -ms-animation: fadein 3s; /* Internet Explorer */
       -o-animation: fadein 3s; /* Opera < 12.1 */
          animation: fadein 3s;
}

@keyframes fadein {
  from { opacity: 0; }
  to   { opacity: 1; }
}

/* Firefox < 16 */
@-moz-keyframes fadein {
  from { opacity: 0; }
  to   { opacity: 1; }
}

/* Safari, Chrome and Opera > 12.1 */
@-webkit-keyframes fadein {
  from { opacity: 0; }
  to   { opacity: 1; }
}

/* Opera < 12.1 */
@-o-keyframes fadein {
  from { opacity: 0; }
  to   { opacity: 1; }
}

/* payer reconcile */
.payer-reconcile-table tr td {
  padding: 5px;
}
.payer-reconcile-table tr.invoice-header td {
  border-bottom: 1px solid #666;
}
.payer-reconcile-table tr.detail-item td {
  border-bottom: 1px solid #bbb;
}
.payer-reconcile-table tr.child-summary td {
  border-top: 1px solid #666;
  border-bottom: 1px solid #bbb;
}
.payer-reconcile-table .table tr.child-header:nth-of-type(even) {
  background-color: #fff; 
}
.payer-reconcile-table .table tr.child-header:nth-of-type(odd) {
  background-color: var(--lighter-primary); 
}
.payer-reconcile-table .person-select-all {
  font-size:14px
}
.payer-reconcile-table > .action-links {
  font-size:12px;
}
.payer-action-footer {
  display:none;
}
#payer-reconciliation-window {
  height:100%
}
#payer-reconciliation-window .card {
  height:100%
}
#payer-reconciliation-window .card-header {
  border-bottom: none;
}
#payer-reconciliation-box.payer-reconcile-results  {
  height: 90%;
  margin-right:15px;
  position:absolute;
  overflow: none;
  width: calc(100% - 60px);
}
.payer-reconcile-results  .payer-reconciliation-flex {
  overflow-y:auto;
  overflow-x:hidden;
  bottom:60px;
  height: calc(100% - 60px);
  
}
.payer-reconcile-results .payer-action-footer {
  position: absolute;
  height:60px;
  bottom:0;
  border-top:3px solid var(--primary);
  width:100%;
  margin-left:-10px;
  padding:5px 10px;
  display:inline-block;
  font-size:125%;
  font-weight:bold;
}
.payer-reconcile-results .recipient-name {
  font-size:18px;
}
.payer-reconcile-table .child-header td,
.payer-reconcile-table .apply-summary td,
.payer-reconcile-table .overpayment-info-row td {
  vertical-align: middle;
}
.payer-action-footer button {
  font-size:110%;
  margin-left:30px;
}
.payer-action-footer .add-non-family-funds {
  font-size: 12px;
  font-weight: 400;
}
.swal2-icon.swal2-info {
  color:#c199d4;
  border-color:#c199d4;
}
.payer-report-data h3,.payer-report-data h4 {
  text-align:center
}
.payer-report-data td, .payer-report-data th {
  padding:5px;
}
.payer-report-data .report-summary-row td {
  border-top:1px solid #333;
  border-bottom:1px solid #000;
  font-weight:bold;
}
.payer-report-data th {
  border-bottom:1px solid #000;
}
.payers-due-chart {
  width:100%;
  margin-bottom:16px;
}
.payers-due-chart-row {
  text-align:center;
  width:100%;
  font-size:11px;
  font-weight:bold;
  padding-bottom:10px;
}
.payers-due-chart-bar-holder {
  width:100%;
}
.payers-due-chart-bar {
  height:10px;
  float:left;
  min-width:1%;
  max-width:99%;
}
.payers-due-chart-bar.under30 {
  background-color:var(--primary);
}
.payers-due-chart-bar.over30 {
  background-color:var(--danger);
}
.payers-due-chart-label-holder {
  width:100%;
}
.payers-due-chart-label-item {
  width:50%;
  float:left;
  font-weight:normal;
}
.payers-due-chart-label-item .chart-amount {
  font-weight:bold;
  font-size:120%;
}
.payers-due-chart-label-item.over30 {
  text-align:left;
}
.payers-due-chart-label-item.under30 {
  text-align:right;
}
  /* deposits */
.deposit-actions {
  position:sticky;
  bottom:0;
  position: -webkit-sticky;
  background-color:#fff !important;
}

.carousel-indicators-top {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 15;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none; }

.carousel-indicators-bottom {
  position: absolute;
  right: 0;
  left:0;
  bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none; }

/* sticky header for class list report */
#classlistschedule-report-table thead .tr-fixed th {
  position:sticky;
  top:0;
  background:white;
}
#classlistschedule-report-table thead .tr-fixed th.second-fixed {
  top:40px;
}

#classlistschedule-report-table .day-slot-value {
  padding:0.15em;
}

/* ledger detail report */
#filter-plan-description .multiselect-native-select {
  /*width: 100% */
}
#filter-plan-description button {
  text-align:left
}

@media print {
  .pagebreak { page-break-before: always; } /* page-break-after works, as well */
}

/* wait list report */
#waitlist-report-container .table-striped th {
  background-color: #fff;
  border-top:none;
  border-bottom:none;
}
#waitlist-report-container .table-striped tbody tr td {
  border-top:none;
  border-bottom:none;
}
#waitlist-report-container .table-striped tbody tr:nth-of-type(odd) {
  background-color:#fff;
}
#waitlist-report-container .table-striped tbody tr:nth-of-type(even) {
  background-color:var(--lighter-primary);
}
#waitlist-report-container table a {
  font-weight:bold;
}
#waitlist-report-container h6 {
  font-weight:bolder;
}
#waitlist-report-container table strong {
  font-weight:bold;
  font-size:125%
}

/* advanced family dashboard */
.icon-dashboard-family-billing {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='currentColor' d='M3.005 3.003h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1h-18a1 1 0 0 1-1-1v-16a1 1 0 0 1 1-1m1 2v14h16v-14zm4.5 9h5.5a.5.5 0 1 0 0-1h-4a2.5 2.5 0 1 1 0-5h1v-2h2v2h2.5v2h-5.5a.5.5 0 0 0 0 1h4a2.5 2.5 0 0 1 0 5h-1v2h-2v-2h-2.5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat no-repeat;
  background-position: center center;
  background-size: cover;
  width: 24px;
  height: 24px;
}