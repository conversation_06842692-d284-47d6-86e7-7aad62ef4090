<template name="_aside">
  
  <!--begin::Aside-->
  				<div class="aside aside-left aside-fixed d-flex flex-column flex-row-auto aside-bg" id="kt_aside">

  					<!--begin::Brand-->
  					<div class="brand flex-column-auto aside-bg-transparent" id="kt_brand">

  						<!--begin::Logo-->
  						<div class="btn btn-hover-icon-primary py-16px min-w-150px" id="kt_aside_mobile_toggle">
                            {{#if whiteLabelLogoOverride}}
                                <img alt="Logo" src="{{whiteLabelLogoOverride.small}}" class="max-w-80px max-h-136px rounded-big-logo" />
                            {{else}}
                                <img alt="Logo" src="/media/svg/icons/ll_brandmark.svg" class="max-w-80px max-h-136px rounded-big-logo" />
                            {{/if}}
  						</div>

  						<!--end::Logo-->
  					</div>

  					<!--end::Brand-->

  					<!--begin::Aside Menu-->
  					<div class="aside-menu-wrapper overflow-y-auto aside-bg-transparent" id="kt_aside_menu_wrapper">

  						<!--begin::Menu Container-->
              <!-- aside-menu-dropdown for class data-menu-dropdown="1" for attr-->
  						<div id="kt_aside_menu" class="aside-menu my-4 aside-bg-transparent" data-menu-vertical="1" data-menu-dropdown-timeout="500">

  							<!--begin::Menu Nav-->
  							<ul class="menu-nav">
  								
  								<li class="menu-item menu-icon-parent mobile-only" aria-haspopup="true">
                    <span class="menu-link" id="kt_header_mobile_toggle">
                      <span class="svg-icon menu-icon menu-icon-aside fad fa-bars"></span>
                      <span class="menu-text menu-text-aside">Menu</span>
                    </span>
  								</li>
                  
                  <li class="menu-item menu-icon-parent {{#if (isCurrentPage 'dashboard')}}menu-item-active{{/if}}" aria-haspopup="true">
                    <a href="/my-site" class="menu-link">
                      <span class="svg-icon menu-icon menu-icon-aside fad fa-columns"></span>
                      <span class="menu-text menu-text-aside" data-cy="dashboard-nav">Dashboard</span>
                    </a>
                  </li>
  								
  								<li class="menu-item menu-item-submenu menu-icon-parent {{#if (isCurrentPage 'manage')}}menu-item-open{{/if}}" aria-haspopup="true" data-menu-toggle="hover">
  									<a href="javascript:;" class="menu-link menu-toggle">
  										<span class="svg-icon menu-icon menu-icon-aside fad fa-users"></span>
  										<span class="menu-text menu-text-aside" data-cy="manage-nav">Manage</span>
  										<span class="menu-arrow menu-text-aside"></span>
  									</a>
  									<div class="menu-submenu">
  										<ul class="menu-subnav">
  											<li class="menu-item menu-item-dd-parent {{#if (isCurrentSubPage 'people')}}menu-item-active{{/if}}" aria-haspopup="true">
  												<a href="/people?tab=directory" class="menu-link">
  													<span class="menu-text menu-item-aside" data-cy="people-nav">People</span>
  												</a>
  											</li>
                        {{#if canSee "groups"}}
    											<li class="menu-item menu-item-dd-parent {{#if (isCurrentSubPage 'groups')}}menu-item-active{{/if}}" aria-haspopup="true">
    												<a href="/groups" class="menu-link">
    													<span class="menu-text menu-item-aside" data-cy="groups-nav">Groups</span>
    												</a>
    											</li>
                        {{/if}}
                        {{#if canSee "inquiries"}}
    											<li class="menu-item menu-item-dd-parent {{#if (isCurrentSubPage 'inquiries')}}menu-item-active{{/if}}" aria-haspopup="true">
    												<a href="/inquiries" class="menu-link">
    													<span class="menu-text menu-item-aside" data-cy="inquiries-nav">Inquiries</span>
    												</a>
    											</li>
                        {{/if}}
  										</ul>
  									</div>
  								</li>
                  {{#if canSeeContent}}
    								<li class="menu-item menu-item-submenu menu-icon-parent {{#if (isCurrentPage 'content')}}menu-item-open{{/if}}" aria-haspopup="true" data-menu-toggle="hover">
                      <a href="javascript:;" class="menu-link menu-toggle">
    										<span class="svg-icon menu-icon menu-icon-aside fad fa-bullhorn"></span>
    										<span class="menu-text menu-text-aside" data-cy="content-nav">Content</span>
    										<span class="menu-arrow menu-text-aside"></span>
    									</a>
    									<div class="menu-submenu">
    										<i class="menu-arrow"></i>
    										<ul class="menu-subnav">
                          {{#if canSee "announcements"}}
                            <li class="menu-item menu-item-dd-parent {{#if (isCurrentSubPage 'announcements')}}menu-item-active{{/if}}" aria-haspopup="true">
                              <a href="/announcements" class="menu-link">
                                <span class="menu-text menu-item-aside">Announcements</span>
                              </a>
                            </li>
                          {{/if}}
                          {{#if canSee "curriculum"}}
      											<li class="menu-item menu-item-dd-parent {{#if (isCurrentSubPage 'activities')}}menu-item-active{{/if}}" aria-haspopup="true">
      												<a href="/activities#curriculumDefinitionsTab" class="menu-link" data-cy="activities-nav">
      													<span class="menu-text menu-item-aside">Activities</span>
      												</a>
      											</li>
                          {{/if}}
                          {{#if canSee "documents"}}
                            <li class="menu-item menu-item-dd-parent {{#if (isCurrentSubPage 'documents')}}menu-item-active{{/if}}" aria-haspopup="true">
                              <a href="/documents" class="menu-link">
                                <span class="menu-text menu-item-aside">Documents</span>
                              </a>
                            </li>
                          {{/if}}
                          {{#if canSee "food"}}
      											<li class="menu-item menu-item-dd-parent {{#if (isCurrentSubPage 'food')}}menu-item-active{{/if}}" aria-haspopup="true">
      												<a href="/food" class="menu-link">
      													<span class="menu-text menu-item-aside">Food</span>
      												</a>
      											</li>
                          {{/if}}

                          <!-- {{#if canSee "forms"}}
                            <li class="menu-item menu-item-dd-parent" aria-haspopup="true">
                              <a href="#" class="menu-link">
                                <span class="menu-text menu-item-aside">Forms</span>
                              </a>
                            </li>
                          {{/if}} -->
    										</ul>
    									</div>
    								</li>
                  {{/if}}
                  <li class="menu-item menu-item-submenu menu-icon-parent {{#if (isCurrentPage 'time')}}menu-item-open{{/if}}" aria-haspopup="true" data-menu-toggle="hover">
                    <a href="javascript:;" class="menu-link menu-toggle">
                      <span class="svg-icon menu-icon menu-icon-aside fad fa-calendar-check"></span>
                      <span class="menu-text menu-text-aside" data-cy="time-nav">Time</span>
                      <span class="menu-arrow menu-text-aside"></span>
                    </a>
                      <div class="menu-submenu">
                          <ul class="menu-subnav">
                              {{#if canSee "calendar"}}
                                  <li class="menu-item menu-item-dd-parent {{#if
                                          (isCurrentSubPage 'calendar')}}menu-item-active{{/if}}" aria-haspopup="true">
                                      <a href="/calendar" class="menu-link">
                                          <span class="menu-text menu-item-aside">Calendar</span>
                                      </a>
                                  </li>
                              {{/if}}
                              {{#if canSee "reservations"}}
                                  <li class="menu-item menu-item-dd-parent {{#if
                                          (isCurrentSubPage 'scheduling')}}menu-item-active{{/if}}"
                                      aria-haspopup="true" >
                                      <a href="/scheduling#calendar" class="menu-link" data-cy="scheduling-tab-option">
                                          <span class="menu-text menu-item-aside">Scheduling</span>
                                      </a>
                                  </li>
                              {{/if}}
                              {{#if canSee "timeCards"}}
                                  <li class="menu-item menu-item-dd-parent {{#if
                                          (isCurrentSubPage 'time')}}menu-item-active{{/if}}" aria-haspopup="true">
                                      <a href="/time" class="menu-link">
                                          <span class="menu-text menu-item-aside">Time Cards</span>
                                      </a>
                                  </li>
                              {{/if}}
                          </ul>
                      </div>
                  </li>
                                {{#if canSee "reports"}}
                                    <li class="menu-item menu-item-submenu menu-icon-parent {{#if
                                            (isCurrentPage 'reports')}}menu-item-active{{/if}}" aria-haspopup="true"
                                        data-menu-toggle="hover">
                                        <a href="/reports" class="menu-link menu-toggle">
                                            <span class="svg-icon menu-icon menu-icon-aside fad fa-chart-pie"></span>
                                            <span class="menu-text menu-text-aside" data-cy="reports-nav">Reports</span>
                                        </a>
                                    </li>
                                {{/if}}
                                {{#if canSee "billing"}}
                                    <li class="menu-item menu-icon-parent {{#if
                                            (isCurrentPage 'billingAdmin')}}menu-item-active{{/if}}"
                                        aria-haspopup="true">
                                        <a href="{{billingRoute}}" class="menu-link menu-toggle">
                                            <span class="svg-icon menu-icon menu-icon-aside fad fa-dollar-sign"></span>
                                            <span class="menu-text menu-text-aside" data-cy="billing-nav">Billing</span>
                                        </a>
                                    </li>
                                {{/if}}
                                {{#if canSee "org"}}
                                    <li class="menu-item menu-icon-parent {{#if
                                            (isCurrentPage 'org')}}menu-item-active{{/if}}" aria-haspopup="true">
                                        <a href="/admin/org#orgSettings" class="menu-link menu-toggle">
                                            <span class="svg-icon menu-icon menu-icon-aside fad fa-tools"></span>
                                            <span class="menu-text menu-text-aside" data-cy="admin-nav">Admin</span>
                                        </a>
                                    </li>
                                {{/if}}
                                {{#if hasEnroll }}
                                  <li class="menu-item menu-icon-parent" aria-haspopup="true">
                                    <a href="https://my.childcarecrm.com" class="menu-link menu-toggle">
                                  {{#if hasWhiteLabel}}
                                      <span class="svg-icon menu-icon menu-icon-aside fa-swap-opacity fad fa-people-arrows"></span>
                                      <span class="menu-text menu-text-aside">Enroll</span>
                                  {{else}}
                                    <img alt="LineLeader Enroll"
                                    src="/media/svg/icons/ll_enroll_fullcolor.svg"
                                    class="btn btn-enroll"/>
                                  {{/if}}
                                    </a>
                                  </li>
                                {{/if}}
                            </ul>

                            <!--end::Menu Nav-->
                        </div>

                        <!--end::Menu Container-->
                    </div>

                    <!--end::Aside Menu-->
                </div>

    <!--end::Aside-->

</template>
