import { Template } from 'meteor/templating';
import _ from '../../../../lib/util/underscore';
import './_bare_aside.html';

Template._bare_aside.helpers({
  whiteLabelLogoOverride: function() {
    var host = window.location.host.split(".")[0];
    var enabledSites = Meteor.settings.public.whitelabel && Meteor.settings.public.whitelabel.enabled_sites;
    const meteorUser = Meteor.user(), currentOrg = meteorUser && meteorUser.fetchOrg();
    if (currentOrg && _.deep(currentOrg,"whiteLabel.assets.appLogo")) {
      x= {
        small: currentOrg.whiteLabel.assets.appLogoSmall,
        large: currentOrg.whiteLabel.assets.appLogo
      };
      
      return x;
    } else if (enabledSites && enabledSites.indexOf(host) > -1) {
      return {
        small: Meteor.settings.public.whitelabel[host].small_logo,
        large: Meteor.settings.public.whitelabel[host].large_logo
      }
    }
  },
});
