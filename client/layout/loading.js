import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';

import './loading.html';


Template.loading.rendered = function () {
	const query = FlowRouter.current().queryParams;
	const setLoc = query && query.setLoc;
	var thisLoc = location;
	if (setLoc == "home") {
		setTimeout(function () {
			thisLoc.replace("/")
		}, 1500);
	} else if (setLoc == "redirect") {
		console.log("desturl found:", query.desturl);
		FlowRouter.go(query.desturl);
	}
}

Template.loading.helpers({
	containerClasses() {
		return Template.instance().data?.containerClasses || '';
	}
});
