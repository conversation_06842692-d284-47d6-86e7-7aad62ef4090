import { Meteor } from 'meteor/meteor';

class ApiService {
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '');
    }

    /**
     * Sends a request to the specified endpoint on the server.
     *
     * @param {string} endpoint - The API endpoint to request.
     * @param {string} [method=GET] - The HTTP method to use.
     * @param {Object} [data=null] - The data to send with the request.
     * @param {Object} [additionalHeaders={}] - Additional headers to send with the request.
     * @throws {Error} If the server returns an unsuccessful response.
     *
     * @returns {Promise<Object>} The response data from the server.
     */
    async request(endpoint, method = 'GET', data = null, additionalHeaders = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const authToken = localStorage.getItem('Meteor.loginToken');

        if (!authToken) {
            throw new Error('Session expired. Please log in again.');
        }

        const headers = {
            'Content-Type': 'application/json',
            'x-auth-token': authToken,
            ...additionalHeaders
        };

        const options = {
            method,
            headers,
            ...(data && { body: JSON.stringify(data) })
        };

        try {
            const response = await fetch(url, options);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Error: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`API Error (${method} ${endpoint}):`, error.message);
            throw error;
        }
    }

    get(endpoint, additionalHeaders = {}) {
        return this.request(endpoint, 'GET', null, additionalHeaders);
    }

    post(endpoint, data, additionalHeaders = {}) {
        return this.request(endpoint, 'POST', data, additionalHeaders);
    }

    put(endpoint, data, additionalHeaders = {}) {
        return this.request(endpoint, 'PUT', data, additionalHeaders);
    }

    delete(endpoint, additionalHeaders = {}) {
        return this.request(endpoint, 'DELETE', null, additionalHeaders);
    }
}

export const apiService = new ApiService();