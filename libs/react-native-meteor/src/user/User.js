import AsyncStorage from '@react-native-async-storage/async-storage';

import Data from '../Data';
import { hashPassword } from '../../lib/utils';
import call from '../Call';
import _ from 'underscore';

const TOKEN_KEY = 'reactnativemeteor_usertoken';
const ACCOUNTS_KEY = 'tendlyrn_accountsv1';

module.exports = {
  async accountSwitch(user) {
    await AsyncStorage.setItem(TOKEN_KEY, user.token);
    Data._tokenIdSaved = user.token;
    this._userIdSaved = user.user._id;
    Data.notify('accountSwitch');
  },
  membershipSwitch() {
    Data.notify('membershipSwitch');
  },
  addAccount() {
    Data.notify('addAccount');
  },
  allSavedAccounts() {
    if (!this._tendlyRnAccounts) return [];
    /*
      EBH 1337 h4x -- Remove at some pt in the future beyond **********
    */
    const accountArr = _.reject(this._tendlyRnAccounts || [], (acc) => {
      if (acc && acc.id && acc.id.length > 18) return true;
      return false;
    })
    return accountArr;
  },
  allOtherAccounts() {
    if (!this._tendlyRnAccounts) return [];
    return _.reject(this._tendlyRnAccounts, (acc) => {
      return acc.id == this._userIdSaved;
    })
  },
  async getAllLocalStorageAccounts() {
    try {
      const allAccounts = await AsyncStorage.getItem(ACCOUNTS_KEY);
      if (allAccounts !== null) {
        return JSON.parse(allAccounts);
      }
    } catch (e) {
      console.warn('AsyncStorage getAllLocalStorageAccounts TENDLYRN error: ' + e.message);
      return [];
    }
  },
  async removeAllOtherAccounts(currentUserId) {
    let accountArr = [];
    try {
      const allAccounts = await AsyncStorage.getItem(ACCOUNTS_KEY);
      if (allAccounts !== null && currentUserId) {
        accountArr = JSON.parse(allAccounts);
        let filteredAccounts = accountArr.filter((acc) => {
          return acc.id === currentUserId;
        });
        await AsyncStorage.setItem(ACCOUNTS_KEY, JSON.stringify(filteredAccounts))
      }
    } catch (e) {
      console.log('AsyncStorage removeAllOtherAccounts error: ' + e.message);
    }
    this._tendlyRnAccounts = accountArr;
  },
  async accountSwitchLogout() {
    const savedId = this._userIdSaved
    let accountArr = [];
    try {
      const allAccounts = await AsyncStorage.getItem(ACCOUNTS_KEY);
      if (allAccounts !== null) {
        accountArr = JSON.parse(allAccounts);
        accountArr = accountArr.filter((acc) => {
          console.log("acc.id", acc.id)


          return acc.id === savedId;
        })
        await AsyncStorage.setItem(ACCOUNTS_KEY, JSON.stringify(accountArr))
      }
    } catch (e) {
      console.warn('AsyncStorage handleLogout TENDLYRN error: ' + e.message);
    }
    this._tendlyRnAccounts = accountArr;
    this.logout();
  },
  user() {
    if (!this._userIdSaved) return null;

    return this.collection('users').findOne(this._userIdSaved);
  },
  userId() {
    if (!this._userIdSaved) return null;

    const user = this.collection('users').findOne(this._userIdSaved);
    return user && user._id;
  },
  _isLoggingIn: true,
  loggingIn() {
    return this._isLoggingIn;
  },
  logout(callback) {
    call('logout', err => {
      this.handleLogout();
      this.connect();

      typeof callback == 'function' && callback(err);
    });
  },
  async handleLogout() {
    let accountArr = [];
    let tokenValue = await AsyncStorage.getItem(TOKEN_KEY);
    try {
      allAccounts = await AsyncStorage.getItem(ACCOUNTS_KEY);
      if (allAccounts !== null) {
        accountArr = JSON.parse(allAccounts);
        accountArr = _.reject(accountArr, (acc) => {
          return acc.token == tokenValue;
        })
      }

      await AsyncStorage.setItem(ACCOUNTS_KEY, JSON.stringify(accountArr));
    } catch (e) {
      console.warn('AsyncStorage handleLogout TENDLYRN error: ' + e.message);
    }

    await AsyncStorage.removeItem(TOKEN_KEY);
    Data._tokenIdSaved = null;
    this._userIdSaved = null;
    Data.notify('loggingOut');
  },
  loginWithPassword(selector, password, callback) {
    if (typeof selector === 'string') {
      if (selector.indexOf('@') === -1) selector = { username: selector };
      else selector = { email: selector };
    }

    this._startLoggingIn();
    call(
      'login',
      {
        user: selector,
        password: hashPassword(password),
      },
      (err, result) => {
        this._endLoggingIn();

        this._handleLoginCallback(err, result);

        typeof callback == 'function' && callback(err, result);
      }
    );
  },
  logoutOtherClients(callback = () => {}) {
    call('getNewToken', (err, res) => {
      if (err) return callback(err);

      this._handleLoginCallback(err, res);

      call('removeOtherTokens', err => {
        callback(err);
      });
    });
  },
  _login(user, callback) {
    this._startLoggingIn();
    this.call('login', user, (err, result) => {
      this._endLoggingIn();

      this._handleLoginCallback(err, result);

      typeof callback == 'function' && callback(err);
    });
  },
  _startLoggingIn() {
    this._isLoggingIn = true;
    Data.notify('loggingIn');
  },
  _endLoggingIn() {
    this._isLoggingIn = false;
    Data.notify('loggingIn');
    Data.notify("loggedIn");
  },
  async _handleLoginCallback(err, result) {
    if (!err) {
      //save user id and token
      await AsyncStorage.setItem(TOKEN_KEY, result.token);
      Data._tokenIdSaved = result.token;
      this._userIdSaved = result.id;

      //CUSTOM MANAGEMENT OF MULTIPLE USER ACCOUNT TOKENS AND IDS
      let allAccounts
      let accountArr = [];
      try {
        allAccounts = await AsyncStorage.getItem(ACCOUNTS_KEY);
        if (allAccounts !== null) {
          accountArr = JSON.parse(allAccounts);
          accountArr = _.reject(accountArr, (acc) => {
            return acc.id == result.id;
          })
        }
        const user = this.collection('users').findOne(result.id) ?? Meteor.user()
        accountArr.push({ id: result.id, token: result.token, user, lastLogin: new Date(),  })
        await AsyncStorage.setItem(ACCOUNTS_KEY, JSON.stringify(accountArr));
      } catch (e) {
        console.warn('AsyncStorage _handleLoginCallback TENDLYRN error: ' + e.message);
      }
      this._tendlyRnAccounts = accountArr;
      // END CUSTOM MANAGEMENT OF TENDLYRN MULTIPLE USER ACCOUNT TOKENS AND IDS

      Data.notify('onLogin');
    } else {
      // EBH: we only want to logout if token is bad
      if (err && err.loginWithToken) {
        Data.notify('onLoginFailure');
        this.handleLogout();
      }
    }
    Data.notify('change');
  },
  _loginWithToken(value, callback) {
    Data._tokenIdSaved = value;
    if (value !== null) {
      this._startLoggingIn();
      call('login', { resume: value }, (err, result) => {
        this._endLoggingIn();
        if (err) {
          this._handleLoginCallback({loginWithToken: true}, result);
        } else {
          this._handleLoginCallback(err, result);
        }
        typeof callback == 'function' && callback(err);
      });
    } else {
      this._endLoggingIn();
    }
  },
  getAuthToken() {
    return Data._tokenIdSaved;
  },
  async _loadInitialUser() {
    let value
    try {
      value = await AsyncStorage.getItem(TOKEN_KEY);
    } catch (error) {
      console.warn('AsyncStorage error: ' + error.message);
    } finally {
      this._loginWithToken(value);
    }
  },
};
