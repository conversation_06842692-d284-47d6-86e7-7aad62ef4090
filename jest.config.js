const lodash = require('lodash')
module.exports = {
	setupFiles: ['./__tests__/setupTests.js','./__tests__/jest.setup.js'],
	testRegex: '/__tests__/.*spec\.js',
	moduleNameMapper: {
		"^meteor/momentjs:moment$": "moment",
		"^meteor/ostrio:flow-router-extra$": "<rootDir>/__tests__/helpers/flowRouter.js",
		"^meteor/peerlibrary:aws-sdk$": "<rootDir>/__tests__/mocks/meteor/aws-sdk.js",
		"^meteor/mongo$": "<rootDir>/__tests__/helpers/meteorMock.js",
		"^meteor/random$": "<rootDir>/__tests__/mocks/meteor/random.js",
		"^meteor/logging$": "<rootDir>/__tests__/mocks/meteor/logging.js",
		"^meteor/fetch$": "<rootDir>/__tests__/mocks/meteor/fetch.js",
		"^meteor/http$": "<rootDir>/__tests__/mocks/meteor/http.js",
		"^meteor/meteor$": "<rootDir>/__tests__/mocks/meteor/meteor.js",
		"^meteor/blaze$": "<rootDir>/__tests__/mocks/meteor/blaze.js",
		"^meteor/spacebars-compiler$": "<rootDir>/__tests__/mocks/meteor/spacebars-compiler.js",
		"^meteor/session": "<rootDir>/__tests__/mocks/meteor/session.js",
		"^meteor/accounts-base": "<rootDir>/__tests__/mocks/meteor/accounts-base.js",
		"^meteor/reactive-var": "<rootDir>/__tests__/mocks/meteor/reactive-var.js",
		"^meteor/email": "<rootDir>/__tests__/mocks/meteor/email.js",
		"^meteor/templating": "<rootDir>/__tests__/mocks/meteor/templating.js",
		"^meteor/(.*)$": "<rootDir>/__tests__/mocks/meteor/$1"
	},
	transform: {
		"^.+\\.js$": "babel-jest"
	},
	transformIgnorePatterns: [
		"node_modules/(?!(chai|sinon)/)"
	],
	collectCoverageFrom: [
		"server/**/*.{js,jsx,ts,tsx}",
		"api/**/*.{js,jsx,ts,tsx}",
		"lib/**/*.{js,jsx,ts,tsx}",
	],
	coveragePathIgnorePatterns: [
		"/node_modules/",
		"/__tests__/",
	],
	globals: {
		'_': lodash,
		'numeral': require('currency.js')
    }
};
