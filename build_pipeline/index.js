// EXAMPLE USAGE from working directory of momentpathmobile:
// WORKSPACE=/path/to/momentpathmobile node ./build_pipeline/index.js
process.env.USE_FASTLANE = true;

const {Consumer} = require('sqs-consumer');
const {
  buildMobileApp,
  buildIosApp,
  buildAndroidApp,
  buildMobileAppLegacy,
} = require('./build_commands');

const handleMessage = async message => {
  console.log(message);
  const buildConfig = JSON.parse(message.Body);
  const {legacy, platform = 'both'} = buildConfig;
  try {
    if (legacy) {
      await buildMobileAppLegacy(buildConfig);
    } else {
      switch (platform.toLowerCase()) {
        case 'ios':
          console.log('building ios only');
          await buildIosApp(buildConfig);
          break;
        case 'android':
          console.log('building android only');
          await buildAndroidApp(buildConfig);
          break;
        default:
          console.log('building ios and android');
          await buildMobileApp(buildConfig);
      }
    }

    console.log('next message');
  } catch (e) {
    console.log('build failed');
    console.log(e);
  }
};

const app = Consumer.create({
  region: 'us-east-1',
  queueUrl:
    'https://sqs.us-east-1.amazonaws.com/799420973518/mobile_build_pipeline',
  handleMessage,
});

app.on('error', err => {
  console.error(err.message);
});

app.on('processing_error', err => {
  console.error(err.message);
});

app.start();

// Example usage:
// buildIosApp({ customer: 'right_at_school', iosMarketingVersion: '1.63.1', platform: 'ios', branch: 'EN-87-mobile-pipeline' })
// buildAndroidApp({ customer: 'right_at_school', androidMarketingVersion: '1.63.1', platform: 'android', branch: 'EN-87-mobile-pipeline' })
// buildMobileApp({ customer: 'right_at_school', iosMarketingVersion: '1.63.1', androidMarketingVersion: '1.63.1', platform: 'both', branch: 'EN-87-mobile-pipeline' })
