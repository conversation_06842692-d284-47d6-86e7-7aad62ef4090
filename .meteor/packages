# Meteor packages used by this project, one per line.
# Check this file (and the other files in this directory) into your repository.
#
# 'meteor add' and 'meteor remove' will edit this file for you,
# but you can also edit it by hand.

#cultofcoders:redis-oplog
#disable-oplog
meteor-base@1.5.2           # Packages every Meteor app needs to have
mobile-experience@1.1.2       # Packages for a great mobile UX
mongo@2.0.2                  # The database Meteor supports right now
blaze-html-templates    # Compile .html files into Meteor Blaze views
reactive-var@1.0.13           # Reactive variable for tracker
tracker@1.3.4                 # Meteor's client-side reactive programming library

standard-minifier-css@1.9.3
standard-minifier-js@3.0.0    # JS minifier run for production mode
es5-shim@4.8.1             # ECMAScript 5 compatibility for older browsers
ecmascript@0.16.9              # Enable ECMAScript2015+ syntax in app code
typescript@5.4.3              # Enable TypeScript syntax in .ts and .tsx modules
shell-server@0.6.0            # Server-side component of the `meteor shell` command

underscore
ccorcos:subs-cache
matb33:collection-hooks
seba:method-hooks@4.0.0-rc.0
session
stevezhu:lodash
spacebars-compiler
accounts-password
http
cleandersonlobo:sweetalert2
reactive-dict
ostrio:meteor-root
percolate:migrations
jquery
reywood:publish-composite
ddp-rate-limiter
ostrio:cstorage
arch:ace-editor
numeral:numeral
froatsnook:valid-email
webapp
momentjs:moment
check
random
edgee:slingshot
ostrio:base64
dynamic-import
nourharidy:ssl
email
fetch
montiapm:agent
force-ssl
dburles:factory
meteortesting:mocha
velocity:meteor-stubs
xolvio:cleaner
spacebars
mslobodan:reloader
hot-module-replacement
blaze-hot
ostrio:flow-router-extra@3.9.0
accounts-passwordless
quave:synced-cron
ground:db
