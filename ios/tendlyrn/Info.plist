<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>App Uses Non-Exempt Encryption </key>
	<false/>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>LineLeader</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>mp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>mp</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CodePushDeploymentKey</key>
	<string>1Iq-3EzWinJe07h4hKqaxr2SRpG7HJQwjYzD4</string>
	<key>DTXApplicationID</key>
	<string>408c4b97-ca6f-4efa-b0a8-415dda4cb8d8</string>
	<key>DTXBeaconURL</key>
	<string>https://bf43303lfz.bf.dynatrace.com/mbeacon</string>
	<key>DTXCrashReporting</key>
	<true/>
	<key>DTXExcludedControls</key>
	<array>
		<string>PickerView</string>
		<string>Switch</string>
	</array>
	<key>DTXFlavor</key>
	<string>react_native</string>
	<key>DTXLogLevel</key>
	<string>ALL</string>
	<key>DTXStartupLoadBalancing</key>
	<true/>
	<key>DTXUserOptIn</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>LineLeader uses your media library to add media to moments.</string>
	<key>NSCameraUsageDescription</key>
	<string>LineLeader uses your camera to add photos to moments.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>LineLeader uses your location for check-in and check-out.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>LineLeader uses your location to aid accuracy in moment entry.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>LineLeader uses your microphone to add media to moments.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>LineLeader lets you add photos and videos from the app to your photo library.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>LineLeader uses your photo library to add photos to moments.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Nucleo.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Roboto_medium.ttf</string>
		<string>Roboto.ttf</string>
		<string>rubicon-icon-font.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDarkContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>bugsnag</key>
	<dict>
		<key>apiKey</key>
		<string>33b2d76d54d686eeedcb6293b7d319ae</string>
	</dict>
</dict>
</plist>
