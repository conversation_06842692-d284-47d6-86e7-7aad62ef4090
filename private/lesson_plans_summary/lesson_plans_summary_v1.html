<html >
   <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
      <meta name="format-detection" content="telephone=no">
	    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	    <meta name="x-apple-disable-message-reformatting" />
      <link rel="stylesheet" type="text/css" href="{{cssPath}}">
      <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i&amp;subset=latin-ext,vietnamese" rel="stylesheet">
      <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i,800,800i&amp;subset=cyrillic,cyrillic-ext,greek,greek-ext,latin-ext,vietnamese" rel="stylesheet">
   </head>
   <body style="padding:0px; margin:0; background:#ffffff; ">
    <div id="oflow_view">
      <div style="display:flex;flex-direction:column;align-items:center;margin-bottom:24px;">
        <img valign="middle" src="{{getLogoSrc}}" style="display: inline-block;" width="300" alt="logo"  border="0" align="top">
        <span class="primary_color_p" style="font-size:24px;font-weight:bold;color:{{getPrimaryColor}}">Lesson Plan: {{groupName}}</span>
        <span style="font-size:24px;">{{startDate}} - {{endDate}}</span>
      </div>
      {{#each matchedThemes}}
          {{#each avr in activities}}
            <div class="avoid" style="display:table;width:100%;margin-bottom:24px;">
              {{#each triple in avr}}
                <div style="width:30%;float:left;margin-left:8px;margin-right:8px;">
                  <div style="display:block;width:100%;margin-bottom:8px;">
                      <span class="nucleo-icon nucleo_o nucleo-icon-school" style="margin-right:16px;font-size:16px;color:{{getPrimaryColor}}"></span>
                    <span class="primary_color_p" style="font-size:16px;font-weight:bold;color:{{getPrimaryColor}}">{{triple.title}}</span>
                  </div>
                  {{#each triple.activities}}
                    <div style="margin-bottom:8px;"><p class="avoid">{{message}}</p></div>
                  {{/each}}
                </div>
              {{/each}}
            </div>
          {{/each}}
      {{/each}}
      </div>
   </body>
   <style>
     #oflow_view { display: flex; flex-direction: column; overflow-y: scroll;}
     .avoid {
        page-break-inside: avoid !important;
        margin: 4px 0 4px 0;  /* to keep the page break from cutting too close to the text in the div */
      }
     @media print {
       .primary_color_p { color: {{getPrimaryColor}} !important }
       #oflow_view {overflow: visible !important;}
       @page {     size: auto;   /* auto is the initial value */

    /* this affects the margin in the printer settings */
    margin: 3mm 3mm 3mm 3mm;   }
     }
   </style>
</html>
