<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="format-detection" content="telephone=no">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="x-apple-disable-message-reformatting" />
    <title>Summary email</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Manrope:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i&amp;subset=latin-ext,vietnamese" />
    <style type="text/css">
        /* Outlook link fix */
        #outlook a {
            padding: 0;
        }

        body {
            font-family: Manrope, Helvetica, "sans-serif" !important;
        }

        .centerTdContents {
            text-align: center;
            vertical-align: middle;
        }

        .emailButton {
            padding-left: 24px;
            padding-right: 24px;
            padding-top: 16px;
            padding-bottom: 16px;
            border-radius: 6px;
            float: left;
            clear: left;
        }

        .emailButtonText {
            font-style: normal;
            font-weight: 600;
            font-size: 12px;
            line-height: 18px;
            color: white;
        }

        .emailH1 {
            font-style: normal;
            font-weight: 600;
            font-size: 20px;
            line-height: 23px;
            text-align: center;
        }

        .emailSubTitle {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #404040;
        }

        .emailSubText {
            font-style: normal;
            font-weight: normal;
            font-size: 12px;
            line-height: 16px;
            color: rgba(0,0,0,.5);
        }

        .personClassroom {
            font-style: normal;
            font-weight: 500;
            font-size: 14px;
            line-height: 16px;
            text-align: center;
            color: rgba(0,0,0,.5);
        }

        .cardContainer {
            padding: 24px;
            background: #FFFFFF;
            box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.05), 0px 1px 1px rgba(0, 0, 0, 0.05);
            border-radius: 16px;
            margin-bottom: 24px;
        }

        .iconDiv {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            box-shadow: 0px 2px 4px rgba(31, 41, 55, 0.06), 0px 4px 6px rgba(31, 41, 55, 0.1);
        }

        .cardHeaderPadding {
            padding-bottom: 16px;
        }

        .cardInnerSectionPadding {
            padding-bottom: 12px;
        }

        .tdOuterPadding {
            padding-bottom: 16px;
        }

        .tdTimelineOuterPadding {
            padding-bottom: 24px;
        }

        .tdInnerPadding {
            padding-right: 12px;
        }

        .footerText {
            font-style: normal;
            font-weight: normal;
            font-size: 14px;
            line-height: 20px;
            /* identical to box height, or 143% */

            text-align: center;

            color: rgba(64,64,64,.25);
        }

        /* Hotmail background &amp; line height fixes */
        .ExternalClass {
            width: 100% !important;
        }

        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        /* Image borders &amp; formatting */
        img {
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
            margin: 0 0 0 0 !important;
            border: 0 !important;
        }

        a img {
            border: none;
            margin: 0 0 0 0 !important;
        }

        .fad {
            position: relative;
            font-family: 'Font Awesome 5 Duotone';
            font-weight: 900;
        }

        .fad:before {
            position: absolute;
            color: {{ whiteLabel.primaryColor }};
            opacity: 1;
        }

        .fad:after {
            color: {{ whiteLabel.primaryColor }};
            opacity: 0.5;
        }

        .fad.fa-clipboard-check:after {
            content: "\f46c\f46c";
        }

        /* Re-style iPhone automatic links (eg. phone numbers) */
        .applelinks a {
            color: #222222;
            text-decoration: none;
        }

        /* Hotmail symbol fix for mobile devices */
        .ExternalClass img[class^=Emoji] {
            width: 10px !important;
            height: 10px !important;
            display: inline !important;
        }

        .tpl-content {
            display: inline !important;
            padding: 0 !important;
        }

        .show_on_mobile {
            display: none !important;
        }

        .hide_on_mobile {
            display: block !important;
        }

        .resize_img1 {
            border-radius: 20px;
            width: 468px
        }

        /* Media Query for mobile */
        @media screen and (max-width:800px) {
            .wrap100 {
                width: 100% !important;
            }

            .resize_img {
                max-width: 100% !important;
                height: auto !important;
            }
        }

        @media screen and (max-width:599px) {
            .pad1 {
                padding: 0 15px !important;
            }

            .d-none {
                display: none !important;
            }

            .resize_img1 {
                max-width: 100% !important;
                height: auto !important;
                width: 100%
            }
        }

        @media only screen and (max-width: 480px) {
            .font-size {
                font-size: 30px !important;
                line-height: 32px !important;
            }

            .font-resize2 {
                font-size: 25px !important;
                line-height: 30px !important;
            }

            .font-size1 {
                font-size: 18px !important;
                line-height: 20px !important;
            }

            .font-resize3 {
                font-size: 20px !important;
                line-height: 22px !important;
            }

            .image-resize {
                width: 48px !important;
            }

            .height {
                height: 15px !important;
            }

            .wrap1001 {
                width: 100% !important;
                display: block !important;
                text-align: center !important;
                float: none !important;
            }

            .text-center {
                text-align: center !important;
            }

            .wrap1002 {
                width: 120px !important;
                float: none !important;
                margin: 0 auto !important;
                text-align: center !important;
                display: block !important;
            }
        }

        /* Portrait and Landscape */
    </style>
</head>

<body style="padding:0; margin:0; padding-bottom:32px;">
<table style="background-color: {{backgroundColor}};" width="100%" border="0" align="left" cellpadding="0" cellspacing="0">
    <tr>
        <td align="center" valign="top">
            <table width="792" border="0" align="center" cellpadding="0" cellspacing="0" class="wrap100">
                <tr>
                    <td align="center" valign="top">
                        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                            <tr>
                                <td align="center" valign="top">
                                    <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
                                        <tr>
                                            <td align="center" valign="top" height="13" style="font-size:1px;line-height: 1px;">&nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td align="center" valign="middle" style="font-size:20px;line-height:46px;color:{{headerOrgNameColor}};font-weight:300;" class="pad1">

                                                <img valign="middle" src="{{whiteLabel.logo}}" style="display: inline-block;" width="300"
                                                     alt="logo" border="0" align="top">
                                                <br />
                                                <span style="text-align:center;display:block;font-style: normal;font-weight: normal;font-size: 20px;line-height: 24px;color:{{headerOrgNameColor}}">{{#if whiteLabel.orgName}}{{whiteLabel.orgName}}{{else}}{{orgName}}{{/if}}</span>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" height="25">&nbsp;</td>
                            </tr>
                        </table>
                        <table class="cardContainer" width="90%" border="0" align="center" cellpadding="0" cellspacing="0">
                            <tr>
                                <td width="38" align="left" valign="top" class="image-resize cardHeaderPadding" >
                                    <div>
                                        <img
                                                src="http://assets.momentpath.com/{{assetPrefix}}/clipboard-check.png"
                                                width="62"  alt="invite">
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td width="100%" class="cardHeaderPadding">
                                    <span class="emailH1" style="color: {{whiteLabel.primaryColor}};" >Registration Submission</span><br/>
                                    <span class="emailSubText">{{ emailDateStamp }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="cardHeaderPadding">
                                    <span class="emailSubText">Thank you for submitting your registration. Our team is verifying your subsidy information, which typically takes 24-48 hours. We will reach out once everything is confirmed or additional information is needed. Your patience is greatly appreciated.</span><br/>
                                </td>
                            </tr>
                        </table>
                        <table width="90%" border="0" align="center" cellpadding="0" cellspacing="0">
                            <tr>
                                <td align="center" valign="top" style="padding-top: 24px;padding-bottom: 24px;">
                                    <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0">
                                        <td align="right" valign="top" style="padding-right:8px;">
                                            <a href="{{whiteLabel.iosAppUrl}}">
                                                <img src="http://assets.momentpath.com/{{assetPrefix}}/momentpath_appstore.png" width="120" height="40" alt="ios app store">
                                            </a>
                                        </td>
                                        <td align="left" valign="top" style="padding-left:8px;">
                                            <a href="{{whiteLabel.androidAppUrl}}" >
                                                <img src="http://assets.momentpath.com/{{assetPrefix}}/momentpath_playstore.png"  width="135" height="40" alt="android app store">
                                            </a>
                                        </td>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" valign="top" class="tdInnerPadding">
                                    <span class="footerText">Sent by LineLeader on behalf of {{#if whiteLabel.longName}}{{whiteLabel.longName}}{{else if orgLongName}}{{orgLongName}}{{else}}{{orgName}}{{/if}}</span>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" valign="top" class="tdInnerPadding">
                                    <span class="footerText">© {{currentYear}} LineLeader • All rights reserved. 433 E Las Colinas Blvd #650, Irving, TX 75039</span>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" valign="top" class="tdInnerPadding" style="padding-bottom: 32px;"><span class="footerText">Visit the app to unsubscribe</span></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html>
