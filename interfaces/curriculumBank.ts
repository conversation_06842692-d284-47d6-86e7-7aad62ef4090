export interface CurriculumBank {
    _id?: string;
    bankId?: string;
    createdBy?: {
      personId?: string;
      orgName?: string;
      name?: string;
    };
    sourceData?: {
      headline?: string;
      notes?: string;
      materials?: string;
      internalNotes?: string;
      internalLink?: string;
      selectedTypes?: [string];
      selectedStandards?: [string];
      selectedAgeGroup?: string;
      mediaFiles?: [];
    };
    published?: boolean;
  }  