import { Groups } from '../lib/collections/groups';
import { Orgs } from '../lib/collections/orgs';
import { Reservations } from '../lib/collections/reservations';
import { getFteForMonth, sortGroupsByAge } from './classList';
import _ from '../lib/util/underscore';

const moment = require('moment-timezone');

/**
 * Formats a ratio as "1-X" from "X"
 * @param {string} ratio - The original ratio string
 * @returns {string} - The formatted ratio string
 */
export function formatRatio(ratio) {
  if (!ratio) return '';
  return `1:${ratio}`;
}

/**
 * Gets FTE projections for future months
 * @param {Object} params - Parameters for FTE projection
 * @param {string} params.orgId - Organization ID
 * @param {string} params.groupId - Group ID
 * @param {string} params.startMonth - Start month in MM/DD/YYYY format
 * @param {Array} params.allReservations - All reservations
 * @param {number} params.months - Number of months to project (1-4)
 * @returns {Object} - FTE projections for specified future months
 */
export async function getFteFutureProjections(params) {
  const { orgId, groupId, startMonth, allReservations, months = 4 } = params;
  const projections = {};

  for (let i = 1; i <= Math.min(months, 4); i++) {
    const projectionMonth = moment(startMonth, "MM/DD/YYYY")
      .add(i, 'month')
      .startOf('month')
      .add(5, "days")
      .startOf('week')
      .add(1, "days")
      .format("MM/DD/YYYY");

    const projectionResult = await getFteForMonth({
      orgId,
      groupId,
      includeTransition: true,
      startMonth: projectionMonth,
      allReservations,
      includeInactive: true
    });

    projections[`currentFtePlus${i}Month${i > 1 ? 's' : ''}`] = projectionResult.totalCount;
  }

  return projections;
}


export async function getFteData(options) {
  const {
      orgId: orgIdList = [],
      startDate = '',
      minChildAge = null,
      maxChildAge = null,
      includeChildDetails = 'false'
    } = options,
    YEARS = 'years';
  const result = [];
  // Using moment.utc with a strict format ensures the input string “MM/DD/YYYY” is parsed exactly as a UTC date.
  // This prevents lenient fallback (e.g., rolling “01-1779-2021” into a valid date) and avoids any timezone‐based shifts
  const inputDate = moment.utc(startDate, "MM/DD/YYYY", true);

  if (!inputDate.isValid()) {
    throw new Error("Start Date is invalid");
  }

  if (inputDate.day() !== 1) {
    throw new Error('Start day should be Monday');
  }

  const startMonth = inputDate.format('MM/DD/YYYY');

  for (const orgId of orgIdList) {
    const currentOrg = await Orgs.findOneAsync({ _id: orgId });

    if (!currentOrg) continue;

    const ageRangeStart = !isNaN(minChildAge) && parseFloat(minChildAge),
      ageRangeEnd = !isNaN(maxChildAge) && parseFloat(maxChildAge),
      timezone = currentOrg.getTimezone();

    const groupsQuery = {
      orgId: currentOrg._id,
      includeClassList: { $ne: false }
    };

    const groupsRaw = await Groups.find(groupsQuery).fetchAsync();

    const groups = sortGroupsByAge(
      _.filter(groupsRaw, ({ ageGroup = {} }) => {
        let { begin = 0, end = 0, type = '' } = ageGroup;
        let groupAgeStart = parseFloat(begin);
        let groupAgeEnd = parseFloat(end);
        if (type === YEARS) {
          groupAgeStart = groupAgeStart * 12;
          groupAgeEnd = groupAgeEnd * 12;
        }
        return (!ageRangeStart || groupAgeStart >= ageRangeStart) && (!ageRangeEnd || groupAgeEnd <= ageRangeEnd);
      })
    );
    const dayMomentValue = new moment.tz(startMonth, 'MM/DD/YYYY', timezone).startOf('day').valueOf(),
      scheduledDateQueryValue = new moment(dayMomentValue).endOf('week').endOf('day').add(4, 'months').valueOf(),
      resQuery = {
        orgId: currentOrg._id,
        scheduledDate: { $lte: scheduledDateQueryValue },
        $or: [{ scheduledEndDate: null }, { scheduledEndDate: { $gte: dayMomentValue } }],
        recurringFrequency: { $exists: true }
      },
      allReservations = await Reservations.find(resQuery).fetchAsync();
    let data = {
      currentFte: 0
    };

    for (const g of groups) {
      if (g.ratio) {
        g.ratio = formatRatio(g.ratio);
      }
      const currentFteResult = await getFteForMonth({
        startMonth,
        orgId: currentOrg._id,
        groupId: g._id,
        allReservations,
        includeInactive: true
      });

      g.currentFte = currentFteResult.totalCount;
      const currentFte = (Math.round((parseFloat(g.currentFte) + parseFloat(data.currentFte)) * 10) / 10).toFixed(1),
        { groupChildren = [] } = currentFteResult;

      const projections = await getFteFutureProjections({
          orgId: currentOrg._id,
          groupId: g._id,
          startMonth,
          allReservations
        });

        // Add projections to group
        Object.assign(g, projections);

      if (JSON.parse(includeChildDetails.toLowerCase())) {
        data = {
          ...data,
          currentFte,
          groupChildren,
          groups: g
        };
      } else {
        data = {
          ...data,
          currentFte,
          groups: g
        };
      }

      result.push(data);
    }
  }
  return result;
};
