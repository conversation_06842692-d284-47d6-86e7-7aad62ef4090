import {AvailableCustomizations} from "../lib/customizations";
import { SSR } from "../lib/util/ssrUtils";
import moment from "moment-timezone";
import { People } from "../lib/collections/people";
import { Orgs } from "../lib/collections/orgs";
import { Relationships } from "../lib/collections/relationships";
import { generateEmailWhiteLabelData } from "./util";
import { sendSMSMessage } from "./snsHelper";
import { Email } from "meteor/email";
export class PinService {
    static async generateAutomaticPin(personId) {
        const person = await People.findOneAsync(personId);
        const pinTypes = ['family', 'admin', 'staff'];
        if (!person || person.pinCode || person.inActive || !pinTypes.includes(person.type)) {
            // bad person id or person already has pin code
            return;
        }
        const needsFamily = person.type === 'family';

        const orgId = person.orgId;
        const org = await Orgs.findOneAsync(orgId);
        if (!org || !org.hasCustomization(AvailableCustomizations.AUTO_PIN)) {
            // bad org or org doesn't have the needed customizations
            return;
        }

        let isFamily = true;
        if (needsFamily) {
            const familyRels = await Relationships.find({ personId, orgId, relationshipType: 'family' }).fetchAsync();
            const familyChild = await People.findOneAsync({orgId, _id: {$in: familyRels.map(rel => rel.targetId)}, inActive: { $ne: true }});
            if (!familyChild) {
                const authPickupRels = await Relationships.find({ personId, orgId, relationshipType: 'authorizedPickup' }).fetchAsync();
                const authPickupChild = await People.findOneAsync({orgId, _id: { $in: authPickupRels.map(rel => rel.targetId)}, inActive: { $ne: true }});
                if (!authPickupChild) {
                    return;
                }
                isFamily = false;
            }
        }

        const doSend = !needsFamily || isFamily;

        const hasZkTeco = org.hasCustomization(AvailableCustomizations.DOOR_LOCKS_ZKTECO);
        let didZkTeco = false;
        if (hasZkTeco) {
            let success = false;
            while (!success) {
                let pinCode = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
                while (await People.find({ pinCode }).countAsync() > 0) {
                    pinCode = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
                }

                await People.direct.updateAsync({ _id: personId }, { $set: { pinCode } });
                try {
                    await Meteor.callAsync('updateZkTecoPerson', personId, true);
                    success = true;
                    didZkTeco = true;
                } catch (e) {
                }
            }
        } else {
            let pinCode = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            while (await People.findOneAsync({orgId, pinCode})) {
                pinCode = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            }
            await People.direct.updateAsync({ _id: personId }, { $set: { pinCode } });
        }
        console.log("do send", doSend, personId)
        if (doSend) {
            const user = await person.findAssociatedUser();
            if (user && !user.pending) {
                console.log("sending now", personId)
                await this.sendPinEmail(personId);
                await this.sendPinText(personId);
            } else {
                await People.direct.updateAsync({ _id: personId }, {$set: { needsPinEmail: true, needsPinText: true }});
            }
        }
        return didZkTeco;
    }

    static async sendPinText(personId) {
        console.log('sending pin text', personId);
        const person = await People.findOneAsync(personId);
        if (!person || !person.pinCode || person.inActive) {
            // bad person id or person already has pin code
            return;
        }
        const org = await Orgs.findOneAsync(person.orgId);
        if (!org || !org.hasCustomization(AvailableCustomizations.AUTO_PIN)) {
            // bad org or org doesn't have the needed customizations
            return;
        }
        
        const user = await person.findAssociatedUser();
        if (!user || user.pending) {
            return;
        }
        const number = person.phonePrimary || person.profileData?.phonePrimary || '';
        if (!number) {
            return;
        }
        const orgName = org?.longName !== undefined && org?.longName !== null ? org?.longName + ' - ' + org?.name : org?.name;
        const orgLongNameOrName = org?.longName !== undefined && org?.longName !== null ? org?.longName : org?.name;
        const pinSmsMessage = `Hello ${person?.firstName}. Your PIN code at ${orgName} has been assigned. Your new PIN code for checking your student in and out of ${orgLongNameOrName} is: ${person?.pinCode}. If you have any questions, please contact your administrator-do not reply to this text. Thank you.`;
        try {
            sendSMSMessage(number, pinSmsMessage, org.originationNumber);
            await People.direct.updateAsync({_id: person._id}, {$set: {pinTextSent: true, needsPinText: false }});
        } catch (e) {
            console.log(e);
        }
    }

    static async sendPinEmail(personId) {
        console.log('sending pin email', personId);
        const person = await People.findOneAsync(personId);
        if (!person || !person.pinCode || person.inActive) {
            // bad person id or person already has pin code
            return;
        }
        const org = await Orgs.findOneAsync(person.orgId);

        if (!org || !org.hasCustomization(AvailableCustomizations.AUTO_PIN)) {
            // bad org or org doesn't have the needed customizations
            return;
        }

        const user = await person.findAssociatedUser();
        const activeUserEmailAddress = await person.getActiveUserEmailAddress();
        if (!user || user.pending || !activeUserEmailAddress) {
            return;
        }
        SSR.compileTemplate('pinResetEmail', await Assets.getTextAsync('email_templates/v2021/assign_pin_email.html'));
        const baseUrl = process.env.ROOT_URL;
        const emailData = {
            person ,
            org,
            baseUrl
        };

        const whiteLabel = generateEmailWhiteLabelData(org);
        emailData.whiteLabel = whiteLabel
        emailData.backgroundColor = `${whiteLabel.primaryColor}1A`;
        emailData.headerOrgNameColor = "#8E8E93";
        emailData.headerBgColor = whiteLabel.primaryColor;
        emailData.secondaryColor = whiteLabel.secondaryColor;
        emailData.assetPrefix = `emailAssets/${whiteLabel.emailAssetPrefix}`;
        emailData.currentYear = new moment().format("YYYY");

        const emailOptions = {
            from: "LineLeader support <<EMAIL>>",
            to: activeUserEmailAddress,
            subject: `Important: Your assigned PIN code`,
            html: SSR.render('pinResetEmail', emailData)
        };
        emailOptions.replyTo = org.replyToAddress || emailOptions.replyTo;
        emailOptions.from = org.fromAddress || emailOptions.from;
        try {
            await Email.sendAsync(emailOptions);
            await People.direct.updateAsync({_id: person._id}, {$set: {pinEmailSent: true, needsPinEmail: false }});
        } catch (e) {
            console.log(e);
        }
    }
}
