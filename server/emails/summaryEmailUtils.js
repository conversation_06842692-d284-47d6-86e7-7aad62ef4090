export class SummaryEmailUtils {
  /**
   * Process timeline moments for email summary
   * @param {Object} emailData - Email data containing timelineMoments
   * @param {Array} timelineIcons - Array of valid timeline icon types
   * @param {Object} currentOrg - Current organization
   * @param {Array} mediaMoments - Array to store media moments
   */
  static async processTimelineMoments(emailData, timelineIcons, currentOrg, mediaMoments) {
    if (emailData && emailData.timelineMoments && emailData.timelineMoments.length > 0) {
      await Promise.all(emailData.timelineMoments.map(async (m) => {
        m.formattedDesc = await m.formattedDescription();
        m.prettyTypeTranslation = await m.momentTypePrettyTranslation(currentOrg?.language);
        m.icon = "https://cfm.momentpath.com/email_images/emailv2_" + m.momentType + "_icon.png";

        if (timelineIcons.includes(m.momentType)) {
          m.timelineIcon = m.momentType;
        } else if (m.momentType === "checkin" || m.momentType === "checkout") {
          m.timelineIcon = "checkin";
        } else if (m.momentType === "alert") {
          m.timelineIcon = "notification";
        } else {
          m.timelineIcon = "generic";
        }

        if (m.attachedMedia().length > 0) {
          mediaMoments.push(m);
        }
      }));
    }
  }
}