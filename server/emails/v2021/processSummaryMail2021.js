import { Log } from '../../../lib/util/log';
import { SSR } from '../../../lib/util/ssrUtils';
import { AvailableCustomizations } from '../../../lib/customizations';
import { Blaze } from "meteor/blaze";
import { SummaryEmailDeferrals } from '../../collections/summaryEmailDeferrals';
import { People } from '../../../lib/collections/people';
import { generateEmailWhiteLabelData } from '../../util';
import { Moments } from '../../../lib/collections/moments';
import _ from '../../../lib/util/underscore';
import moment from 'moment-timezone';
import { SummaryEmailUtils } from '../summaryEmailUtils';

export const processSummaryMail2021 = async function (personId, summaryCC, runNow = false) {
	if (Meteor.isServer) {
		const currentPerson = await People.findOneAsync(personId);
		const currentOrg = await currentPerson.findOrg();
		const whiteLabel = generateEmailWhiteLabelData(currentOrg);
		const orgDefersSummaryEmails = currentOrg.hasCustomization(AvailableCustomizations.ALLOW_MOMENTS_WHEN_CHECKED_OUT);
		const autoCheckoutSummaryEmails = currentOrg.hasCustomization(AvailableCustomizations.ALLOW_MOMENTS_WHEN_AUTO_CHECKED_OUT);
		// Check if we should defer this email for later
		if(!runNow && orgDefersSummaryEmails && !autoCheckoutSummaryEmails) {
			Log.info("Deferring summary email for personId", personId);
			// Defer the email for later
			try {
				await SummaryEmailDeferrals.insertAsync({
					orgId: currentOrg._id,
					personId,
					summaryCC,
					personType: currentPerson.type,
					checkOutTime: new Date().getTime(),
					processed: false
				});
			} catch (err) {
				Log.error("Exception deferring summary email", err.message);
			}
			return;
		}

		const summarySubscribers = await currentPerson.summarySubscribers();
		for(const subscriber of summarySubscribers) {

			SSR.compileTemplate('summaryEmail2021', await Assets.getTextAsync('email_templates/v2021/summary_email.html'));

			Blaze.Template.summaryEmail2021.helpers({
				"equals": function(val1, val2) {
					return (val1 === val2);
				},
				"printWithColon": function(val) {
					if (val && val.trim() !== "") return val + ": ";
				},
				"formatTime": function(val) {
					return (val ? val.toUpperCase() : "");
				},
				"formatUnixTime": function(val) {
					return new moment(val).tz(currentOrg.getTimezone()).format("h:mm a");
				},
				"prependWithDash": function(val) {
					return ((val && val.trim() !== "") ? " - " + val : "");
				},
				"appUrl": function(){
					var baseUrl = (currentOrg && currentOrg.whiteLabel && currentOrg.whiteLabel.ROOT_URL) || process.env.ROOT_URL;
      				if (baseUrl.slice(baseUrl.length-1) !== "/") baseUrl+= "/";
					return baseUrl;
				},
				"hasArrData": function(arr) {
					return arr?.length > 0;
				},
				"indexGTZero": function(i) {
					return i > 0;
				}
			});
			const startDate = moment().tz(currentOrg.getTimezone()).startOf("day").valueOf();

			const dateRange = {
				$gte: startDate
			};

			const sortByTime = function(moments) {
				return _.sortBy(moments,
					function(m) {
						return m.time ? moment(m.time, "HH:mm a").valueOf() : m.sortStamp;
					}
				);
			};

			const personMoments = await Moments.find({taggedPeople: personId, sortStamp: dateRange}).fetchAsync();

			const curriculumData = await currentPerson.findCurriculumForToday();

			const emailData = {
				person: currentPerson,
				classroomName: await currentPerson?.findDefaultGroup?.()?.name,
				recipientId: subscriber._id,
				emailDateStamp: new moment().tz(currentOrg.getTimezone()).format("dddd MMMM D, YYYY"),
				emailDateStampCollapsed: new moment().tz(currentOrg.getTimezone()).format("MMDDYYYY"),
				whiteLabel,
				backgroundColor: `${whiteLabel.primaryColor}1A`,
				headerOrgNameColor: "#8E8E93",
				headerBgColor: whiteLabel.primaryColor,
				secondaryColor: whiteLabel.secondaryColor,
				assetPrefix: `emailAssets/${whiteLabel.emailAssetPrefix}`,
				moments: {
					comments: sortByTime(personMoments.filter((pm)=> pm.momentType === "comment")),
					food: sortByTime(personMoments.filter((pm)=> pm.momentType === "food")),
					potty:  sortByTime(personMoments.filter((pm)=> pm.momentType === "potty")),
					sleep: sortByTime(personMoments.filter((pm)=> pm.momentType === "sleep")),
					checkout:  sortByTime(personMoments.filter((pm)=> pm.momentType === "checkout")),
					checkin: sortByTime(personMoments.filter((pm)=> pm.momentType === "checkin")),
					activity:  sortByTime(personMoments.filter((pm)=> pm.momentType === "activity")),
					medical:  sortByTime(personMoments.filter((pm)=> pm.momentType === "medical")),
					mood: sortByTime(personMoments.filter((pm)=> pm.momentType === "mood")),
					supplies: sortByTime(personMoments.filter((pm)=> pm.momentType === "supplies")),
					learning: sortByTime(personMoments.filter((pm)=> pm.momentType === "learning")),
					incidents: sortByTime(personMoments.filter((pm)=> pm.momentType === "incident")),
					illness: sortByTime(personMoments.filter((pm)=> pm.momentType === "illness")),
					ouches: sortByTime(personMoments.filter((pm)=> pm.momentType === "ouch"))
				},
				announcements: await currentPerson.findAnnouncementsForToday(),
				curriculum: (currentPerson.type !== "staff") ? curriculumData : [],
				currentYear: new moment().format("YYYY"),
			};

			const customMoments = _.chain(await currentOrg.availableDynamicMomentTypes())
				.filter( (cmd) => cmd.includesInSummaryReport)
				.map( cmd => {
					
					return sortByTime(personMoments.filter((pm)=> pm.momentType === cmd.momentType));
				})
				.flatten()
				.value();

			const cMoments = _.flatten([
				emailData.moments.comments,
				emailData.moments.food,
				emailData.moments.potty,
				emailData.moments.sleep,
				emailData.moments.checkout,
				emailData.moments.activity,
				emailData.moments.medical,
				emailData.moments.mood,
				emailData.moments.supplies,
				emailData.moments.checkin,
				emailData.moments.learning,
				emailData.moments.incidents,
				emailData.moments.illness,
				emailData.moments.ouches,
				customMoments
			]);

			// process supply
			emailData.supplyNeeds = _.map(emailData.moments.supplies, function(a) {
				return { headline: a.supplyType, message: a.comment };
			});

			// setup section helpers
			if (emailData.moments.checkout && emailData.moments.checkout.length > 0) {
				const last = _.last(emailData.moments.checkout);
				if (last && (last.mood || (last.comment && last.comment !== ""))) {
					emailData.checkoutMoodMoment = last;
					emailData.showMood = true; emailData.showVitals = true;
					emailData.checkoutMoodFile = last.mood ? last.mood.toLowerCase() : "happy";
				}
			}

			if (emailData.announcements && emailData.announcements.length > 0) {
				emailData.showAnnouncementsAndAlerts = true;
			}

			// handle vitals calculations
			let totalSleepDuration = 0;
			let totalSleepCount = 0;

			_.each(emailData.moments.sleep, function(m) {
				totalSleepDuration += m.durationAmount(); totalSleepCount += 1;
			});

			emailData.totalSleepDurationText = Math.floor(totalSleepDuration / 60) + "h " + (totalSleepDuration % 60) + "m";
			emailData.totalSleepCount = totalSleepCount;

			if (emailData.totalSleepCount > 0) {
				emailData.showSleep = true;
				emailData.showVitals = true;
			}

			let totalMeals = 0;
			let totalMealConsumption = "";
			emailData.vitalsMeals = [];

			_.each(emailData.moments.food, function(m) {
				totalMealConsumption = ( totalMealConsumption !== "" ? ", " : "" ) +
					(m.foodAmountPercent || m.foodTubeAmount || m.foodAmount);
				totalMeals += 1;
				if (m.foodType) emailData.vitalsMeals.push(m.foodType);

			});

			emailData.totalMealConsumptionText = totalMealConsumption;
			emailData.totalMeals = totalMeals;
			if (emailData.totalMeals > 0) {
				emailData.showMeals = true;
				emailData.showVitals = true;
			}

			let totalPotty = 0;
			const pottyBreakdown = {};

			_.each(emailData.moments.potty, function(m) {
				if (m.pottyType) {
					if (!pottyBreakdown[m.pottyType]) {
						pottyBreakdown[m.pottyType] = 1;
					} else {
						pottyBreakdown[m.pottyType] += 1;
					}
				}
				totalPotty +=1;
			});

			emailData.totalPotty = totalPotty;
			emailData.pottyBreakdown = _.map(pottyBreakdown, function(num, key) { return {key: key, num: num};});

			if (emailData.totalPotty > 0 ) {
				emailData.showPotty = true;
				emailData.showVitals = true;
			}

			const earliestCheckin = _.first(emailData.moments.checkin);
			const latestCheckout = _.last(emailData.moments.checkout);

			if (earliestCheckin && latestCheckout) {
				const dayStartTime = new moment(earliestCheckin.time, "h:mm a");
				const dayEndTime = new moment(latestCheckout.time, "h:mm a");
				emailData.checkInTime = earliestCheckin.time;
				emailData.checkOutTime = latestCheckout.time;
			}

			// more accurate checkout time calculations
			let totalTime = 0;
			const checkInTimes = _.chain(emailData.moments.checkin)
				.sortBy(function(i) { return i.sortStamp; })
				.value();

			_.each(checkInTimes, function(checkInTime) {
				const checkOutTime = _.chain(emailData.moments.checkout)
					.filter(function(i) { return i.sortStamp > checkInTime.sortStamp;})
					.sortBy(function(i) { return i.sortStamp; })
					.first()
					.value();

				if (checkInTime && checkOutTime) {
					const blockStartTime = new moment(checkInTime.time, "h:mm a");
					const blockEndTime = new moment(checkOutTime.time, "h:mm a");
					const blockDuration = moment.duration(blockEndTime - blockStartTime).asMinutes();
					totalTime = totalTime + blockDuration;
				}
			});

			if (totalTime > 0) {
				emailData.showVitals = true;
			}

			emailData.dayDurationText = Math.floor(totalTime / 60) + "h " + (totalTime % 60) + "m";

			// now create chronological timeline and filter out media moments
			const mediaMoments = [];
			const timelineIcons = ["potty", "ouch", "medical", "learning", "incident", "illness", "food", "comment", "activity", "notification", "sleep", "supplies" ];

			emailData.timelineMoments = sortByTime(cMoments);

			await SummaryEmailUtils.processTimelineMoments(emailData, timelineIcons, currentOrg, mediaMoments);

			if (mediaMoments.length > 0) {
				emailData.mediaMoments = mediaMoments;
				emailData.showMomentsAndMemories = true;
			}

			if (emailData?.timelineMoments?.length > 0) {
				emailData.timelineMoments[emailData.timelineMoments.length - 1].isLast = true;
			}

			// get moment counts
			let momentCount = 0;

			_.each(emailData.moments, function (v, k) {
				if (!(currentPerson.type === "staff" && k == "checkout")) momentCount += v.length;
			});

			const extraStamp = " @ " + (new moment().tz(currentOrg.getTimezone()).format("h:mm a"));

			Log.info("attempting to process summary email for personId, subscriberId", currentPerson._id, subscriber._id);
			
			const userEmails = await subscriber.getActiveUserEmailAddress();
			if ( userEmails && momentCount > 0) {
				const emailOptions = {
					from: "LineLeader support <<EMAIL>>",
					to: userEmails,
					subject: currentOrg.getLongName() + " Daily Report for " + emailData.emailDateStamp + extraStamp,
					html: SSR.render('summaryEmail2021', emailData)
				};

				if (currentOrg.replyToAddress) {
					emailOptions.replyTo = currentOrg.replyToAddress;
				}

				if (currentOrg.fromAddress) {
					emailOptions.from = currentOrg.fromAddress;
				}

				if (summaryCC && summaryCC !== "") {
					emailOptions.cc = summaryCC;
				}

				try {
					await Email.sendAsync(emailOptions);
					await currentOrg.updateMetric("messagesSent", 1);
				} catch (err) {
					Log.error("Exception processing summary email", err.message);
				}
			} else {
				continue;
			}
		}
	}
};
