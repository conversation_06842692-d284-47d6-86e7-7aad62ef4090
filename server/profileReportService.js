import moment from 'moment-timezone';
import { StringUtils } from '../lib/util/stringUtils';
import { People } from "../lib/collections/people";
import { PermissionsRoles } from "../lib/collections/permissionsRoles";
import { Orgs } from "../lib/collections/orgs";
import { Relationships } from "../lib/collections/relationships";
import _ from "../lib/util/underscore";

export class ProfileReportService {
  static async getProfileReport(options, org, currentPerson) {
    const designation = options.designation && options.designation.length > 0 ? options.designation : false;
    const localType = options.personType === 'familyAndChild' ? 'family' : options.personType;
    const fcMode = options.personType === 'familyAndChild';
    const relationshipTypes = options.includeAuthPickupAndEmergencyOption
      ? ['authorizedPickup', 'emergencyContact', 'family']
      : ['family'];
    let query = { type: localType };
    if (!options.includeInactive) {
      query['inActive'] = { $ne: true };
    }

    if (designation) {
      query.designations = { $in: [designation] };
    }

    if (options.orgIds && options.orgIds.length > 0) {
      const orgsScope = await currentPerson.findScopedOrgs();
      const orgsScopeList = orgsScope && orgsScope.map((o) => o._id);
      const validatedOrgIds = options.orgIds.filter((o) => orgsScopeList.includes(o));
      query['orgId'] = { $in: validatedOrgIds };
    } else {
      query['orgId'] = org._id;
    }

    //If there are Group options - apply those otherwise use the people type for additional steps on filter
    if (options.selectedGroups && options.selectedGroups !== '') {
      if (localType === 'family') {
        const personQuery = { type: 'person', inActive: { $ne: true }, defaultGroupId: options.selectedGroups };
        personQuery.orgId = query['orgId'];
        if (designation) personQuery.designations = { $in: [designation] };
        const peopleInGroupIds = await People.find(personQuery).mapAsync((p) => p._id);
        const relatedPeopleIds = await Relationships.find({
          targetId: { $in: peopleInGroupIds },
          relationshipType: { $in: relationshipTypes }
        }).mapAsync((p) => p.personId);

        query['_id'] = { $in: relatedPeopleIds };
      } else {
        query['$or'] = [{ defaultGroupId: options.selectedGroups }, { checkInGroupId: options.selectedGroups }];
      }
    } else if (localType === 'family') {
      const personQuery = { type: 'person', orgId: query['orgId'] };
      if (designation) personQuery.designations = { $in: [designation] };
      const peopleInQuery = await People.find(personQuery).mapAsync((p) => p._id);
      const relatedPeopleIds = await Relationships.find({
        targetId: { $in: peopleInQuery },
        relationshipType: { $in: relationshipTypes }
      }).mapAsync((p) => p.personId);
      query['_id'] = { $in: relatedPeopleIds };
    }

    const orgs = await Orgs.find({ _id: query['orgId'] }).fetchAsync();
    const results = await People.find(query).fetchAsync();
    const outputResults = [];
    const fieldList = await People.getCollapsedProfileFieldList(localType);
    const childFieldList = await People.getCollapsedProfileFieldList('person');
    const profileFieldPrefix = org.profileDataPrefix() ? org.profileDataPrefix() + '.' : '';
    const defaultRoles = await PermissionsRoles.find({}).fetchAsync();

    for (const result of results) {
      const relationships = await Relationships.find({ personId: result._id }).fetchAsync();
      const outputResult = {
        _id: result._id,
        orgId: result.orgId,
        orgName: (orgs.find((o) => o._id === result.orgId) || {}).name,
        firstName: result.firstName,
        lastName: result.lastName,
        preferredName: result.preferredName,
        middleName: result.middleName,
        inActive: result.inActive,
        authorizedPickup: relationships.filter((r) => r.relationshipType === 'authorizedPickup').length > 0,
        emergencyContact: relationships.filter((r) => r.relationshipType === 'emergencyContact').length > 0
      };

      for (const ft of options.fieldTypes) {
        await this.getFieldResult(
          outputResult,
          result,
          ft,
          orgs,
          fieldList,
          profileFieldPrefix,
          defaultRoles,
          relationships,
          options.includeInactive
        );
      }
      if (fcMode) {
        const childQuery = { _id: { $in: relationships.map((r) => r.targetId) }, type: 'person' };
        if (designation) {
          childQuery.designations = { $in: [designation] };
        }

        if (!options.includeInactive) {
          childQuery['inActive'] = { $ne: true };
        }

        if (options.selectedGroups) {
          childQuery['$or'] = [{ defaultGroupId: options.selectedGroups }, { checkInGroupId: options.selectedGroups }];
        }

        const children = await People.find(childQuery).fetchAsync();
        const childResults = [];
        for (const child of children) {
          const childResult = {
            ...outputResult,
            childFirstName: child.firstName,
            childLastName: child.lastName,
            childPreferredName: child.preferredName,
            childMiddleName: child.middleName,
            childInActive: child.inActive,
            childId: child._id
          };
          const childFields = [];
          for (const cft of options.fieldTypesChild) {
            await this.getFieldResult(
              childFields,
              child,
              cft,
              orgs,
              childFieldList,
              profileFieldPrefix,
              null,
              null,
              null,
              result._id
            );
          }
          for (const childFieldIdx in childFields) {
            childResult['child:' + childFieldIdx] = childFields[childFieldIdx];
          }
          childResults.push(childResult);
        }
        outputResults.push(...childResults);
        continue;
      }
      outputResults.push(outputResult);
    }
    return outputResults;
  }

  static async getFieldResult(
    outputResult,
    result,
    ft,
    orgs,
    fieldList,
    profileFieldPrefix,
    defaultRoles,
    relationships,
    includeInactive,
    familyId
  ) {
    if (ft === 'attachedEmail') {
      outputResult['attachedEmail'] = (await result.getEmailAddress()) || '';
    } else if (ft === 'fcRelationship') {
      const relationships = await Relationships.find({ personId: familyId, targetId: result._id }).fetchAsync();
      const relationshipTypes = [];
      
      for (const r of relationships) {
          const relationshipType = StringUtils.camelToWords(r.relationshipType);
          relationshipTypes.push(relationshipType);
      }
      
      outputResult['fcRelationship'] = relationshipTypes.join(', ');
    } else if (ft === 'familyChildren') {
      const childQuery = { _id: { $in: relationships.map((r) => r.targetId) }, type: 'person' };
      if (!includeInactive) childQuery['inActive'] = { $ne: true };
      const children = await People.find(childQuery).fetchAsync();
      children.sort((a, b) => {
        return a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName);
      });
      outputResult['familyChildren'] = children.map((c) => c.firstName + ' ' + c.lastName).join('; ');
    } else if (ft === 'Roles') {
      const localRoleResult = [];
      const resultRoles = result.roles || [];
      for (const r of resultRoles) {
        const matchedRole = defaultRoles.find((role) => role.name === r);
        if (matchedRole) {
          localRoleResult.push(matchedRole.label);
        }
      }
      outputResult['Roles'] = localRoleResult.join(', ');
    } else if (ft === 'defaultGroupName') {
      const group = await result.findDefaultGroup();
      outputResult['defaultGroupName'] = group && group.name;
    } else if (ft === 'calcAge') {
      outputResult['calcAge'] = await result.calcAge();
      outputResult['calcAgeRaw'] = parseInt(await result.calcAge('days')) || 0;
    } else if (ft === 'waitlistAddedDate') {
      outputResult['waitlistAddedDate'] =
        result.waitlistAddedDate && new moment.utc(result.waitlistAddedDate).format('MM/DD/YYYY');
    } else if (ft === 'pinCode') {
      outputResult['pinCode'] = result.pinCode;
    } else if (ft === 'pinCodeSupplemental') {
      outputResult['pinCodeSupplemental'] = result.pinCodeSupplemental;
    } else {
      const matchedField = fieldList && fieldList.find((f) => f.prefixedName === ft || f.name === ft);
      const key = profileFieldPrefix + ft;
      const val = _.deep(result, key);

      if (val && matchedField && matchedField.type === 'date') {
        const currentOrg = orgs.find((o) => o._id === result.orgId);
        outputResult[ft] = new moment.tz(val, currentOrg.getTimezone()).format('MM/DD/YYYY');
      } else if (val && matchedField && matchedField.type === 'query') {
        const currentOrg = orgs.find((o) => o._id === result.orgId);
        const availableData = currentOrg && (await currentOrg.queryData(matchedField.source, { personId: result._id }));
        console.log('availableData ===>', availableData);
        const selectedItem = availableData && availableData.find((ad) => ad.id === val);
        outputResult[ft] = selectedItem?.value;
      } else {
        outputResult[ft] = val;
      }
    }
  }
}
