import { People } from "../lib/collections/people";

export class PinCodeCheckinService {
    static async eachActiveRelationshipsArray(relationships, onPerson) {
        const targetIds = relationships.map(rel => rel.targetId);
        const relTargets = await People.find({
            _id: { $in: targetIds },
            inActive: { "$ne": true },
            designations: { '$ne': 'Wait List' }
        }).fetchAsync();

        relTargets.map(relTarget => onPerson(relTarget));
    }

    static async eachAuthorizedCompanionsArray(relationships, onPerson) {
        const personIds = relationships.map(r => r.personId);
        const people = await People.find({
            _id: { $in: personIds },
            inActive: { "$ne": true }
        }).fetchAsync();

        people.map(person => onPerson(person));
    }
}
