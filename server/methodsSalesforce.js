import { Orgs } from '../lib/collections/orgs';
import { SalesforceService } from './salesforceService';

Meteor.methods({
	async "salesforceBeginAuthorize"(options) {
		var currentUser = await Meteor.userAsync(), org = await Orgs.current();
		
		/*processPermissions({
			assertions: [{ context: "integrations/airslate", action: "read" }],
			evaluator: (person) => person.type=="admin",
			throwError: true
		});
		*/
		
		const authUrl = await SalesforceService.getAuthorizationUrl({orgId: org._id});

		console.log("authUrl", authUrl);

		return authUrl;
	},
	async "salesforceValidate"(options) {
		const decoded = decodeURIComponent(options.authCode);
		return await SalesforceService.validateAuthorization({authCode: decoded, orgId: (await Orgs.current())._id});
	},
	async "salesforcePullData"(options) {
		await SalesforceService.fullSync({orgId: (await Orgs.current())._id, execute: options.execute});
		
		return;
	}
});