import { _ } from 'underscore';

/**
 * Handles utilities for orgs on the server side.
 */
export class OrgServerUtils {
    /**
     * Get the orgs a person has visibility to within the able-to-work-at logic.
     *
     * @param org
     * @param person
     * @returns {Promise<{}[]>}
     */
    static async getOrgsFromPersonOrg(org, person) {
        const topOrgId = (await org.findTopOrg())?._id ?? org._id;
        return _.sortBy(await person.findScopedOrgs({ topmostOrgId: topOrgId }), "name");
    }
}
