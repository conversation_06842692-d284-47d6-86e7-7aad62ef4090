import {EmailService} from "../emails/emailService";
import { People } from "../../lib/collections/people";
import { Orgs } from "../../lib/collections/orgs";
import { mappings } from './propagateSettings';
const moment = require('moment-timezone');

const _ = require('lodash');
export class OrgCreationService {

    /**
     * Sends an email to accounting when a new organization is created.
     * @param {string} userId
     * @param {Object} doc
     */
    static async sendAccountingEmail(options){
        const { userId, newOrgId } = options
        const currentUser = await Meteor.users.findOneAsync(userId);
        const currentPerson = await People.findOneAsync(currentUser.personId);
        const newOrg = await Orgs.findOneAsync(newOrgId);
        const topOrg = await newOrg.findTopOrg();

        // Send email to accounting
        const topOrgLongName = topOrg.longName || topOrg.name;
        const newOrgFullName = newOrg.longName || newOrg.name;

        const emailSubject = "A new location has been added in Manage";
        const emailAdd = await currentPerson.getEmailAddress();
        const emailBody = `Hello!

A new location has been added in Manage--update billing if appropriate:

User: ${currentPerson.firstName} ${currentPerson.lastName}
User's Email: ${emailAdd}
Customer: ${topOrgLongName} (${topOrg._id})
New Location: ${newOrgFullName} (${newOrg._id})`;

        await EmailService.sendSystemAdminEmail(
            undefined,
            'systemNotificationEmail',
            'email_templates/system_notification_email.html',
            "Manage <<EMAIL>>",
            Meteor.settings.accountingEmail,
            emailSubject,
            { bodyMessage: emailBody },
        );
    }

    /**
     * Copies an organization with modifications for a new instance.
     *
     * @param {string} orgId - The ID of the organization to be copied.
     * @returns {Promise<Object>} - The copied organization with modifications.
     * @throws {Meteor.Error} - If the organization with the given ID is not found.
     */
    static async copyOrg(orgId) {
        const org = await Orgs.findOneAsync(orgId);
        if (!org) {
            throw new Meteor.Error(404, "Org not found");
        }

        const newOrg = {
            parentOrgId: org._id,
            name: org.name
        };

        if (org.billing) {
            newOrg.billing = {
                enabled: org.billing.enabled || false,
                plansAndItems: _.cloneDeep(org.billing.plansAndItems || []),
                programs: _.cloneDeep(org.billing.programs || []),
                dunningComs: _.cloneDeep(org.billing.dunningComs || {})
            };
        }

        const fieldsToKeep = [
            'customizations',
            'enabledMomentTypes',
            'language',
            'valueOverrides',
            'autoCheckoutTime',
            'autoCheckoutSettings',
            'familyRegistrationSettings',
            'busRoutes',
            'brands',
            'selectedBrand',
            'mediaRequirement',
            'checkInCheckOutQrCodesExpireAfterMinutes',
            'chatSupportSettings',
            'cancellationReasons',
            'customStaffPaySettings',
            'curriculumStandard',
            'immunizationDefinitions',
            'forecasting',
            'fromAddress'
        ];

        fieldsToKeep.forEach(field => {
            if (org[field] !== undefined) {
                newOrg[field] = _.cloneDeep(org[field]);
            }
        });

        return newOrg;
    }

    /**
     * Creates a new organization.
     *
     * @param {Object} org - The organization to be created.
     * @returns {Promise<string>} - The ID of the newly created organization.
     */
    static async createOrg(org) {
        const timezone = org.timezone || 'America/New_York';
        org.createdAt = new moment.tz(timezone).valueOf();
        return await Orgs.insertAsync(org);
    }

    static async getOrg(id) {
        if (!id) {
            throw new Meteor.Error(400, "Invalid org ID");
        }
        const org = await Orgs.findOneAsync({_id: id});

        if (!org) {
            throw new Meteor.Error(404, "Org not found");
        }

        return org;
    }

    static async orgNameExists(orgName, currentOrgId) {
        const org = await Orgs.findOneAsync({ name: orgName });
        if (!org) {
            return false;
        } else {
            if (currentOrgId && org._id === currentOrgId) {
                return false;
            }
        }
        return true;
    }

    static superAdminCopyOrg(org) {
        // Helper function to safely clone and filter billing data
        const getSafeBillingData = (billing) => {
            if (!billing) return undefined;

            // Create a deep copy to avoid mutation
            const safeBilling = structuredClone(billing);

            // Remove sensitive org-specific billing data
            delete safeBilling.adyenInfo;
            delete safeBilling.lastBillingComplete;
            delete safeBilling.stats;
            delete safeBilling.adyenBalancePlatformOnboarding;
            delete safeBilling.adyenOnboarding;
            delete safeBilling.cashnetEnabled;
            delete safeBilling.stripeInfo;
            delete safeBilling.stripeAccountId;

            return safeBilling;
        };

        const newOrgSettings = {
            // Core settings - using structuredClone to avoid mutation
            customizations: org.customizations ? structuredClone(org.customizations) : undefined,
            enabledMomentTypes: org.enabledMomentTypes ? structuredClone(org.enabledMomentTypes) : undefined,
            language: org.language,
            valueOverrides: org.valueOverrides ? structuredClone(org.valueOverrides) : undefined,

            // Billing related - filtered to remove sensitive data
            billing: getSafeBillingData(org.billing),
            autoCheckoutTime: org.autoCheckoutTime,

            // Feature settings
            familyRegistrationSettings: org.familyRegistrationSettings ? structuredClone(org.familyRegistrationSettings) : undefined,
            busRoutes: org.busRoutes ? structuredClone(org.busRoutes) : undefined,
            brands: org.brands ? structuredClone(org.brands) : undefined,
            selectedBrand: org.selectedBrand,
            mediaRequirement: org.mediaRequirement ? structuredClone(org.mediaRequirement) : undefined,
            checkInCheckOutQrCodesExpireAfterMinutes: org.checkInCheckOutQrCodesExpireAfterMinutes,

            // Support and communication
            chatSupportSettings: org.chatSupportSettings ? structuredClone(org.chatSupportSettings) : undefined,
            cancellationReasons: org.cancellationReasons ? structuredClone(org.cancellationReasons) : undefined,
            customStaffPaySettings: org.customStaffPaySettings ? structuredClone(org.customStaffPaySettings) : undefined,
            couponCodes: org.couponCodes ? structuredClone(org.couponCodes) : undefined,
            curriculumStandard: org.curriculumStandard,
            immunizationDefinitions: org.immunizationDefinitions ? structuredClone(org.immunizationDefinitions) : undefined,
            forecasting: org.forecasting ? structuredClone(org.forecasting) : undefined,
            fromAddress: org.fromAddress,
        };

        // Special handling for curriculum bank
        if (org.curriculumBankId) {
            newOrgSettings.curriculumBankId = org.curriculumBankId;
        }

        // Special handling for white label
        if (org.whiteLabel) {
            newOrgSettings.whiteLabel = structuredClone(org.whiteLabel);
        }

        // Remove any undefined values to avoid overwriting with null
        Object.keys(newOrgSettings).forEach(key => {
            if (newOrgSettings[key] === undefined) {
                delete newOrgSettings[key];
            }
        });

        return newOrgSettings;
    }
}
