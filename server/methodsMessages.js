import { Messages } from '../lib/collections/messages';
import { Orgs } from '../lib/collections/orgs';
import { MessagesUtils } from '../lib/util/messagesUtils';
import logger from '../imports/winston'
import _ from '../lib/util/underscore';

/**
 * Checks if the current user has any unread messages
 * @returns {Promise<boolean>} True if user has unread messages
 */
export const checkForUnreadMessages = async () => {
    try {
        const user = await Meteor.userAsync();
        const org = await Orgs.current();
        const personId = user.personId;
        if (!org) {
            logger.error('No Org found while checking for unread messages', {personId});
            return false;
        }
        const query = {
            orgId: org._id,
            currentRecipients: {$in: [personId]},
            markedAsRead: {$ne: personId},
        }
        const unreads = await Messages.find(query).countAsync()
        return unreads > 0;
    } catch (e) {
        logger.error('Error checking for unread messages', {reason: e?.reason});
        throw new Meteor.Error('Error checking for Unread Messages', e);
    }
}
/**
 * Whether the current user can view a message.
 *
 * @param options
 * @returns {*}
 */
export const canViewMessage = async (options) => {
    const org = await Orgs.current();
    const user = await Meteor.userAsync();
    const currentPerson = await user.fetchPerson();
    return await MessagesUtils.canPersonViewMessage(currentPerson, options.messageId, org);

}
/**
 * Get the messages for the current user.
 * Based on the current view in the UI.
 *
 * @param options
 * @returns {*|*[]}
 */
export const getMessages = async (options) => {
    if (!options.query) {
        return [];
    }
    const queryOptions = {
        sort: { lastActivity: -1 }
    };
    if (options.queryOptions?.offset) {
        queryOptions.skip = options.queryOptions.offset;
    }
    if (options.queryOptions?.limit) {
        queryOptions.limit = options.queryOptions.limit;
    }

    const user = await Meteor.userAsync();
    const personId = user.personId;
    const searchTerm = options.searchTerm;
    let messages = [];
    if (searchTerm.length > 0) {
        messages = await (await Messages.aggregate(MessagesUtils.getMessageQueryWithSearchTerm(options.query, searchTerm, personId, queryOptions))).toArray();
        if (messages.length) {
            // Repopulate the full message objects with the collection methods attached
            messages = await Messages.find({ _id: { $in: messages.map(m => m._id) } }, { sort: { lastActivity: -1 } }).fetchAsync();
        }
    } else {
        messages = await Messages.find(options.query, queryOptions).fetchAsync();
    }
    for (const thread of messages) {
        const lastMessage = _.last(await thread.threadItems());
        if (lastMessage) {
            thread.threadSummary = lastMessage.message;
        }
        thread.threadInitials = await thread.threadRecipients(true, true);
        thread.fullRecipientDescription = await thread.fullRecipientDescription();
        thread.threadTitle = thread.threadTitle();
        thread.threadItems = await thread.threadItems();
        thread.threadRecipients = await thread.threadRecipients(true, false);
        thread.threadUnread = await thread.threadUnread();
        thread.mostRecentStamp = thread.mostRecentStamp();
    }
    return messages;
}
/**
 * Get a message.
 *
 * @param messageId
 * @returns {*}
 */
export const getSingleMessage = async (messageId) => {
    if (!messageId) {
        return null;
    }
    const message = await Messages.findOneAsync({ _id: messageId });
    if (!message) {
        return null;
    }
    message.threadTitle = message.threadTitle();
    message.threadRecipients = await message.threadRecipients(true, false);
    message.fullRecipientDescription = await message.fullRecipientDescription();
    message.threadItems = await message.threadItems();
    message.threadUnread = await message.threadUnread();
    message.mostRecentStamp = message.mostRecentStamp();

    return message;
}
Meteor.methods({
    checkForUnreadMessages,
    canViewMessage,
    getMessages,
    getSingleMessage
});
