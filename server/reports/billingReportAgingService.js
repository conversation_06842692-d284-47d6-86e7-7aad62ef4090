import { Meteor } from 'meteor/meteor';
import moment from 'moment-timezone';
import { ReportAggregation } from './reportAggregation';
import { BillingUtils } from '../../lib/util/billingUtils';
import { Log } from '../../lib/util/log';
import { MiscUtils } from '../../lib/util/miscUtils';
import { People } from '../../lib/collections/people';
import { Orgs } from '../../lib/collections/orgs';
import { Invoice, Invoices } from '../../lib/collections/invoices';
import _ from '../../lib/util/underscore';
import { Relationships } from '../../lib/collections/relationships';

export const AvailableBalanceTypes = {
    PAYER: 'payer',
    FAMILY: 'family',
    BOTH: 'both'
}

export const FilterBucketTypes = {
    FAMILY: 'family',
    OTHER: 'other'
}

const orgCache = {};
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

export class BillingReportAgingService {

    static getCache(key) {
        const cacheEntry = orgCache[key];
        if (cacheEntry && (Date.now() - cacheEntry.timestamp) < CACHE_TTL) {
            return cacheEntry.value;
        }
        delete orgCache[key]; // Remove stale entry
        return null;
    }

    static setCache(key, value) {
        orgCache[key] = { value, timestamp: Date.now() };
    }

    static clearCache() {
        for (const key in orgCache) {
            delete orgCache[key];
        }
    }


    /**
     * Builds organization and time data based on the provided options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {string[]} options.orgIds - An array of organization IDs.
     * @param {Object} options.currentPerson - The current person object.
     * @param {Object} options.org - The organization object.
     * @param {string} options.filterDate - The filter date in "MM/DD/YYYY" format.
     * @returns {Object} An object containing organization and time-related data.
     * @throws {Meteor.Error} Throws an error if 'org' or 'filterDate' is missing in the options.
     */
    static async buildOrgAndTimeData(options) {
        const { orgIds, currentPerson, org, filterDate } = options;

        if (!org) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildOrgAndTimeData: ", "org is required");
        }

        if (!filterDate) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildOrgAndTimeData: ", "filterDate is required");
        }

        const timezone = org.getTimezone();
        let orgQuery = {};

        if (orgIds && orgIds.length > 0) {
            if (!currentPerson) {
                throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildOrgAndTimeData: ", "currentPerson is required");
            }

            const orgsScope = await currentPerson.findScopedOrgs();
            const orgsScopeList = orgsScope && _.pluck(orgsScope, "_id");
            const orgIdsInScope = _.intersection(orgsScopeList, orgIds);
            orgQuery["orgId"] = { "$in": orgIdsInScope };
        } else {
            orgQuery["orgId"] = org._id;
        }

        // Generate cache keys
        const scopedOrgsCacheKey = `scopedOrgs:${JSON.stringify(orgQuery["orgId"])}`;
        const allOrgsCacheKey = "allOrgs";

        // Retrieve from cache or fetch from DB
        let scopedOrgs = this.getCache(scopedOrgsCacheKey);
        if (!scopedOrgs) {
            scopedOrgs = await Orgs.find({ _id: orgQuery["orgId"] }).fetchAsync();
            this.setCache(scopedOrgsCacheKey, scopedOrgs);
        }

        let allOrgs = this.getCache(allOrgsCacheKey);
        if (!allOrgs) {
            allOrgs = await Orgs.find(
                { inactive: { $ne: true } },
                { fields: { _id: 1, name: 1, parentOrgId: 1 } }
            ).fetchAsync();
            this.setCache(allOrgsCacheKey, allOrgs);
        }

        // Build timestamps and metadata
        const reportDateStamp = new moment.tz(filterDate, "MM/DD/YYYY", timezone).endOf('day').valueOf();
        const todayStamp = new moment.tz(timezone).endOf('day').valueOf();
        const orgsMap = ReportAggregation.orgHierarchyMap(allOrgs, scopedOrgs);
        const orgsMeta = ReportAggregation.orgsMeta(allOrgs, scopedOrgs);

        return {
            scopedOrgs,
            reportDateStamp,
            todayStamp,
            orgsMap,
            orgsMeta,
            orgQuery
        };
    }

    /**
     * Builds a query object based on the provided options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {number} options.reportDateStamp - The report date timestamp.
     * @param {number} options.todayStamp - The current date timestamp.
     * @param {string} options.filterBalanceType - The balance type filter ("payer" or "both").
     * @param {Object} options.orgQuery - The organization query object.
     * @returns {Object} An object containing query parameters.
     * @throws {Meteor.Error} Throws an error if any required parameter is missing in the options.
     */
    static buildQuery(options) {
        const { reportDateStamp, todayStamp,  filterBalanceType, orgQuery} = options;

        if (!reportDateStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildQuery: ", "reportDateStamp is required");
        }

        if (!todayStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildQuery: ", "todayStamp is required");
        }

        if (!filterBalanceType) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildQuery: ", "filterBalanceType is required");
        }

        if (!orgQuery) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildQuery: ", "orgQuery is required");
        }

        let payerQueryMatchPart = null;

        if (reportDateStamp < todayStamp) {
            orgQuery["$and"] = [
                { "$or": [{ "voidedAt": {"$exists": false}}, {"voidedAt": {"$gt": reportDateStamp}}] },
                { "createdAt": { "$lt": reportDateStamp } },
                {
                    "$or": [
                        { "openAmount": { "$gte": 0.01 } },
                        {
                            "credits": {
                                "$elemMatch": {
                                    "createdAt": { "$gt": reportDateStamp },
                                    "$or": [
                                        { "voidedAt": { "$exists": false } },
                                        { "voidedAt": { "$gt": reportDateStamp } }
                                    ]
                                }
                            }
                        }
                    ]
                }
            ];
        } else {
            orgQuery["openAmount"] = { "$gte": 0.01 };
        }

        if (filterBalanceType === AvailableBalanceTypes.PAYER || filterBalanceType === AvailableBalanceTypes.BOTH) {
            payerQueryMatchPart = {"orgId": orgQuery["orgId"], "voidedAt": {"$exists": false}};
            if (todayStamp === reportDateStamp) {
                payerQueryMatchPart["openPayerAmounts"] = {"$ne": {}, "$exists": true};
            } else {
                payerQueryMatchPart["createdAt"] = {"$lte": reportDateStamp};
            }
        }

        return {
            query: orgQuery,
            payerQueryMatchPart
        }
    }

    /**
     * Aggregates aging report data based on the provided options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {Object} options.query - The query object for filtering invoices.
     * @param {Object} options.payerQueryMatchPart - The payer query match part for payer-specific filtering.
     * @param {number} options.reportDateStamp - The report date timestamp.
     * @param {string} options.filterBalanceType - The balance type filter ("payer", "family", or "both").
     * @returns {Array} An array of invoices representing the aging report.
     * @throws {Meteor.Error} Throws an error if any required parameter is missing in the options.
     */
    static async aggregateAgingReport(options) {
        const { query, payerQueryMatchPart, reportDateStamp, filterBalanceType} = options;

        if (!reportDateStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to aggregateAgingReport: ", "reportDateStamp is required");
        }

        let invoices = [];

        if (filterBalanceType === AvailableBalanceTypes.PAYER || filterBalanceType === AvailableBalanceTypes.BOTH) {
            try {
                const payerInvoices = await this.getPayerInvoices(payerQueryMatchPart, reportDateStamp);

                if (payerInvoices && payerInvoices.length) {
                    invoices = invoices.concat(payerInvoices);
                }
            } catch (e) {
                throw new Meteor.Error(e.error || "500", e.reason || e.message, e.details || "");
            }
        }

        if (!filterBalanceType || filterBalanceType === AvailableBalanceTypes.FAMILY || filterBalanceType === AvailableBalanceTypes.BOTH) {
            try {
                const familyInvoices = await this.getFamilyInvoices(query);

                if (familyInvoices && familyInvoices.length) {
                    invoices = invoices.concat(familyInvoices);
                }
            } catch (e) {
                throw new Meteor.Error(e.error || "500", e.reason || e.message, e.details || "");
            }
        }

        return invoices;
    }

    /**
     * Retrieves payer invoices by batching organization IDs and applying a query pipeline.
     *
     * @param {Object} payerQueryMatchPart - The query match part object for payer filtering.
     * @param {Object} payerQueryMatchPart.orgId - The organization ID(s) to filter invoices.
     * @param {number} reportDateStamp - The timestamp of the report date.
     * @returns {Promise<Array>} A promise resolving to an array of processed payer invoices.
     * @throws {Meteor.Error} Throws an error if `payerQueryMatchPart` is not provided.
     */
    static async getPayerInvoices(payerQueryMatchPart, reportDateStamp) {
        if (!payerQueryMatchPart) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to getPayerInvoices: ", "payerQueryMatchPart is required");
        }

        const orgIds = Array.isArray(payerQueryMatchPart.orgId?.$in)
            ? payerQueryMatchPart.orgId.$in
            : [payerQueryMatchPart.orgId];

        const batchSize = 10;
        const batchedInvoices = [];

        for (let i = 0; i < orgIds.length; i += batchSize) {
            const batchOrgIds = orgIds.slice(i, i + batchSize);
            const batchMatchPart = { ...payerQueryMatchPart, orgId: { $in: batchOrgIds } };

            const payerPipeline = this.getPayerPipeline(batchMatchPart, reportDateStamp);
            const batchInvoices = await (await Invoices.aggregate(payerPipeline)).toArray();

            batchedInvoices.push(...batchInvoices?.map(payerInvoice => this.processPayerInvoice(payerInvoice)));
        }

        return batchedInvoices;
    }

    /**
     * Retrieves family invoices by batching organization IDs and applying a query.
     *
     * @param {Object} query - The query object for retrieving family invoices.
     * @param {Object} query.orgId - The organization ID(s) to filter invoices.
     * @returns {Promise<Array>} A promise resolving to an array of sorted family invoices.
     * @throws {Meteor.Error} Throws an error if `query` is not provided.
     */
    static async getFamilyInvoices(query) {
        if (!query) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to getFamilyInvoices: ", "query is required");
        }

        const orgIds = Array.isArray(query.orgId?.$in)
            ? query.orgId.$in
            : [query.orgId];

        const invoiceFields = {
            createdAt: 1,
            orgId: 1,
            lineItems: 1,
            invoiceNumber: 1,
            credits: 1,
            allocationEntries: 1,
            dueDate: 1,
            personId: 1,
            openAmount: 1,
            originalAmount: 1,
            voidedAt: 1
        };

        const batchSize = 10;
        const batchedInvoices = [];

        for (let i = 0; i < orgIds.length; i += batchSize) {
            const batchOrgIds = orgIds.slice(i, i + batchSize);
            const batchQuery = { ...query, orgId: { $in: batchOrgIds } };

            const batchInvoices = await Invoices.find(batchQuery, { fields: invoiceFields, sort: { createdAt: -1 } }).fetchAsync();
            batchedInvoices.push(...batchInvoices);
        }

        return this.sortBatchedInvoicesByCreatedAt(batchedInvoices);
    }

    /**
     * Sorts a list of invoices by their `createdAt` property in descending order.
     *
     * @param {Array<Object>} invoices - An array of invoice objects to be sorted.
     * @param {number} invoices[].createdAt - The timestamp when the invoice was created.
     * @returns {Array<Object>} A sorted array of invoices in descending order by `createdAt`.
     */
    static sortBatchedInvoicesByCreatedAt(invoices) {
        return invoices.sort((a, b) => b.createdAt - a.createdAt);
    }

    /**
     * Processes a payer invoice by calculating and updating its original amount based on open payer amounts and allocation entries.
     *
     * @param {Object} invoice - The invoice object to be processed.
     * @param {Object} invoice.allocationEntries - An array of allocation entries for the invoice.
     * @param {string} invoice.allocationEntries[].source - The source of the allocation entry.
     * @param {string} invoice.allocationEntries[].destination - The destination of the allocation entry.
     * @param {number} invoice.allocationEntries[].amount - The amount of the allocation entry.
     * @param {Object} invoice.openPayerAmounts - An object containing open amounts for different payers.
     * @param {string} invoice.openPayerAmounts[payerName] - The open amount associated with a specific payer.
     * @returns {Object} A processed invoice with an updated `originalAmount` property.
     */
    static processPayerInvoice(invoice) {
        const newInvoice = new Invoice(invoice);
        const openPayerAmounts = newInvoice.openPayerAmounts?.[newInvoice.payerName] ?? 0;
        const allocationEntries = invoice.allocationEntries || [];
        const discountAmount = allocationEntries
            .filter(allocation => allocation.source === newInvoice.payerName && allocation.destination === "bad-debt")
            .reduce((memo, allocation) => memo + (allocation.amount || 0), 0);
        newInvoice.originalAmount = MiscUtils.roundToTwo(openPayerAmounts + discountAmount);
        return newInvoice;
    }

    /**
     * Generates an aggregation pipeline to calculate payer amounts, credits, and allocations for invoices.
     *
     * @param {Object} payerQueryMatchPart - The query match part for filtering invoices by payer criteria.
     * @param {number} reportDateStamp - The timestamp for the report date to filter data.
     * @returns {Array} The MongoDB aggregation pipeline for processing payer-related data.
     */
    static getPayerPipeline(payerQueryMatchPart, reportDateStamp) {
        return [
            { $match: payerQueryMatchPart },
            {
                $project: {
                    lineItems: 1,
                    credits: 1,
                    allocationEntries: 1,
                }
            },
            {
                $facet: {
                    "payerAmounts": [
                        { $unwind: "$lineItems" },
                        { $unwind: "$lineItems.appliedDiscounts" },
                        { $match: { "lineItems.appliedDiscounts.type": "reimbursable", "lineItems.appliedDiscounts.voidedAt": { $exists: false } } },
                        {
                            $group: {
                                _id: { invoiceId: "$_id", payer: "$lineItems.appliedDiscounts.source" },
                                total: { $sum: "$lineItems.appliedDiscounts.amount" }
                            }
                        },
                        {
                            $group: {
                                _id: "$_id.invoiceId",
                                totals: { $push: { payer: "$_id.payer", amount: "$total" } }
                            }
                        }
                    ],
                    "payerCredits": [
                        { $unwind: "$credits" },
                        {
                            $match: {
                                "credits.creditPayerSource": { $exists: true },
                                "credits.createdAt": { $lt: reportDateStamp },
                                $or: [
                                    { "credits.voidedAt": { $exists: false } },
                                    { "credits.voidedAt": { $gt: reportDateStamp } }
                                ]
                            }
                        },
                        {
                            $group: {
                                _id: { invoiceId: "$_id", payer: "$credits.creditPayerSource" },
                                total: { $sum: { $multiply: [{ $ifNull: ["$credits.voidedAmount", "$credits.amount"] }, -1] } }
                            }
                        },
                        {
                            $group: {
                                _id: "$_id.invoiceId",
                                totals: { $push: { payer: "$_id.payer", amount: "$total" } }
                            }
                        }
                    ],
                    "payerAllocations": [
                        { $unwind: "$allocationEntries" },
                        { $match: { "allocationEntries.createdAt": { $lt: reportDateStamp } } },
                        {
                            $group: {
                                _id: { invoiceId: "$_id", payer: "$allocationEntries.source" },
                                total: { $sum: { $multiply: ["$allocationEntries.amount", -1] } }
                            }
                        },
                        {
                            $group: {
                                _id: "$_id.invoiceId",
                                totals: { $push: { payer: "$_id.payer", amount: "$total" } }
                            }
                        }
                    ]
                }
            },
            { $project: { invoice: { $concatArrays: ["$payerAmounts", "$payerCredits", "$payerAllocations"] } } },
            { $unwind: "$invoice" },
            { $unwind: "$invoice.totals" },
            {
                $group: {
                    _id: { invoiceId: "$invoice._id", payer: "$invoice.totals.payer" },
                    amount: { $sum: "$invoice.totals.amount" },
                    original: { $first: "$invoice" }
                }
            },
            { $match: { "amount": { $gte: 0.01 } } },
            {
                $lookup: {
                    from: "invoices",
                    localField: "_id.invoiceId",
                    foreignField: "_id",
                    as: "originalInvoice"
                }
            },
            { $unwind: "$originalInvoice" },
            {
                $replaceRoot: {
                    newRoot: {
                        $mergeObjects: [
                            "$originalInvoice",
                            { payerName: "$_id.payer", openAmount: "$amount" }
                        ]
                    }
                }
            }
        ];
    }

    /**
     * Filters invoices based on the provided options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {string} options.searchQuery - The search query for filtering invoices by person name.
     * @param {boolean} options.includeWaitList - Whether to include invoices with wait list designation.
     * @param {Array} options.invoices - An array of invoices to be filtered.
     * @returns {Array} An array of filtered invoices based on the provided options.
     * @throws {Meteor.Error} Throws an error if 'invoices' is missing in the options.
     */
    static async filterInvoicesByOptions(options) {
        const {searchQuery, includeWaitList, invoices} = options;

        if (!invoices) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to filterInvoicesByOptions: ", "invoices is required");
        }

        let filteredInvoices = invoices;

        if (searchQuery && searchQuery !== "") {
            const matchingInvoices = [];
            const regex = new RegExp(searchQuery, "i");
            
            for (const invoice of filteredInvoices) {
              if ((await invoice.personName() || "").match(regex)) {
                matchingInvoices.push(invoice);
              }
            }
            
            filteredInvoices = matchingInvoices;
        }

        const newFilteredInvoicesWithoutIncludeWaitList = [];
        for (const invoice of filteredInvoices) {
            const { personId } = invoice;
            if (!await invoice.personHasWaitListDesignation(personId)) {
                newFilteredInvoicesWithoutIncludeWaitList.push(invoice);
            }
        }

        filteredInvoices = includeWaitList ? filteredInvoices : newFilteredInvoicesWithoutIncludeWaitList;

        return filteredInvoices;
    }

    /**
     * Filters invoices based on the balance point in time and an optional balance filter.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {Array} options.invoices - An array of invoices to be filtered.
     * @param {string} options.filterBalance - An optional filter for the invoice balance.
     * @returns {Array} An array of filtered invoices based on the balance point in time and filterBalance criteria.
     * @throws {Meteor.Error} Throws an error if 'invoices' is missing in the options.
     */
    static filterInvoicesByBalancePointInTime(options) {
        const {invoices, filterBalance} = options;

        if (!invoices) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to filterInvoicesByBalancePointInTime: ", "invoices is required");
        }

        return invoices.filter(invoice => {
            return BillingUtils.roundToTwo(Math.abs(invoice.balancePointInTime)) >= 0.01 &&
                (!filterBalance || filterBalance === "" || filterBalance === invoice.amountBucket);
        });
    }

    /**
     * Generates age buckets for invoices based on due dates and other options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {Array} options.invoices - An array of invoices to generate age buckets for.
     * @param {string} options.filterBuckets - The filter for age buckets ('family' or 'individual').
     * @param {moment.Moment} options.todayDate - The current date as a Moment.js object.
     * @param {number} options.todayStamp - The current date timestamp.
     * @param {boolean} options.usePeriodDate - Whether to use period dates for age calculation.
     * @param {number} options.reportDateStamp - The report date timestamp.
     * @param {string} options.filterBalance - An optional filter for the invoice balance.
     * @returns {Array} An array of invoices with age buckets assigned.
     * @throws {Meteor.Error} Throws an error if any required parameter is missing in the options.
     */
    static generateAgeBucketsByInvoiceDueDate(options) {
        const {invoices, filterBuckets, todayDate, todayStamp, usePeriodDate, reportDateStamp, filterBalance} = options;

        if (!invoices) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "invoices is required");
        }

        if (!filterBuckets) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "filterBuckets is required");
        }

        if (!todayDate) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "todayDate is required");
        }

        if (!todayStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "todayStamp is required");
        }

        if (!reportDateStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByInvoiceDueDate: ", "reportDateStamp is required");
        }

        invoices.forEach(invoice => {
            if (usePeriodDate && invoice.lineItems?.length) {
                const invoicePeriodStartDates = invoice.lineItems.filter(li => li.periodStartDate).map(li => li.periodStartDate);

                if(invoicePeriodStartDates.length > 0) {
                    invoice.minPeriodDate = Math.min(parseInt(invoicePeriodStartDates)) ?? false;
                } else {
                    invoice.minPeriodDate = false;
                }
            }

            const duration = moment.duration(todayDate.diff(invoice.minPeriodDate || invoice.dueDate)).asDays();
            invoice.amountBucket = this.getAmountBucketByDurationAndFilter(duration, filterBuckets);

            if (!invoice.minPeriodDate && (invoice.payerName || reportDateStamp === todayStamp)) {
                invoice.balancePointInTime = invoice.openAmount;
            } else {
                // Next line removed in BUGS-2666
                // const lineItemAmount = _.reduce(invoice.lineItems, (memo, lineItem) => memo + lineItem.amount - (lineItem.type === "plan" ? _.reduce(lineItem.appliedDiscounts, (disMemo, appliedDiscount) => disMemo + appliedDiscount.amount, 0) : 0), 0);
                const totalInvoicedAmount = invoice.originalAmountWithPayers();
                const lineItemDiscounts = _.reduce(invoice.lineItems, (memo, lineItem) => memo + _.reduce(lineItem.appliedDiscounts, (disMemo, appliedDiscount) => disMemo + appliedDiscount.amount, 0), 0);
                const lineItemAmount = MiscUtils.roundToTwo(totalInvoicedAmount - lineItemDiscounts);
                const invoiceIsValidForCounting = this.getIsInvoiceValidForCounting(invoice, reportDateStamp);
                const startingAmount = invoiceIsValidForCounting ? lineItemAmount : 0;
                const creditAmounts = this.getCreditAmountsFromInvoice(invoice, reportDateStamp);

                invoice.originalAmount = lineItemAmount;
                invoice.balancePointInTime = MiscUtils.roundToTwo(startingAmount - creditAmounts);
            }
        });

        try {
            return this.filterInvoicesByBalancePointInTime({invoices, filterBalance});
        } catch (err) {
            if (err.reason) {
                throw new Meteor.Error(err.error, err.reason, err.details);
            } else {
                throw new Meteor.Error('500', err.message, '');
            }
        }
    }

    /**
     * Determines if an invoice is valid for counting based on its voided status and minimum period date.
     *
     * @param {Object} invoice - The invoice object to check.
     * @param {number} reportDateStamp - The timestamp of the report date for validation.
     * @returns {boolean} - Returns true if the invoice is valid for counting; otherwise, false.
     */
    static getIsInvoiceValidForCounting(invoice, reportDateStamp) {
        const invoiceNotVoidedOrVoidedAfter = !invoice.voidedAt || invoice.voidedAt > reportDateStamp;
        const noMinPeriodOrMinPeriodIsAfterReportDate = !invoice.minPeriodDate || invoice.minPeriodDate > reportDateStamp;
        return invoiceNotVoidedOrVoidedAfter && noMinPeriodOrMinPeriodIsAfterReportDate;
    }

    /**
     * Calculates the total credit amounts from an invoice based on the report date.
     *
     * @param {Object} invoice - The invoice object containing credits.
     * @param {number} reportDateStamp - The timestamp of the report date for filtering.
     * @returns {number} - The total calculated credit amounts from the invoice.
     */
    static getCreditAmountsFromInvoice(invoice, reportDateStamp) {
        if (!invoice.credits || !invoice.credits.length) {
            return 0;
        }
        const filteredCredits = this.filterInvoiceCreditsByReportDate(invoice, reportDateStamp);
        return filteredCredits.reduce((memo, credit) => {
            const multiplier = (credit.type === "refund" || credit.type === "chargeback") ? -1 : 1;
            const amount = (credit.voidedAt && credit.voidedAmount) || credit.amount;
            return memo + multiplier * amount;
        }, 0);
    }

    /**
     * Filters the credits in an invoice based on the provided report date.
     *
     * @param {Object} invoice - The invoice object containing credits.
     * @param {number} reportDateStamp - The timestamp of the report date for filtering.
     * @returns {Array} - An array of credits that meet the filtering criteria.
     */
    static filterInvoiceCreditsByReportDate(invoice, reportDateStamp) {
        return invoice.credits?.filter(credit => {
           const noCreditPayerSource = !credit.creditPayerSource;
           const creditNotVoidedOrVoidedAfter = !credit.voidedAt || credit.voidedAt > reportDateStamp;
           const creditCreatedAtBeforeReportDate = credit.createdAt <= reportDateStamp;

           return noCreditPayerSource && creditNotVoidedOrVoidedAfter && creditCreatedAtBeforeReportDate;
        });
    }

    /**
     * Generates organization center totals based on the provided options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {string} options.filterBuckets - The filter for age buckets ('family' or 'individual').
     * @param {Array} options.scopedOrgs - An array of scoped organization objects.
     * @returns {Object} An object containing organization center totals.
     * @throws {Meteor.Error} Throws an error if 'filterBuckets' or 'scopedOrgs' is missing in the options.
     */
    static generateOrgCenterTotals(options) {
        const {filterBuckets, scopedOrgs} = options;

        if(!filterBuckets) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateOrgCenterTotals", "filterBuckets is required");
        }

        if (!scopedOrgs) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateOrgCenterTotals", "scopedOrgs is required");
        }

        const centerTotals = {};
        if (filterBuckets === "family") {
            _.each(scopedOrgs, (org) => {
                centerTotals[org.name] = {
                    totalOriginalAmount: 0.0,
                    totalNetOriginalAmount: 0.0,
                    totalOpenAmount: 0.0,
                    totalCurrentAmount: 0.0,
                    totalZeroToSix: 0.0,
                    totalSevenToThirteen: 0.0,
                    totalFourteenToTwenty: 0.0,
                    totalTwentyOneToTwentySeven: 0.0,
                    totalOverTwentyEight: 0.0
                };
            });
        } else {
            _.each(scopedOrgs, (org) => {
                centerTotals[org.name] = {
                    totalOriginalAmount: 0.0,
                    totalNetOriginalAmount: 0.0,
                    totalOpenAmount: 0.0,
                    totalCurrentAmount: 0.0,
                    totalOneToThirty: 0.0,
                    totalThirtyOneToSixty: 0.0,
                    totalSixtyOneToNinety: 0.0,
                    totalNinetyOneToOneTwenty: 0.0,
                    totalOverOneTwenty: 0.0
                };
            });
        }

        return centerTotals;
    }

    /**
     * Groups and calculates invoices based on the provided options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {Array} options.invoices - An array of invoices to group and calculate.
     * @param {string} options.filterBuckets - The filter for age buckets ('family' or 'individual').
     * @param {Array} options.scopedOrgs - An array of scoped organization objects.
     * @returns {Array} An array of calculated invoices based on grouping and filtering.
     * @throws {Meteor.Error} Throws an error if 'invoices', 'filterBuckets', or 'scopedOrgs' is missing in the options.
     */
    static async groupAndCalculateInvoices(options) {
        const {invoices, filterBuckets, scopedOrgs} = options;

        if (!invoices) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupAndCalculateInvoices", "invoices is required");
        }

        if (!filterBuckets) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupAndCalculateInvoices", "filterBuckets is required");
        }

        if (!scopedOrgs) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupAndCalculateInvoices", "scopedOrgs is required");
        }
        // Group invoices by personId
        const invoicesGroupedByPersonId = _.groupBy(invoices, 'personId');

        const results = [];
        
        for (const [k, groupedInvoice] of Object.entries(invoicesGroupedByPersonId)) {
            const personName = await groupedInvoice[0].personName();
            const personId = groupedInvoice[0].personId;
            const person = personId && await People.findOneAsync({ _id: personId });
            const personInActive = person && person.inActive;
            const orgName = scopedOrgs.find(org => org._id === groupedInvoice[0].orgId)?.name;
            const waitListed = person?.designations?.length;
            let calculatedInvoice = {};
        
            if (filterBuckets === FilterBucketTypes.FAMILY) {
                // Calculate invoice totals for family grouping
                calculatedInvoice = {
                    orgId: groupedInvoice[0].orgId,
                    waitListed,
                    personName,
                    personId,
                    personInActive,
                    orgName,
                    invoices: _.sortBy(groupedInvoice, (invoice) => { return -1 * invoice.dueDate; }),
                    totalOriginalAmount: this.reduceByProperty(groupedInvoice, 'originalAmount'),
                    totalOpenAmount: this.reduceByProperty(groupedInvoice, 'balancePointInTime'),
                    totalCurrentAmount: this.reduceCreditsByBucketAndProperty(groupedInvoice, 'current', 'balancePointInTime'),
                    totalZeroToSix: this.reduceCreditsByBucketAndProperty(groupedInvoice, '0-6', 'balancePointInTime'),
                    totalSevenToThirteen: this.reduceCreditsByBucketAndProperty(groupedInvoice, '7-13', 'balancePointInTime'),
                    totalFourteenToTwenty: this.reduceCreditsByBucketAndProperty(groupedInvoice, '14-20', 'balancePointInTime'),
                    totalTwentyOneToTwentySeven: this.reduceCreditsByBucketAndProperty(groupedInvoice, '21-27', 'balancePointInTime'),
                    totalOverTwentyEight: this.reduceCreditsByBucketAndProperty(groupedInvoice, 'over28', 'balancePointInTime')
                }
            } else {
                // Calculate invoice totals for payer grouping
                calculatedInvoice = {
                    orgId: groupedInvoice[0].orgId,
                    personName,
                    waitListed,
                    personId,
                    personInActive,
                    orgName,
                    invoices: _.sortBy(groupedInvoice, (invoice) => { return -1 * invoice.dueDate; }),
                    totalOriginalAmount: this.reduceByProperty(groupedInvoice, 'originalAmount'),
                    totalOpenAmount: this.reduceByProperty(groupedInvoice, 'balancePointInTime'),
                    totalCurrentAmount: this.reduceCreditsByBucketAndProperty(groupedInvoice, 'current', 'balancePointInTime'),
                    totalOneToThirty: this.reduceCreditsByBucketAndProperty(groupedInvoice, '1-30', 'balancePointInTime'),
                    totalThirtyOneToSixty: this.reduceCreditsByBucketAndProperty(groupedInvoice, '31-60', 'balancePointInTime'),
                    totalSixtyOneToNinety: this.reduceCreditsByBucketAndProperty(groupedInvoice, '61-90', 'balancePointInTime'),
                    totalNinetyOneToOneTwenty: this.reduceCreditsByBucketAndProperty(groupedInvoice, '91-120', 'balancePointInTime'),
                    totalOverOneTwenty: this.reduceCreditsByBucketAndProperty(groupedInvoice, 'over121', 'balancePointInTime')
                }
            }
        
            results.push(calculatedInvoice);
        }
        
        return results;
    }

    /**
     * Builds a query object for retrieving people with open credit memos based on the provided options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {Object} options.orgQuery - The organization query object.
     * @param {string} options.searchQuery - The search query for filtering people by name.
     * @param {number} options.reportDateStamp - The report date timestamp.
     * @param {number} options.todayStamp - The current date timestamp.
     * @returns {Object} A query object for retrieving people with open credit memos.
     * @throws {Meteor.Error} Throws an error if 'orgQuery', 'reportDateStamp', or 'todayStamp' is missing in the options.
     */
    static buildPeopleCreditsQuery(options) {
        const {orgQuery, searchQuery, reportDateStamp, todayStamp} = options;

        if (!orgQuery) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildPeopleCreditsQuery", "orgQuery is required");
        }

        if (!reportDateStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildPeopleCreditsQuery", "reportDateStamp is required");
        }

        if (!todayStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to buildPeopleCreditsQuery", "todayStamp is required");
        }

        const openCreditsQuery = {
            "type": { "$ne": "securityDepositAuto" }
        };

        if (reportDateStamp < todayStamp) {
            openCreditsQuery["createdAt"] = { "$lt": reportDateStamp };
        } else {
            openCreditsQuery["openAmount"] = { "$gte": 0.01 };
        }

        const peopleCreditsQuery = {
            orgId: orgQuery["orgId"],
            inActive: { "$ne": true },
            type: "family",
            "billing.creditMemos": {
                "$elemMatch": openCreditsQuery
            }
        };
        if (searchQuery && searchQuery !== "") {
            peopleCreditsQuery["$or"] = [
                { "firstName": { "$regex": searchQuery, "$options": "i" } },
                { "lastName": { "$regex": searchQuery, "$options": "i" } }
            ];
        }

        return peopleCreditsQuery;
    }

    /**
     * Retrieves people with open credit memos based on the provided query.
     *
     * @param {Object} peopleCreditsQuery - The query object for retrieving people with open credit memos.
     * @returns {Array} An array of people with open credit memos matching the query.
     * @throws {Meteor.Error} Throws an error if 'peopleCreditsQuery' is missing.
     */
    static async getPeopleWithOpenCredits(peopleCreditsQuery) {
        if (!peopleCreditsQuery) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to getPeopleWithOpenCredits", "peopleCreditsQuery is required");
        }
        const people = await People.find(peopleCreditsQuery).fetchAsync();

        return people;
    }

    /**
     * Determines the age bucket for a given duration (in days) for family-based buckets.
     *
     * @param {number} duration - The duration in days.
     * @returns {string} - The corresponding age bucket ("current", "0-6", "7-13", "14-20", "21-27", "over28").
     */
    static getFamilyBuckets(duration) {
        if (duration <= 0) return "current";
        if (duration > 0 && duration < 7) return "0-6";
        if (duration >= 7 && duration < 14) return "7-13";
        if (duration >= 14 && duration < 21) return "14-20";
        if (duration >= 21 && duration < 28) return "21-27";
        if (duration >= 28) return "over28";
    }

    /**
     * Determines the age bucket for a given duration (in days) for payer-based buckets.
     *
     * @param {number} duration - The duration in days.
     * @returns {string} - The corresponding age bucket ("current", "1-30", "31-60", "61-90", "91-120", "over121").
     */
    static getPayerBuckets(duration) {
        if (duration <= 0) return "current";
        if (duration > 0 && duration < 31) return "1-30";
        if (duration >= 31 && duration < 61) return "31-60";
        if (duration >= 61 && duration < 91) return "61-90";
        if (duration >= 91 && duration < 121) return "91-120";
        if (duration >= 121) return "over121";
    }

    /**
     * Determines the age bucket for a given duration based on the specified filter.
     *
     * @param {number} duration - The duration in days.
     * @param {string} filterBuckets - The filter type, either "family" or "payer".
     * @returns {string} - The corresponding age bucket based on the filter type.
     * @throws {Error} Throws an error if an invalid filter is provided.
     */
    static getAmountBucketByDurationAndFilter(duration, filterBuckets) {
        if (filterBuckets === FilterBucketTypes.FAMILY) {
          return this.getFamilyBuckets(duration);
        } else {
            return this.getPayerBuckets(duration);
        }
    }

    /**
     * Generates a MongoDB aggregation pipeline for credit memo processing.
     *
     * This pipeline processes invoices to calculate total amounts for credit memos,
     * considering adjustments and voided credits as of the provided report date.
     *
     * @param {Array<string>} orgIds - The list of organization IDs to filter.
     * @param {Array<string>} creditMemoIds - The list of credit memo IDs to filter.
     * @param {number} reportDateStamp - The timestamp representing the report date.
     * @returns {Array<Object>} - The MongoDB aggregation pipeline for processing credit memos.
     */
    static generateCreditMemoAggregationQuery(orgIds, creditMemoIds, reportDateStamp) {
        return [
            // Step 1: Match invoices by orgId and creditMemoIds
            {
                $match: {
                    orgId: { $in: orgIds },
                    "credits.creditMemoId": { $in: creditMemoIds }
                }
            },
            // Step 2: Unwind the credits array to process individual credits
            {
                $unwind: "$credits"
            },
            // Step 3: Match only credits that meet the conditions:
            // Created on or before the report date AND not voided OR voided after the report date
            {
                $match: {
                    $and: [
                        { "credits.creditMemoId": { $in: creditMemoIds } },
                        { "credits.createdAt": { $lte: reportDateStamp } },
                        {
                            $or: [
                                { "credits.voidedAt": { $exists: false } },
                                { "credits.voidedAt": { $gt: reportDateStamp } }
                            ]
                        }
                    ]
                }
            },
            // Step 4: Unwind the adjustments array to process individual adjustments (if any)
            {
                $unwind: {
                    path: "$credits.adjustments",
                    preserveNullAndEmptyArrays: true // Preserve credits without adjustments
                }
            },
            // Step 5: Match adjustments based on adjustedAt date
            {
                $match: {
                    $or: [
                        { "credits.adjustments.adjustedAt": { $lte: reportDateStamp } },
                        { "credits.adjustments": { $exists: false } } // Handle credits without adjustments
                    ]
                }
            },
            // Step 6: Group by creditMemoId and calculate the total amounts
            {
                $group: {
                    _id: "$credits.creditMemoId",
                    totalAmount: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        { $gt: ["$credits.voidedAt", reportDateStamp] },
                                        { $ifNull: ["$credits.voidedAmount", false] }
                                    ]
                                },
                                "$credits.voidedAmount",
                                "$credits.amount"
                            ]
                        }
                    },
                    totalAdjustedAmount: {
                        $sum: {
                            $ifNull: ["$credits.adjustments.adjustedAmount", 0]
                        }
                    }
                }
            },
            // Step 7: Calculate the final reduced amounts
            {
                $project: {
                    creditMemoId: "$_id",
                    totalReducedAmount: { $add: ["$totalAmount", "$totalAdjustedAmount"] }
                }
            }
        ];
    }

    /**
     * Groups credit memos by their IDs and reduces the credit amount using an aggregation pipeline.
     *
     * This method processes a list of people with open credits to generate a pipeline for credit memo aggregation
     * and retrieves the reduced credit amounts as of the specified report date.
     *
     * @param {Array<Object>} peopleWithOpenCredits - Array of people objects containing billing and credit memo information.
     * @param {number} reportDateStamp - The timestamp representing the report date for filtering.
     * @returns {Promise<Array<Object>>} - An array of aggregated credit memo data including reduced amounts.
     * @throws {Meteor.Error} Throws an error if the aggregation pipeline execution fails.
     */
    static async groupCreditMemosAndReduceCreditAmount(peopleWithOpenCredits, reportDateStamp) {
        const creditMemoIds = peopleWithOpenCredits.flatMap(person => person.billing?.creditMemos?.map(cm => cm._id) || []);
        const orgIds = [...new Set(peopleWithOpenCredits.map(person => person.orgId))];
        const creditMemoPipeline = this.generateCreditMemoAggregationQuery(orgIds, creditMemoIds, reportDateStamp);
        return await (await Invoices.aggregate(creditMemoPipeline)).toArray();
    }

    /**
     * Calculates the balance point in time for a given credit memo.
     *
     * This method determines the balance of a credit memo at a specific report date by considering its original amount,
     * any applied reductions, refunded amounts, and voided status.
     *
     * @param {Object} creditMemo - The credit memo object containing relevant properties.
     * @param {number} reportDateStamp - The timestamp representing the report date.
     * @param {number} todayStamp - The timestamp representing the current date.
     * @param {Object} [foundInvoiceCreditReduction] - The reduced credit amounts from related invoices (optional).
     * @param {number} [foundInvoiceCreditReduction.totalReducedAmount=0] - The total reduced amount from related invoices.
     * @returns {number} - The calculated balance point in time for the credit memo.
     */
    static getCreditMemoBalancePointInTime(creditMemo, reportDateStamp, todayStamp, foundInvoiceCreditReduction) {
        let balancePointInTime = 0;
        if (reportDateStamp === todayStamp) {
            balancePointInTime = creditMemo.openAmount;
        } else {
            const usedAmount = foundInvoiceCreditReduction?.totalReducedAmount || 0;
            balancePointInTime = creditMemo.originalAmount - usedAmount;

            if (creditMemo.refundedAt <= reportDateStamp) {
                balancePointInTime -= creditMemo.refundedAmount;
            }

            if (creditMemo.voidedAt <= reportDateStamp) {
                balancePointInTime = 0;
            }
        }

        return balancePointInTime;
    }

    /**
     * Generates age buckets for open credits based on the credit created date and provided options.
     *
     * @param {Object} options - The options object containing various parameters.
     * @param {Array} options.peopleWithOpenCredits - An array of people with open credit memos.
     * @param {number} options.reportDateStamp - The report date timestamp.
     * @param {number} options.todayStamp - The current date timestamp.
     * @param {Date} options.todayDate - The current date.
     * @param {string} options.filterBuckets - The filter for age buckets ('family' or 'individual').
     * @param {Array} options.groupedAndCalculatedInvoices - An array of grouped and calculated invoices.
     * @returns {Object} An object containing open credits and intersected people IDs.
     * @throws {Meteor.Error} Throws an error if any required parameter is missing in the options.
     */
    static async generateAgeBucketsByCreditCreatedDate(options) {
        const { peopleWithOpenCredits, reportDateStamp, todayStamp, todayDate, filterBuckets, groupedAndCalculatedInvoices } = options;

        if (!peopleWithOpenCredits) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "peopleWithOpenCredits is required");
        }

        if (!reportDateStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "reportDateStamp is required");
        }

        if (!todayStamp) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "todayStamp is required");
        }

        if (!todayDate) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "todayDate is required");
        }

        if (!filterBuckets) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "filterBuckets is required");
        }

        if (!groupedAndCalculatedInvoices) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgeBucketsByCreditCreatedDate", "groupedAndCalculatedInvoices is required");
        }

        const openInvoicedPeopleIds = _.pluck(groupedAndCalculatedInvoices, 'personId');

        if (!peopleWithOpenCredits.length) {
            return { openCredits: [], intersectedPeopleIds: [...openInvoicedPeopleIds] };
        }

        const reducedCreditMemoInvoiceCredits = await this.groupCreditMemosAndReduceCreditAmount(peopleWithOpenCredits, reportDateStamp);
        const openCredits = peopleWithOpenCredits.flatMap(person =>
            person?.billing?.creditMemos
                ?.filter(creditMemo => (reportDateStamp < todayStamp) ? creditMemo.createdAt <= reportDateStamp : (creditMemo.openAmount >= 0.01 && !creditMemo.voidedAt))
                .map(creditMemo => {
                    creditMemo.orgId = person.orgId;
                    creditMemo.paidByPersonId = person._id;
                    creditMemo.paidByPersonName = `${person.firstName} ${person.lastName}`;
                    const foundInvoiceCreditReduction = reducedCreditMemoInvoiceCredits.find(reducedCredit => reducedCredit._id === creditMemo._id);
                    const duration = moment.duration(todayDate.diff(creditMemo.createdAt)).asDays();
                    creditMemo.amountBucket = this.getAmountBucketByDurationAndFilter(duration, filterBuckets);
                    creditMemo.balancePointInTime = this.getCreditMemoBalancePointInTime(creditMemo, reportDateStamp, todayStamp, foundInvoiceCreditReduction);

                    creditMemo.openAmount *= -1;
                    creditMemo.balancePointInTime *= -1;
                    creditMemo.originalAmount *= -1;
                    creditMemo.isCredit = true;
                    return creditMemo;
                }).filter(creditMemo => creditMemo.balancePointInTime <= -0.01));
        const openCreditRelatedPeopleIds = _.pluck(await Relationships.find({ personId: { "$in": _.pluck(openCredits, 'paidByPersonId') }, relationshipType: "family" }).fetchAsync(), "targetId");
        const intersectedPeopleIds = _.union(openCreditRelatedPeopleIds, openInvoicedPeopleIds);

        return {openCredits, intersectedPeopleIds};
    }

    /**
     * Filters open credits based on the specified balance type.
     *
     * @param {Object} options - The options object containing parameters.
     * @param {Array} options.openCredits - An array of open credit memos to filter.
     * @param {string} options.filterBalanceType - The balance type to filter by ('payer' or 'family').
     * @returns {Array} An array of filtered open credits.
     * @throws {Meteor.Error} Throws an error if the openCredits parameter is missing in the options.
     */
    static filterOpenCreditsByBalanceType(options) {
        const {openCredits, filterBalanceType} = options;

        if (!openCredits) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to filterOpenCreditsByBalanceType", "openCredits is required");
        }

        return  openCredits.filter(credit => {
            if (filterBalanceType === "payer") {
                return credit.type !== 'systemOverpayment' &&
                    !credit.type.startsWith('excess_') &&
                    !credit.type.startsWith('manualCard') &&
                    !credit.type.startsWith('check') &&
                    !credit.type.startsWith('cash') &&
                    !credit.type.startsWith('manualAch') &&
                    !credit.type.startsWith('payrollDeduction');
            }
            if (filterBalanceType === "family") {
                return !credit.type.startsWith('prepaid_');
            }

            return true;
        });
    }

    /**
     * Helper function for calculating and accumulating totals in a center object.
     *
     * @param {Object} totals - The object containing the total values.
     * @param {Object} row - The row object with calculated values to add to the totals.
     * @param {boolean} isRollup - Indicates whether this is a rollup operation.
     * @param {string} filterBuckets - The filter bucket type ('family' or 'payer').
     */
    static centerHelper = (totals, row, isRollup, filterBuckets) => {
        if (row !== undefined && totals !== undefined) {
            totals.totalOriginalAmount += row.totalOriginalAmount;
            totals.totalNetOriginalAmount += 'totalNetOriginalAmount' in row ? (row.totalNetOriginalAmount ?? 0) : (row.totalOriginalAmount ?? 0);
            totals.totalOpenAmount += 'totalNetOpenAmount' in row ? (row.totalNetOpenAmount ?? 0) : (row.totalOpenAmount ?? 0);
            totals.totalCurrentAmount += 'totalNetCurrentAmount' in row ? (row.totalNetCurrentAmount ?? 0) : (row.totalCurrentAmount ?? 0);
            if (filterBuckets === FilterBucketTypes.FAMILY) {
                totals.totalZeroToSix += 'totalNetZeroToSix' in row ? (row.totalNetZeroToSix ?? 0) : (row.totalZeroToSix ?? 0);
                totals.totalSevenToThirteen += 'totalNetSevenToThirteen' in row ? (row.totalNetSevenToThirteen ?? 0) : (row.totalSevenToThirteen ?? 0);
                totals.totalFourteenToTwenty += 'totalNetFourteenToTwenty' in row ? (row.totalNetFourteenToTwenty ?? 0) : (row.totalFourteenToTwenty ?? 0);
                totals.totalTwentyOneToTwentySeven += 'totalNetTwentyOneToTwentySeven' in row ? (row.totalNetTwentyOneToTwentySeven ?? 0) : (row.totalTwentyOneToTwentySeven ?? 0);
                totals.totalOverTwentyEight += 'totalNetOverTwentyEight' in row ? (row.totalNetOverTwentyEight ?? 0) : (row.totalOverTwentyEight ?? 0);
            } else {
                totals.totalOneToThirty += 'totalNetOneToThirty' in row ? (row.totalNetOneToThirty ?? 0) : (row.totalOneToThirty ?? 0);
                totals.totalThirtyOneToSixty += 'totalNetThirtyOneToSixty' in row ? (row.totalNetThirtyOneToSixty ?? 0) : (row.totalThirtyOneToSixty ?? 0);
                totals.totalSixtyOneToNinety += 'totalNetSixtyOneToNinety' in row ? (row.totalNetSixtyOneToNinety ?? 0) : (row.totalSixtyOneToNinety ?? 0);
                totals.totalNinetyOneToOneTwenty += 'totalNetNinetyOneToOneTwenty' in row ? (row.totalNetNinetyOneToOneTwenty ?? 0) : (row.totalNinetyOneToOneTwenty ?? 0);
                totals.totalOverOneTwenty += 'totalNetOverOneTwenty' in row ? (row.totalNetOverOneTwenty ?? 0) : (row.totalOverOneTwenty ?? 0);
            }
            totals.isRollup = isRollup;
        }
    }

    /**
     * Calculates and formats center totals based on the provided options.
     *
     * @param {Object} options - The options object containing parameters.
     * @param {Object} options.centerTotals - The center totals object to be calculated.
     * @param {Object} options.orgsMeta - The organization metadata for formatting.
     * @returns {Object} The calculated and formatted center totals.
     * @throws {Meteor.Error} Throws an error if centerTotals or orgsMeta is missing in the options.
     */
    static calculateCenterTotals(options) {
        const {centerTotals, orgsMeta} = options;

        if (!centerTotals) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to calculateCenterTotals: ", "centerTotals is required");
        }

        if (!orgsMeta) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to calculateCenterTotals: ", "orgsMeta is required");
        }

        const centerTotalsArray = [];
        for (const key in centerTotals) {
            centerTotals[key].orgName = key;
            centerTotalsArray.push(centerTotals[key]);
        }
        ReportAggregation.applyMeta(centerTotalsArray, orgsMeta, 'orgName');
        const rekeyedCenterTotals = {};
        for (const tmp of centerTotalsArray) {
            rekeyedCenterTotals[tmp.orgName] = tmp;

        }

        return rekeyedCenterTotals;
    }

    /**
     * Reduces an array of objects by a specified property and returns the sum of those values.
     *
     * @param {Array} array - The array of objects to reduce.
     * @param {string} property - The property by which to reduce and sum the values.
     * @returns {number} The sum of the values of the specified property in the array.
     */
    static reduceByProperty(array, property) {
        return array.reduce((memo, i) => { return memo + i[property]; }, 0.0);
    }

    /**
     * Filters an array of objects by a specified bucket and reduces the values of a specified property within the filtered array.
     *
     * @param {Array} array - The array of objects to filter and reduce.
     * @param {string} property - The property by which to reduce the values.
     * @param {string} bucket - The bucket to use for filtering.
     * @returns {number} The sum of the values of the specified property in the filtered array.
     */
    static filterByBucketAndReduceByProperty(array, property, bucket) {
        return array.filter(elem => elem.amountBucket === bucket).reduce((memo, i) => { return memo + i[property] }, 0.0);
    }

    /**
     * Reduces the values of a specified property within an array of credit objects based on a specified bucket.
     *
     * @param {Array} array - The array of credit objects to reduce.
     * @param {string} bucket - The bucket used to filter credit objects.
     * @param {string} property - The property by which to reduce the values.
     * @returns {number} The sum of the values of the specified property in the credit objects that match the specified bucket.
     */
    static reduceCreditsByBucketAndProperty(array, bucket, property) {
        return array.reduce((memo, i) => { return i.amountBucket === bucket ? memo + i[property] : memo; }, 0.0)
    }

    /**
     * Groups invoices and processes the output data based on various parameters.
     *
     * @param {Object} options - The options object.
     * @param {Array} options.groupedAndCalculatedInvoices - An array of grouped and calculated invoices.
     * @param {Array} options.filteredCredits - An array of filtered credits.
     * @param {Array} options.intersectedPeopleIds - An array of intersected people IDs.
     * @param {string} options.filterBuckets - The filter buckets parameter.
     * @param {Array} options.scopedOrgs - An array of scoped organizations.
     * @param {Object} options.orgsMap - A map of organization IDs to their corresponding organizations.
     * @param {Object} options.centerTotals - A map of center totals.
     * @returns {Array} An array of processed output data based on the provided parameters.
     */
    static async groupInvoicesByFamilyAndProcessOutput(options) {
        const {
            groupedAndCalculatedInvoices,
            filteredCredits,
            intersectedPeopleIds,
            filterBuckets,
            scopedOrgs,
            orgsMap,
            centerTotals
        } = options;

        if (!filterBuckets) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByFamilyAndProcessOutput: ", "filterBuckets is required");
        }

        if (!scopedOrgs) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByFamilyAndProcessOutput: ", "scopedOrgs is required");
        }

        if (!orgsMap) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByFamilyAndProcessOutput: ", "orgsMap is required");
        }

        if (!centerTotals) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByFamilyAndProcessOutput: ", "centerTotals is required");
        }

        const { relationshipsMap, relationships } = await this.getAllRelationshipsFromIntersectedPeopleAndMapByTargetId(intersectedPeopleIds);
        const parentMap = await this.getAllParentsFromRelationships(relationships);
        const groupedInvoicesMap = _.groupBy(groupedAndCalculatedInvoices, "personId");

        const foundCreditIds = new Set();

        const groupedFamilies = this.groupInvoicesByFamily(intersectedPeopleIds, relationshipsMap)

        // Process each family group
        const results = Object.values(groupedFamilies).map(familyGroup => {
            const familyMemberIds = familyGroup[0].familyMemberIds;

            // Consolidate invoices and credits for all children in the family
            const groupedInvoices = familyGroup.flatMap(({ childId }) => groupedInvoicesMap[childId] || []);
            const familyCredits = filteredCredits.filter(
                c => familyMemberIds.includes(c.paidByPersonId) && !foundCreditIds.has(c._id)
            );

            // Mark credits as processed
            familyCredits.forEach(c => foundCreditIds.add(c._id));

            if (!groupedInvoices.length && !familyCredits.length) {
                return null;
            }

            const outgroup = {
                familyMembers: familyMemberIds.map(fid => parentMap[fid]),
                groupedInvoices,
                familyCredits,
                familyName: familyMemberIds.map(fid => {
                    const familyMember = parentMap[fid];
                    return familyMember?.lastName + ", " + familyMember?.firstName;
                }).sort().join("; "),
            };

            // Aggregate totals
            const totals = this.aggregateTotals(groupedInvoices, familyCredits, filterBuckets);
            return { ...outgroup, ...totals };
        }).filter(group => group !== null);

        this.updateCenterTotals(results, orgsMap, centerTotals, filterBuckets, scopedOrgs);

        return results;
    }

    /**
     * Groups child IDs into family groups based on relationships.
     *
     * @param {Array<string>} intersectedPeopleIds - Array of child IDs to be grouped.
     * @param {Object} relationshipsMap - A map of child IDs to their associated family members.
     * @returns {Object} - An object where keys are family keys (sorted family member IDs joined by '|') and values are arrays of family groups.
     */
    static groupInvoicesByFamily(intersectedPeopleIds, relationshipsMap) {
        const families = intersectedPeopleIds.map(childId => {
            const familyMembers = relationshipsMap[childId] || [];
            const familyMemberIds = [...new Set(familyMembers.map(member => member.personId).sort())];
            return {
                childId,
                familyKey: familyMemberIds.join("|"),
                familyMemberIds,
            };
        });

        return _.groupBy(families, "familyKey");
    }

    /**
     * Retrieves all family relationships for the given intersected people IDs and maps them by target ID.
     *
     * @param {string[]} intersectedPeopleIds - Array of IDs for people to find relationships for.
     * @returns {Promise<{relationshipsMap: Record<string, Object[]>, relationships: Object[]}>}
     *          A promise resolving to an object containing:
     *          - `relationshipsMap`: A map of relationships grouped by target ID.
     *          - `relationships`: An array of all matching relationships.
     */
    static async getAllRelationshipsFromIntersectedPeopleAndMapByTargetId(intersectedPeopleIds) {
        const relationships = await Relationships.find({ targetId: { $in: intersectedPeopleIds }, relationshipType: "family" }).fetchAsync();
        return {
            relationshipsMap: _.groupBy(relationships, "targetId"),
            relationships
        };
    }

    /**
     * Retrieves all parents (people) from the given relationships and maps them by their person ID.
     *
     * @param {Object[]} relationships - Array of relationship objects containing `personId`.
     * @returns {Promise<Record<string, Object>>} A promise resolving to a map of people, keyed by their `_id`.
     */
    static async getAllParentsFromRelationships(relationships) {
        const peopleIds = relationships.map(relationship => relationship.personId);
        const people = await People.find({ "_id": { "$in": peopleIds } }).fetchAsync();
        return people.reduce((acc, person) => {
            acc[person._id] = person;
            return acc;
        }, {});
    }

    /**
     * Aggregates totals for grouped invoices and family credits based on filter buckets.
     *
     * @param {Array} groupedInvoices - Array of grouped invoices with relevant properties for aggregation.
     * @param {Array} familyCredits - Array of family credits with relevant properties for aggregation.
     * @param {string} filterBuckets - Type of buckets used for filtering (e.g., "family" or "payer").
     * @returns {Object} An object containing aggregated totals for invoices, credits, and combined values.
     */
    static aggregateTotals(groupedInvoices, familyCredits, filterBuckets) {
        const invoiceTotals = groupedInvoices.reduce((totals, invoice) => {
            totals.totalOriginalAmount += Number(invoice.totalOriginalAmount) || 0;
            totals.totalOpenAmount += Number(invoice.totalOpenAmount) || 0;
            totals.totalCurrentAmount += Number(invoice.totalCurrentAmount) || 0;

            if (filterBuckets === "family") {
                totals.totalZeroToSix += Number(invoice.totalZeroToSix) || 0;
                totals.totalSevenToThirteen += Number(invoice.totalSevenToThirteen) || 0;
                totals.totalFourteenToTwenty += Number(invoice.totalFourteenToTwenty) || 0;
                totals.totalTwentyOneToTwentySeven += Number(invoice.totalTwentyOneToTwentySeven) || 0;
                totals.totalOverTwentyEight += Number(invoice.totalOverTwentyEight) || 0;
            } else {
                totals.totalOneToThirty += Number(invoice.totalOneToThirty) || 0;
                totals.totalThirtyOneToSixty += Number(invoice.totalThirtyOneToSixty) || 0;
                totals.totalSixtyOneToNinety += Number(invoice.totalSixtyOneToNinety) || 0;
                totals.totalNinetyOneToOneTwenty += Number(invoice.totalNinetyOneToOneTwenty) || 0;
                totals.totalOverOneTwenty += Number(invoice.totalOverOneTwenty) || 0;
            }

            return totals;
        }, this.initializeTotals(filterBuckets)) || this.initializeTotals(filterBuckets);

        const creditTotals = familyCredits?.reduce((totals, credit) => {
            totals.totalCreditOpenAmount += Number(credit.balancePointInTime) || 0;
            totals.totalCreditOriginalAmount += Number(credit.originalAmount) || 0;

            if (filterBuckets === "family") {
                totals.totalCreditZeroToSix += credit.amountBucket === "0-6" ? (credit.balancePointInTime || 0) : 0;
                totals.totalCreditSevenToThirteen += credit.amountBucket === "7-13" ? (credit.balancePointInTime || 0) : 0;
                totals.totalCreditFourteenToTwenty += credit.amountBucket === "14-20" ? (credit.balancePointInTime || 0) : 0;
                totals.totalCreditTwentyOneToTwentySeven += credit.amountBucket === "21-27" ? (credit.balancePointInTime || 0) : 0;
                totals.totalCreditOverTwentyEight += credit.amountBucket === "over28" ? (credit.balancePointInTime || 0) : 0;
            } else {
                totals.totalCreditOneToThirty += credit.amountBucket === "1-30" ? (credit.balancePointInTime || 0) : 0;
                totals.totalCreditThirtyOneToSixty += credit.amountBucket === "31-60" ? (credit.balancePointInTime || 0) : 0;
                totals.totalCreditSixtyOneToNinety += credit.amountBucket === "61-90" ? (credit.balancePointInTime || 0) : 0;
                totals.totalCreditNinetyOneToOneTwenty += credit.amountBucket === "91-120" ? (credit.balancePointInTime || 0) : 0;
                totals.totalCreditOverOneTwenty += credit.amountBucket === "over121" ? (credit.balancePointInTime || 0) : 0;
            }

            return totals;
        }, this.initializeTotals(filterBuckets, true)) || this.initializeTotals(filterBuckets, true);

        // Combine invoice and credit totals
        return {
            ...invoiceTotals,
            ...creditTotals,
            totalNetOriginalAmount: invoiceTotals.totalOriginalAmount + creditTotals.totalCreditOriginalAmount,
            totalNetOpenAmount: invoiceTotals.totalOpenAmount + creditTotals.totalCreditOpenAmount,
            totalNetCurrentAmount: invoiceTotals.totalCurrentAmount + creditTotals.totalCreditCurrentAmount,
            // Combined bucket totals
            ...(filterBuckets === "family"
                ? {
                    totalNetZeroToSix: invoiceTotals.totalZeroToSix + creditTotals.totalCreditZeroToSix,
                    totalNetSevenToThirteen: invoiceTotals.totalSevenToThirteen + creditTotals.totalCreditSevenToThirteen,
                    totalNetFourteenToTwenty: invoiceTotals.totalFourteenToTwenty + creditTotals.totalCreditFourteenToTwenty,
                    totalNetTwentyOneToTwentySeven: invoiceTotals.totalTwentyOneToTwentySeven + creditTotals.totalCreditTwentyOneToTwentySeven,
                    totalNetOverTwentyEight: invoiceTotals.totalOverTwentyEight + creditTotals.totalCreditOverTwentyEight,
                }
                : {
                    totalNetOneToThirty: invoiceTotals.totalOneToThirty + creditTotals.totalCreditOneToThirty,
                    totalNetThirtyOneToSixty: invoiceTotals.totalThirtyOneToSixty + creditTotals.totalCreditThirtyOneToSixty,
                    totalNetSixtyOneToNinety: invoiceTotals.totalSixtyOneToNinety + creditTotals.totalCreditSixtyOneToNinety,
                    totalNetNinetyOneToOneTwenty: invoiceTotals.totalNinetyOneToOneTwenty + creditTotals.totalCreditNinetyOneToOneTwenty,
                    totalNetOverOneTwenty: invoiceTotals.totalOverOneTwenty + creditTotals.totalCreditOverOneTwenty,
                }),
        };
    }

    /**
     * Initializes an object with total properties set to 0 based on the filter bucket type and whether it's for credit totals.
     *
     * @param {string} filterBuckets - The filter type for grouping, e.g., "family" or "payer".
     * @param {boolean} [isCredit=false] - Flag indicating whether to include credit-specific totals.
     * @returns {Object} An object with initialized totals.
     */
    static initializeTotals(filterBuckets, isCredit = false) {
        if (isCredit) {
            return this.initializeCreditTotals(filterBuckets);
        } else {
            return this.initializeInvoiceTotals(filterBuckets);
        }
    }

    static initializeCreditTotals(filterBuckets) {
        const totals = {
            totalCreditOriginalAmount: 0,
            totalCreditOpenAmount: 0,
            totalCreditCurrentAmount: 0,
        };

        if (filterBuckets === FilterBucketTypes.FAMILY) {
            totals.totalCreditZeroToSix = 0;
            totals.totalCreditSevenToThirteen = 0;
            totals.totalCreditFourteenToTwenty = 0;
            totals.totalCreditTwentyOneToTwentySeven = 0;
            totals.totalCreditOverTwentyEight = 0;
        } else {
            totals.totalCreditOneToThirty = 0;
            totals.totalCreditThirtyOneToSixty = 0;
            totals.totalCreditSixtyOneToNinety = 0;
            totals.totalCreditNinetyOneToOneTwenty = 0;
            totals.totalCreditOverOneTwenty = 0;
        }

        return totals;
    }

    static initializeInvoiceTotals(filterBuckets) {
        const totals = {
            totalOriginalAmount: 0,
            totalOpenAmount: 0,
            totalCurrentAmount: 0,
        };

        if (filterBuckets === FilterBucketTypes.FAMILY) {
            totals.totalZeroToSix = 0;
            totals.totalSevenToThirteen = 0;
            totals.totalFourteenToTwenty = 0;
            totals.totalTwentyOneToTwentySeven = 0;
            totals.totalOverTwentyEight = 0;
        } else {
            totals.totalOneToThirty = 0;
            totals.totalThirtyOneToSixty = 0;
            totals.totalSixtyOneToNinety = 0;
            totals.totalNinetyOneToOneTwenty = 0;
            totals.totalOverOneTwenty = 0;
        }

        return totals;
    }

    /**
     * Updates the center totals for each organization by aggregating data from the provided results.
     *
     * @param {Array} results - Array of grouped results containing family members, invoices, and credits.
     * @param {Object} orgsMap - Mapping of organization IDs to their respective parent organizations.
     * @param {Object} centerTotals - Object containing aggregated totals for each organization or center.
     * @param {string} filterBuckets - Type of buckets used for filtering, e.g., "family" or "payer".
     * @param {Array} scopedOrgs - Array of scoped organization objects.
     */
    static updateCenterTotals(results, orgsMap, centerTotals, filterBuckets, scopedOrgs) {
        results.forEach(outgroup => {
            const familyOrgId =
                (outgroup.familyMembers?.[0]?.orgId) ||
                (outgroup.groupedInvoices?.[0]?.orgId) ||
                (outgroup.familyCredits?.[0]?.orgId);

            const familyOrg = scopedOrgs.find(org => org._id === familyOrgId);
            const orgName = familyOrg?.name;

            (orgsMap[familyOrgId] || []).forEach(pOrg => {
                this.centerHelper(centerTotals[pOrg], outgroup, true, filterBuckets);
            });

            if (orgName) {
                outgroup.orgName = orgName;
                this.centerHelper(centerTotals[orgName], outgroup, false, filterBuckets);
            }
        });
    }


    /**
     * Groups invoices by individual and processes the output data based on various parameters.
     *
     * @param {Object} options - The options object.
     * @param {Array} options.filteredCredits - An array of filtered credits.
     * @param {Array} options.scopedOrgs - An array of scoped organizations.
     * @param {Object} options.orgsMap - A map of organization IDs to their corresponding organizations.
     * @param {string} options.filterBuckets - The filter buckets parameter.
     * @param {Array} options.groupedAndCalculatedInvoices - An array of grouped and calculated invoices.
     * @param {Object} options.centerTotals - A map of center totals.
     */
    static async groupInvoicesByIndividualAndProcessOutput(options) {
        const { filteredCredits, scopedOrgs, orgsMap, filterBuckets, groupedAndCalculatedInvoices, centerTotals } = options;

        if (!scopedOrgs) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByIndividualAndProcessOutput: ", "scopedOrgs is required");
        }

        if (!orgsMap) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByIndividualAndProcessOutput: ", "orgsMap is required");
        }

        if (!filterBuckets) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByIndividualAndProcessOutput: ", "filterBuckets is required");
        }

        if (!centerTotals) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to groupInvoicesByIndividualAndProcessOutput: ", "centerTotals is required");
        }

        const groupedCredits = _.groupBy(filteredCredits, "paidByPersonId");
        for (const [paidByPersonId, ocs] of Object.entries(groupedCredits)) {
            const creditPerson = await People.findOneAsync({ _id: paidByPersonId });
            const calculatedCredit = {
                personName: creditPerson && creditPerson.lastName + ', ' + creditPerson.firstName + ' (family)',
                personId: paidByPersonId,
                personInActive: creditPerson && creditPerson.inActive,
                orgName: scopedOrgs.find(org => org._id === ocs[0].orgId)?.name,
                orgId: ocs[0].orgId,
                invoices: _.sortBy(ocs, (i) => { return -1 * i.createdAt; }),
                totalOriginalAmount: this.reduceByProperty(ocs, 'originalAmount'),
                totalOpenAmount: this.reduceByProperty(ocs, 'balancePointInTime'),
                totalCurrentAmount: this.reduceCreditsByBucketAndProperty(ocs, 'current', 'openAmount'),
            };
        
            if (filterBuckets === FilterBucketTypes.FAMILY) {
                calculatedCredit.totalZeroToSix = this.reduceCreditsByBucketAndProperty(ocs, '0-6', 'openAmount');
                calculatedCredit.totalSevenToThirteen = this.reduceCreditsByBucketAndProperty(ocs, '7-13', 'openAmount');
                calculatedCredit.totalFourteenToTwenty = this.reduceCreditsByBucketAndProperty(ocs, '14-20', 'openAmount');
                calculatedCredit.totalTwentyOneToTwentySeven = this.reduceCreditsByBucketAndProperty(ocs, '21-27', 'openAmount');
                calculatedCredit.totalOverTwentyEight = this.reduceCreditsByBucketAndProperty(ocs, 'over28', 'openAmount');
            } else {
                calculatedCredit.totalOneToThirty = this.reduceCreditsByBucketAndProperty(ocs, '1-30', 'openAmount');
                calculatedCredit.totalThirtyOneToSixty = this.reduceCreditsByBucketAndProperty(ocs, '31-60', 'openAmount');
                calculatedCredit.totalSixtyOneToNinety = this.reduceCreditsByBucketAndProperty(ocs, '61-90', 'openAmount');
                calculatedCredit.totalNinetyOneToOneTwenty = this.reduceCreditsByBucketAndProperty(ocs, '91-120', 'openAmount');
                calculatedCredit.totalOverOneTwenty = this.reduceCreditsByBucketAndProperty(ocs, 'over121', 'openAmount');
            }
            groupedAndCalculatedInvoices.push(calculatedCredit);
        }

        _.each(groupedAndCalculatedInvoices, (groupedInvoice) => {
            const org = scopedOrgs.find(org => org._id === groupedInvoice.orgId);
            const orgName = org?.name;
            for (const pOrg of orgsMap[groupedInvoice.orgId] || []) {
                this.centerHelper(centerTotals[pOrg], groupedInvoice, true, filterBuckets);
            }
            this.centerHelper(centerTotals[orgName], groupedInvoice, false, filterBuckets);
        });

    }

    /**
     * Generates an aging report based on the provided options and parameters.
     * @param {Object} options - The options for generating the aging report.
     * @param {string} options.filterBalance - The filter balance option.
     * @param {string} options.filterBalanceType - The filter balance type option.
     * @param {string} options.searchQuery - The search query for filtering.
     * @param {boolean} options.includeWaitList - Indicates whether to include waitlist.
     * @param {boolean} options.usePeriodDate - Indicates whether to use the period date.
     * @param {string} options.filterGrouping - The filter grouping option.
     * @returns {Object} - The aging report data.
     * @throws {Meteor.Error} - Throws an error if invalid parameters are provided.
     */
    static async generateAgingReport(options) {
        if (!options) {
            throw new Meteor.Error("invalid-params", "Invalid parameters passed to generateAgingReport: ", "options is required");
        }

        try {
            const { filterBalance, filterBalanceType, searchQuery, includeWaitList, usePeriodDate, filterGrouping } = options;

            const { scopedOrgs, reportDateStamp, todayStamp, orgsMap, orgsMeta , orgQuery} = await this.buildOrgAndTimeData(options);

            const { query,  payerQueryMatchPart } = this.buildQuery({ reportDateStamp, todayStamp,  filterBalanceType, orgQuery});

            const rawInvoices = await this.aggregateAgingReport({ query, payerQueryMatchPart, filterBalanceType, reportDateStamp });

            const filterBuckets = filterBalanceType === AvailableBalanceTypes.FAMILY ? FilterBucketTypes.FAMILY : FilterBucketTypes.OTHER;
            const todayDate = new moment(reportDateStamp);

            const filteredInvoicesByOptions = await this.filterInvoicesByOptions({searchQuery, includeWaitList, invoices: rawInvoices});
            
            const invoiceAgeBucketsOptions = {
                invoices: filteredInvoicesByOptions,
                filterBuckets,
                todayDate,
                todayStamp,
                usePeriodDate,
                reportDateStamp,
                filterBalance
            };
            const filteredInvoicesWithBuckets = this.generateAgeBucketsByInvoiceDueDate(invoiceAgeBucketsOptions);
            const centerTotals = this.generateOrgCenterTotals({filterBuckets, scopedOrgs});
            const groupedAndCalculatedInvoices = await this.groupAndCalculateInvoices({invoices: filteredInvoicesWithBuckets, filterBuckets, scopedOrgs});
            const peopleCreditsQuery = this.buildPeopleCreditsQuery({orgQuery, searchQuery, reportDateStamp, todayStamp});
            const peopleWithOpenCredits = await this.getPeopleWithOpenCredits(peopleCreditsQuery);
            const creditAgeBucketOptions = {
                filterBuckets,
                groupedAndCalculatedInvoices,
                peopleWithOpenCredits,
                todayDate,
                todayStamp,
                reportDateStamp
            };
            const {openCredits, intersectedPeopleIds} = await this.generateAgeBucketsByCreditCreatedDate(creditAgeBucketOptions);
            const filteredCredits = this.filterOpenCreditsByBalanceType({openCredits, filterBalanceType});

            if (filterGrouping === AvailableBalanceTypes.FAMILY) {
                const invoiceOutputOptions = {
                    groupedAndCalculatedInvoices,
                    filteredCredits,
                    intersectedPeopleIds,
                    filterBuckets,
                    scopedOrgs,
                    orgsMap,
                    centerTotals
                };
                const invoiceOutput = await this.groupInvoicesByFamilyAndProcessOutput(invoiceOutputOptions);
                const calculatedCenterTotals = this.calculateCenterTotals({centerTotals, orgsMeta});

                return {
                    centerTotals: calculatedCenterTotals,
                    invoices: _.sortBy(invoiceOutput.filter(gr => !!gr), (gr) => { return gr.familyName; })
                }
            } else {

                const invoiceOutputOptions = {
                    filteredCredits,
                    scopedOrgs,
                    orgsMap,
                    filterBuckets,
                    groupedAndCalculatedInvoices,
                    centerTotals
                };
                await this.groupInvoicesByIndividualAndProcessOutput(invoiceOutputOptions);
                const calculatedCenterTotals = this.calculateCenterTotals({centerTotals, orgsMeta});

                return {
                    centerTotals: calculatedCenterTotals,
                    invoices: [{ groupedInvoices: _.sortBy(groupedAndCalculatedInvoices, (groupedInvoice) => { return groupedInvoice.personName; }) }]
                }
            }
        } catch (err) {
            if (err.reason) {
                throw new Meteor.Error(err.error, err.reason, err.details);
            } else {
                console.error(err);
                throw new Meteor.Error('500', err.message, '');
            }
        }
    }
}