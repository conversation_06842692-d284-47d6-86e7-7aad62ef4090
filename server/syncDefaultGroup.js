import moment from "moment-timezone";
import { Log } from '../lib/util/log';
import { BillingTimezoneMap } from '../lib/constants/timezoneConstants';
import Papa from 'papaparse';
import { ConfigurationSettingsService, ServerConfigurationConstants } from './config/configurationSettingsService';
import { EmailService } from './emails/emailService';
import { People } from "../lib/collections/people";
import { Orgs } from "../lib/collections/orgs";
import { Reservations } from "../lib/collections/reservations";
import { Groups } from "../lib/collections/groups";
const _ = require('lodash');
export class syncDefaultGroup {

    // static interface ChildReservations: {
    //     child: Child;
    //     newReservations: Array<Reservations>;
    //     oldReservations: Array<Reservations>;
    // }

    /**
     * updates People collection with new default group.
     *
     * @param childId
     * @param groupId
     */
    static async setChildDefaultGroup(childId, groupId) {
        const matchedGroup = await Groups.findOneAsync({ _id: groupId });

        if (!matchedGroup) {
            throw new Meteor.Error(`Group with id ${groupId} not found`);
        }

        try {
            await People.updateAsync({ _id: childId }, { $set: { defaultGroupId: groupId } })
        } catch (e) {
            throw new Meteor.Error(e.message);
        }
    }

    /**
     * gets all uncancelled and recurring reservations with a start date of today
     *
     * @returns Array<Reservations>
     */
    static async fetchAllReservationsStartingToday() {
        const results = [];
        const todayDate = new moment.utc().format('MM/DD/YYYY');
        const orgs = await Orgs.find({"customizations.reservations/enabled":true}, {readPreference: 'secondaryPreferred'}).fetchAsync();;
        for (const org of orgs) {
            const todayTime = new moment.tz(todayDate, 'MM/DD/YYYY', org.getTimezone()).startOf('day').valueOf();
            Log.info(`Fetching reservations for ${org.name} that are starting ${todayTime}`);
            const reservations = await Reservations.find({
                orgId: org._id,
                scheduledDate: { $eq: todayTime },
                reservationType: { $eq: 'person' },
                cancellationDate: { $not: { $exists: true } },
                scheduleType: { $exists: true }
            }, { readPreference: 'secondaryPreferred' }).fetchAsync();
            for (const res of reservations) {
                results.push(res);
            }
        }
        return results;
    }

    /**
     * returns all children associated with the passed in reservations
     *
     * @param arrayOfReservations
     * @returns Array<People>
     */
    static async getChildren(arrayOfReservations) {
        const childIdArray = [];
        arrayOfReservations.forEach(res => {
            if (childIdArray.find(child => child === res.selectedPerson)) {
                return;
            }
            childIdArray.push(res.selectedPerson);
        });
        return await People.find({_id: {$in: childIdArray}, inActive: {$eq: false}}, {readPreference: 'secondaryPreferred'}).fetchAsync();
    }

    /**
     * returns all ended reservations associated with passed in array of children
     *
     * @param arrayOfChildren
     * @returns Array<Reservations>
     */
    static async getReservationsEndingTodayByChild(arrayOfChildren) {
        const results = [];
        const todayDate = new moment.utc().format('MM/DD/YYYY');
        for (const child of arrayOfChildren) {
            const org = await Orgs.findOneAsync({_id: child.orgId});
            if(!org) {
                Log.info(`No valid org for child ${child._id}`);
                continue;
            }
            const todayTime = moment.tz(todayDate, 'MM/DD/YYYY', org.getTimezone()).startOf('day').valueOf();
            const reservations = Reservations.find({orgId: child.orgId, selectedPerson: child._id, scheduledEndDate: {$lte: todayTime}},  {readPreference: 'secondaryPreferred'})
            await reservations.forEachAsync(res => results.push(res));
        }
        return results
    }

    /**
     * returns an array of ChildReservations
     *
     * @returns Array<ChildReservations>
     */
    static async matchChildToReservations() {
        const results = []
        const newResArray = await this.fetchAllReservationsStartingToday();
        const children = await this.getChildren(newResArray);
        const oldResArray = await this.getReservationsEndingTodayByChild(children);
        children.forEach(child => {
            results.push({
                child: child,
                newReservations: _.filter(newResArray, res => res.selectedPerson === child._id),
                oldReservations: oldResArray.length ? _.filter(oldResArray, res => res.selectedPerson === child._id) : []
            });
        });
        return results;
    }

    /**
     * returns an expired reservation that matches the default group id of the child or returns undefined
     *
     * @param childReservations
     * @returns Reservation | undefined
     */
    static hasOldResWithDefaultGroup(childReservations) {
        return childReservations.oldReservations.find(res => res.groupId === childReservations.child.defaultGroupId);
    }

    /**
     * returns the target group id based off matched reservation types or first new reservation.
     *
     * @param childReservations
     * @returns String
     */
    static getTargetGroupId(childReservations) {
        const matchedOldGroup = this.hasOldResWithDefaultGroup(childReservations);
        if (matchedOldGroup) {
            const matchedType = childReservations.newReservations.find(res => res.scheduleType === matchedOldGroup.scheduleType)
            return matchedType ? matchedType.groupId : childReservations.newReservations[0].groupId;
        }
        // if no matching old reservations we'll just set default group to the first new schedule group
        return childReservations.newReservations[0].groupId;
    }

    /**
     * syncronizes children's default group id with new schedules starting today.
     *
     */
    static async runGroupSyncJob() {
        Log.info('Starting daily group sync job');
        const childReservationsArray = await this.matchChildToReservations();
        for(const childReservations of childReservationsArray) {
            const currentGroupId = childReservations.child.defaultGroupId;
            const targetGroupId = this.getTargetGroupId(childReservations);
            //if no default exists or the target is different from current default -- update child
            if (!currentGroupId || currentGroupId !== targetGroupId) {
                try {
                    await this.setChildDefaultGroup(childReservations.child._id, targetGroupId);
                } catch (e) {
                    Log.error(`Daily Group Sync: Error updating default group for child ${childReservations.child._id}: ${e.message || e.reason}`);
                }
            }
        };
        Log.info('Daily group sync completed');
    }

    /**
     * Executes the weekly group synchronization job for organizations across different timezones.
     * This method orchestrates several steps to synchronize group settings based on reservations:
     * 1. Retrieves organizations where a specific customization is enabled and groups them by timezone.
     * 2. For each group of organizations, it generates a query for weekly reservations within their respective timezone.
     * 3. Fetches reservations for these organizations and continues only if reservations exist.
     * 4. Applies group mappings to these reservations.
     * 5. Retrieves children linked to these reservations and maps these children to their respective reservations.
     * 6. Updates children's default group IDs based on their mapped reservations.
     * 7. Logs errors if any updates fail and provides a summary of operations per timezone and overall.
     *
     * @param {boolean} [nextWeek=false] - If true, generate the query for the next week; otherwise, for the current week.
     *
     * The method utilizes logging extensively to provide real-time feedback on its operation, facilitating debugging and monitoring.
     */
    static async runWeeklyGroupSyncJob(nextWeek = false) {
        Log.info('Weekly Group Sync: Starting weekly group sync job');
        // Find orgs with customization
        const orgs = await this.getWeeklyGroupSyncOrgs();
        // Group by timezone
        const orgsByTimezone = _.groupBy(orgs, 'timezone');

        for (const timezone in orgsByTimezone) {
            Log.info(`Weekly Group Sync: Processing orgs in timezone: ${timezone}`);
            // Generate weekly reservation range
            const query = this.generateWeeklyQuery(orgsByTimezone, timezone, nextWeek);
            const errors = [];

            // Find all reservations within range for those orgs
            const reservations = await this.getWeeklyReservations(query);

            // Exit early if no reservations
            if (!reservations.length) {
                Log.info(`Weekly Group Sync: No reservations found for this week for orgs in ${timezone} timezone`);
                continue;
            }

            // Map schedule type to reservation
            const reservationsWithGroupMapping = await this.mapDefaultGroupToReservations(reservations, errors);

            if (!reservationsWithGroupMapping.length) {
                Log.info(`Weekly Group Sync: No reservations found with mapped groups for this week for orgs in ${timezone} timezone`);
                continue;
            }

            // Find all children associated with those reservations
            const children = await this.getWeeklyChildren(reservationsWithGroupMapping);

            Log.info(`Weekly Group Sync: Found ${children.length} children for orgs in ${timezone} timezone`);

            // Map children to reservations
            const childReservations = this.mapChildToReservations(children, reservationsWithGroupMapping);

            // Update default group id of children based on reservations
            await this.updateDefaultGroupIds(childReservations, errors);

            // Log errors
            await this.processErrors(errors);

            Log.info(`Weekly Group Sync: Completed processing orgs in ${timezone} timezone`);
        }
        Log.info('Weekly Group Sync: Completed weekly group sync job');
    }

    /**
     * Retrieves organizations that have the "updateDefaultGroupBySchedule" customization enabled.
     * This method queries the "Orgs" collection for organizations with the specific customization
     * flag set to true. It fetches only the `_id` and `timezone` fields of matching organizations
     * and prefers reading from a secondary database replica if available.
     *
     * @returns {Array<Object>} An array of organization objects, each containing only the `_id` and `timezone`.
     *                          Returns an empty array if no organizations match the criteria or in case of an error.
     * @throws {Error} Logs an error message if the fetching process encounters an error.
     */
    static async getWeeklyGroupSyncOrgs() {
        try {
            return await Orgs.find({"customizations.people/updateDefaultGroupBySchedule/enabled": true }, {readPreference: 'secondaryPreferred', fields: {_id: 1, timezone: 1}}).fetchAsync();
        } catch (e) {
            Log.error(`Error fetching orgs: ${e.message || e.reason}`);
        }
    }

    /**
     * Generates a MongoDB query object for fetching weekly reservations based on the specified timezone.
     * This method constructs a query to find reservations within the current week for organizations
     * grouped by a given timezone. It filters out cancelled reservations and includes only those with a defined schedule type.
     *
     * @param {Object} orgsByTimezone - A dictionary where keys are timezone strings and values are arrays of organization objects.
     * @param {string} timezone - The timezone to consider for determining the start and end of the week.
     * @param {boolean} [nextWeek=false] - If true, generate the query for the next week; otherwise, for the current week.
     * @returns {Object} A MongoDB query object with criteria to find relevant reservations. The query includes:
     *                   - `orgId` to filter documents belonging to the specified organizations.
     *                   - `scheduledDate` to include only those within the current week as per the timezone.
     *                   - `reservationType` set to 'person' to filter specific types of reservations.
     *                   - `cancellationDate` to exclude any reservations that have been cancelled.
     *                   - `scheduleType` to ensure the reservation has an associated schedule.
     */
    static generateWeeklyQuery(orgsByTimezone, timezone, nextWeek = false) {
        const momentNow = moment.tz(timezone);
        const startOfWeek = nextWeek ? momentNow.add(1, 'weeks').startOf('week').valueOf() : momentNow.startOf('week').valueOf();
        const endOfWeek = nextWeek ? momentNow.add(1, 'weeks').endOf('week').valueOf() : momentNow.endOf('week').valueOf();

        const orgIds = orgsByTimezone[timezone].map(org => org._id);

        return {
            orgId: { $in: orgIds },
            scheduledDate: { $gte: startOfWeek, $lte: endOfWeek },
            reservationType: 'person',
            cancellationDate: { $not: { $exists: true } },
            scheduleType: { $nin: [null, '']}
        };
    }

    /**
     * Retrieves weekly reservations based on the provided query. This method queries the
     * Reservations collection, utilizing a read preference of 'secondaryPreferred' to distribute
     * the database load. It's designed to fetch data based on a structured query typically
     * generated by generateWeeklyQuery or similar logic.
     *
     * @param {Object} query - A MongoDB query object that defines the criteria used to fetch reservations.
     * @returns {Array<Object>} An array of reservation documents that match the query. If an error occurs,
     *                          the function may return undefined, and the error is logged.
     * @throws {Error} Logs an error message if the fetching process encounters an error, such as
     *                 a network issue or a malformed query.
     */
    static async getWeeklyReservations(query) {
        try {
            return await Reservations.find(query, { readPreference: 'secondaryPreferred' }).fetchAsync();
        } catch (e) {
            Log.error(`Error fetching reservations: ${e.message || e.reason}`);
        }
    }

    /**
     * Maps default group IDs to each reservation based on the schedule type defined in the organization's settings.
     * This method iterates through an array of reservation objects, retrieves organization details based on the reservation's
     * organization ID, and applies a default group ID from the organization's schedule type settings if available.
     *
     * The function modifies the original reservations by adding a `groupId` if a matching schedule type with a default
     * group ID is found in the organization's configuration. It then returns only those reservations that have a
     * `groupId` assigned.
     *
     * @param {Array<Object>} reservations - An array of reservation objects, each containing at least `orgId` and `scheduleType`.
     * @param {Array<Object>} errors - An array of captured errors.
     * @returns {Array<Object>} An array of modified reservation objects where a `groupId` has been successfully mapped.
     *                          Reservations without a matching `groupId` are filtered out.
     */
    static async mapDefaultGroupToReservations(reservations, errors) {
        for (const reservation of reservations) {
            const org = await Orgs.findOneAsync({_id: reservation.orgId});
            const matchedType = org.valueOverrides?.scheduleTypes?.find(type => type._id === reservation.scheduleType) || null;
            if (matchedType && matchedType.defaultGroupId) {
                reservation.groupId = matchedType.defaultGroupId;
            }

            if (!reservation.groupId) {
                const childReservations = reservations.filter(res => res.selectedPerson === reservation.selectedPerson);
                errors.push({ childId: reservation.selectedPerson, reservations: childReservations, error: `No default group found for schedule type ${reservation.scheduleType}` });
            }
        }

        return reservations.filter(res => res.groupId);
    }

    /**
     * Retrieves child records from the People collection based on a set of reservation IDs. This method
     * is designed to fetch only active child records whose IDs are specified in the given reservations.
     * It uses a MongoDB query to filter children by their IDs and active status. Additionally, the query
     * preference is set to 'secondaryPreferred' to potentially enhance read performance by utilizing secondary replicas.
     *
     * @param {Array<Object>} reservations - An array of reservation objects, each expected to contain a `selectedPerson` field
     *                                       which holds the child's ID.
     * @returns {Array<Object>} An array of People documents that represent the children involved in the provided reservations.
     *                          Returns an empty array if no valid IDs are found or in case of an error.
     * @throws {Error} If an error occurs during the database fetch operation, it logs the error and could also handle it by
     *                 returning an empty array or null, depending on the implementation.
     */
    static async getWeeklyChildren(reservations) {
        const childIds = reservations.map(res => res.selectedPerson);
        try {
            return await People.find({_id: { $in: childIds }, inActive: false}, {readPreference: 'secondaryPreferred'}).fetchAsync();
        } catch (e) {
            Log.error(`Error fetching children: ${e.message || e.reason}`);
        }
    }

    /**
     * Maps each child to their corresponding reservations based on a matching identifier.
     * This method iterates through an array of children and for each child, filters an array of reservations
     * to find those that are associated with the child (via `selectedPerson`). The result is an array of objects,
     * each containing a child object and an array of reservations specific to that child.
     *
     * @param {Array<Object>} children - An array of child objects. Each child should have an `_id` property.
     * @param {Array<Object>} reservations - An array of reservation objects. Each reservation should include a
     *                                       `selectedPerson` property that corresponds to a child's `_id`.
     * @returns {Array<Object>} An array containing objects with two properties: `child` and `reservations`.
     *                          The `child` property holds the child object, and `reservations` is an array
     *                          of all reservations associated with that child.
     */
    static mapChildToReservations(children, reservations) {
        return children.map(child => {
            const childReservations = reservations.filter(res => res.selectedPerson === child._id);
            return {
                child,
                reservations: childReservations
            };
        });
    }

    /**
     * Updates the default group ID for each child based on their reservations.
     * This method iterates through a list of children with their associated reservations,
     * sets the child's default group ID to the group ID of their first reservation, unless
     * multiple conflicting group IDs are found across the child's reservations. It handles
     * and logs errors during the update process and collects any issues encountered into an
     * error list.
     *
     * @param {Array<Object>} childReservations - An array of objects, each containing a `child` object with a `defaultGroupId`
     *                                            and an array of `reservations`. Each reservation must have a `groupId`.
     * @param {Array<Object>} errors - An array of captured errors.
     */
    static async updateDefaultGroupIds(childReservations, errors) {
        for (const child of childReservations) {
            const currentGroupId = child.child.defaultGroupId;
            const targetGroupId = child.reservations[0].groupId;
            const uniqueGroupIds = [...new Set(child.reservations.map(res => res.groupId))];
            if (uniqueGroupIds.length > 1) {
                errors.push({ childId: child.child._id, reservations: child.reservations, error: 'Multiple conflicting reservations found for child, using first found reservation only' })
            }
            if (!currentGroupId || currentGroupId !== targetGroupId) {
                try {
                    await this.setChildDefaultGroup(child.child._id, targetGroupId);
                } catch (e) {
                    Log.error(`Error updating default group for child ${child.child._id}: ${e.message || e.reason}`);
                    errors.push({ childId: child.child._id, reservations: child.reservations, error: e.message || e.reason });
                }
            }
        }
    }

    static async processErrors(errors) {
        if (errors.length) {
            Log.error(`Weekly Group Sync: ${errors.length} errors encountered during processing`);

            // Base columns that will always be present
            const baseColumns = [
                'Child First Name',
                'Child Last Name',
                'Org Name',
                'Org Long Name',
                'Current Default Group'
            ];

            let maxReservations = 0;
            // Determine the maximum number of reservations
            errors.forEach(error => {
                if (error.reservations && error.reservations.length > maxReservations) {
                    maxReservations = error.reservations.length;
                }
            });

            // Dynamically create columns based on the maximum number of reservations
            const columns = [...baseColumns];
            for (let i = 0; i < maxReservations; i++) {
                columns.push(`Schedule Type ${i+1}`);
                columns.push(`Scheduled Start Date ${i+1}`);
                columns.push(`Scheduled End Date ${i+1}`);
                columns.push(`Schedule Group ${i+1}`);
            }

            columns.push('Error Message');

            const data = [];

            for (const error of errors) {
                const child = await People.findOneAsync({ _id: error.childId });

                if (!child) {
                    Log.error(`Weekly Group Sync: Child not found for error: ${JSON.stringify(error)}`);
                    continue;
                }

                const org = await Orgs.findOneAsync({ _id: child.orgId });

                if (!org) {
                    Log.error(`Weekly Group Sync: Org not found for child ${child?._id}`);
                    continue;
                }

                // Gather all the groups we will need so we can display name instead of just the ID.
                const groupIds = error.reservations.map(res => res.groupId);
                if (child?.defaultGroupId && !groupIds.includes(child.defaultGroupId)) {
                    groupIds.push(child.defaultGroupId);
                }
                const groups = await Groups.find({ _id: { $in: groupIds } }).fetchAsync();

                // Gather all the schedule Types as well and reference them against the org object.
                const scheduleTypeIds = error.reservations.map(res => res.scheduleType);
                const scheduleTypes = org.valueOverrides?.scheduleTypes?.filter(type => scheduleTypeIds.includes(type._id)) || [];

                const timezone = org?.timezone || 'America/New_York'
                // Start with base data for the child
                const rowData = [
                    child?.firstName ?? 'Unknown',
                    child?.lastName ?? 'Unknown',
                    org?.name ?? 'Unknown',
                    org?.longName ?? 'Unknown',
                    groups.find(group => group._id === child?.defaultGroupId)?.name ?? 'Unknown'
                ];

                // Add reservation data
                error.reservations.forEach(reservation => {
                    rowData.push(scheduleTypes.find(type => type._id === reservation.scheduleType)?.type ?? 'Unknown');
                    rowData.push(moment.tz(reservation.scheduledDate, timezone).format('MM/DD/YYYY') ?? 'Unknown');
                    rowData.push(reservation.scheduledEndDate ? moment.tz(reservation.scheduledEndDate, timezone).format('MM/DD/YYYY') : 'N/A');
                    rowData.push(groups.find(group => group._id === reservation.groupId)?.name ?? 'Unknown');
                });

                // Fill in the blanks if not all reservations slots are used
                const reservationDataCount = error.reservations.length * 4;
                for (let i = reservationDataCount; i < maxReservations * 4; i++) {
                    rowData.push('N/A'); // Filling empty reservation data
                }

                rowData.push(error.error);

                data.push(rowData);
            }

            const dateString = moment().format('MM/DD/YYYY');
            let filename = 'group_sync_error_' + dateString.replace(/\//g, '-');

            const content = Papa.unparse({ fields: columns, data });
            const attachments = [{ filename: filename + '.csv', content }];
            const groupSyncErrorsEmail = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.GROUP_SYNC_ERRORS_EMAIL);

            await EmailService.sendSystemAdminEmail(
                undefined,
                'systemNotificationEmail',
                'email_templates/system_notification_email.html',
                '<<EMAIL>>',
                '<EMAIL>',
               // groupSyncErrorsEmail,
                'Weekly Group Sync failures for date ' + dateString,
                { bodyMessage: 'Please find a list of all errors encountered during this week’s group sync.' },
                { attachments: attachments }
            );
        }
    }
}
    