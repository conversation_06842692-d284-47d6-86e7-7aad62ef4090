import { Meteor } from 'meteor/meteor';
import { Reservations } from '../../lib/collections/reservations';
import { People } from '../../lib/collections/people';
import { Orgs } from '../../lib/collections/orgs';
import moment from 'moment-timezone';

export class ReservationService {
    constructor(reservationsCollection = Reservations, peopleCollection = People, orgsCollection = Orgs) {
        this.reservations = reservationsCollection;
        this.people = peopleCollection;
        this.orgs = orgsCollection;
    }

    async cancelReservation(reservationId, cancellationReason, cancellationComment, cancelledByUserId, occurrenceTime = null) {
        const reservation = await this.reservations.findOneAsync({ _id: reservationId });
        if (!reservation) {
            throw new Meteor.Error(404, 'Reservation not found');
        }

        const org = await this.orgs.findOneAsync({ _id: reservation.orgId });
        const timezone = org.getTimezone();

        if (occurrenceTime && reservation.recurringFrequency) {
            // Handle specific occurrence of recurring reservation
            const reservationData = await this.reservations.findOneAsync(
                { _id: reservationId },
                {
                    fields: {
                        recurringFrequency: 0,
                        recurringType: 0,
                        recurringDays: 0,
                        recurringExceptions: 0
                    }
                }
            );

            delete reservationData._id;
            reservationData.scheduledDate = occurrenceTime;
            reservationData.cancellationReason = cancellationReason;
            reservationData.cancellationComments = cancellationComment;
            reservationData.cancellationDate = Date.now();
            reservationData.cancellationOriginalReservationId = reservationId;
            reservationData.createdBy = "SYSTEM-CANCEL-RECURRING-INSTANCE";
            reservationData.createdAt = Date.now();
            reservationData.cancelledBy = cancelledByUserId;

            await this.reservations.updateAsync(
                reservationId,
                { $push: { recurringExceptions: occurrenceTime } }
            );
            await this.reservations.insertAsync(reservationData);
        } else if (reservation.recurringFrequency) {
            // Handle recurring reservation
            const reservationData = await this.reservations.findOneAsync(
                { _id: reservationId },
                {
                    fields: {
                        recurringFrequency: 0,
                        recurringType: 0,
                        recurringDays: 0,
                        recurringExceptions: 0
                    }
                }
            );

            delete reservationData._id;
            reservationData.scheduledDate = new moment().tz(timezone).startOf('day').valueOf();
            reservationData.cancellationReason = cancellationReason;
            reservationData.cancellationComments = cancellationComment;
            reservationData.cancellationDate = Date.now();
            reservationData.cancellationOriginalReservationId = reservationId;
            reservationData.createdBy = "Mobile Cancellation";
            reservationData.createdAt = Date.now();
            reservationData.cancelledBy = cancelledByUserId;

            await this.reservations.updateAsync(
                reservationId,
                { $push: { recurringExceptions: reservationData.scheduledDate } }
            );
            await this.reservations.insertAsync(reservationData);
        } else {
            // Handle non-recurring reservation
            const updateData = {
                cancellationReason: cancellationReason,
                cancellationComments: cancellationComment,
                cancellationDate: Date.now(),
                cancelledBy: cancelledByUserId
            };

            if (reservation.cancellationReason) {
                updateData.updatedBy = cancelledByUserId;
                updateData.updatedAt = Date.now();
            } else {
                updateData.createdBy = cancelledByUserId;
                updateData.createdAt = Date.now();
            }

            await this.reservations.updateAsync(
                { _id: reservationId },
                { $set: updateData }
            );

            if (reservation.recurrenceId) {
                await this.reservations.updateAsync(
                    { _id: reservation.recurrenceId },
                    { $push: { recurringExceptions: reservation.scheduledDate } }
                );
            }
        }

        return true;
    }

    async handleFamilyCheckinCancellation(personId, checkInData, cancelledByUserId) {
        const org = await this.orgs.findOneAsync({ _id: checkInData.orgId });
        const timezone = org.getTimezone();
        const today = new moment.tz(timezone).startOf('day').valueOf();

        const existingReservations = await this.reservations.findAsync({
            personId: personId,
            scheduledDate: today,
            cancellationDate: { $exists: false }
        });

        if (!existingReservations.length) {
            return false;
        }

        for (const existingReservation of existingReservations) {
            await this.cancelReservation(
                existingReservation._id,
                checkInData.absentReason || 'Mobile Cancellation',
                checkInData.absentComment,
                cancelledByUserId
            );
        }

        return true;
    }
} 