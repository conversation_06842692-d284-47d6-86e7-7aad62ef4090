import moment from "moment";
import { Invoices } from "../../lib/collections/invoices";
import { Moments } from "../../lib/collections/moments";
import { Orgs } from "../../lib/collections/orgs";
import { People } from "../../lib/collections/people";
import { TimeCards } from "../../lib/collections/timeCards";
import { UserInvitations } from "../../lib/collections/userInvitations";

export class BenchmarkService {

    /**
     * @param orgId
     * @returns {Promise<{"Media Created": number, "Autopay Parents": number, Invites: number, Engagements: number, "Live Orgs": number, "Payments Amount": number, "Subsidies Count": number, "Root Name": *, Checkins: number, "Subsidies Amount": number, "Moments Created": number, "Staff Hours": number, "Payments Count": number}>}
     */
    static async getStats(orgId) {
        let orgs = await this.getOrgs(orgId);
        // If there are no orgs, we can't get any stats, so return this generic object.
        if (!orgs || orgs.length < 1) {
            return {
                'Root Name': 'No Orgs Found',
                'Live Orgs': 0,
                'Invites': 0,
                'Media Created': 0,
                'Moments Created': 0,
                'Engagements': 0,
                'Checkins': 0,
                'Payments Count': 0,
                'Payments Amount': 0,
                'Staff Hours': 0,
                'Subsidies Count': 0,
                'Subsidies Amount': 0,
                'Autopay Parents': 0
            };
        }
        const orgIds = orgs.map(org => org._id);
        // We are done with this array of objects, so let's clean up some memory.
        orgs = null;

        const paymentsInfo = await this.getPayments(orgIds);
        const subsidiesInfo = await this.getSubsidies(orgIds);
        return {
            'Root Name': await Orgs.findOneAsync({_id: orgId}).name,
            'Live Orgs': orgIds.length,
            'Invites': await this.getInvitesCount(orgIds),
            'Media Created': await this.getMediaCount(orgIds),
            'Moments Created': await this.getMomentsCount(orgIds),
            'Engagements': await this.getEngagements(orgIds),
            'Checkins': await this.getCheckinsCount(orgIds),
            'Payments Count': paymentsInfo.count,
            'Payments Amount': paymentsInfo.amount,
            'Staff Hours': await this.getStaffHours(orgIds),
            'Subsidies Count': subsidiesInfo.count,
            'Subsidies Amount': subsidiesInfo.amount,
            'Autopay Parents': await this.getAutopayParents(orgIds),
        }
    }

    static async findAllOrgs(rootOrgId) {
        const rootOrg = await Orgs.findOneAsync({_id: rootOrgId});
        const all = [];
        let currentLayer = [rootOrg];
        let safety = 0;
        while (currentLayer.length > 0 && safety < 20) {
            safety++;
            all.push(...currentLayer);
            currentLayer = await Orgs.find({parentOrgId: {$in: currentLayer.map(org => org._id)}}).fetchAsync();
        }
        return all;
    }

    /**
     * @param rootOrgId
     * @returns {Array<Org>}
     */
    static async getOrgs(rootOrgId) {
        const rootOrg = await Orgs.findOneAsync(rootOrgId);
        if (!rootOrg) {
            // The root org doesn't exist, so we can't get any orgs, so return this empty array.
            return [];
        }

        const relatedOrgIds = await rootOrg.findAllRelatedOrgIds();
        const relatedOrgs = await Orgs.find({ _id: { $in: relatedOrgIds } }).fetchAsync();
        return relatedOrgs.filter(org => !org.inactive && !org.name.match(/Master|Template|Sandbox/));
    }

    /**
     *
     * @param orgIds
     * @returns {number}
     */
    static async getInvitesCount(orgIds) {
        return await UserInvitations.find({ orgId: { $in: orgIds } }).countAsync();
    }

    /**
     * @param orgIds
     * @returns {Promise<number>}
     */
    static async getMediaCount(orgIds) {
        const mediaFilesCount = await (await Moments.aggregate(
            [
                { $match: { orgId: { $in: orgIds } , momentType: { $nin: ['doorUnlocked', 'checkin', 'checkout']}, mediaFiles: {$exists: true}} },
                {
                    $group: {
                        _id: null,
                        count: {
                            $sum: {
                                $cond: {
                                    if: { $isArray: "$mediaFiles" },
                                    then: { $size: "$mediaFiles" },
                                    else: 0
                                }
                            }
                        }
                    }
                }
            ]
        )).toArray();
        return mediaFilesCount.length > 0 ? mediaFilesCount[0].count : 0;
    }

    /**
     * @param orgIds
     * @returns {number}
     */
    static async getMomentsCount(orgIds) {
        return await Moments.find({ orgId: { $in: orgIds } , momentType: {$nin: ['doorUnlocked', 'checkin', 'checkout']}}).countAsync();
    }

    /**
     * @param orgIds
     * @returns {number}
     */
    static async getCheckinsCount(orgIds) {
        return await Moments.find({ orgId: { $in: orgIds } , momentType: 'checkin'}).countAsync();
    }

    /**
     * @param orgIds
     * @returns {Promise<{amount: number, count: number}>}
     */
    static async getSubsidies(orgIds) {
        const subsidiesAgg = await (await Invoices.aggregate([
            { $match: { orgId: { $in: orgIds } } },
            {
                $unwind: {
                    path: "$credits",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $match: {
                    "credits.type": "credit",
                    "credits.creditReason": "reimbursable"
                }
            },
            {
                $group: {
                    _id: null,
                    count: { $sum: 1 },
                    amount: { $sum: "$credits.amount" }
                }
            },
        ])).toArray();
        console.log({subsidiesAgg});
        return subsidiesAgg.length > 0 ? subsidiesAgg[0] : { count : 0, amount: 0 };
    }

    /**
     *
     * @param orgIds
     * @returns {Promise<{amount: number, count: number}>}
     */
    static async getPayments(orgIds) {
        const paymentsAgg = await (await Invoices.aggregate([
            { $match: { orgId: { $in: orgIds } } },
            {
                $project: {
                    paymentTransactionsCount: { $cond: { if: { $isArray: "$paymentTransactions" }, then: { $size: "$paymentTransactions" }, else: 0 } },
                    totalAmount: {
                        $reduce: {
                            input: "$paymentTransactions",
                            initialValue: 0,
                            in: { $add: ["$$value", "$$this.totalAmount"] }
                        }
                    }
                }
            },
            {
                $group: {
                    _id: null,
                    count: { $sum: "$paymentTransactionsCount" },
                    amount: { $sum: "$totalAmount" }
                }
            }
        ])).toArray();
        return paymentsAgg.length > 0 ? paymentsAgg[0] : { count : 0, amount: 0 };
    }

    /**
     *
     * @param orgIds
     * @returns {Promise<number>}
     */
    static async getStaffHours(orgIds) {
        const timecards = await TimeCards.find({ orgId: { $in: orgIds } }, {noCursorTimeout: true}).fetchAsync();
        let total = 0;
        for (const timecard of timecards) {
            const start = new moment(timecard.checkInDate + ' ' + timecard.checkInTime);
            const end = new moment(timecard.checkOutDate + ' ' + timecard.checkOutTime);
            total += end.diff(start, 'hours');
        }
        return total;
    }

    /**
     *
     * @param orgIds
     * @returns {Promise<number>}
     */
    static async getAutopayParents(orgIds) {
        return await People.find({
            orgId: { $in: orgIds },
            type: 'family',
            'billing.autoPay': {$exists: true},
            inActive: { $in: [null, false]}
        }).countAsync();
    }

    /**
     * @param orgIds
     * @returns {Promise<number>}
     */
    static async getEngagements(orgIds) {
        const engagementsCount = await (await Orgs.aggregate(
            [
                { $match: { _id: { $in: orgIds } } },
                {
                    $unwind: {
                        path: '$engagementCounts',
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $group: {
                        _id: null,
                        count: {
                            $sum: {
                                $add: [
                                    { $ifNull: ['$engagementCounts.providerCount', 0] },
                                    { $ifNull: ['$engagementCounts.familyCount', 0] },
                                ]
                            }
                        }
                    }
                }
            ]
        )).toArray();
        console.log(engagementsCount);
        return engagementsCount.length > 0 ? engagementsCount[0].count : 0;
    }
}
