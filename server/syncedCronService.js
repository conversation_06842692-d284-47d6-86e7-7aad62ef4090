import moment from 'moment-timezone';
import { Log } from '../lib/util/log';
import { AvailableTimezoneMap } from '../lib/constants/timezoneConstants';
import { SummaryEmailDeferrals } from './collections/summaryEmailDeferrals';
import { People } from '../lib/collections/people';
import { processSummaryMail2021 } from './emails/v2021/processSummaryMail2021';
import { Orgs } from '../lib/collections/orgs';
import { Relationships } from '../lib/collections/relationships';

export class SyncedCronService {
    static async cleanupPlaceholderGuardians() {
        const oneHourAgo = moment().subtract(1, 'hours').toDate();
        const oneDayAndFewHoursAgo = moment().subtract(28, 'hours').toDate();

        const people = await People.find({
            placeholderGuardian: true,
            createdAt: { $lt: oneHourAgo, $gt: oneDayAndFewHoursAgo }
        }).fetchAsync();

        for ( const person of people ) {
            // Check if there is at least one relationship for this person
            const relationshipExists = await Relationships.findOneAsync({
                personId: person._id
            });

            if (!relationshipExists) {
                // If no relationship is found, delete the person
                await People.updateAsync({ _id: person._id });
            } else {
                // If at least one relationship is found, remove the placeholderGuardian field
                await People.updateAsync({ _id: person._id }, { $unset: { placeholderGuardian: 1 } });
            }
        };
    }

    /**
     * Processes summary email deferrals for a given organization.
     * It retrieves unprocessed summary email deferrals for the specified organization and attempts to send the summary emails.
     * Each deferral is marked as processed after the email is sent.
     *
     * @param {string} orgId - The ID of the organization for which to process email deferrals.
     */
    static async processSummaryEmailDeferrals(orgId) {
        if (!orgId) {
            Log.error('orgId is required for processSummaryEmailDeferrals');
            return;
        }

        const query = {
            orgId,
            processed: false,
        };

        const deferrals = await SummaryEmailDeferrals.find(query).fetchAsync();

        Log.info(`Processing ${deferrals.length} summary email deferrals for org ${orgId}`);
        for (const deferral of deferrals) {
            const { personId, summaryCC } = deferral;

            if (!personId) {
                Log.error('personId is required for processSummaryEmailDeferrals');
                continue;
            }

            try {
                await processSummaryMail2021(personId, summaryCC, true);
                await SummaryEmailDeferrals.updateAsync({ _id: deferral._id }, { $set: { processed: true, processedAt: new Date().getTime() } });
            } catch (e) {
                Log.error(`Error processing summary email deferral for person ${personId}: ${e}`);
            }
        }
        Log.info(`Finished processing summary email deferrals for org ${orgId}`);
    }

    /**
     * Processes email deferrals for organizations that do not auto checkout but defer emails,
     * specifically at 9 PM in their respective time zones.
     *
     * This method checks if it is 9 PM in any of the available time zones. If it is,
     * it retrieves all organizations in that timezone where:
     * 1. Auto checkout (`customizations.moments/checkin/autocheckout`) is either not set or is `false`.
     * 2. Email deferral on checkout (`customizations.moments/postWhenCheckedOut/enabled`) is enabled.
     *
     * For each organization that matches the criteria, the summary email deferral process is triggered.
     *
     * @static
     * @memberof SyncedCronService
     * @returns {void}
     */
    static async processNonAutoCheckoutEmailDeferrals() {
        const its9pmSomewhere = this.is9PMInAvailableTimezones();
        if (!its9pmSomewhere) {
            Log.info('It is not 9 PM in any of the available timezones');
            return;
        }

        // It's 9 PM in at least one timezone, get all orgs that defer emails but don't auto checkout.
        const query = {
            timezone: its9pmSomewhere,
            inactive: { $ne: true },
            $and: [
                {
                    $or: [
                        { "customizations.moments/checkin/autocheckout": { $exists: false } },
                        { "customizations.moments/checkin/autocheckout": false }
                    ]
                },
                {
                    "customizations.moments/postWhenCheckedOut/enabled": true
                }
            ]
        };

        const orgs = await Orgs.find(query).fetchAsync();

        if (!orgs.length) {
            Log.info(`No orgs found that defer emails but don't auto checkout in timezone ${its9pmSomewhere}`);
            return;
        }

        Log.info(`Processing non-auto checkout email deferrals for ${orgs.length} orgs in timezone ${its9pmSomewhere}`);

        for (const org of orgs) {
            await this.processSummaryEmailDeferrals(org._id);
        }
    }

    /**
     * Checks if the current time is 9 PM in any of the available timezones
     * and returns the timezone name if it is.
     *
     * @returns {string | null} - The name of the timezone where it is currently 9 PM, or null if none match.
     */
    static is9PMInAvailableTimezones() {
        const targetHour = 21; // 9 PM

        for (const [timezone, timezoneName] of Object.entries(AvailableTimezoneMap)) {
            const currentHour = moment.tz(timezone).hour();
            if (currentHour === targetHour) {
                return timezone;
            }
        }

        return null;
    }
}