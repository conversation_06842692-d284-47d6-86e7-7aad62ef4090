import { PermissionsRoles } from "../lib/collections/permissionsRoles";

Meteor.methods({
	async getRoles( options) {
		const user = await Meteor.userAsync();
		const userPerson = await user.fetchPerson();
		if (!userPerson.superAdmin) throw new Meteor.Error(403, "Not authorized");
		const query = {};
		if (options.roleId)
			query["_id"] = options.roleId;
		return await PermissionsRoles.find(query, {sort:{name:1}}).fetchAsync();
	},
	async insertRole(options) {
		const user = await Meteor.userAsync();
		const userPerson = await user.fetchPerson();
		if (!userPerson.superAdmin) throw new Meteor.Error(403, "Not authorized");
		if (!options.name) throw new Meteor.Error(500, "Invalid Role Name");
		const id = await PermissionsRoles.insertAsync({
			name: options.name,
			rules: []
		});
		return id;
	},
	async savePermissionsRole(options) {
		const user = await Meteor.userAsync();
		const userPerson = await user.fetchPerson();
		if (!userPerson.superAdmin) throw new Meteor.Error(403, "Not authorized");
		console.log("save role options", options)
		await PermissionsRoles.updateAsync({_id: options._id}, {"$set": {
			label: options.label,
			rules: options.rules,
			passthroughWithoutMatchingRules: options.passthroughWithoutMatchingRules,
			localAdminCanAssign: options.localAdminCanAssign
		}});
	}
});