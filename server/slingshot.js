import { Slingshot } from 'meteor/edgee:slingshot'

Slingshot.fileRestrictions("myFileUploads", {
  allowedFileTypes: ["image/png", "image/jpeg", "image/gif", "video/quicktime", "video/webm", "video/mp4", "video/mov", "video/3gp","video/3gpp"],
  maxSize: 1000 * 1024 * 1024 // 10 MB (use null for unlimited).
});

Slingshot.createDirective("myFileUploads", Slingshot.S3Storage, {
  bucket: "tendlymr",

  acl: "public-read",

  authorize: function () {
    //Deny uploads if user is not logged in.
    if (!this.userId) {
      var message = "Please login before posting files";
      throw new Meteor.Error("Login Required", message);
    }

    return true;
  },

  key: async function (file, metaContext) {
    //Store file into a directory by the user's username.
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = user.fetchPerson();
    var outputKey = "uploads/" + user.orgId + "/" + user._id + "/" + metaContext.tokenId;
    
    return outputKey;
  }
});

Slingshot.fileRestrictions("myDocumentUploads", {
  allowedFileTypes: ["application/pdf"],
  maxSize: 1000 * 1024 * 1024 // 10 MB (use null for unlimited).
});

Slingshot.createDirective("myDocumentUploads", Slingshot.S3Storage, {
  bucket: "tendlymr",

  acl: "public-read",

  authorize: function () {
    //Deny uploads if user is not logged in.
    if (!this.userId) {
      var message = "Please login before posting files";
      throw new Meteor.Error("Login Required", message);
    }

    return true;
  },

  key: async function (file, metaContext) {
    //Store file into a directory by the user's username.
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = user.fetchPerson();
    var outputKey = "uploads/" + user.orgId + "/" + user._id + "/" + metaContext.tokenId;
    
    return outputKey;
  }
});

Slingshot.fileRestrictions("myFileUploadsBase64", {
  allowedFileTypes: ["image/png", "image/jpeg", "image/gif", "video/quicktime", "video/webm", "video/mp4", "video/mov", "video/3gp","video/3gpp"],
  maxSize: 1000 * 1024 * 1024 // 10 MB (use null for unlimited).
});

Slingshot.createDirective("myFileUploadsBase64", Slingshot.S3Storage, {
  bucket: "tendlymr",
  acl: "public-read",

  authorize: function () {
    //Deny uploads if user is not logged in.
    if (!this.userId) {
      var message = "Please login before posting files";
      throw new Meteor.Error("Login Required", message);
    }

    return true;
  },

  key: async function (file, metaContext) {
    //Store file into a directory by the user's username.
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = user.fetchPerson();
    var outputKey = "uploadsbase64/" + user.orgId + "/" + user._id + "/" + metaContext.tokenId;
    
    return outputKey;
  }
});

Slingshot.fileRestrictions("myDocumentRepositoryUploads", {
  allowedFileTypes: ["application/pdf", "image/jpeg", "image/png", "image/tiff", "image/gif", "image/heif", "image/webp", "image/svg+xml", "image/bmp"],
  maxSize: 1000 * 1024 * 1024 // 10 MB (use null for unlimited).
});

Slingshot.createDirective("myDocumentRepositoryUploads", Slingshot.S3Storage, {
  bucket: "momentpathdocumentrepository",

  authorize: function () {
    //Deny uploads if user is not logged in.
    if (!this.userId) {
      var message = "Please login before posting files";
      throw new Meteor.Error("Login Required", message);
    }

    return true;
  },

  key: async function (file, metaContext) {
    //Store file into a directory by the user's username.
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = user.fetchPerson();
    var outputKey = user.orgId + "/" + user._id + "/" + metaContext.tokenId;
    
    return outputKey;
  }
});

Slingshot.fileRestrictions("myAnnouncementUploads", {
  allowedFileTypes: ["image/png", "image/jpeg", "image/gif", "video/quicktime", "video/webm", "video/mp4", "video/mov", "video/3gp", "video/3gpp", "application/pdf"],
  maxSize: 1000 * 1024 * 1024 // 10 MB (use null for unlimited).
});

Slingshot.createDirective("myAnnouncementUploads", Slingshot.S3Storage, {
  bucket: "tendlymr",

  acl: "public-read",

  authorize: function () {
    //Deny uploads if user is not logged in.
    if (!this.userId) {
      var message = "Please login before posting files";
      throw new Meteor.Error("Login Required", message);
    }

    return true;
  },

  key: async function (file, metaContext) {
    //Store file into a directory by the user's username.
    var user = await Meteor.users.findOneAsync(this.userId);
    var person = user.fetchPerson();
    var outputKey = "uploads/" + user.orgId + "/" + user._id + "/" + metaContext.tokenId;
    
    return outputKey;
  }
});

Slingshot.fileRestrictions("mySupportingDocumentUploads", {
  allowedFileTypes: ['application/pdf',
							'application/msword',
							'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
							'image/jpeg',
							'image/png',
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  maxSize: 1000 * 1024 * 1024 // 10 MB (use null for unlimited).
});

Slingshot.createDirective("mySupportingDocumentUploads", Slingshot.S3Storage, {
  bucket: "tendlymr",

  acl: "public-read",

  authorize: function () {
    //Deny uploads if user is not logged in.
    if (!this.userId) {
      var message = "Please login before posting files";
      throw new Meteor.Error("Login Required", message);
    }

    return true;
  },

  key: async function (file, metaContext) {
    //Store file into a directory by the user's username.
    var user = await Meteor.users.findOneAsync(this.userId);
    var outputKey = "uploads/" + user.orgId + "/" + user._id + "/" + metaContext.tokenId;
    
    return outputKey;
  }
});