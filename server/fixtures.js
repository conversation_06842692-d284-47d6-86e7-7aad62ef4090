import { Orgs } from "../lib/collections/orgs";
import { People } from "../lib/collections/people";

Meteor.startup(async function() {
  if (Orgs.find().count() === 0) {
    await Orgs.insertAsync({
      name: "Little Test Org"
    });
  }
  if (Meteor.users.find().count() === 0) {
    o = await Orgs.findOneAsync();
    personId = await People.insertAsync({
      type: "staff",
      firstName: 'Matt',
      lastName: 'Coffman',
      orgId: o["_id"]
    });
    id = await Accounts.createUserAsync({
      username: 'matt',
      email: '<EMAIL>',
      password: 'testing123',
      personId: personId,
      orgId: o["_id"]
    });
  }

});