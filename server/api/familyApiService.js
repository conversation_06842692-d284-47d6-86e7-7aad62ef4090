import {ApiMethods} from "../apiMethods";
import { DESIGNATION_WAIT_LIST } from '../../lib/constants/designationConstants';
import { ChildcareCrmUtil } from '../childcareCrmUtil';
import { HistoryAuditService } from '../historyAuditService';
import { HistoryAuditChangeTypes, HistoryAuditPeoplePerformedByNames } from '../../lib/constants/historyAuditConstants';
import { People } from "../../lib/collections/people";
import { Orgs } from "../../lib/collections/orgs";
import { Groups } from "../../lib/collections/groups";
import { Relationships } from "../../lib/collections/relationships";
const moment = require("moment-timezone");

export class FamilyApiService {
    /**
     * Add a family via the API. Structure expected:
     * {
     *     "org_id": string,
     *     "external_family_id": string,
     *     "external_center_id": string,
     *     "contacts": [
     *         {
     *             "is_primary": bool,
     *             "is_emergency": bool,
     *             "is_authorized_pickup": bool,
     *             "first_name": string,
     *             "last_name": string,
     *             "email": string,
     *             "phone": string,
     *             "address": string,
     *             "city": string,
     *             "state": string,
     *             "postcode": string,
     *             "external_guardian_id": string
     *         }
     *     ],
     *     "children": [
     *          {
     *              "first_name": string,
     *              "last_name": string,
     *              "dob": "YYYY-MM-DD",
     *              "gender": string,
     *              "classroom_id": string,
     *              "external_child_id": string
     *          }
     *     ]
     * }
     */
    async addFamily(req, res) {
        const apiMethods = new ApiMethods(req, res);
        const data = req.body;
        if (!data['org_id']) {
            apiMethods.setStatusCode(400);
            apiMethods.setResponse('Invalid data: org_id required', 400)
            return;
        }

        const org = await Orgs.findOneAsync(data['org_id']);
        if (!org) {
            apiMethods.setStatusCode(400);
            apiMethods.setResponse('Invalid data: org not found', 400);
            return;
        }
        const prefix = org.profileDataPrefix();

        if (!data['contacts'].length) {
            apiMethods.setStatusCode(400);
            apiMethods.setResponse('Invalid data: No contacts provided', 400);
            return;
        }

        const now = new Date();

        // Add children first
        const childIds = [];
        for (const [index, child] of data['children'].entries()) {
            const person = await People.findOneAsync({
                "childcareCrm.childId": child['external_child_id'],
                "orgId": org._id,
                "type": "person"
            });
            if (person) {
                apiMethods.setStatusCode(409);
                apiMethods.setResponse(`Invalid data: child ${child['external_child_id']} already exists`, 409);
                return;
            }
            if (child['external_child_id'] && !data['external_family_id']) {
                apiMethods.setStatusCode(400);
                apiMethods.setResponse('No external_family_id provided', 400);
                return;
            }
            const birthday = new moment.tz(child['dob'] + ' 12:00:00', "YYYY-MM-DD H:i:s", org.getTimezone()).valueOf();
            const profileFields = {
                gender: data['gender'],
                birthday
            }
            const newChild = {
                orgId: org._id,
                firstName: child['first_name'],
                lastName: child['last_name'],
                createdBy: 'API',
                createdAt: now.getTime(),
                type: 'person',
                crmStatusId: child['status_id'],
                inActive: false,
            };
            if (prefix) {
                newChild[prefix] = profileFields;
            } else {
                Object.assign(newChild, profileFields);
            }

            ChildcareCrmUtil.setDesignation(newChild, child['status_id'], child, org, DESIGNATION_WAIT_LIST);

            if (child['external_child_id']) {
                newChild.childcareCrm = {
                    childId: child['external_child_id'],
                    familyId: data['external_family_id'],
                    centerId: data['external_center_id']
                }
            }

            if (child['classroom_id']) {
                const group = await Groups.findOneAsync({ _id: child['classroom_id'] });
                if (group) {
                    newChild.defaultGroupId = group._id;
                }
            }
            const personId = await People.insertAsync(newChild, function(error) {
                if (error) {
                    apiMethods.setStatusCode(500);
                    apiMethods.setResponse(error.message, 500);
                }
            });
            childIds.push(personId);
            data['children'][index].personId = personId;
            await HistoryAuditService.logPersonChange({
                changeType: HistoryAuditChangeTypes.ADD,
                performedByName: HistoryAuditPeoplePerformedByNames.UNIFIED_INTEGRATION,
                previousState: null,
                currentState: { ...newChild, _id: personId },
            });
        }
        const contactIds = [];
        for (const [index, contact] of data['contacts'].entries()) {
            if (contact['external_guardian_id']) {
                const person = await People.findOneAsync({
                    "childcareCrm.guardianId": contact['external_guardian_id'],
                    "orgId": org._id,
                    "family": "family"
                })
                if (person) {
                    apiMethods.setStatusCode(400);
                    apiMethods.setResponse(`Invalid data: person ${contact['external_id']} already exists`, 409);
                    return;
                }
            }

            const profileFields = {
                householdInformation:
                    {
                        parentStreetAddress: contact['address'],
                        parentCity: contact['city'],
                        parentState: contact['state'],
                        parentZip: contact['postcode']
                    },
                phonePrimary: contact['phone']
            }

            const newPerson = {
                orgId: org._id,
                firstName: contact['first_name'],
                lastName: contact['last_name'],
                profileEmailAddress: contact['email'],
                createdBy: 'API',
                createdAt: now.getTime(),
                type: 'family',
                inActive: false,
                childcareCrm: {
                    guardianId: contact['external_guardian_id'],
                    familyId: data['external_family_id'],
                    centerId: data['external_center_id']
                }
            };
            if (contact['is_primary']) {
                newPerson.crmStatusId = data['status_id'];
            }
            if (prefix) {
                newPerson[prefix] = profileFields;
            } else {
                Object.assign(newPerson, profileFields);
            }
            // Create new person
            const personId = await People.insertAsync(newPerson, function(error) {
                if (error) {
                    apiMethods.setStatusCode(500);
                    apiMethods.setResponse(error.message);
                }
            });
            data['contacts'][index].personId = personId;
            contactIds.push({
                personId: personId,
                primary: contact['is_primary'],
                emergency: contact['is_emergency'],
                pickup: contact['is_authorized_picked']
            });
            await HistoryAuditService.logPersonChange({
                changeType: HistoryAuditChangeTypes.ADD,
                performedByName: HistoryAuditPeoplePerformedByNames.UNIFIED_INTEGRATION,
                previousState: null,
                currentState: { ...newPerson, _id: personId },
            });
        }
        for (const childId of childIds) {
            for (const contactInfo of contactIds) {
                const relationship = {
                    orgId: org._id,
                    personId: contactInfo.personId,
                    primaryCaregiver: contactInfo.primary,
                    relationshipType: contactInfo.pickup ? 'authorizedPickup' : 'family',
                    targetId: childId
                }
                await Relationships.insertAsync(relationship, function(error) {
                    if (error) {
                        apiMethods.setStatusCode(500);
                        apiMethods.setResponse(error.message);
                    }
                });
            }
        }

        apiMethods.setStatusCode(200);
        apiMethods.setResponse(data);
    }

    /**
     * Add a child to an existing family.
     * Adds the child person and gives it a relationship with all the given family members in family_ids
     * Expected structure:
     *
     * {
     *     "org_id": string,
     *     "external_family_id": string,
     *     "family_person_ids": [string, string], // personIds for family members to make relationships
     *     "first_name": string,
     *     "last_name": string,
     *     "dob": "YYYY-MM-DD",
     *     "gender": string,
     *     "classroom_id": string,
     *     "external_child_id": string,
     *     "external_center_id": string // necessary for the integration
     * }
     * @param request
     * @param response
     */
    async addChild(request, response) {
        const apiMethods = new ApiMethods(request, response);
        const data = request.body;
        if (!data['org_id']) {
            apiMethods.setStatusCode(400);
            apiMethods.setResponse('Invalid data: org_id required', 400)
            return;
        }
        const org = await Orgs.findOneAsync(data['org_id']);
        if (!org) {
            apiMethods.setStatusCode(400);
            apiMethods.setResponse('Invalid data: org not found', 400);
            return;
        }
        const prefix = org.profileDataPrefix();
        const person = await People.findOneAsync({
            "childcareCrm.childId": data['external_child_id'],
            "childcareCrm.familyId": data['external_family_id'],
            "orgId": org._id,
            "type": "person"
        });
        if (person) {
            apiMethods.setStatusCode(409);
            apiMethods.setResponse(`Invalid data: child ${data['external_child_id']} already exists`, 409);
            return;
        }
        const now = new Date();
        const birthday = new moment.tz(data['dob'] + ' 12:00:00', "YYYY-MM-DD H:i:s", org.getTimezone()).valueOf();
        const profileFields = {
            gender: data['gender'],
            birthday
        }
        const newChild = {
            orgId: org._id,
            firstName: data['first_name'],
            lastName: data['last_name'],
            createdBy: 'API',
            createdAt: now.getTime(),
            type: 'person',
            inActive: false,
            crmStatusId: data['status_id']
        };
        if (prefix) {
            newChild[prefix] = profileFields;
        } else {
            Object.assign(newChild, profileFields);
        }

        ChildcareCrmUtil.setDesignation(newChild, data['status_id'], data, org, DESIGNATION_WAIT_LIST);

        if (data['external_child_id']) {
            newChild.childcareCrm = {
                childId: data['external_child_id'],
                familyId: data['external_family_id'],
                centerId: data['external_center_id']
            }
        }

        if (data['classroom_id']) {
            const group = await Groups.findOneAsync({ _id: data['classroom_id'] });
            if (group) {
                newChild.defaultGroupId = group._id;
            }
        }
        data['person_id'] = await People.insertAsync(newChild, function (error) {
            if (error) {
                apiMethods.setStatusCode(500);
                apiMethods.setResponse(error.message, 500);
            }
        });
        await HistoryAuditService.logPersonChange({
            changeType: HistoryAuditChangeTypes.ADD,
            performedByName: HistoryAuditPeoplePerformedByNames.UNIFIED_INTEGRATION,
            previousState: null,
            currentState: { ...newChild, _id: data['person_id'] },
        });
        // Add relationships...
        for (const [index, familyId] of data['family_person_ids'].entries()) {
            const family = await People.findOneAsync({_id: familyId});
            const relationship = {
                orgId: org._id,
                personId: family._id,
                primaryCaregiver: index === 0, // Just gonna assume first person is the primary
                relationshipType: 'family',
                targetId: data['person_id']
            }
            await Relationships.insertAsync(relationship, function(error) {
                if (error) {
                    apiMethods.setStatusCode(500);
                    apiMethods.setResponse(error.message);
                }
            });
        }
        apiMethods.setStatusCode(200);
        apiMethods.setResponse(data);
    }
}
