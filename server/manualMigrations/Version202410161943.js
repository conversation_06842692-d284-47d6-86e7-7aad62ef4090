import { ManualMigrations } from "./manualMigrations";
import { ChildcareCrmUtil } from "../childcareCrmUtil";
import { ScheduleService } from "../scheduleService";
import { Orgs } from "../lib/collections/orgs";

ManualMigrations.addMigration({
    version: ************,
    description: 'Add a new field to every org that is already saved on the database, showing that it is using the classic platform',
    process: async function (orgId = null) {

        const batch = Orgs.rawCollection().initializeUnorderedBulkOp();
        let hasUpdates = false;
        Orgs.find({
            "billing.adyenInfo.accountCode": { $exists: true },
            "billing.adyenInfo.accountHolderId": { $exists: false },
            "billing.adyenInfo.balanceAccountId": { $exists: false },
            "billing.adyenInfo.storeId": { $exists: false }
        }).forEach(org => {
            batch.find({_id: org._id}).updateOne({ $set: { "billing.adyenInfo.platform": "classic" } });
            hasUpdates = true;
        });

        if (hasUpdates) {
            const execute = Meteor.wrapAsync(batch.execute, batch);
            return execute();
        }
    }
});