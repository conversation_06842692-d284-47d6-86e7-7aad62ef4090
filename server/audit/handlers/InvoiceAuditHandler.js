import { BaseAuditHandler } from './BaseAuditHandler';
import { Invoices } from '../../../lib/collections/invoices';
import { AUDIT_METHOD_NAMES } from '../../../lib/audit/auditTrailConstants';

const SKIP_METHODS = [
    AUDIT_METHOD_NAMES.BILLING.GENERATE_MANUAL_INVOICE,
    AUDIT_METHOD_NAMES.BILLING.SEND_MANUAL_PLAN_INVOICE,
];

/**
 * Audit handler for invoice-related operations.
 * Handles logging of common invoice actions such as voids, credits, reallocations, etc.
 *
 * Expected args shape:
 * {
 *   invoiceId?: string,
 *   invoice?: {
 *     _id: string,
 *     invoiceNumber: string,
 *     personId: string
 *   }
 * }
 */
export class InvoiceAuditHandler extends BaseAuditHandler {
    /**
     * Override the base `log` to skip logging for explicitly excluded methods.
     * @returns {Promise<void>}
     */
    async log() {
        if (SKIP_METHODS.includes(this.methodName)) {
            return;
        }

        return super.log();
    }
    /**
     * Builds the audit log data object for invoice-related actions.
     * Includes invoice ID, number, and associated person ID if available.
     *
     * @returns {Promise<Object>} The structured audit log document.
     */
    async getLogData() {
        const base = await super.getLogData();

        const invoiceId = this.args.invoiceId || this.args.invoice?._id;
        const invoice = invoiceId && await Invoices.findOneAsync({ _id: invoiceId });

        return {
            ...base,
            type: 'Invoice',
            actionType: this.resolveActionType(),
            invoiceId,
            invoiceNumber: invoice?.invoiceNumber,
            personId: invoice?.personId || base.personId,
        };
    }

    /**
     * Resolves the audit `actionType` based on the method name.
     *
     * @returns {
     *   'void' |
     *   'credit' |
     *   'notification' |
     *   'edit' |
     *   'adjustment' |
     *   'refund' |
     *   'reallocation' |
     *   'seizure' |
     *   'reinstatement' |
     *   'generation' |
     *   'send' |
     *   'invoice'
     * } Action type string.
     */
    resolveActionType() {
        const method = this.methodName;

        const map = {
            [AUDIT_METHOD_NAMES.BILLING.VOID_INVOICE]: 'void',
            [AUDIT_METHOD_NAMES.BILLING.CREDIT_INVOICE]: 'credit',
            [AUDIT_METHOD_NAMES.BILLING.RESEND_INVOICE]: 'notification',
            [AUDIT_METHOD_NAMES.BILLING.UPDATE_INVOICE_NOTES]: 'edit',
            [AUDIT_METHOD_NAMES.BILLING.VOID_CREDIT_LINE]: 'void',
            [AUDIT_METHOD_NAMES.BILLING.MODIFY_DISCOUNT]: 'adjustment',
            [AUDIT_METHOD_NAMES.BILLING.ADD_DISCOUNT]: 'adjustment',
            [AUDIT_METHOD_NAMES.BILLING.MANUAL_REFUND_CREDIT_LINE]: 'refund',
            [AUDIT_METHOD_NAMES.BILLING.REALLOCATE_PAYER]: 'reallocation',
            [AUDIT_METHOD_NAMES.BILLING.MANUAL_REVERSE_REALLOCATION]: 'reallocation',
            [AUDIT_METHOD_NAMES.BILLING.SEIZE_SECURITY_DEPOSIT]: 'seizure',
            [AUDIT_METHOD_NAMES.BILLING.REINSTATE_SEIZED_SECURITY_DEPOSIT]: 'reinstatement',
            // Optional: catchalls
            [AUDIT_METHOD_NAMES.BILLING.GENERATE_MANUAL_INVOICE]: 'generation',
            [AUDIT_METHOD_NAMES.BILLING.SEND_MANUAL_PLAN_INVOICE]: 'send',
        };

        return map[method] || 'invoice';
    }
}