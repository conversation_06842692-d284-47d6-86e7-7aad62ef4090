import { Orgs } from '../../../lib/collections/orgs';
import { People } from '../../../lib/collections/people';
import { insertPersonCreateUser } from '../../usersAndPeople';
const fastCsv = require('fast-csv');
const PhoneNumber = require('awesome-phonenumber');
const fs = require('fs');
const moment = require('moment-timezone');
const { HistoryAuditService } = require('../../historyAuditService');
const { HistoryAuditChangeTypes, HistoryAuditPeoplePerformedByNames } = require('../../../lib/constants/historyAuditConstants');

// const s3 = new aws.S3({ apiVersion: '2006-03-01' });

const tokenString = function() {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i=0; i<string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum,rnum+1);
	}
	return randomstring;
};

global.importLspPreselectedFamilies = async (opts) => {
  const options = opts || { }; //TODO
  const org = await Orgs.findOneAsync({ _id: options.orgId });
  // const fileStream = s3.getObject({Bucket: 'momentpath-customer-imports', Key: 'preselected.csv'}).createReadStream();
  const fileName = options.fileName;
  const fileStream = fs.createReadStream(`/Users/<USER>/Projects/gists/tendlymr_scripts/lsp/${fileName}`);
  const dataStream = fastCsv.parseStream(fileStream, { headers: true, escape: '\\'});

  await new Promise((resolve) => {
    dataStream.on('data', Meteor.bindEnvironment(async function(data) {
      dataStream.pause();

      const email = data?.["Email"]?.toLowerCase?.();
      const phone = data["Phone"];
      const name = data["Name"];

      const splitName = name.split(",");
      const firstName = splitName?.[1]?.trim?.();
      const lastName = splitName?.[0]?.trim?.();

      const pn = new PhoneNumber(phone, "US");
      const phoneIsValid = pn.isValid();

      const existingPerson = await People.findOneAsync({orgId: org._id, profileEmailAddress: email});
      if (existingPerson) {
        dataStream.resume();
      } else {
        const personData = {
          orgId: org._id,
          type: "family",
          designations: ["Wait List"],
          profileEmailAddress: email,
          firstName,
          lastName,
          profileData: {
            phonePrimary: phone,
          },
          lspPreselectImport: true,
          inActive: false,
          createdAt: new Date().valueOf(),
          createdBy: "SYSTEM_IMPORT_PRESELECTED"
        }

        if (!phoneIsValid) delete personData.profileData.phonePrimary;
        if (!email) delete personData.profileEmailAddress;

        if (options.confirm) {
          const newPersonId = await People.insertAsync(personData);
          await HistoryAuditService.logPersonChange({
            changeType: HistoryAuditChangeTypes.ADD,
            performedByName: HistoryAuditPeoplePerformedByNames.SYSTEM_IMPORT,
            previousState: null,
            currentState: { ...personData, _id: newPersonId },
          });
          if (personData.profileEmailAddress) {
            // var invitationId = UserInvitations.insert({
            //   token: tokenString(),
            //   email: personData.profileEmailAddress.trim(),
            //   orgId: org._id,
            //   personId: newPersonId,
            //   used:false,
            //   lspWaitlist: true,
            //   lspPreselectImport: true,
            // });
            personData.emailAddress = personData.profileEmailAddress.trim();
            try {
              const opResult = await insertPersonCreateUser({ personData, newPersonId, extraUserObj: {lspPreselectImport: true}});
            } catch (ex) {
              console.log("Error",ex);
            }
          }
        } else {
          console.log("new family member:", personData);
        }
        dataStream.resume();
      }
    }));

    dataStream.on('end', Meteor.bindEnvironment(function() {
      console.log("reached end of line");
      Meteor.setTimeout(function() {
        resolve();
      }, 5000);
    }));

  })

};
