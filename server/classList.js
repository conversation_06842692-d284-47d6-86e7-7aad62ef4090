import moment from "moment";
import _ from 'underscore';
import { Groups } from "../lib/collections/groups";
import { Orgs } from "../lib/collections/orgs";
import { People } from "../lib/collections/people";
import { Reservations } from "../lib/collections/reservations";
import { TimeCards } from "../lib/collections/timeCards";

export const sortGroupsByAge = (groups) => {
  return _.sortBy(groups, function(g) {
    if (g.ageGroup && g.ageGroup.begin) {
      let sortVal = parseInt(g.ageGroup.begin);
      if (g.ageGroup.end) sortVal += parseInt(g.ageGroup.end);
      if (g.ageGroup.type == 'months') return sortVal
      return sortVal + 500;
    }
    return 999999;
  });
}

/**
 * Get the children who are missing (not necessarily marked as absent) from their scheduled group(s) for today.
 * This does not include children who have cancelled the reservations for the day.
 *
 * @param options
 * @returns cursor to the People collection
 */
export const getTodaysMissingChildren = async (options) => {
  const org = await Orgs.findOneAsync({ _id: options.orgId }),
      timezone = org.getTimezone(),
      dayOfWeek = new moment.tz(timezone).format('ddd').toLowerCase(),
      startOfDay = new moment.tz(timezone).startOf('day').valueOf(),
      endOfDay = new moment.tz(timezone).endOf('day').valueOf();

  // This logic is derived from _widgetRatios.js: Template.widgetRatios.helpers::ratioGroups
  const canceledReservationPeopleIdsData = await Reservations.find(
      {
          orgId: org._id,
          scheduledDate: { '$gte': startOfDay, '$lt': endOfDay },
          cancellationReason: { $exists: true }
      },
      {
          fields: { selectedPerson: 1}
      }
  ).fetchAsync();
  const canceledReservationPeopleIds = canceledReservationPeopleIdsData.map(p => p.selectedPerson);
  const allReservationPeopleIdsData = await Reservations.find(
      {
          orgId: org._id,
          scheduledDate: { '$lt': endOfDay },
          reservationType: 'person',
          $and: [
              { $or: [{ scheduledEndDate: { '$gte': startOfDay } }, { scheduledEndDate: { $exists: false } }] },
              { $or: [{ recurringDays: dayOfWeek }, { recurringFrequency: { $exists: false } } ]}
          ]
      },
      {
          fields: { selectedPerson: 1 }
      }
  ).fetchAsync();
  const allReservationPeopleIds = allReservationPeopleIdsData.filter(r => !_.contains(canceledReservationPeopleIds, r.selectedPerson)).map(p => p.selectedPerson);

  return await People.find({
      _id: { $nin: canceledReservationPeopleIds, $in: allReservationPeopleIds },
      inActive: { $ne: true },
      type: 'person',
      $or: [{ checkedInOutTime: { $lt: startOfDay } }, { checkedInOutTime: { $exists: false } }]
  }).fetchAsync();
}

export const getRoomEnrollmentsData = async (options) => {
  const org = await Orgs.findOneAsync({ _id: options.orgId });
  const timezone = org.getTimezone()
  const dayMoment = new moment.tz(timezone);
  const startDateValue = dayMoment.startOf('day').startOf('week').valueOf();
  const endDateValue = dayMoment.endOf('day').endOf('week').valueOf();

  // Add pagination parameters for groups
  const pageSize = options.pageSize || 5;
  const page = options.page || 1;
  const skip = (page - 1) * pageSize;
  const scheduleTypes = await org.getScheduleTypes(),
  dayMap = {1: "M", 2: "T", 3: "W", 4: "R", 5: "F"};
  // Get total group count
  const totalGroupCount = await Groups.find({$and: [{ orgId: org._id }, {includeClassList: {$ne: false}}]}).countAsync();
  // Paginate the groups query
  const groupList = await Groups.find(
    {$and: [{ orgId: org._id }, {includeClassList: {$ne: false}}]},
    { 
      skip: skip,
      limit: pageSize,
      sort: { name: 1 }
    }
  ).fetchAsync();
  // Find people within groups to narrow down reservations query
  const groupPeople = await People.find({
    orgId: org._id,
    $or: [
      { groupId: { $in: groupList.map(group => group._id) } },
      { defaultGroupId: { $in: groupList.map(group => group._id) } }
    ],
    type: "person"
  }, {
    fields: { _id: 1, groupId: 1, defaultGroupId: 1 }
  }).fetchAsync();

  const query = {
    orgId: org._id,
    reservationType: "person",
    cancellationReason: null,
    selectedPerson: { $in: groupPeople.map(p => p._id) },
    "$or": [
        { scheduledEndDate: { "$exists": false } },
        { scheduledEndDate: { "$exists": true, "$gte": startDateValue } },
        { scheduledEndDate: { "$exists": true }, "$eq": null }
    ] 
  };

  const groupData = {};
  groupList.forEach(function (g) {
    groupData[g._id] = {
      totals: {
        M: 0,
        T: 0,
        W: 0,
        R: 0,
        F: 0
      },
      partTimeTotals: {
        M: 0,
        T: 0,
        W: 0,
        R: 0,
        F: 0
      }
    }
  });

  const getReservations = await Reservations.findWithRecurrence({startDateValue, endDateValue, query});
  getReservations.forEach(function(reservation) {
    const p = groupPeople.find(person => person._id === reservation.selectedPerson);
    if (p && !p.inActive) {
      const defaultGroup = reservation.groupId ? reservation.groupId : p.defaultGroupId;
      const matchedScheduleType = _.find(scheduleTypes, st => st._id == reservation.scheduleType);
      const isFullTime = !matchedScheduleType || !matchedScheduleType.startTime;
      const dayOfWeek = new moment.tz(reservation.scheduledDate, timezone).day();
      const dayValue = dayMap[dayOfWeek];
      if (dayValue && defaultGroup && groupData[defaultGroup]) {
        groupData[defaultGroup]["totals"][dayValue] += 1;
        if (!isFullTime) {
          groupData[defaultGroup]["partTimeTotals"][dayValue] += 1;
        }
      }
    }
  });
  return {
    groupData,
    groupList,
    pagination: {
      currentPage: page,
      pageSize: pageSize,
      totalCount: totalGroupCount,
      totalPages: Math.ceil(totalGroupCount / pageSize)
    }
  };
}

export const getTotalClassListDefaultDays = async (options) => {
  const org = await Orgs.findOneAsync({ _id: options.orgId });
  const dayMoment = new moment.tz(org.getTimezone());
  const dayMomentValue = dayMoment.startOf('day').valueOf();
  
  let withdrawQuery = [ { withdrawDate: { $exists: false } }, { withdrawDate: { $gt: dayMomentValue }} ];
  if (org.profileDataPrefix()) {
    let withdrawExists = {};
    let withdrawDateVal = {};
    withdrawExists[`${org.profileDataPrefix()}.withdrawDate`] = { $exists: false };
    withdrawDateVal[`${org.profileDataPrefix()}.withdrawDate`] = { $gt: dayMomentValue };
    withdrawQuery = [ withdrawExists, withdrawDateVal];
  }
  
  let query = {
    orgId: org._id,
    type: "person",
    inActive: { $ne: true },
    defaultGroupId: options.groupId,
    $and: [
      { $or : withdrawQuery},
    ],
  };
  
  if (org.profileDataPrefix()) {
    query[`${org.profileDataPrefix()}.enrollmentDate`] = { $lte: dayMomentValue }
  } else {
    query.enrollmentDate = { $lte: dayMomentValue };
  }
  
  const totals = {
    M: 0,
    T: 0,
    W: 0,
    R: 0,
    F: 0
  }, partTimeTotals = {
    M: 0,
    T: 0,
    W: 0,
    R: 0,
    F: 0
  },
  scheduleTypes = (await Orgs.current()).getScheduleTypes(),
  dayMap = {"M": "mon", "T": "tue", "W": "wed", "R": "thu", "F": "fri"};
  
  const groupPeople = await People.find(query, { fields: { firstName:1, lastName: 1, classList: 1, defaultGroupId: 1 }}).fetchAsync();
  for (const person of groupPeople) {
    /*
    let days = person.classList && person.classList.defaultDays;
    if (days) {
      totals.M += parseFloat(days.M);
      totals.T += parseFloat(days.T);
      totals.W += parseFloat(days.W);
      totals.R += parseFloat(days.R);
      totals.F += parseFloat(days.F);
    }
    */
    const validPersonSchedules = await Reservations.find({selectedPerson: person._id, cancellationReason:{$exists:false}, recurringFrequency:{"$exists":true }}).fetchAsync();
    _.each(validPersonSchedules, schedule => {
      const matchedScheduleType = _.find(scheduleTypes, st => st._id == schedule.scheduleType),
        isFullTime = !matchedScheduleType || !matchedScheduleType.startTime;
      _.each(dayMap, (dayValue, dayCode) => {
        if (_.contains(schedule.recurringDays, dayValue)) {
          totals[dayCode]+=1;
          if (!isFullTime) 
            partTimeTotals[dayCode]+=1;
        }
      });
    });
  }
  return {totals, partTimeTotals};
}

export const getFteForMonth = async (options) => {
  const org = await Orgs.findOneAsync({ _id: options.orgId });
  const timezone = org.getTimezone();
  const dayMoment = new moment.tz(options.startMonth, "MM/DD/YYYY", timezone);
  const dayMomentValue = dayMoment.startOf('day').valueOf();
  
  let withdrawQuery = [ { withdrawDate: { $exists: false } }, { withdrawDate: { $gt: dayMomentValue }} ];
  let hasProfileData = false;
  if (org.profileDataPrefix()) {
    let withdrawExists = {};
    let withdrawDateVal = {};
    withdrawExists[`${org.profileDataPrefix()}.withdrawDate`] = { $exists: false };
    withdrawDateVal[`${org.profileDataPrefix()}.withdrawDate`] = { $gt: dayMomentValue };
    withdrawQuery = [ withdrawExists, withdrawDateVal];
    hasProfileData = org.profileDataPrefix();
  }

  const scheduledDateQueryValue = new moment(dayMoment).endOf('week').endOf('day').valueOf();
  // find defaultScheduled and reservations = options.groupId
  const allReservations = options.allReservations.filter( r=> r.scheduledDate <= scheduledDateQueryValue && (!r.scheduledEndDate || r.scheduledEndDate >= dayMomentValue)),
    defaultScheduledPeopleIds = allReservations.filter( r => !r.groupId).map( r => r.selectedPerson),
    groupScheduledPeopleIds = allReservations.filter( r => r.groupId == options.groupId).map( r => r.selectedPerson);
    //console.log("group", options.groupId, "default", defaultScheduledPeopleIds, "group", groupScheduledPeopleIds);

  let query = {
    orgId: org._id,
    type: "person",
    $and: [
      { $or : [ 
        { defaultGroupId: options.groupId, "$or": [
            {"_id": {"$in" : defaultScheduledPeopleIds}},
            {"_id": {"$nin" : defaultScheduledPeopleIds.concat(groupScheduledPeopleIds)}}
        ]}, 
        { "_id": {"$in": groupScheduledPeopleIds } } 
      ]}, 
      //{ $or : withdrawQuery},
    ],
  };
  if (!options.includeInactive) 
    query["inActive"] = { $ne: true };
  /*
  if (org.profileDataPrefix()) {
    query[`${org.profileDataPrefix()}.enrollmentDate`] = { $lte: scheduledDateQueryValue }
  }
  else {
    query.enrollmentDate = { $lte: scheduledDateQueryValue };
  }
  */

    //console.log("groupPeople", JSON.stringify(query,null,2))
    //console.log("group", options.groupId, "dayMoment", dayMoment, "startMonth", options.startMonth)
  const weekdayValueMapping = {1: "mon", 2: "tue", 3: "wed", 4: "thu", 5: "fri"};
  const rawGroupPeople = await People.find(query).fetchAsync();
  let fteTotal = 0;
  const allScheduleTypes = org.getScheduleTypes(),
    children = [];

  let rawGroupPeopleMapped = await Promise.all(rawGroupPeople?.map(async function (gp) { 
      return ((!options.ageRangeStart || await gp.calcAge('days')/365 >= options.ageRangeStart) && 
      (!options.ageRangeEnd || await gp.calcAge('days')/365 <= options.ageRangeEnd));
  }));
  const groupPeople =  _.filter(rawGroupPeople,(_,i) => rawGroupPeopleMapped[i]);
  for( let x = 0; x < groupPeople.length; x++) {
    let childAdded = false;

    const p = groupPeople[x];
    const withdrawDate = p.getWithdrawDateRaw(hasProfileData) ? new moment.tz(p.getWithdrawDateRaw(hasProfileData), "MM/DD/YYYY", timezone) : null;
    const enrollmentDate = new moment.tz(p.getEnrollmentDateRaw(hasProfileData), "MM/DD/YYYY", timezone);
    
    // Grouping reservations by selectedPerson allows us to add multiple recurring schedules to calculate true FTE
    let allGroupReservations = allReservations.filter(r => r.selectedPerson == p._id && (( !r.groupId && p.defaultGroupId == options.groupId) || (r.groupId == options.groupId)))
    const groupedByReservations = _.groupBy(allGroupReservations, 'selectedPerson');
    for (const [personId, reservations] of Object.entries(groupedByReservations)) {
      
      let childFteTotal = 0, scheduleLabel = "", scheduleLabelDaysOnly = "";
      _.each(reservations, r => {
        const matchedScheduleType = allScheduleTypes.find( st => st._id == r.scheduleType);
        const scheduledDate = (r.scheduledDate) ? new moment.tz(r.scheduledDate, timezone).startOf('day') : null;
        const scheduleEndDate = (r.scheduledEndDate) ? new moment.tz(r.scheduledEndDate, timezone).endOf('day') : null;
        
        if (matchedScheduleType && matchedScheduleType.fteCount) {
          let currentFteDays = r.recurringDays.length
          if (scheduledDate && scheduledDate.isAfter(dayMoment)) {
            const scheduleStartDay = scheduledDate.day();
            const enrolledDay = enrollmentDate.isAfter(dayMoment) ? enrollmentDate.day() : 0;
            const daysEnrolled = [];
            for (const [dayVal, strName] of Object.entries(weekdayValueMapping)) {
              if (scheduleStartDay <= parseInt(dayVal) && enrolledDay <= parseInt(dayVal)) {
                daysEnrolled.push(strName);
              }
            }
            
            currentFteDays = _.intersection(r.recurringDays, daysEnrolled).length;
          } else if (enrollmentDate.isAfter(dayMoment)) {
            const enrolledDay = enrollmentDate.day();
            const daysEnrolled = [];
            for (const [dayVal, strName] of Object.entries(weekdayValueMapping)) {
              if (enrolledDay <= parseInt(dayVal)) {
                daysEnrolled.push(strName);
              }
            }

            currentFteDays = _.intersection(r.recurringDays, daysEnrolled).length
          }

          if (scheduleEndDate && scheduleEndDate.valueOf() < scheduledDateQueryValue) {
            const endDay = scheduleEndDate.day();
            const daysEnrolled = [];
            for (const [dayVal, strName] of Object.entries(weekdayValueMapping)) {
              if (endDay >= dayVal) {
                daysEnrolled.push(strName);
              }
            }

            currentFteDays = _.intersection(r.recurringDays, daysEnrolled).length;
          } /*else if (withdrawDate && withdrawDate.valueOf() < scheduledDateQueryValue) {
            const withdrawDay = withdrawDate.day();
            const daysEnrolled = [];
            for (const [dayVal, strName] of Object.entries(weekdayValueMapping)) {
              if (withdrawDay >= dayVal) {
                daysEnrolled.push(strName);
              }
            }

            currentFteDays = _.intersection(r.recurringDays, daysEnrolled).length;
          }*/
          
          currentFteDays = currentFteDays * parseFloat(matchedScheduleType.fteCount) / 5
          fteTotal += currentFteDays;
          childFteTotal += currentFteDays;
          scheduleLabel += (scheduleLabel != "" ? " + " : "") + matchedScheduleType.type + " " + _.map(r.recurringDays || [], rd => rd.capitalizeFirstLetter()).join(", ");
          scheduleLabelDaysOnly += (scheduleLabelDaysOnly != "" ? ",*" : "*") + _.map(r.recurringDays || [], rd => rd.capitalizeFirstLetter()).join("");
        }
        
      })
      	
      if (enrollmentDate.isAfter(dayMoment.clone().add(6, "days"))) childFteTotal = 0;
      
      children.push({
        _id: p._id,
        firstName: p.firstName, 
        lastName: p.lastName, 
        currentFte: childFteTotal, 
        age: p.calcAgeRawFromDate(hasProfileData, "years", dayMomentValue), 
        enrollmentDate: enrollmentDate && enrollmentDate > dayMoment && enrollmentDate.valueOf(),
        withdrawalDate: withdrawDate && withdrawDate.valueOf(), 
        scheduleLabel,
        scheduleLabelDaysOnly,
        inActive: p.inActive
      });
      childAdded = true;
    }
    //console.log(p.firstName, p.lastName, enrollmentDate, dayMoment, childAdded);
    if (!childAdded && enrollmentDate && enrollmentDate > dayMoment) {
      children.push({
        _id: p._id,
        firstName: p.firstName, 
        lastName: p.lastName, 
        currentFte: 0, 
        age: p.calcAgeRawFromDate(hasProfileData, "years", dayMomentValue), 
        enrollmentDate: enrollmentDate && enrollmentDate > dayMoment && enrollmentDate.valueOf()
      });
    }
  }

  return { 
    totalCount : (Math.round(fteTotal * 10) / 10).toFixed(1),
    groupChildren : _.sortBy(children, c => c.firstName + "|" + c.lastName)
  };  
}

export const personPayCalculation = (options) => {
  var payRate = parseFloat(options.payRate);
  var sf = options.staffForecast;
  var isExempt = (options.exemptStatus && options.exemptStatus.toLowerCase() == "exempt") ? true : false;
  var totalHours = (parseFloat(sf.M) + parseFloat(sf.T) + parseFloat(sf.W) + parseFloat(sf.R) + parseFloat(sf.F)) - parseFloat(sf.BREAK) + parseFloat(sf.PTO) + parseFloat(sf.EVENT); //do I need to multiply by 24????????
  var potentialOTHours = totalHours - parseFloat(sf.PTO);
  var otHours = (potentialOTHours > options.overtimeThreshold) ? (potentialOTHours - options.overtimeThreshold) : 0;
  var weeklyPayroll = ((totalHours - otHours) * payRate) + (otHours * payRate * 1.5);
  
  if (isExempt) {
    weeklyPayroll = (40 * payRate);
  }
  var weeklyLoadedPayroll = weeklyPayroll * 1.13;
  return { weeklyLoadedPayroll, weeklyPayroll };
}

export const timeCardPersonPayCalculation = async (options) => {
  var payRate = parseFloat(options.payRate);
  var ot = parseInt(options.overtimeThreshold);
  var isExempt = (options.exemptStatus && options.exemptStatus.toLowerCase() == "exempt") ? true : false;

  const startRange = new moment.tz(options.startDate, "MM/DD/YYYY", "utc").startOf('day').valueOf();
  const endRange = new moment.tz(options.endDate, "MM/DD/YYYY", "utc").endOf('day').valueOf();
  const query = { 
    orgId: options.orgId, 
    personId: options.personId,
    checkInDate: { $exists: true },
    checkOutDate: { $exists: true },
    checkInTime: { $exists: true },
    checkOutTime: { $exists: true },
    timeCardDateStamp: { $gte: startRange, $lt: endRange },
    void: {$ne: true},
  };
  
  var payTypesAccrural = {};
  var totalMinutesActuals = 0;
  await TimeCards.find(query).forEachAsync(function(tc) {
    var x = new moment(`${tc.checkInDate} ${tc.checkInTime}`, "MM/DD/YYYY h:mm a");
    var y = new moment(`${tc.checkOutDate} ${tc.checkOutTime}`, "MM/DD/YYYY h:mm a");
    var duration = Math.abs(parseInt(moment.duration(x.diff(y)).as('minutes')));
    
    var currentTimeAmmount = payTypesAccrural[tc.selectedPayTypeId] || 0;
    payTypesAccrural[tc.selectedPayTypeId] = currentTimeAmmount + duration;
    totalMinutesActuals += duration;
  });
  
  // Add weekly payroll total based on time card paytypes -- must convert minutes to hours
  // We encounter overtime at the backend of the calculation by multiplying the total hours "over" by 0.5 
  // we will account for the full pay amount (1x) here regardless of overtime threshold
  // THIS DOES NOT TRACK WHICH PAY TYPE(S) ACCRUE OVERTIME - WILL APPLY 1.5X TO STANDARD PAY  
  var weeklyPayrollTotal = 0;
  for (const pay in payTypesAccrural) {
    if (pay == "standard") {
      weeklyPayrollTotal += (payTypesAccrural[pay] / 60) * payRate;
    } else {
      var orgPayType = _.find(options.customPayTypes, function(pt) { return pt._id == pay });
      var rate = payRate;
      if (orgPayType && orgPayType.staffProfileRate != true) {
        rate = parseFloat(orgPayType.rate);
      }
      weeklyPayrollTotal += (payTypesAccrural[pay] / 60) * rate;
    }
  }
  var actualHours = totalMinutesActuals / 60;
  var otHours = ( actualHours > ot) ? (actualHours - ot) : 0;
  var otRate = 0.5; //see aforementioned comment 
  var weeklyPayroll = weeklyPayrollTotal + (otHours * payRate * otRate);
  
  // if staff is exempt then multiple 40 hours by their payRate
  if (isExempt) {
    weeklyPayroll = (40 * payRate);
  }
  
  var weeklyLoadedPayroll = weeklyPayroll * 1.13;
  return {
    weeklyPayroll,
    weeklyLoadedPayroll,
  }
}

export const getChildDetailsForMonth = async (options) => {
  const org = await Orgs.findOneAsync({ _id: options.orgId });
  const dayMoment = new moment.tz(options.startMonth, "MM/DD/YYYY", org.getTimezone());
  const dayMomentValue = dayMoment.startOf('day').valueOf();
  
  let withdrawQuery = [ { withdrawDate: { $exists: false } }, { withdrawDate: { $gt: dayMomentValue }} ];
  if (org.profileDataPrefix()) {
    let withdrawExists = {};
    let withdrawDateVal = {};
    withdrawExists[`${org.profileDataPrefix()}.withdrawDate`] = { $exists: false };
    withdrawDateVal[`${org.profileDataPrefix()}.withdrawDate`] = { $gt: dayMomentValue };
    withdrawQuery = [ withdrawExists, withdrawDateVal];
  }
  
  let query = {
    orgId: org._id,
    type: "person",
    inActive: { $ne: true },
    defaultGroupId: options.groupId,
    $and: [
      { $or : withdrawQuery},
    ],
  };
  
  if (org.profileDataPrefix()) {
    query[`${org.profileDataPrefix()}.enrollmentDate`] = { $lte: dayMomentValue }
  } else {
    query.enrollmentDate = { $lte: dayMomentValue };
  }
  
  const children = [];
  const groupPeople = await People.find(query, { fields: { firstName: 1, lastName: 1, classList: 1}, sort: { lastName: 1, firstName: 1}}).fetchAsync();
  //we only want to include children that have a current forcasted schedule (ie defaultDays)
  _.each(groupPeople, (person) => {
    if (person.classList && person.classList.defaultDays) {
      let days = person.classList.defaultDays,
        currentFte;
      
      if (person.transitionGroupId && person.transitionGroupId == options.groupId && person.transitionGroupDate && person.transitionGroupDate <= dayMomentValue) 
        days = person.classList.transitionDays;

      if (parseFloat(days.M) > 0 || parseFloat(days.T) > 0 || parseFloat(days.W) > 0 || parseFloat(days.R) > 0 || parseFloat(days.F) > 0) {
        currentFte = ((parseFloat(days.M) + parseFloat(days.T) + parseFloat(days.W) + parseFloat(days.R) + parseFloat(days.F)) / 5);
        
        children.push({ firstName: person.firstName, lastName: person.lastName, currentFte });
      }
    }
  });
  return children;
}

export const getGroupMemorizedRateAndDiscount = async (options) => {
  //memorized group rates...not accounting for frequency of rate ( assumes monthly )
  var org = options.org;
  var modifiers = org.availableBillingFrequencyMultipliers();
  var timezone = org.getTimezone();
  var tuitionItems = _.filter(org.billing.plansAndItems, (pi) => pi.category == 'tuition');	
  var g = options.group;
  var memorizedRate = 0;
  var discounts = 0;
  
  
  var dayMomentValue = new moment.tz(options.startDay, "MM/DD/YYYY", timezone).startOf('day').valueOf();
  let withdrawQuery = [ { withdrawDate: { $exists: false } }, { withdrawDate: { $gt: dayMomentValue }} ];
  if (org.profileDataPrefix()) {
    let withdrawExists = {};
    let withdrawDateVal = {};
    withdrawExists[`${org.profileDataPrefix()}.withdrawDate`] = { $exists: false };
    withdrawDateVal[`${org.profileDataPrefix()}.withdrawDate`] = { $gt: dayMomentValue };
    withdrawQuery = [ withdrawExists, withdrawDateVal];
  }
  
  var peopleQuery = {
    orgId: org._id, 
    defaultGroupId: g._id, 
    type: 'person', 
    inActive: { $ne: true },
    $and: [{
      $or: withdrawQuery
    }]
  };
  
  if (org.profileDataPrefix()) {
    peopleQuery[`${org.profileDataPrefix()}.enrollmentDate`] = { $lte: dayMomentValue }
  } else {
    peopleQuery.enrollmentDate = { $lte: dayMomentValue };
  }
  
  var groupMembers = await People.find(peopleQuery).forEachAsync( function(p) {
    var enrolledPlans = p.billing && p.billing.enrolledPlans;
    if (enrolledPlans && _.isArray(enrolledPlans)) {
      _.each(enrolledPlans, (plan) => {
        var matchedTuitionPlan = _.find(tuitionItems, (ti) => ti._id == plan._id);
        if (matchedTuitionPlan) {
          let shouldContinue = (plan.enrollmentForecastStartDate) ? true : false;
          if (plan.enrollmentForecastStartDate && plan.enrollmentForecastStartDate > dayMomentValue) shouldContinue = false;
          if (plan.enrollmentForecastEndDate && plan.enrollmentForecastEndDate < dayMomentValue) shouldContinue = false;
          if (!shouldContinue) return;
          
          const tuitionFrequencyModifier = modifiers[matchedTuitionPlan.frequency] || 1.0;
          memorizedRate += parseFloat(parseFloat(matchedTuitionPlan.amount) * tuitionFrequencyModifier);
          
          if (plan.allocations && _.isArray(plan.allocations)) {
            _.each(plan.allocations, (allocation) => {
              if (_.contains(["discount"], allocation.allocationType)) {
                if (allocation.amountType == 'percent') {
                  discounts += parseFloat((parseFloat(matchedTuitionPlan.amount) * (parseInt(allocation.amount)/100)) * tuitionFrequencyModifier)
                } else {
                  discounts += parseFloat(parseFloat(allocation.amount) * tuitionFrequencyModifier)
                }
              }
            });
          }
        }
      })
    }
  });
  
  return { memorizedRate, discounts };
}
