import { Accounts } from "meteor/accounts-base";
import { CardProviders } from './card_providers/cardProviders';
import currency from 'currency.js';
import { Agenda } from 'agenda/es';
import {InvoiceGenerationService} from "./invoices/invoiceGenerationService";
import moment from 'moment-timezone';
import { AutoPayService } from './invoices/autoPayService';
import { Log } from '../lib/util/log';
import {InvoiceService} from "./invoiceService";
import { ChargebackAutomationService } from './invoices/chargebackAutomationService';
import { ConfigurationSettingsService, ServerConfigurationConstants } from './config/configurationSettingsService';
import { EmailService } from './emails/emailService';
import Papa from 'papaparse';
import { People } from '../lib/collections/people';
import { MappedLedgerEntriesForRange, payInvoiceWithCreditMemo } from './methodsBilling';
import { Orgs } from '../lib/collections/orgs';
import { UserLogins } from '../lib/collections/userLogins';
import _ from '../lib/util/underscore';
import { processAnalytics } from './analytics';
import { processBillingEmail } from './processBillingEmail';
import { Invoices } from '../lib/collections/invoices';
import { Relationships } from '../lib/collections/relationships';
import { AuditLedgerBatchesHistorical } from './collections/auditLedgerBatchesHistorical';
import { SharedConfigs } from './collections/sharedConfigs';
import { findActiveOrgAndUpdateUser } from './usersAndPeople';
import { CreditMemosService } from './invoices/creditMemosService';

const agenda = new Agenda({ db: { address: process.env.MONGO_URL } });


Accounts.onCreateUser(function (options, user) {
	if (options.userData)
		user.userData = options.userData;
	if (options.orgId)
		user.orgId = options.orgId;
	if (options.personId)
		user.personId = options.personId;

    return user;
});

Accounts.validateLoginAttempt(async function(_attemptObj) {
  var attemptObj = _attemptObj;

	// PASSWORDLESS PASSTHRU FOR PAYMENT STATEMENT WORKFLOW
	if (attemptObj?.user && attemptObj?.allowed && attemptObj?.type == "passwordless") {
		return true;
	}

	if (attemptObj && attemptObj.user && attemptObj.allowed) {
		var user = attemptObj.user;
	var person = await People.findOneAsync(user.personId);
    var currentOrg = await Orgs.findOneAsync({_id: user.orgId});
    var errorObj = null;
    if (currentOrg && currentOrg.inactive) {
      errorObj = {
        status: 403,
        message: "Your organization's account is disabled."
      };
    } else if (!currentOrg) {
      errorObj = {
        status: 403,
        message: "Your organization's account is not found"
      };
    }

		if (person && person.inActive) {
      errorObj = {
        status: 403,
        message: "Your account is disabled."
      };
    } else if (!person) {
      errorObj = {
        status: 403,
        message: "Your account is not found"
      };
    }

    if (errorObj) {
      var activeOrgAndPerson = await findActiveOrgAndUpdateUser(user);
      if (!activeOrgAndPerson) throw new Meteor.Error(errorObj.status, errorObj.message);
      attemptObj.user.orgId = activeOrgAndPerson.orgId;
      attemptObj.user.personId = activeOrgAndPerson.personId;
    }

	}

	if (attemptObj && attemptObj.user) {

		if (attemptObj.user.pending) {
			await Meteor.users.updateAsync(attemptObj.user._id, {$set: {pending: false}});
		}

		const currentOrg = await Orgs.findOneAsync({_id: attemptObj.user.orgId});
		const currentPerson = await People.findOneAsync({_id: attemptObj.user.personId});

		Meteor.defer( async function() {
			await UserLogins.upsertAsync({
				userId: attemptObj.user._id,
				orgId: currentOrg._id,
				personId: currentPerson._id,
				lastSeenDate: new moment().startOf("day").toDate()
			 }, {"$set": {
				userId: attemptObj.user._id,
				orgId: currentOrg._id,
				personId: currentPerson._id,
				lastSeenDate: new moment().startOf("day").toDate(),
				personType: currentPerson.type,
				lastSeenAddress: attemptObj.connection.clientAddress,
				lastSeenUserAgent: attemptObj.connection.httpHeaders['user-agent']
			}});
		});

		const trackObj = {
			personId: attemptObj.user.personId,
			personType: currentPerson.type,
			clientAddress: attemptObj.connection.clientAddress,
			orgName: currentOrg.name,
			orgId: attemptObj.user.orgId,
			userAgent: attemptObj.connection.httpHeaders['user-agent']
		};
		try {
			processAnalytics( {metaCxData: {type: "user-login", data: trackObj}});
		} catch(err) {
			console.log("metacx -- couldn't track login");
		}
	}

	return true;
});

export const invoicePerson = async function(options) {
	try {
		return await InvoiceGenerationService.invoicePerson(options);
	} catch (e) {
		throw new Error(e.message);
	}
};

export const creditFamilyPersonInvoices = async function(options) {
	return await CreditMemosService.creditFamilyPersonInvoices(options);
}

const billingTimezoneMap = {
	"Eastern Time": "America/New_York",
	"Central Time": "America/Chicago",
	"Mountain Time": "America/Denver"
};

export const runInvoices =  async function(runOptions) {

	const currentUserId = runOptions.userId || 'SYSTEM';

	const orgQuery = InvoiceService.getOrgQueryForInvoices(runOptions, billingTimezoneMap);

	const billingOrgs = await Orgs.find(orgQuery, { disableOpLog: true }).fetchAsync();

	if (billingOrgs === undefined || billingOrgs.length === 0) {
		console.error('No Org Found');
		console.trace();
		return; // If no org is found, exit runInvoices
	}

	for( const org of billingOrgs ){
		console.log("Found org: " + org.name);
		moment.tz.setDefault(org.getTimezone());
		const batchStamp = Date.now();

		const todayStartStamp = new moment().startOf("day").valueOf();

        const failedInvoicePeopleIds = await InvoiceGenerationService.runInvoicesForOrg(org, batchStamp);

        // find family members who will receive invoices
        const people = await People.find({orgId: org._id, type:"family", inActive:{$ne:true}}).fetchAsync();
		people.forEach(function(familyPerson) {
            try {
                agenda.now("enqueueBillingEmail", {emailType:"family_invoice", familyPersonId: familyPerson._id, batchStamp: batchStamp});
            } catch(e) {
                console.error(`Failed adding agenda to enqueueBillingEmail within runInvoices on ${familyPerson.firstName} ${familyPerson.lastName}, Person Id: ${familyPerson._id}`);
                console.trace();
            }
        });
		// find family members with credit memos and process if appropriate
		const dueDateStamp = new moment().startOf('day').valueOf();
		console.log("looking for credit memos");
		const allPeople = await People.find({orgId: org._id, type:"family", inActive:{$ne:true}, "billing.creditMemos":{"$elemMatch":{"openAmount": {$gt: 0 }, "type":{"$ne":"securityDepositAuto"}}}}).fetchAsync();
		for(const familyPerson of allPeople){	
			try {
				console.log("found person with credit memo:", familyPerson._id, familyPerson.lastName);
				const invoicesCredited = await creditFamilyPersonInvoices({ familyPerson: familyPerson, batchStamp: batchStamp});
			} catch(e) {
				console.error(`Failed running creditFamilyPersonInvoices within runInvoices on ${familyPerson.firstName} ${familyPerson.lastName}, Person Id: ${familyPerson._id}`);
				console.trace();
			}
		};
		// find family members with autopay and process if appropriate
		const paymentProviderName = org.billingCardProviderName(),
			paymentProvider = paymentProviderName && await CardProviders.get(paymentProviderName);

		if (paymentProvider) {
			agenda.now("enqueueAutoPayments", {orgId: org._id, dueDateStamp, batchStamp});
		}

		// expire discounts
		const expirableDiscountTypes = _.pluck( _.filter( org.availableDiscountTypes(false, true), (d) => { return d.expiresWithGracePeriod; } ), "type");
		let expiredDiscountPeople = [];
		const invoiceData = await Invoices.find({orgId: org._id,
			openAmount: {$gt:0},
			discountsExpired: {$ne: true},
			"lineItems.appliedDiscounts": {$elemMatch: {type: "discount", source: {"$in": expirableDiscountTypes }}},
			dueDate: dueDateStamp
		}).fetchAsync();
		invoiceData.forEach(function(expirableInvoice) {
			try{
				console.log("found Expirable Invoice:", expirableInvoice.invoiceNumber);
				let amountToAddBack = 0.00;
				_.each(expirableInvoice.lineItems, (li)=> {
					_.each(li.appliedDiscounts, (ad) => {
						if (ad.type == "discount" && _.contains(expirableDiscountTypes, ad.source) && !ad.expired) {
							ad.expired=true;
							amountToAddBack += ad.amount;
							ad.amount = 0;
							if (ad.originalAllocation && li.enrolledPlan) {
								let oa = _.find(li.enrolledPlan.allocations, (a) => { return a.id == ad.originalAllocation.id;});
								if (oa) {
									oa.expired = true;
								}
							}
						}
					});
				});
				if (amountToAddBack > 0) {
					console.log("adding back:", amountToAddBack);
					Invoices.updateByIdWithJournalEntry(expirableInvoice._id, {
						"$set": {
							lineItems: expirableInvoice.lineItems,
							discountsExpired: true,
							discountsExpiredAt: batchStamp
						},
						"$inc": {
							openAmount: amountToAddBack,
							originalAmount: amountToAddBack,
							discountAmount: -1.0 * amountToAddBack
						}
					}, {
						userId: currentUserId,
						personId: '',
						orgId: org._id,
						reason: `Adding back ${amountToAddBack} to invoice due to expired discount`,
						reasonLocation: 'server/util.js:runInvoices'
					});
					expiredDiscountPeople.push(expirableInvoice.personId);
				}
			} catch(e) {
				console.error(`Failed adding back to Invoice Id: ${expirableInvoice._id}`);
				console.trace();
			}
		});
		//send statements for expired discounts
		if (expiredDiscountPeople.length> 0 ) {
			console.log("processing expired discount statements", expiredDiscountPeople);
			let familyIds = [];
			const allRelationships = await Relationships.find({orgId: org._id, targetId: {$in: expiredDiscountPeople}, relationshipType:"family"}).fetchAsync();
			allRelationships.forEach(function(relationship) {
				familyIds.push(relationship.personId);
			});
			for( const fId of _.uniq(familyIds)) {
				try{
					console.log("processing family statement for ", fId);
					await processBillingEmail({emailType:"family_statement", familyPersonId: fId});
				} catch(e) {
					console.error(`Failed processingBillEmail for Person Id: ${fId}`);
					console.trace();
				}
			};
		}
		try {
			//handle automatic payment fees
			if (_.deep(org, "billing.scheduling.assessLateFee") && (! (org.billing.suspendInvoicingIndefinitely || (org.billing.suspendInvoicingUntilDate && org.billing.suspendInvoicingUntilDate > todayStartStamp))))  {
				console.log("in handling late fees");

				const lateFeeItem = _.find(org.billing.plansAndItems, pi => pi._id === _.deep(org, "billing.scheduling.assessLateFeeItemId"));
				const lateDateStamp = new moment(dueDateStamp).subtract(_.deep(org, "billing.scheduling.assessLateFeeDays"), "days").valueOf();

				if (lateFeeItem && lateDateStamp) {
					console.log("Assessing late fees using ", lateFeeItem.description, " and datestamp ", lateDateStamp);
					const lateInvoices = await Invoices.find({orgId: org._id, voidedAt:{"$ne": true}, openAmount:{"$gt": 0}, dueDate: lateDateStamp, lateFeeAssessedAt: {"$exists": false}}).fetchAsync();
					for (lateInvoice of lateInvoices) {
						try{
							console.log("assessing late fee for ", lateInvoice.personId, " inv id", lateInvoice._id);
							const personLateFeeItem = {...lateFeeItem};
							personLateFeeItem.description += " for invoice #" + lateInvoice.invoiceNumber;
							const lateChargeObject = {"$push": {"billing.pendingCharges":
										{
											"_id": Random.id(),
											"originalItem": personLateFeeItem,
											"createdAt": new Date().valueOf(),
											"price": lateFeeItem.amount,
											"quantity": 1,
											"type": "item"
										}
								}};
							await People.updateAsync({_id: lateInvoice.personId}, lateChargeObject);
							const lateFeeInvoiceId = await invoicePerson({orgId: org._id, personId: lateInvoice.personId, allowItemsOnly: true });
							Invoices.updateByIdWithJournalEntry(lateInvoice._id, {
								"$set": {
									"lateFeeAssessedAt": new Date().valueOf(),
									"lateFeeAssessedInvoiceId": lateFeeInvoiceId
								}
							}, {
								userId: currentUserId,
								personId: '',
								orgId: org._id,
								reason: `Assessed late fee for invoice`,
								reasonLocation: 'server/util.js:runInvoices'
							});
						}catch(e){
							console.error(`Failed updating late invoice, Invoice Id: ${lateInvoice._id}`);
							console.trace();
						}
					}
				}
			}
		} catch(e) {
			console.error(`Failed handling automatic fees for Org Id: ${org._id}`);
			console.trace()
		}
	};

	console.log("billing process complete");

	//flag billing complete
	if (Meteor.settings.billingOfficial)  {
		await SharedConfigs.upsertAsync({key:"lastBillingComplete"}, {"$set": {
			key:"lastBillingComplete",
			createdAt: Date.now()
		}});
	}
};

export const runAutoPayments = async function(options) {
	// Don't move this out of this file yet to ensure the global constants within are loaded
	await AutoPayService.runAutoPayForOrg(options.orgId, options.dueDateStamp, options.batchStamp, agenda);
}

const runRefunds = async function() {
	const refundInvoices = await Invoices.find({
		"credits": {
			"$elemMatch": {"type": "refund", "refundPended": true, "refundConfirmed":{"$ne": true}, "voidedAt": {"$eq": null}}
		}}).fetchAsync();
	for (const refundInvoice of refundInvoices) {
		console.log("found refundInvoice", refundInvoice._id);
		const paymentProvider = await CardProviders.getPaymentProvider(refundOptions.charge_id);

		if (paymentProvider) {
			for (let i = 0; i < refundInvoice.credits.length; i++ ) {
				const credit = refundInvoice.credits[i];

				if ( credit.type == "refund" && credit.refundPended && !credit.refundConfirmed && !credit.voidedAt ) {
					const refundOptions = {
						orgId: refundInvoice.orgId,
						charge_id: credit.originalReference,
						refund_amount: credit.amount,
						invoiceId: refundInvoice._id,
						isRetry: true,
						retryCreditIndex: i
					};
					try {
						console.log("found refund to process", refundOptions);
						const refundResult = await paymentProvider.refundCharge(refundOptions);
					} catch (err) {
						console.log("Error with refund Invoice: ", err);
					}
				}
			}
		}
	}
}

export const queuePayment = async function({paymentOptions, invoice, familyPerson}) {
	const paidByDesc = familyPerson.firstName + " " + familyPerson.lastName,
		 paymentLine = {
			type: "payment",
			payment_type: "bank_account",
			amount: paymentOptions.payment_amount,
			createdAt: new Date().valueOf(),
			paidBy: paymentOptions.personId,
			paidByDesc: paidByDesc,
			queueInfo: {
				batchStamp: paymentOptions.batchStamp,
				processed: false
			}
		};

	await Invoices.updateAsync({_id: paymentOptions.invoiceId},
		{
			$set: {openAmount: currency(invoice.openAmount).subtract(paymentOptions.payment_amount).value},
			$push: {credits: paymentLine}
		}
	);
	return paymentLine;
}

export const generateEmailWhiteLabelData = function(currentOrg) {

	const whiteLabelLogo = _.deep(currentOrg, "whiteLabel.assets.emailLogo") || "http://assets.momentpath.com/IMG_1047.png",
	whiteLabelPrimaryColor = _.deep(currentOrg, "whiteLabel.primaryColor") || "#AC52DB",
	whiteLabelSecondaryColor = _.deep(currentOrg, "whiteLabel.secondaryColor") || "#6EC3CC",
	whiteLabelAndroidAppUrl = _.deep(currentOrg, "whiteLabel.androidAppUrl") || "https://play.google.com/store/apps/details?id=ly.tend.tendly.tendlyapp",
	whiteLableiosAppUrl = _.deep(currentOrg, "whiteLabel.iosAppUrl") || "https://apps.apple.com/us/app/momentpath/id1086197417",
	whileLabelEmailAssetPrefix = _.deep(currentOrg, "whiteLabel.emailAssetPrefix") || "v2021";
	return {
		orgName: currentOrg?.name ?? 'Unknown',
		longName: currentOrg?.getLongName() ?? 'Unknown',
		primaryColor: whiteLabelPrimaryColor,
		secondaryColor: whiteLabelSecondaryColor,
		logo: whiteLabelLogo,
		androidAppUrl: whiteLabelAndroidAppUrl,
		iosAppUrl: whiteLableiosAppUrl,
		emailAssetPrefix: whileLabelEmailAssetPrefix
	};
}

const insertCallout = async function(item) {
	const currentOrgId = (await Orgs.current() && (await Orgs.current())._id) || item.targetOrgId,
		mappedItem = {
			_id: Random.id(),
			title: item.title,
			message: item.message,
			targetLink: item.targetLink,
			targetLinkLabel: item.targetLinkLabel,
			createdAt: Date.now()
		},
		peopleTargetTypes = _.intersection(item.peopleTargetTypes || ["admin"], ["admin", "staff", "family"]);

	if (!currentOrgId) return;

	await People.updateAsync({orgId: currentOrgId, type:{"$in": peopleTargetTypes}, inActive:{"$ne": true}}, {"$push": {callouts: mappedItem}}, {multi: true});
};

export const runDailyLedgerHistoryCapture = async function() {
	const ledgerOrgs = await Orgs.find({"billing.ledgerAccountCodes": {"$exists": true}}).fetchAsync();

	console.log("starting capture");

	for (let orgIndex = 0; orgIndex < ledgerOrgs.length; orgIndex++) {
		const org = ledgerOrgs[orgIndex],
			thisDate = new moment.tz(org.getTimezone()).add(-1, "days").startOf("day"),
			thisEndDate = thisDate.clone().add(1, "day");
		console.log("saving " + org.name);

		const entries = await MappedLedgerEntriesForRange({
			startDate: thisDate.valueOf(),
			endDate: thisEndDate.valueOf(),
			orgId: org._id,
			includeLinkedDetails: true
		});
		await AuditLedgerBatchesHistorical.insertAsync({
			orgId: org._id,
			date: thisDate.format("YYYY-MM-DD"),
			data: entries,
			createdAt: new Date().valueOf()
		});
	}
	console.log("finished capture");
}

const runAccountHolderEmailUpdate = async function() {

	const orgs = await Orgs.find({"billing.adyenInfo":{"$ne":null}, "whiteLabel.smsPostfix":{"$regex": "lightbridge"}}).fetchAsync();
	console.log("Found org count:", orgs.length);

	for ( let orgIndex = 0; orgIndex < orgs.length; orgIndex++) {
		const org = orgs[orgIndex],
			paymentProviderName = org.billingCardProviderName(),
			paymentProvider = paymentProviderName && await CardProviders.get(paymentProviderName);

		let accountHolderEmail = org.valueOverrides?.chargebackInvoiceEmail;

		if (!accountHolderEmail) {
			accountHolderEmail = await paymentProvider.retrieveAccountEmail(org._id);
		}

		console.log("org:", org.name, "account holder email", accountHolderEmail);
		await Orgs.updateAsync({_id: org._id}, {"$set": {"billing.adyenInfo.accountHolderEmail": accountHolderEmail}});
	}
}

export const runChargebacks = async function() {
	await ChargebackAutomationService.runChargebacks();
}

export const retryChargebacks = async function() {
	await ChargebackAutomationService.retryChargebacks();
}
