import moment from "moment-timezone";
import {Log} from "../lib/util/log";
import { Orgs } from "../lib/collections/orgs";
import { Reservations } from "../lib/collections/reservations";
Meteor.methods({
    async "getActiveRecurringReservations"(options) {
        try {
            const timezone = (await Orgs.current()).getTimezone();
            const now = moment.tz(moment(), timezone);
            const peopleIds = options.peopleIds;
            const includeNextWeekInQuery = options.includeNextWeekInQueryBool;
            let startOfWeek = now.clone().startOf('week').valueOf();
            let endOfSaturday = now.clone().endOf('week').valueOf();

            // If selected to include next week's schedule, find the end of next Sunday
            if (includeNextWeekInQuery) {
                startOfWeek = now.clone().add(1, 'week').startOf('week').valueOf();
                endOfSaturday = now.clone().add(1, 'week').endOf('week').valueOf();
            }

            return await Reservations.find({
                scheduledDate: { $lte: endOfSaturday },
                recurringFrequency: { $exists: true },
                selectedPerson: { $in: peopleIds },
                $or: [
                    { scheduledEndDate: { $gte: startOfWeek } },
                    { scheduledEndDate: { $in: [null, false] } }
                ]
            }).fetchAsync();
        } catch (error) {
            Log.error("Error in 'getActiveRecurringReservations':" + error);
        }
    },
})