import { CardProviders } from '../card_providers/cardProviders';
import { Log } from '../../lib/util/log';
import moment from 'moment-timezone';
import { BillingTimezoneMap } from '../../lib/constants/timezoneConstants';
import <PERSON> from 'papaparse';
import { EmailService } from '../emails/emailService';
import { ConfigurationSettingsService, ServerConfigurationConstants } from '../config/configurationSettingsService';
import { People } from '../../lib/collections/people';
import { processPostPaymentLogic } from '../methodsBilling';
import { queuePayment } from '../util';
import { Orgs } from '../../lib/collections/orgs';
import _ from '../../lib/util/underscore';

export class AutoPayService {
    /**
     * Get invoices for families that have auto-pay setup.
     * This will only check the given org.
     *
     * @param orgId        The org id to check
     * @param dueDateStamp The due date of the invoices that will be checked against
     * @returns {*[
     *     {
     *         invoice: Invoice,
     *         person: Person // The family person that will be charged
     *     }
     * ]}
     */
    static async getAutoPayInvoices(orgId, dueDateStamp) {
        const org = await Orgs.findOneAsync({ _id: orgId });
        const dueDateString = moment.tz(dueDateStamp, org.getTimezone()).format('MM/DD/YYYY');

        const autopayQuery = AutoPayQueryUtils.getPeopleAutoPayQuery(org);
        Log.info("Using autopayQuery", autopayQuery);
        const data = [];
        const autopayQ = await People.find(autopayQuery).fetchAsync();
        for (const familyPerson of autopayQ) {
            try {
                Log.info("Found person with auto pay", familyPerson._id, familyPerson.lastName);
                Log.info("Searching familyPerson for openInvoices with dueDateStamp", dueDateStamp, dueDateString);
                const familyPersonOpenInvoices = await familyPerson.openInvoices({ $or: [{ dueDate: dueDateStamp }, { dueDateString: dueDateString }] })
                for (const invoice of familyPersonOpenInvoices) {
                    Log.info("Found invoice", invoice._id);
                    const amountDueForFamilyMember = invoice.amountDueForFamilyMember(familyPerson._id);
                    if (amountDueForFamilyMember <= 1) {
                        Log.info("amount due is less than 1");
                        continue;
                    }
                    if (invoice.paymentRefused) {
                        Log.info("payment refused");
                        continue;
                    }
                    data.push({
                        invoice: invoice,
                        person: familyPerson
                    });
                }
            } catch (error) {
                Log.error("Couldn't retrieve invoices for person ID: " + familyPerson._id, error.message);
                data.push({
                    invoice: null,
                    person: familyPerson
                });
            }
        }

        return data;
    }

    /**
     * Run auto-pay for an org.
     *
     * @param orgId
     * @param dueDateStamp
     * @param batchStamp
     * @param agenda
     * @returns {
     *      Promise<{
     *          failedPeopleIds: *[], // People who failed to pay; excludes failures caught on individual invoices
     *          paidInvoiceIds: *[], // Invoices that were paid
     *          failedInvoiceIds: *[] // Invoices that failed to pay; doesn't add to failedPeopleIds
     *      }>
     * }
     */
    static async runAutoPayForOrg(orgId, dueDateStamp, batchStamp, agenda) {
        const org = await Orgs.findOneAsync({_id: orgId});
        const dueDateString = moment.tz(dueDateStamp, org.getTimezone()).format('MM/DD/YYYY');

        const paymentProviderName = org.billingCardProviderName();
        Log.info("card provider name: '" + paymentProviderName + "'");
        const paymentProvider = paymentProviderName && await CardProviders.get(paymentProviderName);

        const autopayQuery = AutoPayQueryUtils.getPeopleAutoPayQuery(org);
        Log.info("Using autopayQuery", autopayQuery);
        const allPaidInvoiceIds = [];
        const failedInvoiceIds = [];
        const failedPeopleIds = [];
        const autopayQ = await People.find(autopayQuery).fetchAsync();
        for (const familyPerson of autopayQ) {
            try {
                Log.info("Found person with auto pay", familyPerson._id, familyPerson.lastName);
                let paidAmount = 0.0, paidInvoiceIds = [], serviceCharges = 0.0;
                const personMax = familyPerson.billing.autoPayAmount ? parseFloat(familyPerson.billing.autoPayAmount || 0.0) : null;
                Log.info("Searching familyPerson for openInvoices with dueDateStamp", dueDateStamp, dueDateString);
                for (const invoice of await familyPerson.openInvoices({ $or: [{ dueDate: dueDateStamp }, { dueDateString: dueDateString }] })) {
                    Log.info("Found invoice", invoice._id);
                    const amountDueForFamilyMember = invoice.amountDueForFamilyMember(familyPerson._id);
                    if (amountDueForFamilyMember <= 1) {
                        Log.info("amount due is less than 1");
                        continue;
                    }
                    if (allPaidInvoiceIds.includes(invoice._id) && !invoice.getFamilySplits()) {
                        Log.info("Invoice already paid by other family member");
                        continue;
                    }
                    Log.info("amount due: " + amountDueForFamilyMember);
                    let amountToPay = amountDueForFamilyMember;
                    if (personMax) {
                        const reducedAmount = personMax - paidAmount;
                        amountToPay = _.min([amountDueForFamilyMember, reducedAmount]);
                    }
                    Log.info("new amount to pay: " + amountToPay);

                    if (amountToPay > 0) {
                        try {
                            let paymentLine;

                            const paymentOptions = {
                                personId: familyPerson._id,
                                invoiceId: invoice._id,
                                account_type: familyPerson.billing.autoPay,
                                payment_amount: amountToPay,
                                orgId: familyPerson.orgId,
                                suppressReceipt: true,
                                batchStamp: batchStamp
                            };

                            if (org.hasCustomization("billing/queueAutopayments/enabled")) {
                                // TODO: refactor queuePayment to be a method that can be called and tested from within tests
                                if (typeof queuePayment === 'function') {
                                    paymentLine = await queuePayment({ paymentOptions, invoice, org, familyPerson });
                                }
                            } else {
                                paymentLine = await paymentProvider.payInvoice(paymentOptions);
                            }
                            // TODO: refactor processPostPaymentLogic to be a method that can be called and tested from within tests
                            if (typeof processPostPaymentLogic === 'function') {
                                processPostPaymentLogic({ invoiceId: invoice._id, paymentLine });
                            }
                            paidAmount = paidAmount + amountToPay;
                            if (paymentLine?.serviceCharge) {
                                serviceCharges = serviceCharges + paymentLine.serviceCharge;
                            }

                            paidInvoiceIds.push(invoice._id);
                        } catch (error) {
                            Log.error("Billing Error: Couldn't process autopay for invoice ID: " + invoice._id, error);
                            failedInvoiceIds.push(invoice._id);
                        }
                    }
                }
                allPaidInvoiceIds.push(...paidInvoiceIds);
                // TODO: refactor agenda to be be callable from within tests
                if (paidInvoiceIds.length > 0 && typeof agenda !== 'undefined') {
                    Log.info("Enqueuing autopay_receipt email for person", familyPerson._id, familyPerson.lastName);
                    agenda.now("enqueueBillingEmail", {
                        emailType: "autopay_receipt",
                        familyPersonId: familyPerson._id,
                        invoices: paidInvoiceIds,
                        paidAmount: paidAmount
                    });
                }
            } catch (error) {
                Log.error("Billing Error: Couldn't process autopay for person ID: " + familyPerson._id, error);
                failedPeopleIds.push(familyPerson._id);
            }
        }

        return {
            paidInvoiceIds: allPaidInvoiceIds,
            failedInvoiceIds,
            failedPeopleIds
        }
    }

    /**
     * Check for unpaid invoices that should have been paid by auto-pay.
     * Send an email containing a list of unpaid invoices to the billing errors email.
     *
     * @param runOptions
     */
    static async runAutoPayMonitoring(runOptions) {
        let dateString = runOptions?.dateString;
        if (!dateString) {
            dateString = moment().format('MM/DD/YYYY');
        }
        let filename = 'unpaid_invoices_' + dateString.replace(/\//g, '-');

        const orgQuery = { 'billing.enabled': true, 'billing.scheduling': { '$exists': true } };
        if (runOptions && runOptions.orgs) {
            orgQuery['_id'] = { '$in': runOptions.orgs };
        } else {
            if (runOptions && runOptions.timezone && BillingTimezoneMap[runOptions.timezone]) {
                orgQuery['timezone'] = BillingTimezoneMap[runOptions.timezone];
                filename += '_' + runOptions.timezone;
            } else {
                const timezoneList = _.map(BillingTimezoneMap, (v, k) => v);
                orgQuery['timezone'] = { $nin: timezoneList };
                filename += '_others';
            }
        }

        Log.info('billing orgQuery', orgQuery);

        const unpaidAutoPayInvoices = [];
        const billingOrgs = await Orgs.find(orgQuery, { disableOpLog: true }).fetchAsync();
        const orgsMap = new Map();
        for(const org of billingOrgs){
            Log.info("Found org: " + org.name);
            orgsMap.set(org._id, org);
            moment.tz.setDefault(org.getTimezone());
            const startStamp = new moment.tz(dateString, 'MM/DD/YYYY', org.getTimezone()).startOf('day').valueOf();
            try {
                const invoices = await AutoPayService.getAutoPayInvoices(org._id, startStamp);
                unpaidAutoPayInvoices.push(...invoices);
            } catch (error) {
                Log.error("Billing Error: Couldn't process autopay for org ID: " + org._id, error.message);
                unpaidAutoPayInvoices.push({ invoice: null, person: null, orgId: org._id });
            }
        };

        if (unpaidAutoPayInvoices.length > 0) {
            Log.info('Found unpaid invoices');
            const columns = [
                'Org id',
                'Org name',
                'Org long name',
                'Parent First Name',
                'Parent Last Name',
                'Invoice #',
                'invoice id',
                'Invoice Amount',
                'Balance Due'
            ];
            const data = [];
            let currentOrg = null;
            for (const invoiceData of unpaidAutoPayInvoices) {
                Log.debug("invoiceData", invoiceData);
                currentOrg = orgsMap.get(invoiceData.invoice?.orgId ?? (invoiceData.person?.orgId ?? invoiceData.orgId));
                data.push([
                    currentOrg?._id ?? 'Unknown',
                    currentOrg?.name ?? 'Unknown',
                    currentOrg?.longName ?? 'Unknown',
                    invoiceData.person?.firstName ?? 'Unknown',
                    invoiceData.person?.lastName ?? 'Unknown',
                    invoiceData.invoice?.invoiceNumber ?? 'Unknown',
                    invoiceData.invoice?._id ?? 'Unknown',
                    invoiceData.invoice?.originalAmount ?? 'Unknown',
                    invoiceData.invoice?.openAmount ?? 'Unknown'
                ]);
            }
            const content = Papa.unparse({ fields: columns, data });
            const attachments = [{ filename: filename + '.csv', content }];
            const billingErrorsEmail = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.BILLING_ERRORS_EMAIL);
            Log.info('Sending email to billing errors email', billingErrorsEmail);
            await EmailService.sendSystemAdminEmail(
                undefined,
                'systemNotificationEmail',
                'email_templates/system_notification_email.html',
                '<<EMAIL>>',
                billingErrorsEmail,
                'Auto-pay failed on date ' + dateString,
                { bodyMessage: 'Please charge the attached invoices, as their auto-pay failed.' },
                { attachments: attachments }
            );
        } else {
            // No unpaid invoices, send success email
            const dateString = moment().format('MM/DD/YYYY');
            const timezone = runOptions?.timezone;
            const billingErrorsEmail = ConfigurationSettingsService.getServerConfigurationSetting(ServerConfigurationConstants.BILLING_ERRORS_EMAIL);
            Log.info('Sending success email to billing errors email', billingErrorsEmail);
            await EmailService.sendSystemAdminEmail(
                undefined,
                'systemNotificationEmail',
                'email_templates/system_notification_email.html',
                '<<EMAIL>>',
                billingErrorsEmail,
                'Autopay successful on date ' + dateString,
                { bodyMessage: 'Autopay ran successfully for ' + timezone + '. No further action is needed.' },
                { attachments: [] }
            );
        }
    }
}

/**
 * Helps with querying for people who are eligible for auto-pay.
 * Can be treated as a private class to be used by AutoPayService.
 */
class AutoPayQueryUtils {
    /**
     * Returns a query that can be used to find people who are eligible for auto-pay.
     *
     * @param org
     * @returns {{}}
     */
    static getPeopleAutoPayQuery(org) {
        Log.info("searching auto payment people who are active");
        const autopayQuery = {
            orgId: org._id,
            type: "family",
            inActive: { $ne: true },
            "$or": [{ "billing.autoPay": { $exists: true } }]
        };
        if (org.hasCustomization("billing/queueAutopayments/enabled")) {
            const paymentProviderName = org.billingCardProviderName();
            Log.info("card provider name: '" + paymentProviderName + "'");
            const otherAutopayersQueryFragment = {};
            otherAutopayersQueryFragment[`billing.${ paymentProviderName }Info.sources.bank_account`] = { "$exists": true };
            autopayQuery["$or"].push(otherAutopayersQueryFragment);
        }
        return autopayQuery;
    }
}
