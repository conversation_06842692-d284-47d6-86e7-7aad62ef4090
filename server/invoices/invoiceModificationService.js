import { Meteor } from 'meteor/meteor';
import { BillingUtils } from '../../lib/util/billingUtils';
import moment from 'moment-timezone';
import { AwsBillingService } from '../awsBillingService';
import { InvoiceUpdateService } from '../../lib/invoiceUpdateService';
import { Invoices } from '../../lib/collections/invoices';
import { People } from '../../lib/collections/people';

export class InvoiceModificationService {
    static async reallocatePayerBalance(options, currentUser, org) {
        const { invoiceId, amount, lineItemId } = options;
        const allocationMap = {
            "bad-debt": "bad_debt",
            "agency": "agency_write_off",
            "collections": "collections_write_off"
        };

        let messages = "";
        if (isNaN(amount)) {
            throw new Meteor.Error("You must specify a valid amount for this reallocation.");
        }

        const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: invoiceId });

        if (!invoice) {
            throw new Meteor.Error("This invoice is not found or is not editable.");
        }

        if (options["reallocate_from"] === options["reallocate_to"]) {
            throw new Meteor.Error("You cannot reallocate from one entity to the same entity.");
        }

        const selectedLineItemIndex = invoice.lineItems?.findIndex(li => li._id === lineItemId);
        const selectedLineItem = invoice.lineItems[selectedLineItemIndex];
        const changeAmount = parseFloat(amount);
        const updateQuery = { "$inc": {} };

        let netChange = 0;
        let selectedAllocation = options["reallocate_from"];
        let sourceLabel = selectedAllocation;

        if (selectedAllocation !== "family") {
            selectedAllocation = selectedLineItem?.appliedDiscounts[options["reallocate_from"]];

            if (!selectedAllocation) {
                throw new Meteor.Error("Cannot find line item or payer allocation.");
            }

            if (changeAmount > selectedAllocation.amount) {
                throw new Meteor.Error("Cannot reallocate more than original payer amount.");
            }

            if (changeAmount > invoice.openAmountForPayer(selectedAllocation.source)) {
                throw new Meteor.Error("Cannot reallocate more than open amount for payer.");
            }

            const updatePayerKey = "openPayerAmounts." + selectedAllocation.source;
            updateQuery["$inc"][updatePayerKey] = BillingUtils.roundToTwo(-1 * changeAmount);
            sourceLabel = selectedAllocation.source;
        } else {
            if (changeAmount > invoice.openAmount) {
                throw new Meteor.Error("Cannot reallocate more than open family balance.");
            }

            netChange = -1 * changeAmount;
        }

        let targetAllocation = options["reallocate_to"];
        let destinationLabel = targetAllocation;
        let newInvoiceId;

        if (targetAllocation === selectedAllocation) {
            throw new Meteor.Error("Source and target allocation cannot be the same.");
        }

        if (targetAllocation !== "family" && !(Object.keys(allocationMap).includes(targetAllocation))) {
            targetAllocation = selectedLineItem?.appliedDiscounts[options["reallocate_to"]];

            const updatePayerKey = "openPayerAmounts." + targetAllocation.source;
            updateQuery["$inc"][updatePayerKey] = BillingUtils.roundToTwo(changeAmount);
            destinationLabel = targetAllocation.source;
            updateQuery["$push"] = {
                "credits": {
                    type: "credit",
                    amount: BillingUtils.roundToTwo(changeAmount),
                    createdAt: new Date().valueOf(),
                    creditedBy: currentUser.personId,
                    creditReason: "reallocation_to_payer",
                    payerDestination: targetAllocation.source
                }
            };
        } else if (targetAllocation === "family") {
            const itemId = Random.id();
            const pendingCharge = {
                "_id": itemId,
                "originalItem": {
                    "description": options["invoice-description"],
                    "amount": changeAmount,
                    "_id": itemId,
                    "type": "item",
                    "reallocationInvoiceId": invoice._id,
                    "reallocationSource": selectedAllocation
                },
                "createdAt": new Date().valueOf(),
                "price": changeAmount,
                "quantity": 1,
                "type": "item"
            };

            await People.updateAsync({ _id: invoice.personId }, { "$push": { "billing.pendingCharges": pendingCharge } })

            try {
                const invoiceOptions = {
                    orgId: org._id,
                    personId: invoice.personId,
                    allowItemsOnly: true,
                    manualInvoiceDueDate: options["invoice-due-date"] && new moment(options["invoice-due-date"], "YYYY-MM-DD").format("MM/DD/YYYY"),
                    forceInactiveInvoice: true,
                };

                newInvoiceId = await AwsBillingService.createSingleInvoiceWithReturn(invoiceOptions);
            } catch (e) {
                messages += "There was an issue with generating the invoice: " + e.message + ". The pending charge will still show on the child's profile";
            }
        } else if (Object.keys(allocationMap).includes(targetAllocation)) {
            if (selectedAllocation === "family") {
                updateQuery["$push"] = {
                    "credits": {
                        type: "credit",
                        amount: BillingUtils.roundToTwo(changeAmount),
                        createdAt: new Date().valueOf(),
                        creditedBy: currentUser.personId,
                        creditReason: allocationMap[targetAllocation]
                    }
                };
            }
        }

        if (!updateQuery["$push"]) updateQuery["$push"] = {};
        updateQuery["$push"]["allocationEntries"] = {
            source: sourceLabel,
            destination: destinationLabel,
            amount: BillingUtils.roundToTwo(changeAmount),
            createdAt: new Date().valueOf(),
            createdBy: currentUser.personId,
            createdInvoice: newInvoiceId ?? null
        }
        if (netChange !== 0) {
            updateQuery["$inc"]["openAmount"] = BillingUtils.roundToTwo(netChange);
        }

        await InvoiceUpdateService.updateByQueryWithJournalEntry({ _id: invoice._id }, updateQuery, {
            userId: currentUser._id,
            personId: currentUser.personId,
            orgId: currentUser.orgId,
            reason: `Reallocated reimbursement of $${changeAmount} from ${sourceLabel} to ${destinationLabel}`,
            reasonLocation: `server/methods.js:reallocatePayer`
        });
        messages += " Invoice updated successfully.";
        return messages;
    }

    /**
     * Builds the update query for modifying a credit line in an invoice.
     *
     * @param {Object} invoice - The invoice containing credits.
     * @param {Object} options - Modification options.
     * @param {number} options.existingCreditIndex - The index of the credit being modified.
     * @param {string} options.credit_amount - The new credit amount.
     * @param {string} options.credit_note - The updated credit note.
     * @param {Object} currentUser - The user performing the modification.
     * @param {string} currentUser.personId - The ID of the user making the modification.
     * @returns {Object} - The updated credit line, amount adjustment, and update query.
     */
    static getUpdateCreditLineAndQueryForModifiedCredit(invoice, options, currentUser) {
        const creditLine = invoice.credits[options.existingCreditIndex];
        const timestamp = Date.now();
        const creditKeyBase = `credits.${options.existingCreditIndex}`;
        const newAmount = BillingUtils.roundToTwo(parseFloat(options.credit_amount));
        const amountAdjustment = this.calculateAmountAdjustment(creditLine.amount, newAmount);

        // Ensure modification history exists
        const modificationHistory = creditLine.modificationHistory || [];
        const totalModifiedAmount = this.calculateTotalModifiedAmount(modificationHistory, amountAdjustment);

        const query = {
            "$set": {
                ...this.buildBaseUpdateSet(creditKeyBase, newAmount, options.credit_note, currentUser, timestamp, totalModifiedAmount),
            },
            "$push": {
                [`${creditKeyBase}.modificationHistory`]: this.buildModificationRecord(newAmount, amountAdjustment, currentUser, timestamp)
            }
        };

        // Capture original amount only on first modification
        if (this.shouldStoreOriginalAmount(creditLine, modificationHistory)) {
            query["$set"][`${creditKeyBase}.originalAmount`] = creditLine.amount;
            query["$set"][`${creditKeyBase}.isModified`] = true;
        }

        return { creditLine, amountAdjustment, query };
    }

    /**
     * Calculates the adjustment amount for a modified credit.
     *
     * @param {number} oldAmount - The original credit amount.
     * @param {number} newAmount - The new credit amount.
     * @returns {number} - The rounded amount adjustment.
     */
    static calculateAmountAdjustment(oldAmount, newAmount) {
        return BillingUtils.roundToTwo(newAmount - oldAmount);
    }

    /**
     * Computes the total modified amount including the new adjustment.
     *
     * @param {Array<Object>} modificationHistory - The history of previous modifications.
     * @param {number} amountAdjustment - The newly adjusted amount.
     * @returns {number} - The cumulative modified amount.
     */
    static calculateTotalModifiedAmount(modificationHistory, amountAdjustment) {
        return modificationHistory.reduce((total, record) => total + record.amountModified, amountAdjustment);
    }

    /**
     * Builds a modification record for tracking changes to a credit.
     *
     * @param {number} newAmount - The new credit amount after modification.
     * @param {number} amountModified - The difference from the previous amount.
     * @param {Object} currentUser - The user making the modification.
     * @param {string} currentUser.personId - The ID of the user making the modification.
     * @param {number} timestamp - The timestamp of the modification.
     * @returns {Object} - A structured modification record.
     */
    static buildModificationRecord(newAmount, amountModified, currentUser, timestamp) {
        return {
            newAmount,
            amountModified: BillingUtils.roundToTwo(amountModified),
            modifiedBy: currentUser.personId,
            modifiedAt: timestamp
        };
    }

    /**
     * Constructs the base update set for modifying a credit in an invoice.
     *
     * @param {string} creditKeyBase - The base key for the credit in the invoice.
     * @param {number} newAmount - The new credit amount.
     * @param {string} creditNote - The updated credit note.
     * @param {Object} currentUser - The user making the modification.
     * @param {string} currentUser.personId - The ID of the user making the modification.
     * @param {number} timestamp - The timestamp of the modification.
     * @param {number} totalModifiedAmount - The total amount modified after all adjustments.
     * @returns {Object} - The `$set` update object for the database query.
     */
    static buildBaseUpdateSet(creditKeyBase, newAmount, creditNote, currentUser, timestamp, totalModifiedAmount) {
        return {
            [`${creditKeyBase}.amount`]: newAmount,
            [`${creditKeyBase}.creditNote`]: creditNote,
            [`${creditKeyBase}.updatedBy`]: currentUser.personId,
            [`${creditKeyBase}.updatedAt`]: timestamp,
            [`${creditKeyBase}.modifiedAmount`]: totalModifiedAmount,
        };
    }


    /**
     * Determines if the original amount should be stored.
     *
     * @param {Object} creditLine - The credit line being modified.
     * @param {Array<Object>} modificationHistory - The history of previous modifications.
     * @returns {boolean} - `true` if the original amount should be stored, otherwise `false`.
     */
    static shouldStoreOriginalAmount(creditLine, modificationHistory) {
        return !creditLine.originalAmount && modificationHistory.length === 0;
    }
}