import <PERSON> from "papaparse";
import { StringUtils } from "../../lib/util/stringUtils";
import { AdyenMarketpayPayouts } from "../../lib/collections/adyenMarketpayPayouts";
import { AdyenMarketplacePayments } from "../../lib/collections/adyenMarketplacePayments";
import { AdyenReports } from "../../lib/collections/adyenReports";

export class AdyenReportsService {
    static get CHUNK_SIZE() {
        return 5000;
    }
    static get REPORT_TYPES()  {
        return {
            PAYOUT: 'marketpay_payout',
            PAYMENTS: 'marketplace_payments_accounting_report',
        }
    }

    /**
     * Given a report name, createdAt, date, and content, save the report to the AdyenReports collection with
     * chunking
     * @param name
     * @param createdAt
     * @param date
     * @param content
     * @param customChunkSize
     * @return {*[]}
     */
    static async saveReport(name, createdAt, date, content, customChunkSize = null) {
        console.log('saving adyen report', { name, createdAt, date });
        if (!name || !createdAt || !date || !content) {
            console.log('huh?');
            return [];
        }
        if (name !== this.REPORT_TYPES.PAYOUT && name !== this.REPORT_TYPES.PAYMENTS) {
            console.log('unsupported report type');
            return [];
        }
        const rows = content.split("\n");
        if (rows.length <= 1) {
            return [];
        }
        if (name === this.REPORT_TYPES.PAYOUT) {
            rows.shift();
        }
        if (rows.length <= 1) {
            return [];
        }
        const header = rows.shift();
        const chunkSize = customChunkSize || this.CHUNK_SIZE;
        const insertedIds = [];
        let chunkCount = 0;
        for (let i = 0; i < rows.length; i += chunkSize) {
            chunkCount++;
            const chunk = rows.slice(i, i + chunkSize);
            chunk.unshift(header);
            insertedIds.push(
                await AdyenReports.insertAsync({
                    name,
                    createdAt,
                    date,
                    content: chunk.join("\n"),
                    chunkCount
                })
            );
        }
        if (insertedIds.length) {
            await AdyenReports.updateAsync({ _id: { $in: insertedIds } }, { $set: { groupId: insertedIds[0] } }, { multi: true });
        }
        return insertedIds;
    }

    static async parseReports(reportIds) {
        for (const reportId of reportIds) {
            await this.parseReport(reportId);
        }
    }

    /**
     * Given an adyenReport id, parse the report into the appropriate collection.
     * @param reportId
     */
    static async parseReport(reportId) {
        const report = await AdyenReports.findOneAsync({ _id: reportId });
        if (!report || report.parsed || report.parsing) {
            return;
        }
        await AdyenReports.updateAsync({ _id: report._id }, { $set: { parsing: true } });
        if (report.name !== this.REPORT_TYPES.PAYOUT && report.name !== this.REPORT_TYPES.PAYMENTS) {
            console.log('unsupported report type');
            return;
        }
        const parseOptions = {
            header: true,
            transformHeader: header => StringUtils.wordsToCamel(header)
        };
        if (report.name === this.REPORT_TYPES.PAYOUT &&
            'Marketpay Payout Report' === report.content.substring(0, 23)) {
            report.content = report.content.substring(24);
        }
        console.log('begin ady parsing ', report.name, report.date);
        const parsed = Papa.parse(report.content, parseOptions);
        if (!parsed?.data?.length) {
            console.log('no data');
            return;
        }
        console.log('csv parsing done');
        for (const row of parsed.data || []) {
            const storedRow = {
                date: report.date,
                createdAt: report.createdAt,
                originalReportId: report._id,
                ...row
            }
            if (report.name === this.REPORT_TYPES.PAYOUT) {
                await AdyenMarketpayPayouts.insertAsync(storedRow);
            } else {
                await AdyenMarketplacePayments.insertAsync(storedRow);
            }
        }
        console.log('parsed rows inserted');
        await AdyenReports.updateAsync({ _id: report._id }, { $set: { parsed: true, parsing: false } });
    }

    static async parseReportsBetweenDays(startDate, endDate) {
        console.log('parsing ady days');
        console.log({startDate, endDate});
        const reports = await AdyenReports.find({ date: { $gte: startDate, $lte: endDate }}, { fields: { _id: 1 }}).fetchAsync();
        for (const report of reports) {
            await this.parseReport(report._id);
        }
        console.log('all parsing finished');
    }

    static async chunkReportsBetweenDays(startDate, endDate, chunkSize) {
        const reports = await AdyenReports.find({ date: { $gte: startDate, $lte: endDate }, chunkCount: {$exists: false }}, { fields: { _id: 1 }}).fetchAsync();
        for (const report of reports) {
            const reportData = await AdyenReports.findOneAsync({ _id: report._id });
            await this.saveReport(reportData.name, reportData.createdAt, reportData.date, reportData.content, chunkSize);
            await AdyenReports.removeAsync({ _id: report._id });
        }
    }
}