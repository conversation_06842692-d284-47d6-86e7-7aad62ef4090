import moment from 'moment-timezone';
import { Groups } from '../lib/collections/groups';
import { Orgs } from '../lib/collections/orgs';
import { Reservations } from '../lib/collections/reservations';
import { EnrollmentsService } from './enrollments/enrollmentsService';
import _ from '../lib/util/underscore';
import { EnrollmentUtils } from '../lib/util/enrollmentUtils';

Meteor.methods({
    'getCurrentAndPriorReservations': async function (filterStartDate, filterEndDate, currentView, _id, defaultGroupId) {
        const org = await Orgs.current();
        const timezone = org.getTimezone();

        if (currentView === "all") {
            let allReservations = await Reservations.findWithRecurrence({
                startDateValue: moment.tz(filterStartDate, "MM/DD/YYYY", timezone).startOf('day').valueOf(),
                endDateValue: moment.tz(filterEndDate, "MM/DD/YYYY", timezone).endOf('day').valueOf(),
                query: { orgId: org._id, selectedPerson: _id },
                options: { sort: { scheduledDate: 1 } }
            });
            return _.filter(allReservations, (r) => {
                return r.scheduledDate >= moment.tz(timezone).startOf("day") || !r.recurringFrequency;
            });
        } else {
            const todayStamp = new moment.tz(timezone).startOf("day").valueOf(),
                existenceFilter = currentView === "prior" ?
                    [
                        { scheduledEndDate: { "$lt": todayStamp } }
                    ]
                    :
                    [
                        { scheduledEndDate: null },
                        { scheduledEndDate: { "$gte": todayStamp } }
                    ];

            const allReservations = await Reservations.find({
                orgId: org._id,
                recurrenceId: { "$exists": false },
                recurringFrequency: { "$exists": true },
                "$or": existenceFilter,
                selectedPerson: _id
            }, { sort: { scheduledDate: 1 } }).fetchAsync();
            if(allReservations.length === 0) return [];

            const getReservation = await Promise.all(allReservations.map(async r => {
                let targetGroup = r.groupId ? await Groups.findOneAsync(r.groupId) : await Groups.findOneAsync(defaultGroupId);
                r.targetGroupName = targetGroup && targetGroup.name;
                if (!r.groupId && targetGroup && targetGroup._id == defaultGroupId) r.targetGroupName += " (default)";
                r.current = r.scheduledDate <= todayStamp && (!r.scheduledEndDate || r.scheduledEndDate >= todayStamp);
                r.linkedPlan = await r.linkedPlan();
                return r;
            }));
            return getReservation;
        }
    },

    'createSelectiveWeekReservations': async function (childId, selectiveWeekPlan) {
        const timezone = (await Orgs.current()).getTimezone();
        try {
            await EnrollmentsService.createSelectiveWeekReservations(childId, selectiveWeekPlan, timezone);
        } catch (e) {
            throw new Meteor.Error('Error creating selective week reservations: ', e.reason || e.message, e.details || '');
        }
    },

    'getReservationsForToday': async function (personId, orgId, timezone) {
        const startOfDay = moment.tz(timezone).startOf('day').valueOf();
		const endOfDay = moment.tz(timezone).endOf('day').valueOf();
        const reservations = await Reservations.findWithRecurrence({
            query: {
                selectedPerson: personId,
                cancellationReason: null,
                orgId: orgId
            },
            startDateValue: startOfDay,
            endDateValue: endOfDay,
        })
        return reservations;
    },
    
    'getNonRecurringSchedulesForChild': async function (childId, org) {
        const itemMatchedSchedules = await Reservations.find({
            orgId: org._id,
            selectedPerson: childId
        }, {sort: {scheduledDate: 1}}).fetchAsync()
        return itemMatchedSchedules;
    },

    /**
     * Util data getter for EnrollmentUtils.buildEnrolledPrograms
     * 
     * @param {String} personId ID of the person that we want enrolled registrations from
     * @param {String} defaultGroupId Default group ID
     * @returns {Array} Returns an array of reservations for further processing
     */
    'getEnrolledPrograms': async function (personId, defaultGroupId) {
        return await EnrollmentUtils.buildEnrolledPrograms(personId, defaultGroupId)
    },

    /**
     * Run a query to get all reservations with a given invoice ID
     * 
     * @param {String} invoiceId ID of the invoice we want reservations for
     * @param {String} personId ID of the person we want reservations for
     * @returns {Array} Returns an array of reservations
     */
    'getReservationsForInvoice': async function (invoiceId, personId) {
        const reservations = await Reservations.find({generatedFromInvoiceID: invoiceId, selectedPerson: personId}).fetchAsync();
        return reservations;
    },

    /**
     * Run a query to get all reservations with a given invoice ID and a given item ID
     * 
     * @param {String} invoiceId ID of the invoice we want reservations for
     * @param {String} billingChargeId ID of the billing charge item we want reservations for
     * @returns {Array} Returns an array of reservations
     */
    'getReservationsForInvoiceWithItemId': async function (invoiceId, billingChargeId, personId) {
        const reservations = await Reservations.find({generatedFromInvoiceID: invoiceId, generatedFromBillingCharge: billingChargeId, selectedPerson: personId}).fetchAsync();
        return reservations;
    },

    /**
     * Delete reservations part of the invoice. This will PERMANENTLY delete those reservations but this will be logged in the audit log.
     * 
     * @param {String} invoiceId ID of the invoice we want to delete reservations for
     * @param {String} personId ID of the person we want to delete reservations for
     */
    'deleteReservationsByInvoiceId': async function (invoiceId, personId) {
        await Reservations.removeAsync({generatedFromInvoiceID: invoiceId, selectedPerson: personId})
    },

    /**
     * Delete reservations tied to a billing charge item. This will PERMANENTLY delete those reservations but this will be logged in the audit log.
     * 
     * @param {String} invoiceId ID of the invoice we want to delete reservations for
     * @param {String} personId ID of the person we want to delete reservations for
     * @param {String} billingChargeId ID of the billingCharge that we credited so we can delete the reservations tied to it.
     */
    'deleteReservationsByInvoiceAndBillingChargeId': async function (invoiceId, personId, billingChargeId) {
        await Reservations.removeAsync({generatedFromInvoiceID: invoiceId, selectedPerson: personId, generatedFromBillingCharge: billingChargeId})
    }
});
