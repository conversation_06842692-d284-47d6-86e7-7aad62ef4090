const AWS = require('aws-sdk');
import crypto from 'crypto';
import pkceChallenge from "pkce-challenge";
import { SsoService } from "./ssoService";
import jwtDecode from "jwt-decode";
import { UsersRepository } from '../api/v2/repositories/usersRepository';
import { getWhitelabel } from '../lib/whitelabelUtils';
import { setAccountFromMail } from './methodAuthService';

const usersRepository = new UsersRepository();
const config = {
    accessKeyId: Meteor.settings.cognitoAccessKey,
    secretAccessKey: Meteor.settings.cognitoSecretKey,
    region: Meteor.settings.cognitoRegion,
}
if (Meteor.settings.cognitoEndpoint) {
    config.endpoint = Meteor.settings.cognitoEndpoint;
}
const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider(config);

Meteor.methods({

    mobileCognitoConfirmPassword({username, resetToken, password}) {
        const params = {
            Username: username,
            ClientId: Meteor.settings.cognitoClientId,
            Password: password,
            ConfirmationCode: resetToken,
        }
        return new Promise((resolve, reject) => {
            cognitoIdentityServiceProvider.confirmForgotPassword(params, (err, result) => {
                if (err) {
                    return reject(new Meteor.Error(403, 'Password does not meet requirements, please ensure you have one capital letter, one non-capital letter, one number, and a minimum of 8 characters.'));
                }
                return resolve(result);
            });
        });
    },

    /**
     * Request password reset email.
     *
     * @param email
     * @returns {boolean} 
     */
    mobileCognitoForgotPassword({username}) {
        const ClientMetadata = {
            product: 'manage',
        };
        const params = {
            ClientMetadata,
            Username: username,
            ClientId: Meteor.settings.cognitoClientId
        }
        return new Promise((resolve, reject) => {
            cognitoIdentityServiceProvider.forgotPassword(params, (err, result) => {
                if (err) {
                    return reject(new Meteor.Error(403, err?.message ?? 'Forgot password failed'));
                }
                return resolve(true);
            });
        });
    },

    /**
     * Login user with Cognito pool with email and password.
     *
     * @param email
     * @param password
     * @returns { token: string }
     */
    mobileCognitoPoolAuth({username, password}) {
        const authenticationDetails = {
            AuthFlow: 'ADMIN_USER_PASSWORD_AUTH',
            UserPoolId: Meteor.settings.cognitoUserPoolId,
            ClientId: Meteor.settings.cognitoClientId,
            AuthParameters: {
                USERNAME: username,
                PASSWORD: password,
            },
            ClientMetadata: {
                product: 'manage',
            }
        }
        return new Promise((resolve, reject) => {
            cognitoIdentityServiceProvider.adminInitiateAuth(authenticationDetails, Meteor.bindEnvironment(async (err, data) => {
                if (err) {
                    console.log('Cognito Error logging in:', err);
                    return reject(new Meteor.Error(403, err?.message ?? 'Unknown error'));
                }
                console.log(data);
                let idToken = null;
                try {
                    idToken = jwtDecode(data.AuthenticationResult?.IdToken);
                } catch (err) {
                    console.log('Cognito Error decoding ID token:', err);
                    return reject(new Meteor.Error(403, 'Error decoding ID token'));
                }
                const email = idToken?.['cognito:username'];
                if (!email) {
                    return reject(new Meteor.Error(403, 'IDP did not return an email address'));
                }

                // fix here
                const user = await usersRepository.findUserByEmail(email);

                if (!user) {
                    return reject(new Meteor.Error(403, `IDP user not found for username ${username} with email ${email}`));
                }

                // Meteor creates a hashed token for each login session. We need to create one here
                const hash = crypto.createHash('sha256');
                const token = Random.secret();
                hash.update(token);
                const hashedToken = {
                    hashedToken: hash.digest('base64'),
                    when: new Date(),
                }
                await Meteor.users.updateAsync({ _id: user._id }, {
                    $push: {
                        'services.resume.loginTokens': hashedToken
                    }
                });
                return resolve({ token, userId: user._id });
            }));

        });
    },

    /**
     * Get the oauth data that will be used for this user.
     *
     * @param location
     * @returns {oauthState: string, oauthCodeVerifier: string, url: string}
     */
    getSsoAuthData(location) {
        const chars = 'abcdef0123456789';
        let nonce = '';
        for (let i = 0; i < 20; i++) {
            nonce += chars.substr(Math.floor((Math.random() * 15)), 1);
        }
        const whiteLabelSite = getWhitelabel(location.host);
        const challenge = pkceChallenge();
        const params = {
            client_id: 'manage',
            response_type: 'code',
            scope: 'openid',
            redirect_uri: location.protocol + '//' + location.host + '/code',
            code_challenge: challenge.code_challenge,
            code_challenge_method: 'S256',
            state: nonce
        };
        if (whiteLabelSite) {
            params.site = location.protocol + '//' + location.host;
        }

        const baseIdp = new URL(Meteor.settings.idpBaseUrl + '/authorize');
        baseIdp.search = new URLSearchParams(params).toString();
        return {
            oauthState: nonce,
            oauthCodeVerifier: challenge.code_verifier,
            url: baseIdp.toString()
        };
    },
    /**
     * Logout of the IDP provider.
     *
     * @param location
     * @returns {string}
     */
    getSsoLogoutUrl(location) {
        const baseIdp = new URL(Meteor.settings.idpBaseUrl + '/logout');
        const params = {
            post_logout_redirect_uri: location.protocol + '//' + location.host
        };
        baseIdp.search = new URLSearchParams(params).toString();
        return baseIdp.toString();
    },
    getSsoUnauthorizedUrl() {
        return `${Meteor.settings.idpBaseUrl}/unauthorized`;
    },
    /**
     * Gets the user _id of the user that matches the code from the IDP.
     *
     * @param code
     * @param codeVerifier
     * @returns {Promise<*|null>}
     */
    async getUserIdFromCode({code, codeVerifier, location}) {
        const token = await SsoService.getTokenFromCode(code, codeVerifier, location);
        const tokenClaims = await SsoService.decodeIdpToken(token['access_token']);
        const email = tokenClaims['email'];
        const user = await usersRepository.findUserByEmail(email);
        return user ? user._id : null;
    },
    /**
     * Gets the user from an email input and returns a user without a verified email to resend invitations
     * 
     * @param email
     */
    async getPendingUserFromEmail({email}) {
        const escapedEmail = email.replace(/[.+*?^${}()|[\]\\]/g, '\\$&').trim()
        const existingUserQuery = { 'emails.address': { $regex: `^${ escapedEmail }$`, $options: 'i' } };
        const user = await Meteor.users.findOneAsync(existingUserQuery)
        return user
    },

    /**
     * 
     * @param {string} email
     * @returns true 
     */
    setAccountFromMail,
    
});
