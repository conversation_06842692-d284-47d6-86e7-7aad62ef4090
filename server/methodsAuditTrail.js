import { normalizeMethodArgs, shouldSkipAudit } from '../lib/audit/auditTrailUtils';

const moment = require('moment-timezone');
const {HistoryAuditService} = require('./historyAuditService');
const { AuditTrails } = require('../lib/collections/auditTrail');
import _ from '../lib/util/underscore';
import { AuditTrailLogger } from './audit/auditTrailLogger';
const { TimeCards } = require('../lib/collections/timeCards');
const { Invoices } = require('../lib/collections/invoices');

Meteor.methods({
    async getAuditTrailForVoidedTimeCard(timeCardId, voidedByUser) {
        this.unblock();
        const currentUser = await Meteor.userAsync();
        const audits = await AuditTrails.find(
          {
              'args.timeCardId': timeCardId,
              userId: voidedByUser,
              methodName: 'voidTimeCard',
              orgId: currentUser.orgId
          },
          {
              fields: {
                  date: 1
              }
          }
        ).fetchAsync() || [];

        if (audits.length > 0) {
            // Update the time card with the date from the first audit trail
            await TimeCards.updateAsync(
              { _id: timeCardId },
              { $set: { voidedAt: audits[0].date } }
            );
            return audits; // Return the audits
        }

        return []; // Return an empty array if no audits were found
    },
    async getHistoryForPerson(personId) {
        if (!personId) {
            throw new Meteor.Error('400', 'Person ID is required');
        }

        try {
            return await HistoryAuditService.getHistoryForPerson(personId);
        } catch (e) {
            throw new Meteor.Error(e.message || e.reason);
        }
    }
})

Meteor.afterAllMethods(async function (...args) {
    const methodName = this?._methodName;
    const user = await Meteor.userAsync();

    if (shouldSkipAudit(methodName, this.error)) {
        return;
    }

    const normalizedArgs = normalizeMethodArgs(args);

    Meteor.defer(() => {
        AuditTrailLogger.log({ methodName, args: normalizedArgs, user });
    });
});
