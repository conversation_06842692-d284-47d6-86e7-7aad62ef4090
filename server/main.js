import { Meteor } from 'meteor/meteor';
import { DDPRateLimiter } from 'meteor/ddp-rate-limiter'
import {WebApp} from "meteor/webapp";
import { Promise } from 'meteor/promise';
import { People } from '../lib/collections/people';
import { RefreshTokens } from '../lib/collections/refreshTokens';
import { ZkTecoErrors } from './collections/ZkTecoErrors';
import { HistoryAudits } from './collections/historyAudits';
import { SummaryEmailDeferrals } from './collections/summaryEmailDeferrals';
import { AdyenNotifications } from './collections/adyenNotifications';
import { AdyenMarketpayPayouts } from '../lib/collections/adyenMarketpayPayouts';
import { AdyenMarketplacePayments } from '../lib/collections/adyenMarketplacePayments';
import { AdyenReports } from '../lib/collections/adyenReports';
import { AdyenTransactions } from '../lib/collections/adyenTransactions';
import { BalancePayoutTransactions } from '../lib/collections/balancePayoutTransactions';
import { BalanceTransfers } from '../lib/collections/balanceTransfers';
import { Payments } from '../lib/collections/payments';
import { Curriculums } from '../lib/collections/curriculum';
import './slingshot';
/**meteor methods import -start */
import '../lib/methods';
import './methods';
import './methodsActivities';
import './methodsAirslate';
import './methodsAuditTrail';
import './methodsAuth';
import './methodsBilling';
import './methodsBillingAdyen';
import './methodsBillingAdyenBalancePlatform';
import './methodsBillingOverview';
import './methodsBillingStripe';
import './methodsCurriculum';
import './methodsCampaigns';
import './methodsCrm';
import './methodsDashboard';
import './methodsEnrollment';
import './methodsEmail';
import './methodsEnrollment';
import './methodsGroups';
import './methodsHubspot';
import './methodsImport';
import './methodsKinderconnect';
import './methodsMessages';
import './methodsPush';
import './methodsPeople';
import './methodsPubs';
import './methodsRegistration';
import './methodsReports';
import './methodsReservations';
import './methodsRoles';
import './methodsSalesforce';
import './methodsSignInGrid';
import './methodsSuperAdmin';
import './methodsTimecards';
import './methodsTinyMce';
import './methodsZkTeco';
import './mpNextMethods';
import './multiSiteSwitch';
import './punchCardService';
import './queuedReports';
import './serverLogger';
import './manualMigrations/methodsManualMigration';
import './orgs/propagateSettings';
import './settings/cancellationReasonMethods';
import './settings/orgAvailableTagsMethods';
import './settings/designationsMethods';
import './settings/paymentSettingsMethods';
import './methodsRelationships';
import './methodsUser';
import './settings/earlyPickDropReasonMethods';
import '../server/importers/methods'
import './monitoring/performanceMonitoring'
/**meteor methods import -end */

import './publications';
import './smtp';
import '../api/v2/routes/prod/routes';
import './route';
import '../api/v2/routes/dev/devRoutes';
import '../api/v2/reports/attendanceRoutes';
import '../api/v2/reports/customRoutesStream';
import '../api/v2/reports/invoicesRoutes';
import '../api/v2/reports/momentsRoutes';
import '../api/v2/reports/orgRoutes';
import '../api/v2/reports/peopleRoutes';
import './syncedCron';
import './methodsEnrollment';

import { setupPeriodicChecks } from './monitoring/performanceMonitoring';
import { ReportHashes } from "../lib/collections/reportHashes";
import { ReportSaves } from "../lib/collections/reportSaves";
import { CustomerBillingPlanMappings } from '../lib/collections/customerBillingPlanMappings';
import '../lib/collections/payerFunds';
import '../lib/collections/payerReconciliationBatches';

// Import Redis Oplog - make sure it's available
// import { RedisOplog } from 'meteor/cultofcoders:redis-oplog';

Mongo.Collection.prototype.aggregate = async function (pipelines, options = {}) {
  return await this.rawCollection().aggregate(pipelines, options)
}
String.prototype.capitalizeFirstLetter = function () {
	return this.charAt(0).toUpperCase() + this.slice(1);
}

const connectHandler = WebApp.handlers;
Meteor.startup(() => {
  // code to run on server at startup
  console.log("in server startup");
  // Initialize Redis Oplog
  /*
  console.log("Initializing Redis Oplog with ioredis in cluster mode...");
  try {
    const redisOplogConfig = Meteor.settings.redisOplog;
    if (redisOplogConfig) {
      console.log("Redis Oplog config found, using ioredis cluster mode for pub/sub operations");
      // Ensure we're using ioredis with cluster mode
      RedisOplog.init({
        ...redisOplogConfig,
        clientPackage: 'ioredis',
        cluster: true,
        debug: true,
        verbose: true,
        logLevel: 'warn'
      });
      console.log("Redis Oplog with ioredis cluster mode initialized successfully");
    } else {
      console.error("Redis Oplog config not found in settings!");
    }
  } catch (error) {
    console.error("Error initializing Redis Oplog:", error);
  }
  */
  setupPeriodicChecks();

  connectHandler.use(function(req, res, next) {
    // Allow anybody to embed us
    res.setHeader("Access-Control-Allow-Origin", "*");
    return next();
  });

  Accounts.removeDefaultRateLimit()


  People.rawCollection().createIndex({ "orgId": 1, "employeeId": 1 });
  RefreshTokens.rawCollection().createIndex({ "expiresAt": 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });
  RefreshTokens.rawCollection().createIndex({ "token": 1 }, { unique: true });
  ZkTecoErrors.rawCollection().createIndex({ "personId": 1, "superseded": 1 });
  ZkTecoErrors.rawCollection().createIndex({ "retried": 1, "superseded": 1, "code": 1 });
  HistoryAudits.rawCollection().createIndex({ 'orgId': 1, 'personId': 1 });
  SummaryEmailDeferrals.rawCollection().createIndex({ 'orgId': 1, 'personId': 1, 'processed': 1 });
  AdyenNotifications.rawCollection().createIndex({ 'eventType': 1 });
  AdyenMarketpayPayouts.rawCollection().createIndex({ 'account': 1, 'date': 1 } );
  AdyenMarketplacePayments.rawCollection().createIndex({ 'account': 1, 'date': 1 } );
  AdyenMarketpayPayouts.rawCollection().createIndex({ 'originalReportId': 1 } );
  AdyenMarketplacePayments.rawCollection().createIndex({ 'originalReportId': 1 } );
  AdyenReports.rawCollection().createIndex({ 'groupId': 1 } );
  AdyenTransactions.rawCollection().createIndex({ 'response.accountHolderCode': 1 } );
  BalancePayoutTransactions.rawCollection().createIndex({
    'balanceAccount': 1,
    'rollingBalance': 1,
    'category': 1,
    'status': 1,
    'type': 1,
    'bookingDate': 1
  });
  BalancePayoutTransactions.rawCollection().createIndex({ 'transactionId': 1 });
  BalancePayoutTransactions.rawCollection().createIndex({ 'balanceAccount': 1, 'payoutTransactionId': 1 });
  BalanceTransfers.rawCollection().createIndex({ 'categoryData.pspPaymentReference': 1 } );
  Payments.rawCollection().createIndex({ 'pspReferenceId': 1 });
  ReportHashes.rawCollection().createIndex({ 'hash': 1 });
  ReportSaves.rawCollection().createIndex({ 'personId': 1 });
  CustomerBillingPlanMappings.rawCollection().createIndex({ 'programName': 1, 'sessionTypeName': 1 });



  // Define a rule that matches dashboardMomentsWithOptions subscription.
  const dashboardMomentRule = {
    type: 'subscription',
    name: 'dashboardMomentsWithOptions'
  };

  // Add the rule, allowing up to 20 messages every 10000 milliseconds.
  DDPRateLimiter.addRule(dashboardMomentRule, 20, 10000, async (reply, input) => {
    if (!reply.allowed) {
      await Meteor.users.updateAsync({ _id: input.userId }, {$set: {"services.resume": {}}});
    }
  });

  Accounts.registerLoginHandler('LineLeaderIdp', async (loginRequest) => {
    if (!loginRequest.lineLeaderIdp || !loginRequest.userId) {
      return undefined;
    }
    try {
      let stampedToken = Accounts._generateStampedLoginToken();
      let hashStampedToken = Accounts._hashStampedToken(stampedToken);
      await Meteor.users.updateAsync({ _id: loginRequest.userId }, {
        $push: { "services.resume.loginTokens": hashStampedToken }
      });
      return {
        userId: loginRequest.userId,
        token: stampedToken.token
      };
    } catch(err) {
      console.log("Login request error");
      return undefined;
    }
  });

});
