import AWS from "aws-sdk";
import { Meteor } from 'meteor/meteor';
const { v4: uuidv4 } = require("uuid");

const SNS = new AWS.SNS({
  region: Meteor.settings.AWSRegion,
  accessKeyId: Meteor.settings.mpAWSaccessKey,
  secretAccessKey: Meteor.settings.mpAWSsecretKey,
});

export const sendSMSMessage = async (phoneNumber, message, originationNumber) => {
  try {
    //create sqs message to send for processing
    const SQS = new AWS.SQS({
      region: Meteor.settings.AWSRegion,
      accessKeyId: Meteor.settings.mpAWSaccessKey,
      secretAccessKey: Meteor.settings.mpAWSsecretKey,
    });
    // strip non-digits from phone number to use as groupId
    const groupId = phoneNumber.replace(/\D/g, "");

    const sqsMessage = {
      QueueUrl: Meteor.settings.mpSMSQueueUrl,
      DelaySeconds: 0,
      MessageBody: JSON.stringify({
        destinationNumber: phoneNumber,
        originationNumber:
            originationNumber ?? Meteor.settings.defaultOriginationNumber,
        message: message,
      }),
      MessageGroupId: groupId,
      MessageDeduplicationId: uuidv4(),
    };
    const result = await SQS.sendMessage(sqsMessage).promise();
    console.log("SQS Send Results:", result);
  } catch (error) {
    console.log("SQS Send Error:", error);
  }
};


export const deleteEndpointArn = async (arn) => {
  try {
    const result = await SNS.deleteEndpoint({ EndpointArn: arn }).promise();
    console.log("SNS AWS delete endpoint result:", result);
  } catch (error) {
    console.log("AWS SNS delete error:", error);
  }
};

const createSNSPushMessage = ({ title, body, badge }) => {
  const apsBody = {
    alert: {
      title,
      body,
    },
    sound: "default",
  };

  const fcmBody = {
    title,
    body,
    sound: "default",
  };

  if (badge) {
    apsBody.badge = badge;
    fcmBody.badge = `${badge}`;
  }

  const iosMessage = JSON.stringify({
    aps: apsBody,
  });

  const fcmMessage = JSON.stringify({
    notification: fcmBody,
  });

  return JSON.stringify({
    APNS: iosMessage,
    GCM: fcmMessage,
  });
};

export const sendPushNotification = async (endpointArn, messageDetails) => {
  try {
    console.log("preparing AWS SNS send to endpointARN:", endpointArn);

    //create sqs message to send for processing
    const params = {
      Message: createSNSPushMessage(messageDetails),
      MessageStructure: "json",
      TargetArn: endpointArn,
    };

    const result = await SNS.publish(params).promise();

    console.log("SNS AWS delivery results:", result);
  } catch (error) {
    console.log("other AWS SNS error:", error);
  }
};
