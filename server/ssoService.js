import { fetch } from "meteor/fetch";
import jwtDecode from "jwt-decode";
import { JwtRsaVerifier } from "aws-jwt-verify";

/**
 * Handles logic for the LineLeader SSO between products.
 */
export class SsoService {
    /**
     * Get the jwt token from the IDP code.
     *
     * @param code
     * @param codeVerifier
     * @returns {Promise<any|null>}
     */
    static async getTokenFromCode (code, codeVerifier, location) {
        const redirect_uri = location.protocol + '//' + location.host + '/code';
        const data = new URLSearchParams();
        data.append('code', code);
        data.append('client_id', 'manage');
        data.append('client_secret', Meteor.settings.idpClientSecret);
        data.append('grant_type', 'authorization_code');
        data.append('redirect_uri', redirect_uri);
        data.append('code_verifier', codeVerifier);

        const response = await fetch(Meteor.settings.idpBaseUrl + '/token', {
            method: 'POST',
            body: data,
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        });
        const jsonResponse = await response.json();
        return (jsonResponse) ? jsonResponse : null;
    }

    /**
     * Decode the idp access token.
     *
     * @param token
     * @returns {Promise<unknown>}
     */
    static async decodeIdpToken(token) {
        if (!Meteor.isDevelopment) {
            const verifier = JwtRsaVerifier.create({
                issuer: Meteor.settings.idpBaseUrl,
                audience: 'manage',
                jwksUri: Meteor.settings.idpBaseUrl + '/.well-known/jwks.json'
            });
            try {
                const payload = await verifier.verify(token);
                console.log("Token is valid. Payload:", payload);
            } catch (error) {
                console.log("Token not valid!", error);
                return { 'email': null };
            }
        }
        const decodedJwt = jwtDecode(token);
        console.log('post-decoded', decodedJwt);
        return decodedJwt;
    }
}
