import { Mongo } from 'meteor/mongo';
import _ from '../../lib/util/underscore';
 
const HistoryAudit = function(doc) {
    _.extend(this, doc)
};

export const HistoryAudits = new Mongo.Collection('historyAudits', {
    transform: function(doc) {
        return new HistoryAudit(doc);
    }
});

_.extend(HistoryAudits, {
    /**
     * Get the type of change.
     *
     * @returns { string }
     */
    getChangeType() {
        return this.changeType;
    },
    /**
     * The details of the change.
     *
     * @returns { string }
     */
    getDetails() {
        return this.details;
    },
    /**
     * The diff of the change.
     *
     * @returns {*}
     */
    getDiff() {
        return this.diff;
    },
    /**
     * Get the _id of associated org.
     *
     * @returns { string }
     */
    getOrgId() {
        return this.orgId;
    },
    /**
     * Get the _id of associated person.
     *
     * @returns { string }
     */
    getPersonId() {
        return this.personId;
    },
    /**
     * Get the _id of record being change.
     *
     * @returns { string }
     */
    getRecordId() {
        return this.recordId;
    },
    /**
     * Get the _id of person making the change.
     *
     * @returns { string | null }
     */
    getPerformedById() {
        return this.performedById;
    },
    /**
     * Get the name of person/service/process making the change.
     *
     * @returns { string }
     */
    getPerformedByName() {
        return this.performedByName;
    },
    /**
     * Get the type of record being change.
     *
     * @returns { string }
     */
    getRecordType() {
        return this.recordType;
    },
    /**
     * The timestamp of the change.
     *
     * @returns { number }
     */
    getTimestamp() {
        return this.timestamp;
    }
});
