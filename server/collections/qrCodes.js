import AES from 'crypto-js/aes';
import Utf8 from 'crypto-js/enc-utf8';
import moment from 'moment-timezone';
import { People } from '../../lib/collections/people';
import { Orgs } from '../../lib/collections/orgs';
import { Mongo } from 'meteor/mongo';
import _ from '../../lib/util/underscore';

const QrCode = function (doc) {
	_.extend(this, doc)
};

export const QrCodes = new Mongo.Collection('qrCodes', {
	transform: function (doc) {
		return new QrCode(doc);
	}
});

_.extend(QrCodes, {

});

/*

// One day we'll use TypeScript. For now, here's the signature:

interface InsertWithTokenStringOptions {
	orgId: string;
	tokenString: string;
	createdByPersonId: string;
}
*/

QrCodes.insertWithTokenString = async function (options) {

	if (!options.orgId) {
		throw new Meteor.Error("orgId is required");
	}

	if (!options.tokenString) {
		throw new Meteor.Error("tokenString is required");
	}

	if (!options.createdByPersonId) {
		throw new Meteor.Error("createdByPersonId is required");
	}

	const qrTokenString = options.tokenString;
	const currentOrg = await Orgs.findOneAsync(options.orgId);
	const currentPerson = await People.findOneAsync(options.createdByPersonId);

	if (!currentOrg) {
		throw new Meteor.Error("orgId is invalid");
	}

	if (!currentPerson) {
		throw new Meteor.Error("createdByPersonId is invalid");
	}

	const qrCodeId = await QrCodes.insertAsync({
		orgId: currentOrg._id,
		tokenString: qrTokenString,
		createdAt: new Date().valueOf(),
		createdByPersonId: currentPerson._id
	});

	const authString = AES.encrypt(`${currentOrg._id}|${qrTokenString}`, Meteor.settings.mpEntityKey).toString();

	return {
		authString,
		qrCodeId,
		qrCode: await QrCodes.findOneAsync(qrCodeId)
	};
}

/*

// One day we'll use TypeScript. For now, here's the signature:

interface RetrieveFromQrDataOptions {
	orgId: string;
	qrdata: string;
}

*/

QrCodes.retrieveFromQrData = async function (orgId, qrdata) {

	const org = await Orgs.findOneAsync(orgId);

	if (!org) {
		throw new Meteor.Error("orgId is invalid");
	}

	const parsedQrDataBytes = AES.decrypt(qrdata, Meteor.settings.mpEntityKey);
	const parsedQrData = parsedQrDataBytes.toString(Utf8),
		parsedOrgId = parsedQrData.split("|")[0],
		parsedToken = parsedQrData.split("|")[1];

	// We may want to return parsedOrgId in the future, but for now we don't need it.
	if (!parsedToken) {
		return undefined;
	}
	const qrdataMatch = await QrCodes.findOneAsync({ orgId: org._id, tokenString: parsedToken })

	return qrdataMatch;
}

/*

// One day we'll use TypeScript. For now, here's the signature:

interface CheckForExpirationOptions {
	orgId: string;
	qrCodeId: string
}

*/

QrCodes.checkForExpiration = async function (orgId, qrCodeId) {

	// We pass ids in so that if anything changes on the qrCode programmatically
	// we always return the latest from the DB.

	const org = await Orgs.findOneAsync(orgId);

	if (!org) {
		throw new Meteor.Error("orgId is invalid");
	}

	const qrCode = await QrCodes.findOneAsync(qrCodeId);

	if (!qrCode) {
		throw new Meteor.Error("qrCode is required");
	}

	if (org.hasCustomization("people/checkInCheckOutQrCodesExpire/enabled")) {
		// We no longer have expiresAt. Instead, we have "getCheckInCheckOutQrCodesExpireAfterMinutesValue" on the org.
		const orgExpiresAfterMinutes = org.getCheckInCheckOutQrCodesExpireAfterMinutesValue();
		// We compare this to when the qrCode was created.
		const qrCodeExpiresAt = moment(qrCode.createdAt).add(orgExpiresAfterMinutes, "minutes");
		const now = moment();
		if (now.isAfter(qrCodeExpiresAt)) {
			return {
				expiringQrCodesEnabled: true,
				error: "This QR code has expired. Please create a new one."
			}
		}
		return {
			expiringQrCodesEnabled: true,
			error: null
		}
	}

	return {
		expiringQrCodesEnabled: false,
		error: null,
	};
}