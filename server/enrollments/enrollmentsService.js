import { Meteor } from 'meteor/meteor';
import moment from 'moment-timezone';
import { AVAILABILITIES_PLACEHOLDER, DAY_ABBREVIATIONS } from '../../lib/constants/enrollmentConstants';
import { cloneDeep } from 'lodash';
import { Log } from '../../lib/util/log';
import { ItemDateTypes } from '../../lib/constants/billingConstants';
import logger from "../../imports/winston";
import { OrgsLib } from '../../lib/orgsLib';
import { AvailableCustomizations } from '../../lib/customizations';
import { DateTimeUtils } from '../../lib/util/dateTimeUtils';
import { People } from '../../lib/collections/people';
import { Orgs } from '../../lib/collections/orgs';
import { Reservations } from '../../lib/collections/reservations';
import { HOLIDAY_SCHEDULE_TYPES } from '../../lib/constants/holidayConstants';

/**
 * Handles all the business logic for enrollments.
 *
 * @class EnrollmentsService
 */
export class EnrollmentsService {
    /**
     * Gets the schedule type availabilities for the given org, start date, and time period.
     * The availabilities are returned in the following format:
     * {
     *    <scheduleTypeId>: {
     *      1: <enrollments>,
     *      2: <enrollments>,
     *      3: <enrollments>,
     *      4: <enrollments>,
     *      5: <enrollments>
     *    },
     *    ...
     *  }
     *  Where 1 is Monday, 2 is Tuesday, etc.
     *  The "enrollments" is the remaining number of enrollments for that day of the week.
     *  If the scheduleType.maxEnrollment is not set, then the enrollments is AVAILABILITIES_PLACEHOLDER.
     *
     * @param {string} orgId - The organization ID.
     * @param {string} startDateString - The start date in "MM/DD/YYYY" format.
     * @param {string} timePeriodId - The ID of the time period.
     * @param {string|null} endDateString - The optional end date in "MM/DD/YYYY" format.
     * @param {Array<string>} excludedReservationIds - Reservation IDs to exclude from calculations.
     * @returns {Object|null} The availability data or null if invalid inputs.
     */
    static async getScheduleTypeAvailabilities(orgId, startDateString, timePeriodId, endDateString = null, excludedReservationIds = []) {
        const org = await Orgs.findOneAsync({_id: orgId});

        if (!org) {
            return null;
        }

        const timePeriod = (org.billing?.timePeriods ?? []).find((tp) => tp._id === timePeriodId);

        if (!timePeriod && timePeriodId) {
            return null;
        }

        if (!timePeriod && !endDateString) {
            endDateString = new moment().add(1, 'year').format('MM/DD/YYYY');
        }

        const startDate = new moment.tz(startDateString, 'MM/DD/YYYY', org.getTimezone()).startOf('day');
        const startDateTimestamp = startDate.valueOf();
        const endDateTimestamp = timePeriod?.endDate ?? new moment.tz(endDateString, 'MM/DD/YYYY', org.getTimezone()).startOf('day').valueOf();
        const scheduleTypes = org?.getScheduleTypes() ?? [];

        if (scheduleTypes.length === 0) {
            return null;
        }

        const activeChildrenIds  = await this.getActiveChildrenIds(orgId);

        const schedulesQuery = this.buildSchedulesQuery(org, activeChildrenIds, scheduleTypes, startDateTimestamp, endDateTimestamp, excludedReservationIds)

        const schedules = await Reservations.find(schedulesQuery).fetchAsync();

        return this.calculateAvailabilities(schedules, scheduleTypes, startDate, endDateTimestamp, org);
    }

    /**
     * Builds the query for fetching schedules based on the organization and date range.
     *
     * @param {Object} org - The organization object.
     * @param {Array<string>} activeChildrenIds - Array of active child IDs.
     * @param {Array<Object>} scheduleTypes - List of schedule types for the organization.
     * @param {number} startDateTimestamp - Start date timestamp.
     * @param {number} endDateTimestamp - End date timestamp.
     * @param {Array<string>} excludedReservationIds - Array of reservation IDs to exclude.
     *
     * @returns {Object} The MongoDB query object for fetching schedules.
     */
    static buildSchedulesQuery(org, activeChildrenIds, scheduleTypes, startDateTimestamp, endDateTimestamp, excludedReservationIds) {
        const query = {
            orgId: org._id,
            selectedPerson: { $in: activeChildrenIds },
            scheduleType: { $in: scheduleTypes.map(st => st._id) },
            cancellationDate: { $exists: false },
            $or: [
                {
                    $and: [
                        { scheduledDate: { $gte: startDateTimestamp } },
                        { scheduledDate: { $lte: endDateTimestamp } }
                    ]
                },
                {
                    $and: [
                        { scheduledDate: { $lte: startDateTimestamp } },
                        { recurringFrequency: 1 },
                        {
                            $or: [
                                { scheduledEndDate: { $gte: startDateTimestamp } },
                                { scheduledEndDate: null }
                            ]
                        }
                    ]
                }
            ]
        };

        if (excludedReservationIds.length) {
            query._id = { $nin: excludedReservationIds };
        }

        return query;
    }

    /**
     * Calculates availabilities based on schedules, schedule types, and the date range.
     *
     * @param {Array<Object>} schedules - List of schedules fetched from the database.
     * @param {Array<Object>} scheduleTypes - List of schedule types for the organization.
     * @param {Object} startDate - Start date as a moment.js object.
     * @param {number} endDateTimestamp - End date timestamp.
     * @param {Object} org - The organization object.
     *
     * @returns {Object} The calculated schedule type availabilities.
     */
    static calculateAvailabilities(schedules, scheduleTypes, startDate, endDateTimestamp, org) {
        const dayMap = this.getDaysMap(org);

        let scheduleDate = startDate;
        let recurringDays = [];
        const dateCounts = {};
        const personDates = {};

        schedules.forEach((schedule) => {
            if (!schedule.scheduleType || !schedule.selectedPerson) {
                return;
            }

            scheduleDate = this.getMaxBetweenStartDates(schedule, startDate, org.getTimezone());

            recurringDays = schedule.recurringDays ?? [];

            const tempEndDateTimestamp = this.getEndDateToUse(schedule, endDateTimestamp);

            this.setDefaultObjectProperties(dateCounts, personDates, schedule);

            this.loopDaysAndUpdateCounts(dateCounts, personDates, schedule, scheduleDate, tempEndDateTimestamp, recurringDays, dayMap);
        });

        return this.finalizeAvailabilities(dateCounts, scheduleTypes, org);
    }

    /**
     * Provides a mapping of numeric day indices to day names.
     *
     * @param {Object} org - The organization object.
     *
     * @returns {Object} A mapping of day indices to day abbreviations (e.g., 1 -> "mon").
     */
    static getDaysMap(org) {
        const hasWeekends = org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);

        return hasWeekends
            ? { 0: 'sun', 1: 'mon', 2: 'tue', 3: 'wed', 4: 'thu', 5: 'fri', 6: 'sat' }
            : { 1: 'mon', 2: 'tue', 3: 'wed', 4: 'thu', 5: 'fri' };
    }

    /**
     * Gets the maximum date between the schedule's start date and the provided start date.
     *
     * @param {Object} schedule - The schedule object.
     * @param {Object} startDate - The start date as a moment.js object.
     * @param {string} timezone - The timezone string.
     *
     * @returns {Object} The maximum date as a moment.js object.
     */
    static getMaxBetweenStartDates(schedule, startDate, timezone) {
        // Get the max between the start date range and the schedule's start date
        return moment.tz(moment.unix(Math.max(startDate.valueOf(), schedule.scheduledDate) / 1000).format('MM/DD/YYYY'), 'MM/DD/YYYY', timezone);
    }

    /**
     * Determines the end date to use for a schedule based on its scheduled end date and the provided end date.
     *
     * @param {Object} schedule - The schedule object.
     * @param {number} endDateTimestamp - The provided end date timestamp.
     *
     * @returns {number} The end date timestamp to use.
     */
    static getEndDateToUse(schedule, endDateTimestamp) {
        // Determines which end date to use for this schedule: the end date of the schedule or the end date passed into this function
        let tempEndDateTimestamp = endDateTimestamp;

        if (schedule.scheduledEndDate) {
            tempEndDateTimestamp = Math.min(endDateTimestamp, schedule.scheduledEndDate);
        }

        return tempEndDateTimestamp;
    }

    /**
     * Initializes default object properties for dateCounts and personDates for the given schedule.
     *
     * @param {Object} dateCounts - Object tracking the number of enrollments per date.
     * @param {Object} personDates - Object tracking dates a person is scheduled for a schedule type.
     * @param {Object} schedule - The schedule object.
     */
    static setDefaultObjectProperties(dateCounts, personDates, schedule) {
        if (!(schedule.scheduleType in dateCounts)) {
            dateCounts[schedule.scheduleType] = {};
        }

        if (!(schedule.selectedPerson in personDates)) {
            personDates[schedule.selectedPerson] = {};
        }

        if (!(schedule.scheduleType in personDates[schedule.selectedPerson])) {
            personDates[schedule.selectedPerson][schedule.scheduleType] = {};
        }
    }

    /**
     * Loops through days within the schedule's time range and updates availability counts.
     *
     * @param {Object} dateCounts - Object tracking the number of enrollments per date.
     * @param {Object} personDates - Object tracking dates a person is scheduled for a schedule type.
     * @param {Object} schedule - The schedule object.
     * @param {Object} scheduleDate - The current schedule date as a moment.js object.
     * @param {number} tempEndDateTimestamp - The end date timestamp to use for this schedule.
     * @param {Array<string>} recurringDays - Array of recurring days for the schedule.
     * @param {Object} dayMap - Mapping of day indices to day names.
     */
    static loopDaysAndUpdateCounts(dateCounts, personDates, schedule, scheduleDate, tempEndDateTimestamp, recurringDays, dayMap) {
        // Loop through all the days of the schedule in the time range
        while (scheduleDate.valueOf() <= tempEndDateTimestamp) {
            // Make sure to not double count the same person on the same day for the same schedule type
            // If the schedule is recurring, check if the day of the week is in the recurring days
            if (recurringDays.length) {
                const dayOfWeek = scheduleDate.day();
                if (recurringDays.includes(dayMap[dayOfWeek]) && !personDates[schedule.selectedPerson][schedule.scheduleType][scheduleDate.format('MM/DD/YYYY')]) {
                    // Increment the count for the day
                    this.updateDateCounts(dateCounts, personDates, schedule, scheduleDate);
                }
            } else if (!personDates[schedule.selectedPerson][schedule.scheduleType][scheduleDate.format('MM/DD/YYYY')]) {
                // Increment the count for the day
                this.updateDateCounts(dateCounts, personDates, schedule, scheduleDate);
                // Exit since this is a one time schedule
                break;
            } else {
                // BUGS-3020: If the schedule is not recurring and the person is already scheduled for this date, exit the loop
                break;
            }
            scheduleDate.add(1, 'days');
        }
    }

    /**
     * Updates the date counts for a given schedule and date.
     *
     * @param {Object} dateCounts - Object tracking the number of enrollments per date.
     * @param {Object} personDates - Object tracking dates a person is scheduled for a schedule type.
     * @param {Object} schedule - The schedule object.
     * @param {Object} scheduleDate - The current schedule date as a moment.js object.
     */
    static updateDateCounts(dateCounts, personDates, schedule, scheduleDate) {
        dateCounts[schedule.scheduleType][scheduleDate.format('MM/DD/YYYY')] = (dateCounts[schedule.scheduleType][scheduleDate.format('MM/DD/YYYY')] ?? 0) + 1;
        personDates[schedule.selectedPerson][schedule.scheduleType][scheduleDate.format('MM/DD/YYYY')] = true;
    }

    /**
     * Finalizes the availability data by calculating remaining enrollments for each schedule type.
     *
     * @param {Object} dateCounts - Object tracking the number of enrollments per date.
     * @param {Array<Object>} scheduleTypes - List of schedule types for the organization.
     * @param {Object} org - The organization object.
     *
     * @returns {Object} The finalized availability data.
     */
    static finalizeAvailabilities(dateCounts, scheduleTypes, org) {
        const scheduleTypeAvailabilities = {};
        const scheduleTypeEnrollmentCaps = {};
        const placeholder = AVAILABILITIES_PLACEHOLDER;
        const hasWeekends = org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);

        scheduleTypes.forEach((scheduleType) => {
            scheduleTypeAvailabilities[scheduleType._id] = {
                1: Number(scheduleType.maxEnrollment?.[1] ?? placeholder),
                2: Number(scheduleType.maxEnrollment?.[2] ?? placeholder),
                3: Number(scheduleType.maxEnrollment?.[3] ?? placeholder),
                4: Number(scheduleType.maxEnrollment?.[4] ?? placeholder),
                5: Number(scheduleType.maxEnrollment?.[5] ?? placeholder),
                ...(hasWeekends && {
                    6: Number(scheduleType.maxEnrollment?.[6] ?? placeholder),
                    0: Number(scheduleType.maxEnrollment?.[0] ?? placeholder),
                }),
            };
            scheduleTypeEnrollmentCaps[scheduleType._id] = cloneDeep(scheduleTypeAvailabilities[scheduleType._id]);
        });

        Object.keys(dateCounts).forEach((scheduleTypeId) => {
            const dateCount = dateCounts[scheduleTypeId];
            Object.keys(dateCount).forEach((dateString) => {
                const count = dateCount[dateString];
                const dayOfWeek = new moment.tz(dateString, 'MM/DD/YYYY', org.getTimezone()).day();
                // Set the maximum number of available spots for the day of the week
                scheduleTypeAvailabilities[scheduleTypeId][dayOfWeek] = Math.max(
                    Math.min(
                        scheduleTypeEnrollmentCaps[scheduleTypeId][dayOfWeek] - count, // The number of spots left for this given date
                        scheduleTypeAvailabilities[scheduleTypeId][dayOfWeek] ?? placeholder // The current lowest number of spots left for this day of the week
                    ),
                    0 // Don't allow negative numbers
                );
            });
        });

        return scheduleTypeAvailabilities;
    }


    /**
     * Inserts or updates a schedule type for an organization.
     *
     * @param {Object} options - The options for the operation.
     * @param {string} [options.existingId] - The ID of the existing schedule type to update. If not provided, a new schedule type will be created.
     * @param {string} options.scheduleType - The name of the schedule type.
     * @param {string} [options.startTime] - The start time for the schedule type (applicable for "scheduled" types).
     * @param {string} [options.endTime] - The end time for the schedule type (applicable for "scheduled" types).
     * @param {Object} [options.maxEnrollment] - The maximum enrollment for the schedule type, with keys representing days of the week (1 for Monday, etc.).
     * @param {number} [options.fteCount] - The full-time equivalent count for the schedule type.
     * @param {boolean} [options.hideInForecasting] - Whether to hide this schedule type in forecasting.
     * @param {string} [options.defaultGroupId] - The ID of the default group associated with the schedule type.
     * @param {string} options.scheduleTypeTimeBlock - The time block for the schedule type ("all-day" or "scheduled").
     * @param {Object} options.currentUser - The current user performing the operation.
     * @param {string} options.currentUser.orgId - The ID of the organization to which the schedule type belongs.
     *
     * @returns {Promise<void>} Resolves when the schedule type has been inserted or updated successfully.
     *
     * @throws {Meteor.Error} Throws an error if the database operation fails.
     */
    static async insertUpdateScheduleType(options) {
        const {
            existingId,
            scheduleType,
            startTime,
            endTime,
            maxEnrollment,
            fteCount,
            hideInForecasting,
            defaultGroupId,
            scheduleTypeTimeBlock,
            currentUser,
            randomId
        } = options;

        if (existingId) {
            const updateScheduleTypeData = {
                "valueOverrides.scheduleTypes.$.type": scheduleType,
                "valueOverrides.scheduleTypes.$.startTime": startTime,
                "valueOverrides.scheduleTypes.$.endTime": endTime,
                "valueOverrides.scheduleTypes.$.maxEnrollment": maxEnrollment,
                "valueOverrides.scheduleTypes.$.fteCount": fteCount,
                "valueOverrides.scheduleTypes.$.hideInForecasting": hideInForecasting
            };

            if (defaultGroupId) {
                updateScheduleTypeData["valueOverrides.scheduleTypes.$.defaultGroupId"] = defaultGroupId;
            }

            try {
                await Orgs.updateAsync({ _id: currentUser.orgId, "valueOverrides.scheduleTypes._id": existingId }, { $set: updateScheduleTypeData });
            } catch (e) {
                throw new Meteor.Error(500, e.message);
            }

        } else {
            const newScheduleType = {
                _id: randomId,
                type: scheduleType,
            };

            if (scheduleTypeTimeBlock === "scheduled") {
                newScheduleType.startTime = startTime;
                newScheduleType.endTime = endTime;
            }

            if (maxEnrollment) {
                newScheduleType.maxEnrollment = maxEnrollment;
            }

            if (fteCount) {
                newScheduleType.fteCount = fteCount;
            }

            if (hideInForecasting) {
                newScheduleType.hideInForecasting = hideInForecasting;
            }

            if (defaultGroupId) {
                newScheduleType.defaultGroupId = defaultGroupId;
            }

            try {
                await Orgs.updateAsync({ _id: currentUser.orgId }, { $addToSet: { "valueOverrides.scheduleTypes": newScheduleType } });
            } catch (e) {
                throw new Meteor.Error(500, e.message);
            }

        }
    }

    /**
     * Removes a schedule type from an organization.
     * @param {string} scheduleTypeId - The ID of the schedule type to remove.
     * @param {object} org - the organization object
     * @return {Promise<void>} Resolves when the schedule type has been removed successfully.
     *
     * @throws {Meteor.Error} If the organization or schedule type ID is missing.
     */
    static async removeScheduleType(scheduleTypeId, org) {
        if (!org) {
            throw new Meteor.Error(500, "Organization not found");
        }

        if (!scheduleTypeId) {
            throw new Meteor.Error(500, "Schedule type ID is required");
        }

        const currentTypes = OrgsLib.getScheduleTypes(org);
        const filteredTypes = currentTypes.filter((t) => t._id !== scheduleTypeId);

        try {
            await Orgs.updateAsync({ _id: org._id }, { "$set": { "valueOverrides.scheduleTypes": filteredTypes } });
        } catch (e) {
            throw new Meteor.Error(e.error, e.message);
        }
    }

    /**
     * Fetches enrollment data for a specified date range and organization.
     *
     * @param {Object} options - The options for the operation.
     * @param {string} options.rangeStart - The start date of the range (formatted as "MM/DD/YYYY").
     * @param {string} options.rangeEnd - The end date of the range (formatted as "MM/DD/YYYY").
     * @param {Object} org - The organization object.
     *
     * @returns {Promise<Object>} The range reservation data containing schedule types and their corresponding data.
     *
     * @throws {Meteor.Error} Throws an error if the organization or range dates are invalid.
     */
    static async rangedEnrollmentData(options, org) {
        if (!org) {
            throw new Meteor.Error(500, "Organization not found");
        }

        if (!options.rangeStart || !options.rangeEnd) {
            throw new Meteor.Error(500, "Range start and end dates are required");
        }

        const { timezone, startDateMoment, startDate, endDatePlusOne, endDate } = this.getBoundariesForRangedEnrollmentData(options, org);

        const query = this.getQueryForRangedEnrollmentData(org._id, startDateMoment);

        const scheduleTypes = org.getScheduleTypes();
        const rangeReservationData = {
            scheduleTypes: {},
            rangeStart: startDate,
            rangeEnd: endDate,
            holidaysByDay: this.getHolidaysByDayForRangedEnrollmentData(org, startDate, endDate, timezone)
        };

        // Initialize the schedule Types object
        for (const scheduleType of scheduleTypes) {
            const defaultMaxEnrollment = this.getDefaultMaxEnrollmentForRangedEnrollmentData(org);

            const maxEnrollment = scheduleType.maxEnrollment ?? defaultMaxEnrollment;

            this.initializeScheduleTypeData(rangeReservationData, scheduleType, maxEnrollment);
        }

        const allReservations = await Reservations.findWithRecurrence({ startDate, endDate: endDatePlusOne, query });

        allReservations.forEach(reservation => this.processReservationsForRangedEnrollmentData(reservation, rangeReservationData, timezone));

        rangeReservationData.scheduleTypes = Object.values(rangeReservationData.scheduleTypes)
        return rangeReservationData;
    }

    /**
     * Computes the time boundaries and formats them for the enrollment range.
     *
     * @param {Object} options - The options for the operation.
     * @param {string} options.rangeStart - The start date of the range (formatted as "MM/DD/YYYY").
     * @param {string} options.rangeEnd - The end date of the range (formatted as "MM/DD/YYYY").
     * @param {Object} org - The organization object.
     *
     * @returns {Object} The boundaries including startDateMoment, startDate, endDatePlusOne, and endDate.
     */
    static getBoundariesForRangedEnrollmentData(options, org) {
        const timezone = org.getTimezone();

        if (!options.rangeStart && !options.rangeEnd) {
            throw new Meteor.Error(400, "Range start and end dates are required");
        }

        if (!options.rangeStart || !moment.tz(options.rangeStart, "MM/DD/YYYY", timezone).isValid()) {
            throw new Meteor.Error(400, "Invalid rangeStart date");
        }

        if (!options.rangeEnd || !moment.tz(options.rangeEnd, "MM/DD/YYYY", timezone).isValid()) {
            throw new Meteor.Error(400, "Invalid rangeEnd date");
        }

        return {
            timezone: timezone,
            startDateMoment: moment.tz(options.rangeStart, "MM/DD/YYYY", timezone).startOf("day"),
            startDate: options.rangeStart,
            endDatePlusOne: moment.tz(options.rangeEnd, "MM/DD/YYYY", timezone).startOf("day").add(1, "day").format("MM/DD/YYYY"),
            endDate: options.rangeEnd,
        };
    }


    /**
     * Constructs a query object for fetching ranged enrollment data from the database.
     *
     * @param {string} orgId - The ID of the organization.
     * @param {Object} startDateMoment - The start date as a Moment.js object.
     *
     * @returns {Object} The query object to be used with the database.
     */
    static getQueryForRangedEnrollmentData(orgId, startDateMoment) {
        const validStartDateMoment = startDateMoment?.isValid?.() ? startDateMoment : null;

        return {
            orgId: orgId,
            reservationType: "person",
            "$or": [
                { scheduledEndDate: { "$exists": false } },
                { scheduledEndDate: { "$exists": true, "$gte": validStartDateMoment ? validStartDateMoment.valueOf() : null } },
                { scheduledEndDate: { "$exists": true }, "$eq": null }
            ]
        };
    }

    /**
     * Gets the default maximum enrollment for each day of the week based on the organization's customizations.
     *
     * @param {Object} org - The organization object.
     *
     * @returns {Array<number>} An array representing the default max enrollment for each day of the week.
     */
    static getDefaultMaxEnrollmentForRangedEnrollmentData(org) {
        const defaultMaxEnrollment = [0, 0, 0, 0, 0];

        if (!org) {
            return defaultMaxEnrollment;
        }

        const orgHasWeekends = org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);

        if (orgHasWeekends) {
            // Push default values for Sat, Sun.
            defaultMaxEnrollment.push(...[0, 0]);
        }

        return defaultMaxEnrollment;
    }

    /**
     * Initializes the data structure for a schedule type in the range reservation data.
     *
     * @param {Object} rangeReservationData - The data structure for range reservations.
     * @param {Object} scheduleType - The schedule type object.
     * @param {Object} maxEnrollment - The maximum enrollment for the schedule type.
     */
    static initializeScheduleTypeData(rangeReservationData, scheduleType, maxEnrollment) {
        rangeReservationData.scheduleTypes[scheduleType._id] = {
            title: scheduleType.type,
            id: scheduleType._id,
            reservationsCount: 0,
            capacity: 0,
            groups: {},
            reservations: [],
            stats: [
                {"abbr": "Su", "coming": 0, "cap": maxEnrollment['0'] ?? 0, "date": ""},
                {"abbr": "M", "coming": 0, "cap": maxEnrollment['1'] ?? 0, "date": ""},
                {"abbr": "T", "coming": 0, "cap": maxEnrollment['2'] ?? 0, "date": ""},
                {"abbr": "W", "coming": 0, "cap": maxEnrollment['3'] ?? 0, "date": ""},
                {"abbr": "R", "coming": 0, "cap": maxEnrollment['4'] ?? 0, "date": ""},
                {"abbr": "F", "coming": 0, "cap": maxEnrollment['5'] ?? 0, "date": ""},
                {"abbr": "Sa", "coming": 0, "cap": maxEnrollment['6'] ?? 0, "date": ""}
            ]
        }
    }


    /**
     * Processes a reservation and updates the corresponding schedule type data in the range reservation data.
     *
     * @param {Object} reservation - The reservation object.
     * @param {Object} rangeReservationData - The data structure for range reservations.
     * @param {string} timezone - The timezone of the organization.
     */
    static processReservationsForRangedEnrollmentData(reservation, rangeReservationData, timezone) {
        if (!reservation.scheduledDate) {
            return;
        }

        const scheduledDateMoment = moment.tz(reservation.scheduledDate, timezone);

        if (!scheduledDateMoment.isValid()) {
            return;
        }

        const dayOfWeek = new moment.tz(reservation.scheduledDate, timezone).day();
        const scheduleTypeData = rangeReservationData?.scheduleTypes[reservation.scheduleType];

        const abbr = DAY_ABBREVIATIONS[dayOfWeek];
        const holidayInfo = rangeReservationData.holidaysByDay?.[abbr];

        const isDisallowed = holidayInfo &&
            (
                holidayInfo.permittedScheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.NONE) ||
                (
                    !holidayInfo.permittedScheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.ALL) &&
                    !holidayInfo.permittedScheduleTypes.includes(reservation.scheduleType)
                )
            );

        if (isDisallowed) {
            return; // skip this reservation
        }

        if (scheduleTypeData !== undefined) {
            scheduleTypeData.reservationsCount++;
            scheduleTypeData.stats[dayOfWeek]['coming']++
            scheduleTypeData.stats[dayOfWeek]['date'] = moment.tz(reservation.scheduledDate, timezone).format("MM/DD/YYYY");
            scheduleTypeData.stats[dayOfWeek]['cap'] = (scheduleTypeData.stats[dayOfWeek]['cap'] !== '') ? scheduleTypeData.stats[dayOfWeek]['cap'] : 0;
        }
    }

    /**
     * Returns a mapping of weekday abbreviations to holidays that fall within a given date range.
     * Each holiday entry includes its name and the list of permitted schedule types for that day.
     *
     * @param {Object} org - The organization object.
     * @param {string} startDate - The start date of the range (formatted as "MM/DD/YYYY").
     * @param {string} endDate - The end date of the range (formatted as "MM/DD/YYYY").
     * @param {string} timezone - The timezone to use for date calculations (e.g., "America/Chicago").
     *
     * @returns {Object} A map keyed by weekday abbreviations (e.g., "M", "T", "W") containing:
     *   - {string} name - Name of the holiday.
     *   - {string[]} permittedScheduleTypes - List of allowed schedule type IDs (or "All"/"None").
     */
    static getHolidaysByDayForRangedEnrollmentData(org, startDate, endDate, timezone) {
        const holidaysByDay = {}; // keyed by 'M', 'T', etc.
        const holidays = org.getHolidays() || [];
        const currentDate = moment.tz(startDate, 'MM/DD/YYYY', timezone);
        const endDateMoment = moment.tz(endDate, 'MM/DD/YYYY', timezone).endOf('day');

        while (!currentDate.isAfter(endDateMoment)) {
            const dateStr = currentDate.format('YYYY-MM-DD');
            const abbr = DAY_ABBREVIATIONS[currentDate.day()];

            const holiday = holidays.find(h => {
                if (h.deleted) {
                    return false;
                }

                if (!h.dateType || h.dateType === 'individual') {
                    return h.date === dateStr;
                }

                return moment(dateStr).isBetween(h.startDate, h.endDate, null, '[]');
            });

            if (holiday) {
                const permitted = holiday.scheduleTypes || [HOLIDAY_SCHEDULE_TYPES.NONE];
                holidaysByDay[abbr] = {
                    name: holiday.name,
                    permittedScheduleTypes: permitted.includes(HOLIDAY_SCHEDULE_TYPES.ALL) || permitted.includes(HOLIDAY_SCHEDULE_TYPES.NONE) ? permitted : [...permitted],
                };
            }

            currentDate.add(1, 'day');
        }

        return holidaysByDay;
    }

    /**
     * Gets the schedule type usage for the given org, days, and schedule type.
     * The return array is the number of children scheduled for each day in the days array.
     * @param orgId
     * @param days
     * @param timezone
     * @param scheduleTypeId
     * @return {*[]}
     */
    static async getScheduleTypeUsage(orgId, days, timezone, scheduleTypeId) {
        const activeChildrenIds = await this.getActiveChildrenIds(orgId);
        const start = days[0];
        const end = moment.tz(days[days.length - 1], timezone).endOf('day').valueOf();
        const query = {
            orgId,
            scheduleType: scheduleTypeId,
            selectedPerson: {$in: activeChildrenIds},
            cancellationReason: {$exists: false},
            cancellationReasonId: {$exists: false},
            cancellationDate: {$exists: false},
            reservationType: 'person',
            recurringType: {$in: [null, false]},
            scheduledDate: {$gte: start, $lte: end }
        };

        const reservations = await Reservations.find(query).fetchAsync();
        const childIds = [];
        for (const reservation of reservations) {
            const day = moment.tz(reservation.scheduledDate, timezone).startOf('day').valueOf();
            for (const dayIter in days) {
                if (day === days[dayIter]) {
                    if (!childIds[dayIter]) {
                        childIds[dayIter] = new Set();
                    }
                    childIds[dayIter].add(reservation.selectedPerson);
                }
            }
        }
        const returnArray = [];
        for (const dayIter in days) {
            returnArray.push(childIds[dayIter]?.size ?? 0);
        }
        return returnArray;
    }

    /**
     * Gets the maximum daily enrollments by schedule type.
     * @param {string} scheduleTypeId - The ID of the schedule type.
     * @param {Array} orgScheduleTypes - The array of organization schedule types.
     * @param {boolean} orgHasWeekends - Whether the organization has weekends enabled.
     * @returns {Object|null} An object with the maximum enrollments per day or null if not found.
     */
    static getMaxDailyEnrollmentsByScheduleType(scheduleTypeId, orgScheduleTypes, orgHasWeekends) {
        if (!orgScheduleTypes || !scheduleTypeId) {
            return null;
        }

        const scheduleType = orgScheduleTypes.find((st) => st._id === scheduleTypeId);
        const availability = {
            1: Number(scheduleType?.maxEnrollment?.[1] ?? AVAILABILITIES_PLACEHOLDER),
            2: Number(scheduleType?.maxEnrollment?.[2] ?? AVAILABILITIES_PLACEHOLDER),
            3: Number(scheduleType?.maxEnrollment?.[3] ?? AVAILABILITIES_PLACEHOLDER),
            4: Number(scheduleType?.maxEnrollment?.[4] ?? AVAILABILITIES_PLACEHOLDER),
            5: Number(scheduleType?.maxEnrollment?.[5] ?? AVAILABILITIES_PLACEHOLDER)
        };

        if (orgHasWeekends) {
            availability[6] = Number(scheduleType?.maxEnrollment?.[6] ?? AVAILABILITIES_PLACEHOLDER);
            availability[0] = Number(scheduleType?.maxEnrollment?.[0] ?? AVAILABILITIES_PLACEHOLDER);
        }

        return availability;
    }

    /**
     * Gets the operating days of an item based on its details.
     * @param {Object} item - The item object.
     * @param {string} timezone - The timezone string.
     * @returns {Array<string>} An array of days of the week the item operates.
     */
    static getOperatingDays(item, timezone) {
        const { details } = item;
        const operatingDays = new Set();

        if (details.dateType === 'dateRange' && details.serviceStartDate && details.serviceEndDate) {
            const startDate = moment.tz(details.serviceStartDate, timezone);
            const endDate = moment.tz(details.serviceEndDate, timezone);

            for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'days')) {
                operatingDays.add(date.format('ddd').toLowerCase());
            }
        } else if (details.dateType === 'individualDates' && details.individualDates?.length) {
            details.individualDates.forEach(timestamp => {
                const date = moment.tz(timestamp, timezone);
                operatingDays.add(date.format('ddd').toLowerCase());
            });
        } else if (details.dateType === 'recurring' && details.recurringDays?.length) {
            details.recurringDays.forEach(day => {
                operatingDays.add(day);
            });
        }

        return Array.from(operatingDays);
    }

    static get DAYS() {
        return ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
    }

    /**
     * Get the default schedule data for item charges.
     *
     * @param {Object} plan - The plan object containing details of the item.
     * @param {string} personId - The ID of the person for whom the item is being scheduled.
     * @returns {Object} The default schedule data object.
     */
    static getDefaultScheduleDataForItemCharges(plan, personId) {
        return {
            selectedPerson: [personId],
            reservationType: "person",
            groupId: 'default',
            overrideOverlap: true,
            scheduleType: plan.details?.scheduleType ?? '',
            scheduledTime: plan.details?.startTime ?? '',
            scheduledEndTime: plan.details?.endTime ?? ''
        };
    }

    /**
     * Check if the plan is a recurring item.
     *
     * @param {Object} plan - The plan object to check.
     * @returns {boolean} True if the item is recurring, false otherwise.
     */
    static isRecurringItem(plan) {
        return plan.details?.dateType === ItemDateTypes.RECURRING && plan.details?.recurringDays?.length > 0;
    }

    /**
     * Check if the plan is a date range item.
     *
     * @param {Object} plan - The plan object to check.
     * @returns {boolean} True if the item is a date range, false otherwise.
     */
    static isDateRangeItem(plan) {
        return !!(plan.details?.dateType === ItemDateTypes.DATE_RANGE && plan.details?.serviceStartDate && plan.details?.serviceEndDate);
    }

    /**
     * Check if the plan is an individual dates item.
     *
     * @param {Object} plan - The plan object to check.
     * @returns {boolean} True if the item has individual dates, false otherwise.
     */
    static isIndividualDatesItem(plan) {
        return plan.details?.dateType === ItemDateTypes.INDIVIDUAL_DATES && plan.details?.individualDates?.length > 0;
    }

    /**
     * Get the days for a recurring date item.
     *
     * @param {Object} plan - The plan object containing details of the recurring item.
     * @param {string} timezone - The timezone to be used for date calculations.
     * @returns {Array<number>} An array of timestamps representing the recurring item dates.
     */
    static getItemDaysForRecurringDates(plan, timezone) {
        const { details } = plan;
        const days = [];

        if (!details.recurringStartDate || !details.recurringFrequency || !details.recurringOccurrences || !details.recurringDays?.length) {
            return days;
        }

        let startDate = details.recurringStartDate;
        let firstRun = true;
        for (let i = details.recurringOccurrences; i > 0; i--) {
            const startDayNum = moment.tz(startDate, timezone).day();
            for (const day of details.recurringDays) {
                const planDayNum = this.DAYS.indexOf(day);
                if (firstRun && startDayNum > planDayNum) {
                    continue;
                }
                firstRun = false;

                days.push(new moment.tz(startDate, timezone).add(planDayNum - startDayNum, 'days').valueOf());
            }
            startDate = new moment.tz(startDate, timezone).add(details.recurringFrequency, 'weeks').valueOf();
        }

        return days;
    }

    /**
     * Get the days for a date range item, excluding specified days.
     *
     * @param {Object} plan - The plan object containing details of the date range item.
     * @param {string} timezone - The timezone to be used for date calculations.
     * @returns {Array<number>} An array of timestamps representing the dates within the range.
     */
    static getItemDaysForDateRangeItem(plan, timezone) {
        const { details } = plan;
        const days = [];
        const startDate = moment.tz(details.serviceStartDate, timezone);
        const endDate = moment.tz(details.serviceEndDate, timezone);
        const excludeSaturdays = details.excludeSaturdays ?? true;
        const excludeSundays = details.excludeSundays ?? true;

        for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'days')) {
            const dayOfWeek = date.day(); // 0 = Sunday, 6 = Saturday

            // Check if the current day is Saturday or Sunday and skip if necessary
            if ((excludeSaturdays && dayOfWeek === 6) || (excludeSundays && dayOfWeek === 0)) {
                continue; // Skip this iteration and move to the next day
            }

            days.push(date.valueOf());
        }

        return days;
    }

    /**
     * Get the individual dates for an item.
     *
     * @param {Object} plan - The plan object containing details of the individual dates item.
     * @param {string} timezone - The timezone to be used for date calculations.
     * @returns {Array<number>} An array of timestamps representing the individual item dates.
     */
    static getItemDaysForIndividualDates(plan, timezone) {
        const { details } = plan;
        const days = [];
        details.individualDates.forEach(timestamp => {
            const date = moment.tz(timestamp, timezone);
            days.push(date.valueOf());
        });

        return days;
    }

    /**
     * Generate schedule data for item charges based on the specified plan and dates.
     *
     * @param {Object} plan - The plan object containing details of the item.
     * @param {string} personId - The ID of the person for whom the item is being scheduled.
     * @param {string} timezone - The timezone to be used for date calculations.
     * @returns {Array<Object>} An array of schedule objects, each representing a scheduled date.
     */
    static generateScheduleDataForItemCharges(plan, personId, timezone, childEnrolledItemId=null) {
        const schedules = [];
        const scheduleData = this.getDefaultScheduleDataForItemCharges(plan, personId);
        scheduleData.generatedFromBillingCharge = plan._id;
        if(childEnrolledItemId){
            scheduleData.enrolledItemId = childEnrolledItemId;
        }
        const scheduleDays = this.getItemDays(plan, timezone);
        // Create a schedule object for each day the item is scheduled for
        for (const date of scheduleDays) {
            const itemSchedule = Object.assign({}, scheduleData);
            itemSchedule.scheduledDate = date;
            schedules.push(itemSchedule);
        }

        return schedules;
    }

    /**
     * Generate and insert item schedules for a child based on the provided items.
     *
     * @param {string} personId - The ID of the person (child) for whom the items are being scheduled.
     * @param {Array<Object>} items - An array of items that need to be scheduled.
     * @param {string} timezone - The timezone to be used for date calculations.
     * @returns {void}
     */
    static async generateItemSchedulesForChild(personId, items, timezone, childEnrolledItemIds) {
        const schedules = [];
        logger.info("generateItemSchedulesForChild > inputs", {items, timezone, personId});
        for (const [index, item] of items.entries()){
           schedules.push(...this.generateScheduleDataForItemCharges(item, personId, timezone, childEnrolledItemIds?childEnrolledItemIds[index]:null));
        }

        logger.info("generateItemSchedulesForChild > schedules", schedules);
        for (const schedule of schedules) {
            const resp = await Meteor.callAsync('insertReservation', schedule);
            schedule.reservationId = resp.newReservationId
        }
        return schedules;
    }

    /**
     * Given an item, returns an array of timestamps for each day the item is scheduled for. Adapted from
     * registrationApprovalService.js::addUpdateChildPlansAndSchedules
     * @param item
     * @param timezone
     * @return {*[]}
     */
    static getItemDays(item, timezone) {
        const days = [];

        if (this.isDateRangeItem(item)) {
            days.push(...this.getItemDaysForDateRangeItem(item, timezone));
        } else if (this.isIndividualDatesItem(item)) {
            days.push(...this.getItemDaysForIndividualDates(item, timezone));
        } else if (this.isRecurringItem(item)) {
           days.push(...this.getItemDaysForRecurringDates(item, timezone));
        }

        return days;
    }
    /**
     * Determines if the maximum enrollment cap has been met for each item or selective week plan in the array.
     *
     * @param {string} orgId - The ID of the organization.
     * @param {Array<Object>} plansAndItemsArray - The array of item objects.
     * @param {Object | null} regData - The registration object if provided.
     * @returns {Promise<Array<Object>>} The array of items with the atCapacity property or null if invalid input.
     * @throws {Meteor.Error} If org ID or items array is missing or organization not found.
     */
    static async getItemAndSelectiveWeekAvailabilities(orgId, plansAndItemsArray, regData = null) {
        if (!orgId || !plansAndItemsArray || !plansAndItemsArray.length) {
            throw new Meteor.Error('Error getting plan/item availabilities: ', 'Missing org ID or items array');
        }

        const org = await Orgs.findOneAsync({ _id: orgId });
        if (!org) {
            throw new Meteor.Error('Error getting plan/item availabilities: ', 'Organization not found');
        }

        const timezone = org.getTimezone();
        const orgHasWeekends = org.hasCustomization(AvailableCustomizations.WEEKENDS_ENABLED);
        const orgScheduleTypes = org.getScheduleTypes();
        if (!orgScheduleTypes || !orgScheduleTypes.length) {
            return plansAndItemsArray;
        }

        // Loop over all available items and plans, determine if they have met the max daily enrollments or not
        for (const item of plansAndItemsArray) {
            const { type, details } = item;
            if (type === 'item') {
                await this.getItemEnrollmentCapacities(orgId, item, details?.scheduleType, timezone, orgScheduleTypes, regData, orgHasWeekends);
            } else if (type === 'plan' && details?.selectiveWeeks?.length) {
                await this.getSelectiveWeekEnrollmentCapacities(orgId, item, details?.scheduleType, timezone, orgScheduleTypes, regData, orgHasWeekends);
            }
        }

        return plansAndItemsArray;
    }

    static getCartItemsByScheduleType(scheduleTypeId, registration) {
        if (!registration) {
            return [];
        }
        const items = [];
        for (const planGroup of registration.plans || []) {
            for (const plan of planGroup) {
                if (plan.type === 'item' && plan.details?.scheduleType === scheduleTypeId) {
                    items.push(plan);
                }
            }
        }

        return items;
    }

    static countSelectedWeeksById(planId, registration) {
        const selectedWeeksCount = {};

        if (!registration || !registration.plans || !registration.plans.length) {
            return selectedWeeksCount;
        }

        registration.plans.flat().forEach(plan => {
            if (plan._id === planId && plan.details?.selectiveWeeks?.length > 0) {
                if (plan.selectedWeeks && plan.selectedWeeks.length) {
                    plan.selectedWeeks.forEach(weekIndex => {
                        if (!selectedWeeksCount[weekIndex]) {
                            selectedWeeksCount[weekIndex] = 0;
                        }
                        selectedWeeksCount[weekIndex]++;
                    });
                }
            }
        });

        return selectedWeeksCount;
    }

    /**
     * Determines if the maximum enrollment cap has been met for a specific item.
     *
     * @param {string} orgId - The ID of the organization.
     * @param {Object} item - The item object.
     * @param {string} scheduleType - The schedule type ID.
     * @param {string} timezone - The timezone string.
     * @param {Array} orgScheduleTypes - The array of organization schedule types.
     * @param {Object | null} regData - The registration object if provided.
     * @param {boolean} orgHasWeekends - Whether the organization has weekends enabled.
     * @returns {Promise<void>} - Updates the item object with the atCapacity property.
     */
    static async getItemEnrollmentCapacities(orgId, item, scheduleType, timezone, orgScheduleTypes, regData = null, orgHasWeekends = false) {
        let atCapacity = false;

        if (scheduleType) {
            const scheduleTypeAvailabilities = this.getMaxDailyEnrollmentsByScheduleType(scheduleType, orgScheduleTypes, orgHasWeekends);
            const itemDays = this.getItemDays(item, timezone);
            const itemsInCart = this.getCartItemsByScheduleType(scheduleType, regData);
            const cartItemsDays = itemsInCart.map(cartItem => this.getItemDays(cartItem, timezone));

            const usedAvailabilities = await this.getScheduleTypeUsage(orgId, itemDays, timezone, scheduleType);
            for (const dayIter in itemDays) {
                const day = itemDays[dayIter];
                const dayIndex = moment.tz(day, timezone).day();
                const availability = scheduleTypeAvailabilities[dayIndex] ?? AVAILABILITIES_PLACEHOLDER;
                const cartCount = cartItemsDays.filter(cartItemDays => cartItemDays.includes(day)).length;
                const usedAvailability = usedAvailabilities[dayIter] + cartCount;
                if (usedAvailability >= availability) {
                    atCapacity = true;
                    break;
                }
            }
        }
        item.atCapacity = atCapacity;
    }

    /**
     * Gets the IDs of active children in the organization.
     *
     * @param {string} orgId - The ID of the organization.
     * @returns {Array<string>} - The array of active children IDs.
     */
    static async getActiveChildrenIds(orgId) {
        return (await People.find({
            orgId: orgId,
            inActive: { $ne: true },
            type: 'person'
        }, { fields: {_id: 1} }).fetchAsync()).map((p) => p._id);
    }

    /**
     * Converts the start and end dates for a week from strings to timestamps.
     *
     * @param {string} weekStartDateString - The start date string of the week.
     * @param {string} weekEndDateString - The end date string of the week.
     * @param {string} timezone - The timezone string.
     * @returns {Object} - An object containing the start and end dates as timestamps.
     */
    static getStartEndDatesForWeek(weekStartDateString, weekEndDateString, timezone) {
        const startDate = new moment.tz(weekStartDateString, 'MM/DD/YYYY', timezone).startOf('day').valueOf();
        const endDate = new moment.tz(weekEndDateString, 'MM/DD/YYYY', timezone).startOf('day').valueOf();
        return { startDate, endDate };
    }

    /**
     * Gets the number of unique children with a schedule matching the given schedule type within the specified date range.
     *
     * @param {string} orgId - The ID of the organization.
     * @param {string} scheduleTypeId - The schedule type ID.
     * @param {number} weekStartDate - The start date of the week as a timestamp.
     * @param {number} weekEndDate - The end date of the week as a timestamp.
     * @returns {Promise<number>} - The number of unique children with a matching schedule.
     */
    static async getChildrenWithSchedule(orgId, scheduleTypeId, weekStartDate, weekEndDate) {
        // This method fetches the number of unique children who have a schedule matching the given schedule type and within the specified start and end dates.
        const activeChildrenIds = await this.getActiveChildrenIds(orgId);

        if (!activeChildrenIds.length) {
            return 0;
        }

        if (!weekStartDate || !weekEndDate) {
            return 0;
        }

        // We don't need to look at each day since selective weeks are always the full week only
        // so any schedules that do not fall in this exact range can be reasonably assumed to not be part of the selective week.
        const query = {
            orgId: orgId,
            scheduleType: scheduleTypeId,
            selectedPerson: {$in: activeChildrenIds},
            $and: [
                {cancellationReason: {$exists: false}},
                {cancellationReasonId: {$exists: false}},
                {cancellationDate: {$exists: false}},
            ],
            reservationType: 'person',
            recurringType: 'weekly',
            recurringFrequency: 1,
            scheduledDate: weekStartDate,
            scheduledEndDate: weekEndDate
        };

        const reservations = await Reservations.find(query).fetchAsync();
        const childIds = new Set();

        reservations.forEach((reservation) => {
            childIds.add(reservation.selectedPerson);
        });

        return childIds.size;
    }

    /**
     * Generates an array of timestamps for the start and end dates of the selective weeks for the given plan.
     *
     * @param {Object} plan - The plan object containing the weekly configuration.
     * @param {string} timezone - The timezone string.
     * @returns {Array<Object>} - An array of objects representing the start and end dates of each week the plan operates.
     */
    static getOperatingWeeks(plan, timezone) {
        // This method generates an array of timestamps for the start and end dates of the selective weeks for the given plan.
        const planOperatingWeeks = [];
        if (plan.details.selectiveWeeks && plan.details.selectiveWeeks.length) {
            plan.details.selectiveWeeks.forEach((week) => {
                const startEndTimes = this.getStartEndDatesForWeek(week[0], week[1], timezone);
                planOperatingWeeks.push(startEndTimes);
            });
        }

        return planOperatingWeeks;
    }

    /**
     * Updates the selective week plan week array with a boolean representing whether the plan is at capacity for any day of the selective week.
     *
     * @param {Array} curWeek - The current week array.
     * @param {Object} maxDailyEnrollments - The maximum daily enrollments.
     * @param {number} childCount - The number of children enrolled.
     * @param {string} timezone - The timezone string.
     */
    static getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone) {
        // This method updates the selective week plan week array with a boolean
        // representing whether the plan is at capacity based on the actual days in the week.
        if (!curWeek) {
            return;
        }

        // Get the day indices for the current week
        const dayIndices = DateTimeUtils.getDayIndexesForDateRange(curWeek, timezone);

        // Determine if any of the actual days in the selective week are at capacity
        const atCapacity = dayIndices.some((dayIndex) => {
            return childCount >= (maxDailyEnrollments[dayIndex] ?? AVAILABILITIES_PLACEHOLDER);
        });

        // Add the atCapacity status to the curWeek array
        curWeek.push(atCapacity);
    }

    /**
     * Determines if the maximum enrollment cap has been met for each selective week in the plan.
     *
     * @param {string} orgId - The ID of the organization.
     * @param {Object} plan - The plan object.
     * @param {string} scheduleTypeId - The schedule type ID.
     * @param {string} timezone - The timezone string.
     * @param {Array} orgScheduleTypes - The array of organization schedule types.
     * @param {Object | null} regData - The registration object if provided.
     * @param {boolean} orgHasWeekends - Whether the organization has weekends enabled.
     * @returns {Promise<void>} - Updates the plan object with the atCapacity property for each week.
     */
    static async getSelectiveWeekEnrollmentCapacities(orgId, plan, scheduleTypeId, timezone, orgScheduleTypes, regData = null, orgHasWeekends = false) {
        // If no schedule type just mark all weeks as open
        if (scheduleTypeId) {
            const maxDailyEnrollments = this.getMaxDailyEnrollmentsByScheduleType(scheduleTypeId, orgScheduleTypes, orgHasWeekends);
            const operatingWeeks = this.getOperatingWeeks(plan, timezone);
            const alreadySelectedWeeks = this.countSelectedWeeksById(plan._id, regData);
            for (const week of operatingWeeks) {
                const index = operatingWeeks.indexOf(week);
                const numberAlreadySelected = alreadySelectedWeeks[index] ?? 0;
                const curWeek = plan.details.selectiveWeeks[index];

                if (!curWeek) {
                    continue;
                }

                if (!week.startDate || !week.endDate) {
                    curWeek.push(false);
                    continue;
                }

                const childCount = await this.getChildrenWithSchedule(orgId, scheduleTypeId, week.startDate, week.endDate) + numberAlreadySelected;
                this.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            }
        } else {
            plan.details.selectiveWeeks.forEach((week) => {
                week.push(false);
            });
        }
    }

    static splitSelectiveWeekPlanIntoPlansArray(selectiveWeekPlan) {
        const { details, selectedWeeks } = selectiveWeekPlan;
        return selectedWeeks?.length && details?.selectiveWeeks?.length
            ? selectedWeeks.map(weekIndex => ({
                ...selectiveWeekPlan,
                startDate: details.selectiveWeeks[weekIndex][0],
                endDate: details.selectiveWeeks[weekIndex][1],
                selectedDays: ['mon', 'tue', 'wed', 'thu', 'fri']
            }))
            : [];
    }

    static mapSelectiveWeekPlansToReservationData(selectiveWeekPlans, timezone, childId) {
        if (!childId) {
            throw new Meteor.Error('Error mapping selective week plans: ', 'Missing child ID');
        }

        const mappedReservationData = [];
        selectiveWeekPlans.forEach((selectiveWeekPlan, index) => {
           const newReservation = {
               selectedPerson: [childId],
               reservationType: "person",
               groupId: 'default',
               scheduledDate: new moment.tz(selectiveWeekPlan.startDate, 'MM/DD/YYYY', timezone).valueOf(),
               scheduledEndDate: new moment.tz(selectiveWeekPlan.endDate, 'MM/DD/YYYY', timezone).valueOf(),
               overrideOverlap: true,
               scheduleType: selectiveWeekPlan.details?.scheduleType ?? '',
               recurringFrequency: 1,
               recurringDays: selectiveWeekPlan.selectedDays,
               scheduledTime: selectiveWeekPlan.details?.startTime ?? '',
               scheduledEndTime: selectiveWeekPlan.details?.endTime ?? '',
               linkedPlan: {
                   plan: selectiveWeekPlan._id,
                   allocations: [],
                   enrollment_date: selectiveWeekPlan.startDate,
                   expiration_date: selectiveWeekPlan.endDate,
                   incrementCreatedAt: index,
                   enrollment_forecast_start: selectiveWeekPlan.startDate,
               },
               generatedFromBillingCharge: selectiveWeekPlan._id
           }
              mappedReservationData.push(newReservation);
        });

        return mappedReservationData;
    }

    static async insertSelectiveWeekReservations(mappedReservationData) {
        let errors = [];
        for(const reservation of mappedReservationData){
            try {
                await Meteor.callAsync('insertReservation', reservation);
            } catch (e) {
                Log.error('Error inserting reservation: ', e.reason || e.message || e);
                errors.push(e.reason || e.message);
            }
        };

        if (errors.length) {
            throw new Meteor.Error('Error inserting reservations', errors.join(', '));
        }

        return true;
    }

    static async createSelectiveWeekReservations(childId, selectiveWeekPlan, timezone) {
        const splitOutPlansArray = this.splitSelectiveWeekPlanIntoPlansArray(selectiveWeekPlan);
        const mappedReservationData = this.mapSelectiveWeekPlansToReservationData(splitOutPlansArray, timezone, childId);
        await this.insertSelectiveWeekReservations(mappedReservationData);
    }
}
