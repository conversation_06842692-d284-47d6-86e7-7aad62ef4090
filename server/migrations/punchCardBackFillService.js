import { People } from "../../lib/collections/people";

export class PunchCardBackFillService {
    static async backFillPunchCardDays() {
        const peopleWithoutPunchCardDays = await People.find({
            totalPunchCardDays: {$exists: false},
            "billing.enrolledPunchCards": {$exists: true, $not: {$size: 0}}
        }).fetchAsync();

        if (!peopleWithoutPunchCardDays || peopleWithoutPunchCardDays.length === 0) {
            return;
        }

        for (const person of peopleWithoutPunchCardDays) {
            const daysNeeded = this.getDaysNeeded(person);
            await this.addPunchCardDays(person, daysNeeded);
        }
    }

    static getDaysNeeded(person) {
        let totalDays = 0;
        if (person.billing && person.billing.enrolledPunchCards && person.billing.enrolledPunchCards.length > 0) {
            for (const punchCard of person.billing.enrolledPunchCards) {
                const numberOfDays = parseInt(punchCard.originalItem.numberOfDays, 10);
                const quantity = parseInt(punchCard.quantity, 10);
                totalDays += numberOfDays * quantity;
            }
        }
        return totalDays;
    }

    static async addPunchCardDays(person, daysToAdd) {
        const zerosToAdd = Array(daysToAdd).fill(0);
        await People.updateAsync(
            {_id: person._id},
            {$set: {totalPunchCardDays: zerosToAdd}}
        );
    }
}