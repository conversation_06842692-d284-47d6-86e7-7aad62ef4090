import { runInvoices } from "../util";

/**
 * Rerun Invoice Generation Cron Process.
 * @class RerunInvoiceGenerationCronProcess
 */
export class RerunInvoiceGenerationCronProcess {
    /**
     * Run the process.
     * @returns {Promise<void>}
     */
    static async run() {
        const delay = ms => new Promise(res => setTimeout(res, ms));
        try {
            await runInvoices({ timezone: 'Eastern Time' });
        } catch (err) {
            console.log("Error running invoices for eastern time", err);
        }
        delay(5000);
        try {
            await runInvoices({ timezone: 'Central Time' });
        } catch (err) {
            console.log("Error running invoices for central time", err);
        }
        delay(5000);
        try {
            await runInvoices({ timezone: 'Mountain Time' });
        } catch (err) {
            console.log("Error running invoices for mountain time", err);
        }
        delay(5000);
        try {
            await runInvoices({ timezone: 'REMAINING Time' });
        } catch (err) {
            console.log("Error running invoices for remaining timezones", err);
        }
    }
}