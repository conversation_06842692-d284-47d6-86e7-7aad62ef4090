import { fetch } from "meteor/fetch"
import _ from '../lib/util/underscore';
import { People } from "../lib/collections/people";
import { Orgs } from "../lib/collections/orgs";

const fs = require('fs');
const path = require('path');
const Bottleneck = require('bottleneck');

const moment = require("moment-timezone");

const HUBSPOT_API_URL = Meteor.settings.hubspotApi?.url || "https://api.hubapi.com";

const DEFAULT_HUBSPOT_API_TOKEN = Meteor.settings.lspHubspotKey;

// Get a Hubspot API token from https://app.hubspot.com/l/api-key
// Our app is available at https://app.hubspot.com/private-apps/23318157

const DEFAULT_HUBSPOT_MAPPINGS = {
	"startDate": {
		"0": "child_1_start_date",
		"1": "child_2_start_date",
		"2": "child_3_start_date",
		"3": "child_4_start_date",
		"4": "child_5_start_date",
	},
	"withdrawDate": {
		"0": "child_1_leave_date",
		"1": "child_2_leave_date",
		"2": "child_3_leave_date",
		"3": "child_4_leave_date",
		"4": "child_5_leave_date",
	},
	"birthday": {
		"0": "child_birthday",
		"1": "child_2_birthday",
		"2": "child_3_birthday",
		"3": "child_4_birthday",
		"4": "child_5_birthday",
	},
	"name": {
		"0": "child_name_",
		"1": "child_2_name_",
		"2": "child_3_name_",
		"3": "child_4_name_",
		"4": "child_5_name_",
	}
};

export class HubspotService {
	static async getContact({ contactId }) {
		let res = await fetch(`${HUBSPOT_API_URL}/crm/v3/objects/contacts/${contactId}`, {
			method: "GET",
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${DEFAULT_HUBSPOT_API_TOKEN}`
			}
		});
		const contactResponse = await res.json();

		if (contactResponse.status === "error") {
			throw new Error(`Hubspot error for contact ID ${contactId}: ${contactResponse.message}`);
		}

		return contactResponse;
	}

	static async updateContact({ contactId, updateDoc }) {

		let res = await fetch(`${HUBSPOT_API_URL}/crm/v3/objects/contacts/${contactId}`, {
			method: "PATCH",
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${DEFAULT_HUBSPOT_API_TOKEN}`
			},
			body: JSON.stringify({
				"properties": updateDoc
			})
		});

		const contactUpdateResponse = await res.json();

		if (contactUpdateResponse.status === "error") {
			throw new Error(`Hubspot error for contact ID ${contactId}: ${contactUpdateResponse.message}`);
		}

		return contactUpdateResponse;
	}

	static async downloadContacts(downloadToPath) {

		const defaultProperties = [
			'createdate',
			'email',
			'firstname',
			'hs_object_id',
			'lastmodifieddate',
			'lastname',
		];

		const additionalProperties = Object.values(DEFAULT_HUBSPOT_MAPPINGS).reduce((acc, val) => {
			return acc.concat(Object.values(val));
		}, []);

		const finalProperties = defaultProperties.concat(additionalProperties);

		const url = `${HUBSPOT_API_URL}/crm/v3/objects/contacts?archived=false&limit=100&properties=${finalProperties.join(",")}`;

		const headers = {
			'Content-Type': 'application/json',
			'Authorization': `Bearer ${DEFAULT_HUBSPOT_API_TOKEN}`
		};

		let contacts = [];
		let moreResultsAvailable = true;
		let after = null;  // Used for pagination.

		let itemCount = 0;

		while (moreResultsAvailable) {
			let urlWithParams = url;

			if (after) {
				urlWithParams += `&after=${after}`;
			}

			let res = await fetch(urlWithParams, {
				method: "GET",
				headers
			});

			const contactsResponse = await res.json();

			if (contactsResponse.status === "error") {
				throw new Error(`Hubspot error: ${contactsResponse.message}`);
			}

			// Write this JSON item to WRITE_HOSPOT_TO_DIR.
			// It should be like itemCount, where itemCount is formatted like 00001

			itemCount += 1;

			const itemCountStr = itemCount.toString().padStart(5, "0");

			const filename = path.join(downloadToPath, `hubspot_contact_${itemCountStr}.json`);

			// Write the JSON using FS:

			fs.writeFileSync(filename, JSON.stringify(contactsResponse.results, null, 2));

			contacts = [...contacts, ...contactsResponse.results];

			// Update moreResultsAvailable and after for pagination.
			moreResultsAvailable = contactsResponse.paging ? contactsResponse.paging.next : null;
			after = moreResultsAvailable ? contactsResponse.paging.next.after : null;

		}

		// Filter contacts by orgId if necessary. 
		// Note: This is a naive implementation and may be not optimal for large datasets.
		// Check if Hubspot API supports filtering by orgId directly.
		// contacts = contacts.filter(contact => contact.orgId === orgId);

		return contacts;
	}


	static async syncAll() {
		const allOrgs = await Orgs.find({ "customizations.integrations/hubspot/enabled": true }).fetchAsync();

		console.log("Start Hubspot syncAll");

		for (const org of allOrgs) {
			console.log(`Syncing org ${org._id} ${org.name}`)
			await this.syncOrg({ orgId: org._id });
		}

		console.log("Finished Hubspot syncAll");
	}

	static async syncOrg({ orgId }) {

		const self = this;

		const org = await Orgs.findOneAsync({ _id: orgId })

		// Raise an exception if the org is not found.

		if (!org) {
			//
			/// The following few lines can be useful for trouble-shooting
			// this script. They are commented out because a unit test is written
			// to properly handle this missing org case.
			//
			// May want to consider uncommenting this and maybe removing the
			// console.warn (which is used because this is a server-side script)
			//
			//const allOrgs = Orgs.find({}).fetch();
			// Create a string where there's one line per Org and
			// that line is .name (._id):
			//const allOrgsString = allOrgs.map(o => `${o.name} (id: ${o._id})`).join("\n");
			// console.warn(`Org not found: ${orgId}. All Orgs:\n${allOrgsString}`);
			throw new Error(`Org not found: ${orgId}`);
		}

		const profilePrefix = org.profileDataPrefix() ? `${org.profileDataPrefix()}.` : "",
			hubspotFamilies = await People.find({ orgId, type: "family", "hubspotId": { "$ne": null }, hubspotFamilyDisenrolledAt: { "$eq": null } }).fetchAsync();

		// If there are no hubspotFamilies, log a message and return.

		if (hubspotFamilies.length === 0) {
			console.warn("No hubspotFamilies found for org:", org._id, org.name);
			return;
		}

		console.log(`Processing ${hubspotFamilies.length} hubspotFamilies for org: ${org._id}, ${org.name}`);


		// Set up the limiter. Details:

		/*
		minTime is set to 100 ms, ensuring there is at least a 100 ms gap
		between job starts. The reservoir and related options are configured
		to refresh every 10 seconds (10,000 ms), adding 100 new jobs each time. 
		Thus, you get a maximum of 100 jobs every 10 seconds.
		*/

		const limiter = new Bottleneck({
			minTime: 100,
			reservoir: 100,
			reservoirRefreshAmount: 100,
			reservoirRefreshInterval: 10 * 1000,
		});

		async function handleFamilyItem(hubspotFamily) {

			const updateFamilyDoc = {};
			updateFamilyDoc["firstname"] = hubspotFamily.firstName;
			updateFamilyDoc["lastname"] = hubspotFamily.lastName;
			updateFamilyDoc["email"] = await hubspotFamily.getEmailAddress();

			const hubspotRelations = await People.find({ orgId, type: "person", "hubspotId": hubspotFamily.hubspotId }).fetchAsync();
			let activePersonCount = 0, highestWithdrawDateValue = 0, waitlistAddedDateValue;

			// If there are no hubspotRelations, log a message and return.

			if (hubspotRelations.length === 0) {
				console.log("No hubspotRelations found for hubspotFamily:", hubspotFamily._id, hubspotFamily.name);
				return Promise.resolve();
			}

			await Promise.all(hubspotRelations.map(async hubspotRelation => {
				// console.log("Evaluating", profilePrefix, hubspotRelation.firstName);
				const personIdx = hubspotRelation.importIndex,
					hsStartDateKey = DEFAULT_HUBSPOT_MAPPINGS["startDate"][personIdx.toString()],
					mpStartDateKey = `${profilePrefix}enrollmentDate`,
					hsWithdrawDateKey = DEFAULT_HUBSPOT_MAPPINGS["withdrawDate"][personIdx.toString()],
					mpWithdrawDateKey = `${profilePrefix}withdrawDate`,
					mpBirthdayKey = `${profilePrefix}birthday`,
					hsBirthdayKey = DEFAULT_HUBSPOT_MAPPINGS["birthday"][personIdx.toString()],
					mpName = (hubspotRelation.firstName || "") + " " + (hubspotRelation.lastName || "");
				// console.log("keys", mpStartDateKey, mpWithdrawDateKey);
				// console.log("dates", _.deep(hubspotRelation, mpStartDateKey), _.deep(hubspotRelation, mpWithdrawDateKey));
				updateFamilyDoc[hsStartDateKey] = _.deep(hubspotRelation, mpStartDateKey) ? new moment(_.deep(hubspotRelation, mpStartDateKey)).utc().startOf("day").toISOString() : null;
				updateFamilyDoc[hsWithdrawDateKey] = _.deep(hubspotRelation, mpWithdrawDateKey) ? new moment(_.deep(hubspotRelation, mpWithdrawDateKey)).utc().startOf("day").toISOString() : null;
				if (_.deep(hubspotRelation, mpWithdrawDateKey) > highestWithdrawDateValue)
					highestWithdrawDateValue = _.deep(hubspotRelation, mpWithdrawDateKey);
				else if (!_.deep(hubspotRelation, mpWithdrawDateKey))
					activePersonCount++;
				if (hubspotRelation["waitlistAddedDate"])
					waitlistAddedDateValue = hubspotRelation["waitlistAddedDate"];
				if (_.deep(hubspotRelation, mpBirthdayKey))
					updateFamilyDoc[hsBirthdayKey] = new moment(_.deep(hubspotRelation, mpBirthdayKey)).utc().startOf("day").toISOString();
				const hsNameKey = DEFAULT_HUBSPOT_MAPPINGS["name"][personIdx.toString()];
				updateFamilyDoc[hsNameKey] = mpName;
			}));

			if (waitlistAddedDateValue)
				updateFamilyDoc["enrollment_date"] = new moment(waitlistAddedDateValue).utc().startOf("day").toISOString();
			if (activePersonCount === 0) {
				if (highestWithdrawDateValue) {
					updateFamilyDoc["leave_date"] = new moment(highestWithdrawDateValue).utc().startOf("day").toISOString();
				}
				await People.updateAsync({ _id: hubspotFamily._id }, { "$set": { "hubspotFamilyDisenrolledAt": new Date().valueOf() } });
			}

			try {
				const result = await self.updateContact({
					contactId: hubspotFamily.hubspotId,
					updateDoc: updateFamilyDoc
				});
				return Promise.resolve({
					error: false,
					message: "success",
					result
				});
			} catch (e) {
				// console.error("error updating hubspot family", hubspotFamily._id, updateFamilyDoc, e);
				return Promise.reject({
					error: true,
					message: `${e}`
				});
			}

		}

		let tasks = [];

		let processedCount = 0;
		let successCount = 0
		const errorMessages = [];

		for (const hubspotFamily of hubspotFamilies) {
			let task = limiter.schedule(() => handleFamilyItem(hubspotFamily))
				.then(result => {
					// TODO: Something better with the result.
					successCount += 1;
				})
				.catch(err => {
					// handle error
					// console.error('error with family', hubspotFamily, ':', err);
					errorMessages.push(err.message);
				}).finally(() => {
					processedCount += 1;
					// If it's every 10 families, log the count.
					if (processedCount % 10 === 0) {
						console.log(`Processed ${processedCount} families for org: ${org._id}, ${org.name}.`)
						console.log(`Success: ${successCount}, Error: ${errorMessages.length}`)
					}
				});

			tasks.push(task);
		}

		await Promise.all(tasks);

		console.log(`Processed ${processedCount} hubspotFamilies for org: ${org._id}, ${org.name}.`)
		console.log(`Success: ${successCount}, Error: ${errorMessages.length}`)

		// If there's an error, log all unique error messages:

		if (errorMessages.length > 0) {
			// Iterate over each and log unique messages in their own line.
			const uniqueErrorMessages = _.uniq(errorMessages);
			uniqueErrorMessages.forEach(errorMessage => {
				console.error(errorMessage);
			});
		}

		return Promise.resolve({
			error: false,
			message: "success",
			processedCount,
			successCount,
			errorCount: errorMessages.length
		});
	}
}