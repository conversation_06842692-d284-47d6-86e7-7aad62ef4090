import Stripe from 'stripe';
import {StripeProvider} from './card_providers/stripeProvider';
import { People } from '../lib/collections/people';
import { Orgs } from '../lib/collections/orgs';
import _ from '../lib/util/underscore';

const stripe = new Stripe(Meteor.settings.stripe.secretKey,
	{apiVersion:"2019-08-14"});

Meteor.methods({
	async replacePaymentMethod(options) {
		const currentUser =  await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || 
			(!(await Orgs.current()).billing.stripeAccountId) ||
			!(cuser.type == "admin" || 
				(cuser.type == "family" && currentUser.personId == options.personId))
			)
			throw new Meteor.Error(403, "Access denied");
		
		const currentPerson = await People.findOneAsync({orgId: currentUser.orgId, _id: options.personId}),
			currentOrg = await currentPerson.findOrg();

		try {
			let customer;
			if (!_.deep(currentPerson, "billing.stripeInfo")) {
				customer = await stripe.customers.create({
					source: options.token.id,
					description: currentPerson.lastName + ", " + currentPerson.firstName,
					metadata: { personId: currentPerson._id, firstName: currentPerson.firstName, lastName: currentPerson.lastName }
				}, {stripe_account: (await Orgs.current()).billing.stripeAccountId});
			} else {
				new_source = await stripe.customers.createSource(currentPerson.billing.stripeInfo.id, {source: options.token.id}, {stripe_account: (await Orgs.current()).billing.stripeAccountId});
				customer = await StripeProvider.removeCustomerSourceByType({paymentType: new_source.object, personId: currentPerson._id, new_source});
			}
			
			await People.updateAsync({_id: currentPerson._id}, {$set:{"billing.stripeInfo": customer}});
		} catch (error) {
			console.log(error);
			throw new Meteor.Error(500, "Issue with payment method " + options.sourceType + " creation: " + error.message);
		}	
	},



	async verifyBankAccount(options) {
		const currentUser =  await Meteor.userAsync();
		const cuser = await currentUser.fetchPerson();
		if (!currentUser || 
			!(cuser.type == "admin" ||
				(cuser.type == "family" && currentUser.personId == options.personId)))
			throw new Meteor.Error(403, "Access denied");
		
		const currentPerson = await People.findOneAsync({orgId: currentUser.orgId, _id: options.personId});
		try {
			const bank_source = await currentPerson.connectedBankAccount();
			const sourceAmounts = [ options.deposit_amount_1, options.deposit_amount_2 ];
			
			let verified_source = await stripe.customers.verifySource(currentPerson.billing.stripeInfo.id, bank_source.originalObject.id, {amounts: sourceAmounts}, {stripe_account: (await Orgs.current()).billing.stripeAccountId});
			let customer = await stripe.customers.retrieve(currentPerson.billing.stripeInfo.id, {stripe_account: (await Orgs.current()).billing.stripeAccountId});
			await People.updateAsync({_id: currentPerson._id}, {$set:{"billing.stripeInfo": customer}});
		} catch (error) {
			console.log(error);
			throw new Meteor.Error(500, "Issue with bank account verification: " + error.message);
		}
	}
});

