import { BaseImporter } from '../BaseImporter';
import { ParentImporter } from './ParentImporter';
import { AddressImporter } from './AddressImporter';
import { ChildImporter } from './ChildImporter';
import { RelationshipImporter } from './RelationshipImporter';
import { EnrollmentImporter } from './EnrollmentImporter';
import { ProgramPlanImporter } from './ProgramPlanImporter';
import { ScheduleImporter } from './ScheduleImporter';


/**
 * FamilyImporter coordinates multiple sub-importers to ingest parent, child,
 * enrollment, relationship, and address records from a single flattened CSV file.
 *
 * Each sub-importer is responsible for validation, transformation, and insert logic
 * for its relevant section. Shared context (e.g. created `_id`s) is passed between them.
 */
export class FamilyImporter extends BaseImporter {
    constructor({ fileContent, preview = true, rootOrg, allOrgs }) {
        super({ fileContent, preview, rootOrg, allOrgs });
        this.context = { rootOrg, allOrgs }; // shared context

        // Initialize sub-importers
        this.subImporters = [
            new ParentImporter({ preview, context: this.context }),
            new AddressImporter({ preview, context: this.context }),
            new ChildImporter({ preview, context: this.context }),
            new RelationshipImporter({ preview, context: this.context }),
            new EnrollmentImporter({ preview, context: this.context }),
            new ProgramPlanImporter({ preview, context: this.context }),
            new ScheduleImporter({ preview, context: this.context })
        ];
    }

    get requiredFields() {
        return ['idSponsor', 'spon_sponsor.FirstName', 'spon_sponsor.LastName', 'isActive', 'isDelete', 'idStudent', 'std_student.FirstName', 'std_student.LastName', 'idGrade', 'idSite', 'idEnrollmentStatus'];
    }

    /**
     * For each row in the CSV, delegate to each sub-importer.
     * Stop short if any importer throws or returns an error.
     */
    async run() {
        this.data = this.parseCSV();

        for (let i = 0; i < this.data.length; i++) {
            const row = this.data[i];

            // Basic required field check (could also be done in a parent sub-importer)
            const missing = this.requiredFields.filter(f => !(row[f]?.trim()));
            if (missing.length) {
                this.errors.push(`Row ${i + 1}: Missing required fields: ${missing.join(', ')}`);
                continue;
            }

            for (const importer of this.subImporters) {
                const success = await importer.process(row, i);
                if (!success) {
                    this.errors.push(...importer.getErrors());
                }
            }
        }

        return this.errors;
    }

    async commit() {
        this.preview = true;
        const previewErrors = await this.run();
        if (previewErrors.length > 0) {
            return previewErrors;
        }

        this.preview = false;

        // Recreate sub-importers so they're in a fresh state
        this.subImporters.forEach((imp) => imp.clearErrors());
        return await this.run();
    }
}
