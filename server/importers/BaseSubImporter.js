export class BaseSubImporter {
    constructor({ preview = true, context = {} }) {
        this.preview = preview;
        this.context = context;
        this.errors = [];
    }

    /**
     * A map of field validators that validate values for specific columns.
     * Each validator should return `true` or a string describing the error.
     * @returns {Object<string, function>}
     */
    get fieldValidators() {
        return {};
    }

    /**
     * Override to check whether a transformed object is unique.
     * @param {Object} _object - The transformed data object
     * @returns {Promise<boolean | string>} - returns true or error message describing the uniqueness issue
     */
    async isUnique(_object) {
        return true;
    }

    /**
     * Override in subclasses. Transform a CSV row into a document.
     * @param {Object} row - The raw CSV row.
     * @returns {Promise<Object>} - The transformed object.
     */
    async transformRow(row) {
        return row;
    }

    /**
     * Override in subclasses. Insert or update the document in the DB.
     * @param {Object} transformed - The transformed object.
     * @returns {Promise<*>} - Result of the insert/update operation.
     */
    async insertRow(transformed) {
        return null;
    }

    /**
     * Process a row: validate, transform, (optionally) insert.
     * @param {Object} row - The raw row to process.
     * @param {number} index - Row index, for error reporting.
     * @returns {Promise<boolean>} - Whether the row was successful.
     */
    async process(row, index) {
        const validators = this.fieldValidators;

        for (const [field, validateFn] of Object.entries(validators)) {
            const value = row[field];
            const result = validateFn(value, row, index);

            if (result !== true) {
                this.errors.push(`Row ${index + 1}: ${field} - ${result}`);
            }
        }

        const transformed = await this.transformRow(row);
        const unique = await this.isUnique(transformed);

        if (unique !== true) {
            this.errors.push(`Row ${i + 1}: ${unique}`);
            return false;
        }

        if (this.errors.length) {
            return false;
        }

        if (!this.preview) {
            try {
                await this.insertRow(transformed);
            } catch (e) {
                this.errors.push(`Row ${index + 1}: Failed to insert - ${e.message}`);
                return false;
            }
        }

        return true;
    }

    /**
     * Returns the collected error messages.
     * @returns {string[]}
     */
    getErrors() {
        return this.errors;
    }

    clearErrors() {
        this.errors = [];
    }
}

