import { BaseImporter } from './BaseImporter';
import moment from 'moment-timezone';
import { Orgs } from '../../lib/collections/orgs';
import { AvailableCustomizations } from '../../lib/customizations';
import { BillingUtils } from '../../lib/util/billingUtils';
import { ITEM_TYPE } from '../../lib/constants/billingConstants';

export class ItemImporter extends BaseImporter {
  constructor(params) {
    super(params);
    this._processedDropIn = {};
    this.orgNames = new Set(this.allOrgs?.map(org => org.name) || []);
    this.orgNameAndId = new Map(this.allOrgs?.map(org => [org.name, org._id]) || []);
    this.transformedPlansAndItems = this.transformPlansAndItems();
    this._processedDescriptions = new Map(); // Track descriptions for current import
    this._processedProgramDetails = new Map(); // Track program details for current import
  }

   /**
   * Transforms the plansAndItems array into a more accessible format
   * @returns {Object} Object with org names as keys and their plans/items as values
   */
    transformPlansAndItems() {
      const transformed = {};
      
      this.allOrgs.forEach(org => {
        if (org.billing?.plansAndItems) {
          transformed[org.name] = org.billing.plansAndItems.map(item => ({
            description: item.description,
            programDetails: item.programDetails,
            program: item.program,
            amount: item.amount
          }));
        }
      });
  
      return transformed;
    }

  /**
   * Helper to check boolean values.
   * Only accepts "true" or "false" as valid values.
   * @private
   * @param {any} value Input value
   * @returns {boolean|null} True, False, or null if invalid
   */
  isValidBoolean(value) {
    if (typeof value === 'boolean') {
      return value;
    }

    if (typeof value === 'string') {
      const lowerVal = value.trim().toLowerCase();
      if (lowerVal === 'true') return true;
      if (lowerVal === 'false') return false;
    }

    return null;
  }

  /**
   * Helper to validate HTML content
   * @private
   * @param {string} value Input HTML string
   * @returns {boolean} True if valid HTML, false otherwise
   */
  isValidHTML(value) {
    if (typeof value !== 'string') {
      return false;
    }

    // Remove DOCTYPE and comments if present
    const cleanHtml = value
      .replace(/<!DOCTYPE[^>]*>/i, '')
      .replace(/<!--[\s\S]*?-->/g, '');

    // Basic HTML structure validation
    const hasOpeningTag = /<[^>]+>/.test(cleanHtml);
    const hasClosingTag = /<\/[^>]+>/.test(cleanHtml);
    
    // Check for balanced tags
    const stack = [];
    const tagRegex = /<\/?([a-z][a-z0-9-]*)\b[^>]*>/gi;
    let match;
    let isValid = true;

    // List of self-closing tags
    const selfClosingTags = new Set([
      'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input',
      'link', 'meta', 'param', 'source', 'track', 'wbr'
    ]);

    while ((match = tagRegex.exec(cleanHtml)) !== null) {
      const fullTag = match[0];
      const tag = match[1].toLowerCase();

      // Skip self-closing tags
      if (selfClosingTags.has(tag) || fullTag.endsWith('/>')) {
        continue;
      }

      if (fullTag.startsWith('</')) {
        // Closing tag
        if (stack.length === 0 || stack.pop() !== tag) {
          isValid = false;
          break;
        }
      } else {
        // Opening tag
        stack.push(tag);
      }
    }

    // Check if all tags were properly closed
    if (stack.length !== 0) {
      isValid = false;
    }

    return hasOpeningTag && hasClosingTag && isValid;
  }

  /**
   * Helper to validate MM/DD/YYYY date format using robust checks.
   * @private // Indicates this method is intended for internal use.
   * @param {string | any} value Input date string or other value.
   * @returns {boolean} True if valid format and a valid date, false otherwise. Handles empty/null as true (optional).
   */
  isValidMMDDYYYY(value) {
    if (value === null || value === undefined || String(value).trim() === '') {
      return true;
    }

    if (typeof value !== 'string') {
      return false;
    }

    const trimmed = value.trim();
    const regex = /^([1-9]|0?[1-9]|1[0-2])\/([1-9]|0?[1-9]|[12]\d|3[01])\/\d{4}$/;
    if (!regex.test(trimmed)) {
      return false;
    }

    const [month, day, year] = trimmed.split('/').map(Number);
    const date = new Date(year, month - 1, day);
    const isValidDate = date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;
    return isValidDate;
  }

  /**
   * Helper to validate HH:MM AM/PM time format.
   * @private // Indicates this method is intended for internal use.
   * @param {string | any} value Input time string or other value.
   * @returns {boolean} True if valid format, false otherwise.
   */
  isValidTime(value) {
    if (typeof value !== 'string') {
      return false;
    }

    return /^(0?[1-9]|1[0-2]):[0-5]\d\s*(AM|PM)$/.test(value.trim());
  }

  /**
   * Helper to validate currency format (allowing $ and commas).
   * @private // Indicates this method is intended for internal use.
   * @param {string | number | any} value Input currency string or number.
   * @returns {boolean} True if valid format, false otherwise.
   */
  isValidCurrency(value) {
    if (typeof value === 'number') {
      return true;
    }

    if (typeof value !== 'string') {
      return false;
    }

    const trimmedValue = value.trim();
    if (trimmedValue === '') {
      return false;
    }

    const currencyRegex = /^\$?\s*((\d{1,3}(,\d{3})*)|\d+)?(\.\d{0,2})?$/;

    return currencyRegex.test(trimmedValue);
  }

  /**
   * Helper to validate 'Recurring - On' days based on the Date Type.
   * @private // Indicates this method is intended for internal use.
   * @param {string | any} value Input string containing days (e.g., "Monday, Tuesday").
   * @param {string} dateType The type of date (e.g., "recurring", "once").
   * @returns {true | string} True if valid; otherwise, an error message string.
   */
  isValidRecurringOn(value, dateType) {
    const allowedDays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const lowerCaseAllowed = allowedDays.map((d) => d.toLowerCase());

    if (value === null || value === undefined || String(value).trim() === '') {
      if (dateType === 'recurring') {
        return 'Recurring - On (day of week) is required when Date Type is "Recurring".';
      }
      return true; // valid for non-recurring if empty
    }

    const days = String(value)
      .split(',')
      .map((d) => d.trim())
      .filter((d) => d);

    if (dateType === 'recurring' && days.length === 0) {
      return 'Recurring - On cannot be empty when Date Type is "Recurring".';
    }

    const invalidDays = days.filter((day) => !lowerCaseAllowed.includes(day.toLowerCase()));
    if (invalidDays.length > 0) {
      return `Value must be a day of the week`;
    }

    return true;
  }

  /**
   * Required fields based on the specification.
   * @returns {string[]}
   */
  get requiredFields() {
    return ['LL Destination', 'Site/Organization Name','Description', 'Amount (Flat Rate)', 'Refundable Deposit'];
  }

  /**
   * Field-level validation rules.
   * @returns {Object<string, Function>}
   */
  get fieldValidators() {
    return {
      'Site/Organization Name': (value) => {
        const orgExists = this.orgNames.has(value);
        return orgExists ? true : 'Site/Organization Name must exist within the hierarchy.';
      },
      'Description': (value, row, rowIndex) => {
        if (!value) {
          return 'Missing "Description"';
        }

        // Check for spaces
        if (value.includes(' ')) {
          return 'Must be a Unique value at org without any spaces';
        }

        const orgName = row['Site/Organization Name'];
        
        // Check for duplicates in database using transformedPlansAndItems
        const orgItems = this.transformedPlansAndItems[orgName] || [];
        const existsInDB = orgItems.some(item => item.description === value);
        if (existsInDB) {
          return `Must be a  Unique value at org without any spaces`;
        }

        // Check for duplicates in current import
        if (!this._processedDescriptions.has(orgName)) {
          this._processedDescriptions.set(orgName, new Set());
        }
        const orgDescriptions = this._processedDescriptions.get(orgName);
        if (orgDescriptions.has(value)) {
          return `Must be a  Unique value at org without any spaces`;
        }
        orgDescriptions.add(value);

       
        if (rowIndex === this.data.length - 1) {
          this._processedDescriptions.clear();
        }

        return true;
      },
      'Program Details': (value, row, rowIndex) => {

        const orgName = row['Site/Organization Name'];
        
        // Check for valid HTML format
        if (value && !this.isValidHTML(value)) {
          return 'Must be in HTML format and be a unique value at org.';
        }

        // Check for duplicates in database using transformedPlansAndItems
        const orgItems = this.transformedPlansAndItems[orgName] || [];
        
        const normalizeHTML = (html) => {
          return html
            .replace(/\s+/g, ' ')  // Replace multiple spaces with single space
            .replace(/\n/g, '')    // Remove newlines
            .replace(/\r/g, '')    // Remove carriage returns
            .replace(/\t/g, '')    // Remove tabs
            .trim();               // Remove leading/trailing whitespace
        };

        const existsInDB = orgItems.some(item => {
          const normalizedExisting = normalizeHTML(item.programDetails || '');
          const normalizedValue = normalizeHTML(value || '');
          return normalizedExisting === normalizedValue;
        });
        
        if (value && existsInDB) {
          return `Must be in HTML format and be a unique value at org.`;
        }

        // Check for duplicates in current import
        if (!this._processedProgramDetails.has(orgName)) {
          this._processedProgramDetails.set(orgName, new Set());
        }
        const orgProgramDetails = this._processedProgramDetails.get(orgName);
        if (value && orgProgramDetails.has(value)) {
          return `Must be in HTML format and be a unique value at org.`;
        }
        orgProgramDetails.add(value);

        if (rowIndex === this.data.length - 1) {
          this._processedProgramDetails.clear();
        }

        return true;
      },
      'Program': (value, row) => {
        const program = row['Program'];
        if (!program) {
          return true; // Program is optional
        }

        const orgName = row['Site/Organization Name'];
        
        // Find the specific organization
        const org = this.allOrgs.find(org => org.name === orgName);
        
        // Check if program exists in this specific organization
        const programExists = org?.billing?.programs?.some(programObj => 
          programObj.name === program
        );

        if (org && !programExists) {
          return `Data must match an existing program`;
        }

        return true;
      },
      
      'Amount (Flat Rate)': (value) => {
        return this.isValidCurrency(value)
            ? true
            : 'Data must be in Currency Format';
      },
      'Default Expiration': (value) => {
        if (!value) {
          return true;
        }
        return this.isValidMMDDYYYY(value) ? true : 'Data must be in MM/DD/YYYY format';
      },
      'Ledger Account': (value) => {
        if (value === null || value === undefined || String(value).trim() === '') {
          return true;
        }
        return this.isValidBoolean(value) !== null
            ? true
            : 'Value must either be "true" or "false"';
      },
      'Refundable Deposit': (value) => {
        return this.isValidCurrency(value) ? true : 'Data must be in Currency Format';
      },
      'Drop In Daily Rate': (value) => {
        if (value === null || value === undefined || String(value).trim() === '') {
          return true;
        }
        const isValid = this.isValidBoolean(value);
        if (isValid === null) {
          return 'Value must either be "true" or "false" ';
        }

        return true;
      },
     
      'Date Type': (value) => {
        if (!value) {
          return true;
        }
        const allowedTypes = ['Date Range', 'Recurring', 'Individual Dates'];
        const trimmedValue = value?.trim();
        return allowedTypes.some((type) => type.toLowerCase() === trimmedValue?.toLowerCase())
            ? true
            : `Value must be a date range`;
      },
      'Date Range - Start Date': (value, row) => {
        const dateType = row['Date Type']?.trim().toLowerCase();
        if (dateType === 'date range') {
          if (!value) {
            return 'Value must be in MM/DD/YYYY format';
          }

          if (!this.isValidMMDDYYYY(value)) {
            return 'Value must be in MM/DD/YYYY format';
          }
        }
        return true;
      },
      'Date Range - End Date': (value, row) => {
        const dateType = row['Date Type']?.trim().toLowerCase();
        if (dateType === 'date range') {
          if (!value) {
            return 'Value must be in MM/DD/YYYY format';
          }

          if (!this.isValidMMDDYYYY(value)) {
            return 'Value must be in MM/DD/YYYY format';
          }

          const startDateStr = row['Date Range - Start Date'];
          if (startDateStr && this.isValidMMDDYYYY(startDateStr)) {
            const startDate = moment(startDateStr, 'MM/DD/YYYY', true);
            const endDate = moment(value, 'MM/DD/YYYY', true);
            if (startDate.isValid() && endDate.isValid() && endDate.isSameOrBefore(startDate)) {
              return 'Date Range - End Date must be after Date Range - Start Date.';
            }
          }
        } else if (value && !this.isValidMMDDYYYY(value)) {
          return 'Date Range - End Date must be in MM/DD/YYYY format.';
        }
        return true;
      },
      'Recurring - Start Date': (value, row) => {
        const dateType = row['Date Type']?.trim().toLowerCase();
        if (dateType === 'recurring') {
          if (!value) {
            return 'Recurring - Start Date is required when Date Type is "Recurring".';
          }

          if (!this.isValidMMDDYYYY(value)) {
            return 'Value must be in MM/DD/YYYY format';
          }
        } else if (value && !this.isValidMMDDYYYY(value)) {
          return 'Value must be in MM/DD/YYYY format';
        }
        return true;
      },
      'Recurring - Repeats every X weeks': (value, row) => {
        const dateType = row['Date Type']?.trim().toLowerCase();
        if (dateType === 'recurring') {
          if (value === null || value === undefined || String(value).trim() === '') {
            return 'Recurring - Repeats every X weeks is required when Date Type is "Recurring".';
          }
          const trimmedValue = String(value).trim();
          if (!/^\d+$/.test(trimmedValue)) {
            return 'Value must be a number format';
          }

          if (parseInt(trimmedValue, 10) < 1) {
            return 'Recurring - Repeats every X weeks must be 1 or greater.';
          }
        } else if (value !== null && value !== undefined && String(value).trim() !== '') {
          const trimmedValue = String(value).trim();
          if (!/^\d+$/.test(trimmedValue)) {
            return 'Value must be a number format';
          }

          if (parseInt(trimmedValue, 10) < 1) {
            return 'Recurring - Repeats every X weeks must be 1 or greater.';
          }
        }
        return true;
      },
      'Recurring - for X occurrences': (value, row) => {
        const dateType = row['Date Type']?.trim().toLowerCase();
        if (dateType === 'recurring') {
          if (value === null || value === undefined || String(value).trim() === '') {
            return 'Recurring - for X occurrences is required when Date Type is "Recurring".';
          }

          const trimmedValue = String(value).trim();
          if (!/^\d+$/.test(trimmedValue)) {
            return 'Value must be a number format';
          }

          if (parseInt(trimmedValue, 10) < 1) {
            return 'Recurring - for X occurrences must be 1 or greater.';
          }
        } else if (value !== null && value !== undefined && String(value).trim() !== '') {
          const trimmedValue = String(value).trim();
          if (!/^\d+$/.test(trimmedValue)) {
            return 'Value must be a number format';
          }

          if (parseInt(trimmedValue, 10) < 1) {
            return 'Recurring - for X occurrences must be 1 or greater.';
          }
        }
        return true;
      },
      'Recurring - On': (value, row) => {
        const dateType = row['Date Type']?.trim().toLowerCase();
        return this.isValidRecurringOn(value, dateType);
      },

      ...Object.fromEntries(
          Array.from({length: 10}, (_, i) => i + 1).map((num) => [
            `Individual Dates - ${num}`,
            (value, row) => {
              const dateType = row['Date Type']?.trim().toLowerCase();
              if (dateType === 'individual dates' && num === 1 && !value) {
                return `Individual Dates - 1 is required when Date Type is "Individual Dates".`;
              }
              if (value) {
                if (!this.isValidMMDDYYYY(value)) {
                  return `Individual Dates - ${num} Value must be in MM/DD/YYYY format`;
                }
              }
              return true;
            }
          ])
      ),
      'Start Time': (value) => {
        if (!value) {
          return true;
        }
        return this.isValidTime(value) ? true : 'Value must be in HH:MM AM/PM format';
      },
      'End Time': (value, row) => {
        if (!value) {
          return true;
        }
        if (!this.isValidTime(value)) return 'End Time must be in HH:MM AM/PM format.';
        const startTimeStr = row['Start Time'];
        if (startTimeStr && this.isValidTime(startTimeStr)) {
          const startTime = moment(startTimeStr, 'h:mm A');
          const endTime = moment(value, 'h:mm A');
          if (startTime.isValid() && endTime.isValid() && endTime.isSameOrBefore(startTime)) {
            return 'End Time must be after Start Time.';
          }
        }
        return true;
      },
      'Registration Open Date': (value) => {
        if (!value) {
          return true;
        }
        
        return this.isValidMMDDYYYY(value) ? true : 'Value must be in MM/DD/YYYY format';
      },
      'Registration Close Date': (value, row) => {
        if (!value) {
          return true;
        }
        if (!this.isValidMMDDYYYY(value)) return 'Value must be in MM/DD/YYYY format';
        const openDateStr = row['Registration Open Date'];
        if (openDateStr && this.isValidMMDDYYYY(openDateStr)) {
          const openDate = moment(openDateStr, 'MM/DD/YYYY', true);
          const closeDate = moment(value, 'MM/DD/YYYY', true);
          if (openDate.isValid() && closeDate.isValid() && closeDate.isBefore(openDate)) {
            return 'Registration Close Date must be on or after Registration Open Date.';
          }
        }
        return true;
      },

      ...Object.fromEntries(
          [
            'Grades Served - Infant',
            'Grades Served - Toddler',
            'Grades Served - Preschool',
            'Grades Served - Preschool 3',
            'Grades Served - Preschool 4',
            'Grades Served - Pre-K',
            'Grades Served - Transitional Kindergarten',
            'Grades Served - K',
            'Grades Served - 1',
            'Grades Served - 2',
            'Grades Served - 3',
            'Grades Served - 4',
            'Grades Served - 5',
            'Grades Served - 6',
            'Grades Served - 7',
            'Grades Served - 8',
            'Grades Served - 9',
            'Grades Served - 10',
            'Grades Served - 11',
            'Grades Served - 12'
          ].map((fieldName) => [
            fieldName,
            (value) => {
              if (value === null || value === undefined || String(value).trim() === '') return true;
              return this.isValidBoolean(value) !== null
                  ? true
                  : `${fieldName} Value must be "true" or "false"`;
            }
          ])
      ),
      'Link to Schedule': (value, row) => {
        
        if (!value) {
          return true;
        }

        const orgName = row['Site/Organization Name'];
        const org = this.allOrgs.find((o) => o.name === orgName);
        const scheduleTypes = org?.valueOverrides?.scheduleTypes;
        
        if (!Array.isArray(scheduleTypes)) {
          return `Could not find schedule types for organization "${orgName}".`;
        }
        const isValid = scheduleTypes.some(
            (schedule) =>
                schedule &&
                typeof schedule.type === 'string' &&
                typeof value === 'string' &&
                schedule.type.trim().toLowerCase() === value.trim().toLowerCase()
        );
        return isValid ? true : `Value must an existing schedule type`;
      },
      'Restrict to Designation': (value, row) => {
        if (!value) {
          return true;
        }

        const orgName = row['Site/Organization Name'];
        const org = this.allOrgs.find((o) => o.name === orgName);
        const designations = org?.valueOverrides?.designations;
        let isValid = false;
        if (Array.isArray(designations)) {
          isValid = designations.some(
            (desig) =>
              typeof desig === 'string' &&
              typeof value === 'string' &&
              desig.trim().toLowerCase() === value.trim().toLowerCase()
          );
        } else {
          isValid = false;
        }
        return isValid ? true : `Value must match an existing designation`;
      },
      'Exempt from Registration Fee': (value) => {
        if (value === null || value === undefined || String(value).trim() === '') {
          return true;
        }
        return this.isValidBoolean(value) !== null ? true : 'Value must be "true" or "false"';
      },
      'Exempt from Sibling Discount': (value) => {
        if (value === null || value === undefined || String(value).trim() === '') {
          return true;
        }
        return this.isValidBoolean(value) !== null ? true : 'Value must be "true" or "false"';
      }
    };
  }

  /**
   * Transforms a raw CSV row into an item object structure.
   * @param {Object} row - The raw CSV row
   * @returns {Promise<Object>} The transformed item object
   */
  async transformRow(row) {
    const trim = (val) => (typeof val === 'string' ? val.trim() : val);
    const parseCurrency = (val) => {
      if (typeof val !== 'string' || !val) {
        return null;
      }

      const cleaned = val.replace(/[$,]/g, '');
      const number = parseFloat(cleaned);
      return isNaN(number) ? null : number;
    };
    const parseDate = (val) => {
      if (!val) {
        return null;
      }
      const m = moment(trim(val), 'MM/DD/YYYY', true);
      return m.isValid() ? m.valueOf() : null;
    };
    const parseBoolean = (val) => this.isValidBoolean(val);
    const toTimestamp = (val, startOfDay = true) => {
      if (!val) {
        return null;
      }
      const m = moment(trim(val), 'MM/DD/YYYY', true);
      return m.isValid() ? (startOfDay ? m.startOf('day').valueOf() : m.valueOf()) : null;
    };

    const getProgramId = () => {
      for (const org of this.allOrgs) {
        const match = org.billing?.programs?.find((programObj) => programObj.name === row['Program']);
        if (match) {
          return match._id;
        }
      }
    };

    const description = trim(row['Description']);
    const amountFlatRate = parseCurrency(row['Amount (Flat Rate)']);
    const defaultExpirationDate = toTimestamp(row['Default Expiration'], true);
    const refundableDepositAmount = parseCurrency(row['Refundable Deposit']);
    const refundableDeposit = refundableDepositAmount > 0;
    const dropInDailyRate = parseBoolean(row['Drop In Daily Rate']);
    const isExemptFromRegistrationFee = parseBoolean(row['Exempt from Registration Fee']);
    const isExemptFromSiblingDiscount = parseBoolean(row['Exempt from Sibling Discount']);
    const programDetails = BillingUtils.validateProgramDetails(trim(row['Program Details']));
    const startTimeInput = trim(row['Start Time']);
    const endTimeInput = trim(row['End Time']);
    const registrationOpenDate = toTimestamp(row['Registration Open Date'], true);
    const registrationCloseDate = toTimestamp(row['Registration Close Date'], true);
    const program = getProgramId();

    const gradesServedInput = {};
    const gradeFields = [
      'Infant',
      'Toddler',
      'Preschool',
      'Preschool 3',
      'Preschool 4',
      'Pre-K',
      'Transitional Kindergarten',
      'K',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12'
    ];
    gradeFields.forEach((grade) => {
      const fieldName = `Grades Served - ${grade}`;
      const rawValue = trim(row[fieldName]);
      if (rawValue !== '') {
        const parsedBool = parseBoolean(rawValue);
        if (parsedBool !== null) {
          const key = grade.toLowerCase().replace(/[^a-z0-9]/gi, '');
          gradesServedInput[key] = parsedBool;
        }
      }
    });

    const dateTypeRaw = trim(row['Date Type'])?.toLowerCase();
    const dbDateTypeMap = {
      'date range': 'dateRange',
      recurring: 'recurring',
      'individual dates': 'individualDates'
    };
    const resolvedDateType = dbDateTypeMap[dateTypeRaw] || dateTypeRaw;

    const details = {};
    if (startTimeInput) details.startTime = startTimeInput;
    if (endTimeInput) details.endTime = endTimeInput;
    if (registrationOpenDate) details.regStartDate = registrationOpenDate;
    if (registrationCloseDate) details.regEndDate = registrationCloseDate;
    if (Object.keys(gradesServedInput).length > 0) {
      details.grades = gradesServedInput;
    }

    if (resolvedDateType) {
      details.dateType = resolvedDateType;
    }

    if (resolvedDateType === 'dateRange') {
      details.serviceStartDate = toTimestamp(trim(row['Date Range - Start Date']));
      details.serviceEndDate = toTimestamp(trim(row['Date Range - End Date']));
    } else if (resolvedDateType === 'recurring') {
      const recurringStartDateInput = trim(row['Recurring - Start Date']);
      const recurringRepeatsRaw = trim(row['Recurring - Repeats every X weeks']);
      const recurringOccurrencesRaw = trim(row['Recurring - for X occurrences']);
      const recurringDaysRaw = trim(row['Recurring - On']);
      const recurringFrequency = /^\d+$/.test(recurringRepeatsRaw) ? parseInt(recurringRepeatsRaw, 10) : null;
      const recurringOccurrences = /^\d+$/.test(recurringOccurrencesRaw) ? parseInt(recurringOccurrencesRaw, 10) : null;
      const recurringDaysArray = recurringDaysRaw
        ? recurringDaysRaw
            .split(',')
            .map((d) => d.trim())
            .filter((d) => d)
        : [];

      const isRecurringDataValid =
        this.isValidMMDDYYYY(recurringStartDateInput) &&
        recurringFrequency &&
        recurringOccurrences &&
        recurringDaysArray.length > 0;

      if (isRecurringDataValid) {
        details.recurringStartDate = toTimestamp(recurringStartDateInput);
        details.recurringFrequency = recurringFrequency;
        details.recurringOccurrences = recurringOccurrences;
        details.recurringDays = recurringDaysArray;
      }
    } else if (resolvedDateType === 'individualDates') {
      const individualDatesInput = Array.from({ length: 10 }, (_, i) => i + 1)
        .map((num) => trim(row[`Individual Dates - ${num}`]))
        .filter((d) => !!d);
      details.individualDates = individualDatesInput.map((d) => toTimestamp(d)).filter((ts) => ts !== null);
    }

    const orgName = trim(row['Site/Organization Name']);
    const orgId = this.orgNameAndId.get(orgName);
    if (!orgId) {
      return `Organization "${orgName}" not found. Please verify the organization name.`;
    }
    
    const org = this.allOrgs.find((org) => org.name === orgName);
    const orgHasRegistrationFlow = org?.customizations && org.customizations[AvailableCustomizations.REGISTRATION_FLOW];
    
    const transformed = {
      orgId,
      type: ITEM_TYPE,
      description,
      amount: amountFlatRate,
      program: program,
      refundableDeposit,
      dropInDailyRate,
      expires: defaultExpirationDate,
      programDetails: programDetails ?? undefined,
    };

    if (Object.keys(details).length > 0) {
        transformed.details = details;
    }

    if (orgHasRegistrationFlow) {
      transformed.siblingDiscountExempt = isExemptFromSiblingDiscount;
      transformed.regFeeExempt = isExemptFromRegistrationFee;
    }

    return transformed;
  }

  /**
   * REVISED: Inserts a new item record by pushing to Orgs.billing.plansAndItems.
   * @param {Object} itemData - The transformed item data from transformRow
   * @returns {Promise<void>}
   */
  async insertRow(itemData) {
    const { orgId, ...dataToInsert } = itemData;
    await Orgs.updateAsync({ _id: orgId }, { $push: { 'billing.plansAndItems': dataToInsert } });
  }
}