import { BaseImporter } from './BaseImporter';
import { People } from '../../lib/collections/people';
import { Orgs } from '../../lib/collections/orgs';
import { Log } from '../../lib/util/log';
import { HistoryAuditService } from '../../server/historyAuditService';
import { HistoryAuditChangeTypes, HistoryAuditPeoplePerformedByNames } from '../../lib/constants/historyAuditConstants';
import { Random } from 'meteor/random';
import { UserUtils } from '../../lib/util/usersUtil';
import { Groups } from '../../lib/collections/groups';
import { PeopleTypes } from '../../lib/constants/peopleConstants';
import { STAFF_ADMIN_IMPORTER_FIELDS } from '../../lib/constants/importers/importerConstants';

/**
 * Handles importing staff and admin users from CSV files.
 * Validates and creates new staff/admin records in the People collection.
 */
export class StaffAndAdminImporter extends BaseImporter {
    /**
     * Constructs the StaffAndAdminImporter instance.
     * Requires `allOrgs` to be passed as part of the options object.
     *
     * @param {Object} options - Importer configuration options
     * @param {Object} options.allOrgs - Array of all organizations in the hierarchy
     */
    constructor(options) {
        super(options);
        this.allOrgs = options.allOrgs;
        // Map to store existing and newly imported people by email and type for uniqueness checks
        this.peopleMap = new Map();
        this.emailMap = new Map();
        // Flag to track if we've loaded existing people
        this.existingPeopleLoaded = false;
        // Keep track of imported people during this import session
        this.importedPeople = new Set();
        // Map to store org data by name for quick lookups
        this.orgsByName = new Map();
        this.orgNames = new Set(this.allOrgs?.map(org => org.name) || []);
        // Map to store groups by org ID and name for quick lookups
        this.groupsByOrgId = new Map();
        
        // Populate the orgsByName map
        if (this.allOrgs && Array.isArray(this.allOrgs)) {
            this.allOrgs.forEach(org => {
                if (org.name) {
                    this.orgsByName.set(org.name, org);
                    this.orgNames.add(org.name);
                }
            });
        }
    }

    /**
     * Returns the list of required fields for the import, per new requirements.
     * @returns {string[]} Array of required field names
     */
    get requiredFields() {
        return [
            STAFF_ADMIN_IMPORTER_FIELDS.SITE_ORG_NAME,
            STAFF_ADMIN_IMPORTER_FIELDS.FIRST_NAME,
            STAFF_ADMIN_IMPORTER_FIELDS.LAST_NAME,
            STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE,
            STAFF_ADMIN_IMPORTER_FIELDS.EMAIL
        ];
    }

    /**
     * Prepares the importer by loading necessary data into memory
     * @returns {Promise<void>}
     */
    async prepare() {
        if (this.prepared) return;
        
        try {
            // Load all groups for the orgs
            for (const org of this.allOrgs) {
                const groups = await Groups.find({ orgId: org._id }).fetchAsync();
                const groupMap = new Map();
                groups.forEach(group => {
                    groupMap.set(group.name, group);
                });
                this.groupsByOrgId.set(org._id, groupMap);
            }
            
            this.prepared = true;
        } catch (error) {
            Log.error('Error preparing importer:', error);
            throw error;
        }
    }

    /**
     * Get an org by name from context instead of querying the database
     * @param {string} name - The name of the org to find
     * @returns {Object|null} The org object or null if not found
     */
    getOrgByName(name) {
        if (!name) return null;
        const trimmedName = name.trim();

        if (!this.orgNames.has(trimmedName)) {
            return null;
        }

        const org = this.orgsByName.get(trimmedName) || null;
        return org;
    }

    /**
     * Get a group by org ID and name from context instead of querying the database
     * @param {string} orgId - The ID of the org
     * @param {string} name - The name of the group to find
     * @returns {Object|null} The group object or null if not found
     */
    getGroupByOrgIdAndName(orgId, name) {
        if (!orgId || !name) return null;
        const groupMap = this.groupsByOrgId.get(orgId);
        if (!groupMap) return null;
        return groupMap.get(name.trim()) || null;
    }

    /**
     * Returns field-level validation rules.
     * @returns {Object} Object mapping field names to validation functions
     */
    get fieldValidators() {
        const baseValidators = {
            [STAFF_ADMIN_IMPORTER_FIELDS.SITE_ORG_NAME]: (value, row, index) => {
                if (!value || !value.trim()) {
                    return `Missing "Site/ Org Name"`;
                }
                const org = this.getOrgByName(value);
                if (!org) {
                    return `Site/Organization Name must exist within the hierarchy`;
                }
                return true;
            },
            [STAFF_ADMIN_IMPORTER_FIELDS.FIRST_NAME]: (value) => {
                if (!value || !value.trim()) {
                    return 'Please enter a first name';
                }
                return true;
            },
            [STAFF_ADMIN_IMPORTER_FIELDS.LAST_NAME]: (value) => {
                if (!value || !value.trim()) {
                    return 'Please enter a last name';
                }
                return true;
            },
            [STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE]: (value) => {
                if (!value || ![PeopleTypes.STAFF, PeopleTypes.ADMIN].includes(value.trim().toLowerCase())) {
                    return `Please set user type to either staff or admin`;
                }
                return true;
            },
            [STAFF_ADMIN_IMPORTER_FIELDS.ROLE]: (value, row) => {
                if (!value || !value.trim()) {
                    return true; // Optional
                }
                const org = this.getOrgByName(row[STAFF_ADMIN_IMPORTER_FIELDS.SITE_ORG_NAME]);
                if (!org) {
                    return true;
                }
                
                const availableRoles = org.valueOverrides?.availablePermissionsRoles || [];
                
                if (availableRoles.includes(value.trim())) {
                return true;
                }
                
                return 'Please enter an existing role value';
            },
            [STAFF_ADMIN_IMPORTER_FIELDS.TOP_MOST_ORG]: (value) => {
                if (!value || !value.trim()) return true; // Optional
                const org = this.getOrgByName(value);
                if (!org) {
                    return 'Please enter existing Org name';
                }
                return true;
            },
            [STAFF_ADMIN_IMPORTER_FIELDS.EMAIL]: (value) => {
                if (!value || !value.trim()) {
                    return 'Please enter an Email address';
                }
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.trim())) {
                    return 'Must have a valid email set';
                }
                return true;
            },
            [STAFF_ADMIN_IMPORTER_FIELDS.DEFAULT_GROUP]: (value, row) => {
                if (!value || !value.trim()) return true; // Optional
                if (row[STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE] && 
                    row[STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE].toLowerCase() === PeopleTypes.ADMIN) {
                    if (value && value.trim()) {
                        return 'Entry must match an existing group name'; // Should be blank for admins
                    }
                    return true;
                }
                const org = this.getOrgByName(row[STAFF_ADMIN_IMPORTER_FIELDS.SITE_ORG_NAME]);
                if (!org) return true; // Org error handled elsewhere
                const group = this.getGroupByOrgIdAndName(org._id, value);
                if (!group) {
                    return 'Entry must match an existing group name';
                }
                return true;
            },
            [STAFF_ADMIN_IMPORTER_FIELDS.PRIMARY_PHONE]: (value) => {
                if (!value || !value.trim()) return true; // Optional
                const digitPattern = /^\d{10}$/;
                if (!digitPattern.test(value)) {
                    return 'Entry must be a correctly formatted phone number';
                }
                return true;
            }
        };

        // Dynamically add validators for custom profile fields
        const customValidators = {};
        if (this.data && this.data.length > 0) {
            const row = this.data[0];
            const customFieldNames = Object.keys(row).filter(
                (key) => key.startsWith('staffProfile.')
            );
            for (const fieldName of customFieldNames) {
                customValidators[fieldName] = (value, row, index) => {
                    const userType = (row[STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE] || '').toLowerCase();
                    const org = this.getOrgByName(row[STAFF_ADMIN_IMPORTER_FIELDS.SITE_ORG_NAME]);
                    if (!org) return true; 
                    return true;
                };
            }
        }
        return { ...baseValidators, ...customValidators };
    }

    /**
     * Validates a custom profile field against the organization's defined fields
     * @param {string} fieldName - The profile field name (without the prefix)
     * @param {string} value - The value to validate
     * @param {Object} org - The organization object
     * @param {string} userType - Either 'staff' or 'admin'
     * @returns {boolean|string} True if valid, error message if not
     */
    validateCustomProfileField(fieldName, value, org, userType) {
        if (!value || value.trim() === '') {
            return true;
        }
        
        const profileFields = org?.valueOverrides?.staffProfileFields || [];
        
        const profileField = profileFields.find(field => field.name === fieldName);
        
        if (!profileField) {
            return `"${fieldName}" cannot be found for this organization`;
        }
        
        if (profileField.type === 'select' && profileField.values && Array.isArray(profileField.values)) {
            const validValues = profileField.values.map(val => String(val).trim());
            if (!validValues.includes(value.trim())) {
                return `"${fieldName}" value is not valid`;
            }
        }
        
        return true;
    }

    /**
     * Override the validateRow method to properly handle async validators and custom profile fields
     * @param {Object} row - The raw row to validate
     * @param {number} index - The row index in the file
     * @returns {Promise<boolean>} Whether the row passed all validation
     */
    validateRow(row, index) {
        const missingFields = this.requiredFields.filter(field => !(field in row && row[field].trim() !== ''));
        if (missingFields.length > 0) {
            this.errors.push(`Row ${index + 1}: Missing required fields: ${missingFields.join(', ')}`);
            return false;
        }

        const validators = this.fieldValidators;
        let hasErrors = false;

        for (const [field, validateFn] of Object.entries(validators)) {
            const value = row[field];
            try {
                const result = validateFn(value, row, index);
                if (result !== true) {
                    this.errors.push(`Row ${index + 1}: ${field} - ${result}`);
                    hasErrors = true;
                }
            } catch (e) {
                this.errors.push(`Row ${index + 1}: ${field} - ${e.message || 'Validation error'}`);
                hasErrors = true;
            }
        }

        const org = this.getOrgByName(row[STAFF_ADMIN_IMPORTER_FIELDS.SITE_ORG_NAME]);
        if (org) {
            const userType = row[STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE]?.toLowerCase();
            
            const customPrefixes = ['staffProfile.'];
            
            Object.keys(row).forEach(columnName => {
                let prefix = null;
                let fieldName = null;
                
                for (const p of customPrefixes) {
                    if (columnName.startsWith(p)) {
                        prefix = p;
                        fieldName = columnName.substring(p.length);
                        break;
                    }
                }
                
                if (prefix && fieldName) {
                    const result = this.validateCustomProfileField(fieldName, row[columnName], org, userType);
                    
                    if (result !== true) {
                        this.errors.push(`Row ${index + 1}: ${columnName} - ${result}`);
                        hasErrors = true;
                    }
                }
            });
        }

        return !hasErrors;
    }

    /**
     * Transforms a CSV row into a Person object.
     * @param {Object} row - CSV row data
     * @returns {Promise<Object>} Transformed person object
     */
    async transformRow(row) {
        try {
            const org = this.getOrgByName(row[STAFF_ADMIN_IMPORTER_FIELDS.SITE_ORG_NAME]);
            if (!org) {
                throw new Error(`Organization not found for site name: ${row[STAFF_ADMIN_IMPORTER_FIELDS.SITE_ORG_NAME]}`);
            }

            const personObject = {
                type: row[STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE].toLowerCase(),
                orgId: org._id,
                firstName: row[STAFF_ADMIN_IMPORTER_FIELDS.FIRST_NAME],
                lastName: row[STAFF_ADMIN_IMPORTER_FIELDS.LAST_NAME],
                profileEmailAddress: row[STAFF_ADMIN_IMPORTER_FIELDS.EMAIL].toLowerCase().trim(),
                inActive: false,
                createdBy: 'Import',
                createdAt: new Date().valueOf()
            };

            if (row[STAFF_ADMIN_IMPORTER_FIELDS.ROLE] && row[STAFF_ADMIN_IMPORTER_FIELDS.ROLE].trim()) {
                personObject.roles = [row[STAFF_ADMIN_IMPORTER_FIELDS.ROLE].trim()];
            }

            if (row[STAFF_ADMIN_IMPORTER_FIELDS.PRIMARY_PHONE]) {
                personObject.phonePrimary = row[STAFF_ADMIN_IMPORTER_FIELDS.PRIMARY_PHONE];
            }

            if (!personObject.profileData) {
                personObject.profileData = {};
            }

            if (row[STAFF_ADMIN_IMPORTER_FIELDS.EMPLOYEE_STREET]) {
                personObject.profileData.staffAddress = row[STAFF_ADMIN_IMPORTER_FIELDS.EMPLOYEE_STREET];
            }
            
            if (row[STAFF_ADMIN_IMPORTER_FIELDS.EMPLOYEE_CITY]) {
                personObject.profileData.staffAddressCity = row[STAFF_ADMIN_IMPORTER_FIELDS.EMPLOYEE_CITY];
            }
            
            if (row[STAFF_ADMIN_IMPORTER_FIELDS.EMPLOYEE_STATE]) {
                personObject.profileData.staffAddressState = row[STAFF_ADMIN_IMPORTER_FIELDS.EMPLOYEE_STATE];
            }
            
            if (row[STAFF_ADMIN_IMPORTER_FIELDS.EMPLOYEE_ZIP]) {
                personObject.profileData.staffAddressZip = row[STAFF_ADMIN_IMPORTER_FIELDS.EMPLOYEE_ZIP];
            }

            if (row[STAFF_ADMIN_IMPORTER_FIELDS.PRIMARY_PHONE]) {
                personObject.profileData.phonePrimary = row[STAFF_ADMIN_IMPORTER_FIELDS.PRIMARY_PHONE];
            }

            // Add defaultGroupId for staff users if Default Group is provided
            if (
              row[STAFF_ADMIN_IMPORTER_FIELDS.DEFAULT_GROUP] &&
              row[STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE] &&
              row[STAFF_ADMIN_IMPORTER_FIELDS.USER_TYPE].toLowerCase() === PeopleTypes.STAFF
            ) {
              const group = this.getGroupByOrgIdAndName(
                org._id,
                row[STAFF_ADMIN_IMPORTER_FIELDS.DEFAULT_GROUP]
              );
              if (group) {
                personObject.defaultGroupId = group._id;
              }
            }

            if (row[STAFF_ADMIN_IMPORTER_FIELDS.TOP_MOST_ORG]) {
              const topmostOrg = this.getOrgByName(row[STAFF_ADMIN_IMPORTER_FIELDS.TOP_MOST_ORG]);
              if (topmostOrg) {
                personObject.topmostOrgId = topmostOrg._id;
              }
            }

            const userType = personObject.type;
            const prefixToCheck = 'staffProfile.';
            
            Object.keys(row).forEach(columnName => {
                if (columnName.startsWith(prefixToCheck)) {
                    const fieldName = columnName.substring(prefixToCheck.length);
                    const value = row[columnName];
                    
                    if (value && value.trim() !== '') {
                        personObject.profileData[fieldName] = value.trim();
                    }
                }
            });
            
            return personObject;
        } catch (error) {
            Log.error('Error transforming row:', error);
            throw error;
        }
    }

    /**
     * Load all existing staff and admin users into the peopleMap
     * @returns {Promise<void>}
     */
    async loadExistingPeople() {
        if (this.existingPeopleLoaded) return;
        
        try {
            const orgIds = this.allOrgs.map(org => org._id);
            
            const existingPeople = await People.find({
                type: { $in: [PeopleTypes.STAFF, PeopleTypes.ADMIN] },
                orgId: { $in: orgIds }
            }).fetchAsync();

            existingPeople.forEach(person => {
                if (person.profileEmailAddress) {
                    const email = person.profileEmailAddress.toLowerCase();
                    const key = `${email}_${person.type}`;
                    this.peopleMap.set(key, true);
                    
                    this.emailMap.set(email, true);
                }
            });

            this.existingPeopleLoaded = true;
        } catch (error) {
            Log.error('Error loading existing people:', error);
            throw error;
        }
    }

    /**
     * Checks if a staff/admin with the same email already exists in the People collection 
     * using the preloaded peopleMap instead of querying the database each time.
     * This now checks for email uniqueness regardless of user type.
     * 
     * @param {Object} person - The transformed person object
     * @returns {Promise<boolean>} True if the email is unique
     */
    async isUnique(person) {
        // Load existing people if we haven't already
        if (!this.existingPeopleLoaded) {
            await this.loadExistingPeople();
        }

        const email = person.profileEmailAddress.toLowerCase();
        
        if (this.emailMap.has(email)) {
            return false;
        }
        
        return true;
    }

    /**
     * Override the run method to ensure we load existing people at the start
     * @returns {Promise<string[]>} Array of error messages encountered during import
     */
    async run() {
        // Prepare the importer (load orgs, groups, etc.)
        await this.prepare();
        
        // Load existing people at the start of the import
        await this.loadExistingPeople();
        
        return await super.run();
    }

    /**
     * Inserts a transformed row into the People collection.
     * Also creates a user account and logs the change in history audit.
     * @param {Object} object - Transformed person object
     * @returns {Promise<string>} Inserted person ID
     */
    async insertRow(object) {
        try {
            const email = object.profileEmailAddress.toLowerCase();
            
            if (this.emailMap.has(email)) {
                throw new Error(`Duplicate email address: ${object.profileEmailAddress} already exists.`);
            }
            
            const key = `${email}_${object.type}`;
            if (this.peopleMap.has(key)) {
                throw new Error(`Duplicate email address: ${object.profileEmailAddress} already exists for ${object.type} user.`);
            }
            
            this.peopleMap.set(key, true);
            this.emailMap.set(email, true);
            
            const personId = await People.insertAsync(object);

            this.importedPeople.add(key);

            await HistoryAuditService.logPersonChange({
                changeType: HistoryAuditChangeTypes.ADD,
                performedByName: HistoryAuditPeoplePerformedByNames.SYSTEM_IMPORT,
                previousState: null,
                currentState: { ...object, _id: personId }
            });

            const org = this.allOrgs.find(o => o._id === object.orgId);
            
            try {
            const result = await UserUtils.createAutoAccount({
                personData: {
                    emailAddress: object.profileEmailAddress,
                    firstName: object.firstName,
                    lastName: object.lastName
                },
                newPersonId: personId,
                currentOrg: org
            });

            if (!result.success) {
                Log.error(`Failed to create user account for ${object.profileEmailAddress}:`, result.error);
                this.errors.push(`Failed to create user account for ${object.profileEmailAddress}: ${result.error}`);
                }
            } catch (accountError) {
                Log.error(`Exception creating user account for ${object.profileEmailAddress}:`, accountError);
                this.errors.push(`Error creating user account for ${object.profileEmailAddress}: ${accountError.message}`);
            }

            return personId;
        } catch (error) {
            if (error.message.includes('duplicate') || error.message.includes('Duplicate')) {
                this.errors.push(`Row error: ${object.profileEmailAddress} already exists in the system.`);
            } else {
            Log.error('Error inserting row:', error);
                this.errors.push(`Error inserting ${object.firstName} ${object.lastName}: ${error.message}`);
            }
            throw error;
        }
    }
} 