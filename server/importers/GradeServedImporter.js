import { Random } from 'meteor/random';
import { BaseImporter } from './BaseImporter';
import { Orgs } from '../../lib/collections/orgs';
import { Log } from '../../lib/util/log';

/**
 * GradesServedImporter handles importing "Grades Served" records from a CSV file.
 * Ensures valid organization references and enforces data requirements.
 *
 * @extends BaseImporter
 */
export class GradesServedImporter extends BaseImporter {
    constructor(options) {
        super(options);
    }

    get requiredFields() {
        return [
            'Site/Organization Name',
            'Grades Served - Infant',
            'Grades Served - Toddler',
            'Grades Served - Preschool',
            'Grades Served - Preschool 3',
            'Grades Served - Preschool 4',
            'Grades Served - Pre-K',
            'Grades Served - Transitional Kindergarten',
            'Grades Served - K',
            'Grades Served - 1',
            'Grades Served - 2',
            'Grades Served - 3',
            'Grades Served - 4',
            'Grades Served - 5',
            'Grades Served - 6',
            'Grades Served - 7',
            'Grades Served - 8',
            'Grades Served - 9',
            'Grades Served - 10',
            'Grades Served - 11',
            'Grades Served - 12',
        ];
    }

    get fieldValidators() {
        const booleanValidator = (value) => {
            return ['true', 'false'].includes((value || '').trim().toLowerCase()) || 'Entry must be “true” or “false”.';
        };        

        const validators = {
            'Site/Organization Name': (value) => {
                const orgExists = this.allOrgs?.find(org => org.name === (value || '').trim());
                return orgExists ? true : 'Site/Organization Name must exist within the hierarchy.';
            }
        };

        this.requiredFields.forEach(field => {
            if (field !== 'Site/Organization Name') {
                validators[field] = booleanValidator;
            }
        });

        return validators;
    }

    async transformRow(row) {
        const siteName = row['Site/Organization Name'];
        const gradeFields = Object.entries(row)
            .filter(([key, value]) => key.startsWith('Grades Served - ') && (value || '').trim().toLowerCase() === 'true')
            .map(([key]) => key.replace('Grades Served - ', ''));
    
        const overrideValue = {
            name: 'studentGrade',
            description: 'Student Grades',
            type: 'select',
            values: gradeFields
        };
    
        return {
            siteName,
            overrideValue
        };
    }

    async insertRow(data) {
        const { siteName, overrideValue } = data;

        const matchedOrg = this.allOrgs.find(org => org.name === siteName);
        if (!matchedOrg) {
            const errorMessage = `Could not find matching organization for '${siteName}'`;
            Log.error(errorMessage);
            this.errors.push(errorMessage);
            return;
        }
        const existingField = matchedOrg.valueOverrides?.profileFields?.find(
            f => f.name === 'studentGrade'
        );

        if (existingField) {
            await Orgs.updateAsync(
                { _id: matchedOrg._id },
                {
                    $set: {
                        'valueOverrides.profileFields.$[elem].values': overrideValue.values
                    }
                },
                {
                    arrayFilters: [{ 'elem.name': 'studentGrade' }]
                }
            );
        } else {
            await Orgs.updateAsync(
                { _id: matchedOrg._id },
                {
                    $push: {
                        'valueOverrides.profileFields': overrideValue
                    }
                }
            );
        }
    }
}
