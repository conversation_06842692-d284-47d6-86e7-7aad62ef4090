const { MongoClient } = require('mongodb');
const userIndexes = require('./users.json');

const uri = 'mongodb://localhost:27017';
const dbName = 'db_name';

async function createIndexes() {
    const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });

    try {
        await client.connect();
        const db = client.db(dbName);

        await db.collection('users').createIndexes(userIndexes);

        console.log('indexes created successfully');
    } catch (error) {
        console.error('error creating indexes:', error);
    } finally {
        await client.close();
    }
}
