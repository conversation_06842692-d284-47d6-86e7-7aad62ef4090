import moment from 'moment-timezone';
import { SSR } from "../lib/util/ssrUtils";
import _ from '../lib/util/underscore';

export const processPasswordResetMail = async function (emailAddress, resetToken, currentOrg) {
	if (Meteor.isServer) {
		SSR.compileTemplate('passwordResetEmail', await Assets.getTextAsync('email_templates/v2021/reset_password_email.html'));

		var baseUrl = (currentOrg && currentOrg.whiteLabel && currentOrg.whiteLabel.ROOT_URL) || process.env.ROOT_URL;
		if (baseUrl.slice(baseUrl.length-1) != "/") baseUrl+= "/";

		const whiteLabelLogo = _.deep(currentOrg, "whiteLabel.assets.emailLogo") || "http://assets.momentpath.com/IMG_1047.png",
		whiteLabelPrimaryColor = _.deep(currentOrg, "whiteLabel.primaryColor") || "#AC52DB",
		whiteLabelSecondaryColor = _.deep(currentOrg, "whiteLabel.secondaryColor") || "#6EC3CC",
		whiteLabelAndroidAppUrl = _.deep(currentOrg, "whiteLabel.androidAppUrl") || "https://play.google.com/store/apps/details?id=ly.tend.tendly.tendlyapp",
		whiteLableiosAppUrl = _.deep(currentOrg, "whiteLabel.iosAppUrl") || "https://apps.apple.com/us/app/momentpath/id1086197417",
		whileLabelEmailAssetPrefix = _.deep(currentOrg, "whiteLabel.emailAssetPrefix") || "v2021";

		var emailData = {
			passwordResetToken: resetToken,
			passwordResetUrl: baseUrl + "forgotpassword?code=" + resetToken,
			whiteLabel: {
				orgName: currentOrg.name,
				longName: currentOrg.getLongName(),
				primaryColor: whiteLabelPrimaryColor,
				secondaryColor: whiteLabelSecondaryColor,
				logo: whiteLabelLogo,
				androidAppUrl: whiteLabelAndroidAppUrl,
				iosAppUrl: whiteLableiosAppUrl,
			},
			backgroundColor: `${whiteLabelPrimaryColor}1A`,
			headerOrgNameColor: "#8E8E93",
			headerBgColor: whiteLabelPrimaryColor,
			secondaryColor: whiteLabelSecondaryColor,
			assetPrefix: `emailAssets/${whileLabelEmailAssetPrefix}`,
			currentYear: new moment().format("YYYY"),
		};


		const emailOptions = {
			from: "LineLeader Support <<EMAIL>>",
			to: emailAddress,
			subject: "Password reset request",
			html: SSR.render('passwordResetEmail', emailData)
		};
		if (currentOrg.fromAddress) emailOptions.from = currentOrg.fromAddress;

		await Email.sendAsync(emailOptions);
	}
};
