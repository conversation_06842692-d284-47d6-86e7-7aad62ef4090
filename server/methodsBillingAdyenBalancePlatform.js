import {AdyenBalancePlatformProvider} from './balancePlatformProvider/adyenBalancePlatformProvider';
import {AdyenBalancePlatformOnboardingDataService} from "./adyenBalancePlatformOnboardingDataService";
import {AdyenBalancePlatformOnboardingService} from "./adyenBalancePlatformOnboardingService";
import {BillingAdyenBalancePlatformUtils} from "../lib/util/billingAdyenBalancePlatformUtils";
import {Log} from "../lib/util/log";
import {Orgs} from '../lib/collections/orgs';

Meteor.methods({
	async startBillingAdyenBalancePlatformOnboarding(inputData) {
		console.log('startBillingAdyenBalancePlatformOnboarding', inputData);
		const currentUser = await Meteor.userAsync();
		const currentUserOrgId = currentUser.orgId;

		const currentPerson = currentUser && await currentUser.fetchPerson();
		if (!currentUser || currentPerson.type !== "admin") {
			throw new Meteor.Error(403, "Access denied");
		}

		const billingAdyenInfo = await AdyenBalancePlatformOnboardingDataService.getAdyenBillingData(currentUserOrgId);
		const isAdyenClassicPlatformAccount = billingAdyenInfo?.adyenInfo?.accountCode && billingAdyenInfo?.adyenInfo?.accountHolderId;
		const errors = BillingAdyenBalancePlatformUtils.validateStartBillingAdyenBalancePlatformOnboardingInputData(inputData, isAdyenClassicPlatformAccount);
		if (Object.keys(errors).length > 0) {
			throw new Meteor.Error('invalid-data', 'Invalid data', errors);
		}

		const isOnboardingStarted = !!billingAdyenInfo.adyenBalancePlatformOnboarding?.status;
		if (isOnboardingStarted) {
			throw new Meteor.Error('invalid-state', 'Onboarding already started');
		}

		try {
			const onboardingData = await AdyenBalancePlatformOnboardingService.startBalancePlatformOnboarding(
				currentUserOrgId,
				inputData,
				billingAdyenInfo.adyenBalancePlatformOnboarding,
				currentUser._id,
				billingAdyenInfo.adyenInfo
			);

			return {
				destinationUrl: onboardingData.onboardingLink
			}
		} catch (error) {
			if (error.message === "INVALID_WEB_ADDRESS") {
				Log.error("Invalid web address provided", error);
				throw new Meteor.Error('invalid-web-address', 'Invalid web address');
			}

			if (error.message === "CONTACT_SUPPORT_FOR_MIGRATION") {
				throw new Meteor.Error('contact-support-for-migration', 'Please contact the support team for migration to the Balance Platform');
			}

			if (error.invalidFields?.length > 0) {
				throw new Meteor.Error('invalid-data', 'Invalid data', error.invalidFields);
			}

			Log.error("Failed to start onboarding", error);
			throw new Meteor.Error('Error', 'Failed to start onboarding');
		}
	},

	async resumeBillingAdyenBalancePlatformOnboarding(options) {
		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();
		const { adyenBalancePlatformOnboarding: onboardingData } = await AdyenBalancePlatformOnboardingDataService.getAdyenBillingData(
		  currentOrg._id
		);

		const isOnboardingStarted = !!onboardingData?.status
		if (!isOnboardingStarted) {
			throw new Meteor.Error('invalid-state', 'Onboarding not started');
		}

		const legalEntityId = onboardingData.legalEntityId;
		const onboardingLink = await AdyenBalancePlatformOnboardingService.generateHopUrl(currentOrg._id, legalEntityId);

		await AdyenBalancePlatformOnboardingDataService.updateInitiatedByUserId(currentOrg._id, currentUser._id);

		if (onboardingData?.status === 'PendingKYC') {
			await AdyenBalancePlatformOnboardingService.triggerCompleteAdyenBalancePlatformOnboardingChecks(currentOrg._id);
		}

		return {
			destinationUrl: onboardingLink
		}
	},

	async getBillingAdyenBalancePlatformOnboardingStatus() {
		const currentOrg = await Orgs.current();
		const { adyenBalancePlatformOnboarding, adyenInfo, billing } =
      		await AdyenBalancePlatformOnboardingDataService.getAdyenBillingData(currentOrg._id);

		const isOnboardingStarted = !!adyenBalancePlatformOnboarding?.status
		const isAdyenClassicPlatformAccount = !!adyenInfo?.accountCode;
		const initialFormData = {
			phone: billing.phone,
			webAddress: billing.webAddress,
		};

		if (!isOnboardingStarted) {
			return {
				status: 'not-started',
				isAdyenClassicPlatformAccount,
				initialFormData,
			}
		}

		const accountHolder = await AdyenBalancePlatformProvider.getAccountHolder(adyenBalancePlatformOnboarding.accountHolderId);
		const capabilities = Object.values(accountHolder.capabilities);
		const hasPendingCapabilities = capabilities.some(capability => capability.verificationStatus === 'pending');
		const hasInvalidCapabilities = capabilities.some(capability => capability.verificationStatus === 'invalid' || capability.verificationStatus === 'rejected');
		const allCapabilitiesValid = capabilities.every(capability => capability.verificationStatus === 'valid');
		const allCapabilitiesEnabled = capabilities.every(capability => capability.enabled);
		const hasOnlyMissingInformationVerificationErrors = getHasOnlyMissingInformationVerificationErrors(capabilities);
		const isOnboardingComplete = currentOrg.billing.adyenBalancePlatformOnboarding.status === 'OnboardingComplete';

		let capabilitiesStatus = 'pending';
		if (allCapabilitiesValid && allCapabilitiesEnabled && isOnboardingComplete) {
			capabilitiesStatus = 'valid';
		} else if (hasInvalidCapabilities) {
			capabilitiesStatus = 'invalid';
		} else if (hasPendingCapabilities || !isOnboardingComplete) {
			capabilitiesStatus = 'pending';
		} else if (!allCapabilitiesEnabled) {
			capabilitiesStatus = 'disabled';
		}

		return {
			status: accountHolder.status,
			statusDescription: mapAccountHolderStatusToDescription(accountHolder.status),
			isAdyenClassicPlatformAccount,
			capabilitiesStatus,
			capabilitiesStatusDescription: mapCapabilityStatusToDescription(capabilitiesStatus, hasOnlyMissingInformationVerificationErrors, isOnboardingComplete),
			initialFormData,
		}
	}
});

const mapCapabilityStatusToDescription = (status, hasOnlyMissingInformationVerificationErrors, isOnboardingComplete) => {
	switch (status) {
		case 'pending':
			return 'Pending';
		case 'valid':
			return 'Verified';
		case 'invalid': {
			const missingInformationDescription = isOnboardingComplete ? 'Some information is missing' : 'Some information is missing or onboarding is not complete';

			return hasOnlyMissingInformationVerificationErrors ? missingInformationDescription : 'Verification failed';
		}
		case 'disabled':
			return 'Some capabilities are disabled';
		default:
			return 'Unknown';
	}
}

const mapAccountHolderStatusToDescription = (status) => {
	switch (status) {
		case 'active':
			return 'Active';
		case 'suspended':
			return 'Suspended';
		case 'closed':
			return 'Closed';
		default:
			return 'Unknown';
	}
}

const MISSING_INFORMATION_VERIFICATION_ERROR_PREFIX = '2_';
const getHasOnlyMissingInformationVerificationErrors = (capabilitiesList) => {
	const allVerificationErrors = [...capabilitiesList.reduce((acc, capability) => {
		const verificationErrors = capability.problems?.reduce((acc, problem) => {
			return [...acc, ...problem.verificationErrors ?? []];
		}, []) ?? [];

		return [...acc, ...verificationErrors];
	}, [])];

	return allVerificationErrors.every(error => error.code.startsWith(MISSING_INFORMATION_VERIFICATION_ERROR_PREFIX));
}
