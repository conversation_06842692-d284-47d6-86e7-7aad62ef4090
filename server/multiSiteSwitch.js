import { Redshift } from "../api/v2/services/redshiftDbService";

Meteor.methods({
  /**
   *
   * @param {String} index
   * @param {String} loggedInOrg
   * @returns {Array<Object>}
   */
  async getAllOrgnizationData(index) {
    let data = await Redshift.executeQuery(
      `SELECT _id, "orgId", name, "parentOrgName", "orgLevelNames" FROM manage.${index};`
    );
    return data;
  },

  /**
   * Update the hierarchy into the redshift database based on logged in orgnization.
   * @param { String } data 
   * @param { String } loggedInOrg 
   */
  async updateDataRedshift(data,loggedInOrg) {
    await Redshift.executeQuery(
     `UPDATE manage.orgs SET hierarchy = '${data}' WHERE _id = '${loggedInOrg}';`
    );
  }
});
