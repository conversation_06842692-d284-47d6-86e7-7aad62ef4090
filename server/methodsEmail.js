import { EmailService } from "./emails/emailService";
import { UserInvitations } from '../lib/collections/userInvitations';
import { processInvitation } from './processInvitation';

Meteor.methods({
	async sendSystemAdminEmail(options){
    this.unblock();
    await EmailService.sendSystemAdminEmail(
        undefined,
        'systemNotificationEmail',
        'email_templates/system_notification_email.html',
        '<<EMAIL>>',
        options.toEmail,
        options.sub,
        { bodyMessage: options.bodyMessage }
      );
		return true
	},
  /**
   * Resend invitation email to a user who is pending but has an open invite
   *
   * @param email
   * @param orgId
   */
  async resendInvitationForPendingUser({email, orgId}) {
    this.unblock();
    // Send new registration email
    const escapedEmail = email.replace(/[.+*?^${}()|[\]\\]/g, '\\$&').trim()
    let invite = await UserInvitations.findOneAsync({$and: [{email: { $regex: `^${ escapedEmail }$`, $options: 'i' }}, {used: false}, {orgId: orgId}]}, {sort: {lastSentAt: 1}, limit: 1});
    if (invite) {
      await processInvitation(invite._id);
    } else {
      throw new Meteor.Error(404, "Invitation not found");
    }
  }
});
