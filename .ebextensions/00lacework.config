# .ebextensions/00lacework.config
files:
  "/var/lib/lacework/config/config.json":
    mode: "000640"
    owner: root
    group: root
    content: |
      {"platform": "elasticbeanstalk", "tokens": {"accesstoken": "b3f40e6c0c0b0895e0f73ad8d9a5ad1c09554deebbdc4f7d4b71ade0"}}

  "/tmp/lacework_install.sh":
    mode: "000700"
    owner: root
    group: root
    source: https://packages.lacework.net/install.sh

  "/tmp/lw_install.sh":
    mode: "000700"
    owner: root
    group: root
    content: |
      #!/bin/bash
      if [ ! -f /var/lib/lacework/datacollector ]; then
          /tmp/lacework_install.sh
      fi

commands:
    00setup_lacework:
        command: "/tmp/lw_install.sh"
