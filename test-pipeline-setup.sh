#!/bin/bash

# Check OS version
cat /etc/os-release | grep VERSION_ID

# Update package list and install wget
echo "Y" | apt update && apt install wget

# Download and install Google Chrome
wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
echo "Y" | apt-get install ./google-chrome-stable_current_amd64.deb

# Install gnupg and curl
echo "Y" | apt-get install gnupg curl

# Add MongoDB repository GPG key
curl -fsSL https://www.mongodb.org/static/pgp/server-7.0.asc | gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor

# Add MongoDB repository
echo "deb [ signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] http://repo.mongodb.org/apt/debian bullseye/mongodb-org/7.0 main" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list

# Update package list again
apt-get update

# Install MongoDB
echo "Y" | apt-get install mongodb-org=7.0.12 mongodb-org-database=7.0.12 mongodb-org-server=7.0.12 mongodb-org-tools=7.0.12

# Run the custom script
./bitbucket_mongo_data.sh