import _ from '../util/underscore';
import { Mongo } from 'meteor/mongo';

const moment = require('moment-timezone');
const AuditTrail = function (doc) {
    _.extend(this, doc)
};

export const AuditTrails = new Mongo.Collection('auditTrails', {

    /** interface: {
     * _id: string;
     * userId: string;
     * personId: string;
     * orgId: string;
     * date: number;
     * methodName: string;
     * args: Record<any, any>;
    }; */

    transform: function (doc) {
        return new AuditTrail(doc);
    }
});