import { Meteor } from 'meteor/meteor';
import { Mongo } from 'meteor/mongo';
import _ from '../util/underscore';

/*
 META MOMENT SCHEMA -- for Staff/admin only
  {
    _id: String,
    moments: [String] -- momentIds
    type: String
    active: bool
    createdBy: user/person,
    createdAt: Date(long),
    defaultGroupId: String
  }

*/

const MetaMoment = function(doc) {
	_.extend(this, doc)
};

export const MetaMoments = new Mongo.Collection('metaMoments', {
	transform: function(doc) {
		return new MetaMoment(doc);
	}
});
if (Meteor.isServer) {
  MetaMoments.before.update(function (userId, doc, fieldNames, modifier, options) {
    if (doc.moments && doc.moments.length == 1 && modifier && modifier.$pull && modifier.$pull.moments && _.contains(doc.moments, modifier.$pull.moments) ) {
      modifier.$set = modifier.$set || {};
      modifier.$set.active = false;
    }
  })
}