import { Orgs } from "./orgs";
import { Mongo } from 'meteor/mongo';
import _ from '../util/underscore';

const UserInvitation = function(doc) {
	_.extend(this, doc);
};

_.extend(UserInvitation.prototype, {
	fetchOrg: async function() {
		return await Orgs.findOneAsync({_id: this.orgId});
	},
});

export const UserInvitations = new Mongo.Collection('userInvitations', {
	transform: function(doc) {
		return new UserInvitation(doc);
	}
});