import { Mongo } from 'meteor/mongo';
import _ from '../util/underscore';

/**
 * Collection for storing payer reconciliation batch information
 * Each document represents a complete batch reconciliation
 * {
 *   orgId: string,
 *   batchLabel: string, // The payerReconciliationBatchLabel used in other collections
 *   date: timestamp,
 *   payer: string, // Payer source type
 *   payerName: string, // Description of payer
 *   checkNumber: string,
 *   totalAmount: number,
 *   totalNumberDays: number,
 *   startDate: string, // Date range start in MM/DD/YYYY format
 *   endDate: string, // Date range end in MM/DD/YYYY format
 *   createdAt: timestamp,
 *   createdBy: string, // Person ID who created this batch
 *   creatorName: string, // Name of person who created the batch
 *   supportingDocumentId: string, // Optional ID of supporting document
 *   fileName: string, // Original filename of supporting document
 *   fileType: string, // MIME type of supporting document
 *   csvContent: string, // Stored CSV content for later download
 *   csvUrl: string, // URL to download the CSV (may expire)
 *   allocations: [{ // Array of allocations in this batch
 *     invoiceId: string, // Optional - if it's an invoice credit
 *     personId: string, // Optional - person associated with the invoice
 *     personName: string, // Name of person if applicable
 *     fundId: string, // Optional - if it's a non-family fund
 *     amount: number,
 *     invoiceNumber: string, // Optional
 *     coveredDays: [string], // Optional array of days covered
 *     coveredDaysCount: number, // Number of days covered
 *     isNonFamilyFunding: boolean // Whether this is non-family funding
 *   }]
 * }
 */
global.PayerReconciliationBatch = function(doc) {
    _.extend(this, doc);
};

_.extend(PayerReconciliationBatch.prototype, {
    // Return the formatted date string
    formattedDate() {
        return this.date ? moment(this.date).format('MM/DD/YYYY') : '';
    },
    
    // Return the formatted amount with currency symbol
    formattedAmount() {
        return this.totalAmount ? `$${parseFloat(this.totalAmount).toFixed(2)}` : '$0.00';
    },
    
    // Check if this batch has a supporting document
    hasDocument() {
        return !!this.supportingDocumentId;
    }
});

export const PayerReconciliationBatches = new Mongo.Collection('payerReconciliationBatches', {
    transform: function(doc) {
        return new PayerReconciliationBatch(doc);
    }
}); 