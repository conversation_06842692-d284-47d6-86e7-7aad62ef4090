[{"ageGroup": "0-12 Months", "category": "Gross Motor Skills", "benchmark": "Experiment with different way of moving (e.g. rolls over; crawls, begins to use arms and legs purposefully, etc.)", "standardId": "PHM.12.a"}, {"ageGroup": "0-12 Months", "category": "Gross Motor Skills", "benchmark": "Reach for objects", "standardId": "PHM/GK.12.e"}, {"ageGroup": "0-12 Months", "category": "Gross Motor Skills", "benchmark": "Balance while exploring immediate environment (e.g. sits while propped up)", "standardId": "PHM.12.c"}, {"ageGroup": "0-12 Months", "category": "Fine Motor Skills", "benchmark": "Reach for and hold objects purposefully", "standardId": "PHM.12.d"}, {"ageGroup": "0-12 Months", "category": "Physical Exercise", "benchmark": "Show alertness during waking periods", "standardId": "PHM.12.b"}, {"ageGroup": "0-12 Months", "category": "Daily Living Skills", "benchmark": "Show preference for specific adults", "standardId": "PHM/SE.12.f"}, {"ageGroup": "0-12 Months", "category": "Daily Living Skills", "benchmark": "Notice when parent or primary caregiver leaves", "standardId": "PHM/SE.12.g"}, {"ageGroup": "0-12 Months", "category": "Interactions with Adults", "benchmark": "Show preference for specific adults", "standardId": "PHM/SE.12.f"}, {"ageGroup": "0-12 Months", "category": "Interactions with Adults", "benchmark": "Notice when parent or primary caregiver leaves", "standardId": "PHM/SE.12.g"}, {"ageGroup": "0-12 Months", "category": "Interactions with Peers", "benchmark": "Make sounds when other children are in view", "standardId": "SE.12.c"}, {"ageGroup": "0-12 Months", "category": "Interactions with Peers", "benchmark": "Reach out to touch peer’s face, hair, or other body part", "standardId": "SE.12.d"}, {"ageGroup": "0-12 Months", "category": "Interactions with Peers", "benchmark": "Smile at another infant or at self in mirror", "standardId": "SE.12.e"}, {"ageGroup": "0-12 Months", "category": "Interactions with Peers", "benchmark": "Repeat actions many times to cause a desired effect", "standardId": "SE.12.a"}, {"ageGroup": "0-12 Months", "category": "Interactions with Peers", "benchmark": "React when someone is crying or upset", "standardId": "SE.12.b"}, {"ageGroup": "0-12 Months", "category": "Adaptive Social Behavior", "benchmark": "Responds to changes in adults voice or tone", "standardId": "SE.12.h"}, {"ageGroup": "0-12 Months", "category": "Self-efficacy", "benchmark": "Behave in consistent ways to elicit desired response", "standardId": "SE.12.i"}, {"ageGroup": "0-12 Months", "category": "Self-control and Regulation", "benchmark": "Begin to regulate emotions with adult assistance", "standardId": "SE.12.j"}, {"ageGroup": "0-12 Months", "category": "Self-control and Regulation", "benchmark": "Comforts self by clutching, sucking, or stroking when tired or stressed", "standardId": "SE.12.k"}, {"ageGroup": "0-12 Months", "category": "Emotional Expression", "benchmark": "Express emotion related to basic needs (e.g. cry when distressed; laugh when happy; shake head “no” when presented something they do not like)", "standardId": "SE.12.l"}, {"ageGroup": "0-12 Months", "category": "Initiative and Creativity", "benchmark": "Use senses to explore the immediate environment", "standardId": "AL/LA.12.a"}, {"ageGroup": "0-12 Months", "category": "Persistence and Attentiveness", "benchmark": "Pay attention and try to reproduce pleasurable effects and outcomes", "standardId": "AL.12.b"}, {"ageGroup": "0-12 Months", "category": "Persistence and Attentiveness", "benchmark": "Pay attention to sights and sounds", "standardId": "Al.12-24.c"}, {"ageGroup": "0-12 Months", "category": "Problem Solving", "benchmark": "React to a problem", "standardId": "Al/LA.12.d"}, {"ageGroup": "0-12 Months", "category": "Reflection and Interpretation", "benchmark": "Show comfort in routines or experience that mirrors home experiences", "standardId": "AL/LA.12.e"}, {"ageGroup": "0-12 Months", "category": "Scientific and Engineering Practices", "benchmark": "Place objects in mouth to discover their characteristics", "standardId": "GK.12.a"}, {"ageGroup": "0-12 Months", "category": "Physical Science", "benchmark": "Place objects in mouth to discover their characteristics", "standardId": "GK.12.a"}, {"ageGroup": "0-12 Months", "category": "Life Sciences", "benchmark": "Show interest in the natural world", "standardId": "GK.12.b"}, {"ageGroup": "0-12 Months", "category": "Life Sciences", "benchmark": "Recognize self and family members", "standardId": "GK.12.c"}, {"ageGroup": "0-12 Months", "category": "Engineering, Technology, and Applications of Science", "benchmark": "Place objects in mouth to discover their characteristics", "standardId": "GK.12.a"}, {"ageGroup": "0-12 Months", "category": "Geography", "benchmark": "Move to explore environment (e.g. rolls over; crawls, begins to use arms and legs purposefully, etc.)", "standardId": "GK.12.d"}, {"ageGroup": "0-12 Months", "category": "Geography", "benchmark": "Reach for objects", "standardId": "PHM/GK.12.e"}, {"ageGroup": "0-12 Months", "category": "Economics", "benchmark": "Make basic needs known (e.g. cries when hungry, etc.)", "standardId": "GK.12.f"}, {"ageGroup": "0-12 Months", "category": "Community", "benchmark": "Become aware of self", "standardId": "GK.12.g"}, {"ageGroup": "0-12 Months", "category": "Community", "benchmark": "Respond to name", "standardId": "GK.12.h"}, {"ageGroup": "0-12 Months", "category": "Culture", "benchmark": "Recognize simple differences between people", "standardId": "GK.12.i"}, {"ageGroup": "0-12 Months", "category": "Visual", "benchmark": "Explore tools and materials", "standardId": "GK.12.j"}, {"ageGroup": "0-12 Months", "category": "Visual", "benchmark": "Gaze at paintings, pictures, or photographs with interest", "standardId": "GK.12.k"}, {"ageGroup": "0-12 Months", "category": "Musical", "benchmark": "Imitate new sounds and movements", "standardId": "GK.12.l"}, {"ageGroup": "0-12 Months", "category": "Musical", "benchmark": "Respond to music", "standardId": "GK.12.m"}, {"ageGroup": "0-12 Months", "category": "Musical", "benchmark": "Imitate new sounds and movements", "standardId": "GK.12.n"}, {"ageGroup": "0-12 Months", "category": "Movement", "benchmark": "Recognize simple differences between people", "standardId": "GK.12.i"}, {"ageGroup": "0-12 Months", "category": "Dramatic Expression", "benchmark": "Recognize simple differences between people", "standardId": "GK.12.i"}, {"ageGroup": "0-12 Months", "category": "Key Ideas and Details", "benchmark": "Demonstrate a beginning interest in pictures and books that have color, pattern, and contrast", "standardId": "LA.12.b"}, {"ageGroup": "0-12 Months", "category": "Craft and Structure", "benchmark": "Show an interest in the speech of others", "standardId": "LA.12.c"}, {"ageGroup": "0-12 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Demonstrate a beginning interest in pictures and books that have color, pattern, and contrast", "standardId": "LA.12.b"}, {"ageGroup": "0-12 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Show an interest in the speech of others", "standardId": "LA.12.c"}, {"ageGroup": "0-12 Months", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Demonstrate a beginning interest in pictures and books that have color, pattern, and contrast", "standardId": "LA.12.b"}, {"ageGroup": "0-12 Months", "category": "Key Ideas and Details", "benchmark": "Demonstrate a beginning interest in pictures and books that have color, pattern, and contrast", "standardId": "LA.12.b"}, {"ageGroup": "0-12 Months", "category": "Craft and Structure", "benchmark": "Show an interest in the speech of others", "standardId": "LA.12.c"}, {"ageGroup": "0-12 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Demonstrate a beginning interest in pictures and books that have color, pattern, and contrast", "standardId": "LA.12.b"}, {"ageGroup": "0-12 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Show an interest in the speech of others", "standardId": "LA.12.c"}, {"ageGroup": "0-12 Months", "category": "Fluency", "benchmark": "Demonstrate a beginning interest in pictures and books that have color, pattern, and contrast", "standardId": "LA.12.b"}, {"ageGroup": "0-12 Months", "category": "Production and Distribution of Writing", "benchmark": "React to a problem", "standardId": "AL/LA.12.d"}, {"ageGroup": "0-12 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Use senses to explore the immediate environment", "standardId": "AL/LA.12.a"}, {"ageGroup": "0-12 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Show comfort in routines or experience that mirrors home experiences", "standardId": "AL/LA.12.e"}, {"ageGroup": "0-12 Months", "category": "Comprehension and Collaboration", "benchmark": "Respond to repeated words or phrases", "standardId": "LA.12.f"}, {"ageGroup": "0-12 Months", "category": "Comprehension and Collaboration", "benchmark": "Show an interest in the speech of others", "standardId": "LA.12.c"}, {"ageGroup": "0-12 Months", "category": "Comprehension and Collaboration", "benchmark": "Use senses to explore the immediate environment", "standardId": "AL/LA.12.a"}, {"ageGroup": "0-12 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Show comfort in routines or experience that mirrors home experiences", "standardId": "AL/LA.12.e"}, {"ageGroup": "0-12 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Communicate with gestures, babbles, or making word-like sounds", "standardId": "LA.12.g"}, {"ageGroup": "0-12 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Communicate with gestures, babbles, or making word-like sounds", "standardId": "LA.12.g"}, {"ageGroup": "0-12 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Show an interest in the speech of others", "standardId": "LA.12.c"}, {"ageGroup": "0-12 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Show comfort in routines or experience that mirrors home experiences", "standardId": "AL/LA.12.e"}, {"ageGroup": "12-24 Months", "category": "Gross Motor Skills", "benchmark": "Experiment with different ways of moving (e.g. walks across room; marches; walks backwards; etc.)", "standardId": "PHM/GK.24.a"}, {"ageGroup": "12-24 Months", "category": "Gross Motor Skills", "benchmark": "Experiment with different ways of balancing (e.g. squat to pick up toys; kneel when playing, etc.)", "standardId": "PHM.24.b"}, {"ageGroup": "12-24 Months", "category": "Fine Motor Skills", "benchmark": "Hold an object in one hand and manipulate with the other hand", "standardId": "PHM.24.c"}, {"ageGroup": "12-24 Months", "category": "Fine Motor Skills", "benchmark": "Grasp objects and pick up objects with thumb and forefingers", "standardId": "PHM.24.d"}, {"ageGroup": "12-24 Months", "category": "Physical Exercise", "benchmark": "Sustain physical activity such for at least three to five minutes at a time", "standardId": "PHM.24.e"}, {"ageGroup": "12-24 Months", "category": "Daily Living Skills", "benchmark": "Seek out familiar adult when facing a challenging situation", "standardId": "PHM/SE.24.f"}, {"ageGroup": "12-24 Months", "category": "Daily Living Skills", "benchmark": "Change focus and listen when adult is speaking to them", "standardId": "PHM/SE.24.g"}, {"ageGroup": "12-24 Months", "category": "Daily Living Skills", "benchmark": "Recognize basic health care workers in books, pictures, or photographs", "standardId": "PHM.24.h"}, {"ageGroup": "12-24 Months", "category": "Daily Living Skills", "benchmark": "Attempt or complete basic hygiene practices with adult support", "standardId": "PHM.24.i"}, {"ageGroup": "12-24 Months", "category": "Safe Practices", "benchmark": "Cooperate with basic safety practices", "standardId": "PHM/GK.24.j"}, {"ageGroup": "12-24 Months", "category": "Rules and Regulations", "benchmark": "Cooperate with basic safety practices", "standardId": "PHM/GK.24.j"}, {"ageGroup": "12-24 Months", "category": "Interactions with Adults", "benchmark": "Seek out familiar adult when facing a challenging situation", "standardId": "PHM/SE.24.f"}, {"ageGroup": "12-24 Months", "category": "Interactions with Adults", "benchmark": "Change focus and listen when adult is speaking to them", "standardId": "PHM/SE.24.g"}, {"ageGroup": "12-24 Months", "category": "Interactions with Peers", "benchmark": "Imitate actions of other children", "standardId": "SE.24.a"}, {"ageGroup": "12-24 Months", "category": "Interactions with Peers", "benchmark": "Respond appropriately to others’ expressions of wants", "standardId": "SE.24.b"}, {"ageGroup": "12-24 Months", "category": "Interactions with Peers", "benchmark": "Experiment with effects of own actions on objects and people", "standardId": "SE.24.c"}, {"ageGroup": "12-24 Months", "category": "Adaptive Social Behavior", "benchmark": "Understand one word rules such as “no” or “stop”", "standardId": "SE.24.d"}, {"ageGroup": "12-24 Months", "category": "Adaptive Social Behavior", "benchmark": "Asserts ownership (e.g., by saying “mine”)", "standardId": "SE.24.e"}, {"ageGroup": "12-24 Months", "category": "Self-efficacy", "benchmark": "Begin to demonstrate need to complete tasks on his/her own", "standardId": "SE.24.h"}, {"ageGroup": "12-24 Months", "category": "Self-control and Regulation", "benchmark": "Begin to express their likes and dislikes", "standardId": "SE.24.i"}, {"ageGroup": "12-24 Months", "category": "Self-control and Regulation", "benchmark": "Begin to develop strategies to manage expression of feelings", "standardId": "SE.24.j"}, {"ageGroup": "12-24 Months", "category": "Emotional Expression", "benchmark": "Express emotion related to a problem or conflict", "standardId": "SE.24.k"}, {"ageGroup": "12-24 Months", "category": "Emotional Expression", "benchmark": "Show a range of emotions including fear, surprise, happiness, and contentment", "standardId": "SE.24.l"}, {"ageGroup": "12-24 Months", "category": "Initiative and Creativity", "benchmark": "Explore the environment in close proximity to and in constant sight of caregiver", "standardId": "AL/LA.24.a"}, {"ageGroup": "12-24 Months", "category": "Initiative and Creativity", "benchmark": "Show interest in new activities and experiences", "standardId": "AL/LA.24.g"}, {"ageGroup": "12-24 Months", "category": "Persistence and Attentiveness", "benchmark": "Repeat difficult tasks or activities many times to achieve mastery", "standardId": "AL.24.b"}, {"ageGroup": "12-24 Months", "category": "Persistence and Attentiveness", "benchmark": "Pay attention to sights and sounds", "standardId": "AL.12-24.c"}, {"ageGroup": "12-24 Months", "category": "Problem Solving", "benchmark": "Use single object in different ways", "standardId": "AL/LA.24.d"}, {"ageGroup": "12-24 Months", "category": "Reflection and Interpretation", "benchmark": "Prefer routines and activities that mirror home routines", "standardId": "AL/LA.24.e"}, {"ageGroup": "12-24 Months", "category": "Reflection and Interpretation", "benchmark": "Relate objects and people to events", "standardId": "AL/LA.24.f"}, {"ageGroup": "12-24 Months", "category": "Number Sense", "benchmark": "Begin to imitate rote counting using some names of numbers, with errors in sequence", "standardId": "GK.24.b"}, {"ageGroup": "12-24 Months", "category": "Number Sense", "benchmark": "Begin to use number concepts (e.g. “I want more”)", "standardId": "GK.24.c"}, {"ageGroup": "12-24 Months", "category": "Number Sense", "benchmark": "Demonstrate an awareness of simple patterns", "standardId": "GK.24.d"}, {"ageGroup": "12-24 Months", "category": "Operations", "benchmark": "Begin to use number concepts (e.g. “I want more”)", "standardId": "GK.24.c"}, {"ageGroup": "12-24 Months", "category": "Measurement and Data", "benchmark": "Make simple comparison between two objects", "standardId": "GK.24.e"}, {"ageGroup": "12-24 Months", "category": "Measurement and Data", "benchmark": "Show an interest in matching and sorting according to color, size, or shape", "standardId": "GK.24.f"}, {"ageGroup": "12-24 Months", "category": "Geometry", "benchmark": "Move body in different directions, such as up, down, around or under", "standardId": "GK.24.g"}, {"ageGroup": "12-24 Months", "category": "Geometry", "benchmark": "Recognize basic shapes in the environment", "standardId": "GK.24.h"}, {"ageGroup": "12-24 Months", "category": "Geometry", "benchmark": "With adult assistance, explore the ways that shapes and objects fit together", "standardId": "GK.24.i"}, {"ageGroup": "12-24 Months", "category": "Scientific and Engineering Practices", "benchmark": "Use the senses as tools with which to observe", "standardId": "GK.24.k"}, {"ageGroup": "12-24 Months", "category": "Scientific and Engineering Practices", "benchmark": "Ask questions without seeking answers", "standardId": "GK.24.l"}, {"ageGroup": "12-24 Months", "category": "Physical Science", "benchmark": "Explore the characteristics of objects", "standardId": "GK.24.m"}, {"ageGroup": "12-24 Months", "category": "Physical Science", "benchmark": "Move objects in more than one way", "standardId": "GK.24.n"}, {"ageGroup": "12-24 Months", "category": "Life Sciences", "benchmark": "Differentiate between animal and plant", "standardId": "GK.24.o"}, {"ageGroup": "12-24 Months", "category": "Life Sciences", "benchmark": "Distinguish between adult and baby", "standardId": "GK.24.p"}, {"ageGroup": "12-24 Months", "category": "Earth Place in the Universe", "benchmark": "Observe weather conditions", "standardId": "GK.24.q"}, {"ageGroup": "12-24 Months", "category": "Earth Place in the Universe", "benchmark": "Differentiate between night and day", "standardId": "GK.24.r"}, {"ageGroup": "12-24 Months", "category": "Earth Place in the Universe", "benchmark": "Point to or label sky, sun, cloud, star, moon", "standardId": "GK.24.s"}, {"ageGroup": "12-24 Months", "category": "Engineering, Technology, and Applications of Science", "benchmark": "Explore the characteristics of objects", "standardId": "GK.24.m"}, {"ageGroup": "12-24 Months", "category": "History", "benchmark": "Respond to changes in routines or schedule", "standardId": "GK.24.t"}, {"ageGroup": "12-24 Months", "category": "Geography", "benchmark": "Experiment with different ways of moving (e.g. walks across room; marches; walks backwards; etc.)", "standardId": "PHM/GK.24.a"}, {"ageGroup": "12-24 Months", "category": "Economics", "benchmark": "Name some basic needs with single words (e.g. “milk”, “<PERSON>”, etc.)", "standardId": "GK.24.u"}, {"ageGroup": "12-24 Months", "category": "Government/Political Science", "benchmark": "Cooperate with basic safety practices", "standardId": "PHM/GK.24.j"}, {"ageGroup": "12-24 Months", "category": "Community", "benchmark": "Repeat activities through trial and error and look at educator for acknowledgement of success", "standardId": "GK.24.v"}, {"ageGroup": "12-24 Months", "category": "Community", "benchmark": "Accomplish a new task and clap for self", "standardId": "GK.24.w"}, {"ageGroup": "12-24 Months", "category": "Community", "benchmark": "Enjoy poems, stories, and songs about a variety of people and cultures", "standardId": "GK.24.x"}, {"ageGroup": "12-24 Months", "category": "Visual", "benchmark": "Use basic art materials to create an age appropriate product", "standardId": "GK.24.y"}, {"ageGroup": "12-24 Months", "category": "Visual", "benchmark": "Show interest in others’ work or products", "standardId": "GK.24.z"}, {"ageGroup": "12-24 Months", "category": "Visual", "benchmark": "Make scribbles or marks", "standardId": "GK/LA.24.aa"}, {"ageGroup": "12-24 Months", "category": "Musical", "benchmark": "Show pleasure and excitement when exposed to music", "standardId": "GK.24.bb"}, {"ageGroup": "12-24 Months", "category": "Musical", "benchmark": "Use instruments to create sound", "standardId": "GK.24.cc"}, {"ageGroup": "12-24 Months", "category": "Movement", "benchmark": "Show interest in moving body in different ways", "standardId": "GK.24.dd"}, {"ageGroup": "12-24 Months", "category": "Movement", "benchmark": "Act out real behaviors during play using objects for intended purpose", "standardId": "GK.24.ee"}, {"ageGroup": "12-24 Months", "category": "Dramatic Expression", "benchmark": "Enjoy poems, stories, and songs about a variety of people and cultures", "standardId": "GK.24.x"}, {"ageGroup": "12-24 Months", "category": "Dramatic Expression", "benchmark": "Respond to audience appreciation of actions", "standardId": "GK.24.ff"}, {"ageGroup": "12-24 Months", "category": "Key Ideas and Details", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Key Ideas and Details", "benchmark": "Point to a character when named in a story", "standardId": "LA.24.c"}, {"ageGroup": "12-24 Months", "category": "Craft and Structure", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Key Ideas and Details", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Craft and Structure", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Relate objects and people to events", "standardId": "Al/LA.24.f"}, {"ageGroup": "12-24 Months", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Select a favorite book to read", "standardId": "LA.24.h"}, {"ageGroup": "12-24 Months", "category": "Print Concepts", "benchmark": "Show interest in books or photos", "standardId": "LA.24.i"}, {"ageGroup": "12-24 Months", "category": "Phonological Awareness", "benchmark": "Show interest in rhyming words", "standardId": "LA.24.j"}, {"ageGroup": "12-24 Months", "category": "Fluency", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Text Types and Purposes", "benchmark": "Recognize people, objects, and animals in pictures", "standardId": "LA.24.l"}, {"ageGroup": "12-24 Months", "category": "Text Types and Purposes", "benchmark": "Make simple statements about people or things not present", "standardId": "LA.24.m"}, {"ageGroup": "12-24 Months", "category": "Text Types and Purposes", "benchmark": "Make scribbles or marks", "standardId": "GK/LA.24.aa"}, {"ageGroup": "12-24 Months", "category": "Production and Distribution of Writing", "benchmark": "Use single object in different ways", "standardId": "AL/LA.24.d"}, {"ageGroup": "12-24 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Explore the environment in close proximity to and in constant sight of caregiver", "standardId": "AL/LA.24.a"}, {"ageGroup": "12-24 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Show interest in new activities and experiences", "standardId": "AL/LA.24.g"}, {"ageGroup": "12-24 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Relate objects and people to events", "standardId": "AL/LA.24.f"}, {"ageGroup": "12-24 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Prefer routines and activities that mirror home routines", "standardId": "AL/LA.24.e"}, {"ageGroup": "12-24 Months", "category": "Comprehension and Collaboration", "benchmark": "Focus attention on speaker and attempt to imitate speech", "standardId": "LA.24.n"}, {"ageGroup": "12-24 Months", "category": "Comprehension and Collaboration", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Comprehension and Collaboration", "benchmark": "Explore the environment in close proximity to and in constant sight of caregiver", "standardId": "AL/LA.24.a"}, {"ageGroup": "12-24 Months", "category": "Comprehension and Collaboration", "benchmark": "Show interest in new activities and experiences", "standardId": "AL/LA.24.g"}, {"ageGroup": "12-24 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Prefer routines and activities that mirror home routines", "standardId": "AL/LA.24.e"}, {"ageGroup": "12-24 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Recognize people, objects, and animals in pictures", "standardId": "LA.24.l"}, {"ageGroup": "12-24 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Use 2-word combinations to communicate", "standardId": "LA.24.o"}, {"ageGroup": "12-24 Months", "category": "Conventions of Standard English", "benchmark": "Use one- or two-word phrases", "standardId": "LA.24.p"}, {"ageGroup": "12-24 Months", "category": "Conventions of Standard English", "benchmark": "Make scribbles or marks", "standardId": "GK/LA.24.aa"}, {"ageGroup": "12-24 Months", "category": "Conventions of Standard English", "benchmark": "Recognize people, objects, and animals in pictures", "standardId": "LA.24.l"}, {"ageGroup": "12-24 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Name familiar people, animals and objects", "standardId": "LA.24.k"}, {"ageGroup": "12-24 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Show an awareness of word relationships when matching and sorting objects by color, size, or shape", "standardId": "LA.24.q"}, {"ageGroup": "12-24 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Point to pictures or objects in books when asked", "standardId": "LA.24.b"}, {"ageGroup": "12-24 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Prefer routines and activities that mirror home routines", "standardId": "AL/LA.24.e"}, {"ageGroup": "24-36 Months", "category": "Gross Motor Skills", "benchmark": "Continue to experiment with different ways of moving (e.g. walks across room; marches; walks backwards; etc.)", "standardId": "PHM/GK.36.a"}, {"ageGroup": "24-36 Months", "category": "Gross Motor Skills", "benchmark": "Sustain balance during simple movement experiences (e.g. jump off step, landing on two feet; jump over small objects; etc.)", "standardId": "PHM.36.b"}, {"ageGroup": "24-36 Months", "category": "Fine Motor Skills", "benchmark": "Twist the wrist to accomplish a task", "standardId": "PHM.36.c"}, {"ageGroup": "24-36 Months", "category": "Fine Motor Skills", "benchmark": "Refine grasp to manipulate tools and objects", "standardId": "PHM.36.d"}, {"ageGroup": "24-36 Months", "category": "Physical Exercise", "benchmark": "Participate in a variety of physical exercise", "standardId": "PHM.36-48.e"}, {"ageGroup": "24-36 Months", "category": "Daily Living Skills", "benchmark": "Initiate interactions with familiar and unfamiliar adults", "standardId": "PHM/SE.36.f"}, {"ageGroup": "24-36 Months", "category": "Daily Living Skills", "benchmark": "Recognize basic health care workers in books, pictures, photographs, and in person", "standardId": "PHM.36.g"}, {"ageGroup": "24-36 Months", "category": "Daily Living Skills", "benchmark": "Attempt hygiene routines independently", "standardId": "PHM.36.h"}, {"ageGroup": "24-36 Months", "category": "Nutrition", "benchmark": "Select from healthy food choices", "standardId": "PHM.36.i"}, {"ageGroup": "24-36 Months", "category": "Safe Practices", "benchmark": "Use basic safety practices", "standardId": "PHM/GK.36-48.j"}, {"ageGroup": "24-36 Months", "category": "Rules and Regulations", "benchmark": "Use basic safety practices", "standardId": "PHM/GK.36-48.j"}, {"ageGroup": "24-36 Months", "category": "Interactions with Adults", "benchmark": "Initiate interactions with familiar and unfamiliar adults", "standardId": "PHM/SE.36.f"}, {"ageGroup": "24-36 Months", "category": "Interactions with Peers", "benchmark": "Play near other children and uses similar materials or actions", "standardId": "SE.36.a"}, {"ageGroup": "24-36 Months", "category": "Interactions with Peers", "benchmark": "Interact with other children during play", "standardId": "SE.36.b"}, {"ageGroup": "24-36 Months", "category": "Interactions with Peers", "benchmark": "Demonstrate concerns about the feelings of others", "standardId": "SE.36.c"}, {"ageGroup": "24-36 Months", "category": "Adaptive Social Behavior", "benchmark": "Follow rules with teacher support", "standardId": "SE.36.d"}, {"ageGroup": "24-36 Months", "category": "Self-efficacy", "benchmark": "Demonstrate confidence in meeting ones needs", "standardId": "SE.36.e"}, {"ageGroup": "24-36 Months", "category": "Self-control and Regulation", "benchmark": "Begin to regulate emotions", "standardId": "SE.36.f"}, {"ageGroup": "24-36 Months", "category": "Emotional Expression", "benchmark": "Begin to label feelings", "standardId": "SE.36.g"}, {"ageGroup": "24-36 Months", "category": "Initiative and Creativity", "benchmark": "Explore the environment independently but seek occasional approval from near-by adults", "standardId": "AL/LA.36.a"}, {"ageGroup": "24-36 Months", "category": "Initiative and Creativity", "benchmark": "Try new activities or experiences with adult encouragement", "standardId": "AL/LA.36.b"}, {"ageGroup": "24-36 Months", "category": "Persistence and Attentiveness", "benchmark": "Show confidence and pleasure in the completion of a task or activity", "standardId": "AL.36.c"}, {"ageGroup": "24-36 Months", "category": "Persistence and Attentiveness", "benchmark": "Begin to show persistence in a variety of tasks", "standardId": "AL.36.d"}, {"ageGroup": "24-36 Months", "category": "Problem Solving", "benchmark": "Use materials in new ways to accomplish task", "standardId": "AL/LA.36.e"}, {"ageGroup": "24-36 Months", "category": "Reflection and Interpretation", "benchmark": "Recognize specific activities that are home or school functions", "standardId": "AL/LA.36.f"}, {"ageGroup": "24-36 Months", "category": "Reflection and Interpretations", "benchmark": "With adult support, recall the sequence of personal routines or events", "standardId": "AL/LA.36.g"}, {"ageGroup": "24-36 Months", "category": "Effective and Ethical Use of Technology", "benchmark": "Engage in tactile experiences creating letters and other forms", "standardId": "AL.36.h"}, {"ageGroup": "24-36 Months", "category": "Number Sense", "benchmark": "Verbally count to 5", "standardId": "GK.36.b"}, {"ageGroup": "24-36 Months", "category": "Number Sense", "benchmark": "Show an understanding of number concepts (i.e. one/two; more/less)", "standardId": "GK.36.c"}, {"ageGroup": "24-36 Months", "category": "Number Sense", "benchmark": "Recognize and name a few numerals", "standardId": "GK.36.d"}, {"ageGroup": "24-36 Months", "category": "Number Sense", "benchmark": "Recognize simple patterns", "standardId": "GK.36.e"}, {"ageGroup": "24-36 Months", "category": "Measurement and Data", "benchmark": "Identify characteristics for comparison (e.g., size, color, shape)", "standardId": "GK.36.f"}, {"ageGroup": "24-36 Months", "category": "Measurement and Data", "benchmark": "Order a few objects by size with assistance", "standardId": "GK.36.g"}, {"ageGroup": "24-36 Months", "category": "Measurement and Data", "benchmark": "Match and sort according to one attribute: color, size, or shape", "standardId": "GK.36.h"}, {"ageGroup": "24-36 Months", "category": "Geometry", "benchmark": "Imitate basic directionality with adults and peers", "standardId": "GK.36.i"}, {"ageGroup": "24-36 Months", "category": "Geometry", "benchmark": "Match two identical shapes", "standardId": "GK.36.k"}, {"ageGroup": "24-36 Months", "category": "Geometry", "benchmark": "Explore the ways that shapes and objects fit together", "standardId": "GK.36.l"}, {"ageGroup": "24-36 Months", "category": "Scientific Engineering and Practices", "benchmark": "Use the senses as tools with which to observe and describe", "standardId": "GK.36.m"}, {"ageGroup": "24-36 Months", "category": "Scientific Engineering and Practices", "benchmark": "Ask questions and may seek answers", "standardId": "GK.36.n"}, {"ageGroup": "24-36 Months", "category": "Physical Science", "benchmark": "Identify differences in the properties of some objects or materials", "standardId": "GK.36.o"}, {"ageGroup": "24-36 Months", "category": "Physical Science", "benchmark": "Use words to describe when moving objects in different ways", "standardId": "GK.36.p"}, {"ageGroup": "24-36 Months", "category": "Life Sciences", "benchmark": "Begin to understand the difference between living and non-living things", "standardId": "GK.36.q"}, {"ageGroup": "24-36 Months", "category": "Life Sciences", "benchmark": "Identify adults and their offspring", "standardId": "GK.36.r"}, {"ageGroup": "24-36 Months", "category": "Life Sciences", "benchmark": "Name some common animals and their babies", "standardId": "GK.36.s"}, {"ageGroup": "24-36 Months", "category": "Earth’s Place in the Universe", "benchmark": "Notice weather change", "standardId": "GK.36.t"}, {"ageGroup": "24-36 Months", "category": "Earth’s Place in the Universe", "benchmark": "Identify the sky different characteristics during night and day", "standardId": "GK.36.u"}, {"ageGroup": "24-36 Months", "category": "Earth Place in the Universe", "benchmark": "Notice differences in cloud patterns", "standardId": "GK.36.v"}, {"ageGroup": "24-36 Months", "category": "Engineering, Technology, and the Applications of Science", "benchmark": "Uses simple tools to continue exploration", "standardId": "GK.36.w"}, {"ageGroup": "24-36 Months", "category": "History", "benchmark": "State periods of day when events occur", "standardId": "GK.36.x"}, {"ageGroup": "24-36 Months", "category": "Geography", "benchmark": "Follow a pathway or roadway on a large car mat", "standardId": "GK.36.y"}, {"ageGroup": "24-36 Months", "category": "Geography", "benchmark": "Continue to experiment with different ways of moving (e.g. walks across room; marches; walks backwards; etc.)", "standardId": "PHM/GK.36.a"}, {"ageGroup": "24-36 Months", "category": "Economics", "benchmark": "Recognize that others have basic needs (e.g. offers a cookie, or a hug, etc.)", "standardId": "GK.36.z"}, {"ageGroup": "24-36 Months", "category": "Economics", "benchmark": "Name self in pictures", "standardId": "GK.36.aa"}, {"ageGroup": "24-36 Months", "category": "Economics", "benchmark": "Recognize that money is needed to purchase materials", "standardId": "GK.36bb"}, {"ageGroup": "24-36 Months", "category": "Economics", "benchmark": "Identify that businesses provide goods or services", "standardId": "GK.36cc"}, {"ageGroup": "24-36 Months", "category": "Government/Political Science", "benchmark": "Use basic safety practices", "standardId": "PHM/GK.36-48.j"}, {"ageGroup": "24-36 Months", "category": "Community", "benchmark": "Have knowledge of own characteristics (such as name, gender, age, physical traits and family roles", "standardId": "GK.36.dd"}, {"ageGroup": "24-36 Months", "category": "Community", "benchmark": "Name self in pictures", "standardId": "GK.36.aa"}, {"ageGroup": "24-36 Months", "category": "Culture", "benchmark": "With teacher support, begin to develop awareness, knowledge, and appreciation of own culture", "standardId": "GK.36.ee"}, {"ageGroup": "24-36 Months", "category": "Visual", "benchmark": "Tell about own art products", "standardId": "GK.36.ff"}, {"ageGroup": "24-36 Months", "category": "Visual", "benchmark": "With adult assistance, use a variety of tools and materials to create new products", "standardId": "GK.36.gg"}, {"ageGroup": "24-36 Months", "category": "Visual", "benchmark": "With adult assistance, comment on characteristics of others’ work", "standardId": "GK.36.hh"}, {"ageGroup": "24-36 Months", "category": "Visual", "benchmark": "Create age appropriate representations of real objects and concepts in artwork", "standardId": "GK.36.ii"}, {"ageGroup": "24-36 Months", "category": "Musical", "benchmark": "Use instruments to create sound and rhythm", "standardId": "GK.36.jj"}, {"ageGroup": "24-36 Months", "category": "Musical", "benchmark": "Sing songs in recognizable ways", "standardId": "GK.36-48.kk"}, {"ageGroup": "24-36 Months", "category": "Movement", "benchmark": "Use body and energy to move in different", "standardId": "GK.36-48.ll"}, {"ageGroup": "24-36 Months", "category": "Movement", "benchmark": "Begin to take on roles of familiar people, animals, or characters", "standardId": "GK.36.mm"}, {"ageGroup": "24-36 Months", "category": "Movement", "benchmark": "Continue to experiment with different ways of moving (e.g. walks across room; marches; walks backwards; etc.)", "standardId": "PHM/GK.36.a"}, {"ageGroup": "24-36 Months", "category": "Dramatic Expression", "benchmark": "With teacher support, begin to develop awareness, knowledge, and appreciation of own culture", "standardId": "GK.36.ee"}, {"ageGroup": "24-36 Months", "category": "Dramatic Expression", "benchmark": "Seek an audience for one actions", "standardId": "GK.36.nn"}, {"ageGroup": "24-36 Months", "category": "Key Ideas and Details", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Key Ideas and Details", "benchmark": "With prompting and support, retell an event from a story", "standardId": "LA.36.d"}, {"ageGroup": "24-36 Months", "category": "Key Ideas and Details", "benchmark": "Identify a character or recall an event in a story", "standardId": "LA.36.f"}, {"ageGroup": "24-36 Months", "category": "Craft and Structure", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Craft and Structure", "benchmark": "Recognize pictures of familiar characters in book", "standardId": "LA.36.i"}, {"ageGroup": "24-36 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Pretend to read a familiar book", "standardId": "LA.36.j"}, {"ageGroup": "24-36 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Key Ideas and Details", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Craft and Structure", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Craft and Structure", "benchmark": "Request familiar or favorite books", "standardId": "LA.36.k"}, {"ageGroup": "24-36 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Pretend to read a familiar book", "standardId": "LA.36.j"}, {"ageGroup": "24-36 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Integration of Knowledge and Ideas", "benchmark": "With adult support, recall the sequence of personal routines or events", "standardId": "AL/LA.36.g"}, {"ageGroup": "24-36 Months", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Make an “I like” statement about a favorite book or story", "standardId": "LA.36.l"}, {"ageGroup": "24-36 Months", "category": "Print Concepts", "benchmark": "Demonstrate book handling skills", "standardId": "LA.36.m"}, {"ageGroup": "24-36 Months", "category": "Print Concepts", "benchmark": "Recognize and name a few letters", "standardId": "LA.36.n"}, {"ageGroup": "24-36 Months", "category": "Phonological Awareness", "benchmark": "Sing along with rhyming songs", "standardId": "LA.36.o"}, {"ageGroup": "24-36 Months", "category": "Phonological Awareness", "benchmark": "Sing along with songs that have words with repeating initial sound", "standardId": "LA.36.p"}, {"ageGroup": "24-36 Months", "category": "Fluency", "benchmark": "Pretend to read a familiar book", "standardId": "LA.36.j"}, {"ageGroup": "24-36 Months", "category": "Text Types and Purposes", "benchmark": "Notice details in an illustration or picture", "standardId": "LA.36.q"}, {"ageGroup": "24-36 Months", "category": "Text Types and Purposes", "benchmark": "Recognize and label people, objects, and animals in pictures", "standardId": "LA.36.r"}, {"ageGroup": "24-36 Months", "category": "Text Types and Purposes", "benchmark": "Tell simple stories about people or things not present", "standardId": "LA.36.s"}, {"ageGroup": "24-36 Months", "category": "Text Types and Purposes", "benchmark": "Make controlled linear scribbles", "standardId": "LA.36.t"}, {"ageGroup": "24-36 Months", "category": "Production and Distribution of Writing", "benchmark": "Use materials in new ways to accomplish task", "standardId": "AL/LA.36.e"}, {"ageGroup": "24-36 Months", "category": "Production and Distribution of Writing", "benchmark": "Engage in tactile experiences creating letters and other forms", "standardId": "AL.36.h"}, {"ageGroup": "24-36 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Explore the environment independently but seek occasional approval from near-by adults", "standardId": "AL/LA.36.a"}, {"ageGroup": "24-36 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Try new activities or experiences with adult encouragement", "standardId": "AL/LA.36.b"}, {"ageGroup": "24-36 Months", "category": "Research to Build and Present Knowledge", "benchmark": "With adult support, recall the sequence of personal routines or events", "standardId": "AL/LA.36.g"}, {"ageGroup": "24-36 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Recognize specific activities that are home or school functions", "standardId": "AL/LA.36.f"}, {"ageGroup": "24-36 Months", "category": "Comprehension and Collaboration", "benchmark": "Initiate and engage in brief conversations with peers and adults", "standardId": "LA.36.u"}, {"ageGroup": "24-36 Months", "category": "Comprehension and Collaboration", "benchmark": "Begin to use appropriate skills when communicating", "standardId": "LA.36.v"}, {"ageGroup": "24-36 Months", "category": "Comprehension and Collaboration", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Comprehension and Collaboration", "benchmark": "Explore the environment independently but seek occasional approval from near-by adults", "standardId": "AL/LA.36.a"}, {"ageGroup": "24-36 Months", "category": "Comprehension and Collaboration", "benchmark": "Try new activities or experiences with adult encouragement", "standardId": "AL/LA.36.b"}, {"ageGroup": "24-36 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Recognize specific activities that are home or school functions", "standardId": "AL/LA.36.f"}, {"ageGroup": "24-36 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Recognize and label people, objects, and animals in pictures", "standardId": "LA.36.r"}, {"ageGroup": "24-36 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Share experiences using simple 2-3 word combinations", "standardId": "LA.36.w"}, {"ageGroup": "24-36 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Speak to be understood by a familiar adult", "standardId": "LA.36.x"}, {"ageGroup": "24-36 Months", "category": "Conventions of Standard English", "benchmark": "Use three- to four- word sentences (may omit some words)", "standardId": "LA.36.y"}, {"ageGroup": "24-36 Months", "category": "Conventions of Standard English", "benchmark": "Make controlled linear scribbles", "standardId": "LA.36.t"}, {"ageGroup": "24-36 Months", "category": "Conventions of Standard English", "benchmark": "Recognize and label people, objects, and animals in pictures", "standardId": "LA.36.r"}, {"ageGroup": "24-36 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Use some personal pronouns", "standardId": "LA.36.z"}, {"ageGroup": "24-36 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "With guidance and support, begin to explore word relationships by matching and sorting according to color, size, or shape", "standardId": "LA.36.aa"}, {"ageGroup": "24-36 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Contribute language from the book at the appropriate time", "standardId": "LA.36.c"}, {"ageGroup": "24-36 Months", "category": "Vocabulary Acquisition and Use", "benchmark": "Recognize specific activities that are home or school functions", "standardId": "AL/LA.36.f"}, {"ageGroup": "36-48 Months", "category": "Gross Motor Skills", "benchmark": "Move purposefully from place to place with control (e.g. avoids bumping into things when running; walks up and down stairs alternating feet; gallops; etc.)", "standardId": "PHM.48.a"}, {"ageGroup": "36-48 Months", "category": "Gross Motor Skills", "benchmark": "Throw/Kick ball with flexible body movements", "standardId": "PHM.48.b"}, {"ageGroup": "36-48 Months", "category": "Gross Motor Skills", "benchmark": "Continue to sustain balance during simple movement experiences", "standardId": "PHM.48.c"}, {"ageGroup": "36-48 Months", "category": "Fine Motor Skills", "benchmark": "Refine wrist and finger movements for more control (e.g. pours without spilling; buttons, zips, buckles; turns knobs; etc.)", "standardId": "PHM.48.d"}, {"ageGroup": "36-48 Months", "category": "Fine Motor Skills", "benchmark": "Hold writing/drawing tools with a three-point finger grip", "standardId": "PHM.48.f"}, {"ageGroup": "36-48 Months", "category": "Physical Exercise", "benchmark": "Participate in a variety of physical exercise", "standardId": "PHM.36-48.e"}, {"ageGroup": "36-48 Months", "category": "Daily Living Skills", "benchmark": "Ask for help when hurt", "standardId": "PHM.48-KE.g"}, {"ageGroup": "36-48 Months", "category": "Daily Living Skills", "benchmark": "Name one person in the school or community who provides health support for others", "standardId": "PHM.48.h"}, {"ageGroup": "36-48 Months", "category": "Daily Living Skills", "benchmark": "Attend to personal health needs and ask for assistance when needed", "standardId": "PHM.48.i"}, {"ageGroup": "36-48 Months", "category": "Daily Living Skills", "benchmark": "Identify personal hygiene needs (e.g. “I need to go potty”)", "standardId": "PHM.48.k"}, {"ageGroup": "36-48 Months", "category": "Daily Living Skills", "benchmark": "With adult assistance, wash hands when needed and cover mouth when coughing or sneezing", "standardId": "PHM.48.l"}, {"ageGroup": "36-48 Months", "category": "Nutrition", "benchmark": "Identify healthy food choices", "standardId": "PHM.48.m"}, {"ageGroup": "36-48 Months", "category": "Safe Practices", "benchmark": "Use basic safety practices", "standardId": "PHM/GK.36-48.j"}, {"ageGroup": "36-48 Months", "category": "Safe Practices", "benchmark": "Identify helpful and harmful substances", "standardId": "PHM.48.o"}, {"ageGroup": "36-48 Months", "category": "Rules and Regulations", "benchmark": "Retell a rule or safety practice with adult assistance", "standardId": "PHM/GK.48.n"}, {"ageGroup": "36-48 Months", "category": "Interactions with Adults", "benchmark": "With assistance, separates from significant adults without demonstrating a great deal of anxiety", "standardId": "SE.48.a"}, {"ageGroup": "36-48 Months", "category": "Interactions with Peers", "benchmark": "Initiate interactions with other children or interacts when other children initiate", "standardId": "SE.48.b"}, {"ageGroup": "36-48 Months", "category": "Interactions with Peers", "benchmark": "Participate in small- and large-group activities", "standardId": "SE.48.c"}, {"ageGroup": "36-48 Months", "category": "Interactions with Peers", "benchmark": "Make and maintain a friendship with at least one child", "standardId": "SE.48.d"}, {"ageGroup": "36-48 Months", "category": "Interactions with Peers", "benchmark": "At times, recognize and name the feeling of self and others", "standardId": "SE.48.e"}, {"ageGroup": "36-48 Months", "category": "Adaptive Social Behavior", "benchmark": "Follow routines and social rules in a group setting most of the time", "standardId": "SE.48.f"}, {"ageGroup": "36-48 Months", "category": "Self-efficacy", "benchmark": "Demonstrate confidence in own abilities", "standardId": "SE.48.g"}, {"ageGroup": "36-48 Months", "category": "Self-control and Regulation", "benchmark": "Regulate own emotions and behavior most of the time", "standardId": "SE.48.h"}, {"ageGroup": "36-48 Months", "category": "Emotional Expression", "benchmark": "Regulate own emotions and behavior most of the time", "standardId": "SE.48.h"}, {"ageGroup": "36-48 Months", "category": "Initiative and Creativity", "benchmark": "Try new activities and experiences independently", "standardId": "AL/LA.48.a"}, {"ageGroup": "36-48 Months", "category": "Persistence and Attentiveness", "benchmark": "Plan and pursue a variety of challenging tasks", "standardId": "AL.48-K<PERSON><PERSON>b"}, {"ageGroup": "36-48 Months", "category": "Persistence and Attentiveness", "benchmark": "With adult assistance, sustain longer interest in working on a task or in play", "standardId": "AL.48.c"}, {"ageGroup": "36-48 Months", "category": "Problem Solving", "benchmark": "Solve problems without having to try every possibility", "standardId": "AL/LA.48.d"}, {"ageGroup": "36-48 Months", "category": "Reflection and Interpretation", "benchmark": "Retell experiences in order, providing details", "standardId": "AL/LA.48-KE.e"}, {"ageGroup": "36-48 Months", "category": "Reflection and Interpretation", "benchmark": "Recall past experiences in new situations", "standardId": "AL/LA.48.f"}, {"ageGroup": "36-48 Months", "category": "Effective and Ethical Use of Technology", "benchmark": "With assistance, begins to locate information on identified topics using resources provided by teacher", "standardId": "AL.48-KE.g"}, {"ageGroup": "36-48 Months", "category": "Effective and Ethical Use of Technology", "benchmark": "Create letters and other forms using various materials", "standardId": "AL/LA.48.h"}, {"ageGroup": "36-48 Months", "category": "Number Sense", "benchmark": "Verbally count to 10", "standardId": "GK.48.a"}, {"ageGroup": "36-48 Months", "category": "Number Sense", "benchmark": "Recite numbers in the correct order and understand that numbers come “before” or “after” one another", "standardId": "GK.48.b"}, {"ageGroup": "36-48 Months", "category": "Number Sense", "benchmark": "Recognize and name written numerals to 5", "standardId": "GK.48.c"}, {"ageGroup": "36-48 Months", "category": "Number Sense", "benchmark": "Demonstrate an understanding of one-to-one correspondence", "standardId": "GK.48.d"}, {"ageGroup": "36-48 Months", "category": "Number Sense", "benchmark": "Recognize and name the number of items in a small set (up to 5 objects)", "standardId": "GK.48.e"}, {"ageGroup": "36-48 Months", "category": "Number Sense", "benchmark": "Recognize and duplicate simple patterns", "standardId": "GK.48.f"}, {"ageGroup": "36-48 Months", "category": "Operations", "benchmark": "Recognize and name the number of items in a small set (up to 5 objects)", "standardId": "GK.48.e"}, {"ageGroup": "36-48 Months", "category": "Measurement and Data", "benchmark": "Understand the purpose of standard measuring tools", "standardId": "GK.48.g"}, {"ageGroup": "36-48 Months", "category": "Measurement and Data", "benchmark": "Order objects according to one attribute:  length, weight, capacity, or area", "standardId": "GK.48.h"}, {"ageGroup": "36-48 Months", "category": "Measurement and Data", "benchmark": "Sort objects into subgroups by one or two characteristics", "standardId": "GK.48.i"}, {"ageGroup": "36-48 Months", "category": "Geometry", "benchmark": "Follow basic directionality with adults and peers", "standardId": "GK.48.j"}, {"ageGroup": "36-48 Months", "category": "Geometry", "benchmark": "Identify common geometric shapes (e.g., circle, square, rectangle, triangle)", "standardId": "GK.48.k"}, {"ageGroup": "36-48 Months", "category": "Geometry", "benchmark": "With adult assistance, create and represent 3-dimensional shapes (e.g. ball/sphere, square/box/cube, tube/cylinder using various manipulative materials such as play-dough, popsicle sticks, blocks, pipe cleaners, pattern blocks)", "standardId": "GK.48.l"}, {"ageGroup": "36-48 Months", "category": "Scientific and Engineering Practices", "benchmark": "Use the sense as tools with which to observe, describe, and classify", "standardId": "GK.48.m"}, {"ageGroup": "36-48 Months", "category": "Scientific and Engineering Practices", "benchmark": "With adult assistance, discuss changes in materials or objects observed", "standardId": "GK.48.o"}, {"ageGroup": "36-48 Months", "category": "Scientific and Engineering Practices", "benchmark": "Asks questions and seek answers about the world around them", "standardId": "GK.48.p"}, {"ageGroup": "36-48 Months", "category": "Physical Science", "benchmark": "Make comparisons among objects that have been observed", "standardId": "GK.48.q"}, {"ageGroup": "36-48 Months", "category": "Physical Science", "benchmark": "Explore and with adult assistance describe various actions that can change an object motion such as pulling, pushing, twisting, rolling, and throwing", "standardId": "GK.48.r"}, {"ageGroup": "36-48 Months", "category": "Life Sciences", "benchmark": "Identify the physical properties of some living and non-living things", "standardId": "GK.48.s"}, {"ageGroup": "36-48 Months", "category": "Earth’s Place in the Universe", "benchmark": "Notice similarities and differences between animals and their offspring", "standardId": "GK.48.t"}, {"ageGroup": "36-48 Months", "category": "Earth’s Place in the Universe", "benchmark": "Identify the characteristics of weather based on 1st hand observations using related vocabulary", "standardId": "GK.48.u"}, {"ageGroup": "36-48 Months", "category": "Earth’s Place in the Universe", "benchmark": "Describe the effects of the sun or sunlight", "standardId": "GK.48.v"}, {"ageGroup": "36-48 Months", "category": "Engineering, Technology, and Applications of Science", "benchmark": "Identify and use simple tools to extend observations", "standardId": "GK.48.w"}, {"ageGroup": "36-48 Months", "category": "History", "benchmark": "Discuss and identify the order of daily routines", "standardId": "GK.48.x"}, {"ageGroup": "36-48 Months", "category": "History", "benchmark": "Use time phrases and tense selection appropriately (e.g. today, yesterday, tomorrow, later, etc.)", "standardId": "GK.48.y"}, {"ageGroup": "36-48 Months", "category": "Geography", "benchmark": "Construct a roadway or path out of blocks or other building materials", "standardId": "GK.48.z"}, {"ageGroup": "36-48 Months", "category": "Geography", "benchmark": "Respond appropriately to moving body in directional ways", "standardId": "GK.48.aa"}, {"ageGroup": "36-48 Months", "category": "Economics", "benchmark": "Identify some basic needs and how to meet them (e.g. “When I’m thirsty I get a drink”, etc.)", "standardId": "GK.48.bb"}, {"ageGroup": "36-48 Months", "category": "Economics", "benchmark": "Pretend to be a buyer or seller", "standardId": "GK.48-KE.cc"}, {"ageGroup": "36-48 Months", "category": "Economics", "benchmark": "Identify that adults go to work to earn money", "standardId": "GK.48.dd"}, {"ageGroup": "36-48 Months", "category": "Government/Political Science", "benchmark": "Retell a rule or safety practice with adult assistance", "standardId": "PHM/GK.48.n"}, {"ageGroup": "36-48 Months", "category": "Government/Political Science", "benchmark": "Use basic safety practices", "standardId": "PHM/GK.36-48.j"}, {"ageGroup": "36-48 Months", "category": "Community", "benchmark": "Relate own identification information", "standardId": "GK.48.ee"}, {"ageGroup": "36-48 Months", "category": "Culture", "benchmark": "Show awareness, knowledge, and appreciation of own culture", "standardId": "GK.48.ff"}, {"ageGroup": "36-48 Months", "category": "Visual", "benchmark": "Describe color and shape in artwork", "standardId": "GK.48.gg"}, {"ageGroup": "36-48 Months", "category": "Visual", "benchmark": "Use a variety of tools and materials to create new products", "standardId": "GK.48.hh"}, {"ageGroup": "36-48 Months", "category": "Visual", "benchmark": "Comment on characteristics of others’ work", "standardId": "GK.48.ii"}, {"ageGroup": "36-48 Months", "category": "Visual", "benchmark": "Name the feelings that own", "standardId": "GK.48.jj"}, {"ageGroup": "36-48 Months", "category": "", "benchmark": "artwork is intended to express", "standardId": ""}, {"ageGroup": "36-48 Months", "category": "Musical", "benchmark": "Use instruments to create rhythm and sound imitating adults", "standardId": "GK.48.mm"}, {"ageGroup": "36-48 Months", "category": "Musical", "benchmark": "Sing songs in recognizable ways", "standardId": "GK.36-48.kk"}, {"ageGroup": "36-48 Months", "category": "Musical", "benchmark": "Sing songs imitating adults", "standardId": "GK.48.nn"}, {"ageGroup": "36-48 Months", "category": "Movement", "benchmark": "Use body and energy to move in different ways", "standardId": "GK.36-48.ll"}, {"ageGroup": "36-48 Months", "category": "Movement", "benchmark": "Engage in play that has a story line", "standardId": "GK.48.oo"}, {"ageGroup": "36-48 Months", "category": "Movement", "benchmark": "Move imitating adults", "standardId": "GK.48.pp"}, {"ageGroup": "36-48 Months", "category": "Dramatic Expression", "benchmark": "Show awareness, knowledge, and appreciation of own culture", "standardId": "GK.48.ff"}, {"ageGroup": "36-48 Months", "category": "Dramatic Expression", "benchmark": "Listen to storytellers and watch puppet shows", "standardId": "GK.48.qq"}, {"ageGroup": "36-48 Months", "category": "Key Ideas and Details", "benchmark": "Ask and answer questions about essential narrative elements", "standardId": "LA.48.a"}, {"ageGroup": "36-48 Months", "category": "Key Ideas and Details", "benchmark": "With prompting and support, retell a simple story in sequence with picture support or using props", "standardId": "LA.48.b"}, {"ageGroup": "36-48 Months", "category": "IKey Ideas and Details", "benchmark": "Identify characters and recall an event in a story", "standardId": "LA.48.c"}, {"ageGroup": "36-48 Months", "category": "Craft and Structure", "benchmark": "Respond appropriately to specific vocabulary and simple statements, questions, and stories", "standardId": "LA.48.g"}, {"ageGroup": "36-48 Months", "category": "Craft and Structure", "benchmark": "Recognize books written by the same author or illustrator", "standardId": "LA.48.i"}, {"ageGroup": "36-48 Months", "category": "Integration and Knowledge of Ideas", "benchmark": "Pretend to read a familiar book, describing what is on each page using picture cues", "standardId": "LA.48.j"}, {"ageGroup": "36-48 Months", "category": "Integration and Knowledge of Ideas", "benchmark": "Ask and answer questions about essential narrative elements", "standardId": "LA.48.a"}, {"ageGroup": "36-48 Months", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Ask and answer questions about essential narrative elements", "standardId": "LA.48.a"}, {"ageGroup": "36-48 Months", "category": "Key Ideas and Details", "benchmark": "Ask and answer questions about essential narrative elements", "standardId": "LA.48.a"}, {"ageGroup": "36-48 Months", "category": "Craft and Structure", "benchmark": "Respond appropriately to specific vocabulary and simple statements, questions, and stories", "standardId": "LA.48.g"}, {"ageGroup": "36-48 Months", "category": "Craft and Structure", "benchmark": "Recognize books with common subject matter", "standardId": "LA.48.k"}, {"ageGroup": "36-48 Months", "category": "Integration and Knowledge of Ideas", "benchmark": "Pretend to read a familiar book, describing what is on each page using picture cues", "standardId": "LA.48.j"}, {"ageGroup": "36-48 Months", "category": "Integration and Knowledge of Ideas", "benchmark": "Respond appropriately to specific vocabulary and simple statements, questions, and stories", "standardId": "LA.48.g"}, {"ageGroup": "36-48 Months", "category": "Integration and Knowledge of Ideas", "benchmark": "Recall the sequence of personal routines or events", "standardId": "LA.48.l"}, {"ageGroup": "36-48 Months", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Sit and listen to an engaging story from beginning to end", "standardId": "LA.48.m"}, {"ageGroup": "36-48 Months", "category": "Print Concepts", "benchmark": "Handle books respectfully and appropriately, holding them right-side up and turning pages one at a time from front to back", "standardId": "LA.48.n"}, {"ageGroup": "36-48 Months", "category": "Print Concepts", "benchmark": "Identify the sounds of a few letters", "standardId": "LA.48.o"}, {"ageGroup": "36-48 Months", "category": "Print Concepts", "benchmark": "Recognize and name 10 letters", "standardId": "LA.48.p"}, {"ageGroup": "36-48 Months", "category": "Phonological Awareness", "benchmark": "Recognize rhyming words", "standardId": "LA.48.q"}, {"ageGroup": "36-48 Months", "category": "Phonological Awareness", "benchmark": "Hear and show awareness of separate words in sentences", "standardId": "LA.48.r"}, {"ageGroup": "36-48 Months", "category": "Phonological Awareness", "benchmark": "Recognize when words share initial sound (e.g., /b/ as in <PERSON>, ball, baby, boat)", "standardId": "LA.48.s"}, {"ageGroup": "36-48 Months", "category": "Phonics and Word Recognition", "benchmark": "Recognize when words share initial sound (e.g., /b/ as in <PERSON>, ball, baby, boat)", "standardId": "LA.48.s"}, {"ageGroup": "36-48 Months", "category": "Phonics and Word Recognition", "benchmark": "Identify own name in print", "standardId": "LA.48.t"}, {"ageGroup": "36-48 Months", "category": "Phonics and Word Recognition", "benchmark": "Recognize symbols and logos in the environment", "standardId": "LA.48.u"}, {"ageGroup": "36-48 Months", "category": "Fluency", "benchmark": "Pretend to read a familiar book, describing what is on each page using picture cues", "standardId": "LA.48.j"}, {"ageGroup": "36-48 Months", "category": "Text Types and Purposes", "benchmark": "Add detail to drawings and other products with simple descriptive words, symbols, scribbles or letter-like forms", "standardId": "LA.48.v"}, {"ageGroup": "36-48 Months", "category": "Text Types and Purposes", "benchmark": "Sometimes labels after creating drawing, construction, movement, or dramatization", "standardId": "LA.48.w"}, {"ageGroup": "36-48 Months", "category": "Text Types and Purposes", "benchmark": "Tell stories that refer to other times and places with some details", "standardId": "LA.48.x"}, {"ageGroup": "36-48 Months", "category": "Text Types and Purposes", "benchmark": "Write some letters and letter-like forms", "standardId": "LA.48.y"}, {"ageGroup": "36-48 Months", "category": "Production and Distribution of Writing", "benchmark": "Solve problems without having to try every possibility", "standardId": "AL/LA.48.d"}, {"ageGroup": "36-48 Months", "category": "Production and Distribution of Writing", "benchmark": "Create letters and other forms using various materials", "standardId": "AL/LA.48.h"}, {"ageGroup": "36-48 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Try new activities and experiences independently", "standardId": "AL/LA.48.a"}, {"ageGroup": "36-48 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Recall past experiences in new situations", "standardId": "AL/LA. 48.f"}, {"ageGroup": "36-48 Months", "category": "Research to Build and Present Knowledge", "benchmark": "Retell experiences in order, providing details", "standardId": "AL/LA.48-KE.e"}, {"ageGroup": "36-48 Months", "category": "Comprehension and Collaboration", "benchmark": "With adult support, listen and respond attentively to conversations (e.g. engaging in at least 3 exchanges, pose questions and listen to the ideas of others, share experiences when asked)", "standardId": "LA.48.z"}, {"ageGroup": "36-48 Months", "category": "Comprehension and Collaboration", "benchmark": "With adult support, observe and use appropriate ways of interacting in a group (e.g. taking turns in talking, listening to peers, waiting to speak until another person is finished talking, asking questions and waiting for an answer)", "standardId": "LA.48.aa"}, {"ageGroup": "36-48 Months", "category": "Comprehension and Collaboration", "benchmark": "Respond appropriately to specific vocabulary and simple statements, questions, and stories", "standardId": "LA.48.g"}, {"ageGroup": "36-48 Months", "category": "Comprehension and Collaboration", "benchmark": "Try new activities and experiences independently", "standardId": "AL/LA.48.a"}, {"ageGroup": "36-48 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Retell experiences in order, providing details", "standardId": "AL/LA.48-KE.e"}, {"ageGroup": "36-48 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Sometimes labels after creating drawing, construction, movement, or dramatization", "standardId": "LA.48.w"}, {"ageGroup": "36-48 Months", "category": "Presentation of Knowledge and Ideas", "benchmark": "Are understood by most adults", "standardId": "LA.48.bb"}, {"ageGroup": "36-48 Months", "category": "Conventions of Standard English", "benchmark": "Use complete four- to six- word sentences", "standardId": "LA.48.cc"}, {"ageGroup": "36-48 Months", "category": "Conventions of Standard English", "benchmark": "Write some letters and letter-like forms", "standardId": "LA.48.y"}, {"ageGroup": "36-48 Months", "category": "Conventions of Standard English", "benchmark": "Sometimes labels after creating drawing, construction, movement, or dramatization", "standardId": "LA.48.w"}, {"ageGroup": "36-48 Months", "category": "Vocabulary and Acquisition Use", "benchmark": "Describe and tell the use of familiar items", "standardId": "LA.48.dd"}, {"ageGroup": "36-48 Months", "category": "Vocabulary and Acquisition Use", "benchmark": "With guidance and support, use word relationships to sort objects into subgroups by one or two characteristics", "standardId": "LA.48.ee"}, {"ageGroup": "36-48 Months", "category": "Vocabulary and Acquisition Use", "benchmark": "Respond appropriately to specific vocabulary and simple statements, questions, and stories", "standardId": "LA.48.g"}, {"ageGroup": "36-48 Months", "category": "Vocabulary and Acquisition Use", "benchmark": "Retell experiences in order, providing details", "standardId": "AL/LA.48-KE.e"}, {"ageGroup": "Kindergarten", "category": "Gross Motor Skills", "benchmark": "Coordinate complex movements in play and games (e.g. runs quickly, changes direction, stops, starts again, skips)", "standardId": "PHM.KE.a"}, {"ageGroup": "Kindergarten", "category": "Gross Motor Skills", "benchmark": "Throw/Kick/Catch ball with a full range of motion and control", "standardId": "PHM.KE.b"}, {"ageGroup": "Kindergarten", "category": "Gross Motor Skills", "benchmark": "Sustain balance during complex movement experiences (e.g. hop across the playground; attempt to jump rope, etc.)", "standardId": "PHM.KE.c"}, {"ageGroup": "Kindergarten", "category": "Fine Motor Skills", "benchmark": "Use small, precise finger and hand movements (e.g. string small beads; cuts small pictures; uses small Legos)", "standardId": "PHM.KE.d"}, {"ageGroup": "Kindergarten", "category": "Fine Motor Skills", "benchmark": "Has more control while holding writing/drawing tools with a three-point finger grip", "standardId": "PHM.KE.e"}, {"ageGroup": "Kindergarten", "category": "Physical Exercise", "benchmark": "Name a physiological indicator that accompanies moderate to vigorous physical activities (e.g. “I feel tired”, feels increase in heart rate, etc.)", "standardId": "PHM.KE.f"}, {"ageGroup": "Kindergarten", "category": "Physical Exercise", "benchmark": "Name a social or emotional benefit of participating in physical activities", "standardId": "PHM.KE.h"}, {"ageGroup": "Kindergarten", "category": "Physical Exercise", "benchmark": "Participate in physcial activities", "standardId": "PHM.KE.i"}, {"ageGroup": "Kindergarten", "category": "Daily Living Skills", "benchmark": "Identify personal hygiene needs (e.g. “I need to go potty”)", "standardId": "PHM.48.k"}, {"ageGroup": "Kindergarten", "category": "Daily Living Skills", "benchmark": "Ask for help when hurt", "standardId": "PHM.48-KE.g"}, {"ageGroup": "Kindergarten", "category": "Daily Living Skills", "benchmark": "Name people in the school or community who provide health support for others", "standardId": "PHM.KE.j"}, {"ageGroup": "Kindergarten", "category": "<PERSON>ily Living Skills", "benchmark": "Manage routines, i.e. dressing self, using toilet alone, using utensils", "standardId": "PHM.KE.k"}, {"ageGroup": "Kindergarten", "category": "Daily Living Skills", "benchmark": "Identify and attend to personal hygiene needs", "standardId": "PHM.KE.l"}, {"ageGroup": "Kindergarten", "category": "Daily Living Skills", "benchmark": "With reminders, washes hands before eating, after toileting, after using tissues", "standardId": "PHM.KE.m"}, {"ageGroup": "Kindergarten", "category": "Nutrition", "benchmark": "Can explain that some foods help their bodies to grow and be healthy", "standardId": "PHM.KE.n"}, {"ageGroup": "Kindergarten", "category": "Safe Practices", "benchmark": "Can articulate what to do in an emergency (call 911; fire procedures [evacuate, stop, drop, roll])", "standardId": "PHM.KE.o"}, {"ageGroup": "Kindergarten", "category": "Safe Practices", "benchmark": "Recognize everyday dangers (stove, knives, matches, medicine) and follows rules regarding them", "standardId": "PHM.KE.p"}, {"ageGroup": "Kindergarten", "category": "Rules and Regulations", "benchmark": "Discuss examples of rules, fairness, personal responsibilities, and authority in their own experiences and in stories read to them", "standardId": "PHM/GK.KE.q"}, {"ageGroup": "Kindergarten", "category": "Interactions with Adults", "benchmark": "Sometimes use appropriate social conventions in greetings, in introductions, and in conversations", "standardId": "SE.KE.a"}, {"ageGroup": "Kindergarten", "category": "Interactions with Peers", "benchmark": "Observe and use appropriate ways of interacting in a group of 2 to 3 children (e.g. taking turns in talking, listening to peers, waiting until someone is finished, asking questions and waiting for an answer, gaining the floor in appropriate ways)", "standardId": "SE/LA.KE.b"}, {"ageGroup": "Kindergarten", "category": "Interactions with Peers", "benchmark": "Use turn-taking in conversations and in play", "standardId": "SE.KE.c"}, {"ageGroup": "Kindergarten", "category": "Interactions with Peers", "benchmark": "Shares materials, toys, and ideas during play", "standardId": "SE.KE.d"}, {"ageGroup": "Kindergarten", "category": "Interactions with Peers", "benchmark": "Show respect and recognize the feelings of others and the causes of their reactions", "standardId": "SE.KE.e"}, {"ageGroup": "Kindergarten", "category": "Adaptive Social Behavior", "benchmark": "Follow schedule and typical classroom routines (come when called, sit attentively at circle, participate in clean-up)", "standardId": "SE.KE.f"}, {"ageGroup": "Kindergarten", "category": "Self-efficacy", "benchmark": "Show satisfaction in accomplishments", "standardId": "SE.KE.g"}, {"ageGroup": "Kindergarten", "category": "Self-efficacy", "benchmark": "Follow routines for care of own belongings and school supplies", "standardId": "SE.KE.h"}, {"ageGroup": "Kindergarten", "category": "Self-control and Regulation", "benchmark": "Regulate emotions and begin to show self-control in handling frustration and disappointment", "standardId": "SE.KE.i"}, {"ageGroup": "Kindergarten", "category": "Emotional Expression", "benchmark": "Express emotions through socially appropriate actions and words", "standardId": "SE.KE.j"}, {"ageGroup": "Kindergarten", "category": "Emotional Expression", "benchmark": "Communicate personal experiences or interests", "standardId": "SE.KE.k"}, {"ageGroup": "Kindergarten", "category": "Emotional Expression", "benchmark": "Recognize and describe own feelings", "standardId": "SE.KE.l"}, {"ageGroup": "Kindergarten", "category": "Initiative and Creativity", "benchmark": "Show eagerness to learn about a variety of topics and ideas", "standardId": "AL/LA.KE.a"}, {"ageGroup": "Kindergarten", "category": "Persistence and Attentiveness", "benchmark": "Plan and pursue a variety of challenging tasks", "standardId": "AL.48-K<PERSON><PERSON>b"}, {"ageGroup": "Kindergarten", "category": "Persistence and Attentiveness", "benchmark": "Sustain work on age-appropriate, interesting tasks, can ignore most distractions and interruptions", "standardId": "AL.KE.c"}, {"ageGroup": "Kindergarten", "category": "Problem Solving", "benchmark": "Begin to think problems through, considering several possibilities and analyzing results", "standardId": "AL/LA.KE.d"}, {"ageGroup": "Kindergarten", "category": "Reflection and Interpreation", "benchmark": "Retell experiences in order, providing details", "standardId": "AL/LA.48-KE.e"}, {"ageGroup": "Kindergarten", "category": "Reflection and Interpretation", "benchmark": "Use knowledge of everyday experiences to apply to a new situation", "standardId": "AL/LA.KE.f"}, {"ageGroup": "Kindergarten", "category": "Effective and Ethical Use of Technology", "benchmark": "With assistance, locate information on identified topics using resources provided by teacher", "standardId": "AL.48-KE.g"}, {"ageGroup": "Kindergarten", "category": "Effective and Ethical Use of Technology", "benchmark": "Experiment with a variety of writing tools and surfaces", "standardId": "AL/LA.KE.h"}, {"ageGroup": "Kindergarten", "category": "Effective and Ethical Use of Technology", "benchmark": "Identify technology that can be used to gain information", "standardId": "AL.KE.i"}, {"ageGroup": "Kindergarten", "category": "Number Sense", "benchmark": "Verbally count to 20 by ones", "standardId": "GK.KE.a"}, {"ageGroup": "Kindergarten", "category": "Number Sense", "benchmark": "Demonstrate ability to count in sequence", "standardId": "GK.KE.b"}, {"ageGroup": "Kindergarten", "category": "Number Sense", "benchmark": "Recognize and name written numerals to 10", "standardId": "GK.KE.c"}, {"ageGroup": "Kindergarten", "category": "Number Sense", "benchmark": "Count many kinds of concrete objects and actions up to 10 using one-to-one correspondence", "standardId": "GK.KE.d"}, {"ageGroup": "Kindergarten", "category": "Number Sense", "benchmark": "Count as many as 7 things in a scattered configuration with no errors", "standardId": "GK.KE.e"}, {"ageGroup": "Kindergarten", "category": "Number Sense", "benchmark": "Recognize, create, and repeat simple patterns", "standardId": "GK.KE.f"}, {"ageGroup": "Kindergarten", "category": "Operations", "benchmark": "Use a range of strategies, such as counting, subtracting, or matching to compare quantity in two sets of objects and describes the comparison with terms such as more, less, greater than, fewer, or equal to", "standardId": "GK.KE.g"}, {"ageGroup": "Kindergarten", "category": "Operations", "benchmark": "Count as many as 7 things in a scattered configuration with no errors", "standardId": "GK.KE.e"}, {"ageGroup": "Kindergarten", "category": "Measurement and Data", "benchmark": "Recognize the attributes of length, area, weight, and capacity of everyday objects and use appropriate vocabulary (e.g. long, short, light, big, small, wide, narrow)", "standardId": "GK.KE.h"}, {"ageGroup": "Kindergarten", "category": "Measurement and Data", "benchmark": "Compare the attributes of length and weight for 2 objects including: larger/shorter/same length; heavier/lighter/same, holds more, less, same", "standardId": "GK.KE.i"}, {"ageGroup": "Kindergarten", "category": "Measurement and Data", "benchmark": "Sort, classify, and serialize (puts in a pattern) objects using attributes, such as color, shape, or size", "standardId": "GK.KE.j"}, {"ageGroup": "Kindergarten", "category": "Geometry", "benchmark": "Use positional words to describe an object’s location (e.g., up, down, above, under, inside, outside)", "standardId": "GK.KE.k"}, {"ageGroup": "Kindergarten", "category": "Geometry", "benchmark": "Recognize and name common shapes, their parts, and attributes", "standardId": "GK.KE.l"}, {"ageGroup": "Kindergarten", "category": "Geometry", "benchmark": "Create and represent 3-dimensional shapes (e.g. ball/sphere, square/box/cube, tube/cylinder using various manipulative materials such as play-dough, popsicle sticks, blocks, pipe cleaners, pattern blocks)", "standardId": "GK.KE.m"}, {"ageGroup": "Kindergarten", "category": "Scientific and Engineering Practices", "benchmark": "Use senses and tools, including technology, to gather information, investigate materials, and observe processes and relationships", "standardId": "GK.KE.n"}, {"ageGroup": "Kindergarten", "category": "Scientific and Engineering Practices", "benchmark": "Make predictions about changes in materials or objects based on past experience", "standardId": "GK.KE.o"}, {"ageGroup": "Kindergarten", "category": "Scientific and Engineering Practices", "benchmark": "Ask and seek out answers to questions about objects and events with the assistance of interested adults", "standardId": "GK.KE.p"}, {"ageGroup": "Kindergarten", "category": "Physical Science", "benchmark": "Explore different kinds of matter (e.g. wood, metal, water) and describe by observing properties (e.g. visual, aural, textural)", "standardId": "GK.KE.r"}, {"ageGroup": "Kindergarten", "category": "Physical Science", "benchmark": "Explore and describe various actions that can change an object’s motion such as pulling, pushing, twisting, rolling, and throwing", "standardId": "GK.KE.s"}, {"ageGroup": "Kindergarten", "category": "Life Sciences", "benchmark": "Investigate, describe, and compare the characteristics that differentiate living from non-living things", "standardId": "GK.KE.t"}, {"ageGroup": "Kindergarten", "category": "Life Sciences", "benchmark": "Observe and describe plants and animals as they go through predictable life cycles", "standardId": "GK.KE.u"}, {"ageGroup": "Kindergarten", "category": "Life Sciences", "benchmark": "Observe and describe ways in which many plants and animals resemble their parents", "standardId": "GK.KE.v"}, {"ageGroup": "Kindergarten", "category": "Earth’s Place in the Universe", "benchmark": "Describe and anticipate weather changes", "standardId": "GK.KE.w"}, {"ageGroup": "Kindergarten", "category": "Earth’s Place in the Universe", "benchmark": "Name any celestial object seen in the day or night sky", "standardId": "GK.KE.x"}, {"ageGroup": "Kindergarten", "category": "Engineering, Technology, and Applications of Science", "benchmark": "Recognize, with assistance, examples of technologies (e.g., knife, pencil, computer, pencil sharpener, refrigerator at home or in the classroom)", "standardId": "GK.KE.y"}, {"ageGroup": "Kindergarten", "category": "History", "benchmark": "Recognize calendars and simple timelines", "standardId": "GK.KE.z"}, {"ageGroup": "Kindergarten", "category": "Geography", "benchmark": "Construct and describe simple maps of their classroom or home", "standardId": "GK.KE.aa"}, {"ageGroup": "Kindergarten", "category": "Geography", "benchmark": "Engage in activities that build understanding of words for locations and direction", "standardId": "GK.KE.bb"}, {"ageGroup": "Kindergarten", "category": "Economics", "benchmark": "Pretend to be a buyer or seller", "standardId": "GK.48-KE.cc"}, {"ageGroup": "Kindergarten", "category": "Economics", "benchmark": "Identify people’s basic needs and explain how they fulfill them", "standardId": "GK.KE.dd"}, {"ageGroup": "Kindergarten", "category": "Economics", "benchmark": "Identify buyers and sellers", "standardId": "GK.KE.ee"}, {"ageGroup": "Kindergarten", "category": "Economics", "benchmark": "Identify one or two workers and their jobs in the community", "standardId": "GK.KE.ff"}, {"ageGroup": "Kindergarten", "category": "Government/Political Science", "benchmark": "Discuss examples of rules, fairness, personal responsibilities, and authority in their own experiences and in stories read to them", "standardId": "PHM/GK.KE.q"}, {"ageGroup": "Kindergarten", "category": "Community", "benchmark": "Use self-identifying information (e.g. name, age, etc.) in situations outside the classroom", "standardId": "GK.KE.gg"}, {"ageGroup": "Kindergarten", "category": "Culture", "benchmark": "Talk about, compare, and explore similarities and differences in daily practices across cultures", "standardId": "GK.KE.hh"}, {"ageGroup": "Kindergarten", "category": "Visual", "benchmark": "Describe texture, color, and shape in artwork", "standardId": "GK.KE.ii"}, {"ageGroup": "Kindergarten", "category": "Visual", "benchmark": "Explore a variety of age-appropriate materials and media to create two and three-dimensional artwork", "standardId": "GK.KE.jj"}, {"ageGroup": "Kindergarten", "category": "Visual", "benchmark": "Express an opinion about a work of art", "standardId": "GK.KE.kk"}, {"ageGroup": "Kindergarten", "category": "Visual", "benchmark": "Explore how color can convey mood and emotion", "standardId": "GK.KE.ll"}, {"ageGroup": "Kindergarten", "category": "Musical", "benchmark": "Play instruments using different beats, tempo, dynamics, and interpretation", "standardId": "GK.KE.mm"}, {"ageGroup": "Kindergarten", "category": "Musical", "benchmark": "Sing a variety of songs with repetitive phrases and rhythmic patterns independently and with others", "standardId": "GK.KE.nn"}, {"ageGroup": "Kindergarten", "category": "Musical", "benchmark": "Sing songs varying voice and sounds (e.g. high and low, short and long, loud and soft, or fast and slow)", "standardId": "GK.KE.oo"}, {"ageGroup": "Kindergarten", "category": "Musical", "benchmark": "Identify one source of music that can be heard in daily life", "standardId": "GK.KE.pp"}, {"ageGroup": "Kindergarten", "category": "Movement", "benchmark": "Use body, energy, space, and time to move in a few different ways (change: Following adult lead, use body, energy, space, and time to move in a few different ways to match with this one)", "standardId": "GK.KE.qq"}, {"ageGroup": "Kindergarten", "category": "Movement", "benchmark": "Express self freely through movement", "standardId": "GK.KE.rr"}, {"ageGroup": "Kindergarten", "category": "Movement", "benchmark": "Create characters through physical movement, gesture, sound, speech, and facial expressions", "standardId": "GK.KE.ss"}, {"ageGroup": "Kindergarten", "category": "Dramatic Expression", "benchmark": "Talk about, compare, and explore similarities and differences in daily practices across cultures", "standardId": "GK.KE.hh"}, {"ageGroup": "Kindergarten", "category": "Dramatic Expression", "benchmark": "Develop audience skills by observing performances or artists at work in various aspects of the Arts", "standardId": "GK.KE.tt"}, {"ageGroup": "Kindergarten", "category": "Key Ideas and Details", "benchmark": "Identify story-related problems, events, and resolutions during conversations with adult", "standardId": "LA.KE.c"}, {"ageGroup": "Kindergarten", "category": "Key Ideas and Details", "benchmark": "With prompting and support, retell a simple story in sequence", "standardId": "LA.KE.g"}, {"ageGroup": "Kindergarten", "category": "Key Ideas and Details", "benchmark": "Identify characters and recall major events in a story", "standardId": "LA.KE.i"}, {"ageGroup": "Kindergarten", "category": "Craft and Structure", "benchmark": "Respond appropriately to statements, questions, vocabulary, and stories", "standardId": "LA.KE.j"}, {"ageGroup": "Kindergarten", "category": "Craft and Structure", "benchmark": "Begin to demonstrate an understanding of the differences between fantasy (make-believe) and reality", "standardId": "LA.KE.k"}, {"ageGroup": "Kindergarten", "category": "Craft and Structure", "benchmark": "Know some features of a book (title, author, illustrator)", "standardId": "LA.KE.l"}, {"ageGroup": "Kindergarten", "category": "Integration of Knowledge and Ideas", "benchmark": "Pretend to read, using intonation and referring to images in the illustrations", "standardId": "LA.KE.m"}, {"ageGroup": "Kindergarten", "category": "Integration of Knowledge and Ideas", "benchmark": "Identify story-related problems, events, and resolutions during conversations with adult", "standardId": "LA.KE.c"}, {"ageGroup": "Kindergarten", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Identify story-related problems, events, and resolutions during conversations with adult", "standardId": "LA.KE.c"}, {"ageGroup": "Kindergarten", "category": "Key Ideas and Details", "benchmark": "Identify factual information and events during conversations with adult", "standardId": "LA.KE.o"}, {"ageGroup": "Kindergarten", "category": "Craft and Structure", "benchmark": "Respond appropriately to statements, questions, vocabulary, and stories", "standardId": "LA.KE.j"}, {"ageGroup": "Kindergarten", "category": "Craft and Structure", "benchmark": "Know some features of a book (title, author, illustrator)", "standardId": "LA.KE.l"}, {"ageGroup": "Kindergarten", "category": "Integration and Knowledge of Ideas", "benchmark": "Pretend to read, using intonation and referring to images in the illustrations", "standardId": "LA.KE.m"}, {"ageGroup": "Kindergarten", "category": "Integration and Knowledge of Ideas", "benchmark": "Respond appropriately to statements, questions, vocabulary, and stories", "standardId": "LA.KE.j"}, {"ageGroup": "Kindergarten", "category": "Integration and Knowledge of Ideas", "benchmark": "Use knowledge of everyday experiences to apply to a new situation", "standardId": "AL/LA.KE.f"}, {"ageGroup": "Kindergarten", "category": "Range of Reading and Level of Text Complexity", "benchmark": "Listen actively as an individual and as a member of a group to a variety of age-appropriate informational texts read aloud", "standardId": "LA.KE.p"}, {"ageGroup": "Kindergarten", "category": "Print Concepts", "benchmark": "Practice tracking from top to bottom and left to right with scaffolding", "standardId": "LA.KE.q"}, {"ageGroup": "Kindergarten", "category": "Print Concepts", "benchmark": "Identify parts of a book (e.g. front cover, back cover, spine, etc.)", "standardId": "LA.KE.r"}, {"ageGroup": "Kindergarten", "category": "Print Concepts", "benchmark": "Show understanding that sequence of letters represents a sequence of spoken sounds (e.g. asks how to spell a word)", "standardId": "LA.KE.s"}, {"ageGroup": "Kindergarten", "category": "Print Concepts", "benchmark": "With guidance and support, segment words in a simple sentence by clapping and naming number of words in a sentence", "standardId": "LA.KE.t"}, {"ageGroup": "Kindergarten", "category": "Print Concepts", "benchmark": "Recognize and name 10 upper and lower case letters", "standardId": "LA.KE.u"}, {"ageGroup": "Kindergarten", "category": "Phonological Awareness", "benchmark": "With adult support, generate rhyming words", "standardId": "LA.KE.v"}, {"ageGroup": "Kindergarten", "category": "Phonological Awareness", "benchmark": "Clap out the syllables in own name", "standardId": "LA.KE.w"}, {"ageGroup": "Kindergarten", "category": "Phonological Awareness", "benchmark": "With guidance and support, match the initial sound of spoken words", "standardId": "LA.KE.x"}, {"ageGroup": "Kindergarten", "category": "Phonics and Word Recognition", "benchmark": "Associate 3 or more letters with their sounds", "standardId": "LA.KE.y"}, {"ageGroup": "Kindergarten", "category": "Phonics and Word Recognition", "benchmark": "Identify some letters in own name", "standardId": "LA.KE.z"}, {"ageGroup": "Kindergarten", "category": "Phonics and Word Recognition", "benchmark": "Recognize and “read” familiar words or environmental print", "standardId": "LA.KE.aa"}, {"ageGroup": "Kindergarten", "category": "Fluency", "benchmark": "Pretend to read, using intonation and referring to images in the illustrations", "standardId": "LA.KE.m"}, {"ageGroup": "Kindergarten", "category": "Text Types and Purposes", "benchmark": "Add detail to drawings and other products with simple descriptive words, letters or letter forms", "standardId": "LA.KE.bb"}, {"ageGroup": "Kindergarten", "category": "Text Types and Purposes", "benchmark": "Plan and then use drawings, constructions, movements, and dramatizations to represent ideas", "standardId": "LA.KE.cc"}, {"ageGroup": "Kindergarten", "category": "Text Types and Purposes", "benchmark": "Tell detailed stories that refer to other times and places", "standardId": "LA.KE.dd"}, {"ageGroup": "Kindergarten", "category": "Text Types and Purposes", "benchmark": "Begin to use sound spelling (e.g. use initial sound of word to write word; write several sounds heard in word)", "standardId": "LA.KE.ee"}, {"ageGroup": "Kindergarten", "category": "Production and Distribution of Writing", "benchmark": "Begin to think problems through, considering several possibilities and analyzing results", "standardId": "AL/LA.KE.d"}, {"ageGroup": "Kindergarten", "category": "Production and Distribution of Writing", "benchmark": "Experiment with a variety of writing tools and surfaces", "standardId": "AL/LA.KE.h"}, {"ageGroup": "Kindergarten", "category": "Research to Build and Present Knowledge", "benchmark": "Show eagerness to learn about a variety of topics and ideas", "standardId": "AL/LA.KE.a"}, {"ageGroup": "Kindergarten", "category": "Research to Build and Present Knowledge", "benchmark": "Use knowledge of everyday experiences to apply to a new situation", "standardId": "AL/LA.KE.f"}, {"ageGroup": "Kindergarten", "category": "Research to Build and Present Knowledge", "benchmark": "Recall 3 or 4 items removed from view", "standardId": "LA.KE.ff"}, {"ageGroup": "Kindergarten", "category": "Comprehension and Collaboration", "benchmark": "Listen and respond attentively to conversations (e.g. engaging in at least 3 exchanges, pose questions and listen to the ideas of others, share experiences when asked)", "standardId": "LA.KE.gg"}, {"ageGroup": "Kindergarten", "category": "Comprehension and Collaboration", "benchmark": "Observe and use appropriate ways of interacting in a group of 2 to 3 children (e.g. taking turns in talking, listening to peers, waiting until someone is finished, asking questions and waiting for an answer, gaining the floor in appropriate ways)", "standardId": "SE/LA.KE.b"}, {"ageGroup": "Kindergarten", "category": "Comprehension and Collaboration", "benchmark": "Respond appropriately to statements, questions, vocabulary, and stories", "standardId": "LA.KE.j"}, {"ageGroup": "Kindergarten", "category": "Comprehension and Collaboration", "benchmark": "Show eagerness to learn about a variety of topics and ideas", "standardId": "AL/LA.KE.a"}, {"ageGroup": "Kindergarten", "category": "Presentation of Knowledge and Ideas", "benchmark": "Retell experiences in order, providing details", "standardId": "AL/LA.48-KE.e"}, {"ageGroup": "Kindergarten", "category": "Presentation of Knowledge and Ideas", "benchmark": "Plan and then use drawings, constructions, movements, and dramatizations to represent ideas", "standardId": "LA.KE.cc"}, {"ageGroup": "Kindergarten", "category": "Presentation of Knowledge and Ideas", "benchmark": "Are understood by most adults and peers", "standardId": "LA.KE.hh"}, {"ageGroup": "Kindergarten", "category": "Conventions of Standard English", "benchmark": "When Speaking: Use a variety of nouns, verbs, and descriptive phrases in meaningful contexts (vocabulary)", "standardId": "LA.KE.ii"}, {"ageGroup": "Kindergarten", "category": "Conventions of Standard English", "benchmark": "When Speaking: Use a variety of sentence structures from simple to more complex in meaningful contexts (sentence structure)", "standardId": "LA.KE.jj"}, {"ageGroup": "Kindergarten", "category": "Conventions of Standard English", "benchmark": "Plan and then use drawings, constructions, movements, and dramatizations to represent ideas", "standardId": "LA.KE.cc"}, {"ageGroup": "Kindergarten", "category": "Vocabulary Acquisition and Use", "benchmark": "Describe and tell the use of many familiar items", "standardId": "LA.KE.kk"}, {"ageGroup": "Kindergarten", "category": "Vocabulary Acquisition and Use", "benchmark": "With guidance and support, use word relationships to sort, classify, and serialize (puts in a pattern) objects using attributes, such as color, shape, or size", "standardId": "LA.KE.ll"}, {"ageGroup": "Kindergarten", "category": "Vocabulary Acquisition and Use", "benchmark": "Respond appropriately to statements, questions, vocabulary, and stories", "standardId": "LA.KE.j"}, {"ageGroup": "Kindergarten", "category": "Vocabulary Acquisition and Use", "benchmark": "Recall 3 or 4 items removed from view", "standardId": "LA.KE.ff"}]