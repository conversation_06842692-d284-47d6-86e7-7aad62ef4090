import { Groups } from './groups';
import _ from '../../lib/util/underscore';
import { Mongo } from 'meteor/mongo';

const Announcement = function(doc) {
	_.extend(this, doc)
};

_.extend(Announcement.prototype, {
	sendToAll: function() {
		return (this.selectedGroups && this.selectedGroups.length == 0);
	},
	//This proto method used only in client side so no mongo call changes requires for this method
	findRecipientGroups: function() {
		return this.selectedGroups && this.selectedGroups.length > 0 && Groups.find({_id: {$in: this.selectedGroups}}).fetch();
	},
	findRecipientRoles: function() {
		return this.selectedRoles && this.selectedRoles.length > 0 && this.selectedRoles;
	}
});

export const Announcements = new Mongo.Collection('announcements', {
	transform: function(doc) {
		return new Announcement(doc);
	}
});
