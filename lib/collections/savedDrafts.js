import { Mongo } from 'meteor/mongo';
import _ from '../util/underscore';

/**
 * Structure of savedDrafts:
 * {
 *     personId: string,
 *     draftType: SavedDraftType
 *     savedTime: timestamp
 *     draftObject: any,
 *     draftName: string
 * }
 *
 * @param doc
 * @constructor
 */
global.SavedDraft = function(doc) {
	_.extend(this, doc)
};

export const SavedDrafts = new Mongo.Collection('savedDrafts', {
	transform: function(doc) {
		return new SavedDraft(doc);
	}
});
