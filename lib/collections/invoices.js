import { Meteor } from 'meteor/meteor'
import {InvoiceUpdateService} from '../invoiceUpdateService';
import { InvoiceUtils } from '../util/invoiceUtils';
import { Mongo } from 'meteor/mongo';
import { People } from './people';
import { Orgs } from './orgs';
import _ from '../../lib/util/underscore';
import { afterUpdateInvoice } from './invoiceCallbacks';
import { subsCache } from '../util/subsCache';

const moment = require('moment-timezone');

export const Invoice = function(doc) {
	_.extend(this, doc)
};

_.extend(Invoice.prototype, {
	invoiceStatus() {
		if (this.openAmount && roundToTwo(this.openAmount) > 0)
			return "open";
		else if (this.voidedAt)
			return "void";
		else
			return "paid";
	},
	personName() {
		const self = this;
		if (Meteor.isServer) {
			return (async () => {
				const person = await People.findOneAsync({ _id: self.personId });
				if (person) return person.lastName + ', ' + person.firstName;
			})();
		} else {
			subsCache.subscribe('thePeopleListByIds', { peopleIds: [this.personId] });
			const person = People.findOne({ _id: this.personId });
			if (person) return person.lastName + ', ' + person.firstName;
		}
	},
	personHasWaitListDesignation(){
		if (Meteor.isServer) {
            return (async () => {
				const person = await People.findOneAsync({_id: this.personId});
				if(person?.designations?.length){
					return person.designations.includes('Wait List');
				}
				return false;
			})();
		} else {
			const person = People.findOne({_id: this.personId});
			if(person?.designations?.length){
				return person.designations.includes('Wait List');
			}
			return false;
		}
	},
	getChargedBackAmount() {
		let sumOfChargebacks = 0.00;
		_.each(this.credits, (credit) => {
			if (credit.type == "chargeback")
				sumOfChargebacks += credit.amount;
		});
		if (sumOfChargebacks > 0) return sumOfChargebacks;
	},
	getRefundableAmount() {
		let sumOfCharges = 0.00;
		let sumOfRefunds = 0.00;
		this.credits?.forEach(credit => {
			if (credit.type === "payment")
				sumOfCharges+= credit.amount;
			else if (credit.type === "refund" || credit.type === "chargeback")
				sumOfRefunds += credit.amount;
		});
		// BUGS-2236 Javscript's floating point math is not precise.  We need to round to 2 decimal places.
		return parseFloat((sumOfCharges - sumOfRefunds).toFixed(2));
	},
	getRefundedAmount() {
		let sumOfRefunds = 0.00;
		_.each(this.credits, (credit) => {
			if (credit.type == "refund")
				sumOfRefunds += credit.amount;
		});
		if (sumOfRefunds > 0) return sumOfRefunds;
	},
	getRefundableCharges() {
		const charges = [];

		const invoice = this;
		const invoicePayments = invoice.credits.filter(credit => credit.type === "payment" && (credit.adyenInfo || credit.stripeInfo));
		const totalRefundable = invoicePayments.reduce((memo, charge) => memo + charge.amount, 0);
		invoicePayments.forEach(charge => {
				const refunds = invoice.credits.filter(credit => credit.type === "refund");
				const refundIds = refunds.map(refund => refund.originalReference);
				const chargeId = (charge.stripeInfo && charge.stripeInfo.id) || (charge.adyenInfo && charge.adyenInfo.pspReference);
				// get total refunded amount for this charge
				const refundedAmount = refunds.filter(refund => refund.originalReference === chargeId).reduce((memo, refund) => memo + refund.amount, 0);

				//instead of looking at amounts lets match refunds to charges by pspReference
				if (!refundIds.includes(chargeId) || refundedAmount < charge.amount) {
					const chargeDesc = `Payment by ${charge.paidByDesc} $${charge.amount} on ${moment(charge.createdAt).format("MM/DD/YYYY")}`;

					charges.push({
						id: chargeId,
						desc: chargeDesc,
						paidByPersonId: charge.paidBy,
						originalAmount: charge.amount,
						refundableAmount: (charge.amount - refundedAmount).toFixed(2),
					});

				}
			});

		return {
			charges,
			totalRefundable
		};
	},
	openAmountForPayer(payer) {
		const invoice = this;
		
		if (this.voidedAt) return 0;

		const creditedAmount = _.chain(invoice.credits)
			.filter( (credit) => {return credit.creditPayerSource == payer;})
			.pluck ("amount")
			.reduce ( (memo, num) => { return memo + num;}, 0.0)
			.value();
		
		const allocatedAmount = _.chain(invoice.lineItems)
			.pluck( "appliedDiscounts")
			.flatten()
			.reduce( (memo, i) => { return (i && (i.type == "reimbursable" || i.type == "reimbursable-with-copay") && i.source == payer) ? i.amount + memo : memo; }, 0.0 )
			.value();

		const reallocatedAmount = _.chain(invoice.allocationEntries)
			.filter( e => e.source == payer || e.destination == payer)
			.reduce( (memo, i) => memo + ((i.source == payer ? 1 : -1) * i.amount), 0.0)
			.value();
		
		return roundToTwo((allocatedAmount || 0) - (creditedAmount || 0.0) - (reallocatedAmount || 0.0));
	},
	originalOpenAmountForPayer(payer) {
		const invoice = this;

		if (this.voidedAt) return 0;

		const creditedAmount = _.chain(invoice.credits)
			.filter( (credit) => {return credit.creditPayerSource == payer;})
			.pluck ("amount")
			.reduce ( (memo, num) => { return memo + num;}, 0.0)
			.value();

		const allocatedAmount = _.chain(invoice.lineItems)
			.pluck( "appliedDiscounts")
			.flatten()
			.reduce((memo, i) => {
				return (i && (i.type == "reimbursable" || i.type == "reimbursable-with-copay") && i.source == payer) ? (i.originalAllocation.amount ?? (i.amount || 0)) + memo : memo;
			}, 0.0)
			.value();

		const reallocatedAmount = _.chain(invoice.allocationEntries)
			.filter( e => e.source == payer || e.destination == payer)
			.reduce( (memo, i) => memo + ((i.source == payer ? 1 : -1) * i.amount), 0.0)
			.value();

		return roundToTwo((allocatedAmount || 0) - (creditedAmount || 0.0) - (reallocatedAmount || 0.0));
	},
	totalAmountsForPayers() {
		const invoice = this;

		return _.chain(invoice.lineItems)
			.pluck( "appliedDiscounts")
			.flatten()
			.filter( (i) => { return i && (i.type=="reimbursable" || i.type=="reimbursable-with-copay" || i.type =="discount");})
			.groupBy( "source" )
			.map( (v, source) => {
				// Because it is grouped prior to this map, the type will be correct
				return {type: v[0].type, source: source, openAmount: invoice.openAmountForPayer(source), amount: v.reduce((memo, r) => memo + r.amount,0)};
			})
			.value();
		
	},
	totalOriginalAmountsForPayers() {
		const invoice = this;

		return _.chain(invoice.lineItems)
			.pluck( "appliedDiscounts")
			.flatten()
			.filter( (i) => { return i && (i.type=="reimbursable" || i.type=="reimbursable-with-copay");})
			.groupBy( "source" )
			.map((v, k) => {
				return {
					source: k,
					openAmount: invoice.originalOpenAmountForPayer(k),
					amount: v.reduce((memo, r) => memo + r.originalAllocation.amount ?? (r.amount || 0), 0)
				};
			})
			.value();

	},
	originalAmountWithPayers() {
		return this.originalAmount + _.reduce( 
			this.totalAmountsForPayers(),
			(memo, payer) => memo + payer.amount,
			0);
	},
	amountDueForFamilyMember(familyPersonId, throughDateStamp) {
		return InvoiceUtils.amountDueForFamilyMember(this, familyPersonId, throughDateStamp);
	},
	amountPaidByFamilyMember(familyPersonId, throughDateStamp) {
		return InvoiceUtils.amountPaidByFamilyMember(this, familyPersonId, throughDateStamp);
	},
	creditsForFamilyMember(familyPersonId, includeRefunds) {
		return InvoiceUtils.creditsForFamilyMember(this, familyPersonId, includeRefunds);
	},
	isVoidable() {
		if (this.voided) {
			return false;
		}
		if (!this.credits || this.credits.length === 0) {
			return true;
		}

		const filteredCredits = this.credits.filter(c => !c.voidedAt && !c.reversedAt && c.amount !== 0);
		const refundChargebacksCredits = filteredCredits.filter(c => c.type === 'refund' || c.type === 'chargeback');

		// Create a map to track cumulative refund amounts for each credit
		const cumulativeRefundAmounts = new Map();

		refundChargebacksCredits.forEach(rc => {
			const refundRef = (rc.originalReference || rc.adyenInfo.originalReference) ?? null;
			const matchedCredit = filteredCredits.find(credit =>
				credit.type === 'payment' && credit.adyenInfo?.pspReference === refundRef
			);

			if (refundRef && matchedCredit) {
				// Calculate the cumulative refund amount for the credit
				const cumulativeRefund = cumulativeRefundAmounts.get(matchedCredit) || 0;
				cumulativeRefundAmounts.set(matchedCredit, cumulativeRefund + rc.amount);

				// Check if the cumulative refund amount matches the credit's amount
				if (cumulativeRefundAmounts.get(matchedCredit) >= matchedCredit.amount) {
					// If the cumulative refund matches or exceeds the credit amount, remove the credit
					filteredCredits.splice(filteredCredits.indexOf(matchedCredit), 1);
				}
			}
		});

		// Check if all credits are refund or chargeback credits
		return refundChargebacksCredits.length === filteredCredits.length;
	},

	coversPeriodDescription() {
		let desc = "";
		_.each(this.lineItems, (li) => {
			if (li.coversPeriodDesc) 
				desc += (desc ? ", " : "") + li.coversPeriodDesc;
		});
		return desc;
	},
	lineItemDetail() {
		const invoice = this;
		const totalOriginalAmountsForPayers = _.reduce(
			this.totalOriginalAmountsForPayers(),
			(memo, payer) => memo + payer.amount,
			0);
		let lineItemList = [], balanceAmount = invoice.voidedAt ? invoice.originalAmount - totalOriginalAmountsForPayers : invoice.originalAmountWithPayers();


		lineItemList.push({
			date: invoice.createdAt,
			description: "Total invoiced amount",
			debitAmount: invoice.originalAmountWithPayers(),
			balance: invoice.originalAmountWithPayers()
		});
		_.each(invoice.credits, (c,i) => {  c.i = i; });
		_.each(invoice.allocationEntries, (ae,i) => {  ae.i = i; });
		_.each(_.filter(invoice.credits, (c) => c.type!="refund" && c.type!="chargeback" && c.creditReason != "reallocation_to_payer"), (c) => {
			let description = "";
			switch (c.creditReason || c.type) {
				case "reimbursable":
					description = "Payer - " + c.creditPayerSource + (c.creditNote ? " - " + c.creditNote : "");
					break;
				case "manual_payment": 
					description = "Manual payment" 
						+ (c.creditManualPaymentMethod ? " - " + c.creditManualPaymentMethod : "") 
						+ (c.paidByDesc ? " - " + c.paidByDesc : "")
						+ " - " + c.creditNote;
					break;
				case "payment":
					description = "Payment - " 
					switch (c.payment_type) {
						case "bank_account":
							description += "ACH";
							break;
						case "credit_memo":
							description += "Credit Memo" + ( c.creditNote ? " - " + c.creditNote : "");
							break;
						default:
							description += "Credit Card";
					
					}
					description += " - " + c.paidByDesc;
					break;
				case "security_deposit_refund":
					description = "Security deposit refund"
						+ (c.paidByDesc ? " - " + c.paidByDesc : "")
						+ " - " + c.creditNote;
					break;
				case "bad_debt":
					description = "Bad debt"
						+ (c.paidByDesc ? " - " + c.paidByDesc : "")
						+ (c.creditNote ? " - " + c.creditNote : "");
					break;
				case "payroll_deduction":
					description = "Payroll deduction"
						+ (c.paidByDesc ? " - " + c.paidByDesc : "")
						+ " - " + c.creditNote;
					break;
				case "reallocation_to_payer":
					description = "Reallocation to payer "
						+ (c.payerDestination ? " - " + c.payerDestination : "")
					break;
				case "agency_write_off":
					description = "Agency write off"
						+ (c.creditNote ? " - " + c.creditNote : "");
					break;
				case "collections_write_off":
					description = "Collections write off"
						+ (c.creditNote ? " - " + c.creditNote : "");
					break;
				default:
					description = "Other Credit - " + c.creditNote;
					if (c.hasOwnProperty("creditLineItemIndex")) {
						description += " - applied to [" + c.creditLineItemIndex + "] " + (c.creditLineItemOriginal.type == "item" ? 
							c.creditLineItemOriginal.originalItem.description :
							c.creditLineItemOriginal.description);
					}
					break;
			}
			lineItemList.push({
				i: c.i,
				date: c.createdAt,
				description: description,
				creditAmount: c.amount,
				type: c.creditReason || c.type,
				payment_type: c.payment_type,
				voidedReason: c.voidedReason,
				voidedNote: c.voidedNote,
				voidedAt: c.voidedAt,
				refundedNote: c.refundedNote,
				refundedAt: c.refundedAt,
				reversedAt: c.reversedAt,
				reversedNote: c.reversedNote,
				adjustments: c.adjustments,
				balanceAmountImpact: -1 * c.amount
			});
			
		});
		
		_.each(_.filter(invoice.credits, (c) => c.type=="refund" || c.type=="chargeback"), (c) => {
			
			const newItem = {
				i: c.i,
				date: c.createdAt,
				description: c.type.capitalizeFirstLetter() + " - " + c.creditReason + (c.creditNote ? " - " + c.creditNote : ""),
				debitAmount: c.amount,
				type: c.type,
				balanceAmountImpact: c.amount
			};
			lineItemList.push(newItem);
		});
		
		_.each(_.filter(invoice.credits, (c) => c.creditReason=="reallocation_to_payer"), (c) => {
			
			const newItem = {
				i: c.i,
				date: c.createdAt,
				description: c.type.capitalizeFirstLetter() + " - Reallocation To Payer " + c.payerDestination,
				debitAmount: c.amount,
				creditAmount: c.amount,
				type: c.creditReason,
				reversedAt: c.reversedAt,
				reversedNote: c.reversedNote,
				balanceAmountImpact: 0
			};
			lineItemList.push(newItem);
		});

		_.each(invoice.lineItems, (discount) => {
			let description = "";
				_.each(_.filter(discount.appliedDiscounts, (appliedDiscount) => appliedDiscount.type == "discount"), (appliedDiscount) => {
				description = appliedDiscount.originalAllocation ?
					(appliedDiscount.originalAllocation.allocationDescription ?
						"Applied " + appliedDiscount.originalAllocation.allocationDescription :
						(appliedDiscount.originalAllocation.description ?
							appliedDiscount.originalAllocation.description :
							'')) :
					'';


				lineItemList.push({
					date: appliedDiscount.createdAt,
					description: description,
					creditAmount: appliedDiscount.amount,
					type: appliedDiscount.type,
					voidedAt: appliedDiscount.voidedAt,
					balanceAmountImpact: (invoice.voidedAt && !appliedDiscount.voidedAt) ? balanceAmount : -appliedDiscount.amount
				});
			});
		});

		_.each(_.filter(invoice.allocationEntries, ae => ae.source != "family"), (c) => {
			const newItem = {
				ai: c.i,
				date: c.createdAt,
				creditAmount: c.amount,
				type: `reallocation_to_${c.destination}`,
				reversedAt: c.reversedAt,
				reversedNote: c.reversedNote,
				source: c.source,
				destination: c.destination,
				createdInvoice: c.createdInvoice,
				balanceAmountImpact: -1 * c.amount
			};
			if (c.destination == "family")
				newItem.description = "Reallocation to family (new invoice)";
			else if (c.destination == "bad-debt") 
				newItem.description = "Reallocation to bad debt";
			else if (c.destination == "agency") 
				newItem.description = "Reallocation to agency write-off";
			else if (c.destination == "collections") 
				newItem.description = "Reallocation to collections write-off";
			lineItemList.push(newItem);
		});
		if (invoice.voidedAt) {
			lineItemList.push({
				date: invoice.voidedAt,
				description: "Voided invoice",
				creditAmount: balanceAmount,
				balanceAmountImpact: -1 * balanceAmount
			});
		}
		return _.chain(lineItemList)
			.sortBy( c => c.date)
			.map( c => {
				balanceAmount += c.balanceAmountImpact || 0;
				c.balance = balanceAmount;
				return c;
			})
			.value();

	},
	getFamilySplits() {
		if (this.familySplits) {
			return _.isEmpty(this.familySplits) ? null : this.familySplits;
		}
	}
});

export const Invoices = new Mongo.Collection('invoices', {
	transform: function(doc) {
		return new Invoice(doc);
	}
});

_.extend(Invoices, {
	'payerDistributionOfDiscount': (options) => {
		return InvoiceUtils.payerDistributionOfDiscount(options);
	},
	async 'getPlanDetailsBySchedule'(options) {
		const {plan, reservation} = options;

		let org;
		if (Meteor.isServer) {
			org = await Orgs.findOneAsync({_id: reservation.orgId});
			moment.tz.setDefault(org.getTimezone());
		} else {
			org = Orgs.current();
		}

		const startDate = reservation.scheduledDate && new moment(reservation.scheduledDate),
			endDate = reservation.scheduledEndDate && new moment(reservation.scheduledEndDate),
			dayOfWeek = ["saturday", "friday", "thursday", "wednesday", "tuesday", "monday", "sunday"];

		let firstInvoiceDate, lastInvoiceDate;
		if (plan.frequency == "weekly" || plan.frequency == "weekly_scheduled_daily") {
			firstInvoiceDate = startDate && startDate.clone().day(0 - dayOfWeek.indexOf(org.billing.scheduling.generateDay) );
			lastInvoiceDate = endDate && endDate.clone().add(-1, "weeks").day(0 - dayOfWeek.indexOf(org.billing.scheduling.generateDay));
		} else if (plan.freqyency == "biweekly") {
			const seedDateText = org?.billing?.scheduling?.generateBiWeeklyDate;
			if (startDate && seedDateText) {
				const seedDate = new moment.tz(seedDateText, "MM/DD/YYYY", org.getTimezone()),
					duration = moment.duration(new moment(startDate).tz(org.getTimezone()).startOf("day").diff(seedDate)),
					durationAsDays = duration.asDays(), durationAsWeeks = Math.floor(duration.asWeeks()),
					nextDate = seedDate.clone().add(durationAsWeeks - (durationAsWeeks % 2), "weeks").add(durationAsDays % 14 == 0 ? 0 : 2, "weeks");
				firstInvoiceDate = nextDate.valueOf();
			}
		} else if (plan.frequency == "monthly") {
			const lastDayOfMonth = new moment().endOf("month").date()
				monthlyPlanCutoff = org.billing.scheduling.generateMonthDay > lastDayOfMonth ? lastDayOfMonth : org.billing.scheduling.generateMonthDay;
			//firstInvoiceDate = startDate && startDate.clone().day(0 - dayOfWeek.indexOf(org.billing.scheduling.generateDay) );
			//lastInvoiceDate = endDate && endDate.clone().add(-1, "weeks").day(0 - dayOfWeek.indexOf(org.billing.scheduling.generateDay));
		} else if (plan.frequency == "bimonthly") {
			//need to determine logic here
		} else if (plan.frequency == "semimonthly") {

		}
	},
	async getLineItemsWithoutVoidsForVoidedInvoices(options) {
		// This is the method called if Find Invoices is clicked...
		let queryForOrgIds = []
		if (options.orgId) {
			const org = await Orgs.findOneAsync({ _id: options.orgId });
			if (!org) {
				throw new Meteor.Error("org not found");
			}
			queryForOrgIds = [options.orgId];
			if (options.useParentOrg) {
				if (!org.parentOrgId) {
					throw new Meteor.Error("org has no parent");
				}
				// For now, get all orgIds with this parentOrgId
				const orgsList = await Orgs.find({ parentOrgId: org.parentOrgId }, { fields: { _id: 1 } }).fetchAsync();
				queryForOrgIds = _.pluck(orgsList, "_id");
			}
			if (options.useChildrenOrgs) {
				// For now, get all orgIds with this parentOrgId
				const orgsList = await Orgs.find({ parentOrgId: org._id }, { fields: { _id: 1 } }).fetchAsync()
				queryForOrgIds = _.pluck(orgsList, "_id");
			}
		} else {
			// Get all orgIds
			const allOrgs = await Orgs.find({}, { fields: { _id: 1 } }).fetchAsync()
			queryForOrgIds = _.pluck(allOrgs, "_id");
		}
		const voidedQuery = {
			orgId: { $in: queryForOrgIds },
			voided: true,
			voidedAt: { $exists: true },
			"lineItems": {
				$elemMatch: {
					$and: [
						{
							$or: [
								{ "enrolledPlan.allocations.allocationType": "reimbursable" },
								{ "enrolledPlan.allocations.allocationType": "reimbursable-with-copay" }
							]
						},
						{
							"appliedDiscounts": {
								$elemMatch: {
									"amount": { $gt: 0 }
								}
							}
						}
					]
				}
			}
		}
		const sortBy = { voidedAt: -1 };
		const voidedInvoices = await Invoices.find(voidedQuery, { sort: sortBy }).fetchAsync();
		const effectedInvoices = [];
		
		for (const invoice of voidedInvoices) {
		  // This is duplicated with _transactionsTab a bit but because it's only twice
		  // for what I can tell I'm not separating it into a helper method
		  invoice.lineItemDescriptions = [];
		  for (const li of invoice.lineItems) {
			if (li.type == "plan") {
			  invoice.lineItemDescriptions.push(li.description + " - " + li.coversPeriodDesc);
			} else {
			  invoice.lineItemDescriptions.push(li.originalItem ? li.originalItem.description : "Item charge");
			}
		  }
		  const processedInvoice = {
			personName: await invoice.personName(),
			lineItemDetail: invoice.lineItemDetail(), // This is the "Invoice Ledger" section
			...invoice
		  };
		  effectedInvoices.push(processedInvoice);
		} // I thought this would happen in the transform above, but it doesn't seem to
		return {
			effectedInvoices
		}
	},
	'updateByIdWithLog': async (id, modifier, options) => {
		return await InvoiceUpdateService.updateByIdWithLog(id, modifier, options);
	},
	// TODO: updatedMultipleInvoicesWithJournalEntry
	'updateByComplexQueryWithJournalEntry': async (query, modifier, options) => {
		// This is really similar to updateByIdWithJournalEntry, but in the future
		// we may have guards when we use a full query, or some different approach altogether.
		// TODO: Verify that this method works as execpted.
		return await InvoiceUpdateService.updateByQueryWithJournalEntry(query, modifier, options);
	},
	'updateByIdWithJournalEntry': async (id, modifier, options) => {
		return await InvoiceUpdateService.updateByQueryWithJournalEntry({
			_id: id
		}, modifier, options);
	},
});
function roundToTwo(num) {  
	const parsedNum = num.toString().indexOf("e") > 0 ? num.toFixed(20) : num;
    return +(Math.round( parsedNum + "e+2")  + "e-2");
}
if (Meteor.isServer) {

	Invoices.after.update(async function (userId, doc, fieldNames, modifier, options) {

	  if (modifier && ( modifier["$inc"] || doc.waitlistPeopleIds || _.find(doc.lineItems, li => li.type == "item" && li?.originalItem?.description?.toLowerCase()?.includes("enrollment fee")))) {
			await afterUpdateInvoice(doc, modifier);
		}

	});
}