import { CurriculumBanks } from './curriculumBank';
import _ from '../util/underscore';
import { Mongo } from 'meteor/mongo';

const CurriculumThemeBank = function(doc) {
	_.extend(this, doc)
};

_.extend(CurriculumThemeBank.prototype, {	
	findActivities() {
		return CurriculumBanks.find({_id: {$in: this.curriculumBankIds}});
	},
});

export const CurriculumThemeBanks = new Mongo.Collection('curriculumThemeBank', {
	transform: function(doc) {
		return new CurriculumThemeBank(doc);
	}
});
