import {CRM_STATUSES} from "./constants/childcaremCrmConstants";
import { People } from "./collections/people";

export class StatusService {
    static async countEnrollments(orgId, crmMode = false) {
        const query = { type: 'person', orgId, 'inActive': {$ne: true }};
        if (crmMode) {
            query.crmStatusId = CRM_STATUSES.ENROLLED;
        }
        return await People.find(query).countAsync();
    }
}