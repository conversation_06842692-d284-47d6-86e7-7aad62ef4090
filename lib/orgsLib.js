import {
    DefaultDiscountTypes,
    DefaultOrgBillingMaps, DefaultPayerSources,
    ledgerAccountTypes,
    lineItemTypes,
    SCALED_PLAN_FREQUENCIES
} from "./constants/billingConstants";
import { RegistrationDesignationUtils } from "./registrationDesignationUtils";
import { cloneDeep } from "lodash";
import { AvailableCustomizations } from './customizations';
import { BillingUtils } from './util/billingUtils';
import { DiscountTypes } from './discountTypes';
import moment from 'moment-timezone';
import { LedgerDetailServiceUtils } from './util/ledgerDetailServiceUtils';
import { Orgs } from "./collections/orgs";
import { Groups } from "./collections/groups";
import { People } from "./collections/people";
import { HOLIDAY_TYPES } from './constants/holidayConstants';

const _ = require('underscore');
const numeral = require('currency.js');

/**
 * Library for Orgs collection functions
 */
export class OrgsLib {

    /**
     * Retrieves the list of available discount types for an organization.
     *
     * @param {Object} org - The organization object containing customizations and overrides.
     * @param {boolean} showArchived - Whether to include archived discount types.
     * @param {boolean} showNoManualEntry - Whether to include discount types that cannot be manually entered.
     * @returns {Array<Object>} An array of discount type objects with `type`, `description`, and optional `noManualEntry` properties.
     */
    static availableDiscountTypes(org, showArchived, showNoManualEntry) {
        const discountTypes = [];
        if (this.orgHasCustomization(org, AvailableCustomizations.PLAN_BUNDLES) && showNoManualEntry) {
            discountTypes.push({ type: DiscountTypes.BUNDLE, description: 'Bundle', noManualEntry: true });
        }
        if (this.orgHasCustomization(org, AvailableCustomizations.COUPON_CODES) && showNoManualEntry) {
            discountTypes.push({ type: DiscountTypes.COUPON, description: 'Coupon', noManualEntry: true });
        }
        if (org.valueOverrides && org.valueOverrides.discountTypes) {
            discountTypes.push(...org.valueOverrides.discountTypes.filter(dt => showArchived || !dt.archived));
        } else {
            discountTypes.push(...DefaultDiscountTypes);
        }

        return discountTypes;
    }

    /**
     * Retrieves the list of available payer sources for an organization.
     *
     * @param {Object} org - The organization object containing payer source overrides.
     * @param {boolean} showArchived - Whether to include archived payer sources.
     * @returns {Array<Object>} An array of payer source objects, each with `type`, `description`, and optional `archived` properties.
     */
    static availablePayerSources(org, showArchived) {
        if (org.valueOverrides && org.valueOverrides.payerSources) {
            return org.valueOverrides.payerSources.filter(payerSource => showArchived || !payerSource.archived);
        } else {
            return DefaultPayerSources;
        }
    }

    /**
     * Retrieves payer source information by its type.
     *
     * @param {Object} org - The organization object containing payer source overrides.
     * @param {string} type - The type of the payer source to retrieve.
     * @returns {Object|undefined|false} The matching payer source object, `undefined` if not found, or `false` if no type is provided.
     */
    static getPayerInfoByType(org, type) {
        if (!type) {
            return false;
        }

        const payerSources = this.availablePayerSources(org, true) || [];
        return payerSources.find(p => p.type === type);
    }

    /**
     * Retrieves discount information by its type.
     *
     * @param {Object} org - The organization object containing discount type overrides.
     * @param {string} type - The type of the discount to retrieve.
     * @returns {Object|undefined|false} The matching discount type object, `undefined` if not found, or `false` if no type is provided.
     */
    static getDiscountInfoByType(org, type) {
        if (!type) {
            return false;
        }

        const discountTypes = this.availableDiscountTypes(org, true, true) || [];
        return discountTypes.find(p => p.type === type);
    }

    /**
     * Retrieves the billing maps for an organization, merging default maps with existing ones.
     *
     * @param {Object} org - The organization object containing billing data and customizations.
     * @returns {Object|undefined} An object combining default billing maps with the organization's existing billing maps, or `undefined` if conditions aren't met.
     */
    static getBillingMaps(org) {
        if (org.billing && this.orgHasCustomization(org, AvailableCustomizations.REQUIRE_LEDGER_ACCOUNT)) {
            const defaultMaps = DefaultOrgBillingMaps;
            const existingMaps = org.billing.billingMaps;
            _.each(existingMaps, (em, emName) => {
                if (defaultMaps[emName]) {
                    em.description = defaultMaps[emName].description;
                }
            });

            return _.extend(defaultMaps, existingMaps);
        }
    }

    static getMappedLedgerAccount(org, lineItemType, lineItem, modifiedDiscount ) {
        if (!org || !org.billing) {
            return;
        }

        const billingMaps = org.billing.billingMaps || this.getBillingMaps(org);

        if (!billingMaps) {
            return;
        }

        const creditHasLineItem = lineItem && lineItem.hasOwnProperty("creditLineItemIndex") && lineItem.creditLineItemOriginal;

        if (lineItem?.originalItem?.reallocationSource) {
            const payerInfo = this.getPayerInfoByType(org, lineItem.originalItem.reallocationSource.source);

            return { accountName: payerInfo?.ledgerAccountName ?? '', type: ledgerAccountTypes.REVENUE, mapped: true };
        } else if (lineItemType === lineItemTypes.CHARGE_PLAN) {
            const planInfo = _.find(org.billing.plansAndItems, (p) => { return p._id === lineItem.enrolledPlan._id; });

            if (planInfo && planInfo.ledgerAccountName) {
                return { accountName: planInfo.ledgerAccountName, type: ledgerAccountTypes.REVENUE, mapped: true };
            } else if (billingMaps.otherPlanRevenue) {
                return { accountName: billingMaps.otherPlanRevenue.accountName, type: ledgerAccountTypes.REVENUE };
            } else {
                return { accountName: "Plan Revenue", type: ledgerAccountTypes.REVENUE };
            }
        } else if (lineItemType === lineItemTypes.SECURITY_DEPOSIT_LIABILITY) {
            const accountName = (billingMaps.securityDepositsLiability && billingMaps.securityDepositsLiability.accountName) || "Security Deposit";
            const forfeitAccountName = (billingMaps.securityDepositForfeiture && billingMaps.securityDepositForfeiture.accountName) || "Security Deposit";

            return { glImportIgnore: true, accountName: accountName, forfeitAccountName: forfeitAccountName, type: ledgerAccountTypes.SECURITY_DEPOSIT_LIABILITY };
        } else if (lineItemType === lineItemTypes.SECURITY_DEPOSIT_FORFEITURE) {
            const accountName = (billingMaps.securityDepositForfeiture && billingMaps.securityDepositForfeiture.accountName) || "Security Deposit";
            const liabilityAccountName = (billingMaps.securityDepositsLiability && billingMaps.securityDepositsLiability.accountName) || "Security Deposit";

            return { glImportIgnore: true, accountName: accountName, liabilityAccountName: liabilityAccountName, type: ledgerAccountTypes.SECURITY_DEPOSIT_FORFEITURE };
        } else if (lineItemType === lineItemTypes.CHARGE_ITEM) {
            const itemInfo = _.find(org.billing.plansAndItems, (p) => { return p._id === lineItem.originalItem._id; });

            if (itemInfo && itemInfo.ledgerAccountName) {
                return { accountName: itemInfo.ledgerAccountName, type: ledgerAccountTypes.REVENUE, mapped: true };
            } else if (billingMaps.otherPlanRevenue) {
                return { accountName: billingMaps.otherItemRevenue.accountName, type: ledgerAccountTypes.REVENUE };
            } else {
                return { accountName: "Item Revenue", type: ledgerAccountTypes.REVENUE };
            }
        } else if (lineItemType === lineItemTypes.DISCOUNT_PLAN) {
            const discountInfo = this.getDiscountInfoByType(org, lineItem.source);
            const payerInfo = lineItem.type === "reimbursable" && this.getPayerInfoByType(org, lineItem.source);
            const matchedCouponCode = discountInfo?.type === "coupon" && org?.billing?.couponCodes.find(cc => cc.code === lineItem?.originalAllocation?.code);

            let discount = {};

            if (discountInfo?.ledgerAccountName) {
                discount = { accountName: discountInfo.ledgerAccountName, type: ledgerAccountTypes.DISCOUNT, mapped: true };
            } else if (lineItem.type === "reimbursable" && payerInfo?.ledgerAccountName) {
                discount = { accountName: payerInfo.ledgerAccountName, type: ledgerAccountTypes.DISCOUNT };
            } else if (lineItem.type === "reimbursable" && billingMaps.otherPayerDiscounts) {
                discount = { accountName: billingMaps.otherPayerDiscounts.accountName, type: ledgerAccountTypes.DISCOUNT };
            } else if (billingMaps.otherPlanDiscounts) {
                discount = { accountName: billingMaps.otherPlanDiscounts.accountName, type: ledgerAccountTypes.DISCOUNT };
            } else if (discountInfo?.type === "coupon" && matchedCouponCode?.ledgerCode) {
                discount = { accountName: matchedCouponCode.ledgerCode, type: ledgerAccountTypes.DISCOUNT };
            } else {
                discount = { accountName: "Plan Discounts", type: ledgerAccountTypes.DISCOUNT };
            }

            if (modifiedDiscount) {
                discount.type = ledgerAccountTypes.DISCOUNT_MODIFIED;
                discount.amountIncrease = 0;
                discount.amountDecrease = 0;

                if (lineItem.modifiedDiscount > 0) {
                    discount.amountIncrease = Math.abs(lineItem.modifiedDiscount);
                } else {
                    discount.amountDecrease = Math.abs(lineItem.modifiedDiscount);
                }
            }

            return discount;
        } else if (lineItemType === lineItemTypes.DISCOUNT_ITEM) {
            const discountInfo = this.getDiscountInfoByType(org, lineItem.source);

            if (discountInfo && discountInfo.ledgerAccountName) {
                return { accountName: discountInfo.ledgerAccountName, type: ledgerAccountTypes.DISCOUNT, mapped: true };
            } else if (billingMaps.otherItemDiscounts) {
                return { accountName: billingMaps.otherItemDiscounts.accountName, type: ledgerAccountTypes.DISCOUNT };
            } else {
                return { accountName: "Item Discounts", type: ledgerAccountTypes.DISCOUNT };
            }
        } else if (lineItemType === lineItemTypes.PAYMENT) {
            if (lineItem.payment_type === "card" || lineItem.payment_type === "bank_account") {
                return { accountName: "Uncollected Funds", type: ledgerAccountTypes.PAYMENT };
            } else {
                return { accountName: "Credit Memo Payment", type: ledgerAccountTypes.PAYMENT_CREDIT_MEMO };
            }
        } else if (lineItemType === lineItemTypes.BANK_WITHDRAWAL) {
            const accountName = (billingMaps.onlinePaymentDeposits && billingMaps.onlinePaymentDeposits.accountName) ||
                (billingMaps.manualPaymentDeposits && billingMaps.manualPaymentDeposits.accountName) ||
                "Cash";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.BANK_WITHDRAWAL };
        } else if (lineItemType === lineItemTypes.SETTLEMENT) {
            const accountName = (billingMaps.onlinePaymentDeposits && billingMaps.onlinePaymentDeposits.accountName) ||
                (billingMaps.manualPaymentDeposits && billingMaps.manualPaymentDeposits.accountName) ||
                "Cash";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYMENT_SETTLEMENT_CASH };
        } else if (lineItemType === lineItemTypes.SETTLEMENT_FEE) {
            const accountName = (billingMaps.settlementFees && billingMaps.settlementFees.accountName) || "Settlement Fees";
            return { accountName: accountName, type: ledgerAccountTypes.PAYMENT_SETTLEMENT_FEE };
        } else if (lineItemType === lineItemTypes.CHARGEBACK_FEE) {
            const accountName = (billingMaps.settlementFees && billingMaps.settlementFees.accountName) || "Chargeback Fees";
            return { accountName: accountName, type: ledgerAccountTypes.PAYMENT_SETTLEMENT_FEE };
        } else if (lineItemType === lineItemTypes.PAYMENT_REFUNDED) {
            const accountName = (billingMaps.paymentRefunds && billingMaps.paymentRefunds.accountName) || "Payment Refunds";
            return { accountName: accountName, type: ledgerAccountTypes.PAYMENT_REFUNDED };
        } else if (lineItemType === lineItemTypes.PAYMENT_VOIDED && lineItem.creditReason !== 'other') {
            const accountName = (billingMaps.manualPaymentDeposits && billingMaps.manualPaymentDeposits.accountName) || "Cash";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYMENT_VOIDED };
        } else if (lineItemType === lineItemTypes.PAYMENT_VOIDED && lineItem.creditReason === 'other') {
            if (creditHasLineItem) {
                const lineItemDef = LedgerDetailServiceUtils.getLineItemDefinitionForOtherCredits(lineItem, org)

                if (lineItemDef && lineItemDef.ledgerAccountName) {
                    return { glImportIgnore: true, accountName: lineItemDef.ledgerAccountName, type: ledgerAccountTypes.PAYMENT_VOIDED };
                }

                const accountName = (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
                return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYMENT_VOIDED };
            }

            const accountName = (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYMENT_VOIDED };
        } else if (lineItemType === lineItemTypes.MANUAL_DEPOSITS) {
            const accountName = (billingMaps.undepositedFunds && billingMaps.undepositedFunds.accountName) || "Undeposited Funds";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.MANUAL_DEPOSITS, offsetAccountName: lineItem?.cashAccountName ?? null };
        } else if (lineItemType === lineItemTypes.CREDIT || lineItemType === lineItemTypes.PAYMENT_ADJUSTED) {
            let ledger = {};

            const payerInfo = (lineItem.creditReason === "reimbursable" && this.getPayerInfoByType(org, lineItem.creditPayerSource) || (lineItem.creditReason === "reallocation_to_payer" && this.getPayerInfoByType(org, lineItem.payerDestination)));

            switch (lineItem.creditReason) {
                case "manual_payment":
                    ledger = { glImportIgnore: true, accountName: (billingMaps.undepositedFunds && billingMaps.undepositedFunds.accountName) || (billingMaps.manualPaymentDeposits && billingMaps.manualPaymentDeposits.accountName) || "Cash", type: ledgerAccountTypes.PAYMENT_MANUAL };
                    break;
                case "security_deposit_refund":
                    ledger = { accountName: (billingMaps.securityDepositRefunds && billingMaps.securityDepositRefunds.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.SECURITY_DEPOSIT_REFUND };
                    break;
                case "bad_debt":
                    ledger = { accountName: (billingMaps.badDebt && billingMaps.badDebt.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.WRITE_OFF_BAD_DEBT };
                    if (!billingMaps.badDebt) {
                        ledger.glImportIgnore = true;
                    }
                    break;
                case "agency_write_off":
                    ledger = { accountName: (billingMaps.agencyWriteOff && billingMaps.agencyWriteOff.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.WRITE_OFF_AGENCY };
                    if (!billingMaps.agencyWriteOff) {
                        ledger.glImportIgnore = true;
                    }
                    break;
                case "collections_write_off":
                    ledger = { accountName: (billingMaps.collectionsWriteOff && billingMaps.collectionsWriteOff.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.WRITE_OFF_COLLECTIONS };
                    if (!billingMaps.collectionsWriteOff) {
                        ledger.glImportIgnore = true;
                    }
                    break;
                case "payroll_deduction":
                    ledger = { accountName: (billingMaps.payrollDeduction && billingMaps.payrollDeduction.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.PAYROLL_DEDUCTION };
                    break;
                case "other":
                    if (creditHasLineItem) {
                        const lineItemDef = LedgerDetailServiceUtils.getLineItemDefinitionForOtherCredits(lineItem, org)

                        if (lineItemDef && lineItemDef.ledgerAccountName) {
                            ledger = { accountName: lineItemDef.ledgerAccountName, type: ledgerAccountTypes.DISCOUNT };
                        } else { //BUGS-2806 if no line item exists, use the default account
                            const accountName = (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
                            ledger = { accountName: accountName, type: ledgerAccountTypes.DISCOUNT };
                        }
                    } else {
                        const accountName = (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
                        ledger = { accountName: accountName, type: ledgerAccountTypes.DISCOUNT };
                    }
                    break;
                case "reimbursable":
                    if (payerInfo && payerInfo.ledgerAccountName) {
                        ledger = { accountName: payerInfo.ledgerAccountName, type: ledgerAccountTypes.PAYMENT_PAYER, offsetAccountName: payerInfo.cashLedgerAccountName ?? null };
                    } else if (billingMaps.otherPayerDiscounts) {
                        ledger = { accountName: billingMaps.otherPayerDiscounts.accountName, type: ledgerAccountTypes.PAYMENT_PAYER };
                    }
                    ledger.glImportIgnore = true;
                    break;
                case "reallocation_to_payer":
                    if (payerInfo && payerInfo.ledgerAccountName) {
                        ledger = { accountName: payerInfo.ledgerAccountName, type: ledgerAccountTypes.REALLOCATION_TO_PAYER };
                    }
                    break;
                default:
                    ledger = { accountName: "Deferred Revenue", type: ledgerAccountTypes.CREDIT };
                    break;
            }

            if (lineItemType === lineItemTypes.PAYMENT_ADJUSTED) {
                ledger.type = ledgerAccountTypes.PAYMENT_ADJUSTED;
            }

            return ledger;
        } else if (lineItemType === lineItemTypes.CREDIT_MEMO) {
            const accountName = (billingMaps.creditMemos && billingMaps.creditMemos.accountName) || "Credit Memos";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.CREDIT_MEMO };
        } else if (lineItemType === lineItemTypes.PAYROLL_DEDUCTION) {
            const accountName = (billingMaps.payrollDeduction && billingMaps.payrollDeduction.accountName) || (billingMaps.creditMemos && billingMaps.creditMemos.accountName) || "Credit Memos";
            return { accountName: accountName, type: ledgerAccountTypes.PAYROLL_DEDUCTION };
        } else if (lineItemType === lineItemTypes.SECURITY_DEPOSIT_APPLIED) {
            const accountName = (billingMaps.securityDepositsLiability && billingMaps.securityDepositsLiability.accountName) || "Security Deposits Applied";
            return { accountName: accountName, type: ledgerAccountTypes.SECURITY_DEPOSIT_APPLIED };
        } else if (lineItemType === lineItemTypes.SECURITY_DEPOSIT_REFUND_AUTO) {
            const accountName = (billingMaps.securityDepositsLiability && billingMaps.securityDepositsLiability.accountName) || "Security Deposits Refunded";
            return { accountName: accountName, type: ledgerAccountTypes.SECURITY_DEPOSIT_REFUND_AUTO };
        } else if (lineItemType === lineItemTypes.UNAPPLIED_CASH_APPLIED) {
            const accountName = (billingMaps.unappliedCashApplied && billingMaps.unappliedCashApplied.accountName) || "Unapplied Cash Applied";
            return { accountName: accountName, type: ledgerAccountTypes.UNAPPLIED_CASH_APPLIED };
        } else if (lineItemType === lineItemTypes.CREDIT_MEMO_REFUNDED) {
            const accountName = (billingMaps.customerLiabilityPayable && billingMaps.customerLiabilityPayable.accountName) || "Customer Liability/Payable";
            return { accountName: accountName, type: ledgerAccountTypes.CREDIT_MEMO_REFUNDED };
        } else if (lineItemType === lineItemTypes.CREDIT_MEMO_VOID) {
            const accountName = (billingMaps.creditMemos && billingMaps.creditMemos.accountName) || "Credit Memos";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.CREDIT_MEMO_VOID };
        } else if (lineItemType === lineItemTypes.WRITE_OFF_BAD_DEBT) {
            const accountName = (billingMaps.badDebt && billingMaps.badDebt.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
            const payerInfo = lineItem.source && this.getPayerInfoByType(org, lineItem.source);

            return {
                glImportIgnore: true,
                accountName,
                type: ledgerAccountTypes.WRITE_OFF_BAD_DEBT,
                offsetAccountName: payerInfo?.ledgerAccountName || null
            };
        } else if (lineItemType === lineItemTypes.WRITE_OFF_AGENCY) {
            const accountName = (billingMaps.agencyWriteOff && billingMaps.agencyWriteOff.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
            const payerInfo = lineItem.source && this.getPayerInfoByType(org, lineItem.source);

            return {
                glImportIgnore: true,
                accountName,
                type: ledgerAccountTypes.WRITE_OFF_AGENCY,
                offsetAccountName: payerInfo?.ledgerAccountName || null
            };
        } else if (lineItemType === lineItemTypes.WRITE_OFF_COLLECTIONS) {
            const accountName = (billingMaps.collectionsWriteOff && billingMaps.collectionsWriteOff.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
            const payerInfo = lineItem.source && this.getPayerInfoByType(org, lineItem.source);

            return {
                glImportIgnore: true,
                accountName,
                type: ledgerAccountTypes.WRITE_OFF_COLLECTIONS,
                offsetAccountName: payerInfo?.ledgerAccountName || null
            };
        } else if (lineItemType === lineItemTypes.WRITE_OFF_BAD_DEBT_REVERSAL) {
            const accountName = (billingMaps.badDebt && billingMaps.badDebt.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
            const payerInfo = lineItem.source && this.getPayerInfoByType(org, lineItem.source);

            return {
                glImportIgnore: true,
                accountName,
                type: ledgerAccountTypes.WRITE_OFF_BAD_DEBT_REVERSAL,
                offsetAccountName: payerInfo?.ledgerAccountName || null
            };
        } else if (lineItemType === lineItemTypes.WRITE_OFF_AGENCY_REVERSAL) {
            const accountName = (billingMaps.agencyWriteOff && billingMaps.agencyWriteOff.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
            const payerInfo = lineItem.source && this.getPayerInfoByType(org, lineItem.source);

            return {
                glImportIgnore: true,
                accountName,
                type: ledgerAccountTypes.WRITE_OFF_AGENCY_REVERSAL,
                offsetAccountName: payerInfo?.ledgerAccountName || null
            };
        } else if (lineItemType === lineItemTypes.WRITE_OFF_COLLECTIONS_REVERSAL) {
            const accountName = (billingMaps.collectionsWriteOff && billingMaps.collectionsWriteOff.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
            const payerInfo = lineItem.source && this.getPayerInfoByType(org, lineItem.source);
            return {
                glImportIgnore: true,
                accountName,
                type: ledgerAccountTypes.WRITE_OFF_COLLECTIONS_REVERSAL,
                offsetAccountName: payerInfo?.ledgerAccountName || null
            };
        } else if (lineItemType === lineItemTypes.REALLOCATION_TO_PAYER_REVERSAL) {
            const payerInfo = this.getPayerInfoByType(org, lineItem.payerDestination);

            return {
                glImportIgnore: true,
                accountName: payerInfo?.ledgerAccountName || "Other Credits",
                type: ledgerAccountTypes.REALLOCATION_TO_PAYER_REVERSAL
            }
        } else if (lineItemType === lineItemTypes.PAYMENT_MANUAL_REFUNDED) {
            const accountName = (billingMaps.customerLiabilityPayable && billingMaps.customerLiabilityPayable.accountName) || "Customer Liability/Payable";
            return { accountName: accountName, type: ledgerAccountTypes.PAYMENT_MANUAL_REFUNDED };
        } else if (lineItemType === lineItemTypes.PAYER_OVERPAYMENT) {
            if (lineItem.payerOverpaymentDestination === "adjustment-account") {
                const accountName = (billingMaps.agencyOverpayment && billingMaps.agencyOverpayment.accountName) || "Agency Overpayment";
                return { accountName: accountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT };
            } else if (lineItem.payerOverpaymentDestination === "agency-refund-account") {
                const accountName = (billingMaps.agencyOverpayment && billingMaps.agencyOverpayment.accountName) || "Agency Refund";
                return { accountName: accountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT };
            } else if (lineItem.payerOverpaymentDestination === "unapplied-cash-account") {
                const accountName = (billingMaps.unappliedCash && billingMaps.unappliedCash.accountName) || "Unapplied Cash";
                return { accountName: accountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT, glImportIgnore: true };
            } else if (lineItem.payerOverpaymentDestination === "payer-credit-memo") {
                const payerInfo = lineItem.creditReason === "reimbursable" && this.getPayerInfoByType(org, lineItem.creditPayerSource);
                return { glImportIgnore: true, accountName: payerInfo?.ledgerAccountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT_CREDIT_MEMO, offsetAccountName: payerInfo?.cashLedgerAccountName ?? null };
            } else if (lineItem.payerOverpaymentDestination === "family-credit-memo") {
                const accountName = (billingMaps.creditMemos && billingMaps.creditMemos.accountName) || "Credit Memos";
                return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT_CREDIT_MEMO };
            }
        } else if (lineItemType === lineItemTypes.CREDIT_MODIFIED) {
            if (creditHasLineItem) {
                const lineItemDef = LedgerDetailServiceUtils.getLineItemDefinitionForOtherCredits(lineItem, org)

                if (lineItemDef && lineItemDef.ledgerAccountName) {
                    return { accountName: lineItemDef.ledgerAccountName, type: ledgerAccountTypes.CREDIT_MODIFIED };
                }

                const accountName = (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
                return { accountName: accountName, type: ledgerAccountTypes.CREDIT_MODIFIED };
            }

            const accountName = (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
            return { accountName: accountName, type: ledgerAccountTypes.CREDIT_MODIFIED };
        }
    }

    /**
     * Checks if an organization has a specific customization enabled.
     *
     * @param {Object} org - The organization object containing customizations.
     * @param {string} customizationName - The name of the customization to check.
     * @returns {boolean} Returns `true` if the customization is enabled or matches the default enabled customizations, otherwise `false`.
     */
    static orgHasCustomization(org, customizationName) {
        if (!org || !customizationName) {
            return false;
        }

        const defaultEnabledCustomizations = [
            "moments/potty/enabled",
            "moments/sleep/enabled",
            "moments/food/enabled",
            "moments/alert/enabled"
        ];

        if (defaultEnabledCustomizations.includes(customizationName)) {
            return true;
        }

        if (customizationName === "moments/sleep/showEndSleepButton") {
            return true;
        }

        if (customizationName === "moments/learning/enabled") {
            const learningEnabled = org.customizations?.["moments/learning/enabled"];
            const curriculumHidden = org.customizations?.["modules/curriculum/hidden"];

            if (learningEnabled || (learningEnabled === undefined && (curriculumHidden === undefined || !curriculumHidden))) {
                return true;
            }
        }

        return !!org.customizations?.[customizationName];
    }

    /**
     * Get available billing plans, underlies org.availableBillingPlans()
     * @param org
     * @param date
     * @param includeScaledPricing
     * @param showArchived
     * @param requirePrograms
     * @param includeItems
     * @param doDesignationFilter
     * @param designation
     * @return {[]}
     */
    static getAvailableBillingPlans(
        org,
        date,
        includeScaledPricing = true,
        showArchived = false,
        requirePrograms = false,
        includeItems = false,
        doDesignationFilter = false,
        designation = null
    ) {
        const plans = org?.billing?.plansAndItems || [];
        const filteredPlans = cloneDeep(plans.filter((p) =>
            (includeItems ? p.type !== 'bundle' : p.type === 'plan') &&
            (!p.expires || p.expires > date) &&
            (includeScaledPricing || !SCALED_PLAN_FREQUENCIES.includes(p.frequency)) &&
            (showArchived || !p.archived) &&
            (!requirePrograms || !!p.program) &&
            (!doDesignationFilter || RegistrationDesignationUtils.planDesignationFilter(p, designation))
        ));
        filteredPlans.sort((a, b) => a.description.toLowerCase().localeCompare(b.description.toLowerCase()));
        return filteredPlans;
    }

    /**
     * Get available bundles, underlies org.availableBundles()
     * @param org
     * @param showArchived
     * @param doDesignationFilter
     * @param designation
     * @return {[]}
     */
    static getAvailableBundles(org, showArchived = false, doDesignationFilter = false, designation = null) {
        const plans = org?.billing?.plansAndItems || [];
        return plans.filter(plan => plan.type === 'bundle' &&
            (showArchived || !plan.archived) &&
            (!doDesignationFilter || RegistrationDesignationUtils.planDesignationFilter(plan, designation))
        );
    }

    /**
     * Calculates the credit card fee notice for an organization based on the amount.
     *
     * @param {Object} org - The organization object.
     * @param {number | null} [amount=null] - The payment amount to calculate the fee for.
     * @returns {string} - The calculated fee notice.
     */
    static getCardFeeNotice(org, amount = null) {
        const platformFee = OrgsLib.orgHasCustomization(org, AvailableCustomizations.BILLING_PLATFORM_FEES);
        const fees = org.billing && org.billing.paymentFees;
        const showFeeNotice = org.billing && org.billing.passthroughFees && (!org.billing.passthroughFeesAccountTypes || _.contains(org.billing.passthroughFeesAccountTypes, "card"));

        return BillingUtils.calculateFeeAmount(fees, amount, showFeeNotice, platformFee);
    }

    /**
     * Calculates the debit card fee notice for an organization based on the amount.
     *
     * @param {Object} org - The organization object.
     * @param {number | null} [amount=null] - The payment amount to calculate the fee for.
     * @returns {string} - The calculated fee notice.
     */
    static getDebitCardFeeNotice(org, amount = null) {
        const platformFee = OrgsLib.orgHasCustomization(org, AvailableCustomizations.BILLING_PLATFORM_FEES);
        const fees = org.billing && org.billing.paymentFees;
        const showFeeNotice = org.billing && org.billing.passthroughFees && (!org.billing.passthroughFeesAccountTypes || _.contains(org.billing.passthroughFeesAccountTypes, "card"));

        return BillingUtils.calculateFeeAmount({
            cardFee: fees.debitFee,
            cardRate: fees.debitRate
        }, amount, showFeeNotice, platformFee);
    }

    /**
     * Calculates the AMEX card fee notice for an organization based on the amount.
     *
     * @param {Object} org - The organization object.
     * @param {number | null} [amount=null] - The payment amount to calculate the fee for.
     * @returns {string} - The calculated fee notice.
     */
    static getAmexCardFeeNotice(org, amount = null) {
        const platformFee = OrgsLib.orgHasCustomization(org, AvailableCustomizations.BILLING_PLATFORM_FEES);
        const fees = org.billing && org.billing.paymentFees;
        const showFeeNotice = org.billing && org.billing.passthroughFees && (!org.billing.passthroughFeesAccountTypes || _.contains(org.billing.passthroughFeesAccountTypes, "card"));

        return BillingUtils.calculateFeeAmount({
            cardFee: fees.amexFee,
            cardRate: fees.amexRate
        }, amount, showFeeNotice, platformFee);
    }


    /**
     * Returns the ACH fee notice for an organization.
     *
     * @param {Object} org - The organization object.
     * @returns {string} - The ACH fee notice.
     */
    static getAchFeeNotice(org) {
        const fees = org.billing && org.billing.paymentFees;
        const showFeeNotice = org.billing && org.billing.passthroughFees && (!org.billing.passthroughFeesAccountTypes || _.contains(org.billing.passthroughFeesAccountTypes, "bank_account"));
        if (showFeeNotice) {
            if (fees && fees.achFee) {
                return numeral(fees.achFee).format("0.00");
            } else {
                return "$0.40";
            }
        }
    }

    /**
     * Gets the alternate service charge fee description for an organization.
     *
     * @param {Object} org - The organization object.
     * @returns {string} - The alternate service charge fee description.
     */
    static getAlternateServiceChargeFeeDescription(org) {
        return org?.valueOverrides?.alternateServiceChargeFeeDescription;
    }

    /**
     * Retrieves the list of schedule types for an organization, calculating overlaps and sorting times.
     *
     * @param {Object} org - The organization object containing schedule type overrides.
     * @param {Array<Object>} org.valueOverrides.scheduleTypes - The list of schedule types for the organization.
     * @returns {Array<Object>} An array of schedule type objects with the following properties:
     *   - `_id` {string} - The unique ID of the schedule type.
     *   - `startTime` {string} - The start time in "h:mm a" format.
     *   - `endTime` {string} - The end time in "h:mm a" format.
     *   - `sortStart` {string} - The start time in "HH:mm" 24-hour format for sorting.
     *   - `sortEnd` {string} - The end time in "HH:mm" 24-hour format for sorting.
     *   - `overlaps` {Array<string>} - An array of IDs of schedule types that overlap with this one.
     */
    static getScheduleTypes(org) {
        const scheduleTypes = cloneDeep(org.valueOverrides?.scheduleTypes) || [];

        const formatTimesForSorting = (schedule) => {
            if (schedule.startTime && schedule.endTime) {
                schedule.sortStart = new moment(schedule.startTime, "h:mm a").format("HH:mm");
                schedule.sortEnd = new moment(schedule.endTime, "h:mm a").format("HH:mm");
            }
        };

        const calculateOverlaps = (schedule, allSchedules) => {
            if (schedule.startTime && schedule.endTime) {
                schedule.overlaps = _.chain(allSchedules)
                    .filter((otherSchedule) =>
                            otherSchedule._id !== schedule._id && (
                                !otherSchedule.startTime ||
                                (otherSchedule.sortStart >= schedule.sortStart && otherSchedule.sortStart < schedule.sortEnd) ||
                                (otherSchedule.sortEnd > schedule.sortStart && otherSchedule.sortEnd <= schedule.sortEnd)
                            )
                    )
                    .pluck("_id")
                    .value();
            }
        };

        scheduleTypes.forEach(formatTimesForSorting);
        scheduleTypes.forEach(schedule => calculateOverlaps(schedule, scheduleTypes));

        return scheduleTypes;
    }

    static async queryDataForPrimaryFamilyAsync(queryData){
        const qPersonId = await People.findOneAsync(queryData.personId);
        const person = queryData && queryData.personId && qPersonId;
        const relatedPeople = person && await person.findOwnedRelationships({ noDuplicates: true });
        const results = [];

        for (const r of relatedPeople) {
            const p = await People.findOneAsync(r.personId);
            if (p) {
                results.push({ value: p.firstName + ' ' + p.lastName, id: p._id });
            }
        }
        return results;
    }

    /**
     * Get sorted document Definations
     *
     * @param {boolean} showArchived
     * @returns
     */
    static getDocumentDefinitions(showArchived){
        const org = Orgs.current();
        const dds = org && org.activeDocumentDefinitions(showArchived).map( def => {

                if (!def.assignmentType || def.assignmentType == "all") {
                    def.groupsDescription = "All";
                } else if (def.assignmentType == "individuals") {
                    def.groupsDescription = "Individuals";
                } else {
                    def.groupsDescription = "";
                    def.selectedGroupIds?.forEach( sg => {
                        const currentGroup = Groups.findOne(sg);
                        if (currentGroup) def.groupsDescription += (def.groupsDescription != "" ? ", " : "") + currentGroup.name;
                    });
                }
                return def;
            } );
        return _.sortBy(dds, dd => dd.section + "|" + dd.name);
    }

    /**
     * Retrieves the enabled curriculum bank IDs for a given organization.
     *
     * @param {Object} org - The organization object.
     * @param {string} org._id - The unique identifier of the organization.
     * @param {string} [org.curriculumBankId] - The optional curriculum bank ID associated with the organization.
     * @returns {string[]} An array containing the curriculum bank IDs. If the organization has the
     * `CURRICULUM_BANK_GLOBAL_AND_LOCAL` customization and a `curriculumBankId`, both the
     * `curriculumBankId` and the organization's `_id` are returned. Otherwise, a single-item array
     * containing either `curriculumBankId` (if available) or `_id` is returned.
     */
    static getEnabledCurriculumBanks(org) {
        if (OrgsLib.orgHasCustomization(org, AvailableCustomizations.CURRICULUM_BANK_GLOBAL_AND_LOCAL) && org.curriculumBankId) {
            return [org.curriculumBankId, org._id];
        } else {
            return [org.curriculumBankId || org._id];
        }
    }

    /**
     * Retrieves the list of holidays for an organization.
     * Handles both legacy (individual date) and new (individual or range) holiday formats.
     *
     * @param org - The organization whose holidays to retrieve.
     * @param allowPast - If false, filters out holidays before today's date in org's timezone.
     * @returns An array of holiday objects sorted by date (individual date or startDate).
     */
    static getHolidays(org, allowPast) {
        const timezone = org.getTimezone();
        const holidays = JSON.parse(JSON.stringify(org.valueOverrides?.holidays ?? []));
        const todayDate = moment.tz(timezone).format('YYYY-MM-DD');

        return holidays
            .filter(holiday => {
                if (holiday.deleted) {
                    return false;
                }

                if (allowPast) {
                    return true;
                }

                const type = holiday.dateType || HOLIDAY_TYPES.INDIVIDUAL;
                const relevantDate = type === HOLIDAY_TYPES.RANGE ? holiday.startDate : holiday.date;
                return relevantDate >= todayDate;
            })
            .sort((a, b) => {
                const aDate = a.dateType === HOLIDAY_TYPES.RANGE ? a.startDate : a.date;
                const bDate = b.dateType === HOLIDAY_TYPES.RANGE ? b.startDate : b.date;
                return aDate.localeCompare(bDate);
            });
    }
}
