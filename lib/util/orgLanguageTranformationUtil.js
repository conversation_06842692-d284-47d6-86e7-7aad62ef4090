import { Session } from 'meteor/session';
import { Meteor } from 'meteor/meteor';
import { translationsEnAdultCare } from '../../lib/i18nCustom/translationsEnAdultCare';
import { translationsEnChildCare } from '../../lib/i18nCustom/translationsEnChildCare';
import { translationsEnLightbridge } from '../../lib/i18nCustom/translationsEnLightbridge';
import { translationsEnMontessori } from '../../lib/i18nCustom/translationsEnMontessori';
const i18nLang = { 
	translationsEnAdultCare, translationsEnChildCare,
	translationsEnLightbridge, translationsEnMontessori 
};

/**
 * Utility functionality for language transformation for current organization
 */
export class orgLanguageTranformationUtil {

    /**
     * Gets the entityType for current organization
     *
     * @param {string} - entityType
     * @returns {string} transformed entityType for organization like Child, Staff
     */
    static getEntityType(entType) {
        const lang = JSON.parse(Session.get('g_lang'));
        if (lang.en.entityTypes && lang.en.entityTypes.hasOwnProperty(entType)) {
            return lang.en.entityTypes[entType];
        } else {
            return entType.toLowerCase() // Convert the entire string to lowercase
                .split(' ') // Split the string into words
                .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
                .join(' '); // Join the words back into a single string;
        }
    }

    /**
     * gets the Activities for current organization
     *
     * @param {string} activity
     * @returns {string} transformed activity for organization like tag, standard
     */
    static getActivities(activity) {
        const lang = JSON.parse(Session.get('g_lang'));
        if (lang.en.activities && lang.en.activities.hasOwnProperty(activity)) {
            return lang.en.activities[activity];
        } else {
            return activity.toLowerCase() // Convert the entire string to lowercase
                .split(' ') // Split the string into words
                .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
                .join(' '); // Join the words back into a single string;
        }
    }

    /**
     * gets the moment types - food for current organization
     *
     * @param {string} - momenttypes - food
     * @returns {string} transformed moment types - food for organization
     */
    static getMomentTypesFood(mType) {
        const lang = JSON.parse(Session.get('g_lang'));
        if (lang.en.hasOwnProperty("momentTypes")) {
            if (lang.en.momentTypes.food && lang.en.momentTypes.food.foodTypeLabels.hasOwnProperty(mType)) {
                return lang.en.momentTypes.food.foodTypeLabels[mType];
            } else {
                return mType.toLowerCase() // Convert the entire string to lowercase
                    .split(' ') // Split the string into words
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
                    .join(' '); // Join the words back into a single string;
            }
        } else {
            return mType.toLowerCase() // Convert the entire string to lowercase
                .split(' ') // Split the string into words
                .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word
                .join(' '); // Join the words back into a single string;
        }
    }

    /**
     * Gets the localized potty moment type string based on the provided type and language context
     * @static
     * @param {string} mType - The moment type identifier
     * @param {string} [fallbackLanguage=''] - Optional fallback language key for server-side calls
     * @returns {string} The localized string for the potty moment type
     * @throws {Error} If lang object is undefined or missing required properties
     * @example
     * // Client-side usage
     * getMomentTypesPotty('prettyName'); // Returns localized 'Potty' string
     *
     * // Server-side usage
     * getMomentTypesPotty('WetBM', 'en_US'); // Returns 'Wet+BM' using fallback language
     */
    static getMomentTypesPotty(mType, fallbackLanguage = '') {
        let lang;
        if (Meteor.isClient){
            lang = JSON.parse(Session.get('g_lang'));
        } else if (fallbackLanguage){
            lang = i18nLang[fallbackLanguage];
        }
        if (lang.en.momentTypes == undefined) {
            if (mType === 'prettyName') {
                return 'Potty';
            } else if (mType === 'WetBM') {
                return 'Wet+BM';
            } else {
                return mType;
            }

        } else {
            if (lang.en.momentTypes && lang.en.momentTypes.potty.hasOwnProperty(mType)) {
                return lang.en.momentTypes.potty[mType];
            } else if (lang.en.momentTypes && lang.en.momentTypes.potty && lang.en.momentTypes.potty.pottyTypeLabels.hasOwnProperty(mType)) {
                return lang.en.momentTypes.potty.pottyTypeLabels[mType];
            } else if (mType === 'Wet+BM') {
                return 'Wet+BM';
            }
        }
    }

    /**
     * gets the moment types - potty for current organization using select org lang
     *
     * @param {string} - momenttypes - potty
     * @returns {string} transformed moment types - potty
     */
    static getMomentTypesPottyByOrgLang(OrgLang) {
        if (OrgLang === 'translationsEnLightbridge' || OrgLang === 'translationsEnMontessori' || OrgLang === 'translationsEnAdultCare') {
            return 'Toileting';
        } else {
            return 'Potty';
        }
    }
}