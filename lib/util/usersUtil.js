import { PeopleTypes } from '../constants/peopleConstants';

/**
 * UserUtils class for handling user account operations
 */
export class UserUtils {
  /**
   * Determines if a user can see invitation status based on their role and org settings
   * @returns {boolean} Whether the user can see invite status
   */
  static canSeeInviteStatus(personType, hasCustomization) {
    const isStaffOrAdmin = [PeopleTypes.ADMIN, PeopleTypes.STAFF].includes(personType);
    if (!isStaffOrAdmin) {
      return true;
    } else {
      return !hasCustomization;
    }
  }

  /**
   * Creates a user account automatically without invitation for staff and admin users
   * @param {Object} options - Object containing all required parameters
   * @param {Object} options.personData - Data for the person being created
   * @param {string} options.newPersonId - ID of the newly created person
   * @param {Object} options.currentOrg - Current organization
   * @returns {Promise<Object>} Result object with operation status
   */
  static async createAutoAccount(options) {
    const {
      personData,
      newPersonId,
      currentOrg,
    } = options;

    try {
      // Create user without password
      const userId = await Accounts.createUserAsync({
        email: personData.emailAddress.trim().toLowerCase(),
        profile: { name: `${personData.firstName} ${personData.lastName}` }
      });

      if (!userId) {
        return { success: false, error: 'Failed to create user account' };
      }

      // Set up the user record with necessary data
      await Meteor.users.updateAsync(userId, {
        $set: {
          orgId: currentOrg._id,
          personId: newPersonId,
          pending: false,
        }
      });

      return {
        success: true,
        userId
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'Error creating auto account'
      };
    }
  }
}