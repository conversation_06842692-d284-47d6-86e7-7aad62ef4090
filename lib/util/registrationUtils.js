import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { ITEM_TYPE, PLAN_TYPE, PUNCH_CARD_TYPE, SCALED_PLAN_FREQUENCIES } from '../constants/billingConstants';
import moment from 'moment-timezone';
import { MiscUtils } from './miscUtils';
import { DiscountTypes } from "../discountTypes";
import { BillingUtils } from './billingUtils';
import { cloneDeep, _ } from 'lodash';
import { DateTimeUtils } from './dateTimeUtils';
import { parentSource } from '../constants/registrationConstants';
import { DEFAULT_PROGRAM_DAYS } from '../constants/enrollmentConstants';
import { People } from '../collections/people';
import { Invoices } from '../collections/invoices';
import { Orgs } from '../collections/orgs';
import { Reservations } from '../collections/reservations';
import { EnterprisePeople } from '../collections/enterprisePerson';
import { Relationships } from '../collections/relationships';
import { Session } from 'meteor/session';
//import { showModal } from '../../client/app/main';

export const RegistrationFlowType = {
    'PLR': 'registrationFlowStep5',
    'PROGRAM_TAB': 'addProgramsSummary'
}
Object.freeze(RegistrationFlowType);

export class RegistrationUtils {
    /**
     * Calculates the total amount due later for registration.
     * @param {object} registrationData - The registration data object.
     * @returns {string} - The total amount due later.
     */
    static dueLater(registrationData) {
        if (!registrationData) {
            return '0.00';
        }

        const planItems = registrationData.plans.flat().filter(plan => plan.type !== ITEM_TYPE && plan.type !== PUNCH_CARD_TYPE);
        if (planItems.some(plan => plan.details?.selectiveWeekAmounts?.length)) {
            const selectiveWeekAmounts = planItems.filter(plan => plan.details?.selectiveWeekAmounts?.length);
            const nonSelectWeekAmounts = planItems.filter(plan => !plan.details?.selectiveWeekAmounts?.length);
            const staticAmount = nonSelectWeekAmounts.length ? this.calculateTotalAmount(nonSelectWeekAmounts) : 0;
            const range = MiscUtils.getSelectiveWeekRange(selectiveWeekAmounts, true);

            if (!range) {
                return `${MiscUtils.formatCurrency(staticAmount)}`;
            }

            if (range.min === range.max){
                return `${MiscUtils.formatCurrency(range.min + staticAmount)}`;
            }

            return `${MiscUtils.formatCurrency(range.min + staticAmount)} - ${MiscUtils.formatCurrency(range.max + staticAmount)}`;
        }
        return `${MiscUtils.formatCurrency(this.calculateTotalAmount(planItems))}`;
    }

    /**
     * Calculates the total charges for registration.
     * @param {object} registrationData - The registration data object.
     * @param orgId
     * @param includeRegistrationFees - Whether to include registration fees in the total charges.
     * @param includeSelectiveWeeks - Whether to include selective week amounts in the total charges.
     * @returns {number} - The total charges.
     */
    static async totalCharges(registrationData, orgId = null, includeRegistrationFees = true, includeSelectiveWeeks = false) {
        if (!registrationData) {
            return 0;
        }

        const planItems = registrationData.plans.flat().filter(plan => !plan.planDetails);
        const nonSelectiveWeeksPlans = planItems.filter(plan => !plan.details?.selectiveWeekAmounts?.length);
        const regFee = registrationData?.registrationFee?.planTotal
        const newRegData = this.getNonPurchasedPlansFromSessionData(registrationData);
        let total = this.calculateTotalAmount(nonSelectiveWeeksPlans);

        if (includeSelectiveWeeks) {
            const selectiveWeeksPlans = planItems.filter(plan => plan.details?.selectiveWeekAmounts?.length);
            total += MiscUtils.getSelectiveWeekRange(selectiveWeeksPlans, true)?.max ?? 0;
        }

        if (!includeRegistrationFees) {
            return total;
        }
        const totalRegistrationFees = await this.totalRegistrationFees(newRegData, regFee, false, registrationData.contacts[0]?._id, orgId);
        return total + totalRegistrationFees.fee;
    }

    /**
     * Calculates the total range of selective week amounts from provided registration data.
     * This method first flattens the plans array within the registration data, then filters to
     * retain only those plans that contain selective week amounts in their details. If such plans
     * are found, it delegates the computation of the range of these amounts to a utility function.
     *
     * @param {Object} registrationData - The registration data containing a nested array of plan items.
     * @param {Object[]} registrationData.plans - An array of plan objects which may contain nested arrays and need flattening.
     * @returns {Object|null} Returns an object containing the minimum and maximum amounts if applicable plans are found,
     *                        otherwise returns null if no plans with selective week amounts are present.
     */
    static totalRange(registrationData) {
        if (!registrationData) {
            return null;
        }

        // Flatten the plan array and filter for plans that contain selective week amounts
        const selectiveWeekPlans = registrationData.plans.flat().filter(plan => plan.details?.selectiveWeekAmounts?.length);

        // Check if any filtered plans exist and calculate the range using a utility method
        if (selectiveWeekPlans.length) {
            return MiscUtils.getSelectiveWeekRange(selectiveWeekPlans, true);
        }

        return null;
    }

    /**
     * Calculates the total amount for an array of plans.
     * @param {object[]} plans - The array of plan objects.
     * @returns {number} - The total amount.
     */
    static calculateTotalAmount(plans) {
        if (!plans || !Array.isArray(plans)) {
            return 0;
        }

        return plans.reduce((amount, plan) => amount + plan.planTotal, 0);
    }

    /**
     * Calculates the total amount for an array of plans with selective week.
     * @param {object[]} plans - The array of plan objects.
     * @returns {number} - The total amount.
     */
    static calculateTotalAmountWithSelectiveWeek(plans) {
        if (!plans || !Array.isArray(plans)) {
            return 0;
        }

        const getPlanTotal = (plan)=> {
            if (!plan.startDate && plan.details?.selectiveWeeks?.length) {
                const earliestWeek = _.min(plan.selectedWeeks);
                const selectiveWeekAmounts = plan.details?.selectiveWeekAmounts;
                return Number(selectiveWeekAmounts[earliestWeek]);
            }
            else{
                return plan.planTotal
            }
        } 

        return plans.reduce((amount, plan) => amount + getPlanTotal(plan), 0);
    }

    /**
     * Checks if the registration is for a new registration.
     * @param {object} registrationData - The registration data object.
     * @returns {boolean} - True if it is a new registration, false otherwise.
     */
    static isNewRegistration(registrationData) {
        if (!registrationData) {
            return false;
        }

        const { contacts, children } = registrationData;
        const registeredContacts = contacts.filter(contact => contact._id).length > 0;
        const registeredChildren = children.filter(child => child._id).length > 0;
        const hasPersonId = FlowRouter.getQueryParam('personId') !== undefined;
        return !registeredContacts && !registeredChildren && !hasPersonId;
    }

    /**
     * Generates the initials for a child.
     * @param {object} child - The child object.
     * @returns {string} - The initials.
     */
    static childInitials(child) {
        if (!child) {
            return "";
        }

        if (child.firstName && child.lastName) {
            return child.firstName.charAt(0) + child.lastName.charAt(0);
        } else if (child.firstName) {
            return child.firstName.substring(0, 1);
        } else if (child.lastName) {
            return child.lastName.substring(0, 1);
        } else {
            return "";
        }
    }

    /**
     * Generates the full name for a child.
     * @param {object} child - The child object.
     * @returns {string} - The full name.
     */
    static childFullName(child) {
        if (!child) {
            return "";
        }

        if (child.firstName && child.lastName) {
            return child.firstName + ' ' + child.lastName;
        } else if (child.firstName) {
            return child.firstName;
        } else if (child.lastName) {
            return child.lastName;
        } else {
            return "";
        }
    }

    /**
     * Retrieves the program plans for a child in the registration data.
     * @param {number} childIndex - The index of the child.
     * @param {object} registrationData - The registration data object.
     * @returns {object[]} - Array of program plans for the child.
     */
    static childProgramPlans(childIndex, registrationData) {
        if (!registrationData || childIndex === undefined) {
            return [];
        }
        const childPlans = registrationData.plans[childIndex].filter(plan => !plan.planDetails) || [];
        return _.groupBy(childPlans, 'program');
    }

    /**
     * Validates coupon codes and returns codes that cannot be used with discounts.
     * @param {object[]} couponCodes - The array of coupon code objects.
     * @returns {string[]} - Array of coupon codes that cannot be used with discounts.
     */
    static validateCouponWithDiscounts(couponCodes) {
        if (!couponCodes || !couponCodes.length) {
            return [];
        }

        return couponCodes
            .filter(coupon => !coupon.usedWithDiscounts)
            .map(coupon => coupon.code);
    }

    /**
     * Checks if there are new items in the registration data.
     * @param {object} data - The registration data object.
     * @returns {boolean} - True if there are new items, false otherwise.
     */
    static checkForNewItems(data) {
        if (!data || !data.plans?.length) {
            return false;
        }

        return data.some(childPlans => childPlans.some(plan => plan.type === 'item'));
    }

    /**
     * Checks if there are new plans on hold in the registration data.
     * @param {object} data - The registration data object.
     * @returns {boolean} - True if there are new plans on hold, false otherwise.
     */
    static updateOnHold(data) {
        if (!data || !data.plans?.length) {
            return false;
        }

        const allPlans = data.plans.flat();
        const newPlans = allPlans.filter(plan => !plan.createdAt && plan.type !== 'item');
        const oldPlans = allPlans.filter(plan => plan.createdAt && plan.type !== 'item');

        const hasSubsidy = oldPlans.some(plan => {
            return plan.allocations && plan.allocations.some(allocation =>
                allocation.allocationType === 'reimbursable' || allocation.allocationType === 'reimbursable-with-copay'
            );
        });

        return newPlans.length > 0 && hasSubsidy;
    }


    /**
     * Checks if the child is enrolled in a bundle plan.
     * @param {object} child - The child object.
     * @param {object} plan - The plan object.
     * @returns {boolean} - True if enrolled in a bundle plan, false otherwise.
     */
    static isEnrolledInBundle(child, plan) {
        const { bundlePlanId } = plan;

        if (bundlePlanId) {
            const bundlePlan = this.getBundle(bundlePlanId);

            if (!bundlePlan) {
                return false;
            }

            const secondPlan = this.getSecondPlan(child, bundlePlan, plan);

            if (!secondPlan) {
                return false;
            }

            return true;
        } else {
            return false;
        }
    }

    /**
     * Gets the original amount for a plan based on the number of checked days.
     * @param {object} plan - The plan object.
     * @param {number} numCheckedDays - The number of checked days.
     * @returns {number} - The original amount.
     */
    static getOriginalAmount(plan, numCheckedDays) {
        const { planDetails } = plan;
        if (planDetails.scaledAmounts?.length) {
            return parseFloat(planDetails.scaledAmounts[numCheckedDays - 1].toFixed(2));
        } else {
            return parseFloat(planDetails.amount.toFixed(2));
        }
    }

    /**
     * Gets the discounted amount for a bundle plan.
     * @param {object} bundlePlan - The bundle plan object.
     * @param {object} secondPlan - The second plan object.
     * @param {number} numCheckedDays - The number of checked days.
     * @param {number | null} secondPlanNumCheckedDays - The number of checked days for the second plan.
     * @returns {number} - The discounted amount.
     */
    static getBundleAmount(bundlePlan, secondPlan, numCheckedDays, secondPlanNumCheckedDays = null) {
        const secondPlanIndex = bundlePlan.plans.indexOf(secondPlan._id);
        let bundlePrice = 0;

        if (secondPlanIndex < 0) {
            return bundlePrice;
        }

        let firstIndex = 0;
        let secondIndex = 0;

        if (secondPlanNumCheckedDays) {
            firstIndex = secondPlanIndex === 0 ? secondPlanNumCheckedDays - 1 : numCheckedDays - 1;
            secondIndex = secondPlanIndex === 0 ? numCheckedDays - 1 : secondPlanNumCheckedDays - 1;

            bundlePrice = this.validateIndicesAndGetAmount(bundlePlan, firstIndex, secondIndex);
        } else {
            //This is only used in client hence mongo call (Reservations) changes Not required
            const reservation = Reservations.findOne({ _id: secondPlan.reservationId });
            if (reservation && reservation.recurringDays?.length) {
                firstIndex = secondPlanIndex === 0 ? reservation.recurringDays.length - 1 : numCheckedDays - 1;
                secondIndex = secondPlanIndex === 0 ? numCheckedDays - 1 : reservation.recurringDays?.length - 1;

                bundlePrice = this.validateIndicesAndGetAmount(bundlePlan, firstIndex, secondIndex);
            }
        }

        return bundlePrice;
    }
    static validateIndicesAndGetAmount(bundlePlan, firstIndex, secondIndex) {
        const scaledAmounts = bundlePlan.scaledAmounts;

        if (
            scaledAmounts &&
            scaledAmounts.length > firstIndex &&
            scaledAmounts[firstIndex].length > secondIndex
        ) {
            return scaledAmounts[firstIndex][secondIndex] ?? 0;
        }
        return 0;
    }


    /**
     * Gets the bundle plan from the org collection.
     * @param {string} bundlePlanId - The ID of the bundle plan.
     * @returns {object|undefined} - The bundle plan object, or undefined if not found.
     */
    static getBundle(bundlePlanId, org) {
        org = org ?? Orgs.current();
        return org?.billing.plansAndItems.find(plan => plan._id === bundlePlanId);
    }

    /**
     * Gets the second plan that matches the bundle.
     * @param {object} child - The child object.
     * @param {object} bundlePlan - The bundle plan object.
     * @param {object} plan - The plan object.
     * @returns {object|undefined} - The second plan object, or undefined if not found.
     */
    static getSecondPlan(child, bundlePlan, plan) {
        const currentPlans = cloneDeep(child.billing.enrolledPlans);
        const bundlePlans = _.filter(currentPlans, ep => ep.bundlePlanId === bundlePlan._id);
        let matchedPlan = null;
        if (bundlePlans.length < 2) {
            // no op
        } else if (bundlePlans.length === 2) {
            matchedPlan = bundlePlans.find(bp => bp._id !== plan._id);
        } else if (bundlePlans.length > 2 && bundlePlans.length % 2 === 0) {
            // so long as bundle plans aren't being added one at a time, the arbitrary time elapsed check should be fine
            matchedPlan = bundlePlans.slice().reverse().find(bp => bp._id !== plan._id && (
                bp.enrollmentDate === plan.enrollmentDate ||
                bp.enrollmentForecastStartDate === plan.enrollmentForecastStartDate ||
                this.getMinutesApart(bp.createdAt, plan.createdAt) <= 1 ||
                this.getMinutesApart(bp.enrollmentDate, plan.enrollmentDate) < 10
            ));
        }
        return matchedPlan;
    }
    /**
     * Compares two arrays of string value days
     * @param {array} dayArray1 - Array of strings.
     * @param {array} dayArray2 - Second array of strings.
     * @returns {bool} - Whether the arrays are identical
     */
    static compareDays(dayArray1, dayArray2) {
        // Check if the selected days are the same as the recurring days, return early if they are the same.
        let arrayChanged = false;
        if (dayArray1.length !== dayArray2.length) {
            arrayChanged = true;
        }

        for (let i = 0; i < dayArray1.length; i++) {
            if (dayArray1[i] !== dayArray2[i]) {
                arrayChanged = true;
            }
        }
        return arrayChanged;
    }
    /**
     * Compares two unix(ms) timestamps and returns how many minutes separate them.
     * @param {number} timestamp1 - Unix timestamp in milliseconds.
     * @param {number} timestamp2 - Unix timestamp in milliseconds.
     * @returns {number} - Number of minutes between the two timestamps
     */
    static getMinutesApart(timestamp1, timestamp2) {
        const sortedTimestamps = [timestamp1, timestamp2].sort((a, b) => a - b);
        const earliestTimestamp = moment(sortedTimestamps[0]);
        const latestTimestamp = moment(sortedTimestamps[1]);
        return latestTimestamp.diff(earliestTimestamp, 'minutes');
    }

    /**
     * Calculates the total registration fees for a set of plans.
     *
     * @param {Object} regData - The registration data containing plans and children information.
     * @param {number} [registrationFee=0] - The cost of the registration fee item.
     * @param {boolean} [formatted=true] - Whether to return the total fee amount in a currency formatting.
     * @param {string|null} [familyPersonId=null] - The unique _id string of the first primary contact if available.
     * @param {string|null} [orgId=null] - The unique _id string of the organization if available.
     * @param {boolean} [detailed=false] - Whether to return a detailed breakdown of fees for each child.
     * @returns {Object} - An object containing the total fee amount, an array of unique time period ids, and optionally a detailed breakdown of fees per child.
     * @returns {number|string} returns.totalFee - The total registration fee, formatted as a string if `formatted` is true.
     * @returns {number|string} returns.fee - The total registration fee, formatted as a string if `formatted` is true (only if `detailed` is false).
     * @returns {Array} returns.timePeriodIds - An array of unique time period ids for which fees have been assessed.
     * @returns {Array<Object>} returns.childrenFees - An array of objects containing fee details for each child (only if `detailed` is true).
     * @returns {number} returns.childrenFees.childIndex - The index of the child in the original plans array.
     * @returns {number|string} returns.childrenFees.fee - The registration fee for the child, formatted as a string if `formatted` is true.
     * @returns {Array<string>} returns.childrenFees.timePeriodIds - An array of unique time period ids for which fees have been assessed for the child.
     */
    static async totalRegistrationFees(regData, registrationFee = 0, formatted = true, familyPersonId = null, orgId = null, detailed = false) {
        const returnObj = detailed ? {
            totalFee: 0,
            childrenFees: [],
            timePeriodIds: []
        } : {
            fee: 0,
            timePeriodIds: []
        };

        const plans = regData?.plans;
        const children = regData?.children || [];

        if (!plans) {
            if (detailed) {
                returnObj.totalFee = formatted ? MiscUtils.formatCurrency(registrationFee) : registrationFee;
            } else {
                returnObj.fee = formatted ? MiscUtils.formatCurrency(registrationFee) : registrationFee;
            }
            return returnObj;
        }

        const config = await this.getOrgConfig(orgId);

        if (!config || !config.feeId || !config.enabled) {
            if (detailed) {
                returnObj.totalFee = formatted ? MiscUtils.formatCurrency(0) : 0;
            } else {
                returnObj.fee = formatted ? MiscUtils.formatCurrency(0) : 0;
            }
            return returnObj;
        }

        const invoicesByChildId = await this.getInvoicesByChildId(familyPersonId, orgId, config?.feeId);
        let uniqueSelectedPlansWithTimePeriod = new Set();
        let timePeriodCount = this.getTimePeriodCountsByInvoice(invoicesByChildId);
        let totalFee = 0;
        let foundTimePeriodlessPlan = {};

        for (const [childIndex, childPlans] of plans.entries()) {
            const childId = children[childIndex]?._id;
            const { childFee, childTimePeriods } = this.calculateFeesForChild(
                { plans: childPlans, childId},
                invoicesByChildId,
                registrationFee,
                config,
                uniqueSelectedPlansWithTimePeriod,
                timePeriodCount,
                foundTimePeriodlessPlan
            );
            totalFee += childFee;

            if (detailed) {
                returnObj.childrenFees.push({
                    childIndex: childIndex,
                    fee: formatted ? MiscUtils.formatCurrency(childFee) : childFee,
                    timePeriodIds: childTimePeriods
                });
            }
        }

        if (detailed) {
            returnObj.totalFee = totalFee === 0 ? 0 : (formatted ? MiscUtils.formatCurrency(totalFee) : totalFee);
        } else {
            returnObj.fee = totalFee === 0 ? 0 : (formatted ? MiscUtils.formatCurrency(totalFee) : totalFee);
        }

        returnObj.timePeriodIds = Array.from(uniqueSelectedPlansWithTimePeriod);

        return returnObj;
    }

    static async getOrgConfig(orgId) {
        if (!orgId) return null;
        const org = await Orgs.findOneAsync({ _id: orgId });
        return org?.billing?.regFeeConfig;
    }

    static async getInvoicesByChildId(familyPersonId, orgId, feeId) {
        let invoicesByChildId = {};
        if (!familyPersonId || !orgId || !feeId) return invoicesByChildId;

        const person = await People.findOneAsync({ _id: familyPersonId });
        if (!person) return invoicesByChildId;

        const personCursor = person.findInheritedRelationshipsAsArray();
        const childrenIds = await personCursor.mapAsync((r) => r.targetId);
        const invoices = await Invoices.find(
            {
                orgId,
                voided: { $ne: true },
                personId: { $in: childrenIds },
                lineItems: { $elemMatch: { 'originalItem._id': feeId } }
            }
        ).fetchAsync();

        for (const invoice of invoices) {
            if (!invoicesByChildId[invoice.personId]) {
                invoicesByChildId[invoice.personId] = new Set();
            }
            for (const item of invoice.lineItems) {
                if (item.originalItem?._id === feeId && item.timePeriodIds?.length) {
                    item.timePeriodIds.forEach(timePeriodId => {
                        invoicesByChildId[invoice.personId].add(timePeriodId);
                    });
                }
            }
        }
        return invoicesByChildId;
    }

    static getTimePeriodCountsByInvoice(invoicesByChildId) {
        let timePeriodCount = {};
        for (const childId in invoicesByChildId) {
            invoicesByChildId[childId].forEach(timePeriodId => {
                if (!timePeriodCount[timePeriodId]) {
                    timePeriodCount[timePeriodId] = 0;
                }
                timePeriodCount[timePeriodId]++;
            });
        }
        return timePeriodCount;
    }

    static calculateFeesForChild(childPlans, invoicesByChildId, registrationFee, config, uniqueSelectedPlansWithTimePeriod, timePeriodCount, foundTimePeriodlessPlan) {
        let childFee = 0;
        let childTimePeriods = new Set();
        const childId = childPlans.childId;

        for (const plan of childPlans.plans) {
            const timePeriod = (plan?.masterPlanInfo?.details?.timePeriod || plan?.details?.timePeriod);
            const hasEnrollmentDate = plan?.enrollmentDate;

            if (plan.regFeeExempt) {
                continue;
            }

            if (hasEnrollmentDate && timePeriod) {
                // no-op
            } else if (timePeriod) {
                if (config?.perChild) {
                    if (!childTimePeriods.has(timePeriod) && (!childId || !invoicesByChildId[childId] || !invoicesByChildId[childId].has(timePeriod))) {

                        if (!timePeriodCount[timePeriod]) {
                            timePeriodCount[timePeriod] = 0;
                        }

                        timePeriodCount[timePeriod]++;

                        if (!config?.enableMaxPerFamily || (timePeriodCount[timePeriod] <= config?.maxPerFamily)) {
                            childFee += registrationFee;

                            // Only add time periods if they result in a charge so that we don't count time periods that are already covered by a previous charge.
                            childTimePeriods.add(timePeriod);
                            if (!uniqueSelectedPlansWithTimePeriod.has(timePeriod)) {
                                uniqueSelectedPlansWithTimePeriod.add(timePeriod);
                            }
                        }
                    }
                } else {
                    if (!uniqueSelectedPlansWithTimePeriod.has(timePeriod) && !Object.values(invoicesByChildId).some(set => set.has(timePeriod))) {
                        uniqueSelectedPlansWithTimePeriod.add(timePeriod);
                        childFee += registrationFee;

                        if (timePeriod && !childTimePeriods.has(timePeriod)) {
                            childTimePeriods.add(timePeriod);
                        }
                    }
                }
            } else {
                if (hasEnrollmentDate) {
                    if (!foundTimePeriodlessPlan[childId]) {
                        foundTimePeriodlessPlan[childId] = true;
                        childFee += registrationFee;
                    }
                } else {
                    childFee += registrationFee;
                }
            }
        }

        return { childFee, childTimePeriods: Array.from(childTimePeriods) };
    }

    /**
     * Calculates the registration fee for a single child.
     *
     * @param plans
     * @param childIndex
     * @param registrationFee
     * @param regFeeConfig
     * @returns {{timePeriodIds: *[], fee: number}}
     */
    static singleChildRegistrationFee(plans, childIndex, registrationFee, regFeeConfig) { // TODO: update with regFeeConfig
        const returnObj = {
            fee: 0,
            timePeriodIds: []
        }
        if (!plans || !plans[childIndex] || !plans[childIndex].length) {

        }
        const otherChildrenPlans = JSON.parse(JSON.stringify(plans));
        const selectedChildPlans = otherChildrenPlans.splice(childIndex, 1)[0];
        const otherChildrenPlansTimePeriods = [];
        let chosenTimePeriods = [];
        const enrolledTimePeriods = [];

        function hasTimePeriod(plan) {
            return plan?.masterPlanInfo?.details?.timePeriod || plan?.details?.timePeriod;
        }

        const otherChildrenPlansTimePeriodsFlat = [].concat.apply([], otherChildrenPlans.slice(0, childIndex));

        for (const x in otherChildrenPlansTimePeriodsFlat) {
            const timePeriod = hasTimePeriod(otherChildrenPlansTimePeriodsFlat[x]);
            if (timePeriod) {
                otherChildrenPlansTimePeriods.push(timePeriod);
            }
        }

        for (const y in selectedChildPlans) {
            const plan = selectedChildPlans[y];
            const timePeriod = hasTimePeriod(plan);
            const hasEnrollmentDate = plan?.enrollmentDate;
            const isAlreadyChosen = chosenTimePeriods.includes(timePeriod);

            if (plan.regFeeExempt) {
                continue;
            }

            if (hasEnrollmentDate) {
                enrolledTimePeriods.push(timePeriod);
                continue;
            }

            // Registration fee is charged to plans with no time period
            if (!timePeriod) {
                chosenTimePeriods.push('I am but a string. Leave me be.');
            }

            if (timePeriod && !isAlreadyChosen) {
                chosenTimePeriods.push(timePeriod);
            }

        }

        // Remove time periods that exist in another childs plans, or within enrolled plans
        chosenTimePeriods = chosenTimePeriods.filter((el) => !otherChildrenPlansTimePeriods.includes(el));
        chosenTimePeriods = chosenTimePeriods.filter((el) => !enrolledTimePeriods.includes(el));

        returnObj.fee = chosenTimePeriods.length * registrationFee;
        returnObj.timePeriodIds = chosenTimePeriods;

        return returnObj;
    }

    /**
     * Checks if a single-use coupon has been used in the provided registration plan data.
     * @param {Object} coupon - The coupon to check.
     * @param {Object[]} registrationPlanData - An array of registration plan data objects.
     * @param {Object[]} registrationPlanData[].allocations - An array of allocations for a plan.
     * @param {string} registrationPlanData[].allocations[].couponCode - The coupon code in the allocation.
     * @returns {boolean} Returns true if the coupon has been used, false otherwise.
     */
    static singleUseCouponUsed(coupon, registrationPlanData) {
        return registrationPlanData.some(childPlans =>
            childPlans.some(plan =>
                plan.allocations?.some(allocation =>
                    allocation.code === coupon.code
                )
            )
        );
    }

    /**
     * Gets the youngest child from the provided children array.
     * @param {Object[]} children - An array of child objects.
     * @param {string} children[].birthday - The birthday of the child in 'MM/DD/YYYY' format.
     * @returns {Object} An object containing the youngest child and its index in the array.
     */
    static getYoungestChild(children) {
        let youngestChild = children[0];
        let youngestChildIdx = 0;
        let newestTimestamp = moment(youngestChild.birthday, 'MM/DD/YYYY').valueOf();

        for (let i = 1; i < children.length; i++) {
            const currentChild = children[i];
            const currentTimestamp = moment(currentChild.birthday, 'MM/DD/YYYY').valueOf();

            if (currentTimestamp > newestTimestamp) {
                youngestChild = currentChild;
                youngestChildIdx = i;
                newestTimestamp = currentTimestamp;
            }
        }

        return {
            child: youngestChild,
            index: youngestChildIdx
        };
    }

    /**
     * Applies a coupon allocation to the appropriate plans based on various criteria, such as child age,
     * plan type, and whether the plan is part of a bundle. If the error code 'MR-000005' is present in
     * the error details, plans with sibling discounts are skipped.
     *
     * @param {Array} children - An array of child objects, where each object contains information about a child.
     * @param {Array} plans - A 2D array of plan objects associated with the children. Each sub-array corresponds to a child's plans.
     * @param {Object} coupon - The coupon object containing details about the discount to be applied.
     * @param {Object} allocation - The allocation object that contains details about the discount to be applied to each plan.
     * @param {boolean} [update=false] - A flag indicating whether existing allocations should be updated or new ones created.
     * @param {Object} [errorDetails={}] - An optional object containing error details, used to modify behavior based on specific error codes.
     * @param {string} [errorDetails.errorCode] - The specific error code that may affect how the function processes certain plans.
     */
    static addAllocationCouponDiscount(children, plans, coupon, allocation, update = false, errorDetails = {}) {
        const oneTimeUse = coupon.isSingleInstallmentCoupon || false;
        const youngestChild = RegistrationUtils.getYoungestChild(children);
        let applied = oneTimeUse ? RegistrationUtils.singleUseCouponUsed(coupon, plans) : false; // Check if coupon has already been applied.

        if (coupon.expirationDate) {
            allocation.discountExpires = coupon.expirationDate;
        }

        // Only run this block if the coupon is a one-time-use coupon
        if (oneTimeUse && !applied) {
            // Check if the coupon doesn't allow stacking with discounts
            if (!coupon.usedWithDiscounts) {
                // Iterate over the children to find one that doesn't have any discount
                for (const [childIndex, childPlans] of plans.entries()) {
                    // Check if the child has no discounts applied to any plan
                    const childHasNoDiscounts = childPlans.every(plan =>
                        !plan.allocations || !plan.allocations.some(allocation => allocation.discountType !== DiscountTypes.SIBLING)
                    );

                    if (childHasNoDiscounts) {
                        // Apply the coupon to one of their plans
                        for (const plan of childPlans) {
                            if (applied) break; // Stop once coupon is applied

                            if (plan.enrollmentDate || this.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, coupon)) {
                                continue;
                            }

                            this.addCouponAllocationToPlanOrItem(plan, childPlans, coupon, allocation, update);
                            applied = true;
                        }
                        if (applied) break; // Stop once a child without discounts is found and coupon is applied
                    }
                }
            }

            // Fallback to applying to the youngest child if not already applied
            if (!applied && youngestChild) {
                for (const plan of plans[youngestChild.index]) {
                    if (applied) break;

                    if (plan.enrollmentDate || this.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, coupon)) {
                        continue;
                    }

                    this.addCouponAllocationToPlanOrItem(plan, plans[youngestChild.index], coupon, allocation, update);
                    applied = true;
                }
            }
        }

        for (const childPlans of plans) {
            for (const plan of childPlans) {
                // Skip if the error code is 'MR-000005' and plan has siblingDiscount
                if (errorDetails.errorCode === 'MR-000005' && plan.allocations && plan.allocations.some(allocation => allocation.discountType === 'siblingDiscount')) {
                    continue;
                }

                // Skip adding the coupon if the plan already has a sibling discount and the coupon doesn't allow discounts
                if (plan.allocations && plan.allocations.some(allocation => allocation.discountType === 'siblingDiscount') && !coupon.usedWithDiscounts) {
                    continue;
                }

                if (oneTimeUse && applied) continue;
                if (oneTimeUse) {
                    allocation.isSingleUse = true;
                    allocation.used = false;
                    applied = true;
                }
                const used = coupon.isSingleInstallmentCoupon ? this.singleUseCouponUsed(coupon, plans) : null;

                // Don't add coupons to existing plans.
                if (plan.enrollmentDate || (oneTimeUse && this.isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, coupon))) {
                    continue;
                }

                if ('bundlePlanId' in plan && this.checkIfPlanIsInBundle(plans, plan)) {
                    const useCouponInBundlesType = coupon?.useCouponInBundles || 'yes-all';
                    const bundledPlanIdToUpdate = this.planToUpdateBasedOnCouponSetting(useCouponInBundlesType, plans);
                    if (bundledPlanIdToUpdate === 'none') continue;
                    // Not the plan to add the bundle to
                    if (![plan._id, 'all'].includes(bundledPlanIdToUpdate)) continue;
                    plan.allocations = plan.allocations || [];
                    this.addCouponAllocationToPlanOrItem(plan, childPlans, coupon, allocation, update, used);
                } else {
                    this.addCouponAllocationToPlanOrItem(plan, childPlans, coupon, allocation, update, used);
                }
            }
        }
    }

    /**
     * Checks if a plan is excluded from both billing plans and one-time charges in a coupon
     * @param {Object} plan - The plan to check
     * @param {string} plan._id - The unique identifier of the plan
     * @param {Object} coupon - The coupon to check against
     * @param {Array<string>} [coupon.billingPlans] - List of plan IDs that the coupon can be applied to
     * @param {Array<string>} [coupon.oneTimeCharges] - List of one-time charge IDs that the coupon can be applied to
     * @returns {boolean} True if the plan is not included in either the coupon's billing plans or one-time charges lists when those lists exist
     * @static
     */
    static isPlanNotInCouponsBillingPlansOrOneTimeCharges(plan, coupon) {
        if (!coupon || !plan) {
            return false;
        }

        let isRestrictedToBillingPlans = false;
        let isRestrictedToOneTimeCharges = false;

        if (coupon.billingPlans && Array.isArray(coupon.billingPlans) && coupon.billingPlans.length > 0) {
            isRestrictedToBillingPlans = !coupon.billingPlans.includes(plan._id);
        }

        if (coupon.oneTimeCharges && Array.isArray(coupon.oneTimeCharges) && coupon.oneTimeCharges.length > 0) {
            isRestrictedToOneTimeCharges = !coupon.oneTimeCharges.includes(plan._id);
        }

        return isRestrictedToBillingPlans && isRestrictedToOneTimeCharges;
    }

    /**
     * Applies a coupon to the registration fee by adding an allocation to the fee if applicable.
     *
     * @param {Object} registrationFee - The registration fee object to which the coupon will be applied.
     * @param {string} registrationFee._id - The unique identifier of the registration fee.
     * @param {Array<Object>} [registrationFee.allocations] - An optional array of allocations already applied to the registration fee.
     * @param {Object} coupon - The coupon object containing the discount details.
     * @param {boolean} coupon.isSingleInstallmentCoupon - Indicates if the coupon is a single installment coupon.
     * @param {Array<string>} [coupon.oneTimeCharges] - An optional array of registration fee IDs that the coupon can be applied to as a one-time charge.
     * @param {Object} allocation - The allocation object representing the discount to be applied.
     */
    static applyCouponToRegistrationFee(registrationFee, coupon, allocation) {
        if (registrationFee && !coupon.isSingleInstallmentCoupon) {
            registrationFee.allocations = registrationFee.allocations || [];
            if (coupon.oneTimeCharges && coupon.oneTimeCharges.length) {
                if (coupon.oneTimeCharges.includes(registrationFee._id)) {
                    registrationFee.allocations.push(_.clone(allocation));
                }
            } else {
                registrationFee.allocations.push(_.clone(allocation));
            }
        }
    }

    /**
     * Applies all coupons from appliedCoupons to the registration fee.
     *
     * @param {Object} registrationFee - The registration fee object to which the coupons will be applied.
     * @param {Array<Object>} appliedCoupons - The array of applied coupons to be used for allocations.
     */
    static applyNewCouponsToRegistrationFee(registrationFee, appliedCoupons) {
        if (registrationFee) {
            // Wipe existing allocations
            registrationFee.allocations = [];

            // Loop over appliedCoupons and apply each one to the registration fee
            appliedCoupons.forEach(coupon => {
                const allocation = {
                    id: Random.id(),
                    allocationType: "discount",
                    amount: coupon.amount,
                    amountType: coupon.amountType,
                    discountType: DiscountTypes.COUPON,
                    code: coupon.code,
                    allocationDescription: coupon.description || `Discount: Coupon Code ${coupon.code}`
                };

                RegistrationUtils.applyCouponToRegistrationFee(registrationFee, coupon, allocation);
            });
        }
    }


    /**
     * Creates an allocation object based on the provided coupon details.
     *
     * @param {Object} coupon - The coupon object containing the discount details.
     * @param {string} coupon.amount - The discount amount provided by the coupon.
     * @param {string} coupon.amountType - The type of the discount amount (e.g., percentage or fixed amount).
     * @param {string} coupon.code - The unique code for the coupon.
     * @param {string} [coupon.description] - An optional description for the coupon.
     * @param {Date} [coupon.expirationDate] - An optional expiration date for the coupon.
     * @returns {Object} - The allocation object representing the discount to be applied.
     */
    static createAllocationObjectFromCoupon(coupon) {
        const allocation = {
            id: Random.id(),
            allocationType: "discount",
            amount: coupon.amount,
            amountType: coupon.amountType,
            discountType: DiscountTypes.COUPON,
            code: coupon.code,
            allocationDescription: coupon.description || `Discount: Coupon Code ${coupon.code}`
        }

        if (coupon.expirationDate) {
            allocation.discountExpires = coupon.expirationDate;
        }

        if (coupon.isSingleInstallmentCoupon) {
            allocation.isSingleUse = true;
            allocation.used = false;
        }

        return allocation;
    }

    /**
     * Checks if a given coupon was applied to any of the plans or the registration fee in the provided data.
     *
     * @param {Object} data - The data object containing an array of plans and possibly a registration fee.
     * @param {Array<Object>} data.plans - An array of plan objects, where each plan contains allocations.
     * @param {Object} [data.registrationFee] - An optional registration fee object that may contain allocations.
     * @param {Array<Object>} [data.registrationFee.allocations] - An array of allocation objects associated with the registration fee.
     * @param {Object} coupon - The coupon object containing a code to be checked against plan and registration fee allocations.
     * @param {string} coupon.code - The coupon code to check for in the plan and registration fee allocations.
     * @param {boolean} [fromDIY=false] - Indicates whether the coupon was applied from the DIY registration form.
     * @returns {boolean} - Returns true if the coupon was applied to any of the plans or the registration fee; otherwise, false.
     */
    static wasCouponApplied(data, coupon, fromDIY = false) {
        // Lets remove existing plans from the check if this is from DIY flow.
        const plans = fromDIY ? data.plans.flat().filter(plan => !plan.enrollmentDate) : data.plans.flat();
        const appliedToPlans = plans.some(plan => plan.allocations?.some(a => a.code === coupon.code));
        let appliedToRegFee = false;
        if (data.registrationFee?.allocations?.some(a => a.code === coupon.code)) {
            appliedToRegFee = true;
        }

        return appliedToPlans || appliedToRegFee;
    }

    /**
     * Adds a coupon allocation to the specified plan or item within child plans.
     *
     * @param {Object} plan - Plan or item object.
     * @param {Array} childPlans - Array of child plans containing the plan or item.
     * @param {Object} coupon - Coupon object.
     * @param {Object} allocation - Allocation object to be added.
     * @param {boolean} update - Whether to update existing allocations.
     * @param used - Whether the coupon has already been used.
     *
     * @returns {void}
     */
    static addCouponAllocationToPlanOrItem(plan, childPlans, coupon, allocation, update, used) {
        if (!childPlans) {
            return;
        }
        if (coupon.billingPlans && coupon.billingPlans.length) {
            if (plan.bundlePlanId) {
                const otherPlan = childPlans.find(p => p.bundlePlanId === plan.bundlePlanId && p._id !== plan._id);
                if (otherPlan && (!coupon.billingPlans.includes(otherPlan._id) || !coupon.billingPlans.includes(plan._id))) {
                    if (otherPlan.allocations && otherPlan.allocations.length && update) {
                        otherPlan.allocations = otherPlan.allocations.filter(a => !a.code || a.code !== coupon.code);
                    }
                    return;
                }
            }

            if (!coupon.billingPlans.includes(plan._id) && plan.type === PLAN_TYPE) {
                return;
            }
        }
        if (coupon.oneTimeCharges && coupon.oneTimeCharges.length && (plan.type === ITEM_TYPE || plan.type === PUNCH_CARD_TYPE)) {
            if (!coupon.oneTimeCharges.includes(plan._id)) {
                return;
            }
        }

        ('allocations' in plan) || (plan.allocations=[]) // Create allocations array, if doesn't exist
        // Coupon Allocation already exists on plan
        if (plan?.allocations.find(a => a.discountType === DiscountTypes.COUPON && a.code === allocation.code)) {
            return;
        }

        if (coupon.isSingleInstallmentCoupon && !used) {
            return plan.allocations.push(_.clone(allocation));
        } else if (!coupon.isSingleInstallmentCoupon) {
            return plan.allocations.push(_.clone(allocation));
        }
    }

    /**
     * Removes coupon allocations from the specified plan.
     *
     * @param {Object} plan - Plan or item object.
     * @param {Object} coupon - Coupon object.
     *
     * @returns {Object} - Updated plan object after removing coupon allocations.
     */
    static removeCouponAllocationsFromPlan(plan, coupon){
        if (!plan?.allocations) return plan
        plan.allocations = plan.allocations.filter((allocation) => {
            return !(allocation.discountType === DiscountTypes.COUPON && allocation.code === coupon.code);
        });

        return plan;
    }

    /**
     * Determines the plan to update based on the coupon setting.
     *
     * @param {string} type - Coupon setting type ('yes-all', 'yes-most', 'yes-least', 'no').
     * @param {Array} allPlans - Array of all plans.
     *
     * @returns {string} - Plan ID or 'none' based on the coupon setting.
     */
    static planToUpdateBasedOnCouponSetting(type, allPlans){
        switch(type){
            case 'yes-all':
                return 'all';
            case 'yes-most':
                return this.getMostExpensivePlanIdInBundles(allPlans) || 'none';
            case 'yes-least':
                return this.getLeastExpensivePlanIdInBundles(allPlans) || 'none';
            case 'no':
                return 'none';
        }
    }

    /**
     * Retrieves the ID of the most expensive plan within bundled plans.
     *
     * @param {Array} allPlans - Array of all plans.
     *
     * @returns {string|null} - ID of the most expensive plan or null if no bundled plans.
     */
    static getMostExpensivePlanIdInBundles(allPlans){
        const bundledPlans = this.getBundledPlans(allPlans);
        const mostExpensiveBundle = bundledPlans.sort((a, b) => b.amount - a.amount)[0];
        return mostExpensiveBundle?._id;
    }

    /**
     * Retrieves the ID of the least expensive plan within bundled plans.
     *
     * @param {Array} allPlans - Array of all plans.
     *
     * @returns {string|null} - ID of the least expensive plan or null if no bundled plans.
     */
    static getLeastExpensivePlanIdInBundles(allPlans){
        const bundledPlans = this.getBundledPlans(allPlans);
        const leastExpensiveBundle = bundledPlans.reduce((prev, curr) => prev.amount < curr.amount ? prev : curr);
        return leastExpensiveBundle?._id;
    }

    /**
     * Retrieves an array of plans that are part of a bundle.
     *
     * @param {Array} allPlans - Array of all plans.
     *
     * @returns {Array} - Array of plans that are part of a bundle.
     */
    static getBundledPlans(allPlans){
        const bundledPlans = [];
        for (const childPlans of allPlans) {
            for (const plan of childPlans) {
                if ('bundlePlanId' in plan) {
                    bundledPlans.push(plan);
                }
            }
        }
        return bundledPlans;
    }

    /**
     * Retrieves the total count of plans across all child plans.
     *
     * @param {Array} allPlans - Array of all plans.
     *
     * @returns {number} - Total count of plans.
     */
    static getTotalPlansCount(allPlans){
        let plansCount = 0;
        for (const childPlans of allPlans) {
            plansCount = plansCount + childPlans.length
        }
        return plansCount;
    }

    /**
     * Checks if a selected plan is part of a bundle within all plans.
     *
     * @param {Array} allPlans - Array of all plans.
     * @param {Object} selectedPlan - Selected plan to check for bundle association.
     *
     * @returns {boolean} - True if the plan is part of a bundle, false otherwise.
     */
    static checkIfPlanIsInBundle(allPlans, selectedPlan) {
        if(!(selectedPlan?.bundlePlanId)) return false
        const flattenedPlans = Array.prototype.concat.apply([], allPlans);
        return flattenedPlans.some(plan => plan.bundlePlanId === selectedPlan.bundlePlanId)
    }

    /**
     * Finds the newest element in the array based on the 'addedStamp' property.
     *
     * @param {Array} array - The array of objects to search for the newest element.
     * @returns {Object|null} The newest element object or null if the input array is empty or not an array.
     */
    static findNewestElement(array) {
        if (!Array.isArray(array) || array.length === 0) {
            return null; // Return null if the input is not an array or is empty
        }

        let newestElement = array[0];
        let newestTimestamp = newestElement.addedStamp;

        for (let i = 1; i < array.length; i++) {
            const currentElement = array[i];
            const currentTimestamp = currentElement.addedStamp;

            if (currentTimestamp > newestTimestamp) {
                newestElement = currentElement;
                newestTimestamp = currentTimestamp;
            }
        }

        return newestElement;
    }

    /**
     * Apply discounts to a plan used in the registration process.
     *
     * @param plan
     * @param topLinePercentDiscounts
     */
    static applyDiscountsToPlan(plan, topLinePercentDiscounts) {
        let totalDiscounts = 0;
        plan.planTotal = plan.amount
        if (!plan.allocations || !plan.allocations.length || !Array.isArray(plan.allocations)) {
            return;
        }
        const sumOfDollarDiscounts = _.chain(plan.allocations)
            .filter((a) => {
                return a.amountType === "dollars";
            })
            .reduce((memo, a) => {
                return memo + a.amount;
            }, 0.0)
            .value();
        for (const allocation of plan.allocations) {
            let discountAmount = 0;
            if (allocation.amountType === "dollars") {
                discountAmount = allocation.amount;
            } else {
                discountAmount = BillingUtils.roundToTwo((plan.amount - (topLinePercentDiscounts ? 0 : sumOfDollarDiscounts)) * allocation.amount / 100.0);
            }
            if (discountAmount < 0) discountAmount = 0;
            allocation.discountAmount = discountAmount;
            totalDiscounts += discountAmount;
        }
        const totalDiscountedAmount = BillingUtils.roundToTwo(totalDiscounts);
        if (totalDiscountedAmount > plan.planTotal) {
            plan.planTotal = 0;
        } else {
            plan.planTotal -= totalDiscountedAmount;
        }
    }

    /**
     * Sets the primary contact for a specific child by updating the provided Set.
     *
     * @param {Array} contacts - Array of contacts.
     * @param {Set} primaryContact - Set to update with the indices of primary contacts.
     * @param {number} childIndex - Index of the child for whom the primary contact is set.
     */
    static setPrimaryContact(contacts, primaryContact, childIndex){
        const childContacts = this.getChildContacts(contacts, childIndex);
        const primaryContactSet = new Set();
        childContacts.forEach((contact, index) => {
            if (contact.primaryCaregiver === 'Yes') {
                primaryContactSet.add(index);
            }
        });

        primaryContact.set(primaryContactSet);
    }

    /**
     * Adds or updates a contact in the contacts array based on the provided parameters.
     *
     * @param {Array} contacts - Array of contacts.
     * @param {string} contactEmailAddress - Email address of the contact.
     * @param {number} currentChildIndex - Index of the current child.
     * @param {string} contactType - Type of contact to add or update.
     * @param {string} contactTypeValue - Value of the contact type.
     *
     * @returns {Array} - Updated array of contacts.
     */
    static addOrUpdateContact(contacts, contactEmailAddress, currentChildIndex, contactType, contactTypeValue) {
        const matchingContact = this.findMatchingContactByEmailAndIndex( contacts, contactEmailAddress, currentChildIndex);
        if (!matchingContact) {
            return this.createNewContact(contacts, contactEmailAddress, currentChildIndex, contactType, contactTypeValue);
        }

        return this.updateContact(contacts,
            contactEmailAddress,
            currentChildIndex,
            contactType,
            contactTypeValue
        );

    }

    /**
     * Finds the index of a contact in the contacts array that matches the provided email and child index.
     *
     * @param {Array} contacts - Array of contacts.
     * @param {string} contactEmail - Email address of the contact.
     * @param {number} currentChildIndex - Index of the current child.
     *
     * @returns {number} - Index of the matching contact or -1 if not found.
     */
    static findIndexOfMatchingContactByEmailAndIndex(contacts, contactEmail, currentChildIndex) {
        if(!contacts) return -1
        return contacts.findIndex(contact => {
            return (contactEmail === contact.profileEmailAddress
                && currentChildIndex === contact.childIndex);
        });
    }

    /**
     * Finds a contact in the contacts array that matches the provided email and child index.
     *
     * @param {Array} contacts - Array of contacts.
     * @param {string} contactEmail - Email address of the contact.
     * @param {number} currentChildIndex - Index of the current child.
     *
     * @returns {Object|null} - Matching contact object or null if not found.
     */
    static findMatchingContactByEmailAndIndex(contacts, contactEmail, currentChildIndex) {
        return contacts.find(contact => {
            return (contactEmail === contact.profileEmailAddress
                && currentChildIndex === contact.childIndex);
        });
    }

    /**
     * Creates a new contact in the contacts array or updates an existing one based on the provided parameters.
     *
     * @param {Array} contacts - Array of contacts.
     * @param {string} contactEmail - Email address of the contact.
     * @param {number} currentChildIndex - Index of the current child.
     * @param {string} contactType - Type of contact to create or update.
     * @param {string} contactTypeValue - Value of the contact type.
     *
     * @returns {Array} - Updated array of contacts.
     */
    static createNewContact(contacts, contactEmail, currentChildIndex, contactType, contactTypeValue) {
        const contactToDuplicateIndex = contacts.findIndex(contact => contactEmail === contact.profileEmailAddress);
        contacts.push({
            ...contacts[contactToDuplicateIndex],
            childIndex: currentChildIndex,
            copyToChildren: 'Yes',
            [contactType]: contactTypeValue,
        });

        // Change all other contacts with the same email to false for value 'copyToChildren'
        contacts.forEach((contact, index) => {
            if (contact.profileEmailAddress === contactEmail && contact.childIndex !== currentChildIndex) {
                contact['copyToChildren'] = 'No';
            }
        });
        return contacts
    }

    /**
     * Updates an existing contact in the contacts array based on the provided parameters.
     *
     * @param {Array} contacts - Array of contacts.
     * @param {string} contactEmail - Email address of the contact.
     * @param {number} currentChildIndex - Index of the current child.
     * @param {string} contactType - Type of contact to update.
     * @param {string} contactTypeValue - Value of the contact type.
     *
     * @returns {Array} - Updated array of contacts.
     */
    static updateContact(contacts, contactEmail, currentChildIndex, contactType, contactTypeValue) {
        const contactToUpdate = contacts.find(contact => {
            return contact.profileEmailAddress === contactEmail && contact.childIndex === currentChildIndex
        });
        contactToUpdate[contactType] = contactTypeValue;
        return contacts;
    }

    /**
     * Retrieves contacts associated with a specific child index, considering the most recent child if no match is found.
     *
     * @param {Array} contacts - Array of contacts.
     * @param {number} currentChildIndex - Index of the current child.
     *
     * @returns {Array} - Array of chosen contacts based on the provided child index.
     */
    static getChildContacts(contacts, currentChildIndex) {
        const chosenContacts = []
        const contactsGroupedByEmail = contacts.reduce(function(rv, x) {
            (rv[x['profileEmailAddress']] = rv[x['profileEmailAddress']] || []).push(x);
            return rv;
        }, []);

        // Look for contact that matches the child index, if there is none, show the most recent child
        // e.g. If I am creating child #3, then show child #2 selections
        const contactEmails = Object.keys(contactsGroupedByEmail);
        for (const email of contactEmails) {
            const contactWithChildIndex = this.findMatchingContactByEmailAndIndex(contactsGroupedByEmail[email], email, currentChildIndex);
            if (contactWithChildIndex) {
                chosenContacts.push(contactWithChildIndex);
            } else {
                // Find highest childIndex within a single contact group
                chosenContacts.push([...contactsGroupedByEmail[email].reduce((r, o) =>
                        (!r.has(o.profileEmailAddress) || r.get(o.profileEmailAddress).childIndex < o.childIndex) ? r.set(o.profileEmailAddress, o) : r
                    , new Map()).values()][0]);
            }
        }
        return chosenContacts;
    }

    /**
     * Retrieves non-purchased plans from the session data.
     *
     * @param {Object} sessionData - Session data object.
     *
     * @returns {Object} - Updated session data object with non-purchased plans.
     */
    static getNonPurchasedPlansFromSessionData(sessionData){
        const sessionDataArray = sessionData.plans[0] || [];
        const nonPurchasedPlans = sessionDataArray.filter(plan => !plan.planDetails);
        return {
            ...sessionData,
            plans: [nonPurchasedPlans]
        }
    }

    /**
     * Returns only selected plans that aren't punchcards or items and that don't have selective weeks.
     *
     * @param {Array} templatePlans - Session data object.
     *
     * @returns {Array} - Updated session data object with non-purchased plans.
     */
    static getPlansForDaySelect(templatePlans) {
       return templatePlans.filter(plan => plan.type !== ITEM_TYPE && plan.type !== PUNCH_CARD_TYPE && (!plan.details?.selectiveWeeks || plan.details?.selectiveWeeks?.length === 0));
    }

    /**
     * Adjusts labels of selected weeks plans for registration receipt
     *
     * @param {Array} registrationPlans
     * @return {Array}
     */
    static labelSelectedWeeks(registrationPlans) {
        const plans = cloneDeep(registrationPlans);
        const planCounters = new Map();
        for (const plan of plans) {
            if (!plan.selectedWeeks) {
                continue;
            }
            // all selected week numbers are stored on each plan, so we iterate through each one as we encounter
            // the same id plans
            const weekIndex = planCounters.get(plan._id) ?? 0;
            plan.description += ' - Week ' + (plan.selectedWeeks[weekIndex] + 1);
            planCounters.set(plan._id, weekIndex + 1);
            plan.isSelectedWeeks = true;
        }
        return plans;
    }

    /**
     * Get the available selective weeks for a plan given already enrolled plans.
     *
     * @param plan
     * @param enrolledPlans
     * @param timezone
     * @returns {*[]}
     */
    static getAvailableSelectiveWeeks(plan, enrolledPlans, timezone) {
        const today = moment.tz(timezone).startOf('day');
        const isSelectiveWeekPlan = plan.details?.selectiveWeeks?.length > 0;
        let availableUnmatchedWeeks = [];
        enrolledPlans = enrolledPlans ?? [];

        if (isSelectiveWeekPlan) {
            // Get the weeks that the child is already enrolled in for this plan
            const matchedWeekStarts = enrolledPlans
                .filter(p => p._id === plan._id)
                .flatMap(p => {
                    const unprocessedPlanWeekStarts = (p.selectedWeeks ?? []).map(week => p.details.selectiveWeeks[week][0]);
                    return plan.details.selectiveWeeks
                        .filter(weekDates =>
                            weekDates[0] === p.enrollmentDateFormatted || unprocessedPlanWeekStarts.includes(weekDates[0])
                        )
                        .map(weekDates => weekDates[0]);
                });

            // Get the available weeks that are still after today and not at capacity
            availableUnmatchedWeeks = plan.details.selectiveWeeks
                .filter(weekDates =>
                    !matchedWeekStarts.includes(weekDates[0]) &&
                    moment.tz(weekDates[0], 'MM/DD/YYYY', timezone).startOf('day').isAfter(today) &&
                    !weekDates[2] // if a week is at capacity, it will have a third element set to true
                );
        }

        return availableUnmatchedWeeks;
    }


    /**
     * Get the service dates for a plan.
     *
     * @param plan
     * @param timePeriods
     * @param timezone
     * @returns {{endDate: (null|null|*|string), response: string, startDate: (null|null|*|string)}|{endDate: null, response: string, startDate: string}|{endDate: string, response: string, startDate: string}}
     */
    static getServiceDates = (plan, timePeriods, timezone) => {
        if (plan.details) {
            if (plan.details.dateType === 'dateRange' && plan.details.serviceStartDate && plan.details.serviceEndDate) {
                const dateRangeStartDate = moment.tz(plan.details.serviceStartDate, timezone).format('MM/DD/YYYY');
                const dateRangeEndDate = moment.tz(plan.details.serviceEndDate, timezone).format('MM/DD/YYYY');
                return {
                    response: `${dateRangeStartDate}-${dateRangeEndDate}`,
                    startDate: dateRangeStartDate,
                    endDate: dateRangeEndDate
                }
            } else if (plan.details.dateType === 'recurring' && plan.details.recurringStartDate && plan.details.recurringFrequency && plan.details.recurringOccurrences) {
                const startingDate = moment.tz(plan.details.recurringStartDate, timezone).format('MM/DD/YYYY');
                const recurringFrequency = plan.details.recurringFrequency
                const recurringOccurrences = plan.details.recurringOccurrences
                return {
                    response: `Starting ${startingDate}, Repeats every ${recurringFrequency} week(s) for ${recurringOccurrences} occurrences`,
                    startDate: startingDate,
                    endDate: null
                }
            } else if (plan.details.dateType === 'individualDates' && plan.details.individualDates) {
                const individualDates = plan.details.individualDates;
                const formattedDates = [];
                individualDates.forEach(date => {
                    const formatDate = moment.tz(date, timezone).format('MM/DD/YYYY');
                    formattedDates.push(formatDate);
                })
                const { earliestDate, latestDate} = DateTimeUtils.earliestAndLatestDatesOfArray(formattedDates)
                return {
                    response: `${formattedDates.join(" , ")}`,
                    startDate: earliestDate,
                    endDate: latestDate
                }
            }
            const filteredTimePeriods = timePeriods.find(timePeriod => timePeriod._id === plan.details.timePeriod)
            if (filteredTimePeriods) {
                const serviceStartDate = moment.tz(filteredTimePeriods.startDate, timezone).format('MM/DD/YYYY');
                const serviceEndDate = moment.tz(filteredTimePeriods.endDate, timezone).format('MM/DD/YYYY');
                return {
                    response: `${serviceStartDate}-${serviceEndDate}`,
                    startDate: serviceStartDate,
                    endDate: serviceEndDate
                }
            }
        }
    }

    /**
     * Get total number of checked boxes for plan
     */
    static numOfCheckedBoxesInPlan(planId){
        return document.querySelectorAll(`.day-checkbox[data-id="${ planId }"]:checked`)?.length ?? 0;
    }

    /**
     * Get original plan amount (no discounts applied)
     * @param plan
     */
    static getPlanAmount(plan) {
        if (plan.details?.selectiveWeekAmounts) {
            const range = MiscUtils.getSelectiveWeekRange([plan]);
            if (!range) {
                return MiscUtils.formatCurrency(plan.amount);
            } else if (range.min === range.max) {
                return `${MiscUtils.formatCurrency(range.min)}`;
            } else {
                return `${MiscUtils.formatCurrency(range.min)} - ${MiscUtils.formatCurrency(range.max)}`;
            }
        } else {
            return MiscUtils.formatCurrency(plan.amount);
        }
    }

    /**
     * Get discounted plan amount
     * @param plan
     */
    static getPlanDiscountedAmount(plan) {
        if (plan.details?.selectiveWeekAmounts) {
            const range = MiscUtils.getSelectiveWeekRange([plan]);
            if (!range) {
                return MiscUtils.formatCurrency(plan.planTotal);
            } else if (range.min === range.max) {
                return `${MiscUtils.formatCurrency(range.min)}`;
            } else {
                return `${MiscUtils.formatCurrency(range.min)} - ${MiscUtils.formatCurrency(range.max)}`;
            }
        } else {
            return MiscUtils.formatCurrency(plan.planTotal);
        }
    }

    /**
     * Add subsidy create modal
     */
    static addSubsidyModal(data, topLinePercentDiscounts, registrationFlowType, rerenderRegistration) {
        mpSwal.fire({
            title: 'Notice',
            text: 'Entering subsidy information ends the registration process. If you have coupon codes or employee discounts you have not entered yet, please go back and enter those and then come back here to enter your subsidy information. Please note that coupons/discounts cannot be combined with subsidy.',
            showCancelButton: true,
            confirmButtonText: 'Enter Subsidy',
            cancelButtonText: 'Go Back'
        }).then ( result => {
            if (result.value) {
                showModal('addSubsidyModal', {
                    savedData: data,
                    topLinePercentDiscounts: topLinePercentDiscounts,
                    rerenderRegistration: rerenderRegistration,
                    registrationFlowType: registrationFlowType
                },  '#addSubsidyModal');
            }
        });
    }

    /**
     * Add district employee email
     */
    static addDistrictEmployeeEmailModal(data, topLinePercentDiscounts, registrationFlowType, rerenderRegistration) {
        mpSwal.fire({
            title: 'Notice',
            text: 'Entering District Employee Email information ends the registration process. If you have additional information to enter, please go back and enter those and then come back here to enter your district employee email',
            showCancelButton: true,
            confirmButtonText: 'Enter District Employee Email',
            cancelButtonText: 'Go Back'
        }).then ( result => {
            if (result.value) {
                showModal('addSubsidyModal', {
                    savedData: data,
                    topLinePercentDiscounts: topLinePercentDiscounts,
                    rerenderRegistration: rerenderRegistration,
                    showDistrictEmail: true,
                    registrationFlowType: registrationFlowType
                }, '#addSubsidyModal');
            }
        });
    }

    /**
     * Determine if savings should be shown because there is a bundle.
     * Return bool and the bundle.
     *
     * @param currentPlans
     * @param allChildPlans
     * @param bundles
     * @param availablePlans
     * @returns {{showSavings: boolean, bundle: null}}
     */
    static showSavingsForBundles(currentPlans, allChildPlans, bundles, availablePlans) {
        // Handle null inputs
        if (!currentPlans || !bundles || !availablePlans) {
            return {
                bundle: null,
                showSavings: false
            };
        }
    
        const plans = cloneDeep(currentPlans);
        const currentPlanIds = plans.map(p => p._id);
        
        // We still need allChildPlans to detect when a parent is buying the second part of a bundle
        const combinedPlans = [...plans, ...(allChildPlans ?? [])];
        const combinedPlanIds = [...new Set(combinedPlans.map(p => p._id))];
        
        // Find bundles that match when considering both current plans and enrolled plans
        const matchedBundles = MiscUtils.twoMatchedPlans(combinedPlanIds, bundles, availablePlans);
        
        // Filter out bundles that are exclusively in allChildPlans (already enrolled)
        const relevantBundles = matchedBundles.filter(bundle => {
            // A bundle is relevant if at least one of its plans is in the current selection
            return bundle.plans.some(planId => currentPlanIds.includes(planId));
        });
        
        let currentBundle = null;
        let showSavings = false;
        
        if (relevantBundles.length) {
            // Sort bundles by the max amount of the discount, return the one with the greatest discount
            currentBundle = relevantBundles.sort((a, b) => {
                const aMaxAmount = Math.max(...a.scaledAmounts.flat().filter(amount => amount !== null));
                const bMaxAmount = Math.max(...b.scaledAmounts.flat().filter(amount => amount !== null));
                return aMaxAmount - bMaxAmount; // Ascending order to get lowest price first
            })[0];
            
            showSavings = true;
        }
        
        return {
            showSavings,
            bundle: currentBundle
        }
    }

    /**
     * Determines the list of plans to be used for calculating the regular price based on the source.
     * Calls the calculateRegularPrice function with the retrieved plans.
     *
     * @param {Object} instance - The context or instance object.
     * @param {String} source - The source type, indicating where the call is being made (e.g., INITIAL_REGISTRATION, ADD_PROGRAM, EDIT_PROGRAM).
     */
    static getRegularPriceForBundleSavings(instance, source) {
        let plans = [];

        if (source === parentSource.INITIAL_REGISTRATION) {
            plans = instance.currentPlans.get();
        } else if (source === parentSource.ADD_PROGRAM) {
            const allPlans = instance.allChildPlans;
            const selectedPlans = instance.data.selectedPlans.get();

            // Use safe checks to ensure both arrays are defined before merging
            const allPlansArray = Array.isArray(allPlans) ? allPlans : [];
            const selectedPlansArray = Array.isArray(selectedPlans) ? selectedPlans : [];
            plans = _.uniqBy([...allPlansArray, ...selectedPlansArray], '_id');
        }

        RegistrationUtils.calculateAndSetRegularPrice(instance, plans);
    }

    /**
     * Calculates and sets the bundled price for the current bundle.
     * Updates the bundled plan's amounts for use in the summary step and marks the plans as bundled.
     *
     * @param {Object} instance - The context or instance object.
     * @param {String} source - The source type, indicating where the call is being made (e.g., INITIAL_REGISTRATION, ADD_PROGRAM, EDIT_PROGRAM).
     * @param {Object} [selectedPlansReactiveVariable=null] - The reactive variable for selected plans, used when the source is INITIAL_REGISTRATION.
     */
    static calculateAndSetBundledPrice(instance, source, selectedPlansReactiveVariable = null) {
        const currentBundle = instance.currentBundle.get();

        if (!currentBundle || !currentBundle.plans || !currentBundle.plans.length) {
            return;
        }

        const plan1DaysCount = RegistrationUtils.getDaysCountForPlan(instance, currentBundle.plans[0], source);
        const plan2DaysCount = RegistrationUtils.getDaysCountForPlan(instance, currentBundle.plans[1], source);

        if (!plan1DaysCount || !plan2DaysCount) {
            return;
        }

        const bundleAmount = currentBundle.scaledAmounts[plan1DaysCount - 1][plan2DaysCount - 1];

        if (!bundleAmount) {
            return;
        }

        instance.bundledPrice.set(bundleAmount);
        instance.bundleSavings.set(instance.regularPrice.get() - instance.bundledPrice.get());

        const currentPlans = instance.currentPlans.get();
        for (const plan of currentPlans) {
            if (currentBundle.plans.includes(plan._id)) {
                const daysCount = RegistrationUtils.getDaysCountForPlan(instance, plan._id, source);
                const scaledIndex = RegistrationUtils.getScaledAmountIndexFromDaysCount(daysCount, plan);

                plan.amount = plan.scaledAmounts[scaledIndex] ?? 0;
                plan.bundlePlanId = currentBundle._id;
                plan.bundlePlanPrice = instance.bundledPrice.get();
                plan.regularPrice = instance.regularPrice.get();
            }
        }

        if (source === parentSource.INITIAL_REGISTRATION && selectedPlansReactiveVariable) {
            selectedPlansReactiveVariable.set(currentPlans);
        } else {
            instance.data.selectedPlans.set(currentPlans);
        }
    }


    /**
     * Retrieves the selected day count for a specific plan.
     * Falls back to the specified default day count if no checkboxes are selected.
     *
     * @param {Object} instance - The context or instance object.
     * @param {string} planId - The ID of the plan to check for selected days.
     * @param {String} source - The source type, indicating where the call is being made (e.g., INITIAL_REGISTRATION, ADD_PROGRAM, EDIT_PROGRAM).
     * @returns {number} - The selected day count or the default value.
     */
    static getDaysCountForPlan(instance, planId, source) {
        let daysCount = 0;

        if (source === parentSource.INITIAL_REGISTRATION) {
            daysCount = RegistrationUtils.numOfCheckedBoxesInPlan(planId);
        } else if (source === parentSource.ADD_PROGRAM) {
            const planExistsInDOM = !!document.querySelectorAll(`.day-checkbox[data-id="${ planId }"]`).length;
            daysCount = planExistsInDOM ? RegistrationUtils.numOfCheckedBoxesInPlan(planId) : RegistrationUtils.findExistingPlanDayCount(instance.data.selectedPlans.get(), planId);
        }

        return daysCount
    }

    static findExistingPlanDayCount(plans, planId) {
        return plans.find(p => p._id === planId)?.selectedDays?.length || 0;
    }

    /**
     * Determines the index for the scaled amount based on the provided days count.
     * Ensures that the index does not exceed the length of the plan's scaled amounts array.
     *
     * @param {number} daysCount - The number of days to consider.
     * @param {Object} plan - The plan object that contains scaled amounts.
     * @param {Array<number>} [plan.scaledAmounts] - The array of scaled amounts associated with the plan.
     * @returns {number} - The index corresponding to the days count, or -1 if no scaled amounts are present.
     */
    static getScaledAmountIndexFromDaysCount(daysCount, plan) {
        let index = -1;

        if (!plan || daysCount === undefined || daysCount === null) {
            return index;
        }

        if (plan.scaledAmounts?.length) {
            if (daysCount <= 0) {
                // Return minimum index if no days are selected
                return 0;
            }

            if (daysCount > plan.scaledAmounts.length) {
                index = plan.scaledAmounts.length - 1;
            } else {
                index = daysCount - 1;
            }
        }

        return index;
    }

    /**
     * Updates the total amount for a plan with scaled amounts based on the number of selected days.
     *
     * @param {Object} instance - The context or instance object.
     * @param {string} planId - The ID of the plan to update.
     * @param {number} numCheckedDays - The number of days selected for the plan.
     */
    static updateTotalAmountForScaledAmounts(instance, planId, numCheckedDays) {
        let plans = instance.plansWithScaledAmounts.get();
        const planIndex = plans.findIndex(plan => plan._id === planId);

        if (planIndex < 0) {
            // Exit if plan with no scaledAmounts
            return;
        }

        const scaledIndex = RegistrationUtils.getScaledAmountIndexFromDaysCount(numCheckedDays, plans[planIndex]);

        if (scaledIndex < 0) {
            // Exit if no scaled index found
            return
        }

        plans[planIndex]["amount"] = plans[planIndex]["scaledAmounts"][scaledIndex];
        instance.plansWithScaledAmounts.set(plans);
    }

    /**
     * Calculates the regular price for the given plans based on their scaled amounts and sets the price in the instance.
     *
     * @param {Object} instance - The context or instance object.
     * @param {Array} plans - The list of plans to be used for the price calculation.
     */
    static calculateAndSetRegularPrice(instance, plans) {
        const currentBundle = instance.currentBundle.get();
        let price = 0;

        if (currentBundle && currentBundle.plans.length) {
            for (const plan of plans) {

                if (!currentBundle.plans.includes(plan._id)) {
                    continue;
                }

                if (Array.isArray(plan.scaledAmounts) && plan.scaledAmounts.length) {
                    const daysCount = RegistrationUtils.numOfCheckedBoxesInPlan(plan._id);
                    const scaledIndex = RegistrationUtils.getScaledAmountIndexFromDaysCount(daysCount, plan);
                    price += plan.scaledAmounts[scaledIndex] ?? 0; // Handle possible null/undefined values
                }
            }
        }

        instance.regularPrice.set(price);
    }


    /**
     * Formats a number to include commas as thousands separators and ensures two decimal places.
     *
     * @param {number} num - The number to format.
     * @returns {string} - The formatted number as a string with commas and two decimal places.
     */
    static formatNumberWithCommas(num) {
        return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    /**
     * Generates an error message based on the provided error code, additional information, and whether registration fees are included in the totals.
     *
     * @param {Object} params - The parameters object.
     * @param {string} params.errorCode - The specific error code associated with the issue.
     * @param {Object} params.additionalInfo - Additional information related to the error.
     * @param {string} params.additionalInfo.currentCouponCode - The code of the current coupon involved in the error.
     * @param {Object} [params.additionalInfo.conflictingCoupon] - The conflicting coupon object, if applicable.
     * @param {string} [params.additionalInfo.conflictingCoupon.code] - The code of the conflicting coupon, if applicable.
     * @param {boolean} hasRegFeesAllocations - Flag indicating if the registration fees allocations exist. If true, the message will include a note that registration fees are not included in the totals.
     * @returns {string} - A user-friendly error message based on the error code, additional info, and registration fee status.
     */
    static getErrorText({ errorCode, additionalInfo }, hasRegFeesAllocations = false) {
        const couponCode = additionalInfo.currentCouponCode;
        const conflictingCoupon = additionalInfo.conflictingCoupon ? additionalInfo.conflictingCoupon.code : '';
        let errorMessage = '';

        switch (errorCode) {
            case "MR-000005":
                errorMessage = `Unfortunately, the coupon '${couponCode}' can't be used with other discounts. If you choose to keep your current discount, this new discount won't be applied. However, if you switch, your current discount will be replaced by the new one. Please review the details below before making your decision.`;
                break;
            case "MR-000006":
                errorMessage = `Unfortunately, the coupon '${couponCode}' can't be used with other coupons. If you choose to keep your current coupon '${conflictingCoupon}', this new coupon won't be applied. However, if you switch, your current coupon will be replaced by the new one. Please review the details below before making your decision.`;
                break;
            case "MR-000007":
                errorMessage = `Unfortunately, the coupon '${couponCode}' can't be used with other coupons. If you choose to keep your current coupons, this new coupon won't be applied. However, if you switch, your current coupons will be replaced by the new one. Please review the details below before making your decision.`;
                break;
            default:
                errorMessage = `Error code: ${errorCode}`;
        }

        if (hasRegFeesAllocations) {
            errorMessage += ' Please note that registration fees will not be included in the totals.';
        }

        return errorMessage;
    }

    /**
     * Calculates the grand total and total savings from the cart plans list.
     *
     * This method processes a list of cart plans to calculate the grand total and the total savings
     * achieved through discounts or coupons. It returns an object containing these calculated values
     * along with the modified copy of the cart plans list, with keys dynamically named based on the status provided.
     *
     * @param {Array<Object>} cartPlansList - The list of cart plans from which to calculate totals.
     * @param {string} status - A string used to dynamically generate the keys for the returned object.
     *                          For example, if status is "current", the returned object will have keys like
     *                          "currentGrandTotal" and "currentTotalSavings".
     * @returns {Object} - An object containing dynamically named keys for the grand total, total savings,
     *                     and the modified copy of the cart plans list.
     * @returns {string} return.[`${status}GrandTotal`] - The calculated grand total of all plans, formatted as a string with two decimal places.
     * @returns {string} return.[`${status}TotalSavings`] - The total savings from all discounts and coupons, formatted as a string with two decimal places.
     * @returns {Array<Object>} return.[`${status}CartPlansList`] - The modified copy of the cart plans list with applied calculations.
     */
    static calculateCartTotalsAndDiscountSavings(cartPlansList, status) {
        const grandTotal = cartPlansList.flat().reduce((total, plan) => total + (plan.planTotal || 0), 0);
        const totalSavings = cartPlansList.flat().reduce((totalSavings, plan) => {
            if (plan.amount !== undefined && plan.planTotal !== undefined) {
                const savings = plan.amount - plan.planTotal;
                return totalSavings + savings;
            }
            return totalSavings;
        }, 0);

        return {
            [`${status}GrandTotal`]: grandTotal.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
            [`${status}TotalSavings`]: totalSavings.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
            [`${status}CartPlansList`]: cartPlansList // Return the modified copy
        };
    }


    /**
     * Returns the price range for a plan based on scaled amounts, accommodating different configurations.
     * Handles plans with scaled amounts of varying lengths (1 to 7) and accounts for null values.
     * @param {Object} plan - The plan object.
     * @returns {string} - The formatted price range or plan amount.
     */
    static getScaledPlanRange(plan) {
        if (Array.isArray(plan.scaledAmounts) && plan.scaledAmounts.length > 0) {
            const validAmounts = plan.scaledAmounts.filter(amount => amount !== null);
            if (validAmounts.length > 0) {
                const minAmount = Math.min(...validAmounts);
                const maxAmount = Math.max(...validAmounts);
                return `${MiscUtils.formatCurrency(minAmount)} - ${MiscUtils.formatCurrency(maxAmount)}`;
            }
        }
        return MiscUtils.formatCurrency(plan.amount ?? 0);
    }


    /**
     * Gets the price range for matched bundles, accommodating bundles with null values in scaled amounts.
     * It calculates the minimum and maximum amounts for the bundle while ignoring null values.
     * @param {Array} possibleBundles - The list of possible bundles that match the criteria.
     * @param {Object} plan - The current plan object.
     * @param {Array} selectedPlanIds - The list of selected plan IDs.
     * @returns {string|null} - The formatted bundle price range or null if no match is found.
     */
    static getBundlePriceRange(possibleBundles, plan, selectedPlanIds) {
        possibleBundles.sort((a, b) => {
            const aMaxAmount = Math.max(...a.scaledAmounts.flat().filter(amount => amount !== null));
            const bMaxAmount = Math.max(...b.scaledAmounts.flat().filter(amount => amount !== null));
            return aMaxAmount - bMaxAmount;
        });

        const matchingBundle = possibleBundles[0];
        if (matchingBundle && this.isPlanInBundle(matchingBundle, plan, selectedPlanIds)) {
            const allAmounts = matchingBundle.scaledAmounts.flat().filter(amount => amount !== null);
            if (allAmounts.length > 0) {
                const minAmount = Math.min(...allAmounts);
                const maxAmount = Math.max(...allAmounts);
                return `${MiscUtils.formatCurrency(minAmount)} - ${MiscUtils.formatCurrency(maxAmount)}`;
            }
        }

        return null;
    }

    /**
     * Checks if the given plan is part of the bundle and matches with another selected plan.
     * @param {Object} bundle - The bundle object containing plans.
     * @param {Object} plan - The plan object to check.
     * @param {Array} selectedPlanIds - The list of selected plan IDs.
     * @returns {boolean} - True if the plan is in the bundle and matches another selected plan, otherwise false.
     */
    static isPlanInBundle(bundle, plan, selectedPlanIds) {
        const otherPlanId = bundle.plans.find(p => p !== plan._id);
        return bundle.plans.includes(plan._id) && selectedPlanIds.includes(otherPlanId);
    }

    /**
     * Determines if the plan is a simple plan type that doesn't use scaled frequencies.
     * @param {Object} plan - The plan object.
     * @returns {boolean} - True if the plan is a simple type, otherwise false.
     */
    static isSimplePlanType(plan) {
        return (
            plan.type === ITEM_TYPE ||
            plan.type === PUNCH_CARD_TYPE ||
            !SCALED_PLAN_FREQUENCIES.includes(plan.frequency)
        );
    }

    /**
     * Gets the price range for a simple plan with selective week amounts.
     * @param {Object} plan - The plan object.
     * @param {number|null} index - The specific week index to retrieve the price for.
     * @returns {string} - The formatted price or price range.
     */
    static getSimplePlanPriceRange(plan, index) {
        const selectiveWeekAmounts = plan.details?.selectiveWeekAmounts;

        if (selectiveWeekAmounts?.length) {
            if (MiscUtils.isValidIndex(index, selectiveWeekAmounts)) {
                return MiscUtils.formatCurrency(selectiveWeekAmounts[index]);
            }

            const uniqueAmounts = new Set(selectiveWeekAmounts);
            if (uniqueAmounts.size === 1) {
                return MiscUtils.formatCurrency(selectiveWeekAmounts[0]);
            }

            const minAmount = Math.min(...uniqueAmounts);
            const maxAmount = Math.max(...uniqueAmounts);
            return `${MiscUtils.formatCurrency(minAmount)} - ${MiscUtils.formatCurrency(maxAmount)}`;
        }

        return MiscUtils.formatCurrency(plan.amount ?? 0);
    }

    /**
     * Sets the default selected days for a plan based on its configuration.
     * Defaults to using DEFAULT_PROGRAM_DAYS unless the plan specifies otherwise.
     *
     * @param {Object} plan - The plan object that contains details about its configuration.
     * @param {Array<string>} [plan.programOfferedOn] - Days of the week the plan is offered (e.g., ['monday', 'wednesday']).
     * @param {number} [plan.requiredEnrollmentMax] - Maximum number of days required for enrollment.
     * @param {Array<string>} [plan.selectedDays] - Array of currently selected days for the plan.
     * @returns {Array<string>} - The default selected days based on the plan's configuration.
     */
    static setDefaultSelectedDaysByPlanConfig(plan) {
        let selectedDays = DEFAULT_PROGRAM_DAYS;

        // If plan is restricted to certain days, set those as the default selected days
        if (plan.programOfferedOn) {
            selectedDays = [...plan.programOfferedOn];
        }

        // If plan has maxDays, set default days
        if (plan.requiredEnrollmentMax && selectedDays.length > plan.requiredEnrollmentMax) {
            selectedDays = selectedDays.slice(0, plan.requiredEnrollmentMax);
        }

        return selectedDays;
    }

    /**
     * Checks the session to see if we actually have enough primary contacts
     * 
     * @returns {boolean} - True if there is at least 1 primary contact, false otherwise
     */
    static doesSessionHavePrimaryContacts() {
        const regData = Session.get('registrationFlowData')
        if (regData && regData.contacts) {
            const primaryContacts = regData.contacts.filter((contact => contact.primaryCaregiver === "Yes"))
            if (primaryContacts.length === 0) {
                return false
            } else {
                return true
            }
        } else {
            return false
        }
    }

    /**
     * Validate plans to see if bundle discount still applies
     * 
     * @param {Array<Object>} plans - Array of plans
     * @param {Array<Object>} plan - Plan object to check
     * @param {Array<Object>} bundles - Array of bundles
     */
    static filterBundleDiscountsWithoutRequiredPlans(plans, plan, bundles) {
        // If plan has a bundle discount, verify all bundle plans are still selected
        if (plan.allocations?.some(a => a.discountType === DiscountTypes.BUNDLE)) {
            const bundle = bundles.find(b => b._id === plan.bundlePlanId);
            const allBundlePlansSelected = bundle?.plans.every(bundlePlanId => 
                plans.some(p => p._id === bundlePlanId)
            );

            // Remove bundle discount if not all required plans are selected
            if (!allBundlePlansSelected) {
                delete plan.bundlePlanId;
                plan.allocations = plan.allocations.filter(allocation => allocation.discountType !== DiscountTypes.BUNDLE);
            }
        }
    }

    /**
     * Validates a plan's eligibility for a bundle discount and applies or updates the discount allocation accordingly.
     *
     * - Ensures all required bundle plans are selected for the current child.
     * - Applies a bundle discount if not already present.
     * - Updates the bundle discount amount if it exists.
     * - Fills in form-derived fields like `selectedDays` and `startDate`.
     * - Falls back to scaled amount pricing if no bundle applies.
     *
     * @param {Object} currentData - The registration data object containing child-specific plans.
     * @param {Object} plan - The plan being validated and updated.
     * @param {Array<Object>} bundles - The array of available bundle definitions.
     * @param {Array<Object>} formData - Serialized form data for the plan, e.g., from `$('#planId').serializeArray()`.
     * @param {Array<string>} bundlesApplied - Mutable array tracking which bundle IDs have been processed.
     * @param {number} [childIndex=0] - Index of the child whose plans are being processed.
     */
    static validateAndPushBundleDiscount(currentData, plan, bundles, formData, bundlesApplied, childIndex = 0) {
        this.filterBundleDiscountsWithoutRequiredPlans(currentData.plans[childIndex], plan, bundles)

        plan.selectedDays = formData.length ? _.filter(formData, input => input.value === 'on').map(day => day.name) : plan.selectedDays;
        plan.startDate = formData.length ? _.find(formData, input => input.name === 'startDate').value : plan.startDate;
        // Handle bundle discount
        if (plan.bundlePlanId) {
            const bundle = bundles.find(b => b._id === plan.bundlePlanId);
            if (((plan.allocations && !plan.allocations.find(a => a.discountType === DiscountTypes.BUNDLE)) || !plan.allocations)) {
                plan.allocations = plan.allocations || [];
                plan.allocations.push({
                    allocationType: "discount",
                    amount: (plan.regularPrice - plan.bundlePlanPrice) / 2,
                    amountType: 'dollars',
                    discountType: DiscountTypes.BUNDLE,
                    allocationDescription: bundle ? `Bundle: ${bundle.description}` : ''
                })
            }
            if (plan.allocations && plan.allocations.find(a => a.discountType === DiscountTypes.BUNDLE)) {
                plan.allocations = plan.allocations || [];
                plan.allocations.map(a => {
                    if (a.discountType === DiscountTypes.BUNDLE) {
                        a.amount = (plan.regularPrice - plan.bundlePlanPrice) / 2;
                    }
                    return a;
                });
            }
            bundlesApplied.push(plan.bundlePlanId);
        } else if (plan.scaledAmounts?.length) {
            plan.amount = plan.scaledAmounts[(plan.selectedDays?.length || 5) - 1];
        }
    }
    
    /** Determines via enterprisePerson if the familyPerson and person exist and are related at destination org
     * 
     * @param {string} orgId 
     * @param {string} familyPersonId 
     * @param {string} personId 
     * @returns {{ personInOrg: Object, familyPersonInOrg: Object, relationshipInOrg: Object, canRegister: boolean }}
     */

    static async canFamilyRegisterAtOrg( orgId, familyPersonId, personId ) {
        const enterprisePerson = await EnterprisePeople.findOneByPersonId( personId );
        const enterpriseFamilyPerson = await EnterprisePeople.findOneByPersonId( familyPersonId );
        const enterpriseRelationship = await Relationships.findOneAsync({ isEnterprise: true, relationshipType: "family", targetId: enterprisePerson?._id, personId: enterpriseFamilyPerson?._id });
        const personInOrg = enterprisePerson?.orgsPeople?.find( orgPerson => orgPerson.orgId === orgId );
        const familyPersonInOrg = enterpriseFamilyPerson?.orgsPeople?.find( orgPerson => orgPerson.orgId === orgId );  
        const relationshipInOrg = enterpriseRelationship?.orgsRelationships?.find( orgRelationship => orgRelationship.orgId === orgId );
        const familyPersonUser = familyPersonInOrg && Meteor.users.findOneAsync({ personId: familyPersonInOrg.personId });
        const canRegister = personInOrg && familyPersonInOrg && relationshipInOrg && true;
        const creatingEnterprisePersonInOrg = !personInOrg && await People.findOneAsync({ orgId: orgId, enterpriseSourcePersonId: personId });
        const creatingFamilyEnterprisePersonInOrg = !familyPersonInOrg && await People.findOneAsync({ orgId: orgId, enterpriseSourcePersonId: personId })
        // need to add check for allowed registration interaction between orgs in enterprise

        const canCreateEnterprisePerson = enterprisePerson && !personInOrg && !creatingEnterprisePersonInOrg && true;
        const canCreateFamilyEnterprisePerson = enterprisePerson && enterpriseFamilyPerson && personInOrg && !familyPersonInOrg && !creatingFamilyEnterprisePersonInOrg && true;

        return {
            personInOrg,
            familyPersonInOrg,
            relationshipInOrg,
            canRegister,
            canCreateEnterprisePerson
        }
    }
    
    /**
     * Creates a new person in a destination org to trigger enterprisePerson sync
     * 
     * @param {string} personId
     * @param {string} orgId
     * @returns {string} - ID of the new person
     */
    static async createNewEnterprisePersonFromOrgPerson( personId, orgId ) {
        const newPerson = {
            orgId,
            type: "person",
            enterpriseSourcePersonId: personId,
            createdAt: Date.now(),
            createdBy: "SYSTEM_NEW_ENTERPRISE_PERSON_FROM_ORG_PERSON"
        }
        const newPersonId = await People.insertAsync( newPerson );
        console.log("created new person");
        return newPersonId;
    }

    /**
     * Gets the plans that are not selective weeks.
     * Filters out punch cards and items as well.
     *
     * @param plansAndItems
     * @returns {*}
     */
    static getPlansExcludingSelectiveWeeks(plansAndItems) {
        return (plansAndItems ?? []).filter(p => p.type === PLAN_TYPE && !(p.details?.selectiveWeeks ?? []).length);
    }
}