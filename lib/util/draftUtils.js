import { SavedDrafts } from "../collections/savedDrafts";

export class DraftUtils {
    static async saveDraft(draftType, draftObject, draftName, personId, orgId) { 
        return await SavedDrafts.insertAsync({
            draftType,
            draftObject,
            draftName,
            savedTime: new Date().valueOf(),
            personId,
            orgId
        });
    }

    static async getDrafts(draftType, personId, orgId) {
        return await SavedDrafts.find({personId, orgId, draftType}, {sort: {savedTime: 1 }}).fetchAsync();
    }

    static getDraftLabel(draft) {
        return draft.draftName + ' (' + (new moment(draft.savedTime).format('MM/DD/YY h:mm a')) + ')';
    }
}