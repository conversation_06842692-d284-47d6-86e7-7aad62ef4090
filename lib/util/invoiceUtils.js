import moment from 'moment-timezone';
import { lineItemTypes, PLAN_TYPE } from '../constants/billingConstants';
import { LedgerDetailServiceUtils } from './ledgerDetailServiceUtils';
import { BillingUtils } from "./billingUtils";
import _ from './underscore';
import { CreditTypes } from '../constants/invoiceConstants';

// The purpose of this file it to start extracting logic from invoice collection methods so that they can be tested.
export class InvoiceUtils {
    /**
     * Get the mapped ledger account for a credit.
     *
     * @param { * } org
     * @param { * } credit
     * @param { * } invoice
     * @returns { { accountName: string | null, } }
     */
    static getMappedLedgerAccountForCredit(org, credit, invoice) {
        if (credit.type !== "refund") {
            if (credit.creditReason === "reallocation_to_payer" && credit.reversedAt && credit.reversedAmount) {
                return org.getMappedLedgerAccount(lineItemTypes.REALLOCATION_TO_PAYER_REVERSAL, credit);
            } else if (credit.payerOverpaymentAmount) {
                return org.getMappedLedgerAccount(lineItemTypes.PAYER_OVERPAYMENT, credit);
            }
            const item = LedgerDetailServiceUtils.creditItemGroup(credit, invoice, org, '', '');
            return org.getMappedLedgerAccount(item.lineItemType, credit);
        }

        if (LedgerDetailServiceUtils.isVoidedManualCreditInRange(credit, {})) {
            return org.getMappedLedgerAccount(lineItemTypes.PAYMENT_VOIDED, credit);
        }

        if (LedgerDetailServiceUtils.isVoidedOtherCreditInRange(credit, {})) {
            return org.getMappedLedgerAccount(lineItemTypes.PAYMENT_VOIDED, credit);
        }

        if (LedgerDetailServiceUtils.isRefundedManualCreditInRange(credit, {})) {
            return org.getMappedLedgerAccount(lineItemTypes.PAYMENT_MANUAL_REFUNDED, credit);
        }

        return { accountName: null };
    }

    /**
     * Get the mapped ledger account for a discount.
     *
     * @param { * } org
     * @param { * } discount
     * @param { * } lineItem
     * @returns { { accountName: string, } }
     */
    static getMappedLedgerAccountForDiscount(org, discount, lineItem) {
        return org.getMappedLedgerAccount(
            lineItem.type === PLAN_TYPE ? lineItemTypes.DISCOUNT_PLAN : lineItemTypes.DISCOUNT_ITEM,
            discount
        );
    }

    /**
     * Get the mapped ledger account for a line item.
     *
     * @param { * } org
     * @param { * } lineItem
     * @returns { { accountName: string, } }
     */
    static getMappedLedgerAccountForLineItem(org, lineItem) {
        if (lineItem.type === PLAN_TYPE) {
            return org.getMappedLedgerAccount(lineItemTypes.CHARGE_PLAN, lineItem);
        }
        return org.getMappedLedgerAccount(lineItemTypes.CHARGE_ITEM, lineItem);
    }

    /**
     * Calculates the payer distribution of a copay discount based on the provided options.
     * @param {object} options - Options for calculating payer distribution.
     * @param {number} [options.enrollmentDateValue] - Value representing the enrollment date.
     * @param {object} options.discount - Discount information.
     * @param {number} options.dailyRate - Daily rate for the discount.
     * @returns {Array} - Array of objects representing the payer distribution.
     */
    static payerDistributionOfDiscount(options) {
        // Attention: This method is intended for copay discounts and weekly_scheduled_daily type plans only.
        const { enrollmentDateValue, discount, dailyRate } = options;
        const aDate = discount.coveredDays && new moment(discount.coveredDays[0], "YYYY-MM-DD");

        if (!aDate) {
            return;
        }

        const periodStart = aDate.clone().startOf("week").day(1);
        const enrollmentDate = enrollmentDateValue && new moment(enrollmentDateValue);
        const monthSplit = new moment(periodStart).endOf("month");
        const beforeSplitCount = discount.coveredDays.filter(day => day <= monthSplit.format("YYYY-MM-DD")).length;
        const percentageBasedAmount = discount.originalAllocation?.amountType === 'percent';
        const afterEnrollmentDate = (!enrollmentDateValue || periodStart >= enrollmentDate);
        let familyAmount = 0.0; // This is the amount the family pays as a copay.
        if (afterEnrollmentDate) {
            if (percentageBasedAmount) {
                familyAmount = (discount.originalAllocation?.amount / 100) * (dailyRate * beforeSplitCount);
            } else {
                familyAmount = discount.originalAllocation?.amount;
            }
        }

        let beforeSplitRate = (dailyRate * beforeSplitCount - familyAmount) / beforeSplitCount; // This is the amount the payer pays minus the family copay.


        if (beforeSplitRate < 0) {
            beforeSplitRate = 0;
        }

        const afterSplitRate = dailyRate;

        const outputDays = [];
        discount.coveredDays.forEach( coveredDay => {
            const dailySubsidyRate = coveredDay <= monthSplit.format("YYYY-MM-DD") ? beforeSplitRate : afterSplitRate;
            const dayInfo = {
                day: coveredDay,
                dailySubsidyRate,
                dailyRate,
                dailyCopayRate: dailyRate - dailySubsidyRate
            };
            outputDays.push(dayInfo);
        });

        return outputDays;
    }

    /**
     * Calculates the amount due for a specific family member on an invoice.
     *
     * @param {Object} invoice - The invoice object containing details of the transaction.
     * @param {string} familyPersonId - The ID of the family member whose share is to be calculated.
     * @param {number|null} throughDateStamp - A timestamp to filter credits up to a specific date, or null for no filtering.
     * @returns {number} - The calculated amount due for the given family member.
     */
    static amountDueForFamilyMember(invoice, familyPersonId, throughDateStamp = null) {
        // Get the family splits or default to an empty object if none are found
        const familySplits = this.getFamilySplits(invoice) || {};

        // Calculate other credits, excluding certain types and conditions
        const otherCredits = _.chain(invoice.credits)
            .filter((c) => {
                return c.type !== "refund" &&
                    c.type !== "chargeback" &&
                    c.type !== "payment" &&
                    c.type !== "reimbursement" &&
                    c.creditReason !== "reimbursable" &&
                    !(c.type === "credit" && (c.paidBy || c.creditReason === "manual_payment")) &&
                    !(c.type === "credit" && c.creditReason === "security_deposit_refund") &&
                    (!throughDateStamp || c.createdAt <= throughDateStamp);
            })
            .reduce((memo, c) => {
                return memo + c.amount;
            }, 0.00)
            .value();

        // Calculate the adjusted total amount
        const adjustedTotalAmount = invoice.originalAmount - otherCredits;

        // Calculate the individual share based on the adjusted total amount
        const individualShare = this.calculateIndividualShare(adjustedTotalAmount, familySplits, familyPersonId);

        // Calculate the total payments made by the family member
        const familyPersonPayments = this.amountPaidByFamilyMember(invoice, familyPersonId, throughDateStamp);

        // Return the final amount due for the family member, rounded to two decimal places
        return BillingUtils.roundToTwo(individualShare - familyPersonPayments);
    }

    /**
     * Retrieves the credits (payments, credits, refunds) associated with a family member for a given invoice.
     *
     * @param {Object} invoice - The invoice object containing details of the transaction.
     * @param {string} familyPersonId - The ID of the family member whose credits are to be retrieved.
     * @param {boolean} includeRefunds - Whether to include refunds in the retrieved credits.
     * @returns {Array} - The array of credits associated with the family member.
     */
    static creditsForFamilyMember(invoice, familyPersonId, includeRefunds) {
        let familyPersonShare = 1.0;

        // Determine the share of the family member based on family splits
        if (InvoiceUtils.getFamilySplits(invoice)) {
            const shareAmount = InvoiceUtils.getFamilySplits(invoice)[familyPersonId] || 0;
            familyPersonShare = shareAmount / 100.0;
        }

        // Filter credits based on the type and family member's share
        return _.chain(invoice.credits)
            .filter(c => {
                return (
                    (c.type === "payment" && (familyPersonShare === 1 || c.paidBy === familyPersonId)) ||
                    (c.type === "credit" && ((!c.paidBy && c.creditReason === "manual_payment" && familyPersonShare === 1) ||
                        (c.paidBy && (familyPersonShare === 1 || c.paidBy === familyPersonId)))) ||
                    (includeRefunds && c.type === "refund" && (familyPersonShare === 1 || c.paidBy === familyPersonId))
                );
            })
            .value();
    }

    /**
     * Calculates the total amount paid by a family member for a given invoice, taking into account refunds and chargebacks.
     *
     * @param {Object} invoice - The invoice object containing details of the transaction.
     * @param {string} familyPersonId - The ID of the family member whose payments are to be calculated.
     * @param {number|null} throughDateStamp - A timestamp to filter credits up to a specific date, or null for no filtering.
     * @returns {number} - The total amount paid by the family member.
     */
    static amountPaidByFamilyMember(invoice, familyPersonId, throughDateStamp) {
        const familyPersonCredits = this.creditsForFamilyMember(invoice, familyPersonId, false)
            .filter(credit => !throughDateStamp || credit.createdAt <= throughDateStamp);

        // Collect refundable payment IDs
        const refundableChargeIds = familyPersonCredits
            .filter(credit => credit.type === CreditTypes.PAYMENT && credit.adyenInfo)
            .map(credit => {
                const rawRef = credit.adyenInfo?.merchantReference || credit.adyenInfo?.reference;
                if (!rawRef) return null;
                return rawRef.includes("_") ? rawRef.split("_")[0] : rawRef;
            })
            .filter(Boolean);

        const refundCredits = (invoice.credits || []).filter(credit =>
            credit.type === CreditTypes.REFUND &&
            (
                (credit.adyenInfo?.additionalData?.merchantReference && refundableChargeIds.includes(credit.adyenInfo.additionalData.merchantReference)) || // legacy
                (credit.adyenInfo?.reference && refundableChargeIds.includes(credit.adyenInfo.reference)) // new
            )
        );

        const chargebackableChargeIds = familyPersonCredits
            .filter(credit => credit.type === CreditTypes.PAYMENT && credit.adyenInfo?.pspReference)
            .map(credit => credit.adyenInfo.pspReference);

        const chargebackCredits = (invoice.credits || []).filter(credit =>
            credit.type === CreditTypes.CHARGEBACK &&
            credit.adyenInfo?.originalReference &&
            chargebackableChargeIds.includes(credit.adyenInfo.originalReference)
        );

        const paymentsTotal = familyPersonCredits.reduce((sum, credit) => sum + credit.amount, 0);
        const refundsTotal = refundCredits.reduce((sum, credit) => sum + credit.amount, 0);
        const chargebacksTotal = chargebackCredits.reduce((sum, credit) => sum + credit.amount, 0);

        return BillingUtils.roundToTwo(paymentsTotal - refundsTotal - chargebacksTotal);
    }

    /**
     * Calculates the individual share of the total amount for a given family member.
     *
     * @param {number} totalAmount - The total amount to be split among family members.
     * @param {Object} familySplits - An object representing the split percentages for each family member.
     * @param {string} familyPersonId - The ID of the family member whose share is to be calculated.
     * @returns {number} - The calculated share of the total amount for the given family member.
     */
    static calculateIndividualShare(totalAmount, familySplits, familyPersonId) {
        // Extract split values from the familySplits object
        const splitValues = Object.values(familySplits);

        // Calculate the initial split amounts based on the percentages
        const splitAmounts = splitValues.map((splitValue) => BillingUtils.roundToTwo(totalAmount * (splitValue / 100)));

        // Calculate the total of the split amounts
        const totalSplitAmounts = splitAmounts.reduce((memo, num) => BillingUtils.roundToTwo(memo + num), 0.0);

        // If there is a discrepancy between the total split amounts and the total amount, adjust the split amounts
        if (totalSplitAmounts !== totalAmount) {
            let difference = BillingUtils.roundToTwo(totalAmount - totalSplitAmounts);

            if (totalSplitAmounts > totalAmount) {
                // If the split amounts exceed the total amount, decrease the shares starting from the last person
                for (let i = splitAmounts.length - 1; i >= 0 && difference < 0; i--) {
                    const adjustment = BillingUtils.roundToTwo(Math.min(splitAmounts[i], Math.abs(difference)));
                    splitAmounts[i] = BillingUtils.roundToTwo(splitAmounts[i] - adjustment);
                    difference = BillingUtils.roundToTwo(difference + adjustment);
                }
            } else {
                // If the split amounts are less than the total amount, increase the shares starting from the first person
                for (let i = 0; i < splitAmounts.length && difference > 0; i++) {
                    const adjustment = BillingUtils.roundToTwo(Math.min(splitAmounts[i], difference));
                    splitAmounts[i] = BillingUtils.roundToTwo(splitAmounts[i] + adjustment);
                    difference = BillingUtils.roundToTwo(difference - adjustment);
                }
            }
        }

        // Find the index of the familyPersonId in the familySplits keys
        const familyMembers = Object.keys(familySplits);
        const position = familyMembers.indexOf(familyPersonId);

        // Return the amount for the specific position, rounded to two decimal places
        return BillingUtils.roundToTwo(splitAmounts[position] ?? totalAmount);
    }

    /**
     * Get the family splits for an invoice, if available.
     *
     * @param {Object} invoice - The invoice for which to retrieve family splits.
     * @returns {Object | null} The family splits for the invoice, or null if not available.
     */
    static getFamilySplits(invoice){
        if (invoice.familySplits) {
            return _.isEmpty(invoice.familySplits) ? null : invoice.familySplits;
        }

        return null;

    }

    /**
     * Calculates the merchant fee based on the account type and plan details.
     *
     * @param {string} accountType - The type of account, either "bank_account" or "card".
     * @param {Object} plan - The billing plan object containing the calculated amount.
     * @param {Object} orgPaymentFees - The organization's payment fees configuration.
     * @param {number} [orgPaymentFees.cardFee=0.30] - The fixed fee for card transactions.
     * @param {number} [orgPaymentFees.cardRate=0.029] - The percentage rate for card transactions.
     * @param {number} [orgPaymentFees.achFee=0.40] - The fixed fee for ACH transactions.
     *
     * @returns {number} - The calculated merchant fee, rounded to two decimal places.
     */
    static calculateMerchantFee(accountType, plan, orgPaymentFees) {
        const cardFee = (orgPaymentFees && orgPaymentFees.cardFee != null) ? orgPaymentFees.cardFee : 0.30;
        const cardRate = (orgPaymentFees && orgPaymentFees.cardRate) || 0.029;
        const achFee = (orgPaymentFees && orgPaymentFees.achFee != null) ? orgPaymentFees.achFee : 0.40;
        const fee = accountType === "bank_account" ? achFee : (parseFloat(plan.calculatedAmount) * cardRate) + cardFee;
        return parseFloat(fee.toFixed(2));
    }

    /**
     * Calculate the plan total for lineItem from invoice
     * 
     * @param {*} lineItem 
     * @returns 
     */
    static getPlanAmountTotal(lineItem){
        let total = 0;
		total = lineItem.amount;
		if (lineItem?.appliedDiscounts?.length > 0) {
			lineItem.appliedDiscounts.forEach(discount => {
				total -= discount.amount;
			});
		}
		return total;
    }
}