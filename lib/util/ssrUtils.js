import { SpacebarsCompiler } from "meteor/spacebars-compiler";
import { Blaze } from "meteor/blaze";

export class SSR {
    static render(templateName, data) {
        const renderFunc = (data) ? Blaze.toHTMLWithData : Blaze.toHTML;
        const template = Blaze.Template[templateName];
        if (!template) {
            throw new Error(`Template ${templateName} not found`);
        }
        else {
            return renderFunc(template, data);
        }
    }
    static compileTemplate(name, content) {
        const renderfunc = eval(`(function(view) { return ${SpacebarsCompiler.compile(content)}(); })`);
        const template = new Blaze.Template(name, function () { return renderfunc(this); });
        Blaze.Template[name] = template;
        return template;
    }
}