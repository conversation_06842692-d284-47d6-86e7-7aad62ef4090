import { Groups } from './collections/groups';
import { Orgs } from './collections/orgs';
import { People } from './collections/people';

export const recalculateGroupMediaDesignation = async function(groupId, org = null) {
  const group = await Groups.findOneAsync({_id: groupId});
  if (!group) return;
  const orgId = group.orgId;
  let mediaReviewRequired = false;
  const mediaReviewOrgWide = (org || await Orgs.current()).mediaRequirement?.mediaReviewRequired;
  const people = await People.find({orgId, checkInGroupId: groupId, checkedIn: true, type: "person" }).fetchAsync();
  for (const p of people) {
      if (mediaReviewOrgWide == "Yes") {
        mediaReviewRequired = true;
        break
      }
    if (p?.mediaRequirements?.mediaReviewRequired == "Yes" || p?.profileData?.mediaRequirements?.mediaReviewRequired == "Yes") {
      mediaReviewRequired = true;
      break
    }
  }

  await Groups.updateAsync({_id: groupId}, {$set: {mediaReviewRequired: mediaReviewRequired}});
}

/*
 returns object: CommonData, momentData
*/
const moment = require('moment-timezone');
export const processMomentAndCommonData = async function(momentData, editMomentStatus) {
  var commonData = {};
  commonData.attributionPersonId = momentData.attributionPersonId || null;
  if (momentData.metaMomentId) commonData.metaMomentId = momentData.metaMomentId;

  var timezone = (await Orgs.current()).getTimezone();
  commonData.momentType = momentData.momentType;
  if (momentData.comment) commonData.comment = momentData.comment;
  
  if (momentData.time) {
    commonData.time = momentData.time;
  } else {
    commonData.time = new moment().tz(timezone).format("h:mm a");
  }
  
  if (momentData.date) commonData.date = momentData.date;
  
  if (!commonData.date && !editMomentStatus) {
    commonData.date = new moment().tz(timezone).format("MM/DD/YYYY");
  }
  if (commonData.date && commonData.time) {
    commonData.sortStamp = new moment.tz(commonData.date + " " + commonData.time, "MM/DD/YYYY h:mm a", timezone).valueOf();    
  }
  
  if (momentData.isDemo) commonData.isDemo = true;
  if (momentData.tagAllInGroup) momentData.taggedPeople = ["group|" + momentData.tagGroupId];
  if (momentData.tagEntireOrg) momentData.taggedPeople = ["org|org"];
  
  if (!momentData.taggedPeople)  throw new Meteor.Error(500, "At least one person must be tagged.");
  if (momentData.momentType != "alert") momentData.tagOnlyCheckins = true;
  
  return { momentData, commonData };
}

//This method is only used at client side hence async await not needed
export const isCheckinRequired = function () {
  const currentUser = Meteor.user();
  const currentPerson = (currentUser) ? currentUser.fetchPerson() : null;
  const org = Orgs.current();
  // if checkins are required and user is not checked in throw error
  return org && org.hasCustomization("moments/staffRequiredCheckin/enabled") && !currentPerson.checkedIn;
}
