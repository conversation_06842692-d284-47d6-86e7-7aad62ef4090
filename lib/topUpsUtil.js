export class TopUpsUtil {
    static transformMarketplaceRecord(data, response) {
        if (!data) {
            return response;
        }
        // Check if record relates to a topup. Column 13 is description, column 6 is pspMerchantReference
        // This code will be a lot nicer when we have parsed adyen report data.
        if (data[12]?.match(/^TopUp/) || data[5].match(/Negative balance coverage/i)) {
            response.type = 'Balance Adjustment: ' + response.type;
            response.desc = 'Balance Adjustment: Transaction resolved negative balance in payment account ' +
                'due to refunds, chargebacks, and other payment error.';
        }
        return response;
    }
}