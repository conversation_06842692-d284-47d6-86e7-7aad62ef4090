/**
 * The date types for billing.plansAndItems.type === 'item' that have specified dates.
 *
 * INDIVIDUAL_DATES: Separate dates.
 * DATE_RANGE: A range of dates.
 * RECURRING: A set of dates in a week that repeat.
 */
export const ItemDateTypes = {
    DATE_RANGE: 'dateRange',
    INDIVIDUAL_DATES: 'individualDates',
    RECURRING: 'recurring'
}
Object.freeze(ItemDateTypes);

/**
 * The name of the identifier for item in billing.plansAndItems.type.
 */
export const ITEM_TYPE = 'item';
/**
 * The name of the identifier for bundles in billing.plansAndItems.type.
 */
export const PLAN_BUNDLE_TYPE = 'bundle';
/**
 * The name of the identifier for bundles in billing.plansAndItems.type.
 */
export const PLAN_TYPE = 'plan';
/**
 * The name of the identifier for scaled pricing plans that are invoiced biweekly (every other week).
 */
export const SCALED_BIWEEKLY_PLAN = 'scaledBiweekly';
/**
 * The name of the identifier for scaled pricing plans that are invoiced monthly.
 */
export const SCALED_MONTHLY_PLAN = 'scaledMonthly';
/**
 * The name of the identifier for scaled pricing plans that are invoiced weekly.
 */
export const SCALED_WEEKLY_PLAN = 'scaledWeekly';
/**
 * The name of the identifier for punch cards in billing.plansAndItems.type
 */
export const PUNCH_CARD_TYPE = 'punchcard';

export const REGISTRATION_FEE = 'Registration Fee';

export const CreditReasons = {
    AGENCY_WRITE_OFF: 'agency_write_off',
    BAD_DEBT: 'bad_debt',
    COLLECTIONS_WRITE_OFF: 'collections_write_off',
    PAYROLL_DEDUCTION: 'payrollDeduction',
    OTHER: 'other'
}
Object.freeze(CreditReasons);

export const ledgerAccountTypes = {
    CREDIT: 'credit',
    CREDIT_MODIFIED: 'modifiedCredit',
    CREDIT_MEMO: 'creditMemos',
    CREDIT_MEMO_REFUNDED: 'creditMemoRefunded',
    CREDIT_MEMO_VOID: 'creditMemoVoid',
    DISCOUNT: 'discounts',
    DISCOUNT_MODIFIED: 'modifiedDiscount',
    MANUAL_DEPOSITS: 'manualDeposits',
    PAYER_OVERPAYMENT: 'payerOverpayment',
    PAYER_OVERPAYMENT_CREDIT_MEMO: 'payerOverpaymentCreditMemo',
    PAYMENT: 'payment',
    PAYMENT_ADJUSTED: 'adjustedPayment',
    PAYMENT_CREDIT_MEMO: 'paymentCreditMemo',
    PAYMENT_MANUAL: 'paymentManual',
    PAYMENT_MANUAL_REFUNDED: 'refundedManualPayment',
    PAYMENT_PAYER: 'paymentPayer',
    PAYMENT_REFUNDED: 'paymentRefunds',
    PAYMENT_SETTLEMENT_CASH: 'paymentSettlementCash',
    PAYMENT_SETTLEMENT_FEE: 'paymentSettlementFees',
    BANK_WITHDRAWAL: 'bankWithdrawal',
    PAYMENT_VOIDED: 'voidedPayments',
    PAYROLL_DEDUCTION: 'payrollDeduction',
    REALLOCATION_TO_PAYER: 'reallocationToPayer',
    REALLOCATION_TO_PAYER_REVERSAL: 'reversedReallocationToPayer',
    REVENUE: 'revenue',
    SECURITY_DEPOSIT_APPLIED: 'securityDepositApplied',
    SECURITY_DEPOSIT_REFUND: 'securityDepositRefund',
    SECURITY_DEPOSIT_REFUND_AUTO: 'securityDepositRefundAuto',
    SECURITY_DEPOSIT_LIABILITY: 'securityDepositLiability',
    SECURITY_DEPOSIT_FORFEITURE: 'securityDepositForfeiture',
    WRITE_OFF_AGENCY: 'agencyWriteOff',
    WRITE_OFF_AGENCY_REVERSAL: 'agencyWriteOffReversal',
    WRITE_OFF_BAD_DEBT: 'badDebt',
    WRITE_OFF_BAD_DEBT_REVERSAL: 'badDebtReversal',
    WRITE_OFF_COLLECTIONS: 'collectionsWriteOff',
    WRITE_OFF_COLLECTIONS_REVERSAL: 'collectionsWriteOffReversal',
    UNAPPLIED_CASH_APPLIED: 'unappliedCashApplied',
}
Object.freeze(ledgerAccountTypes);

export const lineItemTypes = {
    BANK_WITHDRAWAL: 'bankWithdrawal',
    CHARGE_ITEM: 'itemCharge',
    CHARGE_PLAN: 'planCharge',
    CHARGEBACK_FEE: 'chargebackFee',
    CREDIT: 'credit',
    CREDIT_MEMO: 'creditMemo',
    CREDIT_MEMO_REFUNDED: 'creditMemoRefunded',
    CREDIT_MEMO_VOID: 'creditMemoVoid',
    CREDIT_MODIFIED: 'modifiedCredit',
    DISCOUNT_ITEM: 'itemDiscount',
    DISCOUNT_PLAN: 'planDiscount',
    MANUAL_DEPOSITS: 'manualDeposit',
    PAYER_OVERPAYMENT: 'payerOverpayment',
    PAYER_OVERPAYMENT_CREDIT_MEMO: 'payerOverpaymentCreditMemo',
    PAYMENT: 'payment',
    PAYMENT_ADJUSTED: 'adjustedPayment',
    PAYMENT_MANUAL_REFUNDED: 'refundedManualPayment',
    PAYMENT_REFUNDED: 'refund',
    PAYMENT_VOIDED: 'voidedPayment',
    PAYROLL_DEDUCTION: 'payrollDeduction',
    REALLOCATION_TO_PAYER_REVERSAL: 'reversedReallocationToPayer',
    SECURITY_DEPOSIT_APPLIED: 'securityDepositApplied',
    SECURITY_DEPOSIT_REFUND_AUTO: 'securityDepositRefundedAuto',
    SETTLEMENT: 'settlement',
    SETTLEMENT_FEE: 'settlementFee',
    WRITE_OFF_AGENCY: 'allocationChangeAgency',
    WRITE_OFF_AGENCY_REVERSAL: 'reversedAllocationChangeAgency',
    WRITE_OFF_BAD_DEBT: 'allocationChangeBadDebt',
    WRITE_OFF_BAD_DEBT_REVERSAL: 'reversedAllocationChangeBadDebt',
    WRITE_OFF_COLLECTIONS: 'allocationChangeCollections',
    WRITE_OFF_COLLECTIONS_REVERSAL: 'reversedAllocationChangeCollections',
    UNAPPLIED_CASH_APPLIED: 'unappliedCashApplied',
    SECURITY_DEPOSIT_LIABILITY: 'securityDepositLiability',
    SECURITY_DEPOSIT_FORFEITURE: 'securityDepositForfeiture',
}
Object.freeze(lineItemTypes);
export const BillingFrequencies = {
    DAILY: 'daily',
    WEEKLY: 'weekly',
    SCALED_WEEKLY: 'scaledWeekly',
    WEEKLY_SCHEDULED_DAILY: 'weekly_scheduled_daily',
    BIWEEKLY: 'biweekly',
    SCALED_BIWEEKLY: 'scaledBiweekly',
    SEMIMONTHLY: 'semimonthly',
    MONTHLY: 'monthly',
    SCALED_MONTHLY: 'scaledMonthly',
    BIMONTHLY: 'bimonthly',
    DAILY_CHARGED_MONTHLY: 'charged_daily_invoiced_monthly',
};
Object.freeze(BillingFrequencies);
/**
 * A list of the identifiers for scaled plans.
 */
export const SCALED_PLAN_FREQUENCIES = [BillingFrequencies.SCALED_BIWEEKLY, BillingFrequencies.SCALED_MONTHLY, BillingFrequencies.SCALED_WEEKLY];

export const PLANS_CHARGED_BY_CHECKINS = [BillingFrequencies.DAILY_CHARGED_MONTHLY, BillingFrequencies.WEEKLY_SCHEDULED_DAILY];

export const BILLING_ADMIN_PLANS_PEOPLE = 'billingAdminPlansPeople';
export const AVAILABLE_FAMILY_MEMBERS_DATA = 'availableFamilyMembersData';

export const MANUAL_PAYMENTS = Object.freeze({
    TYPES: [
        {
            value: 'cash',
            text: 'Cash'
        },
        {
            value: 'check',
            text: 'Check'
        },
        {
            value: 'credit_card',
            text: 'Credit Card'
        },
        {
            value: 'ach',
            text: 'ACH'
        },
        {
            value: 'payroll_deduction',
            text: 'Payroll Deduction'
        },
    ]
});

export const PaymentSources = {
    CARD: 'card',
    BANK_ACCOUNT: 'bank_account',
}

export const DefaultOrgBillingMaps = {
    accountsReceivable: { "accountName": null, "description": "Accounts Receivable" },
    agencyOverpayment: { "agencyOverpayment": null, "description": "Agency Overpayment Adjustment" },
    creditMemos: { "accountName": null, "description": "Credit Memos" },
    customerLiabilityPayable: { "accountName": null, "description": "Customer Liability/Payable" },
    onlinePaymentDeposits: { "accountName": null, "description": "Electronic Payment Deposits" },
    manualOtherCredits: { "accountName": null, "description": "Manual and Other Credits" },
    manualPaymentDeposits: { "accountName": null, "description": "Manual Cash and Payment Deposits" },
    otherPlanDiscounts: { "accountName": null, "description": "Other Plan Discounts (Default)" },
    otherPayerDiscounts: { "accountName": null, "description": "Payer Discounts (Default)" },
    settlementFees: { "accountName": null, "description": "Payment Settlement Fees" },
    payrollDeduction: { "accountName": null, "description": "Payroll Deduction" },
    securityDepositsLiability: { "accountName": null, "description": "Security Deposit Liability/Offset" },
    securityDepositRefunds: { "accountName": null, "description": "Security Deposit Refunds (Legacy)" },
    securityDepositForfeiture: {
        "accountName": null,
        "description": "Security Deposit Forfeiture - Misc. Rev"
    },
    unappliedCash: { "accountName": null, "description": "Unapplied Cash" },
    unappliedCashApplied: { "accountName": null, "description": "Unapplied Cash Applied" },
    undepositedFunds: { "accountName": null, "description": "Undeposited Funds" },
    agencyWriteOff: { "accountName": null, "description": "Write Off - Agency" },
    badDebt: { "accountName": null, "description": "Write Off - Bad Debt" },
    collectionsWriteOff: { "accountName": null, "description": "Write Off - Collections" },
}

export const DefaultDiscountTypes = [
    { type: "customerSpecific", description: "Customer-specific" },
    { type: "multipleFamily", description: "Multiple family" },
    { type: "scholarship", description: "Scholarship" },
    { type: "other", description: "Other" }
]

export const DefaultPayerSources = [
    { type: "ccdf", description: "CCDF" },
    { type: "onmywayprek", description: "On My Way Pre-K" },
    { type: "unitedway", description: "United Way" }
]
