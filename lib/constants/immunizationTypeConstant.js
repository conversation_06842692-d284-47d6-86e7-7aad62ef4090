/**
 * Immunization types with it's description and month's required data. 
*/
export const defaultImmunizationTypes = [
    {
      type: "HepB",
      description: "Hepatitis B",
      monthsRequired: [0, 2, 18],
      exempt: false,
    },
    {
      type: "RV",
      description: "Rotavirus",
      monthsRequired: [2, 4, 6],
      exempt: false,
    },
    {
      type: "DTaP",
      description: "Diptheria, tetanus, & acellular pertussis",
      monthsRequired: [2, 4, 6, 18, 72],
      exempt: false,
    },
    {
      type: "Hib",
      description: "Haemophilus influenzae type b",
      monthsRequired: [2, 4, 6, 15],
      exempt: false,
    },
    {
      type: "PCV13",
      description: "Pneumococcal conjugate",
      monthsRequired: [2, 4, 6, 15],
      exempt: false,
    },
    {
      type: "IPV",
      description: "Inactivated poliovirus",
      monthsRequired: [2, 4, 18, 72],
      exempt: false,
    },
    {
      type: "IIV",
      description: "Influenza",
      monthsRequired: [6, 18, 30, 42, 54, 66, 78, 90, 102],
      exempt: false,
    },
    {
      type: "MMR",
      description: "Measles, mumps, rubella",
      monthsRequired: [15, 72],
      exempt: false,
    },
    {
      type: "VAR",
      description: "Varicella",
      monthsRequired: [15, 72],
      exempt: false,
    },
    {
      type: "HepA",
      description: "Hepatitis A",
      monthsRequired: [23, 23],
      exempt: false,
    },
    { type: "MenACWY", description: "Meningococcal", exempt: false },
    {
      type: "PPSV23",
      description: "Pneumococcal polysaccharide",
      exempt: false,
    },
    { type: "UCHR", description: "UCHR", exempt: false },
    { type: "H1N1", description: "H1N1", exempt: false },
];
  
  
  