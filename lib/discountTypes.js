/**
 * Discount types constant
 *
 * @type {{COUPON: string}}
 * @type {{EMPLOYEE: string}}
 * @type {{BUNDLE: string}} {
 */
export const DiscountTypes = {
    COUPON: 'coupon',
    RAS_EMPLOYEE: 'rasEmployeeDiscount',
    STAFF: 'staffDiscount',
    BUNDLE: 'bundle',
    SIBLING: 'siblingDiscount',
    DISTRICT: 'districtDiscount'
};
Object.freeze(DiscountTypes)

export const AllocationTypes = {
    COUPON: 'coupon',
    DISCOUNT: 'discount',
    SUBSIDY: 'reimbursable',
    COPAY: 'reimbursable-with-copay',
    FAMILY: 'family'
}
Object.freeze(AllocationTypes)

export const AllocationAmountTypes = {
    DOLLARS: 'dollars',
    PERCENT: 'percent'
}
Object.freeze(AllocationAmountTypes)