export const translationsEnLightbridge = {
	"en": {
		"momentTypes": {
			"potty": {
				"name": "potty",
				"prettyName": "Toileting",
				"pottyTypeLabels": {
					"Wet": "Wet",
					"BM": "BM",
					"WetBM": "Wet + BM",
					"Dry": "Dry"
				}
			},
			"food": {
				"foodTypeLabels": {
					"All": "All",
					"Most": "Most",
					"Some": "Some",
					"None": "None",
					"NotOffered": "Not Offered"
				}
			}
		},
		"entityTypes": {
			"group": "Group",
			"people": "Children",
			"person": "Child",
			"staff": "Staff",
			"family": "Family",
			"admin": "Administrator",
			"prospect": "Prospect",
			"prospects": "Prospects",
			"Prospect Family": "Prospect Family",
			"user": "User",
			"vendor": "Vendor",
			"serviceAccount": "Service Account"
		},
		"activities": {
			"tag": "Learning Center",
			"tags": "Learning Centers",
			"standard": "Milestone",
			"standards": "Milestones"
		}
	}
};
