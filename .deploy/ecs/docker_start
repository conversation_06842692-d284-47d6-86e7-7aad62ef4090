#!/bin/bash
set -e

npm install shell-escape

# Function to fetch and update AppConfig using appconfig_helper
fetch_and_update_appconfig() {
    echo "Running appconfig_helper to fetch AppConfig settings..."
    ./appconfig_helper
    if [ $? -ne 0 ]; then
        echo "Error: appconfig_helper script failed."
        exit 1
    fi
    echo "AppConfig settings fetched successfully."
}

# Function to generate env.txt using build_env_file.js
generate_env_file() {
    echo "Running build_env_file.js to convert settings.json to env.txt..."
    node build_env_file.js
    if [ $? -ne 0 ]; then
        echo "Error: build_env_file.js script failed."
        exit 1
    fi
    echo "Environment variables file generated successfully."
}

# Main Execution
echo "Starting Docker initialization script..."

# Step 1: Fetch and update AppConfig settings
fetch_and_update_appconfig

# Step 2: Generate env.txt file
generate_env_file

# Reload environment variables from env.txt
echo "Reloading environment variables..."
source env.txt

# Pre-launch checks for Meteor application
echo "Node version:"
echo "$(node --version)"
echo "Npm version:"
echo "$(npm --version)"

# Launch the application
echo "Launching the application..."
export METEOR_SETTINGS=$(node -e 'console.log(decodeURIComponent(process.env.METEOR_SETTINGS_ENCODED))')
if [ -z "$METEOR_SETTINGS" ]; then
    echo "Error: METEOR_SETTINGS is not set."
    exit 1
fi

# Launch the Meteor application
node --max-old-space-size=$ECS_RAM_SIZE /bundle/main.js