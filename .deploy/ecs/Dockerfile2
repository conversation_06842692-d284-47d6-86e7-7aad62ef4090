FROM 378764455794.dkr.ecr.us-east-1.amazonaws.com/manage-nodejs:latest

# Install system dependencies for building native modules
RUN apt-get update && apt-get install -y \
    build-essential \
    python3 \
    python3-pip \
    g++ \
    make

# Copy the entire project into the /bundle directory
COPY . /bundle

# Set the working directory
WORKDIR /bundle/programs/server

# Clear npm cache
RUN npm cache clean --force

# Install dependencies with workaround for peer dependencies
RUN npm install -g npm@latest --force

# Install AWS CLI
RUN apt-get update && apt-get install -y \
    unzip \
    curl \
    jq && \
    curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip ./aws && \
    apt-get clean && rm -rf /var/lib/apt/lists/* && \
    jq --version && \
    aws --version

# Set environment variables
ENV PORT=3000

# Expose the port your application listens on
EXPOSE 3000

# Define the command to start your application
CMD ["/bundle/docker_start"]
