// TODO: this is running on server boot :-/

const shellEscape = require('shell-escape');
const fs = require('fs');

const readJsonFile = function(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const parsedJson = JSON.parse(fileContent);
    return parsedJson;
  } catch (error) {
    console.error('Error reading JSON file:', error);
    return null;
  }
}

const createEnvContent = function(env, settings) {
  let content = '';
  const settingsString = encodeURIComponent(JSON.stringify(settings));

  Object.keys(env).forEach((key) => {
    const value = shellEscape([env[key]]);
    content += `export ${key}=${value}\n`;
  });

  content += `export METEOR_SETTINGS_ENCODED=${shellEscape([settingsString])}`;
  return content
}

const saveStringAsFile = function(filePath, content) {
  try {
    fs.writeFileSync(filePath, content);
    console.log('File saved successfully:', filePath);
  } catch (error) {
    console.error('Error saving file:', error);
  }
}


const jsonSettings = readJsonFile('settings.json');
const envContent = createEnvContent(jsonSettings.ENV_VARS, jsonSettings);
saveStringAsFile("env.txt", envContent);
console.log("done");
