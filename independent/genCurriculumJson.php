<?php
// generates json file for curriculum, pass csv as first argument, creates corresponding json file

function camelCase(string $str) {
    return lcfirst(str_replace(' ', '', ucwords(strtolower($str))));
}


$fileName = $_SERVER['argv'][1] ?? null;

if (!$fileName) {
    die('Please pass a file name as the first argument' . PHP_EOL);
}

$fh = fopen($fileName, 'r');
if (!$fh) {
    die('Could not open file' . PHP_EOL);
}
$headers = fgetcsv($fh);
foreach ($headers as $idx => $header) {
    $headers[$idx] = camelCase($header);
}
$rows = [];
while ($raw = fgetcsv($fh)) {
    $row = [];
    foreach ($raw as $idx => $val) {
        $row[$headers[$idx]] = trim($val);
    }
    $rows[] = $row;
}
fclose($fh);
$outputFh = fopen(basename($fileName, '.csv') . '.json', 'w');
fwrite($outputFh, json_encode($rows, JSON_PRETTY_PRINT));
fclose($outputFh);
