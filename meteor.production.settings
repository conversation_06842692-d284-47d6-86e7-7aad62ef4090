{"awsBucket": "my-example-staging", "AWSAccessKeyId": "********************", "AWSSecretAccessKey": "DE5s4ImtkbQEaYR/dp4hd0XTNXRVPI3I76ChS5VM", "mpAWSaccessKey": "********************", "mpAWSsecretKey": "YOcx/Up+rd7LeaUTdBSF0jLzxZ3YgfUu5R3YTKNi", "cognitoUserPoolId": "us-east-2_xUMszrffZ", "cognitoAccessKey": "********************", "cognitoSecretKey": "ouAIapdZkkIwZlaQuoaadHKtvnztP2ErLy/XTKrW", "cognitoClientId": "4qos7puu57boc5c5t8onqlj9la", "cognitoEndpoint": "", "cognitoRegion": "us-east-2", "mpEntityKey": "ztSnTCEW92Q5zvXrjMkVKzdHsOcLsP55", "mailchimpKey": "*************************************", "mpSMSQueueUrl": "https://sqs.us-east-1.amazonaws.com/799420973518/production_multi_longcode_sms.fifo", "mpEventBridgeQueueUrl": "https://sqs.us-east-1.amazonaws.com/799420973518/production_eventbridge.fifo", "mpMomentEventBridgeQueueUrl": "https://sqs.us-east-1.amazonaws.com/799420973518/production_moments_eventbridge.fifo", "wmgPresenceUrl": "https://presence.internal.watchmegrow.com", "mpEventBridgeMessageGroup": "production", "defaultOriginationNumber": "18336591278", "zkTecoUrl": "", "zkTecoApiToken": "", "adpEmail1": "<EMAIL>", "adpEmail2": "<EMAIL>", "childcareCrmUrl": "https://live.childcarecrm.com", "childcareCrmUIUrl": "https://my.childcarecrm.com/#", "idpBaseUrl": "https://login.lineleader.com", "idpClientSecret": "262e854399a9dde35d6449d68f92cd6a86946afc", "kinderConnectUrl": "https://ccbis.mo.gov/WSCtecMobile/CtecMobileService.svc", "rightAtSchoolUrl": "https://hook.us1.make.celonis.com/atx34a7s1etoquttxm3q43jekc6q712w", "rightAtSchoolWebhookUrl": "https://hook.us1.make.celonis.com/oud9ylqojv6sg1k4xf1pwebrkepuovh5", "mobileApiKey": "8tsD8v7q4ndpkMryhFJ9Hi8AZfKAsKr6jdz8NCZxPHvmBVw9oUXvLpZCGEy6uCdN8nk778JvTFWTUXy6MbayvucnvGLMDrbGgAZG", "intercomSecret": "m5bqgfj4avMu_7CAaW38x3WWuftGIZ7DjM5x9Qn1", "lspHubspotKey": "********************************************", "public": {"supportSite": "https://help-beta.momentpath.com/knowledge", "AWSAccessKeyId": "********************", "stripe": {"publishableKey": "pk_live_LBNK7nn8QtDyEMQWVliBDl2m"}, "awsBucketUrl": "https://my-meteor-example.s3.amazonaws.com", "environment": "staging", "photoBaseUrl": "https://cfm.momentpath.com/", "adyen": {"environment": "live-us", "clientKey": "live_SYG43VILVZANFGQVUJBHTR3MH4ZTZUGF", "environmentPrefix": "a9a3f949980022c7-TendlyLLCDbaMomentPath"}, "whitelabel": {"enabled_sites": ["lightbridge", "connect", "luvnotes", "<PERSON>sunshinehouse", "myfoundations", "myquestzone", "dayearlylearning", "cnmoments", "rightatschool", "mswmoments", "spsmoments", "dramaticed", "kidsinthegame", "discover", "ikids", "apec", "my"], "lightbridge": {"large_logo": "/img/lb_logo-white.svg", "small_logo": "/img/lb_icon-white.svg", "header_logo": "https://assets.momentpath.com/customers/lightbridge/summaryemailv2019/logo.png", "colors": [{"template": "--primary", "color": "#218ACA"}, {"template": "--people-ratio-template-color", "color": "#218ACA"}, {"template": "--people-ratio-staff-template-color", "color": "#8BC53D"}, {"template": "--dark-primary", "color": "#0e3852"}, {"template": "--light-primary", "color": "#b8d2e3"}, {"template": "--lighter-primary", "color": "#dbf1ff"}], "sessionVars": [{"name": "wmgLabel", "value": "ParentView® powered by WatchMeGrow"}]}, "connect": {"large_logo": "/img/bklogo.svg", "small_logo": "/img/bklogo.svg", "header_logo": "https://assets.momentpath.com/customers/buildingkidz/bk.png", "title": "BuildingKidz Connect", "favicon": "/bkfav.ico", "colors": [{"template": "--primary", "color": "#560F2D"}, {"template": "--people-ratio-template-color", "color": "#560F2D"}, {"template": "--people-ratio-staff-template-color", "color": "#939598"}, {"template": "--dark-primary", "color": "#0E3852"}, {"template": "--light-primary", "color": "#DDCFD5"}, {"template": "--lighter-primary", "color": "#EFE7EA"}], "sessionVars": [{"name": "learningPathLink", "value": "https://training.buildingkidzschool.com"}]}, "luvnotes": {"large_logo": "/img/lsp_icon.png", "small_logo": "/img/lsp_icon.png", "header_logo": "https://assets.momentpath.com/customers/littlesunshine/luvnotes.png", "colors": [{"template": "--primary", "color": "#538177"}, {"template": "--people-ratio-template-color", "color": "#538177"}, {"template": "--people-ratio-staff-template-color", "color": "#C67D6D"}, {"template": "--dark-primary", "color": "#09473a"}, {"template": "--light-primary", "color": "#a6ded2"}, {"template": "--lighter-primary", "color": "#cffcf3"}], "sessionVars": [{"name": "eduTitle", "value": "Red Carpet Experience"}]}, "mysunshinehouse": {"large_logo": "/img/sshouse.png", "small_logo": "/img/sshouse.png", "header_logo": "https://assets.momentpath.com/customers/sshouse/header.png", "colors": [{"template": "--primary", "color": "#41748D"}, {"template": "--people-ratio-template-color", "color": "#41748D"}, {"template": "--people-ratio-staff-template-color", "color": "#54585A"}, {"template": "--dark-primary", "color": "#012b40"}, {"template": "--light-primary", "color": "#bde0f2"}, {"template": "--lighter-primary", "color": "#cfecfa"}], "sessionVars": []}, "myfoundations": {"large_logo": "/img/foundations.png", "small_logo": "/img/foundations.png", "header_logo": "https://assets.momentpath.com/customers/foundations/header.png", "colors": [{"template": "--primary", "color": "#2A317D"}, {"template": "--people-ratio-template-color", "color": "#2A317D"}, {"template": "--people-ratio-staff-template-color", "color": "#81BD41"}, {"template": "--dark-primary", "color": "#000430"}, {"template": "--light-primary", "color": "#989feb"}, {"template": "--lighter-primary", "color": "#c9ceff"}], "sessionVars": []}, "myquestzone": {"large_logo": "/img/questzone.png", "small_logo": "/img/questzone.png", "header_logo": "https://assets.momentpath.com/customers/questzone/header.png", "colors": [{"template": "--primary", "color": "#4D008C"}, {"template": "--people-ratio-template-color", "color": "#4D008C"}, {"template": "--people-ratio-staff-template-color", "color": "#F18A00"}, {"template": "--dark-primary", "color": "#10011c"}, {"template": "--light-primary", "color": "#b98fdb"}, {"template": "--lighter-primary", "color": "#e4c5fc"}], "sessionVars": []}, "dayearlylearning": {"large_logo": "/img/dayearlylearning.png", "small_logo": "/img/dayearlylearning.png", "header_logo": "https://assets.momentpath.com/customers/dayearlylearning/header.png", "colors": [{"template": "--primary", "color": "#2A6AA6"}, {"template": "--people-ratio-template-color", "color": "#0C2338"}, {"template": "--people-ratio-staff-template-color", "color": "#EC7D30"}, {"template": "--dark-primary", "color": "#0C2338"}, {"template": "--light-primary", "color": "#5D9BD3"}, {"template": "--lighter-primary", "color": "#7ABCF7"}], "sessionVars": []}, "cnmoments": {"large_logo": "/img/cnmoments.png", "small_logo": "/img/cnmoments.png", "header_logo": "https://assets.momentpath.com/customers/cnmoments/header.png", "colors": [{"template": "--primary", "color": "#007DC3"}, {"template": "--people-ratio-template-color", "color": "#007DC3"}, {"template": "--people-ratio-staff-template-color", "color": "#FFC425"}, {"template": "--dark-primary", "color": "#01314D"}, {"template": "--light-primary", "color": "#59B5EB"}, {"template": "--lighter-primary", "color": "#ADDCF7"}], "sessionVars": []}, "rightatschool": {"large_logo": "/img/ras.png", "small_logo": "/img/ras.png", "header_logo": "https://assets.momentpath.com/customers/ras/header.png", "colors": [{"template": "--primary", "color": "#0086AB"}, {"template": "--people-ratio-template-color", "color": "#0086AB"}, {"template": "--people-ratio-staff-template-color", "color": "#6DBABE"}, {"template": "--dark-primary", "color": "#023a4a"}, {"template": "--light-primary", "color": "#25CAF7"}, {"template": "--lighter-primary", "color": "#A1EBFF"}], "sessionVars": []}, "mswmoments": {"large_logo": "/img/msw.png", "small_logo": "/img/msw.png", "header_logo": "https://assets.momentpath.com/customers/msw/header.png", "colors": [{"template": "--primary", "color": "#3C1053"}, {"template": "--people-ratio-template-color", "color": "#3C1053"}, {"template": "--people-ratio-staff-template-color", "color": "#307FE2"}, {"template": "--dark-primary", "color": "#33004F"}, {"template": "--light-primary", "color": "#B556A8"}, {"template": "--lighter-primary", "color": "#DDADF7"}], "sessionVars": []}, "spsmoments": {"large_logo": "/img/sps.png", "small_logo": "/img/sps.png", "title": "SPS Moments", "favicon": "/spsfav.ico", "header_logo": "https://assets.momentpath.com/customers/sps/header.png", "colors": [{"template": "--primary", "color": "#05BDDE"}, {"template": "--people-ratio-template-color", "color": "#FFB500"}, {"template": "--people-ratio-staff-template-color", "color": "#FFB500"}, {"template": "--dark-primary", "color": "#FFB500"}, {"template": "--light-primary", "color": "#05BDDE"}, {"template": "--lighter-primary", "color": "#dfeff7"}], "sessionVars": []}, "dramaticed": {"large_logo": "/img/dramatic.png", "small_logo": "/img/dramatic.png", "title": "Dramatic Education", "favicon": "/dramatic.ico", "header_logo": "https://assets.momentpath.com/customers/dramatic/header.png", "colors": [{"template": "--primary", "color": "#d92727"}, {"template": "--people-ratio-template-color", "color": "#81BD41"}, {"template": "--people-ratio-staff-template-color", "color": "#FFB500"}, {"template": "--dark-primary", "color": "#992222"}, {"template": "--lighter-primary", "color": "rgba(226, 226, 226, 0.71)"}, {"template": "--info", "color": "#dfeff7"}], "sessionVars": []}, "kidsinthegame": {"large_logo": "/img/kig.png", "small_logo": "/img/kig.png", "title": "Kids in the Game", "favicon": "/kigfav.ico", "header_logo": "https://assets.momentpath.com/customers/kidsinthegame/header.png", "colors": [{"template": "--primary", "color": "#0025FF"}, {"template": "--people-ratio-template-color", "color": "#31B1FF"}, {"template": "--people-ratio-template-color", "color": "#31B1FF"}, {"template": "--people-ratio-staff-template-color", "color": "#31B1FF"}, {"template": "--dark-primary", "color": "#31B1FF"}, {"template": "--danger", "color": "#31B1FF"}]}, "discover": {"large_logo": "/img/discover.png", "small_logo": "/img/discover.png", "title": "Discover After School", "favicon": "/discover.ico", "header_logo": "https://assets.momentpath.com/customers/discover/header.png", "colors": [{"template": "--primary", "color": "#03989e"}, {"template": "--people-ratio-template-color", "color": "#ff7201"}, {"template": "--people-ratio-staff-template-color", "color": "#ff7201"}, {"template": "--dark-primary", "color": "#ff7201"}, {"template": "--light-primary", "color": "#ffd41d"}, {"template": "--light-primary", "color": "#ffd41d"}], "sessionVars": []}, "ikids": {"large_logo": "/img/ikids.png", "small_logo": "/img/ikids.png", "title": "iKids U", "favicon": "/ikidsfav.ico", "header_logo": "https://assets.momentpath.com/customers/ikids/header.png", "colors": [{"template": "--primary", "color": "#007bff"}, {"template": "--secondary", "color": "#E6252B"}, {"template": "--danger", "color": "#E6252B"}, {"template": "--people-ratio-template-color", "color": "#E6252B"}, {"template": "--people-ratio-staff-template-color", "color": "#8BC540"}, {"template": "--dark-primary", "color": "#0861cf"}]}, "apec": {"large_logo": "/img/apec.png", "small_logo": "/img/apec.png", "title": "APEC Family Foundation", "favicon": "/apec.ico", "header_logo": "https://assets.momentpath.com/customers/apec/header.png", "colors": [{"template": "--primary", "color": "#FFD700"}, {"template": "--dark-primary", "color": "#a18903"}, {"template": "--dark-primary", "color": "#a18903"}, {"template": "--white", "color": "#000"}, {"template": "--people-ratio-template-color", "color": "#000"}, {"template": "--people-ratio-staff-template-color", "color": "#000"}]}, "my": {"large_logo": "/img/champions.png", "small_logo": "/img/champions.png", "title": "Discover Champions", "favicon": "/champions.ico", "header_logo": "https://assets.momentpath.com/customers/champions/header.png", "colors": [{"template": "--primary", "color": "#EB0202"}, {"template": "--people-ratio-template-color", "color": "#EB0202"}, {"template": "--people-ratio-staff-template-color", "color": "#00BCDF"}, {"template": "--dark-primary", "color": "#EB0202"}, {"template": "--light-primary", "color": "#F15858"}, {"template": "--light-primary", "color": "#F36C6C"}]}}, "flexmonster": {"key": "Z7ZW-XIC24A-3U0328-701P70-232137-1P123B-644664-4X4S0D-0A3H6N-4W", "componentFolderUrl": "https://cdn.flexmonster.com/"}}, "stripe": {"secretKey": "********************************"}, "TWILIO_ACCOUNT_SID": "**********************************", "TWILIO_AUTH_TOKEN": "402340df90e8f60300a01f170bfb2b95", "TWILIO_NUMBER": "+***********", "adyen": {"apiKey": "AQExhmfuXNWTK0Qc+iSEl2o8q/23ZKhpD5JnV2hTyGybn3JDkX+ID1i78goJPtEJ8SSPYxDBXVsNvuR83LVYjEgiTGAH-T2vhgDHo1R0rOR3ogP3GPpppRKTUOkwJWJ+hLalJWkY=-QU7nd7Pdw2XW3EWN", "notificationUsername": "mp_adnotifier", "notificationPassword": "iA89XtPELnbBaZEDu", "mpapipw": "#efCgYnC=S{RG5iCp{u?=wuuq", "mpapiuser": "<EMAIL>", "reportingPassword": "1+eD4B~77)M\\re9FqEUV&#7E}", "reportingUsername": "<EMAIL>", "defaultAccountCode": "*********", "reportDownloadUrlPrefix": "https://ca-live.adyen.com/reports/download/MarketPlace/TendlyLLCDbaMomentPath/", "transferFundsUrl": "https://cal-live.adyen.com/cal/services/Fund/v6/transferFunds"}, "kadira": {"appId": "CK3H7J4ozHEfpNjJE", "appSecret": "f6f10cd1-0842-4b6c-a13c-e9cbf27efbc9", "options": {"endpoint": "https://meteor-apm-engine.nodechef.com"}}, "monti": {"appId": "4d4nsRdSivWmt9NDW", "appSecret": "cc7e8d44-f1d4-42b7-b92c-dc1839cad88d"}, "transloaditKey": "3d8d69dfd8e8f6a38741b96cd64a8a75e788f686", "suppressJobs": true, "applicationJobs": true, "airSlate": {"bucketLambdaApiKey": "dYHgPcR1YDkqRA0iifLy3LaGYcp7Z2X1xtouaZ46ouLkiJqjFJHFPGo5HSaF", "url": "https://bots.airslate.com"}, "redshift": {"dbHost": "dev-lineleader-workgroup.************.us-east-2.redshift-serverless.amazonaws.com", "dbPort": 5439, "dbName": "dev", "dbUser": "admin", "dbPassword": "Manageapppoc123"}, "redisOplog": {"redis": {"cluster": true, "port": 6379, "host": "clustercfg.dev-meteor3-redis-cluster.pl1iq0.use1.cache.amazonaws.com", "password": "RedisPassword123!", "nodes": [{"host": "clustercfg.dev-meteor3-redis-cluster.pl1iq0.use1.cache.amazonaws.com", "port": 6379}], "options": {"redisOptions": {"tls": {"rejectUnauthorized": true, "servername": "clustercfg.dev-meteor3-redis-cluster.pl1iq0.use1.cache.amazonaws.com"}, "password": "RedisPassword123!"}, "enableOfflineQueue": true}}, "debug": true, "logLevel": "debug", "logPrefix": "RedisSubscriptionManager", "mutationDefaults": {"optimistic": true, "pushToRedis": true}}, "debug": false}