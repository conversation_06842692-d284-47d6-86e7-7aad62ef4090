import { jest } from '@jest/globals';
import _ from '../lib/util/underscore';
import moment from 'moment-timezone';
import { deep } from './helpers/utils';
import { BILLING_ERRORS_EMAIL, GROUP_SYNC_ERRORS_EMAIL } from '../lib/constants/emailConstants';

jest.mock('../lib/util/subsCache', () => ({
    subsCache: jest.fn(),
}));

// Attach underscore to the global object so that Meteor collection methods can access it
// global._ = _;
// apparently _.deep is not a standard underscore function, so we need to add it to the global object
// global._.deep = deep;
// mocks any calls to new Invoice() with the data passed in
//Invoice = jest.fn(data => data);
// global.moment = moment;

//processSummaryMail2021 = jest.fn();
// You can also attach any other global objects or libraries that your tests need
// For example:
global.Meteor = {
        call: jest.fn(),
        callAsync: jest.fn().mockResolvedValue(undefined),
        defer: jest.fn(),
        bindEnvironment: jest.fn(),
        users: {
            update: jest.fn(),
            findOne: jest.fn(),
            findOneAsync: jest.fn()
        },
        settings: {
            stripe: {
                secretKey: 'foo-bar-baz'
            },
            emails: {
                billingErrors: BILLING_ERRORS_EMAIL,
                groupSyncErrors: GROUP_SYNC_ERRORS_EMAIL
            }
        },
        Error: jest.fn(message => new Error(message)),
        methods: jest.fn(),
    };

// // Mock ReactiveVar
class ReactiveVar {
    constructor(initialValue) {
        this.value = initialValue;
    }

    get() {
        return this.value;
    }

    set(newValue) {
        this.value = newValue;
    }
}

global.ReactiveVar = ReactiveVar;

global.responseStream = (res, statusCode, message) => {
  res.writeHeader(`${statusCode}`, {
    'Content-Type': 'application/json',
  });
  res.statusCode = statusCode;
  res.end(JSON.stringify(message));
}
