{"ENV_VARS": {"MONGO_URL": "mongodb://127.0.0.1:27017/meteor"}, "awsBucket": "my-example-staging", "AWSAccessKeyId": "", "AWSSecretAccessKey": "", "mpAWSaccessKey": "<AWS_ACCESS_KEY_ID>", "mpAWSsecretKey": "<AWS_SECRET_ACCESS_KEY>", "mpEntityKey": "", "mpSMSQueueUrl": "", "mpEventBridgeQueueUrl": "", "mpMomentEventBridgeQueueUrl": "", "mpEventBridgeMessageGroup": "staging", "adpEmail1": "<EMAIL>", "adpEmail2": "<EMAIL>", "defaultOriginationNumber": "12058437340", "childcareCrmUrl": "<ENROLL_URL>", "childcareCrmUIUrl": "https://my.childcarecrm.com/#", "idpBaseUrl": "https://login-test.lineleader.com", "idpClientSecret": "<IDP_CLIENT_SECRET>", "zkTecoUrl": "<ZKTECO_URL>", "zkTecoApiToken": "<ZKTECO_KEY>", "rightAtSchoolUrl": "", "rightAtSchoolWebhookUrl": "", "mobileApiKey": "", "public": {"supportSite": "https://help-beta.momentpath.com/knowledge", "awsBucketUrl": "https://my-meteor-example.s3.amazonaws.com", "environment": "localhost", "photoBaseUrl": "https://tendlymr.s3.amazonaws.com/", "stripe": {"publishableKey": ""}, "adyen": {"clientKey": ""}, "intercom": {"id": ""}, "whitelabel": {"enabled_sites": ["mp-staging-indigo"], "mp-staging-indigo": {"large_logo": "/img/lb_logo-white.svg", "small_logo": "/img/lb_icon-white.svg", "header_logo": "https://assets.momentpath.com/customers/lightbridge/summaryemailv2019/logo.png", "colors": [{"template": "--primary", "color": "#218ACA"}, {"template": "--people-ratio-template-color", "color": "#218ACA"}, {"template": "--people-ratio-staff-template-color", "color": "#8BC53D"}, {"template": "--dark-primary", "color": "#0e3852"}, {"template": "--light-primary", "color": "#b8d2e3"}, {"template": "--lighter-primary", "color": "#dbf1ff"}], "sessionVars": [{"name": "wmgLabel", "value": "ParentView® powered by WatchMeGrow"}]}}, "flexmonster": {"key": "<FLEXMONSTER_LICENSE_KEY>", "componentFolderUrl": "https://cdn.flexmonster.com/"}}, "stripe": {"secretKey": ""}, "intercom": {"personalAccessToken": "", "secret": ""}, "adyen": {"apiKey": "", "notificationUsername": "", "notificationPassword": "", "mpapipw": "", "defaultAccountCode": "", "reportDownloadUrlPrefix": "", "transferFundsUrl": ""}, "transloaditKey": "", "TWILIO_ACCOUNT_SID": "", "TWILIO_AUTH_TOKEN": "", "TWILIO_NUMBER": "+***********", "suppressJobs": true, "applicationJobs": true, "skipMessageNotifications": true, "airSlate": {"bucketLambdaApiKey": "", "url": "https://bots.airslate.com"}, "emails": {"billingErrors": "<EMAIL>", "groupSyncErrors": "<EMAIL>"}, "redshift": {"dbHost": "<REDSHIFT_DB_HOST>", "dbPort": "<REDSHIFT_DB_PORT>", "dbName": "<REDSHIFT_DB_NAME>", "dbUser": "<REDSHIFT_DB_USER>", "dbPassword": "<REDSHIFT_DB_PASSWORD>"}, "redisOplog": {"redis": {"cluster": true, "port": 6379, "host": "clustercfg.dev-meteor3-redis-cluster.pl1iq0.use1.cache.amazonaws.com", "password": "RedisPassword123!", "nodes": [{"host": "clustercfg.dev-meteor3-redis-cluster.pl1iq0.use1.cache.amazonaws.com", "port": 6379}], "options": {"redisOptions": {"tls": {"rejectUnauthorized": true, "servername": "clustercfg.dev-meteor3-redis-cluster.pl1iq0.use1.cache.amazonaws.com"}, "password": "RedisPassword123!"}, "enableOfflineQueue": true}}, "debug": true, "logLevel": "debug", "logPrefix": "RedisSubscriptionManager", "mutationDefaults": {"optimistic": true, "pushToRedis": true}}}