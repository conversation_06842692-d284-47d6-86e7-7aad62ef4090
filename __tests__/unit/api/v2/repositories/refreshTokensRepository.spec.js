import { beforeEach } from "@jest/globals";
import { mockCollection } from "../../../../helpers/collectionMock";
import { RefreshTokenRepository } from "../../../../../api/v2/repositories/refreshTokenRepository";
import { RefreshToken, RefreshTokens } from "../../../../../lib/collections/refreshTokens";

describe('RefreshTokenRepository', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('properly find a token', async () => {
        const repository = new RefreshTokenRepository()
        const spy = jest.spyOn(RefreshTokens, 'findOneAsync');
        spy.mockImplementationOnce(() => {
            return {
                _id: 'id',
                userId: 'xyz',
                token: 'token'
            };
        });
        const result = await repository.findOneByToken('token');
        expect(result).toStrictEqual({_id: 'id', userId: 'xyz', token: 'token'});
        expect(spy.mock.calls.length).toBe(1);
        expect(spy.mock.calls[0]).toStrictEqual([ { token: 'token' }]);
    });

    it('properly inserts a token', async () => {
        const repository = new RefreshTokenRepository()
        const spy = jest.spyOn(RefreshTokens, 'insertAsync');
        spy.mockImplementationOnce(() => {
            return 'id';
        });
        const result = await repository.insertOne({ token: 'token' });
        expect(result).toStrictEqual('id');
        expect(spy.mock.calls.length).toBe(1);
        expect(spy.mock.calls[0]).toStrictEqual([ { token: 'token' }]);

    });
});