import { beforeEach, it, describe, jest } from "@jest/globals";
import { OrgsRepository } from '../../../../../api/v2/repositories/orgsRepository';
import { PeopleRepository } from '../../../../../api/v2/repositories/peopleRepository';
import { ReservationsRepository } from '../../../../../api/v2/repositories/reservationsRepository';
import { ReservationsApiService } from '../../../../../api/v2/services/reservationsApiService';
import { OrgCollectionMethods } from '../../../../fixtures/methods/orgCollectionMethods';
import moment from 'moment-timezone';
import { OrgHierarchy } from "../../../../../api/v2/services/utils/orgHierarchy";

jest.mock('../../../../../api/v2/repositories/orgsRepository');
jest.mock('../../../../../api/v2/repositories/peopleRepository');
jest.mock('../../../../../api/v2/repositories/reservationsRepository');
jest.mock('../../../../../api/v2/services/utils/orgHierarchy');

const hierarchyMock = OrgHierarchy.prototype.findOrgHierarchy;
const personFixture = require('../../../../fixtures/api/v2/reservations/person.json');
const orgFixture = require('../../../../fixtures/321-Mariposa-Local.json');
const oneReservation = require('../../../../fixtures/api/v2/reservations/rawResOne.json');
const formattedOneReservation = require('../../../../fixtures/api/v2/reservations/formattedOne.json');
const manyReservations = require('../../../../fixtures/api/v2/reservations/rawResMany.json');
const formattedManyReservations = require('../../../../fixtures/api/v2/reservations/formattedMany.json');

const orgsFindOneMock = OrgsRepository.prototype.findById;
const peopleFineOneMock = PeopleRepository.prototype.findById;
const reservationsFindOneMock = ReservationsRepository.prototype.findById;
const reservationsFindAllMock = ReservationsRepository.prototype.findReservationsByPersonIdAndRange;

orgFixture.getTimezone = OrgCollectionMethods.getTimezone.bind(orgFixture);
const service = new ReservationsApiService();
describe('reservationsApiService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('formats a reservation day correctly', () => {
        expect(ReservationsApiService.formatDay('monday')).toBe('M');
        expect(ReservationsApiService.formatDay('thursday')).toBe('R');
        expect(ReservationsApiService.formatDay('SUN')).toBe('U');
        expect(ReservationsApiService.formatDay('sat')).toBe('S');
    });

    it('should throw for no personId', async () => {
        await expect( service.getReservations({})).rejects.toThrow('No personId provided');
    });

    it('should throw for no person found', async () => {
        peopleFineOneMock.mockImplementationOnce(() => null);
        await expect( service.getReservations({personId: '123'})).rejects.toThrow('Person not found');
    });

    it('should throw for no org found', async () => {
        peopleFineOneMock.mockImplementationOnce(() => (personFixture));
        orgsFindOneMock.mockImplementationOnce(() => null);
        await expect( service.getReservations({personId: '123'})).rejects.toThrow('Org not found');
    });

    it('should return a reservation with id', async () => {
        peopleFineOneMock.mockImplementationOnce(() => (personFixture));
        delete orgFixture.parentOrgId;
        orgsFindOneMock.mockImplementation(() => (orgFixture));
        reservationsFindOneMock.mockImplementationOnce(() => (oneReservation));
        hierarchyMock.mockImplementationOnce(() => ['nTvbx24M2dbM9w6tu']);
        const result = await service.getReservations({personId: 'gqiQJY4cyfv6fszNH', reservationId: 'ppXXyx8kRbShCJin9', user: {orgId: 'nTvbx24M2dbM9w6tu'}});
        expect(result).toStrictEqual(formattedOneReservation);
    });

    it('should return many reservations with no id', async () => {
        peopleFineOneMock.mockImplementationOnce(() => (personFixture));
        delete orgFixture.parentOrgId;
        orgsFindOneMock.mockImplementation(() => (orgFixture));
        reservationsFindAllMock.mockImplementationOnce(() => (manyReservations));
        hierarchyMock.mockImplementationOnce(() => ['nTvbx24M2dbM9w6tu']);
        const result = await service.getReservations({personId: 'gqiQJY4cyfv6fszNH', user: {orgId: 'nTvbx24M2dbM9w6tu'}});
        expect(result).toStrictEqual(formattedManyReservations);
    });

    it('date ranges work', async () => {
        peopleFineOneMock.mockImplementationOnce(() => (personFixture));
        delete orgFixture.parentOrgId;
        const startDate = '11/06/2023';
        const endDate = '11/07/2023';
        const filteredReservations = manyReservations.filter(res => res.scheduledDate >= moment.tz(startDate, 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf() && res.scheduledDate < moment.tz(endDate, 'MM/DD/YYYY', 'America/New_York').startOf('day').valueOf());
        const filteredFormattedReservations = formattedManyReservations.filter(res => res.scheduled_date >= moment.tz(startDate, 'MM/DD/YYYY', 'America/New_York').toISOString(true) && res.scheduled_date < moment.tz(endDate, 'MM/DD/YYYY', 'America/New_York').toISOString(true));
        orgsFindOneMock.mockImplementation(() => (orgFixture));
        reservationsFindAllMock.mockImplementationOnce(() => (filteredReservations));
        hierarchyMock.mockImplementationOnce(() => ['nTvbx24M2dbM9w6tu']);
        const result = await service.getReservations({personId: 'gqiQJY4cyfv6fszNH', user: {orgId: 'nTvbx24M2dbM9w6tu'}, startDate, endDate});
        expect(result).toStrictEqual(filteredFormattedReservations);
    });
});