import { beforeEach } from "@jest/globals";
import { MomentsService } from '../../../../../api/v2/services/momentsService';
import { MomentsRepository } from "../../../../../api/v2/repositories/momentsRepository";
import { PeopleRepository } from "../../../../../api/v2/repositories/peopleRepository";
import { OrgsRepository } from "../../../../../api/v2/repositories/orgsRepository";
import { GroupsRepository } from "../../../../../api/v2/repositories/groupsRepository";

jest.mock("../../../../../api/v2/repositories/peopleRepository");
jest.mock("../../../../../api/v2/repositories/orgsRepository");
jest.mock('../../../../../api/v2/repositories/groupsRepository');
jest.mock("../../../../../api/v2/repositories/momentsRepository");

const createdByFindOneMock = PeopleRepository.prototype.findById;
const ownerFindOneMock = PeopleRepository.prototype.findById;
const checkedInByFindOneMock = PeopleRepository.prototype.findById;
const orgsFindOneMock = OrgsRepository.prototype.findById;
const peopleFindMock = PeopleRepository.prototype.find;
const groupsFindOneMock = GroupsRepository.prototype.findById;
const momentFindByIdMock = MomentsRepository.prototype.findMomentById;
const momentsFindMock = MomentsRepository.prototype.findMomentsByQueryFields;

describe('momentsService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    it('get all moments with limit', async () => {
        const req = {
            query: {
                limit: 4,
                offset: 0,
                org_Id: "orgId1",
                moment_date_start: "3/30/2022",
                moment_date_end: "3/31/2022",
                moment_type: "momentType1"
            }
        };
        const moments = [
            { _id: "id1", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId1", owner: "owner1", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" },
            { _id: "id2", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId2", owner: "owner2", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" },
            { _id: "id3", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId3", owner: "owner3", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" },
            { _id: "id4", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId4", owner: "owner4", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" }
        ];

        const momentsResponse = [{ "id": "id1", "moment_timestamp": "2021-11-23T20:24:44.347Z", "created_by": [{ "href": "/api/v2/people/createdByPersonId1", "rel": "person", "type": "GET" }], "owner": [{ "href": "/api/v2/people/owner1", "rel": "person", "type": "GET" }], "tagged_people": [[{ "href": "/api/v2/people/taggedPeople1", "rel": "person", "type": "GET" }]], "moment_type": "momentType1", "comment": "Comm1", "org": [{ "href": "/api/v2/orgs/orgId1", "rel": "org", "type": "GET" }], "check_in_classroom": [{ "href": "/api/v2/group/checkInGroupId1", "rel": "group", "type": "GET" }], "checked_in_by": [{ "href": "/api/v2/people/checkedInById1", "rel": "person", "type": "GET" }] }, { "id": "id2", "moment_timestamp": "2021-11-23T20:24:44.347Z", "created_by": [{ "href": "/api/v2/people/createdByPersonId2", "rel": "person", "type": "GET" }], "owner": [{ "href": "/api/v2/people/owner2", "rel": "person", "type": "GET" }], "tagged_people": [[{ "href": "/api/v2/people/taggedPeople1", "rel": "person", "type": "GET" }]], "moment_type": "momentType1", "comment": "Comm1", "org": [{ "href": "/api/v2/orgs/orgId1", "rel": "org", "type": "GET" }], "check_in_classroom": [{ "href": "/api/v2/group/checkInGroupId1", "rel": "group", "type": "GET" }], "checked_in_by": [{ "href": "/api/v2/people/checkedInById1", "rel": "person", "type": "GET" }] }, { "id": "id3", "moment_timestamp": "2021-11-23T20:24:44.347Z", "created_by": [{ "href": "/api/v2/people/createdByPersonId3", "rel": "person", "type": "GET" }], "owner": [{ "href": "/api/v2/people/owner3", "rel": "person", "type": "GET" }], "tagged_people": [[{ "href": "/api/v2/people/taggedPeople1", "rel": "person", "type": "GET" }]], "moment_type": "momentType1", "comment": "Comm1", "org": [{ "href": "/api/v2/orgs/orgId1", "rel": "org", "type": "GET" }], "check_in_classroom": [{ "href": "/api/v2/group/checkInGroupId1", "rel": "group", "type": "GET" }], "checked_in_by": [{ "href": "/api/v2/people/checkedInById1", "rel": "person", "type": "GET" }] }, { "id": "id4", "moment_timestamp": "2021-11-23T20:24:44.347Z", "created_by": [{ "href": "/api/v2/people/createdByPersonId4", "rel": "person", "type": "GET" }], "owner": [{ "href": "/api/v2/people/owner4", "rel": "person", "type": "GET" }], "tagged_people": [[{ "href": "/api/v2/people/taggedPeople1", "rel": "person", "type": "GET" }]], "moment_type": "momentType1", "comment": "Comm1", "org": [{ "href": "/api/v2/orgs/orgId1", "rel": "org", "type": "GET" }], "check_in_classroom": [{ "href": "/api/v2/group/checkInGroupId1", "rel": "group", "type": "GET" }], "checked_in_by": [{ "href": "/api/v2/people/checkedInById1", "rel": "person", "type": "GET" }] }];
        momentsFindMock.mockImplementationOnce(() => (moments));

        const momentsService = new MomentsService(req);
        const result = await momentsService.getMoments("orgId1");

        expect(momentsService).toBeInstanceOf(MomentsService);
        expect(result).toHaveLength(4);
        expect(result).toStrictEqual(momentsResponse);
        expect(momentsFindMock).toHaveBeenCalledWith({ orgId: "orgId1", date: { $gte: "03/30/2022", $lte: "03/31/2022" }, momentType: "momentType1" }, { "_id": 1, "checkInGroupId": 1, "checkedInById": 1, "comment": 1, "createdAt": 1, "createdByPersonId": 1, "date": 1, "momentType": 1, "orgId": 1, "owner": 1, "taggedPeople": 1, "time": 1 }, { limit: 4, offset: 0 });
    });

    it('get all moments with no_pagination', async () => {
        const req = {
            query: {
                no_pagination: true,
                org_Id: "orgId1"
            }
        };
        const moments = [
            { _id: "id1", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId1", owner: "owner1", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" },
            { _id: "id2", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId2", owner: "owner2", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" },
            { _id: "id3", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId3", owner: "owner3", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" },
            { _id: "id4", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId4", owner: "owner4", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" }
        ];

        const momentsResponse = [
            {
                "id": "id1",
                "moment_timestamp": "2021-11-23T20:24:44.347Z",
                "created_by": [
                    {
                        "href": "/api/v2/people/createdByPersonId1",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "owner": [
                    {
                        "href": "/api/v2/people/owner1",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "tagged_people": [
                    [
                        {
                            "href": "/api/v2/people/taggedPeople1",
                            "rel": "person",
                            "type": "GET"
                        }
                    ]
                ],
                "moment_type": "momentType1",
                "comment": "Comm1",
                "org": [
                    {
                        "href": "/api/v2/orgs/orgId1",
                        "rel": "org",
                        "type": "GET"
                    }
                ],
                "check_in_classroom": [
                    {
                        "href": "/api/v2/group/checkInGroupId1",
                        "rel": "group",
                        "type": "GET"
                    }
                ],
                "checked_in_by": [
                    {
                        "href": "/api/v2/people/checkedInById1",
                        "rel": "person",
                        "type": "GET"
                    }
                ]
            },
            {
                "id": "id2",
                "moment_timestamp": "2021-11-23T20:24:44.347Z",
                "created_by": [
                    {
                        "href": "/api/v2/people/createdByPersonId2",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "owner": [
                    {
                        "href": "/api/v2/people/owner2",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "tagged_people": [
                    [
                        {
                            "href": "/api/v2/people/taggedPeople1",
                            "rel": "person",
                            "type": "GET"
                        }
                    ]
                ],
                "moment_type": "momentType1",
                "comment": "Comm1",
                "org": [
                    {
                        "href": "/api/v2/orgs/orgId1",
                        "rel": "org",
                        "type": "GET"
                    }
                ],
                "check_in_classroom": [
                    {
                        "href": "/api/v2/group/checkInGroupId1",
                        "rel": "group",
                        "type": "GET"
                    }
                ],
                "checked_in_by": [
                    {
                        "href": "/api/v2/people/checkedInById1",
                        "rel": "person",
                        "type": "GET"
                    }
                ]
            },
            {
                "id": "id3",
                "moment_timestamp": "2021-11-23T20:24:44.347Z",
                "created_by": [
                    {
                        "href": "/api/v2/people/createdByPersonId3",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "owner": [
                    {
                        "href": "/api/v2/people/owner3",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "tagged_people": [
                    [
                        {
                            "href": "/api/v2/people/taggedPeople1",
                            "rel": "person",
                            "type": "GET"
                        }
                    ]
                ],
                "moment_type": "momentType1",
                "comment": "Comm1",
                "org": [
                    {
                        "href": "/api/v2/orgs/orgId1",
                        "rel": "org",
                        "type": "GET"
                    }
                ],
                "check_in_classroom": [
                    {
                        "href": "/api/v2/group/checkInGroupId1",
                        "rel": "group",
                        "type": "GET"
                    }
                ],
                "checked_in_by": [
                    {
                        "href": "/api/v2/people/checkedInById1",
                        "rel": "person",
                        "type": "GET"
                    }
                ]
            },
            {
                "id": "id4",
                "moment_timestamp": "2021-11-23T20:24:44.347Z",
                "created_by": [
                    {
                        "href": "/api/v2/people/createdByPersonId4",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "owner": [
                    {
                        "href": "/api/v2/people/owner4",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "tagged_people": [
                    [
                        {
                            "href": "/api/v2/people/taggedPeople1",
                            "rel": "person",
                            "type": "GET"
                        }
                    ]
                ],
                "moment_type": "momentType1",
                "comment": "Comm1",
                "org": [
                    {
                        "href": "/api/v2/orgs/orgId1",
                        "rel": "org",
                        "type": "GET"
                    }
                ],
                "check_in_classroom": [
                    {
                        "href": "/api/v2/group/checkInGroupId1",
                        "rel": "group",
                        "type": "GET"
                    }
                ],
                "checked_in_by": [
                    {
                        "href": "/api/v2/people/checkedInById1",
                        "rel": "person",
                        "type": "GET"
                    }
                ]
            }
        ];
        momentsFindMock.mockImplementationOnce(() => (moments));

        const momentsService = new MomentsService(req);
        const result = await momentsService.getMoments("orgId1");

        expect(momentsService).toBeInstanceOf(MomentsService);
        expect(result).toHaveLength(4);
        expect(result).toStrictEqual(momentsResponse);
        expect(momentsFindMock).toHaveBeenCalledWith({ orgId: "orgId1" }, { "_id": 1, "checkInGroupId": 1, "checkedInById": 1, "comment": 1, "createdAt": 1, "createdByPersonId": 1, "date": 1, "momentType": 1, "orgId": 1, "owner": 1, "taggedPeople": 1, "time": 1 }, null);
    });

    it('get all moments with limit', async () => {
        const req = {
            query: {
                limit: 1,
                offset: 0,
                org_Id: "orgId1",
                moment_date_start: "3/30/2022",
                moment_date_end: "3/31/2022",
                moment_type: "momentType1"
            }
        };
        const moments = [
            { _id: "id1", time: "8:24 am", date: "11/23/2021", createdAt: 1637699084347, createdByPersonId: "createdByPersonId1", owner: "owner1", taggedPeople: ["taggedPeople1"], momentType: "momentType1", comment: "Comm1", orgId: "orgId1", checkInGroupId: "checkInGroupId1", checkedInById: "checkedInById1" }
        ];

        const momentsResponse = [
            {
                "id": "id1",
                "moment_timestamp": "2021-11-23T20:24:44.347Z",
                "created_by": [
                    {
                        "href": "/api/v2/people/createdByPersonId1",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "owner": [
                    {
                        "href": "/api/v2/people/owner1",
                        "rel": "person",
                        "type": "GET"
                    }
                ],
                "tagged_people": [
                    [
                        {
                            "href": "/api/v2/people/taggedPeople1",
                            "rel": "person",
                            "type": "GET"
                        }
                    ]
                ],
                "moment_type": "momentType1",
                "comment": "Comm1",
                "org": [
                    {
                        "href": "/api/v2/orgs/orgId1",
                        "rel": "org",
                        "type": "GET"
                    }
                ],
                "check_in_classroom": [
                    {
                        "href": "/api/v2/group/checkInGroupId1",
                        "rel": "group",
                        "type": "GET"
                    }
                ],
                "checked_in_by": [
                    {
                        "href": "/api/v2/people/checkedInById1",
                        "rel": "person",
                        "type": "GET"
                    }
                ]
            }
        ];
        momentsFindMock.mockImplementationOnce(() => (moments));

        const momentsService = new MomentsService(req);
        const result = await momentsService.getMoments("orgId1");

        expect(momentsService).toBeInstanceOf(MomentsService);
        expect(result).toHaveLength(1);
        expect(result).toStrictEqual(momentsResponse);
        expect(momentsFindMock).toHaveBeenCalledWith({ orgId: "orgId1", date: { $gte: "03/30/2022", $lte: "03/31/2022" }, momentType: "momentType1" }, { "_id": 1, "checkInGroupId": 1, "checkedInById": 1, "comment": 1, "createdAt": 1, "createdByPersonId": 1, "date": 1, "momentType": 1, "orgId": 1, "owner": 1, "taggedPeople": 1, "time": 1 }, { limit: 1, offset: 0 });
    });

    it('should return a moment object with specified fields', async () => {
        const mockId = '1234567890';
        const moment = {
            "_id": "1234567890",
            "createdAt": 1637699062948,
            "createdByPersonId": "69BREkBxEe8an4TbA",
            "createdBy": "ZdkhmaEScuuboPFbH",
            "owner": "HMKtg6wfCM7b2bn8E",
            "taggedPeople": [
                "HMKtg6wfCM7b2bn8E"
            ],
            "momentType": "checkin",
            "comment": "Checked into Ready-2-Learn",
            "orgId": "fuQK4wqfE2yiZqKt6",
            "checkInGroupId": "zvWiPSxhkgGBvq7A6",
            "checkedInById": "69BREkBxEe8an4TbA"
        }
        momentFindByIdMock.mockImplementationOnce(() => moment)
        const service = new MomentsService();
        const fieldsToRetrieve = { 
            _id: 1, 
            createdAt: 1,
            createdByPersonId: 1, 
            owner:1,
            taggedPeople: 1,
            momentType: 1,
            comment: 1,
            orgId: 1,   
            checkInGroupId: 1, 
            checkedInById: 1 
        };
        const result = await service.getMomentById(mockId);
        expect(mockId).not.toBeNull();
        expect(mockId).not.toBeUndefined();
        expect(result._id).toEqual(mockId);
        expect(result).toEqual(moment);
        expect(momentFindByIdMock).toHaveBeenCalledWith({_id: mockId}, fieldsToRetrieve);
    });

    it('should return a moment object with snake case keys and required hateos links', async () => {
        const moment = {
            "_id": "zyuNPYYy9WdyZrFq5",
            "createdAt": 1637699062948,
            "createdByPersonId": "69BREkBxEe8an4TbA",
            "createdBy": "ZdkhmaEScuuboPFbH",
            "owner": "HMKtg6wfCM7b2bn8E",
            "taggedPeople": [
                "HMKtg6wfCM7b2bn8E"
            ],
            "momentType": "checkin",
            "comment": "Checked into Ready-2-Learn",
            "orgId": "fuQK4wqfE2yiZqKt6",
            "checkInGroupId": "zvWiPSxhkgGBvq7A6",
            "checkedInById": "69BREkBxEe8an4TbA"
        }
        createdByFindOneMock.mockImplementationOnce(() => (
            {
                "_id": "69BREkBxEe8an4TbA",
                "type": "staff",
                "firstName": "Matt",
                "lastName": "Coffman",
                "orgId": "iSNuHxc6zq3mi8CzZ"
            }
        ));
        ownerFindOneMock.mockImplementationOnce(() => (
            {
                "_id": "HMKtg6wfCM7b2bn8E",
                "firstName": "Hannah",
                "lastName": "Abbott",
                "type": "person",
                "orgId": "nTvbx24M2dbM9w6tu"
            }
        ));
        peopleFindMock.mockImplementationOnce( ()=> 
            [
                {
                    "_id": "HMKtg6wfCM7b2bn8E",
                    "firstName": "Hannah",
                    "lastName": "Abbott",
                    "orgId": "nTvbx24M2dbM9w6tu",
                }
            ]
        )
        orgsFindOneMock.mockImplementationOnce(() => ({
            "_id": "fuQK4wqfE2yiZqKt6",
            "name": "Flemington",
            "registrationSource": "app",
            "parentOrgId": "GtAoTHqGeLk9BR8iw",
        }));
        groupsFindOneMock.mockImplementationOnce(() => ({
            "_id": "zvWiPSxhkgGBvq7A6",
            "name": "Ready-2-Learn",
            "capacity": "10",
            "createdBy": "s3xrYq3pkm5Q7adkW",
            "orgId": "nTvbx24M2dbM9w6tu"
        }));
        checkedInByFindOneMock.mockImplementationOnce(() => ({
            "_id": "69BREkBxEe8an4TbA",
            "type": "admin",
            "firstName": "Matt",
            "lastName": "Coffman",
            "orgId": "iSNuHxc6zq3mi8CzZ"
        }));
        const service = new MomentsService();
        expect(await service.formatOneMoments(moment)).toStrictEqual(
            {
                "id": "zyuNPYYy9WdyZrFq5",
                "moment_timestamp": "2021-11-23T20:24:22.948Z",
                "created_by": {
                    "id": "69BREkBxEe8an4TbA",
                    "values": {
                        "name": "Matt Coffman"
                    },
                    "links": [
                        {
                            "href": "/api/v2/people/69BREkBxEe8an4TbA",
                            "rel": "person",
                            "type": "GET"
                        }
                    ]
                },
                "owner": {
                    "id": "HMKtg6wfCM7b2bn8E",
                    "values": {
                        "name": "Hannah Abbott"
                    },
                    "links": [
                        {
                            "href": "/api/v2/people/HMKtg6wfCM7b2bn8E",
                            "rel": "person",
                            "type": "GET"
                        }
                    ]
                },
                "tagged_people": [
                    {
                        "id": "HMKtg6wfCM7b2bn8E",
                        "values": {
                            "name": "Hannah Abbott"
                        },
                        "links": [
                            {
                                "href": "/api/v2/people/HMKtg6wfCM7b2bn8E",
                                "rel": "person",
                                "type": "GET"
                            }
                        ]
                    }
                ],
                "moment_type": "checkin",
                "comment": "Checked into Ready-2-Learn",
                "org": {
                    "id": "fuQK4wqfE2yiZqKt6",
                    "values": {
                        "name": "Flemington"
                    },
                    "links": [
                        {
                            "href": "/api/v2/orgs/fuQK4wqfE2yiZqKt6",
                            "rel": "org",
                            "type": "GET"
                        }
                    ]
                },
                "check_in_classroom": {
                    "id": "zvWiPSxhkgGBvq7A6",
                    "values": {
                        "name": "Ready-2-Learn"
                    },
                    "links": [
                        {
                            "href": "/api/v2/group/zvWiPSxhkgGBvq7A6",
                            "rel": "group",
                            "type": "GET"
                        }
                    ]
                },
                "checked_in_by": {
                    "id": "69BREkBxEe8an4TbA",
                    "values": {
                        "name": "Matt Coffman"
                    },
                    "links": [
                        {
                            "href": "/api/v2/people/69BREkBxEe8an4TbA",
                            "rel": "person",
                            "type": "GET"
                        }
                    ]
                }
            }
        );
    });
});