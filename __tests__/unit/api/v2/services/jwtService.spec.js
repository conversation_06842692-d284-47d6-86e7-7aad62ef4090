import { Meteor } from 'meteor/meteor';
import { JwtService } from "../../../../../api/v2/services/jwtService";
import { KmsService } from "../../../../../api/v2/services/kmsService";
import jwtDecode from "jwt-decode";
import base64url from "base64url";
import { beforeEach } from "@jest/globals";
import { mockCollection } from "../../../../helpers/collectionMock";
import { RefreshTokenRepository } from "../../../../../api/v2/repositories/refreshTokenRepository";

jest.mock('../../../../../api/v2/services/kmsService');
jest.mock('../../../../../api/v2/repositories/refreshTokenRepository');
const refreshInsertMock = RefreshTokenRepository.prototype.insertOneByToken;
const refreshFindMock = RefreshTokenRepository.prototype.findOneByToken;

Meteor.settings = {
    "kmsKeyId": "alias/api-jwt-signer",
    "kmsAccessKey": "foo",
    "kmsSecretKey": "bar",
    "kmsRegion": "us-east-1",
    "kmsEndpoint": "http://localhost:4566",
    "kmsSigningAlgo": "ECDSA_SHA_512",
    "kmsAlgoId": "ECDSA_SHA_512"
}
const refreshTokensCollection = mockCollection('RefreshTokens');

describe('JwtService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.useRealTimers()
    });

    it('returns null for a malformed user', async () => {
        const service = new JwtService();
        const token = await service.getJwt({ _id: '123'});
        expect(token).toBeNull();
    });

    it('should return a JWT', async () => {
        KmsService.prototype.sign = jest.fn(() => 'signature');
        const service = new JwtService();
        const token = await service.getJwt({emails: [{address: '<EMAIL>'}], _id: '123'});
        const tokenParts = token.split('.');
        expect(tokenParts.length).toBe(3);
        const decodedHeader = jwtDecode(token, { header: true });
        expect(decodedHeader).toStrictEqual({ typ: 'JWT', alg: Meteor.settings.kmsAlgoId });
        const decodedClaims = jwtDecode(token);
        expect(decodedClaims).toStrictEqual({
            iat: expect.any(Number),
            exp: expect.any(Number),
            user_id: '123',
            username: '<EMAIL>'
        });
        expect(tokenParts[2]).toBe('signature');
        expect(KmsService.prototype.sign).toHaveBeenCalledWith(`${tokenParts[0]}.${tokenParts[1]}`, Meteor.settings.kmsKeyId);
    });

    it('should decode a JWT', async () => {
        KmsService.prototype.verify = jest.fn(() => true);
        const service = new JwtService();
        const token = await service.getJwt({emails: [{address: '<EMAIL>'}], _id: '456'});
        expect(await service.decodeJwt(token)).toStrictEqual({
            iat: expect.any(Number),
            exp: expect.any(Number),
            user_id: '456',
            username: '<EMAIL>'
        });
    });

    it('should return null for an expired JWT', async () => {
        const service = new JwtService();
        const token = await service.getJwt({emails: [{address: '<EMAIL>'}], _id: '456'});
        jest.useFakeTimers().setSystemTime(new Date('2037-01-01'));
        expect(await service.decodeJwt(token)).toBeNull();
    });

    it('should return null for a JWT with a future iat', async () => {
        const service = new JwtService();
        const token = await service.getJwt({emails: [{address: '<EMAIL>'}], _id: '456'});
        jest.useFakeTimers().setSystemTime(new Date('2020-01-01'));
        expect(await service.decodeJwt(token)).toBeNull();
    });

    it('should return null for a JWT with a bad signature', async () => {
        KmsService.prototype.verify = jest.fn(() => false);
        const service = new JwtService();
        const token = await service.getJwt({emails: [{address: '<EMAIL>'}], _id: '456'});
        expect(await service.decodeJwt(token)).toBeNull();
    });

    it('creates a refresh token', async () => {
        const service = new JwtService();
        refreshFindMock.mockImplementationOnce(() => null);
        const now = new Date().valueOf();
        const token = await service.createRefreshToken('123');
        expect(token).toMatch(/^[a-f0-9]{10,}$/);
        expect(await refreshInsertMock.mock.calls[0][0]).toStrictEqual({
            userId: '123',
            token: token,
            expiresAt: expect.any(Date)
        });
        expect(await refreshInsertMock.mock.calls[0][0].expiresAt.valueOf()).toBeGreaterThanOrEqual(now + (72 * 1000 * 60 * 60));
        expect(await refreshInsertMock.mock.calls[0][0].expiresAt.valueOf()).toBeLessThan(now + (72 * 1000 * 60 * 60 + 5000));
    });

    it('find a refresh token', async () => {
        const service = new JwtService();
        refreshFindMock.mockImplementationOnce(() => {
            return {
                userId: '123',
                token: 'abc',
                expiresAt: new Date().valueOf() + 1000
            }
        });
        expect(await service.getRefreshToken('abc')).toStrictEqual({
            userId: '123',
            token: 'abc',
            expiresAt: expect.any(Number)
        });
        refreshFindMock.mockImplementationOnce(() => {
            return {
                userId: '123',
                token: 'abc',
                expiresAt: new Date().valueOf() - 1000
            }
        });
        expect(await service.getRefreshToken('abc')).toBeNull();
    });
});