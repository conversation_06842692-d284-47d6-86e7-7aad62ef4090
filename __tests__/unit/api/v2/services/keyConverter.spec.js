import { KeyConverter } from '../../../../../api/v2/services/keyConverter';

describe('KeyConverter', () => {
    let converter;
    beforeEach(() => {
        converter = new KeyConverter();
    });
    describe('convertKeysToSnakeCase', () => {
        it('should convert _id key to id', () => {
            const input = {
                _id: '123456',
                name: '<PERSON><PERSON>',
                parentOrgId: "abcdef"
            }
            const expectedOutput = {
                id: '123456',
                name: '<PERSON><PERSON>',
                parent_org_id: "abcdef"
            };
            expect(converter.convertKeysToSnakeCase(input)).toEqual(expectedOutput);
        });

        it('should convert keys to snake case for simple & nested objects', () => {
            const input = {
                _id: "fuQK4wqfE2yiZqKt6",
                name: "<PERSON><PERSON>",
                enabledMomentTypes: ["heroMoment", "covidHealth", "safety"],
                parentOrgId: "abcdef",
                whiteLabel: {
                    primaryColor: "#218ACA",
                    secondaryColor: "#8BC53D",
                    pushwooshProfile: "pushwooshLightbridge",
                }
            }
            const expectedOutput = {
                id: "fuQK4wqfE2yiZqKt6",
                name: "Flemington",
                enabled_moment_types: ["heroMoment", "covidHealth", "safety"],
                parent_org_id: "abcdef",
                white_label: {
                    primary_color: "#218ACA",
                    secondary_color: "#8BC53D",
                    pushwoosh_profile: "pushwooshLightbridge",
                }
            }
            expect(converter.convertKeysToSnakeCase(input)).toEqual(expectedOutput);
        });

        it('should skip specified keys', () => {
            const input = {
                _id: "fuQK4wqfE2yiZqKt6",
                name: "Flemington",
                enabledMomentTypes: ["heroMoment", "covidHealth", "safety"],
                parentOrgId: "abcd1234",
                customizations: {
                    "moments/ouch/enabled": false,
                    "people/types/customerSpecificProfileFields": true,
                    "people/types/customerSpecificStaffProfileFields": true
                }
            }
            const expectedOutput = {
                id: "fuQK4wqfE2yiZqKt6",
                name: "Flemington",
                enabled_moment_types: ["heroMoment", "covidHealth", "safety"],
                parent_org_id: "abcd1234",
                customizations: {
                    "moments/ouch/enabled": false,
                    "people/types/customerSpecificProfileFields": true,
                    "people/types/customerSpecificStaffProfileFields": true
                }
            }
            expect(converter.convertKeysToSnakeCase(input, ['customizations'])).toEqual(expectedOutput);
        });

        it('should preserve keys that are already in snake case', () => {
            const input = {
                _id: "fuQK4wqfE2yiZqKt6",
                name: "Flemington",
                parentOrgId: "abcd1234",
                whiteLabel: {
                    primaryColor: "#218ACA",
                    ROOT_URL: "https://lightbridge.momentpath.com",
                }
            }
            const expectedOutput = {
                id: "fuQK4wqfE2yiZqKt6",
                name: "Flemington",
                parent_org_id: "abcd1234",
                white_label: {
                    primary_color: "#218ACA",
                    ROOT_URL: "https://lightbridge.momentpath.com",
                }
            }
            expect(converter.convertKeysToSnakeCase(input)).toEqual(expectedOutput);
        });

        it('should convert keys to snake case for an array of objects', () => {
            const input = [
                {
                    _id: "id100",
                    name: "Flemington",
                    enabledMomentTypes: ["heroMoment100", "covidHealth100", "safety100"],
                    parentOrgId: "prid100",
                    whiteLabel: {
                        primaryColor: "#218ACA",
                        secondaryColor: "#8BC53D"
                    }
                },
                {
                    _id: "id200",
                    name: "Flemington",
                    enabledMomentTypes: ["heroMoment200", "covidHealth200", "safety200"],
                    parentOrgId: "prid200",
                    whiteLabel: {
                        primaryColor: "#000000",
                        secondaryColor: "#FFFFFF"
                    }
                }
            ];
            const expectedOutput = [
                {
                    id: "id100",
                    name: "Flemington",
                    enabled_moment_types: ["heroMoment100", "covidHealth100", "safety100"],
                    parent_org_id: "prid100",
                    white_label: {
                        primary_color: "#218ACA",
                        secondary_color: "#8BC53D"
                    }
                },
                {
                    id: "id200",
                    name: "Flemington",
                    enabled_moment_types: ["heroMoment200", "covidHealth200", "safety200"],
                    parent_org_id: "prid200",
                    white_label: {
                        primary_color: "#000000",
                        secondary_color: "#FFFFFF"
                    }
                }
            ];
            expect(converter.convertKeysToSnakeCase(input)).toEqual(expectedOutput);
        });

        it('should handle empty array', () => {
            const input = [];
            const expectedOutput = [];
            expect(converter.convertKeysToSnakeCase(input)).toEqual(expectedOutput);
        });

        it('should skip specified keys in an array of objects', () => {
            const input = [
                {
                    _id: "id100",
                    name: "Flemington",
                    parentOrgId: "prid100",
                    customizations: {
                        "moments/ouch/enabled": false,
                        "people/types/customerSpecificProfileFields": true,
                        "people/types/customerSpecificStaffProfileFields": true
                    },
                    whiteLabel: {
                        primaryColor: "#218ACA",
                        secondaryColor: "#8BC53D"
                    }
                },
                {
                    _id: "id200",
                    name: "Flemington",
                    parentOrgId: "prid200",
                    customizations: {
                        "moments/checkin/notifyWithoutReservation": true,
                        "moments/medical/enabled": true,
                        "moments/medical/useProfileMedications": true
                    },
                    whiteLabel: {
                        primaryColor: "#000000",
                        secondaryColor: "#FFFFFF"
                    }
                }
            ];
            const expectedOutput = [
                {
                    id: "id100",
                    name: "Flemington",
                    parent_org_id: "prid100",
                    customizations: {
                        "moments/ouch/enabled": false,
                        "people/types/customerSpecificProfileFields": true,
                        "people/types/customerSpecificStaffProfileFields": true
                    },
                    white_label: {
                        primary_color: "#218ACA",
                        secondary_color: "#8BC53D"
                    }
                },
                {
                    id: "id200",
                    name: "Flemington",
                    parent_org_id: "prid200",
                    customizations: {
                        "moments/checkin/notifyWithoutReservation": true,
                        "moments/medical/enabled": true,
                        "moments/medical/useProfileMedications": true
                    },
                    white_label: {
                        primary_color: "#000000",
                        secondary_color: "#FFFFFF"
                    }
                }
            ];
            expect(converter.convertKeysToSnakeCase(input, ['customizations'])).toEqual(expectedOutput);
        });

        it('should convert keys to snake case but preserve keys with underscores in an array of objects', () => {
            const input = [
                {
                    _id: "id100",
                    name: "Flemington",
                    parentOrgId: "prid100",
                    whiteLabel: {
                        primaryColor: "#218ACA",
                        secondaryColor: "#8BC53D",
                        ROOT_URL: "https://lightbridge.momentpath.com"
                    }
                },
                {
                    _id: "id200",
                    name: "Flemington",
                    parentOrgId: "prid200",
                    whiteLabel: {
                        primaryColor: "#000000",
                        secondaryColor: "#FFFFFF",
                        ROOT_URL: "https://lightbridge.momentpath.com"
                    }
                }
            ];
            const expectedOutput = [
                {
                    id: "id100",
                    name: "Flemington",
                    parent_org_id: "prid100",
                    white_label: {
                        primary_color: "#218ACA",
                        secondary_color: "#8BC53D",
                        ROOT_URL: "https://lightbridge.momentpath.com"
                    }
                },
                {
                    id: "id200",
                    name: "Flemington",
                    parent_org_id: "prid200",
                    white_label: {
                        primary_color: "#000000",
                        secondary_color: "#FFFFFF",
                        ROOT_URL: "https://lightbridge.momentpath.com"
                    }
                }
            ];
            expect(converter.convertKeysToSnakeCase(input)).toEqual(expectedOutput);
        });
    });
});