import expect from 'expect';
import { afterAll, beforeEach, describe, jest, it } from '@jest/globals';
import moment from 'moment-timezone';
import { mockCollection } from '../../../helpers/collectionMock';
import { ReportExportService } from '../../../../server/reportExportService';
import { OrgCollectionMethods } from '../../../fixtures/methods/orgCollectionMethods';
import { PUNCH_CARD_TYPE } from '../../../../lib/constants/billingConstants';
import { Reservations } from '../../../../lib/collections/reservations';
import { People } from '../../../../lib/collections/people';
import { Orgs } from '../../../../lib/collections/orgs';
import { Invoices } from '../../../../lib/collections/invoices';

const orgFixture = require('../../../fixtures/orgs/singleOrg.json');
orgFixture.getTimezone = OrgCollectionMethods.getTimezone.bind(orgFixture);

describe('ReportExportService', () => {
    describe('quickBooksCsvExport', () => {
        afterEach(async () => {
            jest.clearAllMocks();
        });

        beforeEach(async () => {
            orgFixture.getScheduleTypes = jest.fn().mockReturnValue(orgFixture.valueOverrides.scheduleTypes);
            orgFixture.hasCustomization = jest.fn().mockReturnValue(false); // For checking if org has weekends in getPeriodByEffectiveDate
        });

        it('should generate an array of payouts details for CSV export', async () => {
            const payouts = [
                { type: 'payment', id: 'payment1', source: { id: 'transaction1' }, amount: 100.0, },
                { type: 'refund', id: 'refund1', source: { id: 'transaction2' }, amount: 50.0, },
                { type: 'adjustment', id: 'adjustment1', source: { id: 'transaction3' }, amount: 25.0, },
                { type: 'payment', id: 'adjustment2', source: { id: 'transaction4' }, amount: 25.0, },
            ];

            const orgsMock = Orgs.findOneAsync;
            orgsMock.mockImplementation(() => (orgFixture));

            const enrollmentDate = moment.tz('11/07/2023', 'MM/DD/YYYY', 'America/Chicago').valueOf();
            const invoicesMock = Invoices.findOneAsync;
            invoicesMock.mockImplementationOnce(() => ({
                _id: 'invoice1',
                orgId: orgFixture._id,
                personId: 'personId',
                lineItems: [
                    { amount: 200, enrolledPlan: { _id: orgFixture.billing.plansAndItems[0]._id, bundlePlanId: '123', reservationId: 'reservationId', enrollmentDate } }
                ]
            }));
            invoicesMock.mockImplementationOnce(() => ({
                _id: 'invoice2',
                orgId: orgFixture._id,
                personId: 'personId',
                lineItems: [
                    { amount: 70, originalItem: { _id: orgFixture.billing.plansAndItems[2]._id, enrollmentDate } }
                ]
            }));
            invoicesMock.mockImplementationOnce(() => ({
                _id: 'invoice3',
                orgId: orgFixture._id,
                personId: 'personId',
                lineItems: [
                    { amount: 45, originalItem: { _id: orgFixture.billing.plansAndItems.find(p => p.type === PUNCH_CARD_TYPE)._id, enrollmentDate } }
                ]
            }));

            const peopleMock = People.findOneAsync;
            peopleMock.mockImplementation(() => ({ _id: 'personId', firstName: 'John', lastName: 'Doe' }));

            const reservationsMock = Reservations.findOneAsync;
            reservationsMock.mockImplementation(() => ({ _id: 'reservationId', selectedPerson: 'personId', scheduleType: 'type1' }));

            const reportData = await ReportExportService.quickBooksCsvExport(payouts);

            expect(reportData).toHaveLength(3); // Number of payouts
            expect(invoicesMock).toHaveBeenCalledTimes(4); // Called for each payout
            expect(orgsMock).toHaveBeenCalledTimes(3); // Called for each payout
            expect(peopleMock).toHaveBeenCalledTimes(3); // Called for each payout
            expect(reservationsMock).toHaveBeenCalledTimes(1); // Called only once

            console.log(reportData);
            expect(reportData).toEqual([
                [
                    '321-Mariposa Local', 'John Doe', '',
                    'Infant Full Time (bundle)', '', '',
                    '', '', '',
                    '', 'Payment', 'Sale',
                    'invoice1', 'Credit Card', '',
                    '', '0', '0',
                    200, '', '',
                    '', '', '',
                    '', '', '',
                    '11/06/2023', '11/10/2023', '', ''
                ],
                [
                    '321-Mariposa Local', 'John Doe',
                    '',                   'Enrollment Feez',
                    '',                   '',
                    '',                   '',
                    '',                   '',
                    'Refund',             'Refund',
                    'invoice2',           'Credit Card',
                    '',                   '',
                    '0',                  '0',
                    70,                   '',
                    '',                   '',
                    '',                   '',
                    '',                   '',
                    '',                   '',
                    '',                   '',
                    ''
                ],
                [
                    '321-Mariposa Local', 'John Doe',
                    '',                   'My Punch Card',
                    '',                   '',
                    '',                   '',
                    '',                   '',
                    'Chargeback',         'Chargeback',
                    'invoice3',           'Credit Card',
                    '',                   '',
                    '0',                  '0',
                    45,                   '',
                    '',                   '',
                    '',                   '',
                    '',                   '',
                    '',                   '',
                    '',                   '',
                    ''
                ]
            ]);
        });
    });
});