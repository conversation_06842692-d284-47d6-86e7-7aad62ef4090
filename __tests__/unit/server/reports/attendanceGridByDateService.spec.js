import { AttendanceGridByDateService } from '../../../../server/reports/attendanceGridByDateService';
import moment from 'moment-timezone';

const makeOrgWithHolidays = (holidays) => ({
    getHolidays: () => holidays
});

const baseReservation = (scheduleTypeId) => ({
    scheduleType: scheduleTypeId
});

describe('isScheduleTypePermittedOnDate', () => {
    const scheduleA = 'schedA';
    const scheduleB = 'schedB';

    const testDate = '2025-12-25';
    const startDateMoment = moment(testDate);

    it('should permit schedule if no holidays exist', () => {
        const org = makeOrgWithHolidays([]);
        const result = AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleA));
        expect(result).toBe(true);
    });

    it('should permit all schedules if holiday allows "All"', () => {
        const org = makeOrgWithHolidays([{
            date: testDate,
            dateType: 'individual',
            scheduleTypes: ['All'],
        }]);

        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleA))).toBe(true);
        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleB))).toBe(true);
    });

    it('should deny all schedules if holiday allows "None"', () => {
        const org = makeOrgWithHolidays([{
            date: testDate,
            dateType: 'individual',
            scheduleTypes: ['None'],
        }]);

        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleA))).toBe(false);
    });

    it('should permit only specific schedule types listed', () => {
        const org = makeOrgWithHolidays([{
            date: testDate,
            dateType: 'individual',
            scheduleTypes: [scheduleA],
        }]);

        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleA))).toBe(true);
        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleB))).toBe(false);
    });

    it('should treat legacy holiday (no dateType) as individual', () => {
        const org = makeOrgWithHolidays([{
            date: testDate,
            scheduleTypes: ['None'],
        }]);

        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleA))).toBe(false);
    });

    it('should ignore deleted holidays', () => {
        const org = makeOrgWithHolidays([{
            date: testDate,
            deleted: true,
            scheduleTypes: ['None'],
        }]);

        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleA))).toBe(true);
    });

    it('should match range-based holiday with permitted types', () => {
        const org = makeOrgWithHolidays([{
            dateType: 'range',
            startDate: '2025-12-24',
            endDate: '2025-12-26',
            scheduleTypes: [scheduleA],
        }]);

        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleA))).toBe(true);
        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleB))).toBe(false);
    });

    it('should not match a holiday outside of the date range', () => {
        const org = makeOrgWithHolidays([{
            dateType: 'range',
            startDate: '2025-12-20',
            endDate: '2025-12-24',
            scheduleTypes: ['All'],
        }]);

        expect(AttendanceGridByDateService.isScheduleTypePermittedOnDate(org, startDateMoment, baseReservation(scheduleA))).toBe(true);
    });
});