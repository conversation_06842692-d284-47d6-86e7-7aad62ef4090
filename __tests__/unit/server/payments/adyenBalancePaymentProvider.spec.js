import { Transfer } from '@adyen/api-library/lib/src/typings/transfers/transfer';
import { mockMeteor } from '../../../helpers/meteorMock';
import expect from 'expect';
import { beforeAll, afterAll, beforeEach, jest } from '@jest/globals';
import {
  ConfigurationSettingsService,
  ServerConfigurationConstants,
} from '../../../../server/config/configurationSettingsService';
import { AdyenBalancePaymentProvider } from '../../../../server/card_providers/adyenBalancePaymentProvider';
import { AdyenBalancePlatformProvider } from '../../../../server/balancePlatformProvider/adyenBalancePlatformProvider';
import { CheckoutAPI, TransfersAPI } from '@adyen/api-library';
import moment from 'moment-timezone';
import { AdyenProvider } from '../../../../server/card_providers/adyenProvider';
import { HistoryAuditService } from '../../../../server/historyAuditService';
import { Orgs } from '../../../../lib/collections/orgs';
import { Invoices } from '../../../../lib/collections/invoices';
import { BalancePayoutTransactions } from '../../../../lib/collections/balancePayoutTransactions';
import { People } from '../../../../lib/collections/people';
import {PublicConfigurationSettingsService} from "../../../../lib/config/publicConfigurationSettingsService";
import {BalanceTransfers} from "../../../../lib/collections/balanceTransfers";
// import { Invoices } from "../../../../lib/collections/invoices";

jest.mock('meteor/http');
global.fetch = jest.fn();

// Mock moment-timezone
jest.mock('moment-timezone', () => {
  const moment = jest.requireActual('moment');
  const momentTimezone = jest.requireActual('moment-timezone');

  momentTimezone.tz = jest.fn((dateString, timezone) => {
    return moment.utc(dateString).tz(timezone);
  });

  return momentTimezone;
});

describe("AdyenBalancePaymentProvider class tests", () => {
  let orgsMock = null;
  const mockAdyenAccountId = "adyenAccountId123";
  const mockAdyenApiKey = "adyenApiKey123";
  const mockAdyenBalancePlatformApiKey = "adyenBalancePlatformApiKey123";
  const mockAdyenLegalEntityManagementApiKey =
    "adyenLegalEntityManagementApiKey123";
  const mockAdyenLiableBalanceAccountId = "adyenLiableBalanceAccountId123";
  const mockAdyenTransferFundsUrl = "https://adyen-transfer-funds-url.com";

  beforeAll(() => {
    mockMeteor();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    orgsMock = Orgs.findOneAsync;
    orgsMock.mockReturnValue({
      _id: "org123",
      billing: {
        adyenInfo: {
          accountCode: "orgAccountCode123",
        },
      },
    });
    jest
      .spyOn(ConfigurationSettingsService, "getServerConfigurationSetting")
      .mockImplementation((key) => {
        if (key === ServerConfigurationConstants.ADYEN_ACCOUNT_ID) {
          return mockAdyenAccountId;
        }
        if (key === ServerConfigurationConstants.ADYEN_API_KEY) {
          return mockAdyenApiKey;
        }
        if (
          key === ServerConfigurationConstants.ADYEN_BALANCE_PLATFORM_API_KEY
        ) {
          return mockAdyenBalancePlatformApiKey;
        }
        if (
          key ===
          ServerConfigurationConstants.ADYEN_LEGAL_ENTITY_MANAGEMENT_API_KEY
        ) {
          return mockAdyenLegalEntityManagementApiKey;
        }
        if (
          key === ServerConfigurationConstants.ADYEN_LIABLE_BALANCE_ACCOUNT_ID
        ) {
          return mockAdyenLiableBalanceAccountId;
        }
        if (key === ServerConfigurationConstants.ADYEN_TRANSFER_FUNDS_URL) {
          return mockAdyenTransferFundsUrl;
        }
        return null;
      });
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  describe("getTransfersClient", () => {
    it("should return a new TransfersAPI instance with the correct configuration", () => {
      const client = AdyenBalancePaymentProvider.getTransfersClient();
      expect(client).toBeInstanceOf(TransfersAPI);
      expect(client.client.config.apiKey).toBe(mockAdyenBalancePlatformApiKey);
    });
  });

  describe("transferFunds", () => {
    const transferInfo = {
      amount: {
        currency: "USD",
        value: 1000,
      },
      balanceAccountId: "senderAccountId123",
      counterparty: {
        balanceAccountId: "receiverAccountId123",
      },
      category: "payment",
      referenceForBeneficiary: "Payment Reference",
      description: "Transfer Funds Description",
      reference: "uniqueReference123",
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should transfer funds and return true when authorized", async () => {
      const mockTransfersAPI = {
        TransfersApi: {
          transferFunds: jest
            .fn()
            .mockResolvedValue({status: Transfer.StatusEnum.Authorised}),
        },
      };

      jest
        .spyOn(AdyenBalancePaymentProvider, "getTransfersClient")
        .mockReturnValue(mockTransfersAPI);

      const result = await AdyenBalancePaymentProvider.transferFunds(
        transferInfo
      );

      expect(result).toStrictEqual({status: "authorised"});
      expect(mockTransfersAPI.TransfersApi.transferFunds).toHaveBeenCalledWith({
        amount: transferInfo.amount,
        balanceAccountId: transferInfo.balanceAccountId,
        counterparty: transferInfo.counterparty,
        category: transferInfo.category,
        referenceForBeneficiary: transferInfo.referenceForBeneficiary,
        description: transferInfo.description,
        reference: transferInfo.reference,
      });
    });

    it("should throw an error if the transfer fails", async () => {
      const mockError = new Error("Transfer failed");

      const mockTransfersAPI = {
        TransfersApi: {
          transferFunds: jest.fn().mockRejectedValue(mockError),
        },
      };

      jest
        .spyOn(AdyenBalancePaymentProvider, "getTransfersClient")
        .mockReturnValue(mockTransfersAPI);

      await expect(
        AdyenBalancePaymentProvider.transferFunds(transferInfo)
      ).rejects.toThrowError(mockError);
    });
  });

  describe("assessChargebackFee", () => {
    const options = {
      balanceAccountId: "balanceAccountId123",
      orgId: "orgId123",
      amount: "100",
      merchantReference: "merchantReference123",
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should assess chargeback fee successfully", async () => {
      const mockBalanceAccount = {
        status: "active",
        balances: [{currency: "USD", available: 500}],
      };

      const mockTransfersAPI = {
        TransfersApi: {
          transferFunds: jest.fn().mockResolvedValue(true),
        },
      };

      jest
        .spyOn(AdyenBalancePlatformProvider, "getBalanceAccount")
        .mockResolvedValue(mockBalanceAccount);
      jest
        .spyOn(AdyenBalancePaymentProvider, "getTransfersClient")
        .mockReturnValue(mockTransfersAPI);
      jest
        .spyOn(AdyenBalancePaymentProvider, "transferFunds")
        .mockResolvedValue({status: "authorised"});
      jest
        .spyOn(ConfigurationSettingsService, "getServerConfigurationSetting")
        .mockReturnValue("defaultLiableAccountId");

      const res = await AdyenBalancePaymentProvider.assessChargebackFee(
        options
      );

      expect(res).toBe(true);
    });

    it("should throw an error if balance account is not found", async () => {
      jest
        .spyOn(AdyenBalancePlatformProvider, "getBalanceAccount")
        .mockRejectedValue(new Error("Balance account not found"));

      await expect(
        AdyenBalancePaymentProvider.assessChargebackFee(options)
      ).rejects.toThrowError("Balance account not found");
    });

    it("should throw an error if transfer fails", async () => {
      const mockBalanceAccount = {
        status: "active",
        balances: [{currency: "USD", available: 500}],
      };

      jest
        .spyOn(AdyenBalancePlatformProvider, "getBalanceAccount")
        .mockResolvedValue(mockBalanceAccount);
      jest
        .spyOn(ConfigurationSettingsService, "getServerConfigurationSetting")
        .mockReturnValue("defaultLiableAccountId");
      jest
        .spyOn(AdyenBalancePaymentProvider, "transferFunds")
        .mockRejectedValue(new Error("Transfer failed"));

      await expect(
        AdyenBalancePaymentProvider.assessChargebackFee(options)
      ).rejects.toThrowError("Chargeback fee not assessed, transfer failed");
    });

    it("should throw an error if chargeback fee cannot be assessed due to insufficient balance", async () => {
      const mockBalanceAccount = {
        status: "active",
        balances: [{currency: "USD", available: 50}],
      };

      jest
        .spyOn(AdyenBalancePlatformProvider, "getBalanceAccount")
        .mockResolvedValue(mockBalanceAccount);
      jest
        .spyOn(ConfigurationSettingsService, "getServerConfigurationSetting")
        .mockReturnValue("defaultLiableAccountId");

      await expect(
        AdyenBalancePaymentProvider.assessChargebackFee(options)
      ).rejects.toThrowError();
    });
  });

  describe("billingPayoutsReport", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return data when given valid inputs', async () => {
      const options = {
        orgIds: ['org1'],
        startDate: '01/01/2023',
        endDate: '01/31/2023',
        excludeEndDate: false,
      };

      orgsMock.mockReturnValue({
        _id: 'org1',
        name: 'Test Org',
        billing: {
          adyenInfo: {
            balanceAccountId: 'balance123',
          },
        },
        getTimezone: jest.fn().mockReturnValue('UTC'),
      });

      const payouts = [
        {
          payoutDate: new Date('2023-01-15T12:00:00Z'),
          balancePC: 100,
          transactionId: 'txn123',
          id: 'payout1',
          reference: 'ref123',
        },
      ];

      let balancePayoutTransactionsMock = BalancePayoutTransactions.find;
      balancePayoutTransactionsMock.mockReturnValue({ fetchAsync: () => payouts });

      const result = await AdyenBalancePaymentProvider.billingPayoutsReport(options);

      expect(result).toEqual({
        data: [
          {
            arrival_date: new Date('2023-01-15T12:00:00Z').getTime(),
            amount: -100,
            id: 'txn123',
            ref: 'ref123',
            org: 'Test Org',
            orgId: 'org1',
          },
        ],
      });
    });

    it("should skip orgs without balanceAccountId", async () => {
      const options = {
        orgIds: ["org1", "org2"],
        startDate: "01/01/2023",
        endDate: "01/31/2023",
      };

      const orgWithoutBalanceAccount = {
        _id: "org1",
        name: "Org Without Balance Account",
        billing: {},
        getTimezone: jest.fn().mockReturnValue("UTC"),
      };

      const orgWithBalanceAccount = {
        _id: "org2",
        name: "Org With Balance Account",
        billing: {
          adyenInfo: {
            balanceAccountId: "balance456",
          },
        },
        getTimezone: jest.fn().mockReturnValue("UTC"),
      };

      orgsMock
        .mockReturnValueOnce(orgWithoutBalanceAccount)
        .mockReturnValueOnce(orgWithBalanceAccount);

      moment.tz.mockImplementation((dateString, format, timezone) => {
        return moment.utc(dateString, format).tz(timezone);
      });

      const payouts = [];

      let balancePayoutTransactionsMock = BalancePayoutTransactions.find;
      balancePayoutTransactionsMock.mockReturnValue({ fetchAsync: () => payouts });

      const result = await AdyenBalancePaymentProvider.billingPayoutsReport(
        options
      );

      expect(result).toEqual({data: []});
      expect(orgsMock).toHaveBeenCalledTimes(2);
      expect(balancePayoutTransactionsMock).toHaveBeenCalledTimes(1);
    });

    it("should handle excludeEndDate option correctly", async () => {
      const options = {
        startDate: "10/23/2021",
        endDate: "10/23/2024",
        excludeEndDate: true,
        orgIds: ["org1"],
      };

      orgsMock.mockReturnValue({
        _id: "org1",
        name: "Test Org",
        billing: {
          adyenInfo: {
            balanceAccountId: "balance123",
          },
        },
        getTimezone: jest.fn().mockReturnValue("UTC"),
      });

      let balancePayoutTransactionsMock = BalancePayoutTransactions.find;
      balancePayoutTransactionsMock.mockReturnValue({ fetchAsync: () => [] });

      await AdyenBalancePaymentProvider.billingPayoutsReport(options);

      // Verify that the aggregate query used the correct date range
      expect(balancePayoutTransactionsMock).toHaveBeenCalledWith(
        expect.objectContaining({
          bookingDate: {
            $gte: new Date("2021-10-23T00:00:00.000Z"),
            $lte: new Date("2024-10-23T00:00:00.000Z"),
          },
        })
      );
    });
  });

  describe('retrievePaymentFee', () => {
    let balanceTransfersMock;

    beforeEach(() => {
      balanceTransfersMock = BalancePayoutTransactions.find;
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    test('should return null if pspPaymentReference is null', async () => {
      const result = await AdyenBalancePaymentProvider.retrievePaymentFee(null, 'merchantAccountId');
      expect(result).toBeNull();
    });

    test('should return null if no paymentTransfers are found', async () => {
      balanceTransfersMock.mockReturnValue({
        fetchAsync: () => [],
      });

      const result = await AdyenBalancePaymentProvider.retrievePaymentFee('pspRef123', 'merchantAccountId');
      expect(result).toBeNull();
    });

    test('should calculate the fee correctly when transfers are found', async () => {
      const paymentTransfers = [
        // Total incoming amount: 10000 (sum of incoming transfers)
        {
          amount: { value: 10000 },
          direction: 'incoming',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
        // Merchant outgoing transfer: 2000
        {
          amount: { value: 2000 },
          direction: 'outgoing',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
      ];


      balanceTransfersMock = BalanceTransfers;
      balanceTransfersMock.find.mockReturnValueOnce({
        fetchAsync:jest.fn().mockImplementation(()=>paymentTransfers)
      });

      const result = await AdyenBalancePaymentProvider.retrievePaymentFee('pspRef123', 'merchantAccountId');
      expect(result).toBe(20);
    });

    test('should calculate fee as zero if netMerchantAmount equals totalIncoming', async () => {
      const paymentTransfers = [
        {
          amount: { value: 10000 },
          direction: 'incoming',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
      ];
      balanceTransfersMock = BalanceTransfers;
      balanceTransfersMock.find.mockReturnValueOnce({
        fetchAsync:jest.fn().mockImplementation(()=>paymentTransfers)
      });

      // balanceTransfersMock.mockReturnValue({
      //   fetchAsync: () => paymentTransfers,
      // });

      const result = await AdyenBalancePaymentProvider.retrievePaymentFee('pspRef123', 'merchantAccountId');
      expect(result).toBe(0);
    });

    test('should handle split fee going to liable account', async () => {
      const paymentTransfers = [
        {
          amount: { value: 10000 },
          direction: 'incoming',
          balanceAccount: { id: 'otherAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
        {
          amount: { value: 8000 },
          direction: 'incoming',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
      ];

      balanceTransfersMock = BalanceTransfers;
      balanceTransfersMock.find.mockReturnValueOnce({
        fetchAsync:jest.fn().mockImplementation(()=>paymentTransfers)
      });

      const result = await AdyenBalancePaymentProvider.retrievePaymentFee('pspRef123', 'merchantAccountId');
      expect(result).toBe(100);
    });

    test('should handle when there are outgoing transfers from the merchant (cost plus)', async () => {
      const paymentTransfers = [
        {
          amount: { value: 10000 },
          direction: 'incoming',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
        {
          amount: { value: 500 },
          direction: 'outgoing',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
        {
          amount: { value: 1500 },
          direction: 'outgoing',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
      ];

      balanceTransfersMock = BalanceTransfers;
      balanceTransfersMock.find.mockReturnValueOnce({
        fetchAsync:jest.fn().mockImplementation(()=>paymentTransfers)
      });

      const result = await AdyenBalancePaymentProvider.retrievePaymentFee('pspRef123', 'merchantAccountId');
      expect(result).toBe(20);
    });

    test('should return fee as zero when there is no outgoing or liable split', async () => {
      const paymentTransfers = [
        {
          amount: { value: 10000 },
          direction: 'incoming',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured',
        },
      ];

      balanceTransfersMock = BalanceTransfers;
      balanceTransfersMock.find.mockReturnValueOnce({
        fetchAsync:jest.fn().mockImplementation(()=>paymentTransfers)
      });

      const result = await AdyenBalancePaymentProvider.retrievePaymentFee('pspRef123', 'merchantAccountId');
      expect(result).toBe(0);
    });


    test('should return null if no transfers match the pspPaymentReference', async () => {
      balanceTransfersMock.mockReturnValue({
        fetchAsync: () => [],
      });

      const result = await AdyenBalancePaymentProvider.retrievePaymentFee('nonexistentRef', 'merchantAccountId');
      expect(result).toBeNull();
    });

    test('should only consider transfers with status Captured and category PlatformPayment', async () => {
      const paymentTransfers = [
        {
          amount: { value: 10000 },
          direction: 'incoming',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Pending', // Should be ignored
        },
        {
          amount: { value: 5000 },
          direction: 'incoming',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'OtherCategory', // Should be ignored
          status: 'Captured',
        },
        {
          amount: { value: 8000 },
          direction: 'incoming',
          balanceAccount: { id: 'merchantAccountId' },
          categoryData: { pspPaymentReference: 'pspRef123' },
          category: 'PlatformPayment',
          status: 'Captured', // Should be included
        },
      ];

      balanceTransfersMock = BalanceTransfers;
      balanceTransfersMock.find.mockReturnValueOnce({
        fetchAsync:jest.fn().mockImplementation(()=>paymentTransfers)
      });

      const result = await AdyenBalancePaymentProvider.retrievePaymentFee('pspRef123', 'merchantAccountId');
      expect(result).toBe(0);
    });
  });

  describe('convertPayoutTransactionToBillingDetail', () => {
    afterEach(() => {
      jest.clearAllMocks();
    });

    let invoicesMock;
    let peopleMock;

    beforeEach(() => {
      invoicesMock = Invoices.findOneAsync;
      peopleMock = People.findOneAsync
    });

    it('should process a payment transaction with positive amount', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.Capture,
        status: Transfer.StatusEnum.Captured,
        pspPaymentPspReference: 'psp_ref_123',
        balancePC: 100,
        pspPaymentMerchantReference: 'invoice-123_payment',
        bookingDate: '2023-10-01',
      };
      const balanceAccountId = 'balance_account_1';

      invoicesMock.mockReturnValue({
        id: 'invoice-123',
        invoiceNumber: 'INV-123',
        invoiceDate: '2023-09-30',
        credits: [
          {
            adyenInfo: { pspReference: 'psp_ref_123' },
            serviceCharge: 5,
            createdAt: '2023-09-30',
            payment_type: 'credit_card',
            paidBy: 'person_456',
          },
        ],
        personId: 'person_123',
      });

      jest.spyOn(AdyenBalancePaymentProvider, 'retrievePaymentFee').mockResolvedValue(10);

      peopleMock.mockImplementation((id) => {
        if (id === 'person_123') {
          return { firstName: 'John', lastName: 'Doe' };
        } else if (id === 'person_456') {
          return { firstName: 'Jane', lastName: 'Smith' };
        }
        return null;
      });

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'payment',
        arrival_date: new Date('2023-10-01').getTime(),
        amount: 105,
        fee: 5,
        net: 100,
        description: 'Invoice #INV-123 issued 2023-09-30 for John Doe',
        subDescription: 'Paid by Jane Smith on 9/30/2023 with credit_card',
        source: { id: 'psp_ref_123' },
        id: 'invoice-123',
        destinationType: 'invoice',
      });
    });

    it('should process a refund transaction', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.Refund,
        status: Transfer.StatusEnum.Refunded,
        pspPaymentPspReference: 'psp_ref_456',
        balancePC: -100,
        bookingDate: '2023-10-02',
      };
      const balanceAccountId = 'balance_account_1';

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'refund',
        arrival_date: new Date('2023-10-02').getTime(),
        amount: -100,
        fee: 0,
        net: -100,
        description: '',
        subDescription: '',
        source: { id: 'psp_ref_456' },
        id: null,
        destinationType: null,
      });
    });

    it('should process a bank transfer transaction', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.BankTransfer,
        status: Transfer.StatusEnum.Booked,
        pspPaymentPspReference: 'psp_ref_789',
        balancePC: 200,
        bookingDate: '2023-10-03',
      };
      const balanceAccountId = 'balance_account_1';

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'transfer',
        arrival_date: new Date('2023-10-03').getTime(),
        amount: 200,
        fee: 0,
        net: 200,
        description: '',
        subDescription: '',
        source: { id: 'psp_ref_789' },
        id: null,
        destinationType: null,
      });
    });

    it('should process a chargeback transaction', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.Chargeback,
        status: Transfer.StatusEnum.Chargeback,
        pspPaymentPspReference: 'psp_ref_101',
        balancePC: -150,
        bookingDate: '2023-10-04',
      };
      const balanceAccountId = 'balance_account_1';

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'adjustment',
        arrival_date: new Date('2023-10-04').getTime(),
        amount: -150,
        fee: 0,
        net: -150,
        description: '',
        subDescription: '',
        source: { id: 'psp_ref_101' },
        id: null,
        destinationType: null,
      });
    });

    it('should process an chargeback fee transaction with description', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.InternalTransfer,
        status: Transfer.StatusEnum.Booked,
        pspPaymentPspReference: 'psp_ref_202',
        balancePC: -50,
        bookingDate: '2023-10-05',
      };
      const balanceAccountId = 'balance_account_1';

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'adjustment',
        arrival_date: new Date('2023-10-05').getTime(),
        amount: -50,
        fee: 0,
        net: -50,
        description: 'Chargeback fee(s) for prior month',
        subDescription: '',
        source: { id: 'psp_ref_202' },
        id: null,
        destinationType: null,
      });
    });

    it('should return empty entryType when type and status are not matched', async () => {
      const payoutTransaction = {
        type: 'UnknownType',
        status: 'UnknownStatus',
        pspPaymentPspReference: 'psp_ref_303',
        balancePC: 0,
        bookingDate: '2023-10-06',
      };
      const balanceAccountId = 'balance_account_1';

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: '',
        arrival_date: new Date('2023-10-06').getTime(),
        amount: 0,
        fee: 0,
        net: 0,
        description: '',
        subDescription: '',
        source: { id: 'psp_ref_303' },
        id: null,
        destinationType: null,
      });
    });

    it('should process payment transaction when matchedPaymentEntrySourceInvoice is not found', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.Capture,
        status: Transfer.StatusEnum.Captured,
        pspPaymentPspReference: 'psp_ref_123',
        balancePC: 100,
        pspPaymentMerchantReference: 'invoice-999_payment',
        bookingDate: '2023-10-01',
      };
      const balanceAccountId = 'balance_account_1';

      invoicesMock.mockReturnValue(null);

      jest.spyOn(AdyenBalancePaymentProvider, 'retrievePaymentFee').mockResolvedValue(10);

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'payment',
        arrival_date: new Date('2023-10-01').getTime(),
        amount: 110,
        fee: 10,
        net: 100,
        description: '',
        subDescription: '',
        source: { id: 'psp_ref_123' },
        id: 'invoice-999',
        destinationType: 'invoice',
      });
    });

    it('should process payment transaction when matchedPaymentEntry is not found', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.Capture,
        status: Transfer.StatusEnum.Captured,
        pspPaymentPspReference: 'psp_ref_123',
        balancePC: 100,
        pspPaymentMerchantReference: 'invoice-123_payment',
        bookingDate: '2023-10-01',
      };
      const balanceAccountId = 'balance_account_1';

      invoicesMock.mockReturnValue({
        id: 'invoice-123',
        invoiceNumber: 'INV-123',
        invoiceDate: '2023-09-30',
        credits: [],
        personId: 'person_123',
      });

      jest.spyOn(AdyenBalancePaymentProvider, 'retrievePaymentFee').mockResolvedValue(10);

      peopleMock.mockImplementation((id) => {
        if (id === 'person_123') {
          return { firstName: 'John', lastName: 'Doe' };
        }
        return null;
      });

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'payment',
        arrival_date: new Date('2023-10-01').getTime(),
        amount: 110,
        fee: 10,
        net: 100,
        description: '',
        subDescription: '',
        source: { id: 'psp_ref_123' },
        id: 'invoice-123',
        destinationType: 'invoice',
      });
    });

    it('should handle missing matchedInvoicePerson', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.Capture,
        status: Transfer.StatusEnum.Captured,
        pspPaymentPspReference: 'psp_ref_123',
        balancePC: 100,
        pspPaymentMerchantReference: 'invoice-123_payment',
        bookingDate: '2023-10-01',
      };
      const balanceAccountId = 'balance_account_1';

      invoicesMock.mockReturnValue({
        id: 'invoice-123',
        invoiceNumber: 'INV-123',
        invoiceDate: '2023-09-30',
        credits: [
          {
            adyenInfo: { pspReference: 'psp_ref_123' },
            serviceCharge: 5,
            createdAt: '2023-09-30',
            payment_type: 'credit_card',
            paidBy: 'person_456',
          },
        ],
        personId: 'person_123',
      });

      jest.spyOn(AdyenBalancePaymentProvider, 'retrievePaymentFee').mockResolvedValue(10);

      peopleMock.mockImplementation((id) => {
        if (id === 'person_456') {
          return { firstName: 'Jane', lastName: 'Smith' };
        }
        return null;
      });

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'payment',
        arrival_date: new Date('2023-10-01').getTime(),
        amount: 105,
        fee: 5,
        net: 100,
        description: 'Invoice #INV-123 issued 2023-09-30',
        subDescription: 'Paid by Jane Smith on 9/30/2023 with credit_card',
        source: { id: 'psp_ref_123' },
        id: 'invoice-123',
        destinationType: 'invoice',
      });
    });

    it('should handle missing matchedPaymentPerson', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.Capture,
        status: Transfer.StatusEnum.Captured,
        pspPaymentPspReference: 'psp_ref_123',
        balancePC: 100,
        pspPaymentMerchantReference: 'invoice-123_payment',
        bookingDate: '2023-10-01',
      };
      const balanceAccountId = 'balance_account_1';

      invoicesMock.mockReturnValue({
        id: 'invoice-123',
        invoiceNumber: 'INV-123',
        invoiceDate: '2023-09-30',
        credits: [
          {
            adyenInfo: { pspReference: 'psp_ref_123' },
            serviceCharge: 5,
            createdAt: '2023-09-30',
            payment_type: 'credit_card',
            paidBy: 'person_456',
          },
        ],
        personId: 'person_123',
      });

      jest.spyOn(AdyenBalancePaymentProvider, 'retrievePaymentFee').mockResolvedValue(10);

      peopleMock.mockImplementation((id) => {
        if (id === 'person_123') {
          return { firstName: 'John', lastName: 'Doe' };
        }
        return null;
      });

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'payment',
        arrival_date: new Date('2023-10-01').getTime(),
        amount: 105,
        fee: 5,
        net: 100,
        description: '',
        subDescription: '',
        source: { id: 'psp_ref_123' },
        id: 'invoice-123',
        destinationType: 'invoice',
      });
    });

    it('should handle missing serviceCharge in matchedPaymentEntry', async () => {
      const payoutTransaction = {
        type: Transfer.TypeEnum.Capture,
        status: Transfer.StatusEnum.Captured,
        pspPaymentPspReference: 'psp_ref_123',
        balancePC: 100,
        pspPaymentMerchantReference: 'invoice-123_payment',
        bookingDate: '2023-10-01',
      };
      const balanceAccountId = 'balance_account_1';

      invoicesMock.mockReturnValue({
        id: 'invoice-123',
        invoiceNumber: 'INV-123',
        invoiceDate: '2023-09-30',
        credits: [
          {
            adyenInfo: { pspReference: 'psp_ref_123' },
            createdAt: '2023-09-30',
            payment_type: 'credit_card',
            paidBy: 'person_456',
            serviceCharge: 10
          },
        ],
        personId: 'person_123',
      });

      jest.spyOn(AdyenBalancePaymentProvider, 'retrievePaymentFee').mockResolvedValue(10);

      peopleMock.mockImplementation((id) => {
        if (id === 'person_123') {
          return { firstName: 'John', lastName: 'Doe' };
        } else if (id === 'person_456') {
          return { firstName: 'Jane', lastName: 'Smith' };
        }
        return null;
      });

      const result = await AdyenBalancePaymentProvider.convertPayoutTransactionToBillingDetail(payoutTransaction, balanceAccountId);

      expect(result).toEqual({
        type: 'payment',
        arrival_date: new Date('2023-10-01').getTime(),
        amount: 100,
        fee: 0,
        net: 100,
        description: 'Invoice #INV-123 issued 2023-09-30 for John Doe',
        subDescription: 'Paid by Jane Smith on 9/30/2023 with credit_card',
        source: { id: 'psp_ref_123' },
        id: 'invoice-123',
        destinationType: 'invoice',
      });
    });
  });

  describe('refundCharge', () => {
    let orgMock, invoiceMock, logRefundMock, currentUserMock, getApiClientMock, refundMock;

    beforeEach(() => {
      jest.clearAllMocks();

      // Mock getApiClient to completely bypass any real configuration or `apiKey` access
      getApiClientMock = jest.spyOn(AdyenProvider, 'getApiClient');
      getApiClientMock.mockResolvedValue({});

      // Mock the Modification class and its refund method
      refundMock = jest.fn().mockResolvedValue({ status: 'received' });

      // Mock Modification class with refund method
      jest.spyOn(CheckoutAPI.prototype, 'ModificationsApi', 'get').mockImplementation(() => ({
        refundCapturedPayment: refundMock
      }));

      // Mock HistoryAuditService.logRefund to just check if it's called with correct data
      logRefundMock = jest.spyOn(HistoryAuditService, 'logRefund').mockImplementation(() => {});

      const mockOrg = {
        _id: 'org123',
        billing: { adyenInfo: { accountCode: 'orgAccountCode123' } }
      }

      const orgsMock = Orgs.findOneAsync;
      orgsMock.mockImplementation(() => (mockOrg));


      // Mocking Invoices.findOne to return a test invoice
      invoiceMock = Invoices.findOneAsync;
      invoiceMock.mockReturnValue({
        _id: 'invoice123',
        credits: [
          { type: 'payment', adyenInfo: { pspReference: 'charge123' }, paidBy: 'parentId123' }
        ],
        invoiceNumber: 'INV123',
        personId: 'childId123'
      });

      // Manually mock the `updateByIdWithJournalEntry` method for the `Invoices` object
      Invoices.updateByIdWithJournalEntry = jest.fn();

      // Mocking current user
      currentUserMock = { _id: 'user123', personId: 'person123' };
    });

    it('should process the refund successfully', async () => {
      const options = {
        currentUser: currentUserMock,
        orgId: 'org123',
        refund_amount: 100,
        charge_id: 'charge123',
        invoiceId: 'invoice123',
        creditedByPersonId: 'person456',
        refund_reason: 'Test Refund',
        refund_note: 'Test Note',
        isRetry: false
      };

      await AdyenBalancePaymentProvider.refundCharge(options);

      expect(getApiClientMock).toHaveBeenCalled();
      expect(refundMock).toHaveBeenCalledWith(expect.stringMatching(options.charge_id), expect.objectContaining({
        merchantAccount: 'TendlyLLCDbaMomentPathMP',
        amount: {
          value: options.refund_amount * 100,
          currency: 'USD'
        },
        reference: options.invoiceId,
        splits: expect.any(Array)
      }));
      expect(logRefundMock).toHaveBeenCalledWith(expect.objectContaining({
        amount: parseFloat(options.refund_amount),
        invoiceNumber: 'INV123',
        invoiceId: 'invoice123',
        parentId: 'parentId123',
        childId: 'childId123',
        performedByUser: currentUserMock
      }));
    });

    it('should throw an error when refund fails', async () => {
      refundMock.mockRejectedValueOnce(new Error('Refund Error'));

      const options = {
        currentUser: currentUserMock,
        orgId: 'org123',
        refund_amount: 100,
        charge_id: 'charge123',
        invoiceId: 'invoice123',
        creditedByPersonId: 'person456',
        refund_reason: 'Test Refund',
        refund_note: 'Test Note',
        isRetry: false
      };

      await expect(AdyenBalancePaymentProvider.refundCharge(options)).rejects.toThrow(new Meteor.Error(500, "There was an error processing the refund."));
      expect(logRefundMock).not.toHaveBeenCalled();
    });

    it('should process the refund successfully when retrying', async () => {
      const options = {
        currentUser: currentUserMock,
        orgId: 'org123',
        refund_amount: 100,
        charge_id: 'charge123',
        invoiceId: 'invoice123',
        creditedByPersonId: 'person456',
        refund_reason: 'Test Refund',
        refund_note: 'Test Note',
        isRetry: true,
        retryCreditIndex: 0
      };

      const mockOrg = {
        _id: 'org123',
        billing: { adyenInfo: { accountCode: 'orgAccountCode123' } }
      }

      const orgsMock = Orgs.findOneAsync;
      orgsMock.mockImplementation(() => (mockOrg));

      await AdyenBalancePaymentProvider.refundCharge(options);

      expect(getApiClientMock).toHaveBeenCalled();
      expect(refundMock).toHaveBeenCalledWith(expect.stringMatching(options.charge_id), expect.objectContaining({
        merchantAccount: 'TendlyLLCDbaMomentPathMP',
        amount: {
          value: options.refund_amount * 100,
          currency: 'USD'
        },
        reference: options.invoiceId,
        splits: expect.any(Array)
      }));
      expect(Invoices.updateByIdWithJournalEntry).toHaveBeenCalled();
    });

    it('should process the refund without currentUser', async () => {
      const options = {
        currentUser: null,
        orgId: 'org123',
        refund_amount: 100,
        charge_id: 'charge123',
        invoiceId: 'invoice123',
        creditedByPersonId: 'person456',
        refund_reason: 'Test Refund',
        refund_note: 'Test Note',
        isRetry: false
      };

      const mockOrg = {
        _id: 'org123',
        billing: { adyenInfo: { accountCode: 'orgAccountCode123' } }
      }

      const orgsMock = Orgs.findOneAsync;
      orgsMock.mockImplementation(() => (mockOrg));

      await AdyenBalancePaymentProvider.refundCharge(options);

      expect(getApiClientMock).toHaveBeenCalled();
      expect(refundMock).toHaveBeenCalledWith(expect.stringMatching(options.charge_id), expect.objectContaining({
        merchantAccount: 'TendlyLLCDbaMomentPathMP',
        amount: {
          value: options.refund_amount * 100,
          currency: 'USD'
        },
        reference: options.invoiceId,
        splits: expect.any(Array)
      }));
      expect(logRefundMock).toHaveBeenCalledWith(expect.objectContaining({
        amount: parseFloat(options.refund_amount),
        invoiceNumber: 'INV123',
        invoiceId: 'invoice123',
        parentId: 'parentId123',
        childId: 'childId123',
        performedByUser: null
      }));
    });
  });
});
