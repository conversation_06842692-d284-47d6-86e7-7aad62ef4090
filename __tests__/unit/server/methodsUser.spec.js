import { Meteor } from 'meteor/meteor';
import { deleteUser } from '../../../server/methodsUser';
import { mockCollection } from '../../helpers/collectionMock';
import { People } from '../../../lib/collections/people';

const findOneAsyncMock = People.findOneAsync;
const usersFindOneMock = Meteor.users.findOne;
const removeAsyncMock = Meteor.users.removeAsync;

describe('Meteor User Methods', () => {
  describe('delete User', () => {
    const mockCurrentUser = {
      fetchPerson: jest.fn(),
      orgId: 'org123'
    };
    const mockCallAsync = jest.fn().mockResolvedValue(true);

    beforeEach(() => {
      jest.clearAllMocks();
      Meteor.userAsync.mockResolvedValue(mockCurrentUser);
      Meteor.callAsync= mockCallAsync,
      Meteor.isServer= true,
      Meteor.users= {
        findOneAsync: jest.fn().mockImplementation((query) => ({
          _id: 'user123',
          personId: query.$or[0].personId
        })),
        removeAsync: removeAsyncMock
      }
    });

    it('throws error user not found if user has been deleted and trying to delete again', async () => {
      const personId = 'person123';

      mockCurrentUser.fetchPerson.mockResolvedValue({
        type: 'admin',
        superAdmin: true,
        orgId: 'org123'
      });
      Meteor.users= {
        findOneAsync: jest.fn().mockImplementation((query) => (null)),
        removeAsync: removeAsyncMock
      }
      await expect(deleteUser(personId)).rejects.toThrow('404');
    });

    it('successfully deletes a user when current user is super admin', async () => {
      const personId = 'person123';
      const mockPerson = {
        _id: personId,
        type: 'user'
      };

      mockCurrentUser.fetchPerson.mockResolvedValue({
        type: 'admin',
        superAdmin: true,
        orgId: 'org123'
      });

      findOneAsyncMock.mockResolvedValue(mockPerson);
      removeAsyncMock.mockResolvedValue(true);

      await deleteUser(personId);

      expect(removeAsyncMock).toHaveBeenCalledWith('user123');
    });

    it('deletes an admin user and sends manage user updates', async () => {
      const personId = 'person123';
      const mockPerson = {
        _id: personId,
        type: 'admin'
      };
      const mockAssociatedUser = {
        _id: 'user123',
        personId: personId
      };

      mockCurrentUser.fetchPerson.mockResolvedValue({
        type: 'admin',
        superAdmin: true,
        orgId: 'org123'
      });

      findOneAsyncMock.mockResolvedValue(mockPerson);
      removeAsyncMock.mockResolvedValue(true);

      await deleteUser(personId);

      expect(mockCallAsync).toHaveBeenCalledWith('sendManageUserUpdates', mockAssociatedUser, true);
    });

    it('throws access denied error when user is not super admin', async () => {
      const personId = 'person123';

      mockCurrentUser.fetchPerson.mockResolvedValue({
        type: 'admin',
        superAdmin: false,
        orgId: 'org123'
      });

      await expect(deleteUser(personId)).rejects.toThrow('403');
    });

    it('throws error when user deletion fails', async () => {
      const personId = 'person123';
      const mockPerson = {
        _id: personId,
        type: 'user'
      };
      const mockAssociatedUser = {
        _id: 'user123',
        personId: personId
      };

      mockCurrentUser.fetchPerson.mockResolvedValue({
        type: 'admin',
        superAdmin: true,
        orgId: 'org123'
      });

      findOneAsyncMock.mockResolvedValue(mockPerson);
      usersFindOneMock.mockReturnValue(mockAssociatedUser);
      removeAsyncMock.mockRejectedValue(new Error('Deletion failed'));

      await expect(deleteUser(personId)).rejects.toThrow('500');
    });
  });

});
