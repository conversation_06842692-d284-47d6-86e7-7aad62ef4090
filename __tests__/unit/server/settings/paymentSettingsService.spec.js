import { mockCollection } from '../../../helpers/collectionMock';
import { PaymentSettingsService } from '../../../../server/settings/paymentSettingsService';
import { AvailableCustomizations } from '../../../../lib/customizations';
import { Orgs } from '../../../../lib/collections/orgs';

describe('PaymentSettingsService', () => {
    let orgFindOneAsyncMock;
    let orgUpdateAsyncMock;

    beforeEach(() => {
        // Mock Orgs collection methods
        orgFindOneAsyncMock = Orgs.findOneAsync;
        orgUpdateAsyncMock = Orgs.updateAsync;

        orgFindOneAsyncMock.mockResolvedValue({
            _id: 'orgId',
            billing: {
                paymentMethodRequired: true,
                autopayEnrollmentRequired: true,
                excludedManualPayTypes: ['cash', 'check'],
            },
            customizations: {
                'billing/requirePaymentMethod/enabled': true,
            },
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('updateOrgPaymentSettings', () => {
        it('should throw an error if orgId is not provided', async () => {
            await expect(
                PaymentSettingsService.updateOrgPaymentSettings(
                    null,
                    true,
                    false,
                    ['cash']
                )
            ).rejects.toThrowError('Organization ID is required');
        });

        it('should throw an error if paymentMethodRequired is not a boolean', async () => {
            await expect(
                PaymentSettingsService.updateOrgPaymentSettings(
                    'orgId',
                    'not-boolean',
                    false,
                    ['cash']
                )
            ).rejects.toThrowError('Payment method required must be a boolean');
        });

        it('should throw an error if autopayEnrollmentRequired is not a boolean', async () => {
            await expect(
                PaymentSettingsService.updateOrgPaymentSettings(
                    'orgId',
                    true,
                    'not-boolean',
                    ['cash']
                )
            ).rejects.toThrowError(
                'Autopay enrollment required must be a boolean'
            );
        });

        it('should throw an error if excludedManualPayTypes is not an array', async () => {
            await expect(
                PaymentSettingsService.updateOrgPaymentSettings(
                    'orgId',
                    true,
                    false,
                    'not-an-array'
                )
            ).rejects.toThrowError(
                'Excluded manual pay types must be an array'
            );
        });

        it('should throw an error if organization is not found', async () => {
            orgFindOneAsyncMock.mockResolvedValue(null);

            await expect(
                PaymentSettingsService.updateOrgPaymentSettings(
                    'invalidOrgId',
                    true,
                    false,
                    ['cash']
                )
            ).rejects.toThrowError('Organization not found');
        });

        it('should update the organization payment settings and enable customization', async () => {
            await PaymentSettingsService.updateOrgPaymentSettings(
                'orgId',
                true, // paymentMethodRequired
                false, // autopayEnrollmentRequired
                ['cash'] // excludedManualPayTypes
            );

            expect(orgFindOneAsyncMock).toHaveBeenCalledWith('orgId');
            expect(orgUpdateAsyncMock).toHaveBeenCalledWith('orgId', {
                $set: {
                    'billing.paymentMethodRequired': true,
                    'billing.autopayEnrollmentRequired': false,
                    'billing.excludedManualPayTypes': ['cash'],
                    'customizations.billing/requirePaymentMethod/enabled': true,
                },
            });
        });

        it('should update the organization payment settings and disable customization', async () => {
            await PaymentSettingsService.updateOrgPaymentSettings(
                'orgId',
                false, // paymentMethodRequired
                true, // autopayEnrollmentRequired
                ['credit_card'] // excludedManualPayTypes
            );

            expect(orgFindOneAsyncMock).toHaveBeenCalledWith('orgId');
            expect(orgUpdateAsyncMock).toHaveBeenCalledWith('orgId', {
                $set: {
                    'billing.paymentMethodRequired': false,
                    'billing.autopayEnrollmentRequired': true,
                    'billing.excludedManualPayTypes': ['credit_card'],
                },
                $unset: {
                    'customizations.billing/requirePaymentMethod/enabled': '',
                },
            });
        });
    });

    describe('fetchOrgPaymentSettings', () => {
        it('should throw an error if orgId is not provided', async () => {
            await expect(
                PaymentSettingsService.fetchOrgPaymentSettings(null)
            ).rejects.toThrowError('Organization ID is required');
        });

        it('should throw an error if organization is not found', async () => {
            orgFindOneAsyncMock.mockResolvedValue(null);

            await expect(
                PaymentSettingsService.fetchOrgPaymentSettings('invalidOrgId')
            ).rejects.toThrowError('Organization not found');
        });

        it('should return the organization payment settings', async () => {
            const result = await PaymentSettingsService.fetchOrgPaymentSettings(
                'orgId'
            );

            expect(orgFindOneAsyncMock).toHaveBeenCalledWith('orgId');
            expect(result).toEqual({
                paymentMethodRequired: true,
                autopayEnrollmentRequired: true,
                excludedManualPayTypes: ['cash', 'check'],
            });
        });

        it('should return default values if billing is not defined', async () => {
            orgFindOneAsyncMock.mockResolvedValue({ _id: 'orgId' });

            const result = await PaymentSettingsService.fetchOrgPaymentSettings(
                'orgId'
            );

            expect(result).toEqual({
                paymentMethodRequired: false,
                autopayEnrollmentRequired: false,
                excludedManualPayTypes: [],
            });
        });
    });

    describe('generateUpdateQuery', () => {
        it('should generate the correct query when paymentMethodRequired is true', () => {
            const paymentMethodRequired = true;
            const autopayEnrollmentRequired = false;
            const excludedManualPayTypes = ['cash', 'check'];

            const result = PaymentSettingsService.generateUpdateQuery(
                paymentMethodRequired,
                autopayEnrollmentRequired,
                excludedManualPayTypes
            );

            const expectedQuery = {
                $set: {
                    'billing.paymentMethodRequired': true,
                    'billing.autopayEnrollmentRequired': false,
                    'billing.excludedManualPayTypes': ['cash', 'check'],
                    [`customizations.${AvailableCustomizations.REQUIRE_PAYMENT_METHOD}`]: true,
                },
            };

            expect(result).toEqual(expectedQuery);
        });

        it('should generate the correct query when paymentMethodRequired is false', () => {
            const paymentMethodRequired = false;
            const autopayEnrollmentRequired = true;
            const excludedManualPayTypes = ['credit_card'];

            const result = PaymentSettingsService.generateUpdateQuery(
                paymentMethodRequired,
                autopayEnrollmentRequired,
                excludedManualPayTypes
            );

            const expectedQuery = {
                $set: {
                    'billing.paymentMethodRequired': false,
                    'billing.autopayEnrollmentRequired': true,
                    'billing.excludedManualPayTypes': ['credit_card'],
                },
                $unset: {
                    [`customizations.${AvailableCustomizations.REQUIRE_PAYMENT_METHOD}`]: '',
                },
            };

            expect(result).toEqual(expectedQuery);
        });

        it('should handle an empty excludedManualPayTypes array', () => {
            const paymentMethodRequired = true;
            const autopayEnrollmentRequired = true;
            const excludedManualPayTypes = [];

            const result = PaymentSettingsService.generateUpdateQuery(
                paymentMethodRequired,
                autopayEnrollmentRequired,
                excludedManualPayTypes
            );

            const expectedQuery = {
                $set: {
                    'billing.paymentMethodRequired': true,
                    'billing.autopayEnrollmentRequired': true,
                    'billing.excludedManualPayTypes': [],
                    [`customizations.${AvailableCustomizations.REQUIRE_PAYMENT_METHOD}`]: true,
                },
            };

            expect(result).toEqual(expectedQuery);
        });

        it('should generate an empty $unset if paymentMethodRequired is true', () => {
            const paymentMethodRequired = true;
            const autopayEnrollmentRequired = false;
            const excludedManualPayTypes = ['cash'];

            const result = PaymentSettingsService.generateUpdateQuery(
                paymentMethodRequired,
                autopayEnrollmentRequired,
                excludedManualPayTypes
            );

            expect(result).not.toHaveProperty('$unset');
        });

        it('should generate an empty $set if customizationUpdate does not include $set', () => {
            const paymentMethodRequired = false;
            const autopayEnrollmentRequired = true;
            const excludedManualPayTypes = ['check'];

            const result = PaymentSettingsService.generateUpdateQuery(
                paymentMethodRequired,
                autopayEnrollmentRequired,
                excludedManualPayTypes
            );

            // Verify $unset contains the correct customization key
            expect(result.$unset).toEqual({
                [`customizations.${AvailableCustomizations.REQUIRE_PAYMENT_METHOD}`]: '',
            });

            // Verify $set is correctly populated
            expect(result.$set).toEqual({
                'billing.paymentMethodRequired': false,
                'billing.autopayEnrollmentRequired': true,
                'billing.excludedManualPayTypes': ['check'],
            });
        });
    });
});
