// Function to upsert a Kinder System
async function upsertKinderSystem({ SA, URL }) {
  if (typeof SA !== 'string' || typeof URL !== 'string') {
    throw new Error('Invalid arguments provided.');
  }

  let config = await KCURL.findOne();
  if (!config) {
    config = await KCURL.create({ kinder_systems: [] });
  }

  const kinderSystems = config.kinder_systems;
  const existingIndex = kinderSystems.findIndex(ks => ks.SA === SA);

  if (existingIndex !== -1) {
    kinderSystems[existingIndex].URL = URL;
  } else {
    kinderSystems.push({ SA, URL });
  }

  await KCURL.updateOne({ _id: config._id }, {
    $set: { kinder_systems: kinderSystems }
  });
}

// Function to get the URL for a given state abbreviation
async function getBaseUrlByState(SA) {
  if (typeof SA !== 'string') {
    throw new Error('Invalid argument provided.');
  }

  const config = await KCURL.findOne();
  if (!config) {
    throw new Error('No configuration found in the database.');
  }

  const kinderSystem = config.kinder_systems.find(ks => ks.SA === SA);
  if (!kinderSystem) {
    return 'URL NOT FOUND';
  }

  return {
    URL: kinderSystem.URL
  };
}



const KCURL = {
  findOne: jest.fn(),
  create: jest.fn(),
  updateOne: jest.fn()
};

// Assuming your function is in a separate file


describe('upsertKinderSystem', () => {
  beforeEach(() => {
      KCURL.findOne.mockClear();
      KCURL.create.mockClear();
      KCURL.updateOne.mockClear();
  });

  it('should create a new configuration if none exists', async () => {
      KCURL.findOne.mockResolvedValue(null);
      KCURL.create.mockResolvedValue({ _id: '1', kinder_systems: [] });

      await upsertKinderSystem({ SA: 'TX', URL: 'http://example.com' });

      expect(KCURL.create).toHaveBeenCalledWith({ kinder_systems: [] });
      expect(KCURL.updateOne).toHaveBeenCalledWith({ _id: '1' }, {
          $set: { kinder_systems: [{ SA: 'TX', URL: 'http://example.com' }] }
      });
  });
 
});




describe('getBaseUrlByState', () => {
  beforeEach(() => {
      KCURL.findOne.mockClear();
  });

  it('should return the URL for a given state abbreviation', async () => {
    const State = 'TX'
    console.log(`${State}`)
    KCURL.findOne.mockResolvedValue({
          kinder_systems: [{ SA: State, URL: 'https://proto.controltec.com' }, { SA: 'MS', URL: 'https://proto1.controltec.com' } ]
      });
       
      const result = await getBaseUrlByState(`${State}`);

      expect(result).toEqual({ URL: 'https://proto.controltec.com' });
      console.log(`${result.URL}`)
  });

  
  it('should return the URL for a given state abbreviation', async () => {
    const State = 'TX'
    const State1 = 'MS'
    console.log(`${State1}`)
    KCURL.findOne.mockResolvedValue({
          kinder_systems: [{ SA: State, URL: 'https://proto.controltec.com' }]
      });
       
      const result = await getBaseUrlByState(`${State1}`);
      expect(result).toEqual('URL NOT FOUND' );
      console.log(`${result}`)
  });
  
});
