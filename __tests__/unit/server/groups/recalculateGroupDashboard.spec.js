import moment from 'moment';

// Mock repositories
jest.mock('../../../../api/v2/repositories/peopleRepository');
jest.mock('../../../../api/v2/repositories/relationshipsRepository');
jest.mock('../../../../api/v2/repositories/orgsRepository');
jest.mock('../../../../api/v2/repositories/groupsRepository');
jest.mock('../../../../api/v2/repositories/momentsRepository');

// Add this with other mocks at the top
jest.mock('../../../../lib/util/log');

import { mockCollection } from "../../../helpers/collectionMock";
import { recalculateGroupDashboard } from "../../../../server/agenda/recalculateGroupDashboard";
import { Log } from '../../../../lib/util/log';

describe('recalculateGroupDashboard', () => {
  let mocks;

  const createMockPerson = (overrides = {}) => ({
    _id: 'testPersonId',
    checkedInOutTime: moment().subtract(1, 'hour').valueOf(),
    checkedIn: true,
    type: 'person',
    firstName: 'Test',
    lastName: 'Person',
    personInitials: () => 'TP',
    getAvatarUrl: () => 'https://example.com/avatar.jpg',
    lastMomentByType: {},
    ...overrides
  });

  const createMockGroup = (rules = []) => ({
    _id: 'testGroupId',
    orgId: 'testOrgId',
    groupSuggestionRules: rules
  });

  const setupMocks = (options = {}) => {
    const {
      group = createMockGroup(),
      org = { availableMomentTypes: () => [] },
      eduPeople = [],
      checkedInPeople = [],
      existingDashboard = null,
      lastMoment = null
    } = options;

    mocks.groups.findOneAsyncMock.mockResolvedValue(group);
    mocks.groupDashboards.findOneAsyncMock.mockResolvedValue(existingDashboard);
    mocks.orgs.findOneAsyncMock.mockResolvedValue(org);
    mocks.moments.findOneAsyncMock.mockResolvedValue(lastMoment);
    
    let callCount = 0;
    mocks.people.cursorMock.mockImplementation(() => {
      callCount++;
      return callCount === 1 ? eduPeople : checkedInPeople;
    });
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mocks = {
      groups: mockCollection('Groups'),
      groupDashboards: mockCollection('GroupDashboards'),
      orgs: mockCollection('Orgs'),
      people: mockCollection('People'),
      moments: mockCollection('Moments')
    };
    
    // Mock Log methods
    Log.info = jest.fn();
    Log.error = jest.fn();
  });

  describe('Group Dashboard Initialization and Creation', () => {
    it.only('should return early if group not found', async () => {
      setupMocks({ group: null });
      await recalculateGroupDashboard('testGroupId');
      expect(mocks.groupDashboards.insertAsyncMock).not.toHaveBeenCalled();
    });

    it('should create new group dashboard if one does not exist', async () => {
      setupMocks();
      await recalculateGroupDashboard('testGroupId');
      expect(mocks.groupDashboards.insertAsyncMock).toHaveBeenCalledWith({
        _id: 'testGroupId',
        orgId: 'testOrgId',
        edu: { arrivals: [], departures: [], here: [] },
        momentSuggestions: []
      });
    });

    it('should update existing dashboard', async () => {
      setupMocks({ 
        existingDashboard: { _id: 'testGroupId' } 
      });
      
      await recalculateGroupDashboard('testGroupId');
      
      expect(mocks.groupDashboards.updateAsyncMock).toHaveBeenCalledWith(
        { _id: 'testGroupId' },
        expect.objectContaining({
          $set: expect.objectContaining({
            orgId: 'testOrgId',
            edu: expect.any(Object),
            momentSuggestions: []
          })
        })
      );
    });
  });

  describe('EDU People Categorization', () => {
    it('should correctly categorize edu people', async () => {
      const eduPeople = [
        { _id: 'p1', familyCheckIn: { dropOffArrival: true } },
        { _id: 'p2', familyCheckIn: { dropOffTimeEstimate: new Date() } },
        { _id: 'p3', familyCheckOut: { pickUpTimeEstimate: new Date() } }
      ];

      setupMocks({ eduPeople });
      await recalculateGroupDashboard('testGroupId');

      expect(mocks.groupDashboards.insertAsyncMock).toHaveBeenCalledWith(
        expect.objectContaining({
          edu: {
            here: [eduPeople[0]],
            arrivals: [eduPeople[1]],
            departures: [eduPeople[2]]
          }
        })
      );
    });

    it('should handle missing familyCheckIn/familyCheckOut data', async () => {
      const eduPeople = [
        { _id: 'p1' },
        { _id: 'p2', familyCheckIn: {} },
        { _id: 'p3', familyCheckOut: {} }
      ];

      setupMocks({ eduPeople });
      await recalculateGroupDashboard('testGroupId');

      expect(mocks.groupDashboards.insertAsyncMock).toHaveBeenCalledWith(
        expect.objectContaining({
          edu: {
            arrivals: [],
            departures: [],
            here: []
          }
        })
      );
    });
  });

  describe('Moment Suggestions', () => {
    it('should handle name to face suggestions', async () => {
      const rules = [{ momentType: 'nameToFace', thresholdHours: '2' }];
      const checkedInPeople = [createMockPerson()];
      
      setupMocks({
        group: createMockGroup(rules),
        org: { 
          availableMomentTypes: () => [
            { momentType: 'nameToFace', prettyName: 'Name to Face' }
          ]
        },
        checkedInPeople
      });

      await recalculateGroupDashboard('testGroupId');

      expect(mocks.groupDashboards.insertAsyncMock).toHaveBeenCalledWith(
        expect.objectContaining({
          momentSuggestions: [
            expect.objectContaining({
              type: 'nameToFace',
              count: 1,
              nextMomentDue: expect.any(Number),
              prettyName: 'Name to Face',
              sort: 1
            })
          ]
        })
      );
    });

    it('should handle multiple suggestion rules', async () => {
      const rules = [
        { momentType: 'nameToFace', thresholdHours: '0' },
        { momentType: 'potty', thresholdHours: '0' }
      ];
      
      const checkedInPeople = [createMockPerson({
        lastMomentByType: {
          potty: {
            createdAt: moment().subtract(2, 'hours').toDate()
          }
        }
      })];

      setupMocks({
        group: createMockGroup(rules),
        org: {
          availableMomentTypes: () => [
            { momentType: 'nameToFace', prettyName: 'Name to Face' },
            { momentType: 'potty', prettyName: 'Potty' }
          ]
        },
        checkedInPeople
      });

      await recalculateGroupDashboard('testGroupId');

      const result = mocks.groupDashboards.insertAsyncMock.mock.calls[0][0];
      expect(result.momentSuggestions).toHaveLength(2);
      expect(result.momentSuggestions).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'nameToFace',
            prettyName: 'Name to Face'
          }),
          expect.objectContaining({
            type: 'potty',
            prettyName: 'Potty',
            data: expect.arrayContaining([
              expect.objectContaining({
                _id: 'testPersonId'
              })
            ])
          })
        ])
      );
    });

    it('should not create suggestions if no rules exist', async () => {
      setupMocks({
        group: createMockGroup([])
      });

      await recalculateGroupDashboard('testGroupId');

      expect(mocks.groupDashboards.insertAsyncMock).toHaveBeenCalledWith(
        expect.objectContaining({
          momentSuggestions: []
        })
      );
    });

    it('should handle moment suggestions with missing lastMomentByType', async () => {
      const rules = [{ momentType: 'customType', thresholdHours: '0' }];
      const checkedInPeople = [createMockPerson({
        lastMomentByType: undefined
      })];

      setupMocks({
        group: createMockGroup(rules),
        org: {
          availableMomentTypes: () => [{
            momentType: 'customType',
            prettyName: 'Custom Type'
          }]
        },
        checkedInPeople
      });

      await recalculateGroupDashboard('testGroupId');

      expect(mocks.groupDashboards.insertAsyncMock).toHaveBeenCalledWith(
        expect.objectContaining({
          momentSuggestions: [
            expect.objectContaining({
              type: 'customType',
              data: [expect.objectContaining({
                _id: 'testPersonId'
              })]
            })
          ]
        })
      );
    });
  });

  describe('Avatar Handling', () => {
    it('should handle non-http avatar URLs', async () => {
      const rules = [{ momentType: 'customType', thresholdHours: '0' }];
      const checkedInPeople = [createMockPerson({
        getAvatarUrl: () => '/local/path/avatar.jpg'
      })];

      setupMocks({
        group: createMockGroup(rules),
        org: {
          availableMomentTypes: () => [{
            momentType: 'customType',
            prettyName: 'Custom Type'
          }]
        },
        checkedInPeople
      });

      await recalculateGroupDashboard('testGroupId');

      const result = mocks.groupDashboards.insertAsyncMock.mock.calls[0][0];
      expect(result.momentSuggestions[0].data[0].avatarPath).toBeNull();
    });
  });

  describe('Moment Date Handling', () => {
    it('should handle lastMoment with different date fields', async () => {
      const rules = [{ momentType: 'potty', thresholdHours: 0 }];
      
      const mockPerson = {
        _id: 'testPersonId',
        checkedInOutTime: moment().valueOf(),
        checkedIn: true,
        type: 'person',
        firstName: 'Test',
        lastName: 'Person',
        personInitials: () => 'TP',
        getAvatarUrl: () => 'https://example.com/avatar.jpg',
        lastMomentByType: {
          potty: {
            createdAt: moment().subtract(3, 'hours').toDate(),
            sortStamp: moment().subtract(3, 'hours').valueOf()
          }
        }
      };

      setupMocks({
        group: createMockGroup(rules),
        org: {
          availableMomentTypes: () => [{
            momentType: 'potty',
            prettyName: 'Potty'
          }]
        },
        checkedInPeople: [mockPerson]
      });

      await recalculateGroupDashboard('testGroupId');

      const result = mocks.groupDashboards.insertAsyncMock.mock.calls[0][0];
     
      
      expect(result.momentSuggestions).toHaveLength(1);
      expect(result.momentSuggestions[0]).toMatchObject({
        type: 'potty',
        data: [{
          _id: 'testPersonId',
          firstName: 'Test',
          lastName: 'Person',
          avatarPath: 'https://example.com/avatar.jpg',
          initials: 'TP'
        }],
        sort: 1,
        prettyName: /Potty/i
      });
    });

    it('should handle nameToFace suggestion with existing moment', async () => {
      const rules = [{ momentType: 'nameToFace', thresholdHours: '2' }];
      const lastMomentTime = moment().subtract(1, 'hour');
      
      setupMocks({
        group: createMockGroup(rules),
        checkedInPeople: [createMockPerson()],
        lastMoment: {
          createdAt: lastMomentTime.toDate()
        }
      });

      await recalculateGroupDashboard('testGroupId');

      const result = mocks.groupDashboards.insertAsyncMock.mock.calls[0][0];
      const suggestion = result.momentSuggestions.find(s => s.type === 'nameToFace');
      expect(suggestion.nextMomentDue).toBe(
        lastMomentTime.add(2, 'hours').valueOf()
      );
    });
   
  });
});