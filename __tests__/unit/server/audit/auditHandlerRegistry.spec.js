import { getAuditHandlerFor } from '../../../../server/audit/auditHandlerRegistry';
import { InvoiceAuditHandler } from '../../../../server/audit/handlers/InvoiceAuditHandler';
import { OrgHolidayAuditHandler } from '../../../../server/audit/handlers/OrgHolidayAuditHandler';
import { BaseAuditHandler } from '../../../../server/audit/handlers/BaseAuditHandler';
import { AUDIT_METHOD_NAMES } from '../../../../lib/audit/auditTrailConstants';

describe('getAuditHandlerFor', () => {
    it('should return InvoiceAuditHandler for a billing method', () => {
        const handler = getAuditHandlerFor(AUDIT_METHOD_NAMES.BILLING.CREDIT_INVOICE);
        expect(handler).toBe(InvoiceAuditHandler);
    });

    it('should return OrgHolidayAuditHandler for a holiday method', () => {
        const handler = getAuditHandlerFor(AUDIT_METHOD_NAMES.ORG_HOLIDAYS.REMOVE);
        expect(handler).toBe(OrgHolidayAuditHandler);
    });

    it('should return BaseAuditHandler for an unregistered method', () => {
        const handler = getAuditHandlerFor('nonExistentMethod');
        expect(handler).toBe(BaseAuditHandler);
    });
});