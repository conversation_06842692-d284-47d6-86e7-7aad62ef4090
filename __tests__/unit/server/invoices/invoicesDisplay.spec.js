import { InvoicesService } from "../../../../server/invoices/invoicesService.js";
import { Invoices } from "../../../../lib/collections/invoices";

jest.mock("../../../../lib/collections/invoices", () => ({
    Invoices: {
        find: jest.fn(),
    },
}));

describe("InvoicesService", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("should return invoices sorted by createdAt in descending order", async () => {
        const mockInvoices = [
            { id: 1, createdAt: new Date("2023-01-02") },
            { id: 2, createdAt: new Date("2023-01-01") }
        ];

        Invoices.find.mockReturnValue({
            fetchAsync: jest.fn().mockResolvedValue(mockInvoices)
        });

        const query = { personId: "123" };

        const result = await InvoicesService.getInvoice(query);

        expect(Invoices.find).toHaveBeenCalledWith(query, { sort: { createdAt: -1 } });
        expect(result).toEqual(mockInvoices);
    });
});
