import { mockCollection } from '../../../helpers/collectionMock';
import expect from 'expect';
import moment from 'moment-timezone';
import { EmailService } from '../../../../server/emails/emailService';
import <PERSON> from 'papaparse';
import { InvoiceMonitoringService } from '../../../../server/invoices/invoiceMonitoringService';
import { beforeEach, describe, jest } from '@jest/globals';
import { BillingFrequencies } from '../../../../lib/constants/billingConstants';
import { cloneDeep } from 'lodash';
import { BillingUtils } from '../../../../lib/util/billingUtils';
import { InvoiceGenerationService } from '../../../../server/invoices/invoiceGenerationService';
import { Moments } from '../../../../lib/collections/moments';
import { Orgs } from '../../../../lib/collections/orgs';
import { People } from '../../../../lib/collections/people';

const columns = [
    'Org id',
    'Org name',
    'Org long name',
    'Child Id',
    'Child First Name',
    'Child Last Name'
];
const timezone = 'America/Chicago';

describe('Invoice Monitoring Service', () => {
    let today = moment.tz('America/Chicago');
    afterEach(async () => {
        jest.clearAllMocks();
        jest.resetAllMocks();
        jest.restoreAllMocks();
    });

    beforeEach(() => {
        today = new moment.tz('America/Chicago');
    });

    const mockOrgData = (hasCustomization = false) => {
        return {
            _id: '123',
            name: 'org name',
            longName: 'org long name',
            getTimezone: jest.fn().mockImplementation(() => 'America/Chicago'),
            billing: {
                suspendInvoicingIndefinitely: false,
                suspendInvoicingUntilDate: undefined,
                scheduling: {
                    generateDay: today.format('dddd').toLowerCase(),
                    generateMonthDay: today.date(),
                    generateWhen: "advance",
                    gracePeriodDays: 1,
                    generateBiWeeklyDate: true
                }
            },
            nextBiWeeklyBillingTimestamp: jest.fn().mockImplementation((date) => {
                // Make it today plus the grace period
                return new moment.tz(date, 'America/Chicago').add(1, 'days').startOf('day').valueOf();
            }),
            hasCustomization: jest.fn().mockImplementation(() => hasCustomization)
        };
    };

    describe('getPeopleQueryForInvoices', () => {
        it('Should return correct MongoDB query for active people needing invoicing', () => {
            const todayStartStamp = new moment.tz('America/Chicago').startOf('day').valueOf();
            const orgId = '123';
            const expectedQuery = {
                orgId: orgId,
                inActive: { $ne: true },
                $and: [
                    { $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
                    { $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
                ]
            };
            expect(InvoiceMonitoringService.getPeopleQueryForInvoices(orgId, todayStartStamp)).toEqual(expectedQuery);
        });
    });

    describe('planInvoiceDayIsToday', () => {
        let orgData = {};

        afterEach(async () => {
            jest.clearAllMocks();
        });

        beforeEach(() => {
            orgData = cloneDeep(mockOrgData());
        });

        it('should return true for daily frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.DAILY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for daily frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.DAILY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for weekly frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.WEEKLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for weekly frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.WEEKLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for scaled weekly frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.SCALED_WEEKLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for scaled weekly frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.SCALED_WEEKLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for weekly scheduled daily frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.WEEKLY_SCHEDULED_DAILY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for weekly scheduled daily frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.WEEKLY_SCHEDULED_DAILY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for bi-weekly frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.BIWEEKLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for weekly scheduled daily frequency', () => {
            // Set no bi-weekly date
            orgData.billing.scheduling.generateBiWeeklyDate = false;
            let result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.BIWEEKLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();

            // Instead, change the next biweekly timestamp
            orgData.billing.scheduling.generateBiWeeklyDate = true;
            orgData.nextBiWeeklyBillingTimestamp = jest.fn().mockImplementation((date) => {
                // Make it not match the expected
                return today.clone().add(orgData.billing.scheduling.gracePeriodDays + 2, 'days').startOf('day').valueOf();
            });
            result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.BIWEEKLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for bi-monthly frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.BIMONTHLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                new moment.tz(today.valueOf(), timezone).date(),
                today.clone().subtract(2, 'months').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for bi-monthly frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.BIMONTHLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                today.clone().subtract(1, 'days').date(),
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for semi-monthly frequency', () => {
            let result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.SEMIMONTHLY,
                new moment.tz('03/31/2024', 'MM/DD/YYYY', timezone).startOf('day').valueOf(), // 31 + 1 = 4/1/24
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
            result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.SEMIMONTHLY,
                new moment.tz('04/14/2024', 'MM/DD/YYYY', timezone).startOf('day').valueOf(), // 14 + 1 = 15
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for semi-monthly frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.SEMIMONTHLY,
                new moment.tz('04/13/2024', 'MM/DD/YYYY', timezone).startOf('day').valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for monthly frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.MONTHLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                new moment.tz(today.valueOf(), timezone).date(),
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for monthly frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.MONTHLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                today.clone().subtract(1, 'days').date(),
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for scaled monthly frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.SCALED_MONTHLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                new moment.tz(today.valueOf(), timezone).date(),
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for scaled monthly frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.SCALED_MONTHLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                today.clone().subtract(1, 'days').date(),
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });

        it('should return true for daily charged monthly frequency', () => {
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.DAILY_CHARGED_MONTHLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                new moment.tz(today.valueOf(), timezone).date(),
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeTruthy();
        });

        it('should return false for daily charged monthly frequency', () => {
            orgData.billing.scheduling.generateDay = today.clone().subtract(1, 'days');
            const result = InvoiceMonitoringService.planInvoiceDayIsToday(
                orgData,
                BillingFrequencies.DAILY_CHARGED_MONTHLY,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                today.clone().subtract(1, 'days').date(),
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays
            );
            expect(result).toBeFalsy();
        });
    });

    describe('planShouldBeInvoicedToday', () => {
        const person = {};
        let plan = {};

        afterEach(async () => {
            jest.clearAllMocks();
            jest.resetAllMocks();
            jest.restoreAllMocks();
        });

        beforeEach(() => {
            plan = {};
        });

        function setupPlanInvoiceDayIsToday(returnValue) {
            InvoiceMonitoringService.planInvoiceDayIsToday = jest.fn().mockReturnValue(returnValue);
        }

        it('Should return true for weekly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.WEEKLY;
            const orgData = mockOrgData();
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for weekly frequency', async () => {
            setupPlanInvoiceDayIsToday(false);
            plan.frequency = BillingFrequencies.WEEKLY;
            const orgData = mockOrgData();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeFalsy();

            setupPlanInvoiceDayIsToday(true);
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.clone().add(2, 'weeks').valueOf()
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for scaled weekly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.SCALED_WEEKLY;
            const orgData = mockOrgData();
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for scaled weekly frequency', async () => {
            setupPlanInvoiceDayIsToday(false);
            plan.frequency = BillingFrequencies.SCALED_WEEKLY;
            const orgData = mockOrgData();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeFalsy();

            setupPlanInvoiceDayIsToday(true);
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.clone().add(2, 'weeks').valueOf()
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for weekly scheduled daily frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            InvoiceMonitoringService.getReservations = jest.fn().mockReturnValue(['04/22/2024']);
            plan.frequency = BillingFrequencies.WEEKLY_SCHEDULED_DAILY;
            const orgData = mockOrgData();
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for weekly scheduled daily frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            InvoiceMonitoringService.getReservations = jest.fn().mockReturnValue([]);
            plan.frequency = BillingFrequencies.WEEKLY_SCHEDULED_DAILY;
            const orgData = mockOrgData();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeFalsy();

            setupPlanInvoiceDayIsToday(false);
            InvoiceMonitoringService.getReservations = jest.fn().mockReturnValue(['04/22/2024']);
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for daily frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            const lastInvoicedDaily = today.clone().subtract(1, 'days').valueOf();
            plan.frequency = BillingFrequencies.DAILY;
            person._id = 'personId';
            person.billing = {
                lastInvoicedDaily: lastInvoicedDaily
            };
            const orgData = mockOrgData();

            const momentsMock = Moments;
            const momentsCursorMock = momentsMock.find;
            momentsCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[{ sortStamp: 1 }])
                })
            });

            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeTruthy();
            expect(momentsCursorMock.mock.calls.length).toBe(1);
            expect(momentsMock.find.mock.calls[0]).toStrictEqual([{
                orgId: orgData._id,
                taggedPeople: person._id,
                momentType: 'checkin',
                sortStamp: { $gte: today.valueOf() } // enrollment date is greatest
            }]);

            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.clone().subtract(3, 'days').valueOf()
            );
            expect(shouldInvoice).toBeTruthy();
            expect(momentsCursorMock.mock.calls.length).toBe(2);
            expect(momentsMock.find.mock.calls[1]).toStrictEqual([{
                orgId: orgData._id,
                taggedPeople: person._id,
                momentType: 'checkin',
                sortStamp: { $gte: lastInvoicedDaily } // lastInvoicedDaily is greatest
            }]);
        });

        it('Should return false for daily frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            const lastInvoicedDaily = today.clone().subtract(1, 'days').valueOf();
            plan.frequency = BillingFrequencies.DAILY;
            person._id = 'personId';
            person.billing = {
                lastInvoicedDaily: lastInvoicedDaily
            };
            const orgData = mockOrgData();

            const momentsMock = Moments;
            const momentsCursorMock = momentsMock.find;
            momentsCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });

            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                today.valueOf()
            );
            expect(shouldInvoice).toBeFalsy();
            expect(momentsCursorMock.mock.calls.length).toBe(1);
        });

        it('Should return true for bi-weekly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.BIWEEKLY;
            jest.spyOn(BillingUtils, 'getPeriodByEffectiveDate').mockReturnValue({
                start: today.clone().subtract(1, 'days').format('MM/DD/YYYY'),
                end: today.clone().add(12, 'days').format('MM/DD/YYYY')
            });
            const enrollmentDate = today.valueOf();
            const orgData = mockOrgData();
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for bi-weekly frequency', async () => {
            setupPlanInvoiceDayIsToday(false);
            plan.frequency = BillingFrequencies.BIWEEKLY;
            jest.spyOn(BillingUtils, 'getPeriodByEffectiveDate').mockReturnValue({
                start: today.clone().subtract(1, 'days').format('MM/DD/YYYY'),
                end: today.clone().add(12, 'days').format('MM/DD/YYYY')
            });
            let enrollmentDate = today.valueOf();
            const orgData = mockOrgData();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();

            setupPlanInvoiceDayIsToday(true);
            enrollmentDate = today.clone().add(14, 'days').valueOf();
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for scaled bi-weekly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.SCALED_BIWEEKLY;
            jest.spyOn(BillingUtils, 'getPeriodByEffectiveDate').mockReturnValue({
                start: today.clone().subtract(1, 'days').format('MM/DD/YYYY'),
                end: today.clone().add(12, 'days').format('MM/DD/YYYY')
            });
            const enrollmentDate = today.valueOf();
            const orgData = mockOrgData();
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for scaled bi-weekly frequency', async () => {
            setupPlanInvoiceDayIsToday(false);
            plan.frequency = BillingFrequencies.SCALED_BIWEEKLY;
            jest.spyOn(BillingUtils, 'getPeriodByEffectiveDate').mockReturnValue({
                start: today.clone().subtract(1, 'days').format('MM/DD/YYYY'),
                end: today.clone().add(12, 'days').format('MM/DD/YYYY')
            });
            let enrollmentDate = today.valueOf();
            const orgData = mockOrgData();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();

            setupPlanInvoiceDayIsToday(true);
            enrollmentDate = today.clone().add(14, 'days').valueOf();
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for bi-monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.BIMONTHLY;
            const enrollmentDate = today.valueOf();
            const orgData = mockOrgData();
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for bi-monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(false);
            plan.frequency = BillingFrequencies.BIMONTHLY;
            let enrollmentDate = today.valueOf()
            const orgData = mockOrgData();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();

            setupPlanInvoiceDayIsToday(true);
            enrollmentDate = today.clone().add(3, 'months').valueOf();
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for semi monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.SEMIMONTHLY;
            const orgData = mockOrgData();
            let todayDate = moment.tz('03/31/2024', 'MM/DD/YYYY', timezone).valueOf();
            let enrollmentDate = moment.tz('04/12/2024', 'MM/DD/YYYY', timezone).valueOf();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                todayDate,
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeTruthy();

            todayDate = moment.tz('04/14/2024', 'MM/DD/YYYY', timezone).valueOf();
            enrollmentDate = moment.tz('04/20/2024', 'MM/DD/YYYY', timezone).valueOf();
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                todayDate,
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for semi monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.SEMIMONTHLY;
            const orgData = mockOrgData();
            let todayDate = moment.tz('03/31/2024', 'MM/DD/YYYY', timezone).valueOf();
            let enrollmentDate = moment.tz('04/20/2024', 'MM/DD/YYYY', timezone).valueOf();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                todayDate,
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();

            todayDate = moment.tz('04/14/2024', 'MM/DD/YYYY', timezone).valueOf();
            enrollmentDate = moment.tz('05/20/2024', 'MM/DD/YYYY', timezone).valueOf();
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                todayDate,
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.MONTHLY;
            const orgData = mockOrgData();
            const enrollmentDate = today.valueOf();
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(false);
            plan.frequency = BillingFrequencies.MONTHLY;
            const orgData = mockOrgData();
            let enrollmentDate = today.valueOf();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();

            enrollmentDate = today.clone().add(2, 'months').valueOf();
            setupPlanInvoiceDayIsToday(true);
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for scaled monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.SCALED_MONTHLY;
            const orgData = mockOrgData();
            const enrollmentDate = today.valueOf();
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for scaled monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(false);
            plan.frequency = BillingFrequencies.SCALED_MONTHLY;
            const orgData = mockOrgData();
            let enrollmentDate = today.valueOf();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();

            enrollmentDate = today.clone().add(2, 'months').valueOf();
            setupPlanInvoiceDayIsToday(true);
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();
        });

        it('Should return true for daily charged monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(true);
            plan.frequency = BillingFrequencies.DAILY_CHARGED_MONTHLY;
            const orgData = mockOrgData();
            const enrollmentDate = today.valueOf();
            jest.spyOn(InvoiceGenerationService, 'calulateMonthlyChargedDailyRate').mockReturnValue({});
            const shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeTruthy();
        });

        it('Should return false for daily charged monthly frequency', async () => {
            setupPlanInvoiceDayIsToday(false);
            plan.frequency = BillingFrequencies.DAILY_CHARGED_MONTHLY;
            const orgData = mockOrgData();
            jest.spyOn(InvoiceGenerationService, 'calulateMonthlyChargedDailyRate').mockReturnValue({});
            const enrollmentDate = today.valueOf();
            let shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();

            jest.clearAllMocks();
            jest.resetAllMocks();
            jest.restoreAllMocks();

            setupPlanInvoiceDayIsToday(true);
            jest.spyOn(InvoiceGenerationService, 'calulateMonthlyChargedDailyRate').mockReturnValue(false);
            shouldInvoice = await InvoiceMonitoringService.planShouldBeInvoicedToday(
                orgData,
                person,
                plan,
                today.valueOf(),
                orgData.billing.scheduling.generateDay,
                28,
                today.clone().subtract(1, 'days').valueOf(),
                orgData.billing.scheduling.gracePeriodDays,
                enrollmentDate
            );
            expect(shouldInvoice).toBeFalsy();
        });
    });

    describe('getValidPlansForPerson', () => {
        let org, person, plans, todayDate, monthlyPlanCutOff, generateDay, gracePeriodDays;

        afterEach(() => {
            jest.clearAllMocks();
            jest.resetAllMocks()
            jest.restoreAllMocks();
        });

        beforeEach(() => {
            org = mockOrgData();
            org.billing.plansAndItems = [
                { _id: 'plan1', suspendUntil: undefined },
                { _id: 'plan2', suspendUntil: new moment.tz(timezone).add(10, 'days').valueOf() }, // suspended
                { _id: 'plan3' }
            ];

            person = {
                _id: 'person1',
                billing: {
                    lastInvoiced: new moment.tz(timezone).subtract(1, 'month').valueOf(),
                    enrolledPlans: [
                        {
                            _id: 'plan1',
                            expirationDate: new moment.tz(timezone).add(1, 'year').valueOf(),
                            enrollmentDate: new moment.tz(timezone).subtract(1, 'year').valueOf()
                        },
                        {
                            _id: 'plan2',
                            expirationDate: new moment.tz(timezone).subtract(1, 'day').valueOf(),
                            enrollmentDate: new moment.tz(timezone).subtract(1, 'year').valueOf()
                        }, // expired
                        {
                            _id: 'plan3',
                            expirationDate: new moment.tz(timezone).add(1, 'year').valueOf(),
                            enrollmentDate: new moment.tz(timezone).add(1, 'day').valueOf()
                        } // not started
                    ]
                }
            };

            todayDate = today.startOf('day').valueOf();
            monthlyPlanCutOff = 28;
            generateDay = org.billing.scheduling.generateDay;
            gracePeriodDays = org.billing.scheduling.gracePeriodDays;

            // Reset mocks before each test
            InvoiceMonitoringService.planShouldBeInvoicedToday = jest.fn();
        });

        it('Should include plans that are not suspended and not expired', async () => {
            InvoiceMonitoringService.planShouldBeInvoicedToday.mockReturnValue(true);
            const validPlans = await InvoiceMonitoringService.getValidPlansForPerson(person, org, todayDate, monthlyPlanCutOff, generateDay, gracePeriodDays);

            expect(validPlans).toContainEqual(expect.objectContaining({ _id: 'plan1' }));
            expect(validPlans.length).toBe(1);
        });

        it('Should exclude plans that are currently suspended', async () => {
            InvoiceMonitoringService.planShouldBeInvoicedToday.mockReturnValue(true);
            const validPlans = await InvoiceMonitoringService.getValidPlansForPerson(person, org, todayDate, monthlyPlanCutOff, generateDay, gracePeriodDays);

            expect(validPlans).not.toContainEqual(expect.objectContaining({ _id: 'plan2' }));
        });

        it('Should exclude plans that have expired', async () => {
            InvoiceMonitoringService.planShouldBeInvoicedToday.mockReturnValue(true);
            const validPlans = await InvoiceMonitoringService.getValidPlansForPerson(person, org, todayDate, monthlyPlanCutOff, generateDay, gracePeriodDays);

            expect(validPlans).not.toContainEqual(expect.objectContaining({ _id: 'plan2' })); // Expired
        });

        it('Should exclude plans that have not started', async () => {
            InvoiceMonitoringService.planShouldBeInvoicedToday.mockReturnValue(true);
            const validPlans = await InvoiceMonitoringService.getValidPlansForPerson(person, org, todayDate, monthlyPlanCutOff, generateDay, gracePeriodDays);

            expect(validPlans).not.toContainEqual(expect.objectContaining({ _id: 'plan3' })); // Not started
        });

        it('Should exclude plans that have not started even if the org has the customization but doesn\'t bill in advance', async () => {
            InvoiceMonitoringService.planShouldBeInvoicedToday.mockReturnValue(true);
            org.hasCustomization = jest.fn().mockImplementation(() => true);
            org.billing.scheduling.generateWhen = 'arrears';
            const validPlans = await InvoiceMonitoringService.getValidPlansForPerson(person, org, todayDate, monthlyPlanCutOff, generateDay, gracePeriodDays);

            expect(validPlans).not.toContainEqual(expect.objectContaining({ _id: 'plan3' })); // Not started
        });

        it('Should include plans that have not started if the org has the customization and bills in advance', async () => {
            InvoiceMonitoringService.planShouldBeInvoicedToday.mockReturnValue(true);
            org.hasCustomization = jest.fn().mockImplementation(() => true);
            const validPlans = await InvoiceMonitoringService.getValidPlansForPerson(person, org, todayDate, monthlyPlanCutOff, generateDay, gracePeriodDays);

            expect(validPlans).toContainEqual(expect.objectContaining({ _id: 'plan3' })); // Not started
        });
    });

    describe('runAutoPayMonitoring', () => {
        let org, person, plans, todayDate, monthlyPlanCutOff, generateDay, gracePeriodDays;

        afterEach(() => {
            jest.clearAllMocks();
            jest.resetAllMocks()
            jest.restoreAllMocks();
        });

        beforeEach(() => {
            org = mockOrgData();
            org.billing.plansAndItems = [
                { _id: 'plan1', suspendUntil: undefined },
                { _id: 'plan2', suspendUntil: new moment.tz(timezone).add(10, 'days').valueOf() }, // suspended
                { _id: 'plan3' }
            ];

            person = {
                _id: '123456',
                firstName: 'fn',
                lastName: 'ln',
                billing: {
                    lastInvoiced: new moment.tz(timezone).subtract(1, 'month').valueOf(),
                    enrolledPlans: [
                        {
                            _id: 'plan1',
                            expirationDate: new moment.tz(timezone).add(1, 'year').valueOf(),
                            enrollmentDate: new moment.tz(timezone).subtract(1, 'year').valueOf()
                        },
                        {
                            _id: 'plan2',
                            expirationDate: new moment.tz(timezone).subtract(1, 'day').valueOf(),
                            enrollmentDate: new moment.tz(timezone).subtract(1, 'year').valueOf()
                        }, // expired
                        {
                            _id: 'plan3',
                            expirationDate: new moment.tz(timezone).add(1, 'year').valueOf(),
                            enrollmentDate: new moment.tz(timezone).add(1, 'day').valueOf()
                        } // not started
                    ]
                }
            };

            todayDate = today.startOf('day').valueOf();
            monthlyPlanCutOff = 28;
            generateDay = org.billing.scheduling.generateDay;
            gracePeriodDays = org.billing.scheduling.gracePeriodDays;

            // Reset mocks before each test
            InvoiceMonitoringService.getValidPlansForPerson = jest.fn();
        });

        it('runAutoPayMonitoring skips orgs that have invoicing suspended by a certain date', async () => {
            const orgsMock = Orgs;
            const orgsFindMock = orgsMock.find;
            const orgMockData = mockOrgData();
            orgMockData.billing.suspendInvoicingUntilDate = (new moment.tz('America/Chicago')).add(1, 'day').startOf('day').valueOf();
            orgsFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[orgMockData])
                })
            });

            const peopleMock = People;
            const peopleCursorMock = peopleMock.find;

            const emailMock = jest.spyOn(EmailService, 'sendSystemAdminEmail')
                .mockImplementation(() => {
                    return undefined;
                });

            await InvoiceMonitoringService.monitorRunInvoices({ timezone: 'Central Time' });
            expect(orgsFindMock.mock.calls.length).toBe(1);
            expect(orgsFindMock.mock.calls[0]).toStrictEqual([
                {
                    'billing.enabled': true,
                    'billing.scheduling': { '$exists': true },
                    "billing.suspendInvoicingIndefinitely": { $ne: true },
                    timezone: 'America/Chicago'
                },
                {
                    disableOpLog: true
                }
            ]);
            expect(peopleCursorMock.mock.calls.length).toBe(0);
            expect(emailMock.mock.calls.length).toBe(1);
        });
        it('runAutoPayMonitoring works', async () => {
            const orgsMock = Orgs;
            const orgsFindMock = orgsMock.find;
            orgsFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[mockOrgData()])
                })
            });

            const peopleMock = People;
            const peopleFindMock = peopleMock.find;
            peopleFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[person])
                })
            });

            InvoiceMonitoringService.getValidPlansForPerson.mockReturnValue([{ _id: 'plan1' }]);

            const emailMock = jest.spyOn(EmailService, 'sendSystemAdminEmail')
                .mockImplementation(() => {
                    return undefined;
                });

            const data = [
                '123',
                'org name',
                'org long name',
                '123456',
                'fn',
                'ln'
            ];

            const expectedDateString = moment().format('MM/DD/YYYY');
            const content = Papa.unparse({ fields: columns, data });
            const attachments = [{
                filename: 'failed_invoicing_' + expectedDateString.replace(/\//g, '-') + '_Central Time.csv',
                content
            }];

            const todayStartStamp = new moment.tz('America/Chicago').startOf('day').valueOf();
            await InvoiceMonitoringService.monitorRunInvoices({ timezone: 'Central Time' });
            expect(orgsFindMock.mock.calls.length).toBe(1);
            expect(orgsFindMock.mock.calls[0]).toStrictEqual([
                {
                    'billing.enabled': true,
                    'billing.scheduling': { '$exists': true },
                    "billing.suspendInvoicingIndefinitely": { $ne: true },
                    timezone: 'America/Chicago'
                },
                {
                    disableOpLog: true
                }
            ]);
            expect(peopleFindMock.mock.calls.length).toBe(1);
            expect(peopleFindMock.mock.calls[0]).toStrictEqual([
                {
                    orgId: '123',
                    inActive: { $ne: true },
                    $and: [
                        { $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
                        { $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
                    ]
                }
            ]);
            expect(emailMock.mock.calls.length).toBe(1);
            expect(emailMock.mock.calls[0][0]).toBe(undefined);
            expect(emailMock.mock.calls[0][1]).toBe('systemNotificationEmail');
            expect(emailMock.mock.calls[0][2]).toBe('email_templates/system_notification_email.html');
            expect(emailMock.mock.calls[0][3]).toBe('<<EMAIL>>');
            expect(emailMock.mock.calls[0][4]).toBe("<EMAIL>");
            expect(emailMock.mock.calls[0][5]).toBe('Auto-invoicing failed on date ' + expectedDateString);
            expect(emailMock.mock.calls[0][6]).toStrictEqual({ bodyMessage: 'Please invoice the following children, as their auto-invoicing failed.' });
            expect(emailMock.mock.calls[0][7]).toStrictEqual({ attachments: attachments });
        });
        it('runAutoPayMonitoring works when invoicing suspension is over', async () => {
            const orgsMock = Orgs;
            const orgsFindMock = orgsMock.find;
            const orgMockData = mockOrgData();
            orgMockData.billing.suspendInvoicingUntilDate = (new moment.tz('America/Chicago')).startOf('day').valueOf();
            orgsFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[orgMockData])
                })
            });

            const peopleMock = People;
            const peopleFindMock = peopleMock.find;
            peopleFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[person])
                })
            });

            InvoiceMonitoringService.getValidPlansForPerson.mockReturnValue([{ _id: 'plan1' }]);

            const emailMock = jest.spyOn(EmailService, 'sendSystemAdminEmail')
                .mockImplementation(() => {
                    return undefined;
                });

            const data = [
                '123',
                'org name',
                'org long name',
                '123456',
                'fn',
                'ln'
            ];

            const expectedDateString = moment().format('MM/DD/YYYY');
            const content = Papa.unparse({ fields: columns, data });
            const attachments = [{
                filename: 'failed_invoicing_' + expectedDateString.replace(/\//g, '-') + '_Central Time.csv',
                content
            }];

            const todayStartStamp = new moment.tz('America/Chicago').startOf('day').valueOf();
            await InvoiceMonitoringService.monitorRunInvoices({ timezone: 'Central Time' });
            expect(orgsFindMock.mock.calls.length).toBe(1);
            expect(orgsFindMock.mock.calls[0]).toStrictEqual([
                {
                    'billing.enabled': true,
                    'billing.scheduling': { '$exists': true },
                    "billing.suspendInvoicingIndefinitely": { $ne: true },
                    timezone: 'America/Chicago'
                },
                {
                    disableOpLog: true
                }
            ]);
            expect(peopleFindMock.mock.calls.length).toBe(1);
            expect(peopleFindMock.mock.calls[0]).toStrictEqual([
                {
                    orgId: '123',
                    inActive: { $ne: true },
                    $and: [
                        { $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
                        { $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
                    ]
                }
            ]);
            expect(emailMock.mock.calls.length).toBe(1);
            expect(emailMock.mock.calls[0][0]).toBe(undefined);
            expect(emailMock.mock.calls[0][1]).toBe('systemNotificationEmail');
            expect(emailMock.mock.calls[0][2]).toBe('email_templates/system_notification_email.html');
            expect(emailMock.mock.calls[0][3]).toBe('<<EMAIL>>');
            expect(emailMock.mock.calls[0][4]).toBe("<EMAIL>");
            expect(emailMock.mock.calls[0][5]).toBe('Auto-invoicing failed on date ' + expectedDateString);
            expect(emailMock.mock.calls[0][6]).toStrictEqual({ bodyMessage: 'Please invoice the following children, as their auto-invoicing failed.' });
            expect(emailMock.mock.calls[0][7]).toStrictEqual({ attachments: attachments });
        });
        it('runAutoPayMonitoring different run options', async () => {
            const orgsMock = Orgs;
            const orgsFindMock = orgsMock.find;
            const org = mockOrgData();
            org.getTimezone = jest.fn().mockImplementation(() => 'America/Los_Angeles');
            orgsFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[org])
                })
            });

            const peopleMock = People;
            const peopleFindMock = peopleMock.find;
            peopleFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[person])
                })
            });

            InvoiceMonitoringService.getValidPlansForPerson.mockReturnValue([{ _id: 'plan1' }]);

            const emailMock = jest.spyOn(EmailService, 'sendSystemAdminEmail')
                .mockImplementation(() => {
                    return undefined;
                });

            const data = [
                '123',
                'org name',
                'org long name',
                '123456',
                'fn',
                'ln'
            ];

            const expectedDateString = moment().format('MM/DD/YYYY');
            const content = Papa.unparse({ fields: columns, data });
            const attachments = [{
                filename: 'failed_invoicing_' + expectedDateString.replace(/\//g, '-') + '_others.csv',
                content
            }];

            const todayStartStamp = new moment.tz('America/Los_Angeles').startOf('day').valueOf();
            await InvoiceMonitoringService.monitorRunInvoices({ timezone: 'REMAINING' });
            expect(orgsFindMock.mock.calls.length).toBe(1);
            expect(orgsFindMock.mock.calls[0]).toStrictEqual([
                {
                    'billing.enabled': true,
                    'billing.scheduling': { '$exists': true },
                    "billing.suspendInvoicingIndefinitely": { $ne: true },
                    timezone: {
                        $nin: [
                            "America/New_York",
                            "America/Chicago",
                            "America/Denver"
                        ]
                    }
                },
                {
                    disableOpLog: true
                }
            ]);
            expect(peopleFindMock.mock.calls.length).toBe(1);
            expect(peopleFindMock.mock.calls[0]).toStrictEqual([
                {
                    orgId: '123',
                    inActive: { $ne: true },
                    $and: [
                        { $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
                        { $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
                    ]
                }
            ]);
            expect(emailMock.mock.calls.length).toBe(1);
            expect(emailMock.mock.calls[0][0]).toBe(undefined);
            expect(emailMock.mock.calls[0][1]).toBe('systemNotificationEmail');
            expect(emailMock.mock.calls[0][2]).toBe('email_templates/system_notification_email.html');
            expect(emailMock.mock.calls[0][3]).toBe('<<EMAIL>>');
            expect(emailMock.mock.calls[0][4]).toBe("<EMAIL>");
            expect(emailMock.mock.calls[0][5]).toBe('Auto-invoicing failed on date ' + expectedDateString);
            expect(emailMock.mock.calls[0][6]).toStrictEqual({ bodyMessage: 'Please invoice the following children, as their auto-invoicing failed.' });
            expect(emailMock.mock.calls[0][7]).toStrictEqual({ attachments: attachments });
        });
        it('runAutoPayMonitoring no valid plans for person', async () => {
            const orgsMock = Orgs;
            const orgsFindMock = orgsMock.find;
            orgsFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[mockOrgData()])
                })
            });

            const peopleMock = People;
            const peopleFindMock = peopleMock.find;
            peopleFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[person])
                })
            });

            InvoiceMonitoringService.getValidPlansForPerson.mockReturnValue([]);

            const emailMock = jest.spyOn(EmailService, 'sendSystemAdminEmail')
                .mockImplementation(() => {
                    return undefined;
                });

            const todayStartStamp = new moment.tz('America/Chicago').startOf('day').valueOf();
            await InvoiceMonitoringService.monitorRunInvoices({ timezone: 'Central Time' });
            expect(orgsFindMock.mock.calls.length).toBe(1);
            expect(orgsFindMock.mock.calls[0]).toStrictEqual([
                {
                    'billing.enabled': true,
                    'billing.scheduling': { '$exists': true },
                    "billing.suspendInvoicingIndefinitely": { $ne: true },
                    timezone: 'America/Chicago'
                },
                {
                    disableOpLog: true
                }
            ]);
            expect(peopleFindMock.mock.calls.length).toBe(1);
            expect(peopleFindMock.mock.calls[0]).toStrictEqual([
                {
                    orgId: '123',
                    inActive: { $ne: true },
                    $and: [
                        { $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
                        { $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
                    ]
                }
            ]);
            expect(emailMock.mock.calls.length).toBe(1);
        });
        it('runAutoPayMonitoring no person with valid last invoice date', async () => {
            const orgsMock = Orgs;
            const orgsFindMock = orgsMock.find;
            orgsFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[mockOrgData()])
                })
            });

            const peopleMock = People;
            const peopleFindMock = peopleMock.find;
            peopleFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[{
                        _id: '123456',
                        firstName: 'fn',
                        lastName: 'ln',
                        billing: {
                            lastInvoiced: moment().valueOf()
                        }
                    }])
                })
            });

            const emailMock = jest.spyOn(EmailService, 'sendSystemAdminEmail')
                .mockImplementation(() => {
                    return undefined;
                });

            const todayStartStamp = new moment.tz('America/Chicago').startOf('day').valueOf();
            await InvoiceMonitoringService.monitorRunInvoices({ timezone: 'Central Time' });
            expect(orgsFindMock.mock.calls.length).toBe(1);
            expect(orgsFindMock.mock.calls[0]).toStrictEqual([
                {
                    'billing.enabled': true,
                    'billing.scheduling': { '$exists': true },
                    "billing.suspendInvoicingIndefinitely": { $ne: true },
                    timezone: 'America/Chicago'
                },
                {
                    disableOpLog: true
                }
            ]);
            expect(peopleFindMock.mock.calls.length).toBe(1);
            expect(peopleFindMock.mock.calls[0]).toStrictEqual([
                {
                    orgId: '123',
                    inActive: { $ne: true },
                    $and: [
                        { $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
                        { $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
                    ]
                }
            ]);
            // Expecting at least one email due to success notification
            expect(emailMock.mock.calls.length).toBe(1);
        });
        it('runAutoPayMonitoring no email because no people', async () => {
            const orgsMock = Orgs;
            const orgsFindMock = orgsMock.find;
            orgsFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[mockOrgData()])
                })
            });

            const peopleMock = People;
            const peopleFindMock = peopleMock.find;
            peopleFindMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[])
                })
            });

            const emailMock = jest.spyOn(EmailService, 'sendSystemAdminEmail')
                .mockImplementation(() => {
                    return undefined;
                });

            const todayStartStamp = new moment.tz('America/Chicago').startOf('day').valueOf();
            await InvoiceMonitoringService.monitorRunInvoices({ timezone: 'Central Time' });
            expect(orgsFindMock.mock.calls.length).toBe(1);
            expect(orgsFindMock.mock.calls[0]).toStrictEqual([
                {
                    'billing.enabled': true,
                    'billing.scheduling': { '$exists': true },
                    "billing.suspendInvoicingIndefinitely": { $ne: true },
                    timezone: 'America/Chicago'
                },
                {
                    disableOpLog: true
                }
            ]);
            expect(peopleFindMock.mock.calls.length).toBe(1);
            expect(peopleFindMock.mock.calls[0]).toStrictEqual([
                {
                    orgId: '123',
                    inActive: { $ne: true },
                    $and: [
                        { $or: [{ "billing.enrolledPlans": { $gt: [] } }, { "billing.pendingCharges": { $gt: [] } }] },
                        { $or: [{ "billing.suspendUntil": { "$exists": false } }, { "billing.suspendUntil": { $lt: todayStartStamp } }] }
                    ]
                }
            ]);
            // Expecting at least one email due to success notification
            expect(emailMock.mock.calls.length).toBe(1);
        });
        it('monitorRunInvoices sends success email when there are no invoicing missed children', async () => {
            const orgsMock = Orgs;
            const orgsCursorMock = orgsMock.find;
            orgsCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[mockOrgData()])
                })
            });

            const peopleMock = People;
            const peopleCursorMock = peopleMock.find;
            peopleCursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[{
                        _id: '123456',
                        firstName: 'fn',
                        lastName: 'ln',
                        billing: {
                            lastInvoiced: moment().valueOf()
                        }
                    }])
                })
            });

            const emailMock = jest.spyOn(EmailService, 'sendSystemAdminEmail')
                .mockImplementation(() => {
                    return undefined;
                });

            await InvoiceMonitoringService.monitorRunInvoices({ timezone: 'Central Time' });
            expect(orgsCursorMock.mock.calls.length).toBe(1);
            expect(peopleCursorMock.mock.calls.length).toBe(1);
            expect(emailMock.mock.calls.length).toBe(1);
            expect(emailMock.mock.calls[0][0]).toBe(undefined);
            expect(emailMock.mock.calls[0][1]).toBe('systemNotificationEmail');
            expect(emailMock.mock.calls[0][2]).toBe('email_templates/system_notification_email.html');
            expect(emailMock.mock.calls[0][3]).toBe('<<EMAIL>>');
            expect(emailMock.mock.calls[0][4]).toBe("<EMAIL>");
            expect(emailMock.mock.calls[0][5]).toBe('Auto-invoicing successful on date ' + moment().format('MM/DD/YYYY'));
            expect(emailMock.mock.calls[0][6]).toStrictEqual({ bodyMessage: 'Auto-invoicing ran successfully for Central Time. No further action is needed at this time.' });
            expect(emailMock.mock.calls[0][7]).toStrictEqual({ attachments: [] });
        });
    });
});