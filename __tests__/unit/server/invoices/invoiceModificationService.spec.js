import { jest, describe, it, expect } from '@jest/globals';
import { InvoiceModificationService } from '../../../../server/invoices/invoiceModificationService';
import { AwsBillingService } from '../../../../server/awsBillingService';
import { InvoiceUpdateService } from '../../../../lib/invoiceUpdateService';
import { mockCollection } from '../../../helpers/collectionMock';
import { mockRandom } from '../../../helpers/randomMock';
import { Invoices } from '../../../../lib/collections/invoices';
import { People } from '../../../../lib/collections/people';

jest.mock('../../../../server/awsBillingService', () => ({
    AwsBillingService: {
        createSingleInvoiceWithReturn: jest.fn(),
    },
}));

jest.mock('../../../../lib/invoiceUpdateService', () => ({
    InvoiceUpdateService: {
        updateByQueryWithJournalEntry: jest.fn(),
    },
}));

jest.mock('../../../../lib/util/billingUtils', () => ({
    BillingUtils: {
        roundToTwo: jest.fn((value) => parseFloat(value.toFixed(2))),
    },
}));

describe('InvoiceModificationService.reallocatePayerBalance', () => {
    let invoiceFindOneAsyncMock;
    let peopleUpdateAsyncMock;
    const currentUser = {
        _id: 'user123',
        personId: 'person123',
        orgId: 'org123',
    };

    const org = {
        _id: 'org123',
    };

    const mockInvoice = {
        _id: 'invoice123',
        orgId: 'org123',
        personId: 'person456',
        openAmount: 100.0,
        lineItems: [
            {
                _id: 'lineItem123',
                appliedDiscounts: [
                    { source: 'family', amount: 50.0 },
                ],
            },
            {
                _id: 'lineItem456',
                appliedDiscounts: [
                    { source: 'payer', amount: 50.0 },
                ],
            }
        ],
        openAmountForPayer: jest.fn().mockReturnValue(50.0),
    };

    beforeEach(() => {
        jest.clearAllMocks();

        invoiceFindOneAsyncMock = Invoices.findOneAsync;
        peopleUpdateAsyncMock = People.updateAsync;
    });

    it('should throw an error if amount is not a number', async () => {
        const options = { invoiceId: 'invoice123', amount: 'not-a-number', lineItemId: 'lineItem123' };

        await expect(
            InvoiceModificationService.reallocatePayerBalance(options, currentUser, org)
        ).rejects.toThrowError('You must specify a valid amount for this reallocation.');
    });

    it('should throw an error if invoice is not found', async () => {
        invoiceFindOneAsyncMock.mockReturnValue(null);
        const options = { invoiceId: 'invoice123', amount: 50, lineItemId: 'lineItem123' };

        await expect(
            InvoiceModificationService.reallocatePayerBalance(options, currentUser, org)
        ).rejects.toThrowError('This invoice is not found or is not editable.');
    });

    it('should throw an error if reallocate_from and reallocate_to are the same', async () => {
        invoiceFindOneAsyncMock.mockResolvedValueOnce(mockInvoice);
        const options = {
            invoiceId: 'invoice123',
            amount: 50,
            lineItemId: 'lineItem123',
            reallocate_from: 'family',
            reallocate_to: 'family',
        };

        await expect(
            InvoiceModificationService.reallocatePayerBalance(options, currentUser, org)
        ).rejects.toThrowError('You cannot reallocate from one entity to the same entity.');
    });

    it('should throw an error if attempting to reallocate more than original payer amount', async () => {
        invoiceFindOneAsyncMock.mockResolvedValueOnce(mockInvoice);
        const options = {
            invoiceId: 'invoice123',
            amount: 110, // Exceeds 100 for the invoice
            lineItemId: 'lineItem123',
            reallocate_from: 'family',
            reallocate_to: 'collections',
        };

        await expect(
            InvoiceModificationService.reallocatePayerBalance(options, currentUser, org)
        ).rejects.toThrowError('Cannot reallocate more than open family balance.');
    });

    it('should handle reallocation from family to collections correctly', async () => {
        // Mock the invoice with sufficient open balance
        const mockInvoiceWithBalance = {
            _id: 'invoice123',
            orgId: currentUser.orgId,
            personId: 'person123',
            lineItems: [
                {
                    _id: 'lineItem123',
                    appliedDiscounts: {},
                },
            ],
            openAmount: 50, // Open family balance
        };

        // Mocking dependencies
        invoiceFindOneAsyncMock.mockResolvedValueOnce(mockInvoiceWithBalance);
        InvoiceUpdateService.updateByQueryWithJournalEntry.mockResolvedValue();

        const options = {
            invoiceId: 'invoice123',
            amount: 40, // Reallocating $40
            lineItemId: 'lineItem123',
            reallocate_from: 'family', // From family balance
            reallocate_to: 'collections', // To collections write-off
        };

        // Call the method
        const result = await InvoiceModificationService.reallocatePayerBalance(options, currentUser, org);

        // Verify that InvoiceUpdateService.updateByQueryWithJournalEntry was called correctly
        expect(InvoiceUpdateService.updateByQueryWithJournalEntry).toHaveBeenCalledWith(
            { _id: 'invoice123' },
            expect.objectContaining({
                $push: {
                    allocationEntries: expect.objectContaining({
                        source: 'family',
                        destination: 'collections',
                        amount: 40,
                        createdBy: currentUser.personId,
                    }),
                    credits: expect.objectContaining({
                        type: 'credit',
                        amount: 40,
                        createdAt: expect.any(Number),
                        creditedBy: currentUser.personId,
                        creditReason: 'collections_write_off',
                    }),
                },
                $inc: {
                    openAmount: -40, // Family balance decreased
                },
            }),
            expect.objectContaining({
                userId: currentUser._id,
                personId: currentUser.personId,
                orgId: currentUser.orgId,
                reason: 'Reallocated reimbursement of $40 from family to collections',
                reasonLocation: expect.any(String),
            })
        );

        // Verify the return message
        expect(result).toContain('Invoice updated successfully.');
    });

    it('should send a new invoice if the reallocation results in a new invoice', async () => {
        const randomMock = mockRandom();
        invoiceFindOneAsyncMock.mockResolvedValueOnce(mockInvoice);
        AwsBillingService.createSingleInvoiceWithReturn.mockResolvedValue('newInvoice123');

        const options = {
            invoiceId: 'invoice123',
            amount: 30,
            lineItemId: 'lineItem456',
            reallocate_from: '0',
            reallocate_to: 'family',
        };

        const result = await InvoiceModificationService.reallocatePayerBalance(options, currentUser, org);

        expect(AwsBillingService.createSingleInvoiceWithReturn).toHaveBeenCalledWith(
            expect.objectContaining({
                orgId: org._id,
                personId: mockInvoice.personId,
                allowItemsOnly: true,
                forceInactiveInvoice: true,
            })
        );

        expect(result).toContain('Invoice updated successfully.');
    });

    it('should handle reallocation to a specific allocation (e.g., collections_write_off)', async () => {
        invoiceFindOneAsyncMock.mockReturnValue(mockInvoice);
        const options = {
            invoiceId: 'invoice123',
            amount: 30,
            lineItemId: 'lineItem123',
            reallocate_from: 'family',
            reallocate_to: 'collections',
        };

        const result = await InvoiceModificationService.reallocatePayerBalance(options, currentUser, org);

        expect(InvoiceUpdateService.updateByQueryWithJournalEntry).toHaveBeenCalledWith(
            { _id: mockInvoice._id },
            expect.objectContaining({
                $inc: expect.any(Object),
                $push: expect.objectContaining({
                    allocationEntries: expect.any(Object),
                }),
            }),
            expect.objectContaining({
                reason: expect.stringContaining('Reallocated reimbursement'),
            })
        );

        expect(result).toContain('Invoice updated successfully.');
    });
});
describe('InvoiceModificationService', () => {

    describe('calculateAmountAdjustment', () => {
        it('should calculate the correct amount adjustment', () => {
            expect(InvoiceModificationService.calculateAmountAdjustment(100, 80)).toBe(-20);
            expect(InvoiceModificationService.calculateAmountAdjustment(80, 100)).toBe(20);
            expect(InvoiceModificationService.calculateAmountAdjustment(100, 100)).toBe(0);
        });
    });

    describe('calculateTotalModifiedAmount', () => {
        it('should return amountAdjustment if there is no modification history', () => {
            expect(InvoiceModificationService.calculateTotalModifiedAmount([], -20)).toBe(-20);
            expect(InvoiceModificationService.calculateTotalModifiedAmount([], 10)).toBe(10);
        });

        it('should correctly sum previous modifications with the new adjustment', () => {
            const history = [
                { amountModified: -20 },
                { amountModified: 10 }
            ];
            expect(InvoiceModificationService.calculateTotalModifiedAmount(history, 5)).toBe(-5);
            expect(InvoiceModificationService.calculateTotalModifiedAmount(history, -10)).toBe(-20);
        });
    });

    describe('buildModificationRecord', () => {
        it('should create a properly structured modification record', () => {
            const record = InvoiceModificationService.buildModificationRecord(80, -20, { personId: 'user123' }, 1712345678902);
            expect(record).toEqual({
                newAmount: 80,
                amountModified: -20,
                modifiedBy: 'user123',
                modifiedAt: 1712345678902
            });
        });
    });

    describe('buildBaseUpdateSet', () => {
        it('should return an object with correctly mapped keys and values', () => {
            const result = InvoiceModificationService.buildBaseUpdateSet(
                'credits.0',
                80,
                'Updated credit',
                { personId: 'user123' },
                1712345678902,
                -20
            );

            expect(result).toEqual({
                'credits.0.amount': 80,
                'credits.0.creditNote': 'Updated credit',
                'credits.0.updatedBy': 'user123',
                'credits.0.updatedAt': 1712345678902,
                'credits.0.modifiedAmount': -20
            });
        });
    });

    describe('shouldStoreOriginalAmount', () => {
        it('should return true if originalAmount is not set and there is no modification history', () => {
            const creditLine = { amount: 100 };
            const modificationHistory = [];
            expect(InvoiceModificationService.shouldStoreOriginalAmount(creditLine, modificationHistory)).toBe(true);
        });

        it('should return false if originalAmount is already set', () => {
            const creditLine = { amount: 100, originalAmount: 100 };
            const modificationHistory = [];
            expect(InvoiceModificationService.shouldStoreOriginalAmount(creditLine, modificationHistory)).toBe(false);
        });

        it('should return false if there is existing modification history', () => {
            const creditLine = { amount: 100 };
            const modificationHistory = [{ newAmount: 80, amountModified: -20 }];
            expect(InvoiceModificationService.shouldStoreOriginalAmount(creditLine, modificationHistory)).toBe(false);
        });
    });

});
