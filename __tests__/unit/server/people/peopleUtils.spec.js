import { People } from "../../../../lib/collections/people";
import { PeopleUtils } from "../../../../server/people/peopleUtils";
import { Meteor } from 'meteor/meteor';
import expect from 'expect';

describe('getPeopleByQueryAndOptions', () => {
    const mockUser = { orgId: 'org123' };
    const mockPeople = [
        { _id: 'person1', firstName: 'Alice', orgId: 'org123' },
        { _id: 'person2', firstName: 'Bob', orgId: 'org123' }
    ];
    
    // Track the queries passed to People.find
    let capturedQueries = [];
    let capturedOptions = [];
    
    // Store original methods
    let originalFind;
    let originalUserAsync;

    beforeEach(() => {
        jest.clearAllMocks();
        capturedQueries = [];
        capturedOptions = [];
        
        // Save original methods
        originalFind = People.find;
        originalUserAsync = Meteor.userAsync;
        
        // Mock Meteor.userAsync
        Meteor.userAsync = jest.fn().mockResolvedValue(mockUser);
        
        // Mock People.find directly
        People.find = jest.fn((query, options) => {
            capturedQueries.push({...query}); // Clone to avoid reference issues
            capturedOptions.push({...options});
            
            return {
                fetchAsync: jest.fn().mockResolvedValue(mockPeople)
            };
        });
    });
    
    afterEach(() => {
        // Restore original methods
        People.find = originalFind;
        Meteor.userAsync = originalUserAsync;
    });

    it('should add orgId to query and return results', async () => {
        const query = { firstName: 'Alice' };
        const result = await PeopleUtils.getPeopleByQueryAndOptions(query);
        
        // Check that People.find was called
        expect(People.find).toHaveBeenCalled();
        
        // Check the query that was passed to People.find
        expect(capturedQueries[0]).toEqual({ firstName: 'Alice', orgId: 'org123' });
        
        // Check the options that were passed to People.find
        expect(capturedOptions[0]).toEqual({});
        
        // Check the result
        expect(result).toEqual(mockPeople);
    });

    it('should handle sort option correctly', async () => {
        const query = {};
        const options = { sort: { firstName: 1 } };
        
        await PeopleUtils.getPeopleByQueryAndOptions(query, options);
        
        expect(capturedQueries[0]).toEqual({ orgId: 'org123' });
        expect(capturedOptions[0]).toEqual({ sort: { firstName: 1 } });
    });

    it('should handle fields projection option', async () => {
        const query = {};
        const options = { fields: { firstName: 1, lastName: 1 } };
        
        await PeopleUtils.getPeopleByQueryAndOptions(query, options);
        
        expect(capturedQueries[0]).toEqual({ orgId: 'org123' });
        expect(capturedOptions[0]).toEqual({ fields: { firstName: 1, lastName: 1 } });
    });

    it('should handle pagination options', async () => {
        const query = {};
        const options = { skip: 10, limit: 5 };
        
        await PeopleUtils.getPeopleByQueryAndOptions(query, options);
        
        expect(capturedQueries[0]).toEqual({ orgId: 'org123' });
        expect(capturedOptions[0]).toEqual({ skip: 10, limit: 5 });
    });

    it('should handle all options combined', async () => {
        const query = { status: 'active' };
        const options = {
            sort: { lastName: -1 },
            fields: { firstName: 1 },
            skip: 20,
            limit: 10
        };
        
        await PeopleUtils.getPeopleByQueryAndOptions(query, options);
        
        expect(capturedQueries[0]).toEqual({ status: 'active', orgId: 'org123' });
        expect(capturedOptions[0]).toEqual({
            sort: { lastName: -1 },
            fields: { firstName: 1 },
            skip: 20,
            limit: 10
        });
    });

    it('should handle empty query object', async () => {
        const result = await PeopleUtils.getPeopleByQueryAndOptions({});
        
        expect(capturedQueries[0]).toEqual({ orgId: 'org123' });
        expect(capturedOptions[0]).toEqual({});
        expect(result).toEqual(mockPeople);
    });

    it('should handle undefined options', async () => {
        const query = { firstName: 'Alice' };
        
        await PeopleUtils.getPeopleByQueryAndOptions(query, undefined);
        
        expect(capturedQueries[0]).toEqual({ firstName: 'Alice', orgId: 'org123' });
        expect(capturedOptions[0]).toEqual({});
    });

    it('should handle partial options object', async () => {
        const query = {};
        const options = { sort: { firstName: 1 }, invalidOption: true };
        
        await PeopleUtils.getPeopleByQueryAndOptions(query, options);
        
        expect(capturedQueries[0]).toEqual({ orgId: 'org123' });
        // The invalid option should be filtered out
        expect(capturedOptions[0]).toEqual({ sort: { firstName: 1 } });
    });
    
    it('should override any orgId provided in the query', async () => {
        const query = { orgId: 'different-org' };
        
        await PeopleUtils.getPeopleByQueryAndOptions(query, {});
        
        // The user's orgId should take precedence
        expect(capturedQueries[0]).toEqual({ orgId: 'org123' });
    });
    
    it('should use the correct orgId when the user changes', async () => {
        // Change the mock user's orgId
        const newOrgId = 'another-org-456';
        Meteor.userAsync.mockResolvedValue({ orgId: newOrgId });
        
        await PeopleUtils.getPeopleByQueryAndOptions({}, {});
        
        // The query should use the new orgId
        expect(capturedQueries[0]).toEqual({ orgId: newOrgId });
    });
});

describe('getRelationshipsQuery', () => {
    it('should return the right query', () => {
        expect(PeopleUtils.getRelationshipsQuery('personId')).toEqual({
            $or: [ { personId: 'personId'}, { targetId: 'personId' } ]
        });
    })
})