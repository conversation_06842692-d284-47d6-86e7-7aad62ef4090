const mockSlingshot = {
    fileRestrictions: (name) => ({
      validate: (file) => {
        const allowedTypes = [
          "application/pdf",
          "image/jpeg",
          "image/png",
          "image/tiff",
          "image/gif",
          "image/heif",
          "image/webp",
          "image/svg+xml",
          "image/bmp"
        ];
        if (!allowedTypes.includes(file.type)) {
          throw new Error('Invalid file type');
        }
      }
    })
  };

describe('Slingshot file restrictions', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    it('accepts valid file types', () => {
      const validFiles = [
        { type: 'image/jpeg', size: 1000000 },
        { type: 'image/png', size: 1000000 },
        { type: 'image/tiff', size: 1000000 },
        { type: 'image/gif', size: 1000000 },
        { type: 'image/heif', size: 1000000 },
        { type: 'image/webp', size: 1000000 },
        { type: 'image/svg+xml', size: 1000000 },
        { type: 'image/bmp', size: 1000000 },
        { type: 'application/pdf', size: 1000000 }
      ];
  
      validFiles.forEach(file => {
        expect(() => {
          mockSlingshot.fileRestrictions("myDocumentRepositoryUploads").validate(file);
        }).not.toThrow();
      });
    });
  
    it('rejects invalid file types', () => {
      const invalidFiles = [
        { type: 'application/msword', size: 1000000 },
        { type: 'text/plain', size: 1000000 }
      ];
  
      invalidFiles.forEach(file => {
        expect(() => {
          mockSlingshot.fileRestrictions("myDocumentRepositoryUploads").validate(file);
        }).toThrow();
      });
    });
  });