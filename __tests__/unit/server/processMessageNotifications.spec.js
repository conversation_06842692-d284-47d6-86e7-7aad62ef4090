import { mockCollection } from '../../helpers/collectionMock';
import { mockProcessMessageNotifications } from '../../helpers/processMessageNotificationsMock';

jest.mock('../../../server/processMessageNotifications', () => ({
  processMessageNotifications: mockProcessMessageNotifications
}));

describe('processMessageNotifications', () => {
  beforeEach(() => {
    const Messages = mockCollection('Messages');
    const People = mockCollection('People');
    const Orgs = mockCollection('Orgs');
    const NewMessages = mockCollection('NewMessages');
  });

  it('processes a basic message notification', async () => {
    const messageId = 'msg123';

    Messages.findOneAsync.mockResolvedValue({
      _id: messageId,
      personId: 'sender1',
      currentRecipients: ['recipient1'],
      markedAsRead: [],
      realtimeProcessed: false
    });

    People.findOneAsync.mockResolvedValue({
      _id: 'recipient1',
      orgId: 'org123',
      type: 'parent',
      getActiveUserEmailAddress: jest.fn().mockResolvedValue('<EMAIL>'),
      getPushTargets: jest.fn().mockResolvedValue([])
    });

    Orgs.findOneAsync.mockResolvedValue({
      _id: 'org123',
      getLongName: jest.fn().mockReturnValue('Test Org'),
      hasCustomization: jest.fn().mockReturnValue(false)
    });

    await mockProcessMessageNotifications(messageId);

    expect(Messages.findOneAsync).toHaveBeenCalledWith(messageId);
    expect(NewMessages.upsertAsync).toHaveBeenCalled();
  });

  it('suppresses notifications for staff when customization is enabled', async () => {
    Messages.findOneAsync.mockResolvedValue({
      _id: 'msg123',
      personId: 'sender1',
      currentRecipients: ['staffMember1'],
      markedAsRead: [],
      realtimeProcessed: false
    });

    People.findOneAsync.mockResolvedValue({
      _id: 'staffMember1',
      orgId: 'org123',
      type: 'staff',
      getActiveUserEmailAddress: jest.fn(),
      getPushTargets: jest.fn().mockResolvedValue([])
    });

    Orgs.findOneAsync.mockResolvedValue({
      _id: 'org123',
      hasCustomization: jest.fn().mockReturnValue(true)
    });

    await mockProcessMessageNotifications('msg123');
    expect(Email.send).not.toHaveBeenCalled();
  });

  it('skips already processed messages', async () => {
    Messages.findOneAsync.mockResolvedValue({
      _id: 'msg123',
      realtimeProcessed: true
    });

    await mockProcessMessageNotifications('msg123');
    expect(People.findOneAsync).not.toHaveBeenCalled();
  });
  it('sends email notifications to valid recipients', async () => {
    Messages.findOneAsync.mockResolvedValue({
      _id: 'msg123',
      personId: 'sender1',
      currentRecipients: ['recipient1'],
      markedAsRead: [],
      realtimeProcessed: false
    });

    People.findOneAsync.mockResolvedValue({
      _id: 'recipient1',
      orgId: 'org123',
      type: 'parent',
      getActiveUserEmailAddress: jest.fn().mockResolvedValue('<EMAIL>'),
      getPushTargets: jest.fn().mockResolvedValue([{ endpointArn: 'arn:aws:123' }])
    });

    Orgs.findOneAsync.mockResolvedValue({
      _id: 'org123',
      getLongName: jest.fn().mockReturnValue('Test Org'),
      hasCustomization: jest.fn().mockReturnValue(false)
    });

    await mockProcessMessageNotifications('msg123');
    expect(Email.send).toHaveBeenCalled();
  });

  it('sends push notifications to recipients with valid targets', async () => {
    Messages.findOneAsync.mockResolvedValue({
      _id: 'msg123',
      personId: 'sender1',
      currentRecipients: ['recipient1'],
      markedAsRead: [],
      realtimeProcessed: false
    });

    People.findOneAsync.mockResolvedValue({
      _id: 'recipient1',
      orgId: 'org123',
      type: 'parent',
      getActiveUserEmailAddress: jest.fn().mockResolvedValue('<EMAIL>'),
      getPushTargets: jest.fn().mockResolvedValue([{ endpointArn: 'arn:aws:123' }])
    });

    Orgs.findOneAsync.mockResolvedValue({
      _id: 'org123',
      getLongName: jest.fn().mockReturnValue('Test Org'),
      hasCustomization: jest.fn().mockReturnValue(false)
    });

    await mockProcessMessageNotifications('msg123');

    expect(sendPushNotification).toHaveBeenCalled();
  });
});
