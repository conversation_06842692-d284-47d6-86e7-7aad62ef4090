import { registerExpoPushToken } from '../../../server/methodsPush';

describe('Meteor Push Notification Methods', () => {
  const mockUserId = 'user123';
  const mockToken = 'ExponentPushToken[1234]';
  const mockUser = {
    _id: mockUserId,
    fetchPerson: jest.fn()
  };
  const mockSuperAdmin = {
    _id: 'person123',
    type: 'admin',
    superAdmin: true
  };

  beforeEach(() => {
    jest.clearAllMocks();
    Meteor.userAsync = jest.fn().mockResolvedValue(mockUser);
    mockUser.fetchPerson = jest.fn().mockResolvedValue(mockSuperAdmin);
    Meteor.users = {
      updateAsync: jest.fn().mockResolvedValue(true)
    };
    Meteor.Error = jest.fn(function (code, message) {
      this.error = code;
      this.reason = message;
      this.errorType = 'Meteor.Error';
    });
  });

  describe('registerExpoPushToken', () => {
    it('successfully registers a valid push token for a user', async () => {
      const result = await registerExpoPushToken(mockToken);

      expect(Meteor.userAsync).toHaveBeenCalled();
      expect(Meteor.users.updateAsync).toHaveBeenCalledWith(
        mockUserId,
        {
          $set: {
            'services.expo.pushToken': mockToken
          }
        },
        { upsert: true }
      );
      expect(result).toEqual({ success: true });
    });
    it('handles empty or invalid token gracefully', async () => {
      // Testing with empty token
      await registerExpoPushToken('');

      expect(Meteor.users.updateAsync).toHaveBeenCalledWith(
          mockUserId,
          {
            $set: {
              'services.expo.pushToken': ''
            }
          },
          { upsert: true }
      );

      // Testing with null token
      Meteor.users.updateAsync.mockClear();
      await registerExpoPushToken(null);

      expect(Meteor.users.updateAsync).toHaveBeenCalledWith(
          mockUserId,
          {
            $set: {
              'services.expo.pushToken': null
            }
          },
          { upsert: true }
      );
    });
  });
});
