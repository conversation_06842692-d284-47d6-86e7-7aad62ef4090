import { Meteor } from 'meteor/meteor';
import { ReservationService } from '../../../../server/services/reservationService';
import moment from 'moment-timezone';

describe('ReservationService', () => {
    let service;
    let mockReservations;
    let mockPeople;
    let mockOrgs;

    beforeEach(() => {
        // Create mock collections with all required methods
        mockReservations = {
            findOneAsync: jest.fn(),
            updateAsync: jest.fn(),
            insertAsync: jest.fn(),
            findAsync: jest.fn()
        };
        mockPeople = {
            findOneAsync: jest.fn()
        };
        mockOrgs = {
            findOneAsync: jest.fn(),
            getTimezone: jest.fn().mockReturnValue('America/New_York')
        };

        // Mock moment to return fixed timestamp
        jest.spyOn(global.Date, 'now').mockImplementation(() => 1234567890);

        service = new ReservationService(mockReservations, mockPeople, mockOrgs);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('cancelReservation', () => {
        const mockReservation = {
            _id: 'reservation123',
            orgId: 'org123',
            recurringFrequency: null
        };

        beforeEach(() => {
            mockReservations.findOneAsync.mockResolvedValue(mockReservation);
            mockOrgs.findOneAsync.mockResolvedValue({
                getTimezone: () => 'America/New_York'
            });
        });

        it('should throw error if reservation not found', async () => {
            mockReservations.findOneAsync.mockResolvedValue(null);

            await expect(service.cancelReservation(
                'nonexistent',
                'cancellationReason',
                'cancellationComment',
                'user123'
            )).rejects.toThrow('404,Reservation not found');
        });

        it('should handle non-recurring reservation cancellation', async () => {
            await service.cancelReservation(
                'reservation123',
                'cancellationReason',
                'cancellationComment',
                'user123'
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'reservation123' },
                {
                    $set: {
                        cancellationDate: 1234567890,
                        cancellationReason: 'cancellationReason',
                        cancellationComments: 'cancellationComment',
                        cancelledBy: 'user123',
                        createdBy: 'user123',
                        createdAt: 1234567890
                    }
                }
            );
        });

        it('should handle non-recurring reservation cancellation with existing cancellation', async () => {
            mockReservations.findOneAsync.mockResolvedValue({
                ...mockReservation,
                cancellationReason: 'previous reason'
            });

            await service.cancelReservation(
                'reservation123',
                'cancellationReason',
                'cancellationComment',
                'user123'
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'reservation123' },
                {
                    $set: {
                        cancellationDate: 1234567890,
                        cancellationReason: 'cancellationReason',
                        cancellationComments: 'cancellationComment',
                        cancelledBy: 'user123',
                        updatedBy: 'user123',
                        updatedAt: 1234567890
                    }
                }
            );
        });

        it('should handle recurring reservation cancellation with occurrence time', async () => {
            const recurringReservation = {
                ...mockReservation,
                recurringFrequency: 1
            };
            mockReservations.findOneAsync.mockResolvedValue(recurringReservation);

            await service.cancelReservation(
                'reservation123',
                'cancellationReason',
                'cancellationComment',
                'user123',
                1227600000
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                'reservation123',
                { $push: { recurringExceptions: 1227600000 } }
            );

            expect(mockReservations.insertAsync).toHaveBeenCalledWith(expect.objectContaining({
                scheduledDate: 1227600000,
                cancellationReason: 'cancellationReason',
                cancellationComments: 'cancellationComment',
                cancellationDate: 1234567890,
                cancellationOriginalReservationId: 'reservation123',
                createdBy: 'SYSTEM-CANCEL-RECURRING-INSTANCE',
                createdAt: 1234567890,
                cancelledBy: 'user123'
            }));
        });

        it('should handle recurring reservation cancellation without occurrence time', async () => {
            const recurringReservation = {
                ...mockReservation,
                recurringFrequency: 1
            };
            mockReservations.findOneAsync.mockResolvedValue(recurringReservation);

            await service.cancelReservation(
                'reservation123',
                'cancellationReason',
                'cancellationComment',
                'user123'
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                'reservation123',
                { $push: { recurringExceptions: expect.any(Number) } }
            );

            expect(mockReservations.insertAsync).toHaveBeenCalledWith(expect.objectContaining({
                cancellationReason: 'cancellationReason',
                cancellationComments: 'cancellationComment',
                cancellationDate: 1234567890,
                cancellationOriginalReservationId: 'reservation123',
                createdBy: 'Mobile Cancellation',
                createdAt: 1234567890,
                cancelledBy: 'user123'
            }));
        });

        it('should handle non-recurring reservation with recurrenceId', async () => {
            mockReservations.findOneAsync.mockResolvedValue({
                ...mockReservation,
                recurrenceId: 'recurrence123',
                scheduledDate: 1227600000
            });

            await service.cancelReservation(
                'reservation123',
                'cancellationReason',
                'cancellationComment',
                'user123'
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledTimes(2);
            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'reservation123' },
                expect.any(Object)
            );
            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'recurrence123' },
                { $push: { recurringExceptions: 1227600000 } }
            );
        });

        it('should handle recurring reservation with future occurrence time', async () => {
            const recurringReservation = {
                ...mockReservation,
                recurringFrequency: 1
            };
            mockReservations.findOneAsync.mockResolvedValue(recurringReservation);
            const futureDate = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7 days in future

            await service.cancelReservation(
                'reservation123',
                'cancellationReason',
                'cancellationComment',
                'user123',
                futureDate
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                'reservation123',
                { $push: { recurringExceptions: futureDate } }
            );
        });

        it('should handle recurring reservation with past occurrence time', async () => {
            const recurringReservation = {
                ...mockReservation,
                recurringFrequency: 1
            };
            mockReservations.findOneAsync.mockResolvedValue(recurringReservation);
            const pastDate = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days in past

            await service.cancelReservation(
                'reservation123',
                'cancellationReason',
                'cancellationComment',
                'user123',
                pastDate
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                'reservation123',
                { $push: { recurringExceptions: pastDate } }
            );
        });

        it('should handle recurring reservation with multiple exceptions', async () => {
            const recurringReservation = {
                ...mockReservation,
                recurringFrequency: 1,
                recurringExceptions: [1227600000]
            };
            mockReservations.findOneAsync.mockResolvedValue(recurringReservation);

            await service.cancelReservation(
                'reservation123',
                'cancellationReason',
                'cancellationComment',
                'user123',
                1227600001
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                'reservation123',
                { $push: { recurringExceptions: 1227600001 } }
            );
        });
    });

    describe('handleFamilyCheckinCancellation', () => {
        const mockCheckInData = {
            orgId: 'org123',
            absent: true,
            absentReason: 'Sick',
            absentComment: 'Not feeling well'
        };

        beforeEach(() => {
            mockOrgs.findOneAsync.mockResolvedValue({
                getTimezone: () => 'America/New_York'
            });
        });

        it('should return false if no active reservations found', async () => {
            mockReservations.findAsync.mockResolvedValue([]);

            const result = await service.handleFamilyCheckinCancellation(
                'person123',
                mockCheckInData,
                'user123'
            );

            expect(result).toBe(false);
        });

        it('should cancel all active reservations for the day', async () => {
            const mockActiveReservations = [
                { _id: 'reservation1', orgId: 'org123' },
                { _id: 'reservation2', orgId: 'org123' }
            ];

            mockReservations.findAsync.mockResolvedValue(mockActiveReservations);
            mockReservations.findOneAsync.mockImplementation((query) => {
                return Promise.resolve(mockActiveReservations.find(r => r._id === query._id));
            });

            await service.handleFamilyCheckinCancellation(
                'person123',
                mockCheckInData,
                'user123'
            );

            expect(mockReservations.findAsync).toHaveBeenCalledWith({
                personId: 'person123',
                scheduledDate: expect.any(Number),
                cancellationDate: { $exists: false }
            });

            expect(mockReservations.updateAsync).toHaveBeenCalledTimes(2);
            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'reservation1' },
                {
                    $set: {
                        cancellationDate: 1234567890,
                        cancellationReason: 'Sick',
                        cancellationComments: 'Not feeling well',
                        cancelledBy: 'user123',
                        createdBy: 'user123',
                        createdAt: 1234567890
                    }
                }
            );
            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'reservation2' },
                {
                    $set: {
                        cancellationDate: 1234567890,
                        cancellationReason: 'Sick',
                        cancellationComments: 'Not feeling well',
                        cancelledBy: 'user123',
                        createdBy: 'user123',
                        createdAt: 1234567890
                    }
                }
            );
        });

        it('should handle missing absent reason', async () => {
            const mockCheckInDataWithoutReason = {
                orgId: 'org123',
                absent: true
            };

            const mockActiveReservations = [
                { _id: 'reservation1', orgId: 'org123' }
            ];

            mockReservations.findAsync.mockResolvedValue(mockActiveReservations);
            mockReservations.findOneAsync.mockResolvedValue(mockActiveReservations[0]);

            await service.handleFamilyCheckinCancellation(
                'person123',
                mockCheckInDataWithoutReason,
                'user123'
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'reservation1' },
                {
                    $set: {
                        cancellationDate: 1234567890,
                        cancellationReason: 'Mobile Cancellation',
                        cancellationComments: undefined,
                        cancelledBy: 'user123',
                        createdBy: 'user123',
                        createdAt: 1234567890
                    }
                }
            );
        });

        it('should handle missing absent comment', async () => {
            const mockCheckInDataWithoutComment = {
                orgId: 'org123',
                absent: true,
                absentReason: 'Sick'
            };

            const mockActiveReservations = [
                { _id: 'reservation1', orgId: 'org123' }
            ];

            mockReservations.findAsync.mockResolvedValue(mockActiveReservations);
            mockReservations.findOneAsync.mockResolvedValue(mockActiveReservations[0]);

            await service.handleFamilyCheckinCancellation(
                'person123',
                mockCheckInDataWithoutComment,
                'user123'
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'reservation1' },
                {
                    $set: {
                        cancellationDate: 1234567890,
                        cancellationReason: 'Sick',
                        cancellationComments: undefined,
                        cancelledBy: 'user123',
                        createdBy: 'user123',
                        createdAt: 1234567890
                    }
                }
            );
        });

        it('should handle multiple active reservations with different orgs', async () => {
            const mockActiveReservations = [
                { _id: 'reservation1', orgId: 'org123' },
                { _id: 'reservation2', orgId: 'org456' }
            ];

            mockReservations.findAsync.mockResolvedValue(mockActiveReservations);
            mockReservations.findOneAsync.mockImplementation((query) => {
                return Promise.resolve(mockActiveReservations.find(r => r._id === query._id));
            });
            mockOrgs.findOneAsync.mockImplementation((query) => {
                return Promise.resolve({
                    getTimezone: () => query._id === 'org123' ? 'America/New_York' : 'America/Los_Angeles'
                });
            });

            await service.handleFamilyCheckinCancellation(
                'person123',
                mockCheckInData,
                'user123'
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledTimes(2);
        });

        it('should handle active reservations with existing cancellations', async () => {
            const mockActiveReservations = [
                { 
                    _id: 'reservation1', 
                    orgId: 'org123',
                    cancellationDate: 1234567890,
                    cancellationReason: 'Previous Cancellation'
                }
            ];

            mockReservations.findAsync.mockResolvedValue([]); // No active reservations due to existing cancellation

            const result = await service.handleFamilyCheckinCancellation(
                'person123',
                mockCheckInData,
                'user123'
            );

            expect(result).toBe(false);
            expect(mockReservations.updateAsync).not.toHaveBeenCalled();
        });

        it('should handle active reservations with recurrenceId', async () => {
            const mockActiveReservations = [
                { 
                    _id: 'reservation1', 
                    orgId: 'org123',
                    recurrenceId: 'recurrence123',
                    scheduledDate: 1227600000
                }
            ];

            mockReservations.findAsync.mockResolvedValue(mockActiveReservations);
            mockReservations.findOneAsync.mockResolvedValue(mockActiveReservations[0]);

            await service.handleFamilyCheckinCancellation(
                'person123',
                mockCheckInData,
                'user123'
            );

            expect(mockReservations.updateAsync).toHaveBeenCalledTimes(2);
            expect(mockReservations.updateAsync).toHaveBeenCalledWith(
                { _id: 'recurrence123' },
                { $push: { recurringExceptions: 1227600000 } }
            );
        });

        it('should handle active reservations with invalid org', async () => {
            const mockActiveReservations = [
                { _id: 'reservation1', orgId: 'invalid-org' }
            ];

            mockReservations.findAsync.mockResolvedValue(mockActiveReservations);
            mockOrgs.findOneAsync.mockResolvedValue(null);

            await expect(service.handleFamilyCheckinCancellation(
                'person123',
                mockCheckInData,
                'user123'
            )).rejects.toThrow();
        });
    });
}); 