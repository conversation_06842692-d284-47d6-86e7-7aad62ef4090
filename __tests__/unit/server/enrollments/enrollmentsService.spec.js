import { Meteor } from 'meteor/meteor';
import { mockCollection } from "../../../helpers/collectionMock";
import { mockMeteor } from '../../../helpers/meteorMock';
import { EnrollmentsService } from '../../../../server/enrollments/enrollmentsService';
import { describe, it, afterAll, afterEach, beforeEach, jest } from '@jest/globals';
import moment from 'moment-timezone';
import { ItemDateTypes } from '../../../../lib/constants/billingConstants';
import {DateTimeUtils} from "../../../../lib/util/dateTimeUtils";
import { OrgsLib } from '../../../../lib/orgsLib';
import { mockRandom } from '../../../helpers/randomMock';
import { AvailableCustomizations } from '../../../../lib/customizations';
import { Orgs } from "../../../../lib/collections/orgs";
import { People } from "../../../../lib/collections/people";
import { Reservations } from "../../../../lib/collections/reservations";
import { Invoices } from "../../../../lib/collections/invoices";

const meteorMock = mockMeteor();
const randomMock = mockRandom();
describe('EnrollmentsService', () => {
    afterAll(async () => {
        jest.clearAllMocks();
    });

    describe('getItemDays', () => {
        it('should return the correct days', () => {
            const item = {
                details: {
                    dateType: 'dateRange',
                    serviceStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf(),
                    serviceEndDate: moment.tz('2023-01-06', 'America/New_York').valueOf(),
                }
            }
            expect(EnrollmentsService.getItemDays(item, 'America/New_York')).toStrictEqual([
                moment.tz('2023-01-02', 'America/New_York').valueOf(),
                moment.tz('2023-01-03', 'America/New_York').valueOf(),
                moment.tz('2023-01-04', 'America/New_York').valueOf(),
                moment.tz('2023-01-05', 'America/New_York').valueOf(),
                moment.tz('2023-01-06', 'America/New_York').valueOf(),
            ]);
            const item2 = {
                details: {
                    dateType: 'individualDates',
                    individualDates: [
                        moment.tz('2023-01-06', 'America/New_York').valueOf(),
                        moment.tz('2023-01-12', 'America/New_York').valueOf(),
                    ]
                }
            }
            expect(EnrollmentsService.getItemDays(item2, 'America/New_York')).toStrictEqual([
                moment.tz('2023-01-06', 'America/New_York').valueOf(),
                moment.tz('2023-01-12', 'America/New_York').valueOf(),
            ]);
            const item3 = {
                details: {
                    dateType: 'recurring',
                    recurringDays: ['mon', 'wed', 'fri'],
                    recurringFrequency: 2,
                    recurringOccurrences: 3,
                    recurringStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf(),
                }
            }
            expect(EnrollmentsService.getItemDays(item3, 'America/New_York')).toStrictEqual([
                moment.tz('2023-01-02', 'America/New_York').valueOf(),
                moment.tz('2023-01-04', 'America/New_York').valueOf(),
                moment.tz('2023-01-06', 'America/New_York').valueOf(),
                moment.tz('2023-01-16', 'America/New_York').valueOf(),
                moment.tz('2023-01-18', 'America/New_York').valueOf(),
                moment.tz('2023-01-20', 'America/New_York').valueOf(),
                moment.tz('2023-01-30', 'America/New_York').valueOf(),
                moment.tz('2023-02-01', 'America/New_York').valueOf(),
                moment.tz('2023-02-03', 'America/New_York').valueOf(),
            ]);

            const item4 = {
                details: {
                    dateType: 'recurring',
                    recurringDays: ['mon', 'wed', 'fri'],
                    recurringFrequency: 2,
                    recurringOccurrences: 3,
                    recurringStartDate: moment.tz('2023-01-06', 'America/New_York').valueOf(),
                }
            }
            expect(EnrollmentsService.getItemDays(item4, 'America/New_York')).toStrictEqual([
                moment.tz('2023-01-06', 'America/New_York').valueOf(),
                moment.tz('2023-01-16', 'America/New_York').valueOf(),
                moment.tz('2023-01-18', 'America/New_York').valueOf(),
                moment.tz('2023-01-20', 'America/New_York').valueOf(),
                moment.tz('2023-01-30', 'America/New_York').valueOf(),
                moment.tz('2023-02-01', 'America/New_York').valueOf(),
                moment.tz('2023-02-03', 'America/New_York').valueOf(),
            ]);
        });

        it('should return correct days for a date range item with weekend exclusions', () => {
            const item = {
                details: {
                    dateType: 'dateRange',
                    serviceStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf(), // Monday
                    serviceEndDate: moment.tz('2023-01-08', 'America/New_York').valueOf(), // Sunday
                    excludeSaturdays: true,
                    excludeSundays: true
                }
            };

            expect(EnrollmentsService.getItemDays(item, 'America/New_York')).toStrictEqual([
                moment.tz('2023-01-02', 'America/New_York').valueOf(), // Monday
                moment.tz('2023-01-03', 'America/New_York').valueOf(), // Tuesday
                moment.tz('2023-01-04', 'America/New_York').valueOf(), // Wednesday
                moment.tz('2023-01-05', 'America/New_York').valueOf(), // Thursday
                moment.tz('2023-01-06', 'America/New_York').valueOf(), // Friday
            ]);
        });

        it('should return an empty array for a recurring item with zero occurrences', () => {
            const item = {
                details: {
                    dateType: 'recurring',
                    recurringDays: ['mon', 'wed'],
                    recurringFrequency: 1,
                    recurringOccurrences: 0,
                    recurringStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf(),
                }
            };

            expect(EnrollmentsService.getItemDays(item, 'America/New_York')).toStrictEqual([]);
        });

        it('should return an empty array for an unsupported dateType', () => {
            const item = {
                details: {
                    dateType: 'unsupportedType'
                }
            };

            expect(EnrollmentsService.getItemDays(item, 'America/New_York')).toStrictEqual([]);
        });

        it('should return an empty array if item details are missing', () => {
            const item = {
                details: null
            };

            expect(EnrollmentsService.getItemDays(item, 'America/New_York')).toStrictEqual([]);
        });
    });

    describe('getScheduleTypeAvailabilities', () => {
        const peopleMock = People;
        const reservationsMock = Reservations;
        const scheduleTypes = [
            {
                _id: "WAayXcBzyaRbFxuDd",
                type: "Before Care",
                maxEnrollment: {
                    1: 5,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                }
            },
            {
                _id: "WAayXcBzyaRbFxuDe",
                type: "After Care",
                maxEnrollment: {
                    1: 2,
                    2: 2,
                    3: 2,
                    4: 2,
                    5: 2
                }
            }
        ];
        const reservations = [];
        let date = new moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
        for (let i = 0; i < 2; i++) {
            for (let j = 0; j < 2; j++) {
                reservations.push({
                    _id: `reservation${ i }${ j }`,
                    orgId: '123',
                    selectedPerson: '8a4b2c3',
                    scheduleType: scheduleTypes[j]._id,
                    scheduledDate: date.valueOf(),
                    recurringFrequency: 1,
                    recurringDays: ['mon']
                });
            }
        }
        beforeEach(async () => {
            peopleMock.find.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=>[{
                    _id: '8a4b2c3',
                    orgId: '123',
                    type: 'person'
                }])
            });
            reservationsMock.find.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=>reservations)
            });
        });
        afterEach(() => {
            jest.clearAllMocks();
        });
        it('getScheduleTypeAvailabilities no org provided', async () => {
            const orgsMock = Orgs.findOneAsync;
            orgsMock.mockImplementationOnce(() => null);
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('orgId', '07/17/2023', 'timePeriodId')).toBeNull();
            expect(orgsMock.mock.calls.length).toBe(1);
        });

        it('getScheduleTypeAvailabilities no time period provided', async () => {
            const orgsMock = Orgs.findOneAsync;
            const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');
            orgsMock.mockImplementation(() => ({ _id: '123', getTimezone: getTimezone }));
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', 'timePeriodId')).toBeNull();
            expect(orgsMock.mock.calls.length).toBe(1);
        });

        it('getScheduleTypeAvailabilities no schedule types', async () => {
            const orgsMock = Orgs.findOneAsync;
            const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');
            const getScheduleTypes = jest.fn().mockImplementation(() => []);
            const endDate = new moment.tz('08/04/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            orgsMock.mockImplementation(() => ({
                _id: '123',
                getTimezone: getTimezone,
                billing: {
                    timePeriods: [{ _id: '123456', endDate: endDate.valueOf() }]
                },
                getScheduleTypes: getScheduleTypes
            }));
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toBeNull();
            expect(getTimezone.mock.calls.length).toBe(1);
            expect(getScheduleTypes.mock.calls.length).toBe(1);
        });

        it('getScheduleTypeAvailabilities does not double count schedules for the same person on the same day', async () => {
            const scheduleTypes = [
                {
                    _id: "WAayXcBzyaRbFxuDd",
                    type: "Before Care",
                    maxEnrollment: {
                        1: 5,
                        2: 5,
                        3: 5,
                        4: 5,
                        5: 5
                    }
                },
                {
                    _id: "WAayXcBzyaRbFxuDe",
                    type: "After Care",
                    maxEnrollment: {
                        1: 2,
                        2: 2,
                        3: 2,
                        4: 2,
                        5: 2
                    }
                }
            ];
            const orgsMock = Orgs.findOneAsync;
            const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');
            const hasCustomization = jest.fn().mockImplementation(() => false);
            const getScheduleTypes = jest.fn().mockImplementation(() => scheduleTypes);
            // 8/4 is a Friday
            const endDate = new moment.tz('08/04/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            orgsMock.mockImplementation(() => ({
                _id: '123',
                getTimezone: getTimezone,
                hasCustomization: hasCustomization,
                billing: {
                    timePeriods: [{ _id: '123456', endDate: endDate.valueOf() }]
                },
                getScheduleTypes: getScheduleTypes
            }));

            // Add 2 schedules for the same person for each schedule type for a Monday
            // const reservations = [];
            // let date = new moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            // for (let i = 0; i < 2; i++) {
            //     for (let j = 0; j < 2; j++) {
            //         reservations.push({
            //             _id: `reservation${ i }${ j }`,
            //             orgId: '123',
            //             selectedPerson: '8a4b2c3',
            //             scheduleType: scheduleTypes[j]._id,
            //             scheduledDate: date.valueOf(),
            //             recurringFrequency: 1,
            //             recurringDays: ['mon']
            //         });
            //     }
            // }
            // reservationsMock.find.mockImplementation(() => reservations);

            // 7/17 is a Monday
            // There should only be 1 availability used for each schedule type since we aren't double counting
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 4,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                },
                [scheduleTypes[1]._id]: {
                    1: 1,
                    2: 2,
                    3: 2,
                    4: 2,
                    5: 2
                }
            });
        });

        it('getScheduleTypeAvailabilities works correctly', async () => {
            const scheduleTypes = [
                {
                    _id: "WAayXcBzyaRbFxuDd",
                    type: "Before Care",
                    maxEnrollment: {
                        1: 5,
                        2: 5,
                        3: 5,
                        4: 5,
                        5: 5
                    }
                },
                {
                    _id: "WAayXcBzyaRbFxuDe",
                    type: "After Care",
                    maxEnrollment: {
                        1: 2,
                        2: 2,
                        3: 2,
                        4: 2,
                        5: 2
                    }
                }
            ];
            const orgsMock = Orgs.findOneAsync;
            //const reservationsMock = Reservations;
            const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');
            const hasCustomization = jest.fn().mockImplementation(() => false);
            const getScheduleTypes = jest.fn().mockImplementation(() => scheduleTypes);
            // 8/4 is a Friday
            const endDate = new moment.tz('08/04/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            orgsMock.mockImplementation(() => ({
                _id: '123',
                getTimezone: getTimezone,
                hasCustomization: hasCustomization,
                billing: {
                    timePeriods: [{ _id: '123456', endDate: endDate.valueOf() }]
                },
                getScheduleTypes: getScheduleTypes
            }));

            let reservations = [];
            reservationsMock.find.mockReturnValue({fetchAsync: () => reservations});
            //reservationsMock.find.mockImplementation(() => reservations);

            // 7/17 is a Monday
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 5,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                },
                [scheduleTypes[1]._id]: {
                    1: 2,
                    2: 2,
                    3: 2,
                    4: 2,
                    5: 2
                }
            });
            expect(getTimezone.mock.calls.length).toBe(1);
            expect(getScheduleTypes.mock.calls.length).toBe(1);
            expect(peopleMock.find.mock.calls.length).toBe(1);
            expect(reservationsMock.find.mock.calls.length).toBe(1);

            // Add a schedule for a Monday
            reservations = [];
            const excludedReservationIds = [];
            let date = new moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            for (let i = 0; i < 1; i++) {
                for (let j = 0; j < 2; j++) {
                    excludedReservationIds.push(`reservation${ i }${ j }`);
                    reservations.push({
                        _id: `reservation${i}${j}`,
                        orgId: '123',
                        selectedPerson: '8a4b2c3',
                        scheduleType: scheduleTypes[j]._id,
                        scheduledDate: date.valueOf(),
                        recurringFrequency: 1,
                        recurringDays: ['mon']
                    });
                }
            }
            reservationsMock.find.mockReturnValue({fetchAsync: () => reservations});
            //reservationsMock.find.mockImplementation(() => reservations);
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 4,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                },
                [scheduleTypes[1]._id]: {
                    1: 1,
                    2: 2,
                    3: 2,
                    4: 2,
                    5: 2
                }
            });

            // Now exclude those schedules; will go back to original availability
            reservationsMock.find.mockReturnValue({fetchAsync: () => []});
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456', null, excludedReservationIds)).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 5,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                },
                [scheduleTypes[1]._id]: {
                    1: 2,
                    2: 2,
                    3: 2,
                    4: 2,
                    5: 2
                }
            });
            expect(reservationsMock.find.mock.calls.length).toBe(3);
            expect(reservationsMock.find.mock.calls[2]).toEqual([{
                orgId: '123',
                selectedPerson: { '$in': ['8a4b2c3'] },
                scheduleType: { '$in': ['WAayXcBzyaRbFxuDd', 'WAayXcBzyaRbFxuDe'] },
                cancellationDate: { '$exists': false },
                '$or': [{
                    '$and': [
                        {
                            "scheduledDate": {
                                "$gte": 1689570000000
                            }
                        },
                        {
                            "scheduledDate": {
                                "$lte": 1691125200000
                            }
                        }
                    ]
                }, {
                    '$and': [
                        {
                            "scheduledDate": {
                                "$lte": 1689570000000
                            }
                        },
                        {
                            "recurringFrequency": 1
                        },
                        {
                            "$or": [
                                {
                                    "scheduledEndDate": {
                                        "$gte": 1689570000000
                                    }
                                },
                                {
                                    "scheduledEndDate": null
                                }
                            ]
                        }
                    ]
                }],
                _id: { '$nin': excludedReservationIds }
            }])

            // Add another monday schedule
            date = new moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            for (let i = 0; i < 1; i++) {
                for (let j = 0; j < 2; j++) {
                    reservations.push({
                        _id: `reservation${i}${j}`,
                        orgId: '123',
                        selectedPerson: `8a4b2c3${ j }`,
                        scheduleType: scheduleTypes[j]._id,
                        scheduledDate: date.valueOf(),
                        recurringFrequency: 1,
                        recurringDays: ['mon']
                    });
                }
            }
            reservationsMock.find.mockReturnValue({fetchAsync: () => reservations });
            //reservationsMock.find.mockImplementation(() => reservations);
            // There should be 2 Monday schedules now, which blocks all Monday schedules for the 2nd schedule type
            // The 1st is still available since there are 5 slots and only 2 are taken
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 3,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                },
                [scheduleTypes[1]._id]: {
                    1: 0,
                    2: 2,
                    3: 2,
                    4: 2,
                    5: 2
                }
            });

            // Make 3 Monday and Wednesday schedules
            reservations = [];
            date = new moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            for (let i = 0; i < 3; i++) {
                for (let j = 0; j < 2; j++) {
                    reservations.push({
                        _id: `reservation${i}${j}`,
                        orgId: '123',
                        selectedPerson: `8a4b2c3${ i }${ j }`,
                        scheduleType: scheduleTypes[j]._id,
                        scheduledDate: date.valueOf(),
                        recurringFrequency: 1,
                        recurringDays: ['mon', 'wed']
                    });
                }
            }
            date.add(2, 'days'); // Wednesday
            date.add(1, 'week'); // 7/26
            // With 2 more Wednesday schedules
            // This will make 5 Wednesday schedules on 7/26, blocking that day of the week for both schedule types
            for (let i = 0; i < 2; i++) {
                for (let j = 0; j < 2; j++) {
                    reservations.push({
                        _id: `reservation${i}${j}`,
                        orgId: '123',
                        selectedPerson: `8a4b2c3h${ i }${ j }`,
                        scheduleType: scheduleTypes[j]._id,
                        scheduledDate: date.valueOf(),
                    });
                }
            }
            reservationsMock.find.mockReturnValue({fetchAsync: () => reservations});
            // There should be 2 Monday schedules now, which blocks all Monday schedules for the 2nd schedule type
            // The 1st is still available since there are 5 slots and only 2 are taken
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 2,
                    2: 5,
                    3: 0,
                    4: 5,
                    5: 5
                },
                [scheduleTypes[1]._id]: {
                    1: 0,
                    2: 2,
                    3: 0,
                    4: 2,
                    5: 2
                }
            });

            // Make 6 Friday schedules for the 1st schedule type
            date = new moment.tz('07/21/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            reservations = [];
            for (let i = 0; i < 6; i++) {
                reservations.push({
                    _id: `reservation0`,
                    orgId: '123',
                    selectedPerson: `8a4b2c3a${ i }`,
                    scheduleType: scheduleTypes[0]._id,
                    scheduledDate: date.valueOf(),
                });
            }
            // Only Friday of the 1st will be blocked
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 5,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 0
                },
                [scheduleTypes[1]._id]: {
                    1: 2,
                    2: 2,
                    3: 2,
                    4: 2,
                    5: 2
                }
            });

            // Make 2 Tuesday single day schedules for the 2nd schedule type on different Tuesdays
            date = new moment.tz('07/18/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            reservations = [];
            for (let i = 0; i < 2; i++) {
                reservations.push({
                    _id: `reservation0`,
                    orgId: '123',
                    selectedPerson: `8a4b2c3${ i }m`,
                    scheduleType: scheduleTypes[1]._id,
                    scheduledDate: date.valueOf(),
                });
                date.add(1, 'week');
            }
            // All are available since there is only a max of 1 schedule per any Tuesday
            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 5,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                },
                [scheduleTypes[1]._id]: {
                    1: 2,
                    2: 1,
                    3: 2,
                    4: 2,
                    5: 2
                }
            });
        });

        it('getScheduleTypeAvailabilities does not count non-recurring single-day schedules outside date range', async () => {
            const scheduleTypes = [
                {
                    _id: "WAayXcBzyaRbFxuDd",
                    type: "Before Care",
                    maxEnrollment: {
                        1: 5,
                        2: 5,
                        3: 5,
                        4: 5,
                        5: 5
                    }
                }
            ];
            const orgsMock = Orgs.findOneAsync;
            //const reservationsMock = Reservations;
            const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');
            const hasCustomization = jest.fn().mockImplementation(() => false);
            const getScheduleTypes = jest.fn().mockImplementation(() => scheduleTypes);
            const endDate = new moment.tz('08/04/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            orgsMock.mockImplementation(() => ({
                _id: '123',
                getTimezone: getTimezone,
                hasCustomization: hasCustomization,
                billing: {
                    timePeriods: [{_id: '123456', endDate: endDate.valueOf()}]
                },
                getScheduleTypes: getScheduleTypes
            }));

            const reservations = [
                {
                    _id: 'zzzzpHcsnkh3ynvad',
                    orgId: '123',
                    selectedPerson: '8a4b2c3',
                    scheduleType: scheduleTypes[0]._id,
                    scheduledDate: moment.tz('07/10/2023', 'MM/DD/YYYY', 'America/Chicago').valueOf(),
                    recurringFrequency: null,
                    recurringDays: []
                }
            ];
            reservationsMock.find.mockReturnValue({fetchAsync: () => reservations.filter(
                reservation => reservation.scheduledDate >= moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day').valueOf() &&
                    reservation.scheduledDate <= endDate.valueOf()
            )});

            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 5,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                }
            });
        });

        it('getScheduleTypeAvailabilities counts non-recurring single-day schedules within date range', async () => {
            const scheduleTypes = [
                {
                    _id: "WAayXcBzyaRbFxuDd",
                    type: "Before Care",
                    maxEnrollment: {
                        1: 5,
                        2: 5,
                        3: 5,
                        4: 5,
                        5: 5
                    }
                }
            ];
            const orgsMock = Orgs.findOneAsync;
            //const reservationsMock = Reservations;
            const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');
            const hasCustomization = jest.fn().mockImplementation(() => false);
            const getScheduleTypes = jest.fn().mockImplementation(() => scheduleTypes);
            const endDate = new moment.tz('08/04/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            orgsMock.mockImplementation(() => ({
                _id: '123',
                getTimezone: getTimezone,
                hasCustomization: hasCustomization,
                billing: {
                    timePeriods: [{_id: '123456', endDate: endDate.valueOf()}]
                },
                getScheduleTypes: getScheduleTypes
            }));

            const reservations = [
                {
                    _id: 'zzzzpHcsnkh3ynvad',
                    orgId: '123',
                    selectedPerson: '8a4b2c3',
                    scheduleType: scheduleTypes[0]._id,
                    scheduledDate: moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').valueOf(),
                    recurringFrequency: null,
                    recurringDays: []
                }
            ];
            reservationsMock.find.mockReturnValue({fetchAsync: () => reservations.filter(
                reservation => reservation.scheduledDate >= moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day').valueOf() &&
                    reservation.scheduledDate <= endDate.valueOf()
            )});

            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 4,
                    2: 5,
                    3: 5,
                    4: 5,
                    5: 5
                }
            });
        });

        it('getScheduleTypeAvailabilities counts daily recurring schedules correctly', async () => {
            const scheduleTypes = [
                {
                    _id: "WAayXcBzyaRbFxuDd",
                    type: "Before Care",
                    maxEnrollment: {
                        1: 5,
                        2: 5,
                        3: 5,
                        4: 5,
                        5: 5
                    }
                }
            ];
            const orgsMock = Orgs.findOneAsync;
            //const reservationsMock = Reservations;
            const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');
            const hasCustomization = jest.fn().mockImplementation(() => false);
            const getScheduleTypes = jest.fn().mockImplementation(() => scheduleTypes);
            const endDate = new moment.tz('08/04/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            orgsMock.mockImplementation(() => ({
                _id: '123',
                getTimezone: getTimezone,
                hasCustomization: hasCustomization,
                billing: {
                    timePeriods: [{_id: '123456', endDate: endDate.valueOf()}]
                },
                getScheduleTypes: getScheduleTypes
            }));

            const reservations = [
                {
                    _id: 'zzzzpHcsnkh3ynvad',
                    orgId: '123',
                    selectedPerson: '8a4b2c3',
                    scheduleType: scheduleTypes[0]._id,
                    scheduledDate: moment.tz('07/10/2023', 'MM/DD/YYYY', 'America/Chicago').valueOf(),
                    recurringFrequency: 1,
                    recurringDays: ['mon', 'tue', 'wed', 'thu', 'fri']
                }
            ];

            reservationsMock.find.mockReturnValue({
                fetchAsync: () =>reservations});

            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 4,
                    2: 4,
                    3: 4,
                    4: 4,
                    5: 4
                }
            });
        });

        it('getScheduleTypeAvailabilities counts daily recurring schedules that started before date range', async () => {
            const scheduleTypes = [
                {
                    _id: "WAayXcBzyaRbFxuDd",
                    type: "Before Care",
                    maxEnrollment: {
                        1: 5,
                        2: 5,
                        3: 5,
                        4: 5,
                        5: 5
                    }
                }
            ];
            const orgsMock = Orgs.findOneAsync;
           // const reservationsMock = Reservations;
            const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');
            const hasCustomization = jest.fn().mockImplementation(() => false);
            const getScheduleTypes = jest.fn().mockImplementation(() => scheduleTypes);
            const endDate = new moment.tz('08/04/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day');
            orgsMock.mockImplementation(() => ({
                _id: '123',
                getTimezone: getTimezone,
                hasCustomization: hasCustomization,
                billing: {
                    timePeriods: [{_id: '123456', endDate: endDate.valueOf()}]
                },
                getScheduleTypes: getScheduleTypes
            }));

            const reservations = [
                {
                    _id: 'zzzzpHcsnkh3ynvad',
                    orgId: '123',
                    selectedPerson: '8a4b2c3',
                    scheduleType: scheduleTypes[0]._id,
                    scheduledDate: moment.tz('07/10/2023', 'MM/DD/YYYY', 'America/Chicago').valueOf(),
                    recurringFrequency: 1,
                    recurringDays: ['mon', 'tue', 'wed', 'thu', 'fri'],
                    scheduledEndDate: null
                }
            ];
            //reservationsMock.find.mockReturnValue({fetchAsync: () => reservations});
            reservationsMock.find.mockReturnValue({fetchAsync: () => reservations.filter(
                reservation => reservation.recurringFrequency === 1 &&
                    (reservation.scheduledEndDate === null || reservation.scheduledEndDate >= moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago').startOf('day').valueOf())
            )});

            expect(await EnrollmentsService.getScheduleTypeAvailabilities('123', '07/17/2023', '123456')).toStrictEqual({
                [scheduleTypes[0]._id]: {
                    1: 4,
                    2: 4,
                    3: 4,
                    4: 4,
                    5: 4
                }
            });
        });
    });

    describe('buildSchedulesQuery', () => {
        it('should build a query with the provided parameters', () => {
            const org = { _id: 'org123' };
            const activeChildrenIds = ['child1', 'child2'];
            const scheduleTypes = [{ _id: 'type1' }, { _id: 'type2' }];
            const startDateTimestamp = 1689570000000; // July 17, 2023
            const endDateTimestamp = 1691125200000; // August 4, 2023
            const excludedReservationIds = ['res1', 'res2'];

            const result = EnrollmentsService.buildSchedulesQuery(
                org,
                activeChildrenIds,
                scheduleTypes,
                startDateTimestamp,
                endDateTimestamp,
                excludedReservationIds
            );

            expect(result).toEqual({
                orgId: 'org123',
                selectedPerson: { $in: ['child1', 'child2'] },
                scheduleType: { $in: ['type1', 'type2'] },
                cancellationDate: { $exists: false },
                $or: [
                    {
                        $and: [
                            { scheduledDate: { $gte: 1689570000000 } },
                            { scheduledDate: { $lte: 1691125200000 } }
                        ]
                    },
                    {
                        $and: [
                            { scheduledDate: { $lte: 1689570000000 } },
                            { recurringFrequency: 1 },
                            {
                                $or: [
                                    { scheduledEndDate: { $gte: 1689570000000 } },
                                    { scheduledEndDate: null }
                                ]
                            }
                        ]
                    }
                ],
                _id: { $nin: ['res1', 'res2'] }
            });
        });
    });

    describe('calculateAvailabilities', () => {
        it('should calculate availabilities for given schedules', () => {
            const schedules = [
                {
                    scheduleType: 'type1',
                    selectedPerson: 'person1',
                    scheduledDate: 1689570000000, // July 17, 2023
                    recurringDays: ['mon', 'wed'],
                    recurringFrequency: 1
                }
            ];

            const scheduleTypes = [
                {
                    _id: 'type1',
                    maxEnrollment: { 1: 5, 3: 5 } // Monday and Wednesday
                }
            ];

            const startDate = new moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago');
            const endDateTimestamp = 1691125200000; // August 4, 2023
            const org = { getTimezone: () => 'America/Chicago', hasCustomization: () => false };

            const result = EnrollmentsService.calculateAvailabilities(schedules, scheduleTypes, startDate, endDateTimestamp, org);

            expect(result).toEqual({
                type1: {
                    1: 4, // Monday
                    2: 999999, // Tuesday (default max enrollment if none are set)
                    3: 4, // Wednesday
                    4: 999999, // Thursday
                    5: 999999 // Friday
                }
            });
        });
    });

    describe('getDaysMap', () => {
        it('should return a day map for the organization', () => {
            const org = { hasCustomization: () => false };
            const result = EnrollmentsService.getDaysMap(org);
            expect(result).toEqual({
                1: 'mon',
                2: 'tue',
                3: 'wed',
                4: 'thu',
                5: 'fri'
            });
        });
        it('should return a day map for the organization with weekends when enabled', () => {
            const org = { hasCustomization: () => true };
            const result = EnrollmentsService.getDaysMap(org);
            expect(result).toEqual({
                0: 'sun',
                1: 'mon',
                2: 'tue',
                3: 'wed',
                4: 'thu',
                5: 'fri',
                6: 'sat'
            });
        });
    });

    describe('getMaxBetweenStartDates', () => {
        it('should return the maximum date between the schedule start date and the provided start date', () => {
            const schedule = { scheduledDate: 1689570000000 }; // July 17, 2023
            const startDate = new moment.tz('07/10/2023', 'MM/DD/YYYY', 'America/Chicago');
            const timezone = 'America/Chicago';

            const result = EnrollmentsService.getMaxBetweenStartDates(schedule, startDate, timezone);

            expect(result.format('MM/DD/YYYY')).toBe('07/17/2023');
        });
    });

    describe('getEndDateToUse', () => {
        it('should return the earliest end date between the schedule end date and the provided end date', () => {
            const schedule = { scheduledEndDate: 1690204800000 }; // July 24, 2023
            const endDateTimestamp = 1691125200000; // August 4, 2023

            const result = EnrollmentsService.getEndDateToUse(schedule, endDateTimestamp);

            expect(result).toBe(1690204800000);
        });

        it('should return the provided end date if the schedule has no end date', () => {
            const schedule = { scheduledEndDate: null };
            const endDateTimestamp = 1691125200000; // August 4, 2023

            const result = EnrollmentsService.getEndDateToUse(schedule, endDateTimestamp);

            expect(result).toBe(1691125200000);
        });
    });

    describe('setDefaultObjectProperties', () => {
        it('should initialize default properties for dateCounts and personDates', () => {
            const dateCounts = {};
            const personDates = {};
            const schedule = { scheduleType: 'type1', selectedPerson: 'person1' };

            EnrollmentsService.setDefaultObjectProperties(dateCounts, personDates, schedule);

            expect(dateCounts).toEqual({ type1: {} });
            expect(personDates).toEqual({ person1: { type1: {} } });
        });
    });

    describe('loopDaysAndUpdateCounts', () => {
        it('should loop through days and update counts for recurring schedules', () => {
            const dateCounts = {};
            const personDates = {};
            const schedule = {
                scheduleType: 'type1',
                selectedPerson: 'person1',
                recurringDays: ['mon', 'wed']
            };
            const scheduleDate = new moment.tz('07/17/2023', 'MM/DD/YYYY', 'America/Chicago'); // Monday
            const tempEndDateTimestamp = 1691125200000; // August 4, 2023
            const dayMap = { 1: 'mon', 3: 'wed' };

            EnrollmentsService.setDefaultObjectProperties(dateCounts, personDates, schedule);
            EnrollmentsService.loopDaysAndUpdateCounts(dateCounts, personDates, schedule, scheduleDate, tempEndDateTimestamp, schedule.recurringDays, dayMap);

            expect(dateCounts).toEqual({
                type1: {
                    '07/17/2023': 1,
                    '07/19/2023': 1,
                    '07/24/2023': 1,
                    '07/26/2023': 1,
                    '07/31/2023': 1,
                    '08/02/2023': 1
                }
            });
        });
        it('BUGS-3020: should not increment schedule days if looping a single-installment schedule when the recurring record was counted first', () => {
            const dateCounts = {};
            const personDates = {};
            const schedule = { // Recurring schedule
                _id: 'schedule_1',
                scheduleType: 'type1',
                selectedPerson: 'person1',
                recurringDays: ['thu']
            };
            const systemGeneratedSchedule = { // System generated single day schedule
                _id: 'schedule_2',
                scheduleType: 'type1',
                selectedPerson: 'person1'
            };

            const scheduleArray = [schedule, systemGeneratedSchedule];

            const tempEndDateTimestamp = 1742014800000;
            let recurringDays;
            const dayMap = { 0: 'sun', 1: 'mon', 2: 'tue', 3: 'wed', 4: 'thu', 5: 'fri', 6: 'sat' };

            scheduleArray.forEach(schedule => {
                recurringDays = schedule.recurringDays ?? [];
                const scheduleDate = new moment.tz('03/13/2025', 'MM/DD/YYYY', 'America/Chicago');
                EnrollmentsService.setDefaultObjectProperties(dateCounts, personDates, schedule);
                EnrollmentsService.loopDaysAndUpdateCounts(dateCounts, personDates, schedule, scheduleDate, tempEndDateTimestamp, recurringDays, dayMap);
            })

            expect(dateCounts).not.toEqual({ // What was happening before
                type1: {
                    '03/13/2025': 1,
                    '03/14/2025': 1 // Single day schedule was looping to  the next day and counting there instead.
                }
            });

            expect(dateCounts).toEqual({
                type1: {
                    '03/13/2025': 1, // The expected behavior
                }
            });
        })
    });

    describe('finalizeAvailabilities', () => {
        it('should calculate final availabilities based on date counts (weekdays only)', () => {
            const dateCounts = {
                type1: { '07/17/2023': 2, '07/19/2023': 1 } // Monday and Wednesday
            };
            const scheduleTypes = [
                { _id: 'type1', maxEnrollment: { 1: 5, 3: 3 } }
            ];
            const org = { getTimezone: () => 'America/Chicago', hasCustomization: () => false };

            const result = EnrollmentsService.finalizeAvailabilities(dateCounts, scheduleTypes, org);

            expect(result).toEqual({
                type1: {
                    1: 3, // Monday: 5 max - 2 used
                    2: 999999, // Default placeholder
                    3: 2, // Wednesday: 3 max - 1 used
                    4: 999999, // Default placeholder
                    5: 999999 // Default placeholder
                }
            });
        });

        it('should calculate availabilities with weekends enabled', () => {
            const dateCounts = {
                type1: { '07/22/2023': 3, '07/23/2023': 1 } // Saturday and Sunday
            };
            const scheduleTypes = [
                { _id: 'type1', maxEnrollment: { 6: 5, 0: 4 } } // Saturday and Sunday capacities
            ];
            const org = {
                getTimezone: () => 'America/Chicago',
                hasCustomization: (key) => key === AvailableCustomizations.WEEKENDS_ENABLED // Enable weekends
            };

            const result = EnrollmentsService.finalizeAvailabilities(dateCounts, scheduleTypes, org);

            expect(result).toEqual({
                type1: {
                    0: 3,  // Sunday: 4 max - 1 used
                    1: 999999, // Default placeholder
                    2: 999999, // Default placeholder
                    3: 999999, // Default placeholder
                    4: 999999, // Default placeholder
                    5: 999999, // Default placeholder
                    6: 2 // Saturday: 5 max - 3 used
                }
            });
        });

        it('should handle mixed weekday and weekend logic', () => {
            const dateCounts = {
                type1: {
                    '07/17/2023': 2, // Monday
                    '07/19/2023': 1, // Wednesday
                    '07/22/2023': 3, // Saturday
                    '07/23/2023': 1  // Sunday
                }
            };
            const scheduleTypes = [
                {
                    _id: 'type1',
                    maxEnrollment: { 0: 4, 1: 5, 3: 3, 6: 5 } // Capacities for Mon, Wed, Sat, Sun
                }
            ];
            const org = {
                getTimezone: () => 'America/Chicago',
                hasCustomization: (key) => key === AvailableCustomizations.WEEKENDS_ENABLED // Enable weekends
            };

            const result = EnrollmentsService.finalizeAvailabilities(dateCounts, scheduleTypes, org);

            expect(result).toEqual({
                type1: {
                    0: 3,  // Sunday: 4 max - 1 used
                    1: 3, // Monday: 5 max - 2 used
                    2: 999999, // Default placeholder
                    3: 2, // Wednesday: 3 max - 1 used
                    4: 999999, // Default placeholder
                    5: 999999, // Default placeholder
                    6: 2 // Saturday: 5 max - 3 used
                }
            });
        });

        it('should handle missing maxEnrollment for weekends', () => {
            const dateCounts = {
                type1: { '07/22/2023': 2, '07/23/2023': 1 } // Saturday and Sunday
            };
            const scheduleTypes = [
                { _id: 'type1', maxEnrollment: { 1: 5, 3: 3 } } // Only weekdays specified
            ];
            const org = {
                getTimezone: () => 'America/Chicago',
                hasCustomization: (key) => key === AvailableCustomizations.WEEKENDS_ENABLED // Enable weekends
            };

            const result = EnrollmentsService.finalizeAvailabilities(dateCounts, scheduleTypes, org);

            expect(result).toEqual({
                type1: {
                    0: 999998,  // Default placeholder for Sunday - 1 used
                    1: 5,
                    2: 999999, // Default placeholder
                    3: 3,
                    4: 999999, // Default placeholder
                    5: 999999, // Default placeholder
                    6: 999997 // Default placeholder for Saturday - 2 used
                }
            });
        });

        it('should handle no date counts gracefully', () => {
            const dateCounts = {};
            const scheduleTypes = [
                { _id: 'type1', maxEnrollment: { 0: 4, 1: 5, 3: 3, 6: 5 } }
            ];
            const org = {
                getTimezone: () => 'America/Chicago',
                hasCustomization: (key) => key === AvailableCustomizations.WEEKENDS_ENABLED // Enable weekends
            };

            const result = EnrollmentsService.finalizeAvailabilities(dateCounts, scheduleTypes, org);

            expect(result).toEqual({
                type1: {
                    0: 4,  // Sunday: maxEnrollment
                    1: 5, // Monday: maxEnrollment
                    2: 999999, // Default placeholder
                    3: 3, // Wednesday: maxEnrollment
                    4: 999999, // Default placeholder
                    5: 999999, // Default placeholder
                    6: 5 // Saturday: maxEnrollment
                }
            });
        });
    });

    describe('splitSelectiveWeekPlanIntoPlansArray', () => {
        it('should take a selective week plan and split it into an array of plans for each week', () => {
            const selectiveWeekPlan = {
                _id: 'Rq6srzm6PH6359y6d',
                description: 'Year Weeks',
                type: 'plan',
                program: 'wDRo5Lia3bLaYYhsD',
                frequency: 'weekly',
                category: 'tuition',
                amount: 10,
                scaledAmounts: [],
                ledgerAccountName: '654654',
                details: {
                    startTime: '6:00 am',
                    endTime: '6:00 pm',
                    regStartDate: *************,
                    regEndDate: *************,
                    grades: [ 'Preschool', 'K', '1', '2', '3' ],
                    dateType: 'timePeriod',
                    timePeriod: 'FioymGRrXaDXXZqHR',
                    selectiveWeeks: [
                        ['01/01/2024', '01/05/2024'], ['01/08/2024', '01/12/2024'], ['01/15/2024', '01/19/2024'], ['01/22/2024', '01/26/2024']
                    ]
                },
                serviceDates: {
                    response: '09/02/2023-03/01/2024',
                    startDate: '09/02/2023',
                    endDate: '03/01/2024'
                },
                selectedWeeks: [ 0, 1, 2 ]
            }
            const results = EnrollmentsService.splitSelectiveWeekPlanIntoPlansArray(selectiveWeekPlan);
            expect(results.length).toBe(3);
            results.forEach((result, index) => {
                expect(result.selectedDays).toStrictEqual(['mon', 'tue', 'wed', 'thu', 'fri']);
                expect(result.startDate).toStrictEqual(result.details.selectiveWeeks[result.selectedWeeks[index]][0]);
                expect(result.endDate).toStrictEqual(result.details.selectiveWeeks[result.selectedWeeks[index]][1]);
            });
        });
    });

    describe('mapSelectiveWeekPlansToReservationData', () => {
        it('should take a selective week plan and map it to the correct reservation data', () => {
            const selectiveWeekPlans = [{
                _id: 'Rq6srzm6PH6359y6d',
                description: 'Year Weeks',
                type: 'plan',
                program: 'wDRo5Lia3bLaYYhsD',
                frequency: 'weekly',
                category: 'tuition',
                amount: 10,
                scaledAmounts: [],
                ledgerAccountName: '654654',
                details: {
                    startTime: '6:00 am',
                    endTime: '6:00 pm',
                    regStartDate: *************,
                    regEndDate: *************,
                    grades: [ 'Preschool', 'K', '1', '2', '3' ],
                    dateType: 'timePeriod',
                    timePeriod: 'FioymGRrXaDXXZqHR',
                    selectiveWeeks: [
                        ['01/01/2024', '01/05/2024'], ['01/08/2024', '01/12/2024'], ['01/15/2024', '01/19/2024'], ['01/22/2024', '01/26/2024']
                    ]
                },
                serviceDates: {
                    response: '09/02/2023-03/01/2024',
                    startDate: '09/02/2023',
                    endDate: '03/01/2024'
                },
                selectedWeeks: [ 0, 1, 2 ],
                startDate: '01/01/2024',
                endDate: '01/26/2024',
                selectedDays: ['mon', 'tue', 'wed', 'thu', 'fri']
            }];
            const result = EnrollmentsService.mapSelectiveWeekPlansToReservationData(selectiveWeekPlans, 'America/Chicago', '123456');
            const expected = [
                {
                    selectedPerson: [ '123456' ],
                    reservationType: 'person',
                    groupId: 'default',
                    scheduledDate: 1704088800000,
                    scheduledEndDate: 1706248800000,
                    overrideOverlap: true,
                    scheduleType: '',
                    recurringFrequency: 1,
                    recurringDays: [ 'mon', 'tue', 'wed', 'thu', 'fri' ],
                    scheduledTime: '6:00 am',
                    scheduledEndTime: '6:00 pm',
                    linkedPlan: {
                        plan: 'Rq6srzm6PH6359y6d',
                        allocations: [],
                        enrollment_date: '01/01/2024',
                        expiration_date: '01/26/2024',
                        incrementCreatedAt: 0,
                        enrollment_forecast_start: '01/01/2024'
                    },
                    generatedFromBillingCharge: "Rq6srzm6PH6359y6d"
                }
            ];
            expect(result).toStrictEqual(expected);
        });
    });

    describe('getMaxDailyEnrollmentsByScheduleType', () => {
        const AVAILABILITIES_PLACEHOLDER = 999999;

        it('should return null if orgScheduleTypes or scheduleTypeId is not provided', () => {
            const result1 = EnrollmentsService.getMaxDailyEnrollmentsByScheduleType(null, [], false);
            expect(result1).toBeNull();

            const result2 = EnrollmentsService.getMaxDailyEnrollmentsByScheduleType('someId', null, false);
            expect(result2).toBeNull();
        });

        it('should return max enrollments for each weekday if scheduleType is found and weekends are disabled', () => {
            const orgScheduleTypes = [
                {
                    _id: 'scheduleTypeId1',
                    maxEnrollment: {
                        1: 10,
                        2: 20,
                        3: 30,
                        4: 40,
                        5: 50
                    }
                }
            ];

            const result = EnrollmentsService.getMaxDailyEnrollmentsByScheduleType('scheduleTypeId1', orgScheduleTypes, false);
            expect(result).toEqual({
                1: 10,
                2: 20,
                3: 30,
                4: 40,
                5: 50
            });
        });

        it('should include weekend availability when orgHasWeekends is true', () => {
            const orgScheduleTypes = [
                {
                    _id: 'scheduleTypeId2',
                    maxEnrollment: {
                        1: 10,
                        2: 20,
                        3: 30,
                        4: 40,
                        5: 50,
                        6: 60,
                        0: 70
                    }
                }
            ];

            const result = EnrollmentsService.getMaxDailyEnrollmentsByScheduleType('scheduleTypeId2', orgScheduleTypes, true);
            expect(result).toEqual({
                1: 10,
                2: 20,
                3: 30,
                4: 40,
                5: 50,
                6: 60,
                0: 70
            });
        });

        it('should return placeholder values for weekends if maxEnrollment is not defined and orgHasWeekends is true', () => {
            const orgScheduleTypes = [
                {
                    _id: 'scheduleTypeId3',
                    maxEnrollment: {
                        1: 5,
                        2: 15,
                        3: 25,
                        4: 35,
                        5: 45
                    }
                }
            ];

            const result = EnrollmentsService.getMaxDailyEnrollmentsByScheduleType('scheduleTypeId3', orgScheduleTypes, true);
            expect(result).toEqual({
                1: 5,
                2: 15,
                3: 25,
                4: 35,
                5: 45,
                6: AVAILABILITIES_PLACEHOLDER,
                0: AVAILABILITIES_PLACEHOLDER
            });
        });

        it('should return placeholder values for all days if scheduleType is not found', () => {
            const orgScheduleTypes = [
                {
                    _id: 'scheduleTypeId4',
                    maxEnrollment: {
                        1: 10,
                        2: 20,
                        3: 30,
                        4: 40,
                        5: 50,
                        6: 60,
                        0: 70
                    }
                }
            ];

            const result = EnrollmentsService.getMaxDailyEnrollmentsByScheduleType('nonExistingId', orgScheduleTypes, true);
            expect(result).toEqual({
                1: AVAILABILITIES_PLACEHOLDER,
                2: AVAILABILITIES_PLACEHOLDER,
                3: AVAILABILITIES_PLACEHOLDER,
                4: AVAILABILITIES_PLACEHOLDER,
                5: AVAILABILITIES_PLACEHOLDER,
                6: AVAILABILITIES_PLACEHOLDER,
                0: AVAILABILITIES_PLACEHOLDER
            });
        });

        it('should handle the case where maxEnrollment is completely undefined', () => {
            const orgScheduleTypes = [
                {
                    _id: 'scheduleTypeId5'
                }
            ];

            const result = EnrollmentsService.getMaxDailyEnrollmentsByScheduleType('scheduleTypeId5', orgScheduleTypes, true);
            expect(result).toEqual({
                1: AVAILABILITIES_PLACEHOLDER,
                2: AVAILABILITIES_PLACEHOLDER,
                3: AVAILABILITIES_PLACEHOLDER,
                4: AVAILABILITIES_PLACEHOLDER,
                5: AVAILABILITIES_PLACEHOLDER,
                6: AVAILABILITIES_PLACEHOLDER,
                0: AVAILABILITIES_PLACEHOLDER
            });
        });
    });

    describe('getOperatingDays', () => {
        const timezone = 'America/New_York';

        it('should return correct operating days for dateRange', () => {
            const item = {
                details: {
                    dateType: 'dateRange',
                    serviceStartDate: moment.tz('2023-01-01', timezone).valueOf(),
                    serviceEndDate: moment.tz('2023-01-03', timezone).valueOf()
                }
            };

            const result = EnrollmentsService.getOperatingDays(item, timezone);
            expect(result).toEqual(['sun', 'mon', 'tue']);
        });

        it('should return correct operating days for individualDates', () => {
            const item = {
                details: {
                    dateType: 'individualDates',
                    individualDates: [
                        moment.tz('2023-01-01', timezone).valueOf(),
                        moment.tz('2023-01-05', timezone).valueOf()
                    ]
                }
            };

            const result = EnrollmentsService.getOperatingDays(item, timezone);
            expect(result).toEqual(['sun', 'thu']);
        });

        it('should return correct operating days for recurring', () => {
            const item = {
                details: {
                    dateType: 'recurring',
                    recurringStartDate: moment.tz('2023-01-01', timezone).valueOf(),
                    recurringFrequency: 1,
                    recurringOccurrences: 2,
                    recurringDays: ['mon', 'wed']
                }
            };

            const result = EnrollmentsService.getOperatingDays(item, timezone);
            expect(result).toEqual(['mon', 'wed']);
        });

        it('should handle empty or null details gracefully', () => {
            const item = {
                details: {
                    dateType: 'dateRange',
                    serviceStartDate: null,
                    serviceEndDate: null
                }
            };

            const result = EnrollmentsService.getOperatingDays(item, timezone);
            expect(result).toEqual([]);
        });

        it('should return an empty array if dateType is not recognized', () => {
            const item = {
                details: {
                    dateType: 'unknownType'
                }
            };

            const result = EnrollmentsService.getOperatingDays(item, timezone);
            expect(result).toEqual([]);
        });
    });

    describe('getScheduleTypeUsage', () => {
        let org;
        let orgFindOneMock;
        let reservationsFindMock;
        let peopleFindMock;

        beforeEach(() => {
            peopleFindMock = People.find;
            peopleFindMock.mockReturnValue({
                fetchAsync: () => [1, 2, 3]
            });
            reservationsFindMock = Reservations.find;
            reservationsFindMock.mockReturnValue({
                fetchAsync: () => [
                    { selectedPerson: 1, scheduledDate: moment.tz('2023-01-02', 'America/New_York').valueOf() },
                    { selectedPerson: 1, scheduledDate: moment.tz('2023-01-03', 'America/New_York').valueOf() },
                    { selectedPerson: 2, scheduledDate: moment.tz('2023-01-03', 'America/New_York').valueOf() },
                    { selectedPerson: 2, scheduledDate: moment.tz('2023-01-04', 'America/New_York').valueOf() },
                ]
            });
        });
        afterEach(() => {
            jest.clearAllMocks();
        });

        it('correctly calulates schedule usage for given days', async () => {
            const days1 = [
                moment.tz('2023-01-02', 'America/New_York').valueOf(),
                moment.tz('2023-01-03', 'America/New_York').valueOf(),
                moment.tz('2023-01-04', 'America/New_York').valueOf()
            ]
            expect(await EnrollmentsService.getScheduleTypeUsage('1', days1, 'America/New_York', 'foo')).toStrictEqual([1, 2, 1]);
            const days2 = [
                moment.tz('2023-01-04', 'America/New_York').valueOf(),
                moment.tz('2023-01-05', 'America/New_York').valueOf(),
                moment.tz('2023-01-06', 'America/New_York').valueOf()
            ]
            expect(await EnrollmentsService.getScheduleTypeUsage('1', days2, 'America/New_York', 'foo')).toStrictEqual([1, 0, 0]);
        });
    });

    describe('getItemAndSelectiveWeekAvailabilities', () => {
        let org;
        let orgFindOneMock;
        let invoicesFindMock;
        let reservationsFindMock;
        let peopleFindMock;

        beforeEach(() => {
            org = {
                _id: 'orgId',
                getTimezone: jest.fn().mockReturnValue('America/New_York'),
                hasCustomization: jest.fn().mockReturnValue(false),
                getScheduleTypes: jest.fn().mockReturnValue([
                    {
                        _id: 'scheduleTypeId1',
                        maxEnrollment: {
                            1: 5,
                            2: 5,
                            3: 5,
                            4: 5,
                            5: 5
                        }
                    },
                    {
                        _id: 'scheduleTypeId2',
                        maxEnrollment: {
                            1: 20,
                            2: 20,
                            3: 20,
                            4: 20,
                            5: 20
                        }
                    },
                    {
                        _id: 'scheduleTypeId3',
                        maxEnrollment: {}
                    },
                    {
                        _id: 'scheduleTypeId4',
                        maxEnrollment: {
                            1: 10,
                            2: 10,
                            3: 10,
                            4: 9,
                            5: 10
                        }
                    },
                    {
                        _id: 'scheduleTypeId5',
                        maxEnrollment: {
                            1: 1,
                            2: 2,
                            3: 2,
                            4: 2,
                            5: 2
                        }
                    }
                ])
            };

            orgFindOneMock = Orgs.findOneAsync;
            orgFindOneMock.mockReturnValue(org);


            invoicesFindMock = Invoices.find;
            invoicesFindMock.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=> [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
            });

            reservationsFindMock = Reservations.find;
            reservationsFindMock.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=> [{selectedPerson: 1}, {selectedPerson: 2}, {selectedPerson: 3}, {selectedPerson: 4}, {selectedPerson: 5}, {selectedPerson: 6}, {selectedPerson: 7}, {selectedPerson: 8}, {selectedPerson: 9}])
            });

            peopleFindMock = People.find;
            peopleFindMock.mockReturnValue({
                fetchAsync:jest.fn().mockImplementation(()=> ['1', '2', '3', '4', '5', '6', '7', '8', '9'])
            });
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should throw an error if orgId or itemsArray is not provided', async () => {
            await expect(EnrollmentsService.getItemAndSelectiveWeekAvailabilities(null, []))
                .rejects
                .toThrowError();
            expect(orgFindOneMock).not.toHaveBeenCalled();
            expect(invoicesFindMock).not.toHaveBeenCalled();

            await expect(EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', null))
                .rejects
                .toThrowError();
            expect(orgFindOneMock).not.toHaveBeenCalled();
            expect(invoicesFindMock).not.toHaveBeenCalled();

            await expect(EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', []))
                .rejects
                .toThrowError();
            expect(orgFindOneMock).not.toHaveBeenCalled();
            expect(invoicesFindMock).not.toHaveBeenCalled();
        });
        it('should throw an error if org is not found', async () => {
            orgFindOneMock.mockReturnValue(null);

            await expect(EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', [{ details: {} }]))
                .rejects
                .toThrowError();
        });
        it('should mark items at capacity if enrollment exceeds max enrollment', async () => {
            const itemsArray = [
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId5',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-04', 'America/New_York').valueOf(),
                        serviceEndDate: moment.tz('2023-01-06', 'America/New_York').valueOf()
                    }
                },
                {
                    _id: 'itemId2',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId5',
                        dateType: 'recurring',
                        recurringDays: ['mon', 'wed'],
                        recurringFrequency: 1,
                        recurringOccurrences: 2,
                        recurringStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf()
                    }
                }
            ];
            reservationsFindMock.mockReturnValue({
                fetchAsync: () => [
                    { selectedPerson: 1, scheduledDate: moment.tz('2023-01-02', 'America/New_York').valueOf() },
                    { selectedPerson: 1, scheduledDate: moment.tz('2023-01-03', 'America/New_York').valueOf() },
                    { selectedPerson: 2, scheduledDate: moment.tz('2023-01-03', 'America/New_York').valueOf() },
                    { selectedPerson: 2, scheduledDate: moment.tz('2023-01-04', 'America/New_York').valueOf() },
                ]
            });

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId5',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-04', 'America/New_York').valueOf(),
                        serviceEndDate: moment.tz('2023-01-06', 'America/New_York').valueOf()
                    },
                    atCapacity: false // we have existing reservations at max for monday and tuesday, but Jan 4-6 2023 is Wed - Fri
                },
                {
                    _id: 'itemId2',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId5',
                        dateType: 'recurring',
                        recurringDays: ['mon', 'wed'],
                        recurringFrequency: 1,
                        recurringOccurrences: 2,
                        recurringStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf()
                    },
                    atCapacity: true // the first monday collides with existing schedule cap
                }
            ]);
            itemsArray[0].details.serviceStartDate = moment.tz('2023-01-03', 'America/New_York').valueOf();
            itemsArray[1].details.recurringStartDate = moment.tz('2023-01-03', 'America/New_York').valueOf();
            const result2 = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result2).toEqual([
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId5',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-03', 'America/New_York').valueOf(),
                        serviceEndDate: moment.tz('2023-01-06', 'America/New_York').valueOf()
                    },
                    atCapacity: true // now we collide here too
                },
                {
                    _id: 'itemId2',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId5',
                        dateType: 'recurring',
                        recurringDays: ['mon', 'wed'],
                        recurringFrequency: 1,
                        recurringOccurrences: 2,
                        recurringStartDate: moment.tz('2023-01-03', 'America/New_York').valueOf()
                    },
                    atCapacity: false // but the later start date means we avoid the existing monday schedules
                }
            ]);
            const regData = {
                plans: [
                    [
                        {
                            _id: 'planId1',
                            type: 'item',
                            details: {
                                scheduleType: 'scheduleTypeId5',
                                dateType: 'dateRange',
                                serviceStartDate: moment.tz('2023-01-04', 'America/New_York').valueOf(),
                                serviceEndDate: moment.tz('2023-01-04', 'America/New_York').valueOf()
                            }
                        }
                    ]
                ]
            }
            const result3 = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray, regData);

            expect(result3).toEqual([
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId5',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-03', 'America/New_York').valueOf(),
                        serviceEndDate: moment.tz('2023-01-06', 'America/New_York').valueOf()
                    },
                    atCapacity: true // now we collide here too
                },
                {
                    _id: 'itemId2',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId5',
                        dateType: 'recurring',
                        recurringDays: ['mon', 'wed'],
                        recurringFrequency: 1,
                        recurringOccurrences: 2,
                        recurringStartDate: moment.tz('2023-01-03', 'America/New_York').valueOf()
                    },
                    atCapacity: true // the plan in the cart now fills up the wednesday
                }
            ]);
        });
        it('should not mark items at capacity if no max enrollments exist', async () => {
            const itemsArray = [
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId3',
                        dateType: 'recurring',
                        recurringDays: ['mon', 'thu']
                    }
                }
            ];

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId3',
                        dateType: 'recurring',
                        recurringDays: ['mon', 'thu']
                    },
                    atCapacity: false
                }
            ]);
        });
        it('should mark selective weeks at capacity if enrollment exceeds max enrollment', async () => {
            const itemsArray = [
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023'],
                            ['01/09/2023', '01/13/2023']
                        ]
                    }
                },
                {
                    _id: 'planId2',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId1',
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023'],
                            ['01/09/2023', '01/13/2023']
                        ]
                    }
                }
            ];

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023', true],
                            ['01/09/2023', '01/13/2023', true]
                        ]
                    }
                },
                {
                    _id: 'planId2',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId1',
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023', true],
                            ['01/09/2023', '01/13/2023', true]
                        ]
                    }
                }
            ]);
        });
        it('should not mark selective weeks at capacity if enrollment does not exceed max enrollment', async () => {
            reservationsFindMock.mockReturnValue({
                fetchAsync: () => [{selectedPerson: 1}, {selectedPerson: 2}, {selectedPerson: 3}]
            });

            const itemsArray = [
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId2',
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023'],
                            ['01/09/2023', '01/13/2023']
                        ]
                    }
                },
                {
                    _id: 'planId2',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId3',
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023'],
                            ['01/09/2023', '01/13/2023']
                        ]
                    }
                },
                {
                    _id: 'planId3',
                    type: 'plan',
                    details: {
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023'],
                            ['01/09/2023', '01/13/2023']
                        ]
                    }
                }
            ];

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId2',
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023', false],
                            ['01/09/2023', '01/13/2023', false]
                        ]
                    }
                },
                {
                    _id: 'planId2',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId3',
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023', false],
                            ['01/09/2023', '01/13/2023', false]
                        ]
                    }
                },
                {
                    _id: 'planId3',
                    type: 'plan',
                    details: {
                        selectiveWeeks: [
                            ['01/02/2023', '01/06/2023', false],
                            ['01/09/2023', '01/13/2023', false]
                        ]
                    }
                }
            ]);
        });
        it('should consider weekend capacities when orgHasWeekends is true', async () => {
            org.hasCustomization.mockReturnValue(true); // Enable weekends
            org.getScheduleTypes.mockReturnValueOnce([
                {
                    _id: 'scheduleTypeId2',
                    maxEnrollment: {
                        0: 5,
                        1: 99,
                        2: 99,
                        3: 99,
                        4: 99,
                        5: 99,
                        6: 5,
                    }
                }
            ])

            const itemsArray = [
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId2',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-07', 'America/New_York').valueOf(), // Saturday
                        serviceEndDate: moment.tz('2023-01-08', 'America/New_York').valueOf(),  // Sunday
                        excludeSaturdays: false,
                        excludeSundays: false
                    }
                }
            ];

            const usageMock = jest.spyOn(EnrollmentsService, 'getScheduleTypeUsage');
            usageMock.mockResolvedValueOnce([4,4]);

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId2',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-07', 'America/New_York').valueOf(),
                        serviceEndDate: moment.tz('2023-01-08', 'America/New_York').valueOf(),
                        excludeSaturdays: false,
                        excludeSundays: false
                    },
                    atCapacity: false // Weekend capacities are considered, and not at capacity
                }
            ]);
        });
        it('should exclude weekends when orgHasWeekends is false', async () => {
            org.hasCustomization.mockReturnValue(false); // Disable weekends
            const itemsArray = [
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId2',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-07', 'America/New_York').valueOf(), // Saturday
                        serviceEndDate: moment.tz('2023-01-08', 'America/New_York').valueOf(),  // Sunday
                        excludeSaturdays: false,
                        excludeSundays: false
                    }
                }
            ];

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId2',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-07', 'America/New_York').valueOf(),
                        serviceEndDate: moment.tz('2023-01-08', 'America/New_York').valueOf(),
                        excludeSaturdays: false,
                        excludeSundays: false
                    },
                    atCapacity: false // Weekends are ignored, and no capacity issues are detected
                }
            ]);
        });
        it('should mark atCapacity as true if weekend capacities are exceeded when orgHasWeekends is true', async () => {
            org.hasCustomization.mockReturnValue(true); // Enable weekends
            org.getScheduleTypes.mockReturnValueOnce([
                {
                    _id: 'scheduleTypeId2',
                    maxEnrollment: {
                        0: 5,
                        1: 99,
                        2: 99,
                        3: 99,
                        4: 99,
                        5: 99,
                        6: 5,
                    }
                }
            ])
            const itemsArray = [
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId2',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-07', 'America/New_York').valueOf(), // Saturday
                        serviceEndDate: moment.tz('2023-01-08', 'America/New_York').valueOf(),  // Sunday
                        excludeSaturdays: false,
                        excludeSundays: false
                    }
                }
            ];

            const usageMock = jest.spyOn(EnrollmentsService, 'getScheduleTypeUsage');
            usageMock.mockResolvedValueOnce([5,5]);

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'itemId1',
                    type: 'item',
                    details: {
                        scheduleType: 'scheduleTypeId2',
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-07', 'America/New_York').valueOf(),
                        serviceEndDate: moment.tz('2023-01-08', 'America/New_York').valueOf(),
                        excludeSaturdays: false,
                        excludeSundays: false
                    },
                    atCapacity: true // Weekend capacity exceeded
                }
            ]);
        });
        it('should respect full week capacities when any day within the range is at capacity', async () => {
            org.hasCustomization.mockReturnValue(true); // Enable weekends
            org.getScheduleTypes.mockReturnValueOnce([
                {
                    _id: 'scheduleTypeId4',
                    maxEnrollment: {
                        1: 20,
                        2: 20,
                        3: 20,
                        4: 20,
                        5: 20,
                        6: 10, // Saturday is full so entire week gets flagged as full.
                        0: 20
                    }
                }
            ])
            const itemsArray = [
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/26/2025', '02/01/2025'] // Full week: Saturday to Friday
                        ]
                    }
                }
            ];

            const mockChildCount = jest.spyOn(EnrollmentsService, 'getChildrenWithSchedule');
            mockChildCount.mockResolvedValueOnce(10);

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/26/2025', '02/01/2025', true] // Entire week is marked at capacity
                        ]
                    }
                }
            ]);
        });
        it('even if a weekend is full, it should not flag as full is enabled weekends is false', async () => {
            org.hasCustomization.mockReturnValue(false); // Enable weekends
            org.getScheduleTypes.mockReturnValueOnce([
                {
                    _id: 'scheduleTypeId4',
                    maxEnrollment: {
                        1: 20,
                        2: 20,
                        3: 20,
                        4: 20,
                        5: 20,
                        6: 0, // Saturday is full so entire week gets flagged as full.
                        0: 0
                    }
                }
            ])
            const itemsArray = [
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/26/2025', '02/01/2025'] // Full week: Saturday to Friday
                        ]
                    }
                }
            ];

            const mockChildCount = jest.spyOn(EnrollmentsService, 'getChildrenWithSchedule');
            mockChildCount.mockResolvedValueOnce(10);

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/26/2025', '02/01/2025', false] // Entire week is marked at capacity
                        ]
                    }
                }
            ]);
        });
        it('should correctly handle partial week selective plans that include weekends when orgHasWeekends is true', async () => {
            org.hasCustomization.mockReturnValue(true); // Enable weekends
            org.getScheduleTypes.mockReturnValueOnce([
                {
                    _id: 'scheduleTypeId4',
                    maxEnrollment: {
                        0: 10,
                        1: 10,
                        2: 99,
                        3: 99,
                        4: 99,
                        5: 99,
                        6: 99
                    }
                }
            ]);

            const itemsArray = [
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/05/2025', '01/06/2025'] // Sunday, Monday
                        ]
                    }
                }
            ];

            const mockChildCount = jest.spyOn(EnrollmentsService, 'getChildrenWithSchedule');
            mockChildCount.mockResolvedValueOnce(10);

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/05/2025', '01/06/2025', true] // Entire week marked at capacity because monday and sunday are
                        ]
                    }
                }
            ]);
        });
        it('should correctly handle partial week selective plans with weekends excluded when orgHasWeekends is false', async () => {
            org.hasCustomization.mockReturnValue(false); // Disable weekends
            org.getScheduleTypes.mockReturnValueOnce([
                {
                    _id: 'scheduleTypeId4',
                    maxEnrollment: {
                        0: 15,
                        1: 15,
                        2: 15,
                        3: 20,
                        4: 20,
                        5: 20,
                        6: 20
                    }
                }
            ]);

            const itemsArray = [
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/08/2025', '01/11/2025'] // Partial week: Wednesday to Saturday
                        ]
                    }
                }
            ];

            const mockChildCount = jest.spyOn(EnrollmentsService, 'getChildrenWithSchedule');
            mockChildCount.mockResolvedValueOnce(15);

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/08/2025', '01/11/2025', false] // Week not at capacity as days not within range are ignored (Sunday, Monday, Tuesday)
                        ]
                    }
                }
            ]);
        });
        it('should correctly handle partial week selective plans with weekends when orgHasWeekends is true but schedule type is missing weekends', async () => {
            org.hasCustomization.mockReturnValue(false); // Disable weekends
            org.getScheduleTypes.mockReturnValueOnce([
                {
                    _id: 'scheduleTypeId4',
                    maxEnrollment: {
                        1: 15,
                        2: 15,
                        3: 20,
                        4: 20,
                        5: 20
                    }
                }
            ]);

            const itemsArray = [
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/08/2025', '01/11/2025'] // Partial week: Wednesday to Saturday
                        ]
                    }
                }
            ];

            const mockChildCount = jest.spyOn(EnrollmentsService, 'getChildrenWithSchedule');
            mockChildCount.mockResolvedValueOnce(15);

            const result = await EnrollmentsService.getItemAndSelectiveWeekAvailabilities('orgId', itemsArray);

            expect(result).toEqual([
                {
                    _id: 'planId1',
                    type: 'plan',
                    details: {
                        scheduleType: 'scheduleTypeId4',
                        selectiveWeeks: [
                            ['01/08/2025', '01/11/2025', false] // Week not at capacity as days not within range are ignored, and Saturday uses placeholder of 9999999
                        ]
                    }
                }
            ]);
        });
    });

    describe('getPlanAvailabilities', () => {
        const timezone = 'America/New_York';

        it('should do nothing if curWeek is not provided', () => {
            const curWeek = null;
            const maxDailyEnrollments = { 1: 10, 2: 10, 3: 10, 4: 10, 5: 10 };
            const childCount = 5;

            EnrollmentsService.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            expect(curWeek).toBeNull();
        });

        it('should add false to curWeek if child count is less than max enrollments for all days in the range', () => {
            const curWeek = ['02/26/2025', '02/28/2025']; // Wednesday to Friday
            const maxDailyEnrollments = { 3: 10, 4: 10, 5: 10 }; // Wednesday, Thursday, Friday
            const childCount = 5;

            EnrollmentsService.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            expect(curWeek).toEqual(['02/26/2025', '02/28/2025', false]);
        });

        it('should add true to curWeek if child count equals the max enrollment for any day in the range', () => {
            const curWeek = ['02/26/2025', '02/28/2025']; // Wednesday to Friday
            const maxDailyEnrollments = { 3: 5, 4: 10, 5: 10 }; // Wednesday, Thursday, Friday
            const childCount = 5;

            EnrollmentsService.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            expect(curWeek).toEqual(['02/26/2025', '02/28/2025', true]); // Wednesday at capacity
        });

        it('should add true to curWeek if child count exceeds the max enrollment for any day in the range', () => {
            const curWeek = ['02/26/2025', '02/28/2025']; // Wednesday to Friday
            const maxDailyEnrollments = { 3: 5, 4: 10, 5: 10 }; // Wednesday, Thursday, Friday
            const childCount = 6;

            EnrollmentsService.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            expect(curWeek).toEqual(['02/26/2025', '02/28/2025', true]); // Wednesday at capacity
        });

        it('should handle mixed enrollment capacities correctly', () => {
            const curWeek = ['02/26/2025', '02/28/2025']; // Wednesday to Friday
            const maxDailyEnrollments = { 3: 5, 4: 8, 5: 10 }; // Wednesday, Thursday, Friday
            const childCount = 7;

            EnrollmentsService.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            expect(curWeek).toEqual(['02/26/2025', '02/28/2025', true]); // Thursday at capacity
        });

        it('should work with exact match on multiple days', () => {
            const curWeek = ['02/26/2025', '02/28/2025']; // Wednesday to Friday
            const maxDailyEnrollments = { 3: 5, 4: 7, 5: 10 }; // Wednesday, Thursday, Friday
            const childCount = 7;

            EnrollmentsService.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            expect(curWeek).toEqual(['02/26/2025', '02/28/2025', true]); // Thursday at capacity
        });

        it('should return false if child count is less than max enrollments on all days in the range', () => {
            const curWeek = ['02/26/2025', '02/28/2025']; // Wednesday to Friday
            const maxDailyEnrollments = { 3: 10, 4: 10, 5: 10 }; // Wednesday, Thursday, Friday
            const childCount = 4;

            EnrollmentsService.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            expect(curWeek).toEqual(['02/26/2025', '02/28/2025', false]);
        });

        it('should handle missing maxDailyEnrollments values by defaulting to no limit', () => {
            const curWeek = ['02/26/2025', '02/28/2025']; // Wednesday to Friday
            const maxDailyEnrollments = {}; // No limits configured
            const childCount = 4;

            EnrollmentsService.getPlanAvailabilities(curWeek, maxDailyEnrollments, childCount, timezone);
            expect(curWeek).toEqual(['02/26/2025', '02/28/2025', false]); // No days at capacity
        });
    });

    describe('getStartEndDatesForWeek', () => {

        it('should convert start and end date strings to timestamps', () => {
            const weekStartDateString = '01/01/2023';
            const weekEndDateString = '01/05/2023';
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getStartEndDatesForWeek(weekStartDateString, weekEndDateString, timezone);

            expect(result).toEqual({
                startDate: moment.tz('01/01/2023', 'MM/DD/YYYY', timezone).startOf('day').valueOf(),
                endDate: moment.tz('01/05/2023', 'MM/DD/YYYY', timezone).startOf('day').valueOf()
            });
        });

        it('should handle invalid date strings gracefully', () => {
            const weekStartDateString = 'invalid-date';
            const weekEndDateString = 'invalid-date';
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getStartEndDatesForWeek(weekStartDateString, weekEndDateString, timezone);

            expect(result).toEqual({
                startDate: NaN,
                endDate: NaN
            });
        });
    });

    describe('getOperatingWeeks', () => {

        it('should return an array of start and end date objects for each week in the plan', () => {
            const plan = {
                details: {
                    selectiveWeeks: [
                        ['01/01/2023', '01/05/2023'],
                        ['01/08/2023', '01/12/2023']
                    ]
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getOperatingWeeks(plan, timezone);

            expect(result).toEqual([
                {
                    startDate: moment.tz('01/01/2023', 'MM/DD/YYYY', timezone).startOf('day').valueOf(),
                    endDate: moment.tz('01/05/2023', 'MM/DD/YYYY', timezone).startOf('day').valueOf()
                },
                {
                    startDate: moment.tz('01/08/2023', 'MM/DD/YYYY', timezone).startOf('day').valueOf(),
                    endDate: moment.tz('01/12/2023', 'MM/DD/YYYY', timezone).startOf('day').valueOf()
                }
            ]);
        });

        it('should return an empty array if no selective weeks are provided in the plan', () => {
            const plan = { details: { selectiveWeeks: [] } };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getOperatingWeeks(plan, timezone);

            expect(result).toEqual([]);
        });

        it('should handle missing selectiveWeeks property gracefully', () => {
            const plan = { details: {} };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getOperatingWeeks(plan, timezone);

            expect(result).toEqual([]);
        });

        it('should handle invalid date strings gracefully', () => {
            const plan = {
                details: {
                    selectiveWeeks: [
                        ['invalid-date', 'invalid-date']
                    ]
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getOperatingWeeks(plan, timezone);

            expect(result).toEqual([
                {
                    startDate: NaN,
                    endDate: NaN
                }
            ]);
        });
    });

    describe('getDefaultScheduleDataForItemCharges', () => {
        it('should return default schedule data for a plan with complete details', () => {
            const plan = {
                details: {
                    scheduleType: 'TypeA',
                    startTime: '09:00',
                    endTime: '17:00',
                }
            };
            const personId = 'person123';

            const result = EnrollmentsService.getDefaultScheduleDataForItemCharges(plan, personId);

            expect(result).toEqual({
                selectedPerson: [personId],
                reservationType: 'person',
                groupId: 'default',
                overrideOverlap: true,
                scheduleType: 'TypeA',
                scheduledTime: '09:00',
                scheduledEndTime: '17:00',
            });
        });

        it('should return default schedule data with empty fields if plan details are missing', () => {
            const plan = {};
            const personId = 'person123';

            const result = EnrollmentsService.getDefaultScheduleDataForItemCharges(plan, personId);

            expect(result).toEqual({
                selectedPerson: [personId],
                reservationType: 'person',
                groupId: 'default',
                overrideOverlap: true,
                scheduleType: '',
                scheduledTime: '',
                scheduledEndTime: '',
            });
        });
    });

    describe('isRecurringItem', () => {
        it('should return true if the plan is a recurring item', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.RECURRING,
                    recurringDays: ['Monday', 'Wednesday'],
                }
            };

            const result = EnrollmentsService.isRecurringItem(plan);

            expect(result).toBe(true);
        });

        it('should return false if the plan is not a recurring item', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.DATE_RANGE,
                    recurringDays: ['Monday', 'Wednesday'],
                }
            };

            const result = EnrollmentsService.isRecurringItem(plan);

            expect(result).toBe(false);
        });

        it('should return false if recurringDays is empty', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.RECURRING,
                    recurringDays: [],
                }
            };

            const result = EnrollmentsService.isRecurringItem(plan);

            expect(result).toBe(false);
        });
    });

    describe('isDateRangeItem', () => {
        it('should return true if the plan is a date range item', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.DATE_RANGE,
                    serviceStartDate: '2024-01-01',
                    serviceEndDate: '2024-12-31',
                }
            };

            const result = EnrollmentsService.isDateRangeItem(plan);

            expect(result).toBe(true);
        });

        it('should return false if the plan is not a date range item', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.RECURRING,
                    serviceStartDate: '2024-01-01',
                    serviceEndDate: '2024-12-31',
                }
            };

            const result = EnrollmentsService.isDateRangeItem(plan);

            expect(result).toBe(false);
        });

        it('should return false if start or end dates are missing', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.DATE_RANGE,
                    serviceStartDate: null,
                    serviceEndDate: '2024-12-31',
                }
            };

            const result = EnrollmentsService.isDateRangeItem(plan);

            expect(result).toBe(false);
        });
    });

    describe('isIndividualDatesItem', () => {
        it('should return true if the plan is an individual dates item', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.INDIVIDUAL_DATES,
                    individualDates: ['2024-01-01', '2024-01-02'],
                }
            };

            const result = EnrollmentsService.isIndividualDatesItem(plan);

            expect(result).toBe(true);
        });

        it('should return false if the plan is not an individual dates item', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.DATE_RANGE,
                    individualDates: ['2024-01-01', '2024-01-02'],
                }
            };

            const result = EnrollmentsService.isIndividualDatesItem(plan);

            expect(result).toBe(false);
        });

        it('should return false if individualDates is empty or not provided', () => {
            const plan = {
                details: {
                    dateType: ItemDateTypes.INDIVIDUAL_DATES,
                    individualDates: [],
                }
            };

            const result = EnrollmentsService.isIndividualDatesItem(plan);

            expect(result).toBe(false);
        });
    });

    describe('getItemDaysForRecurringDates', () => {
        it('should return recurring dates correctly', () => {
            const plan = {
                details: {
                    recurringStartDate: '2024-01-01', // Monday
                    recurringFrequency: 1,
                    recurringOccurrences: 2,
                    recurringDays: ['mon', 'wed']
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getItemDaysForRecurringDates(plan, timezone);
            expect(result).toHaveLength(4); // Expecting two weeks of Monday and Wednesday
            expect(result[0]).toBe(moment.tz('2024-01-01', timezone).valueOf()); // First Monday
            expect(result[1]).toBe(moment.tz('2024-01-03', timezone).valueOf()); // First Wednesday
            expect(result[2]).toBe(moment.tz('2024-01-08', timezone).valueOf()); // Second Monday
            expect(result[3]).toBe(moment.tz('2024-01-10', timezone).valueOf()); // Second Wednesday
        });

        it('should return an empty array if any recurring details are missing', () => {
            const plan = {
                details: {
                    recurringStartDate: null,
                    recurringFrequency: 1,
                    recurringOccurrences: 2,
                    recurringDays: ['mon']
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getItemDaysForRecurringDates(plan, timezone);
            expect(result).toEqual([]);
        });
    });

    describe('getItemDaysForDateRangeItem', () => {
        it('should return dates correctly within a range, excluding weekends', () => {
            const plan = {
                details: {
                    serviceStartDate: '2024-01-01',
                    serviceEndDate: '2024-01-07',
                    excludeSaturdays: true,
                    excludeSundays: true
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getItemDaysForDateRangeItem(plan, timezone);
            expect(result).not.toContain(moment.tz('2024-01-06', timezone).valueOf()); // Saturday
            expect(result).not.toContain(moment.tz('2024-01-07', timezone).valueOf()); // Sunday
        });

        it('should return dates including weekends if exclusions are not set', () => {
            const plan = {
                details: {
                    serviceStartDate: '2024-01-01',
                    serviceEndDate: '2024-01-07',
                    excludeSaturdays: false,
                    excludeSundays: false
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getItemDaysForDateRangeItem(plan, timezone);
            expect(result).toContain(moment.tz('2024-01-06', timezone).valueOf()); // Saturday
            expect(result).toContain(moment.tz('2024-01-07', timezone).valueOf()); // Sunday
        });

        it('should treat undefined exclusions as true exclusions for  those without weekend customizations', () => {
            const plan = {
                details: {
                    serviceStartDate: '2024-01-01',
                    serviceEndDate: '2024-01-07',
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getItemDaysForDateRangeItem(plan, timezone);
            expect(result).not.toContain(moment.tz('2024-01-06', timezone).valueOf()); // Saturday
            expect(result).not.toContain(moment.tz('2024-01-07', timezone).valueOf()); // Sunday
        });
    });

    describe('getItemDaysForIndividualDates', () => {
        it('should return individual dates correctly', () => {
            const plan = {
                details: {
                    individualDates: ['2024-01-01', '2024-01-02']
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getItemDaysForIndividualDates(plan, timezone);
            expect(result).toEqual([
                moment.tz('2024-01-01', timezone).valueOf(),
                moment.tz('2024-01-02', timezone).valueOf()
            ]);
        });

        it('should return an empty array if individualDates is not provided', () => {
            const plan = {
                details: {
                    individualDates: []
                }
            };
            const timezone = 'America/New_York';

            const result = EnrollmentsService.getItemDaysForIndividualDates(plan, timezone);
            expect(result).toEqual([]);
        });
    });

    describe('generateScheduleDataForItemCharges', () => {
        it('should generate schedule data for a date range item correctly', () => {
            const plan = {
                _id:"123",
                details: {
                    dateType: 'dateRange',
                    serviceStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf(),
                    serviceEndDate: moment.tz('2023-01-04', 'America/New_York').valueOf(),
                }
            };
            const personId = 'person123';
            const timezone = 'America/New_York';

            const result = EnrollmentsService.generateScheduleDataForItemCharges(plan, personId, timezone);
            expect(result).toHaveLength(3);
            expect(result).toEqual([
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-02', 'America/New_York').valueOf(),
                    generatedFromBillingCharge: "123"
                },
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-03', 'America/New_York').valueOf(),
                    generatedFromBillingCharge: "123"
                },
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-04', 'America/New_York').valueOf(),
                    generatedFromBillingCharge: "123"
                },
            ]);
        });

        it('should generate schedule data for individual dates item correctly', () => {
            const plan = {
                details: {
                    dateType: 'individualDates',
                    individualDates: [
                        moment.tz('2023-01-06', 'America/New_York').valueOf(),
                        moment.tz('2023-01-12', 'America/New_York').valueOf(),
                    ]
                }
            };
            const personId = 'person456';
            const timezone = 'America/New_York';

            const result = EnrollmentsService.generateScheduleDataForItemCharges(plan, personId, timezone);
            expect(result).toHaveLength(2);
            expect(result).toEqual([
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-06', 'America/New_York').valueOf(),
                },
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-12', 'America/New_York').valueOf(),
                },
            ]);
        });

        it('should generate schedule data for recurring dates item correctly', () => {
            const plan = {
                details: {
                    dateType: 'recurring',
                    recurringDays: ['mon', 'wed'],
                    recurringFrequency: 1,
                    recurringOccurrences: 2,
                    recurringStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf(), // Monday
                }
            };
            const personId = 'person789';
            const timezone = 'America/New_York';

            const result = EnrollmentsService.generateScheduleDataForItemCharges(plan, personId, timezone);
            expect(result).toHaveLength(4);
            expect(result).toEqual([
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-02', 'America/New_York').valueOf(), // First Monday
                },
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-04', 'America/New_York').valueOf(), // First Wednesday
                },
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-09', 'America/New_York').valueOf(), // Second Monday
                },
                {
                    selectedPerson: [personId],
                    reservationType: 'person',
                    groupId: 'default',
                    overrideOverlap: true,
                    scheduleType: '',
                    scheduledTime: '',
                    scheduledEndTime: '',
                    scheduledDate: moment.tz('2023-01-11', 'America/New_York').valueOf(), // Second Wednesday
                },
            ]);
        });

        it('should return an empty array if there are no schedule days', () => {
            const plan = {
                details: {
                    dateType: 'recurring',
                    recurringDays: [],
                    recurringFrequency: 1,
                    recurringOccurrences: 0,
                    recurringStartDate: null,
                }
            };
            const personId = 'personEmpty';
            const timezone = 'America/New_York';

            const result = EnrollmentsService.generateScheduleDataForItemCharges(plan, personId, timezone);
            expect(result).toStrictEqual([]);
        });
    });

    describe('generateItemSchedulesForChild', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should return null for any invalid dates', () => {
            expect(DateTimeUtils.getDatestampInTimezone('not a date', 'America/New_York', 'startOf')).toBeNull();
            expect(DateTimeUtils.getDatestampInTimezone('', 'America/New_York', 'startOf')).toBeNull();
            expect(DateTimeUtils.getDatestampInTimezone(null, 'America/New_York', 'startOf')).toBeNull();
            expect(DateTimeUtils.getDatestampInTimezone(undefined, 'America/New_York', 'startOf')).toBeNull();
        });

        it('should generate and insert reservations for date range items', async () => {
            const personId = 'person123';
            const items = [
                {
                    details: {
                        dateType: 'dateRange',
                        serviceStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf(),
                        serviceEndDate: moment.tz('2023-01-04', 'America/New_York').valueOf(),
                    },
                },
            ];
            const timezone = 'America/New_York';

            await EnrollmentsService.generateItemSchedulesForChild(personId, items, timezone);

            expect(Meteor.callAsync).toHaveBeenCalledTimes(3);
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-02', timezone).valueOf(),
            }));
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-03', timezone).valueOf(),
            }));
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-04', timezone).valueOf(),
            }));
        });

        it('should generate and insert reservations for individual dates items', async () => {
            const personId = 'person456';
            const items = [
                {
                    details: {
                        dateType: 'individualDates',
                        individualDates: [
                            moment.tz('2023-01-06', 'America/New_York').valueOf(),
                            moment.tz('2023-01-12', 'America/New_York').valueOf(),
                        ],
                    },
                },
            ];
            const timezone = 'America/New_York';

            await EnrollmentsService.generateItemSchedulesForChild(personId, items, timezone);

            expect(Meteor.callAsync).toHaveBeenCalledTimes(2);
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-06', timezone).valueOf(),
            }));
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-12', timezone).valueOf(),
            }));
        });

        it('should generate and insert reservations for recurring dates items', async () => {
            const personId = 'person789';
            const items = [
                {
                    details: {
                        dateType: 'recurring',
                        recurringDays: ['mon', 'wed'],
                        recurringFrequency: 1,
                        recurringOccurrences: 2,
                        recurringStartDate: moment.tz('2023-01-02', 'America/New_York').valueOf(), // Monday
                    },
                },
            ];
            const timezone = 'America/New_York';

            await EnrollmentsService.generateItemSchedulesForChild(personId, items, timezone);

            expect(Meteor.callAsync).toHaveBeenCalledTimes(4);
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-02', timezone).valueOf(), // First Monday
            }));
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-04', timezone).valueOf(), // First Wednesday
            }));
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-09', timezone).valueOf(), // Second Monday
            }));
            expect(Meteor.callAsync).toHaveBeenCalledWith('insertReservation', expect.objectContaining({
                scheduledDate: moment.tz('2023-01-11', timezone).valueOf(), // Second Wednesday
            }));
        });

        it('should not generate reservations if no items are provided', async () => {
            const personId = 'personEmpty';
            const items = [];
            const timezone = 'America/New_York';

            await EnrollmentsService.generateItemSchedulesForChild(personId, items, timezone);

            expect(Meteor.callAsync).not.toHaveBeenCalled();
        });

        it('should handle an unsupported date type gracefully without errors', async () => {
            const personId = 'personInvalid';
            const items = [
                {
                    details: {
                        dateType: 'unsupportedType',
                    },
                },
            ];
            const timezone = 'America/New_York';

            await EnrollmentsService.generateItemSchedulesForChild(personId, items, timezone);

            expect(Meteor.callAsync).not.toHaveBeenCalled();
        });
    });

    describe('insertUpdateScheduleType', () => {
        let orgUpdateAsyncMock;

        beforeEach(() => {
            jest.clearAllMocks();
            orgUpdateAsyncMock = Orgs.updateAsync;
        });

        it('should update an existing schedule type', async () => {
            const options = {
                existingId: 'schedule1',
                scheduleType: 'Updated Schedule',
                startTime: '09:00 AM',
                endTime: '12:00 PM',
                maxEnrollment: { 1: 5, 2: 5 },
                fteCount: 1.0,
                hideInForecasting: false,
                defaultGroupId: 'group1',
                scheduleTypeTimeBlock: 'scheduled',
                currentUser: { orgId: 'org123' },
            };

            await EnrollmentsService.insertUpdateScheduleType(options);

            expect(orgUpdateAsyncMock).toHaveBeenCalledWith(
                { _id: 'org123', 'valueOverrides.scheduleTypes._id': 'schedule1' },
                {
                    $set: {
                        'valueOverrides.scheduleTypes.$.type': 'Updated Schedule',
                        'valueOverrides.scheduleTypes.$.startTime': '09:00 AM',
                        'valueOverrides.scheduleTypes.$.endTime': '12:00 PM',
                        'valueOverrides.scheduleTypes.$.maxEnrollment': { 1: 5, 2: 5 },
                        'valueOverrides.scheduleTypes.$.fteCount': 1.0,
                        'valueOverrides.scheduleTypes.$.hideInForecasting': false,
                        'valueOverrides.scheduleTypes.$.defaultGroupId': 'group1',
                    },
                }
            );
        });

        it('should insert a new schedule type', async () => {
            const options = {
                scheduleType: 'New Schedule',
                startTime: '09:00 AM',
                endTime: '12:00 PM',
                maxEnrollment: { 1: 5, 2: 5 },
                fteCount: 1.0,
                hideInForecasting: true,
                defaultGroupId: 'group1',
                scheduleTypeTimeBlock: 'scheduled',
                currentUser: { orgId: 'org123' },
                randomId: "id1"
            };

            await EnrollmentsService.insertUpdateScheduleType(options);

            expect(orgUpdateAsyncMock).toHaveBeenCalledWith(
                { _id: 'org123' },
                {
                    $addToSet: {
                        'valueOverrides.scheduleTypes': {
                            _id: 'id1',
                            type: 'New Schedule',
                            startTime: '09:00 AM',
                            endTime: '12:00 PM',
                            maxEnrollment: { 1: 5, 2: 5 },
                            fteCount: 1.0,
                            hideInForecasting: true,
                            defaultGroupId: 'group1',
                        },
                    },
                }
            );
        });

        it('should throw a Meteor.Error if the update fails', async () => {
            orgUpdateAsyncMock.mockRejectedValue(new Error('Update failed'));

            const options = {
                existingId: 'schedule1',
                scheduleType: 'Updated Schedule',
                currentUser: { orgId: 'org123' },
            };

            await expect(EnrollmentsService.insertUpdateScheduleType(options)).rejects.toThrowError(
                new Meteor.Error( '500,Update failed')
            );
        });
    });

    describe('removeScheduleType', () => {
    
        beforeEach(() => {
            jest.restoreAllMocks();
        });
    
        it('should remove a schedule type successfully', async () => {
            const org = { _id: 'org123', valueOverrides: { scheduleTypes: [{ _id: 'schedule1' }, { _id: 'schedule2' }] } };
            jest.spyOn(OrgsLib, 'getScheduleTypes').mockReturnValue([{ _id: 'schedule1' }, { _id: 'schedule2' }]);
            Orgs.updateAsync.mockResolvedValue({"_id": 'org123'});
            await EnrollmentsService.removeScheduleType('schedule1', org);
    
            expect(Orgs.updateAsync).toHaveBeenCalledWith(
                { _id: 'org123' },
                {
                    $set: {
                        'valueOverrides.scheduleTypes': [{ _id: 'schedule2' }],
                    },
                }
            );
        });
    
        it('should throw an error if organization is missing', async () => {
            await expect(EnrollmentsService.removeScheduleType('schedule1', null)).rejects.toThrowError(
                new Meteor.Error(500, 'Organization not found')
            );
        });
    
        it('should throw an error if schedule type ID is missing', async () => {
            const org = { _id: 'org123' };
    
            await expect(EnrollmentsService.removeScheduleType(null, org)).rejects.toThrowError(
                new Meteor.Error(500, 'Schedule type ID is required')
            );
        });
    
        it('should throw a Meteor.Error if the removal fails', async () => {
            Orgs.updateAsync.mockRejectedValue(new Error('Removal failed'));
    
            const org = { _id: 'org123', valueOverrides: { scheduleTypes: [{ _id: 'schedule1' }] } };
            jest.spyOn(OrgsLib, 'getScheduleTypes').mockReturnValue([{ _id: 'schedule1' }]);
    
            await expect(EnrollmentsService.removeScheduleType('schedule1', org)).rejects.toThrowError(
                new Meteor.Error(',Removal failed')
            );
        });
    });

    describe('getHolidaysByDayForRangedEnrollmentData', () => {
        let mockTimezone;
        let mockScheduleA;
        let mockScheduleB;
        let makeOrg;

        beforeEach(() => {
            mockTimezone = 'America/Chicago';
            mockScheduleA = 'schedA';
            mockScheduleB = 'schedB';

            makeOrg = (holidays) => ({
                getHolidays: () => holidays
            });
        });

        it('returns empty object when no holidays fall within range', () => {
            const org = makeOrg([]);
            const result = EnrollmentsService.getHolidaysByDayForRangedEnrollmentData(org, '06/02/2025', '06/06/2025', mockTimezone);
            expect(result).toEqual({});
        });

        it('maps individual holiday to correct day', () => {
            const org = makeOrg([
                { name: 'Holiday A', date: '2025-06-03', dateType: 'individual', scheduleTypes: [mockScheduleA] }
            ]);

            const result = EnrollmentsService.getHolidaysByDayForRangedEnrollmentData(org, '06/02/2025', '06/06/2025', mockTimezone);
            expect(result).toHaveProperty('T'); // Tuesday
            expect(result.T).toEqual({
                name: 'Holiday A',
                permittedScheduleTypes: [mockScheduleA]
            });
        });

        it('handles range-based holiday that spans multiple days', () => {
            const org = makeOrg([
                { name: 'Holiday B', dateType: 'range', startDate: '2025-06-04', endDate: '2025-06-05', scheduleTypes: [mockScheduleB] }
            ]);

            const result = EnrollmentsService.getHolidaysByDayForRangedEnrollmentData(org, '06/02/2025', '06/06/2025', mockTimezone);
            expect(result.W).toEqual({ name: 'Holiday B', permittedScheduleTypes: [mockScheduleB] });
            expect(result.R).toEqual({ name: 'Holiday B', permittedScheduleTypes: [mockScheduleB] });
        });

        it('returns "None" when scheduleTypes is empty or missing', () => {
            const org = makeOrg([
                { name: 'Closed Day', date: '2025-06-06' } // no scheduleTypes, legacy
            ]);

            const result = EnrollmentsService.getHolidaysByDayForRangedEnrollmentData(org, '06/02/2025', '06/06/2025', mockTimezone);
            expect(result.F).toEqual({ name: 'Closed Day', permittedScheduleTypes: ['None'] });
        });

        it('skips deleted holidays', () => {
            const org = makeOrg([
                { name: 'Old Holiday', date: '2025-06-04', deleted: true, scheduleTypes: ['All'] }
            ]);

            const result = EnrollmentsService.getHolidaysByDayForRangedEnrollmentData(org, '06/02/2025', '06/06/2025', mockTimezone);
            expect(result).not.toHaveProperty('W');
        });

        it('treats "All" and "None" as literals', () => {
            const org = makeOrg([
                { name: 'All Day', date: '2025-06-02', dateType: 'individual', scheduleTypes: ['All'] },
                { name: 'None Day', date: '2025-06-03', dateType: 'individual', scheduleTypes: ['None'] }
            ]);

            const result = EnrollmentsService.getHolidaysByDayForRangedEnrollmentData(org, '06/02/2025', '06/06/2025', mockTimezone);

            expect(result.M).toEqual({ name: 'All Day', permittedScheduleTypes: ['All'] });
            expect(result.T).toEqual({ name: 'None Day', permittedScheduleTypes: ['None'] });
        });
    });

    describe('processReservationsForRangedEnrollmentData', () => {
        it('should update reservationsCount and stats for a valid reservation and schedule type', () => {
            const reservation = {
                scheduledDate: '2025-01-07',
                scheduleType: 'scheduleType1',
            };

            const rangeReservationData = {
                scheduleTypes: {
                    scheduleType1: {
                        reservationsCount: 0,
                        stats: [
                            { abbr: 'Su', coming: 0, cap: 5, date: '' },
                            { abbr: 'M', coming: 0, cap: 10, date: '' },
                            { abbr: 'T', coming: 0, cap: 10, date: '' },
                            { abbr: 'W', coming: 0, cap: 10, date: '' },
                            { abbr: 'R', coming: 0, cap: 10, date: '' },
                            { abbr: 'F', coming: 0, cap: 10, date: '' },
                            { abbr: 'Sa', coming: 0, cap: 10, date: '' },
                        ],
                    },
                },
            };

            const timezone = 'America/New_York';

            EnrollmentsService.processReservationsForRangedEnrollmentData(reservation, rangeReservationData, timezone);

            expect(rangeReservationData.scheduleTypes.scheduleType1.reservationsCount).toBe(1);
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].coming).toBe(1); // Tuesday
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].date).toBe('01/07/2025');
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].cap).toBe(10);
        });

        it('should not update anything if scheduleType is not found', () => {
            const reservation = {
                scheduledDate: '2025-01-07',
                scheduleType: 'invalidScheduleType',
            };

            const rangeReservationData = {
                scheduleTypes: {
                    scheduleType1: {
                        reservationsCount: 0,
                        stats: [
                            { abbr: 'Su', coming: 0, cap: 5, date: '' },
                            { abbr: 'M', coming: 0, cap: 10, date: '' },
                            { abbr: 'T', coming: 0, cap: 10, date: '' },
                            { abbr: 'W', coming: 0, cap: 10, date: '' },
                            { abbr: 'R', coming: 0, cap: 10, date: '' },
                            { abbr: 'F', coming: 0, cap: 10, date: '' },
                            { abbr: 'Sa', coming: 0, cap: 10, date: '' },
                        ],
                    },
                },
            };

            const timezone = 'America/New_York';

            EnrollmentsService.processReservationsForRangedEnrollmentData(reservation, rangeReservationData, timezone);

            expect(rangeReservationData.scheduleTypes.scheduleType1.reservationsCount).toBe(0);
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].coming).toBe(0); // Tuesday
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].date).toBe('');
        });

        it('should handle reservations with invalid scheduledDate gracefully', () => {
            const reservation = {
                scheduledDate: 'invalid-date',
                scheduleType: 'scheduleType1',
            };

            const rangeReservationData = {
                scheduleTypes: {
                    scheduleType1: {
                        reservationsCount: 0,
                        stats: [
                            { abbr: 'Su', coming: 0, cap: 5, date: '' },
                            { abbr: 'M', coming: 0, cap: 10, date: '' },
                            { abbr: 'T', coming: 0, cap: 10, date: '' },
                            { abbr: 'W', coming: 0, cap: 10, date: '' },
                            { abbr: 'R', coming: 0, cap: 10, date: '' },
                            { abbr: 'F', coming: 0, cap: 10, date: '' },
                            { abbr: 'Sa', coming: 0, cap: 10, date: '' },
                        ],
                    },
                },
            };

            const timezone = 'America/New_York';

            // Call the method and assert no exceptions are thrown
            expect(() =>
                EnrollmentsService.processReservationsForRangedEnrollmentData(reservation, rangeReservationData, timezone)
            ).not.toThrow();

            // Assert that the reservationsCount remains unchanged
            expect(rangeReservationData.scheduleTypes.scheduleType1.reservationsCount).toBe(0);

            // Assert that the stats remain unchanged
            rangeReservationData.scheduleTypes.scheduleType1.stats.forEach((stat) => {
                expect(stat.coming).toBe(0);
                expect(stat.date).toBe('');
            });
        });

        it('should skip reservation if scheduleType is not permitted on a holiday (specific types)', () => {
            const reservation = {
                scheduledDate: '2025-01-07', // Tuesday
                scheduleType: 'scheduleType1',
            };

            const rangeReservationData = {
                scheduleTypes: {
                    scheduleType1: {
                        reservationsCount: 0,
                        stats: [
                            { abbr: 'Su', coming: 0, cap: 5, date: '' },
                            { abbr: 'M', coming: 0, cap: 10, date: '' },
                            { abbr: 'T', coming: 0, cap: 10, date: '' }, // Tuesday
                            { abbr: 'W', coming: 0, cap: 10, date: '' },
                            { abbr: 'R', coming: 0, cap: 10, date: '' },
                            { abbr: 'F', coming: 0, cap: 10, date: '' },
                            { abbr: 'Sa', coming: 0, cap: 10, date: '' },
                        ],
                    },
                },
                holidaysByDay: {
                    T: {
                        name: 'Holiday A',
                        permittedScheduleTypes: ['scheduleType2'], // excludes scheduleType1
                    }
                }
            };

            const timezone = 'America/New_York';

            EnrollmentsService.processReservationsForRangedEnrollmentData(reservation, rangeReservationData, timezone);

            expect(rangeReservationData.scheduleTypes.scheduleType1.reservationsCount).toBe(0);
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].coming).toBe(0); // Tuesday
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].date).toBe('');
        });

        it('should skip reservation if holiday permits "None"', () => {
            const reservation = {
                scheduledDate: '2025-01-07', // Tuesday
                scheduleType: 'scheduleType1',
            };

            const rangeReservationData = {
                scheduleTypes: {
                    scheduleType1: {
                        reservationsCount: 0,
                        stats: [
                            { abbr: 'Su', coming: 0, cap: 5, date: '' },
                            { abbr: 'M', coming: 0, cap: 10, date: '' },
                            { abbr: 'T', coming: 0, cap: 10, date: '' }, // Tuesday
                            { abbr: 'W', coming: 0, cap: 10, date: '' },
                            { abbr: 'R', coming: 0, cap: 10, date: '' },
                            { abbr: 'F', coming: 0, cap: 10, date: '' },
                            { abbr: 'Sa', coming: 0, cap: 10, date: '' },
                        ],
                    },
                },
                holidaysByDay: {
                    T: {
                        name: 'Closed for Holiday',
                        permittedScheduleTypes: ['None'],
                    }
                }
            };

            const timezone = 'America/New_York';

            EnrollmentsService.processReservationsForRangedEnrollmentData(reservation, rangeReservationData, timezone);

            expect(rangeReservationData.scheduleTypes.scheduleType1.reservationsCount).toBe(0);
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].coming).toBe(0);
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].date).toBe('');
        });

        it('should allow reservation if scheduleType is permitted on a holiday', () => {
            const reservation = {
                scheduledDate: '2025-01-07', // Tuesday
                scheduleType: 'scheduleType1',
            };

            const rangeReservationData = {
                scheduleTypes: {
                    scheduleType1: {
                        reservationsCount: 0,
                        stats: [
                            { abbr: 'Su', coming: 0, cap: 5, date: '' },
                            { abbr: 'M', coming: 0, cap: 10, date: '' },
                            { abbr: 'T', coming: 0, cap: 10, date: '' }, // Tuesday
                            { abbr: 'W', coming: 0, cap: 10, date: '' },
                            { abbr: 'R', coming: 0, cap: 10, date: '' },
                            { abbr: 'F', coming: 0, cap: 10, date: '' },
                            { abbr: 'Sa', coming: 0, cap: 10, date: '' },
                        ],
                    },
                },
                holidaysByDay: {
                    T: {
                        name: 'Partial Open',
                        permittedScheduleTypes: ['scheduleType1'],
                    }
                }
            };

            const timezone = 'America/New_York';

            EnrollmentsService.processReservationsForRangedEnrollmentData(reservation, rangeReservationData, timezone);

            expect(rangeReservationData.scheduleTypes.scheduleType1.reservationsCount).toBe(1);
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].coming).toBe(1); // Tuesday
            expect(rangeReservationData.scheduleTypes.scheduleType1.stats[2].date).toBe('01/07/2025');
        });
    });

    describe('initializeScheduleTypeData', () => {
        it('should correctly initialize data for a schedule type with valid inputs', () => {
            const rangeReservationData = { scheduleTypes: {} };
            const scheduleType = {
                _id: 'scheduleType1',
                type: 'Morning Class',
            };
            const maxEnrollment = {
                '0': 5,  // Sunday
                '1': 10, // Monday
                '2': 15, // Tuesday
                '3': 12, // Wednesday
                '4': 14, // Thursday
                '5': 16, // Friday
                '6': 8,  // Saturday
            };

            EnrollmentsService.initializeScheduleTypeData(rangeReservationData, scheduleType, maxEnrollment);

            expect(rangeReservationData.scheduleTypes.scheduleType1).toEqual({
                title: 'Morning Class',
                id: 'scheduleType1',
                reservationsCount: 0,
                capacity: 0,
                groups: {},
                reservations: [],
                stats: [
                    { abbr: 'Su', coming: 0, cap: 5, date: '' },
                    { abbr: 'M', coming: 0, cap: 10, date: '' },
                    { abbr: 'T', coming: 0, cap: 15, date: '' },
                    { abbr: 'W', coming: 0, cap: 12, date: '' },
                    { abbr: 'R', coming: 0, cap: 14, date: '' },
                    { abbr: 'F', coming: 0, cap: 16, date: '' },
                    { abbr: 'Sa', coming: 0, cap: 8, date: '' },
                ],
            });
        });

        it('should use default capacity values for Saturday and Sunday if not provided in maxEnrollment', () => {
            const rangeReservationData = { scheduleTypes: {} };
            const scheduleType = {
                _id: 'scheduleType2',
                type: 'Afternoon Class',
            };

            const maxEnrollment = {
                '1': 10, // Monday
                '2': 15, // Tuesday
            };

            EnrollmentsService.initializeScheduleTypeData(rangeReservationData, scheduleType, maxEnrollment);

            expect(rangeReservationData.scheduleTypes.scheduleType2).toEqual({
                title: 'Afternoon Class',
                id: 'scheduleType2',
                reservationsCount: 0,
                capacity: 0,
                groups: {},
                reservations: [],
                stats: [
                    { abbr: 'Su', coming: 0, cap: 0, date: '' },
                    { abbr: 'M', coming: 0, cap: 10, date: '' },
                    { abbr: 'T', coming: 0, cap: 15, date: '' },
                    { abbr: 'W', coming: 0, cap: 0, date: '' },
                    { abbr: 'R', coming: 0, cap: 0, date: '' },
                    { abbr: 'F', coming: 0, cap: 0, date: '' },
                    { abbr: 'Sa', coming: 0, cap: 0, date: '' },
                ],
            });
        });

        it('should handle an empty maxEnrollment object gracefully', () => {
            const rangeReservationData = { scheduleTypes: {} };
            const scheduleType = {
                _id: 'scheduleType3',
                type: 'Evening Class',
            };
            const maxEnrollment = {};

            EnrollmentsService.initializeScheduleTypeData(rangeReservationData, scheduleType, maxEnrollment);

            expect(rangeReservationData.scheduleTypes.scheduleType3).toEqual({
                title: 'Evening Class',
                id: 'scheduleType3',
                reservationsCount: 0,
                capacity: 0,
                groups: {},
                reservations: [],
                stats: [
                    { abbr: 'Su', coming: 0, cap: 0, date: '' },
                    { abbr: 'M', coming: 0, cap: 0, date: '' },
                    { abbr: 'T', coming: 0, cap: 0, date: '' },
                    { abbr: 'W', coming: 0, cap: 0, date: '' },
                    { abbr: 'R', coming: 0, cap: 0, date: '' },
                    { abbr: 'F', coming: 0, cap: 0, date: '' },
                    { abbr: 'Sa', coming: 0, cap: 0, date: '' },
                ],
            });
        });
    });

    describe('getDefaultMaxEnrollmentForRangedEnrollmentData', () => {
        it('should return default max enrollment for weekdays only when weekends are not enabled', () => {
            const mockOrg = {
                hasCustomization: jest.fn().mockReturnValue(false), // Weekends not enabled
            };

            const result = EnrollmentsService.getDefaultMaxEnrollmentForRangedEnrollmentData(mockOrg);

            expect(mockOrg.hasCustomization).toHaveBeenCalledWith(AvailableCustomizations.WEEKENDS_ENABLED);
            expect(result).toEqual([0, 0, 0, 0, 0]); // Only weekdays
        });

        it('should return default max enrollment for weekdays and weekends when weekends are enabled', () => {
            const mockOrg = {
                hasCustomization: jest.fn().mockReturnValue(true), // Weekends enabled
            };

            const result = EnrollmentsService.getDefaultMaxEnrollmentForRangedEnrollmentData(mockOrg);

            expect(mockOrg.hasCustomization).toHaveBeenCalledWith(AvailableCustomizations.WEEKENDS_ENABLED);
            expect(result).toEqual([0, 0, 0, 0, 0, 0, 0]); // Weekdays + Saturday and Sunday
        });

        it('should handle missing or null organization gracefully', () => {
            const mockOrg = null;

            const result = EnrollmentsService.getDefaultMaxEnrollmentForRangedEnrollmentData(mockOrg);

            expect(result).toEqual([0, 0, 0, 0, 0]);
        });
    });

    describe('getQueryForRangedEnrollmentData', () => {
        it('should construct a query object for ranged enrollment data', () => {
            const mockOrgId = 'org123';
            const mockStartDateMoment = moment('2025-01-01', 'YYYY-MM-DD');

            const result = EnrollmentsService.getQueryForRangedEnrollmentData(mockOrgId, mockStartDateMoment);

            expect(result).toEqual({
                orgId: mockOrgId,
                reservationType: "person",
                "$or": [
                    { scheduledEndDate: { "$exists": false } },
                    { scheduledEndDate: { "$exists": true, "$gte": mockStartDateMoment.valueOf() } },
                    { scheduledEndDate: { "$exists": true }, "$eq": null }
                ]
            });
        });

        it('should handle an invalid orgId gracefully', () => {
            const mockOrgId = null;
            const mockStartDateMoment = moment('2025-01-01', 'YYYY-MM-DD');

            const result = EnrollmentsService.getQueryForRangedEnrollmentData(mockOrgId, mockStartDateMoment);

            expect(result).toEqual({
                orgId: null,
                reservationType: "person",
                "$or": [
                    { scheduledEndDate: { "$exists": false } },
                    { scheduledEndDate: { "$exists": true, "$gte": mockStartDateMoment.valueOf() } },
                    { scheduledEndDate: { "$exists": true }, "$eq": null }
                ]
            });
        });

        it('should handle an invalid startDateMoment gracefully', () => {
            const mockOrgId = 'org123';
            const mockStartDateMoment = null;

            const result = EnrollmentsService.getQueryForRangedEnrollmentData(mockOrgId, mockStartDateMoment);

            expect(result).toEqual({
                orgId: mockOrgId,
                reservationType: "person",
                "$or": [
                    { scheduledEndDate: { "$exists": false } },
                    { scheduledEndDate: { "$exists": true, "$gte": null } },
                    { scheduledEndDate: { "$exists": true }, "$eq": null }
                ]
            });
        });

        it('should handle both orgId and startDateMoment being invalid', () => {
            const mockOrgId = null;
            const mockStartDateMoment = null;

            const result = EnrollmentsService.getQueryForRangedEnrollmentData(mockOrgId, mockStartDateMoment);

            expect(result).toEqual({
                orgId: null,
                reservationType: "person",
                "$or": [
                    { scheduledEndDate: { "$exists": false } },
                    { scheduledEndDate: { "$exists": true, "$gte": null } },
                    { scheduledEndDate: { "$exists": true }, "$eq": null }
                ]
            });
        });
    });

    describe('getBoundariesForRangedEnrollmentData', () => {
        let mockOrg;

        beforeEach(() => {
            mockOrg = {
                getTimezone: jest.fn().mockReturnValue("America/New_York"),
            };
        });

        it("should compute boundaries correctly with valid inputs", () => {
            const options = {
                rangeStart: "01/01/2025",
                rangeEnd: "01/07/2025",
            };

            const result = EnrollmentsService.getBoundariesForRangedEnrollmentData(options, mockOrg);

            expect(mockOrg.getTimezone).toHaveBeenCalled();
            expect(result).toEqual({
                timezone: "America/New_York",
                startDateMoment: expect.any(moment),
                startDate: "01/01/2025",
                endDatePlusOne: "01/08/2025",
                endDate: "01/07/2025",
            });

            expect(result.startDateMoment.format("MM/DD/YYYY")).toBe("01/01/2025");
        });

        it("should handle invalid rangeStart gracefully", () => {
            const options = {
                rangeStart: "invalid-date",
                rangeEnd: "01/07/2025",
            };

            expect(() =>
                EnrollmentsService.getBoundariesForRangedEnrollmentData(options, mockOrg)
            ).toThrow("Invalid rangeStart date");
        });

        it("should handle invalid rangeEnd gracefully", () => {
            const options = {
                rangeStart: "01/01/2025",
                rangeEnd: "invalid-date",
            };

            expect(() =>
                EnrollmentsService.getBoundariesForRangedEnrollmentData(options, mockOrg)
            ).toThrow("Invalid rangeEnd date");
        });

        it("should handle missing timezone gracefully", () => {
            mockOrg.getTimezone.mockReturnValue(undefined);

            const options = {
                rangeStart: "01/01/2025",
                rangeEnd: "01/07/2025",
            };

            const result = EnrollmentsService.getBoundariesForRangedEnrollmentData(options, mockOrg);

            expect(result).toEqual({
                timezone: undefined,
                startDateMoment: expect.any(moment),
                startDate: "01/01/2025",
                endDatePlusOne: "01/08/2025",
                endDate: "01/07/2025",
            });

            expect(result.startDateMoment.format("MM/DD/YYYY")).toBe("01/01/2025");
        });

        it("should handle missing rangeStart and rangeEnd gracefully", () => {
            const options = {};

            expect(() =>
                EnrollmentsService.getBoundariesForRangedEnrollmentData(options, mockOrg)
            ).toThrow("Range start and end dates are required");
        });
    });

    describe('rangedEnrollmentData', () => {

        const mockOrg = {
            _id: 'org123',
            getTimezone: jest.fn(() => 'America/New_York'),
            getScheduleTypes: jest.fn(() => [
                { _id: 'type1', type: 'Type 1', maxEnrollment: { 1: 10, 2: 15 } },
                { _id: 'type2', type: 'Type 2' },
            ]),
            hasCustomization: jest.fn(),
            getHolidays: jest.fn(() => []),
        };

        const mockOptions = {
            rangeStart: '01/01/2025',
            rangeEnd: '01/07/2025',
        };
        let findWithRecurrenceMock;
        beforeEach(() => {
            jest.clearAllMocks();

            findWithRecurrenceMock = jest.spyOn( Reservations, 'findWithRecurrence');
        });

        it('should throw an error if the organization is missing', async () => {
            await expect(EnrollmentsService.rangedEnrollmentData(mockOptions, null)).rejects.toThrow(
                new Meteor.Error(500, 'Organization not found')
            );
        });

        it('should throw an error if rangeStart or rangeEnd is missing', async () => {
            const invalidOptions = { rangeStart: '01/01/2025' };

            await expect(EnrollmentsService.rangedEnrollmentData(invalidOptions, mockOrg)).rejects.toThrow(
                new Meteor.Error(500, 'Range start and end dates are required')
            );
        });

        it('should initialize the rangeReservationData with schedule types', async () => {
            findWithRecurrenceMock.mockResolvedValue([]);

            const result = await EnrollmentsService.rangedEnrollmentData(mockOptions, mockOrg);

            expect(mockOrg.getScheduleTypes).toHaveBeenCalled();
            expect(result.scheduleTypes).toHaveLength(2);
            expect(result.scheduleTypes[0]).toMatchObject({
                id: 'type1',
                title: 'Type 1',
                reservationsCount: 0,
                stats: expect.any(Array),
            });
            expect(result.scheduleTypes[1]).toMatchObject({
                id: 'type2',
                title: 'Type 2',
                reservationsCount: 0,
                stats: expect.any(Array),
            });
        });

        it('should fetch reservations and process them', async () => {
            const mockReservations = [
                { scheduleType: 'type1', scheduledDate: '01/02/2025' },
                { scheduleType: 'type1', scheduledDate: '01/03/2025' },
                { scheduleType: 'type2', scheduledDate: '01/05/2025' },
            ];
            findWithRecurrenceMock.mockResolvedValue(mockReservations);

            const result = await EnrollmentsService.rangedEnrollmentData(mockOptions, mockOrg);

            expect(findWithRecurrenceMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    startDate: mockOptions.rangeStart,
                    endDate: expect.any(String),
                    query: expect.any(Object),
                })
            );

            const scheduleType1 = result.scheduleTypes.find((st) => st.id === 'type1');
            const scheduleType2 = result.scheduleTypes.find((st) => st.id === 'type2');

            expect(scheduleType1.reservationsCount).toBe(2);
            expect(scheduleType2.reservationsCount).toBe(1);
            expect(scheduleType1.stats[3].coming).toBe(1); // Day of week for '01/02/2025'
            expect(scheduleType1.stats[4].coming).toBe(1); // Day of week for '01/03/2025'
            expect(scheduleType2.stats[6].coming).toBe(1); // Day of week for '01/05/2025'
        });

        it('should handle missing or invalid reservations gracefully', async () => {
            const mockReservations = [
                { scheduleType: 'type1', scheduledDate: 'invalid-date' },
                { scheduleType: 'type2' },
            ];
            findWithRecurrenceMock.mockResolvedValue(mockReservations);

            const result = await EnrollmentsService.rangedEnrollmentData(mockOptions, mockOrg);

            const scheduleType1 = result.scheduleTypes.find((st) => st.id === 'type1');
            const scheduleType2 = result.scheduleTypes.find((st) => st.id === 'type2');

            expect(scheduleType1.reservationsCount).toBe(0);
            expect(scheduleType2.reservationsCount).toBe(0);
        });

        it('should use default max enrollment if scheduleType.maxEnrollment is undefined', async () => {
            Meteor.user = jest.fn().mockReturnValue({ fetchPerson: jest.fn().mockReturnValue({ superAdmin: true }) });
           const getTimezone = jest.fn().mockImplementation(() => 'America/Chicago');

            const orgDetails = {
                "_id": "type1",
                "name": "Flemington",
                getTimezone: getTimezone,
                billing: { toplinePercentDiscounts: 5 }
            };
            mockOrg.getScheduleTypes.mockReturnValue([
                orgDetails
            ]);
            const org = {
                ...mockOrg,
                _id: 'type1', 
                getTimezone: getTimezone  
            }
            
            findWithRecurrenceMock.mockResolvedValue([]);

            const result = await EnrollmentsService.rangedEnrollmentData(mockOptions, org);

            const scheduleType1 = result.scheduleTypes.find((st) => st.id === 'type1');
            expect(scheduleType1.stats).toEqual(
                expect.arrayContaining([
                    { abbr: 'M', coming: 0, cap: 0, date: '' },
                    { abbr: 'T', coming: 0, cap: 0, date: '' },
                    { abbr: 'W', coming: 0, cap: 0, date: '' },
                    { abbr: 'R', coming: 0, cap: 0, date: '' },
                    { abbr: 'F', coming: 0, cap: 0, date: '' },
                ])
            );
        });
    });
});

