import { HistoryAuditChangeTypes, HistoryAuditPeoplePerformedByNames, HistoryAuditRecordTypes } from '../../../lib/constants/historyAuditConstants';
import { jest, expect } from '@jest/globals';
import { mockCollection } from '../../helpers/collectionMock';
import { HistoryAuditService } from '../../../server/historyAuditService';
import { Log } from '../../../lib/util/log';
import { People } from "../../../lib/collections/people";
import { HistoryAudits } from "../../../server/collections/historyAudits";

describe('HistoryAuditService', () => {
    let cursorMock;
    let findMock;
    let insertMock;
    let logErrorSpy;
    let peopleMock;
    let peopleFindMock;

    afterEach(() => {
        jest.clearAllMocks();
    });

    beforeEach(() => {
        const collectionMock = HistoryAudits;
        cursorMock = collectionMock.find;
        findMock = collectionMock.find;
        insertMock = collectionMock.insertAsync;
        logErrorSpy = jest.spyOn(Log, 'error').mockImplementation(() => {});
    });

    describe('logStatusChange', () => {
        let logHistoryMock;

        beforeEach(() => {
            jest.resetAllMocks();
            logHistoryMock = jest.spyOn(HistoryAuditService, 'logHistory').mockImplementation(() => { });
        });

        afterEach(() => {
            logHistoryMock.mockRestore();
        });

        it('should log status change with all options provided', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.EDIT,
                details: 'Changed status',
                performedByUser: { _id: 'user123' },
                performedByName: 'User Name',
                previousState: { _id: 'person123', orgId: 'org123', inActive: false },
                currentState: { _id: 'person123', orgId: 'org123', inActive: true }
            };

            await HistoryAuditService.logStatusChange(options);

            expect(logHistoryMock).toHaveBeenCalledWith(expect.objectContaining({
                changeType: options.changeType,
                details: `${options.details} via ${options.performedByName}`,
                personId: options.previousState._id,
                orgId: options.previousState.orgId,
                recordId: options.previousState._id,
                recordType: HistoryAuditRecordTypes.STATUS,
                performedByUser: options.performedByUser,
                performedByName: options.performedByName,
                previousState: options.previousState,
                currentState: options.currentState
            }));
        });

        it('should log status change with default change type and details', async () => {
            const options = {
                performedByUser: { _id: 'user123' },
                performedByName: 'User Name',
                previousState: { _id: 'person123', orgId: 'org123', inActive: false },
                currentState: { _id: 'person123', orgId: 'org123', inActive: true }
            };

            await HistoryAuditService.logStatusChange(options);

            expect(logHistoryMock).toHaveBeenCalledWith(expect.objectContaining({
                changeType: HistoryAuditChangeTypes.EDIT,
                details: `Status changed to inactive via ${options.performedByName}`,
                personId: options.previousState._id,
                orgId: options.previousState.orgId,
                recordId: options.previousState._id,
                recordType: HistoryAuditRecordTypes.STATUS,
                performedByUser: options.performedByUser,
                performedByName: options.performedByName,
                previousState: options.previousState,
                currentState: options.currentState
            }));
        });

        it('should log status change with default change type and no performedByName', async () => {
            const options = {
                performedByUser: { _id: 'user123' },
                previousState: { _id: 'person123', orgId: 'org123', inActive: false },
                currentState: { _id: 'person123', orgId: 'org123', inActive: true }
            };

            await HistoryAuditService.logStatusChange(options);

            expect(logHistoryMock).toHaveBeenCalledWith(expect.objectContaining({
                changeType: HistoryAuditChangeTypes.EDIT,
                details: `Status changed to inactive`,
                personId: options.previousState._id,
                orgId: options.previousState.orgId,
                recordId: options.previousState._id,
                recordType: HistoryAuditRecordTypes.STATUS,
                performedByUser: options.performedByUser,
                performedByName: undefined,
                previousState: options.previousState,
                currentState: options.currentState
            }));
        });

        it('should log status change with missing currentState', async () => {
            const options = {
                performedByUser: { _id: 'user123' },
                performedByName: 'User Name',
                previousState: { _id: 'person123', orgId: 'org123', inActive: false }
            };

            await HistoryAuditService.logStatusChange(options);

            expect(logHistoryMock).toHaveBeenCalledWith(expect.objectContaining({
                changeType: HistoryAuditChangeTypes.EDIT,
                details: `Status changed to active via ${options.performedByName}`,
                personId: options.previousState._id,
                orgId: options.previousState.orgId,
                recordId: options.previousState._id,
                recordType: HistoryAuditRecordTypes.STATUS,
                performedByUser: options.performedByUser,
                performedByName: options.performedByName,
                previousState: options.previousState,
                currentState: undefined
            }));
        });

        it('should log status change with missing previousState', async () => {
            const options = {
                performedByUser: { _id: 'user123' },
                performedByName: 'User Name',
                currentState: { _id: 'person123', orgId: 'org123', inActive: true }
            };

            await HistoryAuditService.logStatusChange(options);

            expect(logHistoryMock).toHaveBeenCalledWith(expect.objectContaining({
                changeType: HistoryAuditChangeTypes.EDIT,
                details: `Status changed to inactive via ${options.performedByName}`,
                personId: options.currentState._id,
                orgId: options.currentState.orgId,
                recordId: options.currentState._id,
                recordType: HistoryAuditRecordTypes.STATUS,
                performedByUser: options.performedByUser,
                performedByName: options.performedByName,
                previousState: undefined,
                currentState: options.currentState
            }));
        });

        it('should log error when personId is missing', async () => {
            const logErrorMock = jest.spyOn(Log, 'error').mockImplementation(() => { });

            const options = {
                performedByUser: { _id: 'user123' },
                performedByName: 'User Name',
                previousState: { orgId: 'org123', inActive: false },
                currentState: { orgId: 'org123', inActive: true }
            };

            await HistoryAuditService.logStatusChange(options);

            expect(logErrorMock).toHaveBeenCalledWith("There is no associated person for this status change.");
            expect(logHistoryMock).not.toHaveBeenCalled();

            logErrorMock.mockRestore();
        });

        it('should log error when orgId is missing', async () => {
            const logErrorMock = jest.spyOn(Log, 'error').mockImplementation(() => { });

            const options = {
                performedByUser: { _id: 'user123' },
                performedByName: 'User Name',
                previousState: { _id: 'person123', inActive: false },
                currentState: { _id: 'person123', inActive: true }
            };

            await HistoryAuditService.logStatusChange(options);

            expect(logErrorMock).toHaveBeenCalledWith('Org ID is required for logging status change.');
            expect(logHistoryMock).not.toHaveBeenCalled();

            logErrorMock.mockRestore();
        });
    });

    describe('getHistoryForPerson', () => {
        it('should call the correct query when getting the changes for a person', async () => {
            cursorMock.mockImplementation(() => {
                return ({
                    fetchAsync:jest.fn().mockImplementation(()=>[{ _id: 'audit123', personId: 'person123' }])
                })
            });
            const history = await HistoryAuditService.getHistoryForPerson('person123');
            expect(history).toStrictEqual([{ _id: 'audit123', personId: 'person123' }]);
            expect(cursorMock.mock.calls.length).toBe(1);
            expect(findMock.mock.calls.length).toBe(1);
            expect(findMock.mock.calls[0]).toStrictEqual([
                { personId: 'person123' },
                { sort: { timestamp: -1 } }
            ]);
        });
    });

    describe('logAudit', () => {
        it('should insert audit data correctly without performedByUser', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.EDIT,
                details: 'Updated details',
                personId: 'person123',
                orgId: 'org123',
                recordId: 'record123',
                recordType: HistoryAuditRecordTypes.PERSON,
                performedByUser: null,
                performedByName: 'Admin User',
                previousState: { name: 'Old Name' },
                currentState: { name: 'New Name' }
            };

            await HistoryAuditService.logHistory(options);

            expect(insertMock.mock.calls.length).toBe(1);
            expect(insertMock.mock.calls[0]).toEqual([{
                changeType: options.changeType,
                details: options.details,
                diff: [
                    {
                        kind: "E",
                        lhs: "Old Name",
                        path: [
                            "name"
                        ],
                        rhs: "New Name"
                    }
                ],
                personId: options.personId,
                orgId: options.orgId,
                recordId: options.recordId,
                recordType: options.recordType,
                performedById: null,
                performedByName: options.performedByName,
                timestamp: expect.any(Number)
            }]);
        });

        it('should insert audit data correctly with performedByUser', async () => {
            const performedByUser = {
                fetchPerson: jest.fn().mockReturnValue({ firstName: 'John', lastName: 'Doe', _id: 'userPerson123' })
            };

            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Added details',
                personId: 'person123',
                orgId: 'org123',
                recordId: 'record123',
                recordType: HistoryAuditRecordTypes.PERSON,
                performedByUser,
                performedByName: null,
                previousState: null,
                currentState: { name: 'New Name' }
            };

            await HistoryAuditService.logHistory(options);

            expect(insertMock.mock.calls.length).toBe(1);
            expect(insertMock.mock.calls[0]).toEqual([{
                changeType: options.changeType,
                details: options.details,
                diff: [
                    {
                        kind: "E",
                        lhs: null,
                        rhs: {
                            "name": "New Name"
                        }
                    }
                ],
                personId: options.personId,
                orgId: options.orgId,
                recordId: options.recordId,
                recordType: options.recordType,
                performedById: 'userPerson123',
                performedByName: 'John Doe',
                timestamp: expect.any(Number)
            }]);
        });
    });

    describe('logPersonChange', () => {
        it('should log an error if personId is missing', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Updated details',
                performedByUser: null,
                performedByName: 'Admin User',
                previousState: null,
                currentState: null
            };

            await HistoryAuditService.logPersonChange(options);

            expect(insertMock.mock.calls.length).toBe(0);
        });

        it('should log an error if orgId is missing', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Updated details',
                performedByUser: null,
                performedByName: 'Admin User',
                previousState: { _id: 'person123' },
                currentState: null
            };

            await HistoryAuditService.logPersonChange(options);

            expect(insertMock.mock.calls.length).toBe(0);
        });

        it('should log person change correctly', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Added details',
                performedByUser: null,
                performedByName: HistoryAuditPeoplePerformedByNames.ADP,
                previousState: { _id: 'person123', orgId: 'org123' },
                currentState: { _id: 'person123', orgId: 'org123', name: 'New Name' }
            };

            await HistoryAuditService.logPersonChange(options);

            expect(insertMock.mock.calls.length).toBe(1);
            expect(insertMock.mock.calls[0]).toEqual([{
                changeType: options.changeType,
                details: 'Added via ADP',
                diff: [
                    {
                        kind: "N",
                        path: [
                            "name"
                        ],
                        rhs: "New Name"
                    }
                ],
                personId: 'person123',
                orgId: 'org123',
                recordId: 'person123',
                recordType: HistoryAuditRecordTypes.PERSON,
                performedById: null,
                performedByName: 'ADP',
                timestamp: expect.any(Number)
            }]);

            options.performedByUser = {
                fetchPerson: jest.fn().mockReturnValue({ firstName: 'John', lastName: 'Doe', _id: 'userPerson123' })
            };
            delete options.performedByName;

            await HistoryAuditService.logPersonChange(options);

            expect(insertMock.mock.calls.length).toBe(2);
            expect(insertMock.mock.calls[1]).toEqual([{
                changeType: options.changeType,
                details: 'User added record',
                diff: [
                    {
                        kind: "N",
                        path: [
                            "name"
                        ],
                        rhs: "New Name"
                    }
                ],
                personId: 'person123',
                orgId: 'org123',
                recordId: 'person123',
                recordType: HistoryAuditRecordTypes.PERSON,
                performedById: 'userPerson123',
                performedByName: 'John Doe',
                timestamp: expect.any(Number)
            }]);
        });

        it('should log person change correctly for PLR when user is also known', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Added details',
                performedByUser: {
                    fetchPerson: jest.fn().mockReturnValue({ firstName: 'John', lastName: 'Doe', _id: 'userPerson123' })
                },
                performedByName: HistoryAuditPeoplePerformedByNames.PLR,
                previousState: null,
                currentState: { _id: 'person123', orgId: 'org123', name: 'New Name' }
            };

            await HistoryAuditService.logPersonChange(options);

            expect(insertMock.mock.calls.length).toBe(1);
            expect(insertMock.mock.calls[0]).toEqual([{
                changeType: options.changeType,
                details: 'Added via Parent-Led Registration',
                diff: [
                    {
                        kind: "E",
                        lhs: null,
                        rhs: {
                            _id: "person123",
                            name: "New Name",
                            orgId: "org123"
                        }
                    }
                ],
                personId: 'person123',
                orgId: 'org123',
                recordId: 'person123',
                recordType: HistoryAuditRecordTypes.PERSON,
                performedById: 'userPerson123',
                performedByName: 'Parent-Led Registration',
                timestamp: expect.any(Number)
            }]);
        });
    });

    describe('logScheduleChange', () => {
        it('should log an error if recordId is missing', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.EDIT,
                details: 'Updated details',
                performedByUser: null,
                performedByName: 'Admin User',
                previousState: null,
                currentState: null
            };

            await HistoryAuditService.logScheduleChange(options);
            expect(insertMock.mock.calls.length).toBe(0);
        });

        it('should log an error if personId is missing', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Updated details',
                performedByUser: null,
                performedByName: 'Admin User',
                previousState: { _id: 'schedule123' },
                currentState: null
            };

            await HistoryAuditService.logScheduleChange(options);
            expect(insertMock.mock.calls.length).toBe(0);
        });

        it('should log an error if orgId is missing', async () => {
            const options = {
                changeType: 'UPDATE',
                details: 'Updated details',
                performedByUser: null,
                performedByName: 'Admin User',
                previousState: { _id: 'schedule123', selectedPerson: 'person123' },
                currentState: null
            };

            await HistoryAuditService.logScheduleChange(options);
            expect(insertMock.mock.calls.length).toBe(0);
        });

        it('should log schedule change correctly', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Added details',
                performedByUser: null,
                performedByName: 'Admin User',
                previousState: null,
                currentState: { _id: 'schedule123', selectedPerson: 'person123', orgId: 'org123' }
            };

            await HistoryAuditService.logScheduleChange(options);
            expect(insertMock.mock.calls.length).toBe(1);
            expect(insertMock.mock.calls[0]).toEqual([{
                changeType: options.changeType,
                details: options.details += ' via Admin User',
                diff: [
                    {
                        kind: "E",
                        lhs: null,
                        rhs: {
                            _id: "schedule123",
                            orgId: "org123",
                            selectedPerson: "person123"
                        }
                    }
                ],
                personId: 'person123',
                orgId: 'org123',
                recordId: 'schedule123',
                recordType: HistoryAuditRecordTypes.SCHEDULE,
                performedById: null,
                performedByName: 'Admin User',
                timestamp: expect.any(Number)
            }]);
        });
    });

    describe('getOptionsForChange', () => {
        it('should return false if type is not provided', () => {
            const options = {
                currentState: { _id: 'person123', orgId: 'org123' }
            };
            const result = HistoryAuditService.getOptionsForChange(options);
            expect(result).toBe(false);
            expect(logErrorSpy).toHaveBeenCalledWith('Type is required for getting options for change.');
        });

        it('should return false if personId is missing', () => {
            const options = {
                currentState: { orgId: 'org123' }
            };
            const result = HistoryAuditService.getOptionsForChange(options, HistoryAuditRecordTypes.PERSON);
            expect(result).toBe(false);
            expect(logErrorSpy).toHaveBeenCalledWith('There is no associated person for this person change.');
        });

        it('should return false if orgId is missing', () => {
            const options = {
                currentState: { _id: 'person123' }
            };
            const result = HistoryAuditService.getOptionsForChange(options, HistoryAuditRecordTypes.PERSON);
            expect(result).toBe(false);
            expect(logErrorSpy).toHaveBeenCalledWith('Org ID is required for logging person change.');
        });

        it('should return correct options for person change', () => {
            const options = {
                currentState: { _id: 'person123', orgId: 'org123' }
            };
            const result = HistoryAuditService.getOptionsForChange(options, HistoryAuditRecordTypes.PERSON);
            expect(result).toStrictEqual({
                personId: 'person123',
                orgId: 'org123',
                recordId: 'person123'
            });
        });

        it('should return correct options for schedule change', () => {
            const options = {
                currentState: { selectedPerson: 'person123', orgId: 'org123' }
            };
            const result = HistoryAuditService.getOptionsForChange(options, HistoryAuditRecordTypes.SCHEDULE);
            expect(result).toStrictEqual({
                personId: 'person123',
                orgId: 'org123',
                recordId: null
            });
        });
    });

    describe('logPaymentChange', () => {
        it('should log payment change correctly when performedByName is provided', async () => {
            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: null,
                performedByUser: null,
                performedByName: 'Admin User',
                currentState: { _id: 'person123', orgId: 'org123' },
                previousState: null
            };

            await HistoryAuditService.logPaymentChange(options);

            expect(insertMock.mock.calls.length).toBe(1);
            expect(insertMock.mock.calls[0]).toEqual([{
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Payment method added via Admin User',
                diff: [
                    {
                        kind: "E",
                        lhs: null,
                        rhs: {
                            _id: "person123",
                            orgId: "org123"
                        }
                    }
                ],
                personId: 'person123',
                orgId: 'org123',
                recordId: 'person123',
                recordType: HistoryAuditRecordTypes.PAYMENT_METHOD,
                performedById: null,
                performedByName: 'Admin User',
                timestamp: expect.any(Number)
            }]);
        });

        it('should log payment change correctly when performedByUser is provided', async () => {
            const performedByUser = {
                fetchPerson: jest.fn().mockReturnValue({ firstName: 'John', lastName: 'Doe', _id: 'userPerson123' })
            };

            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: null,
                performedByUser,
                performedByName: null,
                currentState: { _id: 'person123', orgId: 'org123' },
                previousState: null
            };

            await HistoryAuditService.logPaymentChange(options);

            expect(insertMock.mock.calls.length).toBe(1);
            expect(insertMock.mock.calls[0]).toEqual([{
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Payment method added by user',
                diff: [
                    {
                        kind: "E",
                        lhs: null,
                        rhs: {
                            _id: "person123",
                            orgId: "org123"
                        }
                    }
                ],
                personId: 'person123',
                orgId: 'org123',
                recordId: 'person123',
                recordType: HistoryAuditRecordTypes.PAYMENT_METHOD,
                performedById: 'userPerson123',
                performedByName: 'John Doe',
                timestamp: expect.any(Number)
            }]);
        });

        it('should not log payment change if getOptionsForChange returns false', async () => {
            jest.spyOn(HistoryAuditService, 'getOptionsForChange').mockReturnValue(false);

            const options = {
                changeType: HistoryAuditChangeTypes.ADD,
                details: 'Added payment method',
                performedByUser: null,
                performedByName: 'Admin User',
                currentState: { _id: 'person123', orgId: 'org123' },
                previousState: null
            };

            await HistoryAuditService.logPaymentChange(options);

            expect(insertMock.mock.calls.length).toBe(0);
        });
    });

    describe('logRefund', () => {

        beforeEach(() => {
            // Mock the People collection
            peopleMock = People;
            peopleFindMock = peopleMock.find;
        });

        it('should log refund history for both the parent and the child', async () => {
            // Mock People.find to return parent and child data
            peopleFindMock.mockReturnValue({
                fetchAsync: jest.fn(() => [
                    { _id: 'parentId', firstName: 'ParentFirst', lastName: 'ParentLast' },
                    { _id: 'childId', firstName: 'ChildFirst', lastName: 'ChildLast' }
                ])
            });

            // Mock performedByUser with a fetchPerson function
            const performedByUser = {
                fetchPerson: jest.fn(() => ({ firstName: 'Admin', lastName: 'User', _id: 'userId' })),
                orgId: 'org123'
            };

            const options = {
                amount: 50,
                invoiceNumber: 'INV123',
                invoiceId: 'abc123',
                parentId: 'parentId',
                childId: 'childId',
                performedByUser
            };

            await HistoryAuditService.logRefund(options);

            // Verify People.find was called with the correct query
            expect(peopleFindMock).toHaveBeenCalledWith(
                { _id: { $in: ['parentId', 'childId'] } },
                { fields: { firstName: 1, lastName: 1 } }
            );

            // Verify insert was called twice for parent and child
            expect(insertMock).toHaveBeenCalledTimes(2);

            // Verify first insert call for the child
            expect(insertMock).toHaveBeenCalledWith({
                changeType: HistoryAuditChangeTypes.ADD,
                details: '$50 from invoice INV123 refunded to ParentFirst ParentLast',
                personId: 'childId',
                orgId: 'org123',
                recordId: 'abc123',
                recordType: HistoryAuditRecordTypes.REFUND,
                performedById: 'userId',
                performedByName: 'Admin User',
                diff: null,
                timestamp: expect.any(Number)
            });

            // Verify second insert call for the parent
            expect(insertMock).toHaveBeenCalledWith({
                changeType: HistoryAuditChangeTypes.ADD,
                details: '$50 from invoice INV123 refunded for ChildFirst ChildLast',
                personId: 'parentId',
                orgId: 'org123',
                recordId: 'abc123',
                recordType: HistoryAuditRecordTypes.REFUND,
                performedById: 'userId',
                performedByName: 'Admin User',
                diff: null,
                timestamp: expect.any(Number)
            });
        });

        it('should handle missing parent or child records gracefully', async () => {
            // Mock People.find to return an empty array
            peopleFindMock.mockReturnValue({
                fetchAsync: jest.fn(() => [])
            });

            // Mock performedByUser with a fetchPerson function
            const performedByUser = {
                fetchPerson: jest.fn(() => ({ firstName: 'Admin', lastName: 'User', _id: 'userId' })),
                orgId: 'org123'
            };

            const options = {
                amount: 50,
                invoiceNumber: 'INV123',
                invoiceId: 'abc123',
                parentId: 'parentId',
                childId: 'childId',
                performedByUser
            };

            await HistoryAuditService.logRefund(options);

            // Verify insert was called twice even when no records are found
            expect(insertMock).toHaveBeenCalledTimes(2);

            // Check the first log with undefined names
            expect(insertMock).toHaveBeenCalledWith({
                changeType: HistoryAuditChangeTypes.ADD,
                details: '$50 from invoice INV123 refunded to undefined undefined',
                personId: 'childId',
                orgId: 'org123',
                recordId: 'abc123',
                recordType: HistoryAuditRecordTypes.REFUND,
                performedById: 'userId',
                performedByName: 'Admin User',
                diff: null,
                timestamp: expect.any(Number)
            });

            // Check the second log with undefined names
            expect(insertMock).toHaveBeenCalledWith({
                changeType: HistoryAuditChangeTypes.ADD,
                details: '$50 from invoice INV123 refunded for undefined undefined',
                personId: 'parentId',
                orgId: 'org123',
                recordId: 'abc123',
                recordType: HistoryAuditRecordTypes.REFUND,
                performedById: 'userId',
                performedByName: 'Admin User',
                diff: null,
                timestamp: expect.any(Number)
            });
        });

        it('should log refund with null performedByName and performedById if fetchPerson returns undefined', async () => {
            // Mock People.find to return parent and child data
            peopleFindMock.mockReturnValue({
                fetchAsync: jest.fn(() => [
                    { _id: 'parentId', firstName: 'ParentFirst', lastName: 'ParentLast' },
                    { _id: 'childId', firstName: 'ChildFirst', lastName: 'ChildLast' }
                ])
            });

            // Mock performedByUser with fetchPerson returning undefined
            const performedByUser = {
                fetchPerson: jest.fn(() => undefined), // Simulate fetchPerson returning undefined
                orgId: 'org123'
            };

            const options = {
                amount: 50,
                invoiceNumber: 'INV123',
                invoiceId: 'abc123',
                parentId: 'parentId',
                childId: 'childId',
                performedByUser
            };

            await HistoryAuditService.logRefund(options);

            // Verify People.find was called with the correct query
            expect(peopleFindMock).toHaveBeenCalledWith(
                { _id: { $in: ['parentId', 'childId'] } },
                { fields: { firstName: 1, lastName: 1 } }
            );

            // Verify insert was called twice for parent and child
            expect(insertMock).toHaveBeenCalledTimes(2);

            // Verify first insert call for the child
            expect(insertMock).toHaveBeenCalledWith({
                changeType: HistoryAuditChangeTypes.ADD,
                details: '$50 from invoice INV123 refunded to ParentFirst ParentLast',
                personId: 'childId',
                orgId: 'org123',
                recordId: 'abc123',
                recordType: HistoryAuditRecordTypes.REFUND,
                performedById: null, // Expect null instead of undefined
                performedByName: null, // Expect null instead of undefined
                diff: null,
                timestamp: expect.any(Number) // Allow any number
            });

            // Verify second insert call for the parent
            expect(insertMock).toHaveBeenCalledWith({
                changeType: HistoryAuditChangeTypes.ADD,
                details: '$50 from invoice INV123 refunded for ChildFirst ChildLast',
                personId: 'parentId',
                orgId: 'org123',
                recordId: 'abc123',
                recordType: HistoryAuditRecordTypes.REFUND,
                performedById: null, // Expect null instead of undefined
                performedByName: null, // Expect null instead of undefined
                diff: null,
                timestamp: expect.any(Number) // Allow any number
            });
        });
    });
});
