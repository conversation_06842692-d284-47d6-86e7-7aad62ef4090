import { Meteor } from 'meteor/meteor';
import { describe, it, expect, jest } from '@jest/globals';
import { PersonPaymentInfoClientService } from '../../../../../client/services/people/PersonPaymentInfoClientService';
import { AvailableCustomizations } from '../../../../../lib/customizations';
import { USER_TYPES } from '../../../../../lib/constants/profileConstants';
import { AvailableActionTypes, AvailablePermissions } from '../../../../../lib/constants/permissionsConstants';
import { UserPermissionService } from '../../../../../lib/userPermissionService';
import '../../../../../client/app/billing/_billingPayment';
import { showModal } from '../../../../../client/app/main';
import '../../../../../client/app/simpleModal/simpleModal';
import { People } from '../../../../../lib/collections/people';

jest.mock('../../../../../client/app/simpleModal/simpleModal', () => ({
    showModal: jest.fn(),
}));

jest.mock('../../../../../client/app/main', () => ({
    showModal: jest.fn(),
}));

jest.mock('../../../../../client/app/billing/_billingPayment', () => ({
    showModal: jest.fn(),
}));

global.showModal = jest.fn();
global.mpSwal = { fire: jest.fn() };
global.$ = jest.fn(() => ({
    html: jest.fn().mockReturnThis(),
    prop: jest.fn().mockReturnThis(),
}));

describe('PersonPaymentInfoClientService', () => {
    let service;
    let mockOrg, mockPersonId;

    beforeEach(() => {
        mockOrg = {
            billing: { passthroughFees: true },
            hasCustomization: jest.fn(),
            billingCardProviderShared: jest.fn(() => ({
                getEnvironment: jest.fn().mockReturnValue('test'),
                getClientKey: jest.fn().mockReturnValue('test-key'),
            })),
        };
        mockPersonId = 'person123';

        service = new PersonPaymentInfoClientService(mockOrg, mockPersonId);

        jest.clearAllMocks();
    });

    describe('Helpers', () => {
        it('should return the current org', () => {
            const result = service.getCurrentOrg();

            expect(result).toBe(mockOrg);
        });

        it('should return true for showServiceChargeNotice when passthroughFees and customization exist', () => {
            mockOrg.hasCustomization.mockReturnValue(true);

            const result = service.showServiceChargeNotice();

            expect(result).toBe(true);
            expect(mockOrg.hasCustomization).toHaveBeenCalledWith(AvailableCustomizations.BILLING_PLATFORM_FEES);
        });

        it('should return false for preventAch when DISABLE_ACH customization is not present', () => {
            mockOrg.hasCustomization.mockReturnValue(false);

            const result = service.preventAch();

            expect(result).toBe(false);
            expect(mockOrg.hasCustomization).toHaveBeenCalledWith(AvailableCustomizations.DISABLE_ACH);
        });

        it('should return false for preventCreditCard when DISABLE_CREDIT_CARDS customization is not present', () => {
            mockOrg.hasCustomization.mockReturnValue(false);

            const result = service.preventCreditCard();

            expect(result).toBe(false);
            expect(mockOrg.hasCustomization).toHaveBeenCalledWith(AvailableCustomizations.DISABLE_CREDIT_CARDS);
        });

        it('should return true for allowAutoPay when QUEUE_AUTO_PAYMENTS customization is not present', () => {
            mockOrg.hasCustomization.mockReturnValue(false);

            const result = service.allowAutoPay();

            expect(result).toBe(true);
            expect(mockOrg.hasCustomization).toHaveBeenCalledWith(AvailableCustomizations.QUEUE_AUTO_PAYMENTS);
        });

        it('should return true if org has billing.autoPayEnrollmentRequired, false otherwise', () => {
            const result1 = service.autoPayRequired()
            expect(result1).toBe(false);

            mockOrg.billing.autopayEnrollmentRequired = true;

            const result2 = service.autoPayRequired();
            expect(result2).toBe(true);
        });

        it('should return true if user is superAdmin or admin, false if otherwise', () => {
            Meteor.user = jest.fn().mockReturnValue({ fetchPerson: jest.fn().mockReturnValue({ superAdmin: true }) });

            const result1 = service.isAdmin();
            expect(result1).toBe(true);

            Meteor.user = jest.fn().mockReturnValue({ fetchPerson: jest.fn().mockReturnValue({ type: USER_TYPES.ADMIN }) });

            const result2 = service.isAdmin();
            expect(result2).toBe(true);

            Meteor.user = jest.fn().mockReturnValue({ fetchPerson: jest.fn().mockReturnValue({ type: USER_TYPES.STAFF }) });

            const result3 = service.isAdmin();
            expect(result3).toBe(false);
        });
    });

    describe('hasUpdatePermission', () => {
        let processPermissionsSpy;

        beforeEach(() => {
            mockOrg.availablePermissionsContexts = jest.fn().mockReturnValue([AvailablePermissions.BILLING_AUTO_PAY_UPDATE]);
            mockOrg.requireRoles = jest.fn().mockReturnValue(true);
            mockOrg.valueOverrides = {};
            mockOrg.valueOverrides.roleDefinitions = {
                denyRole:  {
                    rules: [
                        {
                            context: AvailablePermissions.BILLING_AUTO_PAY_UPDATE,
                            action: AvailableActionTypes.DENY
                        }
                    ]
                },
                allowRole: {
                    rules: [
                        {
                            context: AvailablePermissions.BILLING_AUTO_PAY_UPDATE,
                            action: AvailableActionTypes.EDIT
                        }
                    ]
                },
            }
            processPermissionsSpy = jest.spyOn(UserPermissionService, 'processPermissions');

            jest.clearAllMocks();
        });

        it('should return true when processPermissions allows access', () => {
            const mockUserPerson = { type: USER_TYPES.ADMIN, roles: ['allowRole'] };
            Meteor.user = jest.fn().mockReturnValue({ fetchPerson: jest.fn().mockReturnValue(mockUserPerson) });

            const result = service.hasUpdatePermission();

            expect(result).toBe(true);
            expect(Meteor.user).toHaveBeenCalled();
            expect(processPermissionsSpy).toHaveBeenCalledWith({
                assertions: [{ context: AvailablePermissions.BILLING_AUTO_PAY_UPDATE, action: AvailableActionTypes.EDIT }],
                evaluator: expect.any(Function),
                currentPerson: mockUserPerson,
                currentOrg: mockOrg,
            });
        });

        it('should return false when processPermissions denies access', () => {
            const mockUserPerson = { type: USER_TYPES.ADMIN, roles: ['denyRole'] };
            Meteor.user = jest.fn().mockReturnValue({ fetchPerson: jest.fn().mockReturnValue(mockUserPerson) });

            const result = service.hasUpdatePermission();

            expect(result).toBe(false);
            expect(processPermissionsSpy).toHaveBeenCalledWith({
                assertions: [{ context: AvailablePermissions.BILLING_AUTO_PAY_UPDATE, action: AvailableActionTypes.EDIT }],
                evaluator: expect.any(Function),
                currentPerson: mockUserPerson,
                currentOrg: mockOrg,
            });
        });

        it('should return false when userPerson is null', () => {
            Meteor.user = jest.fn().mockReturnValue(null);

            const result = service.hasUpdatePermission();

            expect(result).toBe(false);
            expect(processPermissionsSpy).not.toHaveBeenCalled();
        });

        it('should call evaluator with correct logic for user type', () => {
            mockOrg.requireRoles.mockReturnValue(false);
            const mockUserPerson = { type: USER_TYPES.ADMIN };
            Meteor.user = jest.fn().mockReturnValue({ fetchPerson: jest.fn().mockReturnValue(mockUserPerson) });

            const result = service.hasUpdatePermission();

            expect(result).toBe(true);
            expect(processPermissionsSpy).toHaveBeenCalledWith({
                assertions: [{ context: AvailablePermissions.BILLING_AUTO_PAY_UPDATE, action: AvailableActionTypes.EDIT }],
                evaluator: expect.any(Function),
                currentPerson: mockUserPerson,
                currentOrg: mockOrg,
            });
        });

        it('evaluator should return false for non-admin user types', () => {
            mockOrg.requireRoles.mockReturnValue(false);
            const mockUserPerson = { type: USER_TYPES.STAFF };
            Meteor.user = jest.fn().mockReturnValue({ fetchPerson: jest.fn().mockReturnValue(mockUserPerson) });

            const result = service.hasUpdatePermission();

            expect(result).toBe(false);
            expect(processPermissionsSpy).toHaveBeenCalled();
        });
    });

    describe('handleAddOrReplaceCreditCard', () => {
        it('should call showModal with correct arguments', () => {
            service.handleAddOrReplaceCreditCard();

            expect(showModal).toHaveBeenCalledWith('simpleModal', {
                title: 'Add/Replace Credit Card',
                data: { providerAdyen: true },
                template: 'billingPaymentCreditCard',
                onRendered: expect.any(Function),
                onSave: expect.any(Function),
            });
        });
    });

    describe('handleAddOrReplaceBankAccount', () => {
        it('should call showModal with correct arguments', () => {
            service.handleAddOrReplaceBankAccount();

            expect(showModal).toHaveBeenCalledWith('simpleModal', {
                title: 'Add/Replace Bank Account',
                template: 'billingPaymentBankAccount',
                data: { providerAdyen: true },
                onRendered: expect.any(Function),
                onSave: expect.any(Function),
            });
        });
    });

    describe('handleRemovePaymentOption', () => {
        it('should call removeSourceAndAutopay when modal is confirmed', async () => {
            const paymentType = 'card';
            const mockPerson = { autoPayMethod: jest.fn().mockReturnValue(paymentType) };

            People.findOneAsync.mockResolvedValue(mockPerson),

            mpSwal.fire.mockResolvedValue({ value: true });

            jest.spyOn(service, 'removeSourceAndAutopay').mockImplementation();

            await service.handleRemovePaymentOption(paymentType);

            expect(People.findOneAsync).toHaveBeenCalledWith(mockPersonId);
            expect(mpSwal.fire).toHaveBeenCalled();
            expect(service.removeSourceAndAutopay).toHaveBeenCalledWith(paymentType, mockPersonId, true);
        });

        it('should not call removeSourceAndAutopay when modal is cancelled', async () => {
            const paymentType = 'card';
            const mockPerson = { autoPayMethod: jest.fn().mockReturnValue(paymentType) };

            global.People = {
                findOneAsync: jest.fn().mockResolvedValue(mockPerson),
            };
            mpSwal.fire.mockResolvedValue({ value: false });

            jest.spyOn(service, 'removeSourceAndAutopay').mockImplementation();

            await service.handleRemovePaymentOption(paymentType);

            expect(service.removeSourceAndAutopay).not.toHaveBeenCalled();
        });
    });

    describe('removeSourceAndAutopay', () => {
        it('should call removeCustomerSource and removeAutoPay when isAutoPay is true', () => {
            const paymentType = 'card';

            service.removeSourceAndAutopay(paymentType, mockPersonId, true);

            expect(Meteor.callAsync).toHaveBeenCalledWith(
                'removeCustomerSource',
                { type: paymentType, personId: mockPersonId }
            );
            expect(Meteor.callAsync).toHaveBeenCalledWith(
                'removeAutoPay',
                { personId: mockPersonId }
            );
        });

        it('should only call removeCustomerSource when isAutoPay is false', () => {
            const paymentType = 'card';

            service.removeSourceAndAutopay(paymentType, mockPersonId, false);

            expect(Meteor.callAsync).toHaveBeenCalledWith(
                'removeCustomerSource',
                { type: paymentType, personId: mockPersonId }
            );
            expect(Meteor.callAsync).not.toHaveBeenCalledWith('removeAutoPay', expect.anything());
        });
    });

    describe('handleAddAutoPay', () => {
        it('should call showModal with correct arguments for configuring AutoPay', async () => {
            const mockPerson = {
                connectedBankAccount: jest.fn().mockReturnValue({ status: 'verified' }),
                connectedCreditCard: jest.fn().mockReturnValue(true),
            };

            People.findOneAsync.mockResolvedValue(mockPerson);

            await service.handleAddAutoPay();

            expect(People.findOneAsync).toHaveBeenCalledWith(mockPersonId);
            expect(showModal).toHaveBeenCalledWith('simpleModal', {
                title: 'Configure AutoPay',
                template: 'billingConfigureAutoPay',
                data: {
                    disableBankAccount: false,
                    disableCreditCard: false,
                    showServiceChargeNotice: true,
                    currentOrg: mockOrg,
                },
                onSave: expect.any(Function),
            });
        });

        it('should handle missing bank account and credit card gracefully', async () => {
            const mockPerson = {
                connectedBankAccount: jest.fn().mockReturnValue(null),
                connectedCreditCard: jest.fn().mockReturnValue(null),
            };

            People.findOneAsync.mockResolvedValue(mockPerson);

            await service.handleAddAutoPay();

            expect(People.findOneAsync).toHaveBeenCalledWith(mockPersonId);
            expect(showModal).toHaveBeenCalledWith('simpleModal', {
                title: 'Configure AutoPay',
                template: 'billingConfigureAutoPay',
                data: {
                    disableBankAccount: true,
                    disableCreditCard: true,
                    showServiceChargeNotice: true,
                    currentOrg: mockOrg,
                },
                onSave: expect.any(Function),
            });
        });
    });

    describe('handleDisableAutoPay', () => {
        it('should call removeAutoPay when confirmed', async () => {
            mpSwal.fire.mockResolvedValue({ value: true });

            await service.handleDisableAutoPay();

            expect(Meteor.callAsync).toHaveBeenCalledWith(
                'removeAutoPay',
                { personId: mockPersonId }
            );
        });
    });
});
