import { ImporterUtils } from '../../../../../../../client/app/admin/importer/utils/importerUtils';
import { importerTypes } from '../../../../../../../lib/constants/importers/importerConstants';

describe('ImporterUtils.getImporterOptionsById', () => {
    const rootOrg = { _id: 'org1', name: 'Root Org' };
    const allOrgs = [{ _id: 'org1', name: 'Root Org' }, { _id: 'org2', name: 'Child Org' }];
    const context = { rootOrg, allOrgs };

    it('should return rootOrg and allOrgs for org-importer', () => {
        const result = ImporterUtils.getImporterOptionsById(importerTypes.ORG, context);
        expect(result).toEqual({ rootOrg, allOrgs });
    });

    it('should return an empty object for unknown importer type', () => {
        const result = ImporterUtils.getImporterOptionsById('unknown-importer', context);
        expect(result).toEqual({});
    });

    it('should return empty object when no context is passed for known importer', () => {
        const result = ImporterUtils.getImporterOptionsById(importerTypes.ORG);
        expect(result).toEqual({ rootOrg: undefined, allOrgs: undefined });
    });
});
