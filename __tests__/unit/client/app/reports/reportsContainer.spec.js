global.ReactiveVar = function (initialValue) {
  let value = initialValue;
  return {
    get: () => value,
    set: (newVal) => { value = newVal; },
  };
};

let onCreatedCallback = null;

global.Template = {
  reportsContainer: {
    onCreated(callback) {
      onCreatedCallback = callback;
    },
    helpers(helpersObj) {
      this._helpers = helpersObj;
    },
    events(eventsObj) {
      this._events = eventsObj;
    },
  },
  instance() {
    return {
      enabledReports: new ReactiveVar([]),
      searchText: new ReactiveVar(''),
      isLoading: new ReactiveVar(true),
    };
  },
};

global.Meteor = { callAsync: jest.fn() };
global.$ = jest.fn(() => ({ datepicker: jest.fn() }));
global.FlowRouter = { getParam: jest.fn() };
global._ = require('underscore');

jest.mock('../../../../../client/app/reports/reportsContainer.html', () => ({}), { virtual: true });

jest.mock('../../../../../lib/collections/orgs', () => ({
   Orgs: {
    current: jest.fn(() => ({
      hasCustomization: jest.fn().mockReturnValue(false),
      busRoutes: false,
    })),
  },
}));

jest.mock('../../../../../lib/permissions', () => ({
  processPermissions: jest.fn(),
}));

jest.mock('meteor/ostrio:flow-router-extra', () => ({
  FlowRouter: { getParam: jest.fn() },
}));

import {BASE_REPORTS, CONDITIONAL_REPORTS} from "../../../../../lib/constants/reportConstants";
const reportsContainerModule = require('../../../../../client/app/reports/reportsContainer');
const { getAvailableReports, clearReportCache } = reportsContainerModule;

import { Orgs } from '../../../../../lib/collections/orgs';
import { processPermissions } from '../../../../../lib/permissions';

getAvailableReports.cache = { clear: jest.fn() };

jest.spyOn(reportsContainerModule, 'clearReportCache').mockImplementation(() => {
  getAvailableReports.cache.clear();
  const instance = Template.instance();
  if (instance && instance.enabledReports) {
    instance.enabledReports.set([]);
  }
  console.log('Report cache cleared');
});

describe('reportsContainer module', () => {
  let fakeInstance;

  beforeEach(() => {
    jest.clearAllMocks();
    reportsContainerModule.clearReportCache();

    fakeInstance = {
      enabledReports: new ReactiveVar([]),
      searchText: new ReactiveVar(''),
      isLoading: new ReactiveVar(true),
      autorun(fn) {
        fn();
      },
    };

    if (onCreatedCallback) {
      onCreatedCallback.call(fakeInstance);
    }
  });

  describe('getAvailableReports', () => {
    it('should return BASE_REPORTS plus the inverse conditional "Activities Report" when no customizations are enabled and ADP is disabled', async () => {
      Orgs.current.mockReturnValue({
        hasCustomization: jest.fn().mockReturnValue(false),
        busRoutes: false,
      });
      Meteor.callAsync.mockResolvedValue(false);
      processPermissions.mockReturnValue(false);

      const reports = await getAvailableReports();
      expect(reports.length).toBe(BASE_REPORTS.length + 1);

      BASE_REPORTS.forEach((baseReport) => {
        expect(reports).toContainEqual(expect.objectContaining({ _id: baseReport._id }));
      });

      const expectedExtra = CONDITIONAL_REPORTS.customizations['modules/curriculum/hidden']
        .find(r => r.name === 'Activities Report');
      expect(reports).toContainEqual(expect.objectContaining({
        name: 'Activities Report',
        _id: expectedExtra._id,
      }));
    });
  });
});