import { ReportSaveService } from '../../../lib/customReports/reportSaveService';
import { ReportSaves } from '../../../lib/collections/reportSaves';

// Mock the collections and Meteor
jest.mock('../../../lib/collections/reportSaves');
jest.mock('meteor/meteor', () => ({
    Error: jest.fn().mockImplementation((message) => ({ message }))
}));

describe('ReportSaveService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.restoreAllMocks();
    });

    describe('saveReport', () => {
        it('should save a report with the original name if not duplicate', async () => {
            const mockReport = { data: 'test' };
            const mockIndex = 'index123';
            const mockPersonId = 'user123';
            const mockName = 'Test Report';

            ReportSaves.insertAsync.mockResolvedValueOnce();
            jest.spyOn(ReportSaveService, 'getSavedReports').mockResolvedValueOnce([]);

            const result = await ReportSaveService.saveReport(mockReport, mockIndex, mockPersonId, mockName);

            expect(result).toBe(mockName);
            expect(ReportSaves.insertAsync).toHaveBeenCalledWith({
                personId: mockPersonId,
                name: mockName,
                reportObject: mockReport,
                index: mockIndex,
                createdOn: expect.any(Date)
            });
        });

        it('should save a report with a suffixed name if duplicate exists', async () => {
            const mockReport = { data: 'test' };
            const mockIndex = 'index123';
            const mockPersonId = 'user123';
            const mockName = 'Test Report';
            const existingReports = [{ name: 'Test Report' }];

            ReportSaves.insertAsync.mockResolvedValueOnce();
            jest.spyOn(ReportSaveService, 'getSavedReports').mockResolvedValueOnce(existingReports);

            const result = await ReportSaveService.saveReport(mockReport, mockIndex, mockPersonId, mockName);

            expect(result).toBe('Test Report (1)');
            expect(ReportSaves.insertAsync).toHaveBeenCalledWith({
                personId: mockPersonId,
                name: 'Test Report (1)',
                reportObject: mockReport,
                index: mockIndex,
                createdOn: expect.any(Date)
            });
        });

        it('should throw an error if unable to find a unique name', async () => {
            const mockReport = { data: 'test' };
            const mockIndex = 'index123';
            const mockPersonId = 'user123';
            const mockName = 'Test Report';

            // Mock all possible names being taken
            const existingReports = [];
            for (let i = 1; i < 1000; i++) {
                existingReports.push({ name: `Test Report (${i})` });
            }
            existingReports.push({ name: 'Test Report' });

            jest.spyOn(ReportSaveService, 'getSavedReports').mockResolvedValueOnce(existingReports);

            await expect(ReportSaveService.saveReport(mockReport, mockIndex, mockPersonId, mockName))
                .rejects.toEqual(new Error('Unable to find a unique name'));
        });
    });

    describe('getSavedReports', () => {
        it('should return saved reports for a person with person details', async () => {
            const mockPersonId = 'user123';
            const mockReports = [
                {
                    _id: 'report1',
                    personId: mockPersonId,
                    name: 'Report 1',
                    fullName: 'John Doe'
                }
            ];

            const mockAggregate = {
                toArray: jest.fn().mockResolvedValue(mockReports)
            };

            ReportSaves.aggregate.mockReturnValue(mockAggregate);

            const result = await ReportSaveService.getSavedReports(mockPersonId);

            expect(result).toEqual([
                {
                    _id: 'report1',
                    personId: mockPersonId,
                    name: 'Report 1',
                    fullName: 'John Doe',
                }
            ]);
            expect(ReportSaves.aggregate).toHaveBeenCalledWith([
                {
                    $match: {
                        personId: mockPersonId
                    }
                },
                {
                    $lookup: {
                        from: "people",
                        localField: "personId",
                        foreignField: "_id",
                        as: "person"
                    }
                },
                {
                    $unwind: "$person"
                },
                {
                    $project: {
                        _id: 1,
                        personId: 1,
                        index: 1,
                        createdOn: 1,
                        name: 1,
                        fullName: {
                            $concat: ["$person.firstName", " ", "$person.lastName"]
                        }
                    }
                }
            ]);
        });
    });

    describe('getOneSavedReport', () => {
        it('should return a single saved report', async () => {
            const mockReportId = 'report123';
            const mockReport = { _id: mockReportId, name: 'Test Report' };

            ReportSaves.findOneAsync.mockResolvedValueOnce(mockReport);

            const result = await ReportSaveService.getOneSavedReport(mockReportId);

            expect(result).toEqual(mockReport);
            expect(ReportSaves.findOneAsync).toHaveBeenCalledWith({ _id: mockReportId });
        });

        it('should return null if report not found', async () => {
            const mockReportId = 'nonexistent';

            ReportSaves.findOneAsync.mockResolvedValueOnce(null);

            const result = await ReportSaveService.getOneSavedReport(mockReportId);

            expect(result).toBeNull();
            expect(ReportSaves.findOneAsync).toHaveBeenCalledWith({ _id: mockReportId });
        });
    });
    describe('deleteSavedReport', () => {
        it('should delete a report successfully', async () => {
            const mockReportId = 'report123';
            ReportSaves.removeAsync.mockResolvedValueOnce(1); // 1 document deleted

            await ReportSaveService.deleteSavedReport(mockReportId);

            expect(ReportSaves.removeAsync).toHaveBeenCalledWith({ _id: mockReportId });
        });
    });

    describe('copySavedReport', () => {
        const mockOriginalReport = {
            _id: 'original123',
            reportObject: { data: 'test' },
            index: 'index123',
            personId: 'user123',
            name: 'Original Report'
        };

        it('should successfully copy a report with a new name', async () => {
            const mockNewName = 'Copied Report';

            // Mock the dependencies
            ReportSaveService.getOneSavedReport = jest.fn()
                .mockResolvedValueOnce(mockOriginalReport);
            ReportSaveService.saveReport = jest.fn()
                .mockResolvedValueOnce(mockNewName);

            const result = await ReportSaveService.copySavedReport(
                mockOriginalReport._id,
                mockNewName
            );

            expect(result).toBe(mockNewName);
            expect(ReportSaveService.getOneSavedReport).toHaveBeenCalledWith(mockOriginalReport._id);
            expect(ReportSaveService.saveReport).toHaveBeenCalledWith(
                mockOriginalReport.reportObject,
                mockOriginalReport.index,
                mockOriginalReport.personId,
                mockNewName
            );
        });

        it('should throw an error if original report not found', async () => {
            const mockReportId = 'nonexistent';

            ReportSaveService.getOneSavedReport = jest.fn()
                .mockResolvedValueOnce(null);

            await expect(ReportSaveService.copySavedReport(mockReportId, 'New Name'))
                .rejects.toEqual(new Error('Report not found'));
        });
    });
});