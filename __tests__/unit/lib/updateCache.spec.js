import { Cache } from "../../../lib/util/cacheUtils.js";
import { CACHE_KEYS } from "../../../lib/constants/cacheConstants.js";
import { updateCacheOnPersonChange, updateCacheOnOrgChange } from "../../../lib/updateCache.js";

describe('update cache on collection value change', () => {
    it('check if cache got deleted or not if type and superAdmin is updated', () => {
        Cache.set("123-"+CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT, true);
        updateCacheOnPersonChange(["type", "superAdmin"],"123");
        expect(Cache.get("123-"+CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT)).toBe(undefined);
    });
    it('check if cache got deleted or not if type and superAdmin is not updated', () => {
        Cache.set(CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT, true);
        updateCacheOnPersonChange([],"123");
        expect(Cache.get(CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT)).toBe(true);
    });
    it('check if cache got deleted or not if valueOverrides and customizations is updated', () => {
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETPERSON, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_INVOICES, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETGROUP, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TGROUP, "test");
        updateCacheOnOrgChange(["valueOverrides", "customizations"], "123");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETPERSON)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_INVOICES)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETGROUP)).toBe(undefined);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TGROUP)).toBe(undefined);
    });
    it('check if cache got deleted or not if valueOverrides and customizations is not updated', () => {
        Cache.set("123-"+CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT, true);
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETPERSON, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_INVOICES, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETGROUP, "test");
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TGROUP, "test");
        updateCacheOnOrgChange([], "123");
        expect(Cache.get("123-"+CACHE_KEYS.PROCESSPERMISSIONS_PEOPLE_MOVEMENT_EDIT)).toBe(true);
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_REQUIRERESERVATION)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_NOTIFYWITHOUTRESERVATION)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_STAFFLOCKDOWN)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_CHECKINORDER)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_FAMILYCHECKIN_ENABLED)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_BILLING_CONFIGURATION_PUNCHCARDS)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWTRANSPORTATION)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_SHOWHEALTHCHECK)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETPERSON)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_INVOICES)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TARGETGROUP)).toBe("test");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_MOMENTS_CHECKIN_TGROUP)).toBe("test");
    });

    it('set multiple org key and for pass orgid, cache should be deleted for respective org', () => {
        Cache.set("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED, true);
        Cache.set("456-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED, true);
        updateCacheOnOrgChange(["valueOverrides", "customizations"], "123");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED)).toBe(undefined);
        expect(Cache.get("456-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED)).toBe(true);
        updateCacheOnOrgChange(["valueOverrides", "customizations"], "456");
        expect(Cache.get("123-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED)).toBe(undefined);
        expect(Cache.get("456-"+CACHE_KEYS.ORGS_CUSTOMIZATION_PEOPLE_STAFFREQUIREDPINCODECHECKIN_ENABLED)).toBe(undefined);
    });
    
});