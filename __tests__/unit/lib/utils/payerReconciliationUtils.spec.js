import expect from 'expect';
import moment from 'moment-timezone';
import { describe, beforeEach, afterEach, it } from '@jest/globals';
import { PayerReconciliationUtils } from '../../../../lib/util/payerReconciliationUtils';
import { Invoices } from '../../../../lib/collections/invoices';
import { People } from '../../../../lib/collections/people';
import { BillingUtils } from '../../../../lib/util/billingUtils';
import { Log } from '../../../../lib/util/log';
import { PayerReconciliationBatches } from '../../../../lib/collections/payerReconciliationBatches';
import { PayerFunds } from '../../../../lib/collections/payerFunds';

// Mock dependencies
jest.mock('../../../../lib/collections/invoices');
jest.mock('../../../../lib/collections/people');
jest.mock('../../../../lib/util/billingUtils');
jest.mock('../../../../lib/util/log');
jest.mock('../../../../lib/collections/payerReconciliationBatches', () => ({
    PayerReconciliationBatches: {
        findOneAsync: jest.fn(),
        find: jest.fn(() => ({
            fetchAsync: jest.fn()
        })),
        updateAsync: jest.fn()
    }
}));
jest.mock('../../../../lib/collections/payerFunds', () => ({
    PayerFunds: {
        find: jest.fn(() => ({
            fetchAsync: jest.fn()
        }))
    }
}));

describe('PayerReconciliationUtils', () => {
    const timezone = 'America/New_York';
    
    beforeEach(() => {
        jest.clearAllMocks();
        moment.tz.setDefault(timezone);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    // Setup common test data
    const currentOrg = {
        _id: 'org123',
        getTimezone: jest.fn().mockReturnValue(timezone)
    };
    const startDateString = '2023-01-01';
    const endDateString = '2023-01-31';
    const startDateValue = moment(startDateString);
    const endDateValue = moment(endDateString);
    const payer = 'payer123';
    const options = {
        payer,
        hidePaidItems: false
    };

    describe('buildPayerInvoiceQuery', () => {
        it('should build a query with the correct structure', () => {
            const result = PayerReconciliationUtils.buildPayerInvoiceQuery(
                currentOrg, payer, startDateString, endDateString, startDateValue, endDateValue
            );

            expect(result.orgId).toBe(currentOrg._id);
            expect(result.$or).toHaveLength(4);
            expect(result.voidedAt).toEqual({ $exists: false });

            // Check payer in all $or conditions
            result.$or.forEach(condition => {
                expect(condition.lineItems.$elemMatch.appliedDiscounts.$elemMatch.source).toBe(payer);
            });
        });
    });

    describe('processAllPersons', () => {
        it('should process empty input correctly', async () => {
            const invoicesGroupedByPerson = {};

            const result = await PayerReconciliationUtils.processAllPersons(
                invoicesGroupedByPerson, startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result).toEqual([]);
            expect(Log.info).toHaveBeenCalledWith(expect.stringContaining('Starting payer reconciliation'), expect.any(Object));
        });

        it('should process multiple persons correctly', async () => {
            const person1 = { _id: 'person1', firstName: 'John', lastName: 'Doe' };
            const person2 = { _id: 'person2', firstName: 'Jane', lastName: 'Smith' };

            const invoices1 = [{
                _id: 'invoice1',
                lineItems: [{ type: 'plan', frequency: 'weekly_scheduled_daily' }]
            }];

            const invoices2 = [{
                _id: 'invoice2',
                lineItems: [{ type: 'plan', frequency: 'monthly' }]
            }];

            const invoicesGroupedByPerson = {
                'person1': invoices1,
                'person2': invoices2
            };

            People.findOneAsync.mockImplementation((id) => {
                if (id === 'person1') return person1;
                if (id === 'person2') return person2;
                return null;
            });

            // Mock our processing methods
            const processPersonInvoicesSpy = jest.spyOn(PayerReconciliationUtils, 'processPersonInvoices')
                .mockImplementation((invoices, personId, personName) => {
                    return {
                        personId,
                        name: personName,
                        matchedLineItems: [],
                        hasDays: false,
                        totalOriginalAmount: 100,
                        totalPayerOpenAmount: 80,
                        totalCopays: 20,
                        totalCopayCount: 1,
                        totalDays: 5
                    };
                });

            const result = await PayerReconciliationUtils.processAllPersons(
                invoicesGroupedByPerson, startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result).toHaveLength(2);
            expect(result[0].personId).toBe('person1');
            expect(result[0].name).toBe('Doe, John');
            expect(result[1].personId).toBe('person2');
            expect(result[1].name).toBe('Smith, Jane');

            expect(People.findOneAsync).toHaveBeenCalledTimes(2);
            expect(processPersonInvoicesSpy).toHaveBeenCalledTimes(2);

            processPersonInvoicesSpy.mockRestore();
        });

        it('should handle missing persons gracefully', async () => {
            const invoicesGroupedByPerson = {
                'missing1': [{ _id: 'invoice1' }]
            };

            People.findOneAsync.mockResolvedValue(null);

            const result = await PayerReconciliationUtils.processAllPersons(
                invoicesGroupedByPerson, startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result).toHaveLength(0);
            expect(Log.error).toHaveBeenCalledWith(expect.stringContaining('Person not found'), expect.any(Object));
        });

        it('should handle errors during processing', async () => {
            const invoicesGroupedByPerson = {
                'error1': [{ _id: 'invoice1' }]
            };

            People.findOneAsync.mockRejectedValue(new Error('Test error'));

            const result = await PayerReconciliationUtils.processAllPersons(
                invoicesGroupedByPerson, startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result).toHaveLength(0);
            expect(Log.error).toHaveBeenCalledWith(expect.stringContaining('Failed to process person'), expect.any(Object));
        });
    });

    describe('processPersonInvoices', () => {
        it('should process empty invoices correctly', () => {
            const invoices = [];
            const personId = 'person123';
            const personName = 'Doe, John';

            const result = PayerReconciliationUtils.processPersonInvoices(
                invoices, personId, personName, startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result.personId).toBe(personId);
            expect(result.name).toBe(personName);
            expect(result.matchedLineItems).toEqual([]);
            expect(result.hasDays).toBe(false);
            expect(result.totalOriginalAmount).toBe(0);
            expect(result.totalPayerOpenAmount).toBe(0);
            expect(result.totalCopays).toBe(0);
            expect(result.totalCopayCount).toBe(0);
            expect(result.totalDays).toBe(0);
        });

        it('should process invoices with weekly scheduled items', () => {
            const invoices = [
                {
                    _id: 'invoice123',
                    invoiceDate: '01/15/2023',
                    lineItems: [{ type: 'plan', frequency: 'weekly_scheduled_daily' }]
                }
            ];

            const personId = 'person123';
            const personName = 'Doe, John';

            // Mock the weekly items processing
            const processWeeklyItemsSpy = jest.spyOn(PayerReconciliationUtils, 'processWeeklyScheduledItems')
                .mockReturnValue({
                    items: [{ _id: 'item1', invoiceId: 'invoice123' }],
                    totalOriginalAmount: 100,
                    totalPayerOpenAmount: 80,
                    totalCopays: 20,
                    totalCopayCount: 1,
                    totalDays: 5
                });

            const result = PayerReconciliationUtils.processPersonInvoices(
                invoices, personId, personName, startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result.personId).toBe(personId);
            expect(result.name).toBe(personName);
            expect(result.matchedLineItems).toEqual([{ _id: 'item1', invoiceId: 'invoice123' }]);
            expect(result.totalOriginalAmount).toBe(100);
            expect(result.totalPayerOpenAmount).toBe(80);
            expect(result.totalCopays).toBe(20);
            expect(result.totalCopayCount).toBe(1);
            expect(result.totalDays).toBe(5);

            expect(processWeeklyItemsSpy).toHaveBeenCalledTimes(1);
            processWeeklyItemsSpy.mockRestore();
        });

        it('should process invoices with other plan items', () => {
            const invoices = [
                {
                    _id: 'invoice123',
                    invoiceDate: '01/15/2023',
                    lineItems: [{ type: 'plan', frequency: 'monthly' }]
                }
            ];

            const personId = 'person123';
            const personName = 'Doe, John';

            // Mock the weekly items processing (empty result)
            const processWeeklyItemsSpy = jest.spyOn(PayerReconciliationUtils, 'processWeeklyScheduledItems')
                .mockReturnValue({
                    items: [],
                    totalOriginalAmount: 0,
                    totalPayerOpenAmount: 0,
                    totalCopays: 0,
                    totalCopayCount: 0,
                    totalDays: 0
                });

            // Mock the other items processing
            const processOtherItemsSpy = jest.spyOn(PayerReconciliationUtils, 'processOtherPlanItems')
                .mockReturnValue({
                    lineItem: { _id: 'item2', invoiceId: 'invoice123' },
                    totalOriginalAmount: 200,
                    totalPayerOpenAmount: 150,
                    totalCopays: 50,
                    totalCopayCount: 1
                });

            const result = PayerReconciliationUtils.processPersonInvoices(
                invoices, personId, personName, startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result.personId).toBe(personId);
            expect(result.name).toBe(personName);
            expect(result.matchedLineItems).toEqual([{ _id: 'item2', invoiceId: 'invoice123' }]);
            expect(result.totalOriginalAmount).toBe(200);
            expect(result.totalPayerOpenAmount).toBe(150);
            expect(result.totalCopays).toBe(50);
            expect(result.totalCopayCount).toBe(1);

            expect(processWeeklyItemsSpy).toHaveBeenCalledTimes(1);
            expect(processOtherItemsSpy).toHaveBeenCalledTimes(1);

            processWeeklyItemsSpy.mockRestore();
            processOtherItemsSpy.mockRestore();
        });
    });

    describe('processWeeklyScheduledItems', () => {
        beforeEach(() => {
            // Setup mock for BillingUtils.roundToTwo
            BillingUtils.roundToTwo.mockImplementation(val => Math.round(val * 100) / 100);

            // Setup mock for Invoices.payerDistributionOfDiscount
            Invoices.payerDistributionOfDiscount.mockImplementation(({ discount }) => {
                return [
                    {
                        day: '2023-01-02',
                        dailyCopayRate: discount.type === 'reimbursable-with-copay' ? 10 : 0,
                        dailySubsidyRate: discount.type === 'reimbursable-with-copay' ? 40 : 50
                    },
                    {
                        day: '2023-01-03',
                        dailyCopayRate: discount.type === 'reimbursable-with-copay' ? 10 : 0,
                        dailySubsidyRate: discount.type === 'reimbursable-with-copay' ? 40 : 50
                    }
                ];
            });
        });

        it('should handle empty line items', () => {
            const invoice = {
                _id: 'invoice123',
                lineItems: [],
                credits: []
            };

            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                invoice, moment(), startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result.items).toEqual([]);
            expect(result.totalOriginalAmount).toBe(0);
            expect(result.totalPayerOpenAmount).toBe(0);
            expect(result.totalCopays).toBe(0);
            expect(result.totalCopayCount).toBe(0);
            expect(result.totalDays).toBe(0);
        });

        it('should calculate copay amounts correctly', () => {
            const invoice = {
                _id: 'invoice123',
                invoiceNumber: 'INV123',
                invoiceDate: '01/02/2023',
                credits: [],
                lineItems: [
                    {
                        _id: 'li1',
                        type: 'plan',
                        frequency: 'weekly_scheduled_daily',
                        price: 50,
                        enrolledPlan: {
                            enrollmentDate: startDateValue.valueOf()
                        },
                        appliedDiscounts: [
                            {
                                type: 'reimbursable-with-copay',
                                source: payer,
                                amount: 200,
                                coveredDays: ['2023-01-02', '2023-01-03', '2023-01-04'],
                                voidedAt: undefined
                            }
                        ]
                    }
                ]
            };

            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                invoice, moment('2023-01-02'), startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result.items).toHaveLength(1);
            expect(result.items[0].copayAmount).toBe(20); // 10 per day × 2 days
            expect(result.totalCopays).toBe(20);
            expect(result.totalCopayCount).toBe(1);
        });

        it('should handle no discounts on line items gracefully', () => {
            const invoice = {
                _id: 'invoice123',
                invoiceNumber: 'INV123',
                invoiceDate: '01/02/2023',
                credits: [],
                lineItems: [
                    {
                        _id: 'li1',
                        type: 'plan',
                        frequency: 'weekly_scheduled_daily',
                        price: 50,
                        enrolledPlan: {
                            enrollmentDate: startDateValue.valueOf()
                        }
                    }
                ]
            };

            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                invoice, moment('2023-01-02'), startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result.items).toHaveLength(1);
            expect(result.items[0].copayAmount).toBe(undefined);
            expect(result.totalCopays).toBe(0);
            expect(result.totalCopayCount).toBe(0);
        });

        it('should handle no credits array gracefully', () => {
            const invoice = {
                _id: 'invoice123',
                invoiceNumber: 'INV123',
                invoiceDate: '01/02/2023',
                lineItems: [
                    {
                        _id: 'li1',
                        type: 'plan',
                        frequency: 'weekly_scheduled_daily',
                        price: 50,
                        enrolledPlan: {
                            enrollmentDate: startDateValue.valueOf()
                        },
                        appliedDiscounts: [
                            {
                                type: 'reimbursable-with-copay',
                                source: payer,
                                amount: 200,
                                coveredDays: ['2023-01-02', '2023-01-03', '2023-01-04'],
                                voidedAt: undefined
                            }
                        ]
                    }
                ]
            };

            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                invoice, moment('2023-01-02'), startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result.items).toHaveLength(1);
            expect(result.items[0].copayAmount).toBe(20);
            expect(result.totalCopays).toBe(20);
            expect(result.totalCopayCount).toBe(1);
        });
    });

    describe('processWeeklyScheduledItems - Edge Cases', () => {
        beforeEach(() => {
            BillingUtils.roundToTwo.mockImplementation(val => Math.round(val * 100) / 100);

            Invoices.payerDistributionOfDiscount.mockImplementation(({ discount }) => {
                return discount.coveredDays?.map(day => ({
                    day,
                    dailyCopayRate: discount.type === 'reimbursable-with-copay' ? 10 : 0,
                    dailySubsidyRate: discount.type === 'reimbursable-with-copay' ? 40 : 50
                })) || [];
            });
        });

        it('should handle an empty invoice object gracefully', () => {
            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                {}, moment(), '2023-01-01', '2023-01-07', moment('2023-01-01'), moment('2023-01-07'), {}
            );

            expect(result.items).toEqual([]);
            expect(result.totalOriginalAmount).toBe(0);
            expect(result.totalPayerOpenAmount).toBe(0);
        });

        it('should handle missing coveredDays in appliedDiscounts', () => {
            const invoice = {
                _id: 'invoice123',
                lineItems: [
                    {
                        _id: 'li1',
                        type: 'plan',
                        frequency: 'weekly_scheduled_daily',
                        price: 50,
                        enrolledPlan: { enrollmentDate: '2023-01-01' },
                        appliedDiscounts: [
                            { type: 'reimbursable-with-copay', source: 'payer1', amount: 100 }
                        ]
                    }
                ],
                credits: []
            };

            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                invoice, moment('2023-01-02'), '2023-01-01', '2023-01-07', moment('2023-01-01'), moment('2023-01-07'), { payer: 'payer1' }
            );

            expect(result.items).toHaveLength(1);
            expect(result.items[0].discountLineItemDays).toHaveLength(0);
        });

        it('should exclude fully paid items when hidePaidItems is enabled', () => {
            const invoice = {
                _id: 'invoice123',
                lineItems: [
                    {
                        _id: 'li1',
                        type: 'plan',
                        frequency: 'weekly_scheduled_daily',
                        price: 50,
                        enrolledPlan: { enrollmentDate: '2023-01-01' },
                        appliedDiscounts: [
                            { type: 'reimbursable-with-copay', source: 'payer1', amount: 100, coveredDays: ['2023-01-02', '2023-01-03'], voidedAt: undefined }
                        ]
                    }
                ],
                credits: [
                    { creditReason: 'reimbursable', creditPayerSource: 'payer1', lineItemId: 'li1', coveredDays: ['2023-01-02', '2023-01-03'], voidedAt: undefined }
                ]
            };

            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                invoice, moment('2023-01-02'), '2023-01-01', '2023-01-07', moment('2023-01-01'), moment('2023-01-07'), { payer: 'payer1', hidePaidItems: true }
            );

            expect(result.items).toHaveLength(0);
        });

        it('should ignore voided discounts', () => {
            const invoice = {
                _id: 'invoice123',
                lineItems: [
                    {
                        _id: 'li1',
                        type: 'plan',
                        frequency: 'weekly_scheduled_daily',
                        price: 50,
                        enrolledPlan: { enrollmentDate: '2023-01-01' },
                        appliedDiscounts: [
                            { type: 'reimbursable', source: 'payer1', amount: 100, coveredDays: ['2023-01-02', '2023-01-03'], voidedAt: '2023-01-01' }
                        ]
                    }
                ],
                credits: []
            };

            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                invoice, moment('2023-01-02'), '2023-01-01', '2023-01-07', moment('2023-01-01'), moment('2023-01-07'), { payer: 'payer1' }
            );

            expect(result.items).toHaveLength(1);
            expect(result.items[0].discountLineItemDays).toHaveLength(0);
        });

        it('should return empty discountLineItemDays if payerDistributionOfDiscount returns empty array', () => {
            Invoices.payerDistributionOfDiscount.mockImplementation(() => []);

            const invoice = {
                _id: 'invoice123',
                lineItems: [
                    {
                        _id: 'li1',
                        type: 'plan',
                        frequency: 'weekly_scheduled_daily',
                        price: 50,
                        enrolledPlan: { enrollmentDate: '2023-01-01' },
                        appliedDiscounts: []
                    }
                ],
                credits: []
            };

            const result = PayerReconciliationUtils.processWeeklyScheduledItems(
                invoice, moment('2023-01-02'), '2023-01-01', '2023-01-07', moment('2023-01-01'), moment('2023-01-07'), { payer: 'payer1' }
            );

            expect(result.items).toHaveLength(1);
            expect(result.items[0].discountLineItemDays).toEqual([]);
        });
    });

    describe('processOtherPlanItems', () => {
        it('should handle copay calculations correctly', () => {
            const invoice = {
                _id: 'invoice123',
                invoiceNumber: 'INV123',
                originalAmount: 100,
                invoiceDate: '01/15/2023',
                totalAmountsForPayers: jest.fn().mockReturnValue([
                    { source: payer, amount: 80, openAmount: 80 }
                ]),
                lineItems: [{
                    _id: 'li1',
                    type: 'plan',
                    frequency: 'monthly',
                    enrolledPlan: { enrollmentDate: startDateValue.valueOf() },
                    appliedDiscounts: [{
                        type: 'reimbursable-with-copay',
                        source: payer,
                        voidedAt: undefined
                    }],
                    copayCount: 1,
                    copayAmount: 50
                }],
                credits: []
            };

            const result = PayerReconciliationUtils.processOtherPlanItems(
                invoice, moment('2023-01-15'), startDateString, endDateString, startDateValue, endDateValue, options
            );

            expect(result.lineItem.copayAmount).toBe(150);
            expect(result.totalCopays).toBe(150);
            expect(result.totalCopayCount).toBe(2);
        });
    });

    describe('generateReconciliationCsv', () => {
        beforeEach(() => {
            // Mock the necessary collections
            PayerReconciliationBatches.findOneAsync = jest.fn();
            Invoices.find = jest.fn();
            PayerFunds.find = jest.fn();
            People.findOneAsync = jest.fn();
            PayerReconciliationBatches.updateAsync = jest.fn();
            
            // Mock fetch methods
            Invoices.find.mockReturnValue({ fetchAsync: jest.fn().mockResolvedValue([]) });
            PayerFunds.find.mockReturnValue({ fetchAsync: jest.fn().mockResolvedValue([]) });
        });
        
        it('should return existing CSV content if available', async () => {
            // Mock batch with existing CSV content
            PayerReconciliationBatches.findOneAsync.mockResolvedValue({
                _id: 'batch123',
                csvContent: 'existing,csv,content'
            });
            
            const result = await PayerReconciliationUtils.generateReconciliationCsv({
                batchId: 'TEST-Batch-2023',
                orgId: 'org123'
            });
            
            expect(result.csvContent).toBe('existing,csv,content');
            expect(Log.info).toHaveBeenCalledWith(`Using stored CSV content for batch TEST-Batch-2023`);
        });
        
        it('should generate CSV from batch allocations if available', async () => {
            // Mock batch with allocations
            PayerReconciliationBatches.findOneAsync.mockResolvedValue({
                _id: 'batch123',
                batchLabel: 'TEST-Batch-2023',
                date: new Date('2023-01-15').valueOf(),
                creatorName: 'Test User',
                totalAmount: 150.75,
                allocations: [
                    {
                        personName: 'Doe, John',
                        invoiceNumber: 'INV001',
                        coveredDaysLabel: '2023-01-01, 2023-01-02',
                        coveredDaysCount: 2,
                        amount: 100.50
                    },
                    {
                        personName: 'Smith, Jane',
                        invoiceNumber: 'INV002',
                        coveredDaysLabel: '2023-01-03',
                        coveredDaysCount: 1,
                        amount: 50.25
                    }
                ]
            });
            
            const result = await PayerReconciliationUtils.generateReconciliationCsv({
                batchId: 'TEST-Batch-2023',
                orgId: 'org123'
            });
            
            // Verify CSV content was generated correctly
            expect(result.csvContent).toContain('Batch ID,TEST-Batch-2023');
            expect(result.csvContent).toContain('"Doe, John",INV001,"2023-01-01, 2023-01-02",2,"$100.50"');
            expect(result.csvContent).toContain('"Smith, Jane",INV002,2023-01-03,1,"$50.25"');
            expect(result.csvContent).toContain('Total for batch,,,,"$150.75"');
        });
        
        it('should generate CSV from invoices and payer funds if allocations not available', async () => {
            // Mock batch without allocations
            PayerReconciliationBatches.findOneAsync.mockResolvedValue({
                _id: 'batch123',
                batchLabel: 'TEST-Batch-2023',
                date: new Date('2023-01-15').valueOf(),
                creatorName: 'Test User',
                totalAmount: 200.00
            });
            
            // Mock invoices and people
            const mockInvoices = [
                {
                    _id: 'invoice1',
                    personId: 'person1',
                    invoiceNumber: 'INV001',
                    credits: [
                        {
                            payerReconciliationBatchLabel: 'TEST-Batch-2023',
                            coveredDays: ['2023-01-01', '2023-01-02'],
                            amount: 100.00
                        }
                    ]
                }
            ];
            
            const mockPayerFunds = [
                {
                    payerSource: 'CCDF',
                    amount: 100.00,
                    payerReconciliationBatchLabel: 'TEST-Batch-2023'
                }
            ];
            
            Invoices.find.mockReturnValue({ fetchAsync: jest.fn().mockResolvedValue(mockInvoices) });
            PayerFunds.find.mockReturnValue({ fetchAsync: jest.fn().mockResolvedValue(mockPayerFunds) });
            People.findOneAsync.mockResolvedValue({ firstName: 'John', lastName: 'Doe' });
            
            const result = await PayerReconciliationUtils.generateReconciliationCsv({
                batchId: 'TEST-Batch-2023',
                orgId: 'org123'
            });
            
            // Verify CSV content was generated correctly
            expect(result.csvContent).toContain('Batch ID,TEST-Batch-2023');
            expect(result.csvContent).toContain('"Doe, John",INV001,"2023-01-01, 2023-01-02",2,"$100.00"');
            expect(result.csvContent).toContain('CCDF Non-Family Funds,,,,"$100.00"');
            expect(result.csvContent).toContain('Total for batch,,,,"$200.00"');
        });
        
        it('should handle S3 upload for large CSV files', async () => {
            // Mock batch without CSV content but with size indicator
            PayerReconciliationBatches.findOneAsync.mockResolvedValue({
                _id: 'batch123',
                batchLabel: 'TEST-Batch-2023',
                date: new Date('2023-01-15').valueOf(),
                creatorName: 'Test User',
                totalAmount: 200.00,
                csvContentSize: 200000, // Large size
                allocations: [
                    {
                        personName: 'Doe, John',
                        invoiceNumber: 'INV001',
                        coveredDaysLabel: '2023-01-01, 2023-01-02',
                        coveredDaysCount: 2,
                        amount: 200.00
                    }
                ]
            });
            
            // Mock S3 client
            const mockS3Client = {
                putObject: jest.fn().mockReturnValue({
                    promise: jest.fn().mockResolvedValue({})
                }),
                getSignedUrlPromise: jest.fn().mockResolvedValue('https://s3-signed-url.example.com/file.csv')
            };
            
            const result = await PayerReconciliationUtils.generateReconciliationCsv({
                batchId: 'TEST-Batch-2023',
                orgId: 'org123',
                s3Client: mockS3Client,
                awsBucket: 'test-bucket'
            });
            
            // Verify CSV content and URL
            expect(result.csvContent).toBeDefined();
            expect(result.csvUrl).toBe('https://s3-signed-url.example.com/file.csv');
            expect(mockS3Client.putObject).toHaveBeenCalledWith(expect.objectContaining({
                Bucket: 'test-bucket',
                ContentType: 'text/csv'
            }));
            expect(PayerReconciliationBatches.updateAsync).toHaveBeenCalledWith(
                { _id: 'batch123' },
                { $set: { csvUrl: 'https://s3-signed-url.example.com/file.csv' } }
            );
        });
        
        it('should handle errors gracefully', async () => {
            PayerReconciliationBatches.findOneAsync.mockRejectedValue(new Error('Database error'));
            
            // Just check that the error is thrown
            await expect(PayerReconciliationUtils.generateReconciliationCsv({
                batchId: 'TEST-Batch-2023',
                orgId: 'org123'
            })).rejects.toThrow();
        });
    });
});