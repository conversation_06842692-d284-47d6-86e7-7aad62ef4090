import { MiscUtils } from '../../../../lib/util/miscUtils.js';
import { mockRandom } from '../../../helpers/randomMock';
import { afterAll, beforeEach, jest, expect } from '@jest/globals';
import { ITEM_TYPE, PLAN_TYPE, SCALED_WEEKLY_PLAN } from '../../../../lib/constants/billingConstants';
import currency from 'currency.js';

describe('MiscUtils', () => {
    it('generates a range of numbers', () => {
        expect(MiscUtils.numberRange(5, 12)).toStrictEqual([5, 6, 7, 8, 9, 10, 11, 12]);
        expect(MiscUtils.numberRange(7, 7)).toStrictEqual([7]);
    });

    describe('comparePlans method', () => {
        // Helper function to create a mock plan with serviceDates
        const createMockPlan = (startDate, endDate, description) => {
            return {
                serviceDates: {
                    startDate,
                    endDate
                },
                description
            };
        };

        it('should correctly compare plans based on start date', () => {
            const plan1 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');
            const plan2 = createMockPlan('2023-02-01', '2023-02-15', 'Plan B');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(-1); // Plan A should come before Plan B
        });

        it('should correctly compare plans based on end date when start dates are the same', () => {
            const plan1 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');
            const plan2 = createMockPlan('2023-01-01', '2023-01-30', 'Plan B');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(-1); // Plan A should come before Plan B
        });

        it('should correctly compare plans based on description when start and end dates are the same', () => {
            const plan1 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');
            const plan2 = createMockPlan('2023-01-01', '2023-01-15', 'Plan B');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(-1); // Plan A should come before Plan B alphabetically
        });

        it('should return 0 when both plans have no service dates', () => {
            const plan1 = createMockPlan(null, null, 'Plan A');
            const plan2 = createMockPlan(null, null, 'Plan B');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(0); // Both plans are considered equal
        });

        it('should correctly compare plans with same start and end dates and different descriptions', () => {
            const plan1 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');
            const plan2 = createMockPlan('2023-01-01', '2023-01-15', 'Plan B');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(-1); // Plan A should come before Plan B alphabetically
        });

        it('should correctly compare plans with same start and end dates and same descriptions', () => {
            const plan1 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');
            const plan2 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(0); // Both plans are considered equal
        });

        it('should correctly compare plans with same start dates and different end dates', () => {
            const plan1 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');
            const plan2 = createMockPlan('2023-01-01', '2023-01-30', 'Plan B');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(-1); // Plan A should come before Plan B
        });

        it('should correctly compare plans with same end dates and different start dates', () => {
            const plan1 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');
            const plan2 = createMockPlan('2023-02-01', '2023-01-15', 'Plan B');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(-1); // Plan A should come before Plan B
        });

        it('should return 1 when plan with service dates comes after plan without service dates', () => {
            const plan1 = createMockPlan('2023-01-01', '2023-01-15', 'Plan A');
            const plan2 = createMockPlan(null, null, 'Plan B');

            const result = MiscUtils.comparePlans(plan1, plan2);

            expect(result).toBe(-1); // Plan B (without service dates) should come after Plan A
        });
    })

    it('grabs the selected options from a select node', () => {
        const node = {
            options: [
                { value: '1', selected: true },
                { value: '2', selected: false },
                { value: '3', selected: true },
                { value: '4', selected: false }
            ]
        }
        expect(MiscUtils.getMultiselectValues(node)).toStrictEqual(['1', '3']);
    });

    describe('setDocumentId', () => {
        afterAll(() => {
            jest.clearAllMocks();
        });
        beforeEach(() => {
            mockRandom();
        });
        it('sets the document _id', () => {
            const doc = {};
            MiscUtils.setDocumentId(doc);
            expect(doc).toStrictEqual({ _id: expect.any(String) });
        });
        it('does not overwrite the document _id', () => {
            const doc = { _id: 'docId' };
            MiscUtils.setDocumentId(doc);
            expect(doc).toStrictEqual({ _id: 'docId' });
        });
        it('allows setting a different identifier', () => {
            const doc = { _id: 'docId' };
            MiscUtils.setDocumentId(doc, 'uid');
            expect(doc).toStrictEqual({ _id: 'docId', uid: expect.any(String) });
        });
    });

    describe('applyDiscounts', () => {
        it('applies a percentage discount correctly', () => {
            const amount = 100.00;
            const allocations = [{ amount: 10, amountType: 'percent' }];

            const result = MiscUtils.applyDiscounts(amount, allocations);

            expect(result).toBe(90.00);
        });

        it('applies a dollar discount correctly', () => {
            const amount = 100.00;
            const allocations = [{ amount: 10, amountType: 'dollars' }];

            const result = MiscUtils.applyDiscounts(amount, allocations);

            expect(result).toBe(90.00);
        });

        it('does not reduce amount below zero', () => {
            const amount = 10.00;
            const allocations = [{ amount: 20, amountType: 'dollars' }];

            const result = MiscUtils.applyDiscounts(amount, allocations);

            expect(result).toBe(0.00);
        });

        it('applies multiple discounts correctly', () => {
            const amount = 100.00;
            const allocations = [
                { amount: 10, amountType: 'percent' },
                { amount: 5, amountType: 'dollars' }
            ];

            const result = MiscUtils.applyDiscounts(amount, allocations);

            expect(result).toBe(85.00);
        });

        it('handles empty allocations array', () => {
            const amount = 100.00;
            const allocations = [];

            const result = MiscUtils.applyDiscounts(amount, allocations);

            expect(result).toBe(100.00);
        });

        it('handles missing amount in allocation', () => {
            const amount = 100.00;
            const allocations = [{ amountType: 'percent' }];

            const result = MiscUtils.applyDiscounts(amount, allocations);

            expect(result).toBe(100.00);
        });

        it('applies single-use discount only on first week', () => {
            const amount = 100.00;
            const allocations = [
                { amount: 10, amountType: 'percent', isSingleUse: true }
            ];

            // First week should apply discount
            const firstWeekResult = MiscUtils.applyDiscounts(amount, allocations, true);
            expect(firstWeekResult).toBe(90.00);

            // Subsequent weeks should not apply discount
            const subsequentWeekResult = MiscUtils.applyDiscounts(amount, allocations, false);
            expect(subsequentWeekResult).toBe(100.00);
        });
    });

    describe('getSelectiveWeekRange', () => {
        it('returns correct min and max amounts without discounts', () => {
            const plans = [
                {
                    selectedWeeks: [0, 1],
                    details: {
                        selectiveWeekAmounts: ["100.00", "150.00"]
                    }
                },
                {
                    selectedWeeks: [0],
                    details: {
                        selectiveWeekAmounts: ["80.00"]
                    }
                }
            ];

            const result = MiscUtils.getSelectiveWeekRange(plans);

            expect(result).toEqual({ min: 80.00, max: 150.00 });
        });

        it('applies discounts correctly and returns the adjusted min and max amounts', () => {
            const plans = [
                {
                    selectedWeeks: [0, 1],
                    details: {
                        selectiveWeekAmounts: ["100.00", "150.00"]
                    },
                    allocations: [
                        { amount: 10, amountType: 'percent' }
                    ]
                },
                {
                    selectedWeeks: [0],
                    details: {
                        selectiveWeekAmounts: ["80.00"]
                    },
                    allocations: [
                        { amount: 5, amountType: 'dollars' }
                    ]
                }
            ];

            const result = MiscUtils.getSelectiveWeekRange(plans, true);

            expect(result).toEqual({ min: 75.00, max: 135.00 });
        });

        it('returns null if no valid amounts are found', () => {
            const plans = [
                {
                    selectedWeeks: [],
                    details: {
                        selectiveWeekAmounts: []
                    }
                },
                {
                    selectedWeeks: [],
                    amount: undefined
                }
            ];

            const result = MiscUtils.getSelectiveWeekRange(plans);

            expect(result).toBeNull();
        });

        it('handles a single plan with amount and no selected weeks', () => {
            const plans = [
                {
                    amount: 200.00
                }
            ];

            const result = MiscUtils.getSelectiveWeekRange(plans);

            expect(result).toEqual({ min: 200.00, max: 200.00 });
        });

        it('applies discounts to single plan correctly', () => {
            const plans = [
                {
                    amount: 200.00,
                    allocations: [
                        { amount: 10, amountType: 'percent' }
                    ]
                }
            ];

            const result = MiscUtils.getSelectiveWeekRange(plans, true);

            expect(result).toEqual({ min: 180.00, max: 180.00 });
        });

        it('applies single-use discounts only to the first selected week', () => {
            const plans = [
                {
                    selectedWeeks: [0, 1],
                    details: {
                        selectiveWeekAmounts: ["100.00", "150.00"]
                    },
                    allocations: [
                        { amount: 10, amountType: 'percent', isSingleUse: true }
                    ]
                }
            ];

            const result = MiscUtils.getSelectiveWeekRange(plans, true);

            // First week (100.00 - 10%) = 90.00, Second week = 150.00 (no discount applied)
            expect(result).toEqual({ min: 90.00, max: 150.00 });
        });
    });

    describe('MiscUtils.sortGroupedWhitelist', () => {
        test('should sort people within each group alphabetically', () => {
            const input = [
                { id: 'separator-checked-in-same-group', value: 'Checked In: Group 1', type: 'separator' },
                { id: 'person1', value: 'Charlie', type: 'person' },
                { id: 'person2', value: 'Alice', type: 'person' },
                { id: 'person3', value: 'Bob', type: 'person' },
                { id: 'separator-checked-in-other-groups', value: 'Checked In: Group 2', type: 'separator' },
                { id: 'person4', value: 'Eve', type: 'person' },
                { id: 'person5', value: 'Dave', type: 'person' }
            ];

            const expectedOutput = [
                { id: 'separator-checked-in-same-group', value: 'Checked In: Group 1', type: 'separator' },
                { id: 'person2', value: 'Alice', type: 'person' },
                { id: 'person3', value: 'Bob', type: 'person' },
                { id: 'person1', value: 'Charlie', type: 'person' },
                { id: 'separator-checked-in-other-groups', value: 'Checked In: Group 2', type: 'separator' },
                { id: 'person5', value: 'Dave', type: 'person' },
                { id: 'person4', value: 'Eve', type: 'person' }
            ];

            const result = MiscUtils.sortGroupedWhitelist(input);
            expect(result).toEqual(expectedOutput);
        });

        test('should handle groups with no people correctly', () => {
            const input = [
                { id: 'separator-checked-in-same-group', value: 'Checked In: Group 1', type: 'separator' },
                { id: 'separator-checked-in-other-groups', value: 'Checked In: Group 2', type: 'separator' }
            ];

            const expectedOutput = [
                { id: 'separator-checked-in-same-group', value: 'Checked In: Group 1', type: 'separator' },
                { id: 'separator-checked-in-other-groups', value: 'Checked In: Group 2', type: 'separator' }
            ];

            const result = MiscUtils.sortGroupedWhitelist(input);
            expect(result).toEqual(expectedOutput);
        });

        test('should correctly sort a mixed list of people and groups', () => {
            const input = [
                { id: 'separator-checked-in-same-group', value: 'Checked In: Group 1', type: 'separator' },
                { id: 'person1', value: 'Charlie', type: 'person' },
                { id: 'person2', value: 'Alice', type: 'person' },
                { id: 'separator-checked-in-other-groups', value: 'Checked In: Group 2', type: 'separator' },
                { id: 'person3', value: 'Eve', type: 'person' },
                { id: 'person4', value: 'Dave', type: 'person' }
            ];

            const expectedOutput = [
                { id: 'separator-checked-in-same-group', value: 'Checked In: Group 1', type: 'separator' },
                { id: 'person2', value: 'Alice', type: 'person' },
                { id: 'person1', value: 'Charlie', type: 'person' },
                { id: 'separator-checked-in-other-groups', value: 'Checked In: Group 2', type: 'separator' },
                { id: 'person4', value: 'Dave', type: 'person' },
                { id: 'person3', value: 'Eve', type: 'person' }
            ];

            const result = MiscUtils.sortGroupedWhitelist(input);
            expect(result).toEqual(expectedOutput);
        });

        test('should handle an empty list without errors', () => {
            const input = [];
            const expectedOutput = [];
            const result = MiscUtils.sortGroupedWhitelist(input);
            expect(result).toEqual(expectedOutput);
        });

        test('should not reorder separators', () => {
            const input = [
                { id: 'separator-checked-in-group-1', value: 'Checked In: Group 1', type: 'separator' },
                { id: 'person1', value: 'Charlie', type: 'person' },
                { id: 'person2', value: 'Alice', type: 'person' },
                { id: 'separator-checked-in-group-2', value: 'Checked In: Group 2', type: 'separator' },
                { id: 'person3', value: 'Eve', type: 'person' },
                { id: 'person4', value: 'Dave', type: 'person' }
            ];

            const expectedOutput = [
                { id: 'separator-checked-in-group-1', value: 'Checked In: Group 1', type: 'separator' },
                { id: 'person2', value: 'Alice', type: 'person' },
                { id: 'person1', value: 'Charlie', type: 'person' },
                { id: 'separator-checked-in-group-2', value: 'Checked In: Group 2', type: 'separator' },
                { id: 'person4', value: 'Dave', type: 'person' },
                { id: 'person3', value: 'Eve', type: 'person' }
            ];

            const result = MiscUtils.sortGroupedWhitelist(input);
            expect(result).toEqual(expectedOutput);
        });
    });

    describe('getPriceRange', () => {
        let formatCurrencyMock;
        let twoMatchedPlansMock;

        beforeEach(() => {
            // Mock formatCurrency and twoMatchedPlans functions used in the getPriceRange method
            formatCurrencyMock = jest.spyOn(MiscUtils, 'formatCurrency').mockImplementation((amount) => currency(amount).format());
            twoMatchedPlansMock = jest.spyOn(MiscUtils, 'twoMatchedPlans').mockImplementation(() => []);
        });

        afterEach(() => {
            jest.restoreAllMocks(); // Reset all mocks to their original implementation
        });

        // Test cases for selective week amounts
        it('should return the price for a specific week if index is provided', () => {
            const plan = {
                type: ITEM_TYPE,
                details: { selectiveWeekAmounts: [50, 75, 100] }
            };
            const result = MiscUtils.getPriceRange(plan, [], [], [], 1); // Pass index 1
            expect(result).toBe('$75.00');
            expect(formatCurrencyMock).toHaveBeenCalledWith(75);
        });

        it('should return a single price if all selective week amounts are the same', () => {
            const plan = {
                type: ITEM_TYPE,
                details: { selectiveWeekAmounts: [50, 50, 50] }
            };
            const result = MiscUtils.getPriceRange(plan, [], [], []);
            expect(result).toBe('$50.00');
            expect(formatCurrencyMock).toHaveBeenCalledWith(50);
        });

        it('should return a price range if selective week amounts vary', () => {
            const plan = {
                type: ITEM_TYPE,
                details: { selectiveWeekAmounts: [50, 75, 100] }
            };
            const result = MiscUtils.getPriceRange(plan, [], [], []);
            expect(result).toBe('$50.00 - $100.00');
            expect(formatCurrencyMock).toHaveBeenCalledWith(50);
            expect(formatCurrencyMock).toHaveBeenCalledWith(100);
        });

        // Plan amount handling
        it('should return the plan amount if no selective week amounts are available', () => {
            const plan = {
                type: ITEM_TYPE,
                amount: 150
            };
            const result = MiscUtils.getPriceRange(plan, [], [], []);
            expect(result).toBe('$150.00');
            expect(formatCurrencyMock).toHaveBeenCalledWith(150);
        });

        it('should return scaled amounts range if available plans and bundles are not provided', () => {
            const plan = {
                type: PLAN_TYPE,
                frequency: SCALED_WEEKLY_PLAN,
                scaledAmounts: [100, 150, 200, 250, 300]
            };
            const result = MiscUtils.getPriceRange(plan, [], null, null);
            expect(result).toBe('$100.00 - $300.00');
            expect(formatCurrencyMock).toHaveBeenCalledWith(100);
            expect(formatCurrencyMock).toHaveBeenCalledWith(300);
        });

        it('should return plan amount if scaled amounts are not defined', () => {
            const plan = {
                type: PLAN_TYPE,
                frequency: SCALED_WEEKLY_PLAN,
                amount: 200,
            };
            const result = MiscUtils.getPriceRange(plan, [], null, null);
            expect(result).toBe('$200.00');
            expect(formatCurrencyMock).toHaveBeenCalledWith(200);
        });

        // Combined test for bundle pricing with two matched plans
        it('should handle bundle pricing correctly when exactly two plans in the bundle match the criteria', () => {
            const plan = { _id: 'JDq5rrSQL3ZsqRpYG', type: PLAN_TYPE, frequency: SCALED_WEEKLY_PLAN };
            const bundles = [
                {
                    _id: 'nRz3QQ9s8dzBrFdxi',
                    type: 'bundle',
                    plans: ['JDq5rrSQL3ZsqRpYG', 'kWgG67X4jKtjoFz6e'],
                    scaledAmounts: [
                        [100, 200, 300, 400, 500],
                        [200, 300, 400, 500, 600],
                        [300, 400, 500, 600, 700],
                        [400, 500, 600, 700, 800],
                        [500, 600, 700, 800, 900]
                    ]
                }
            ];
            const availablePlans = [{ _id: 'JDq5rrSQL3ZsqRpYG' }, { _id: 'kWgG67X4jKtjoFz6e' }];
            twoMatchedPlansMock.mockReturnValue(bundles);

            const result = MiscUtils.getPriceRange(plan, availablePlans, availablePlans, bundles);
            expect(result).toBe('$100.00 - $900.00');
            expect(formatCurrencyMock).toHaveBeenCalledWith(100);
            expect(formatCurrencyMock).toHaveBeenCalledWith(900);
        });

        // Additional edge case tests
        it('should return $0.00 if the plan has no amount or selective amounts', () => {
            const plan = {
                type: PLAN_TYPE,
                amount: null,
                scaledAmounts: []
            };
            const result = MiscUtils.getPriceRange(plan, [], [], []);
            expect(result).toBe('$0.00');
            expect(formatCurrencyMock).toHaveBeenCalledWith(0);
        });

        it('should handle empty or null inputs gracefully', () => {
            const plan = null;
            const result = MiscUtils.getPriceRange(plan, [], [], []);
            expect(result).toBe('$0.00');
        });
    });

    describe('isValidIndex', () => {
        it('should return true for a valid index', () => {
            expect(MiscUtils.isValidIndex(1, [10, 20, 30])).toBe(true);
        });

        it('should return false for an invalid index', () => {
            expect(MiscUtils.isValidIndex(3, [10, 20, 30])).toBe(false);
        });

        it('should return false for a null index', () => {
            expect(MiscUtils.isValidIndex(null, [10, 20, 30])).toBe(false);
        });
    });

    describe('convertNumericKeyedObjectToArray', () => {
        it('should correctly convert an object with only numeric keys to an array', () => {
            const testObject = {
                "0": {
                    "amount": 0,
                    "updatedAt": 1722885375860,
                    "updatedByPersonId": "Mb9qcdGBNWdvvSe3F",
                    "voidedAt": 1722885375860,
                    "voidedByPersonId": "Mb9qcdGBNWdvvSe3F"
                }
            };

            const result = MiscUtils.convertNumericKeyedObjectToArray(testObject);

            expect(result).toStrictEqual([
                {
                    "amount": 0,
                    "updatedAt": 1722885375860,
                    "updatedByPersonId": "Mb9qcdGBNWdvvSe3F",
                    "voidedAt": 1722885375860,
                    "voidedByPersonId": "Mb9qcdGBNWdvvSe3F"
                }
            ]);
        });
        it('should not convert an object with only non-numeric keys to an array', () => {
            const testObject = {
                "a": {
                    "amount": 0,
                    "updatedAt": 1722885375860,
                    "updatedByPersonId": "Mb9qcdGBNWdvvSe3F",
                    "voidedAt": 1722885375860,
                    "voidedByPersonId": "Mb9qcdGBNWdvvSe3F"
                }
            };

            const result = MiscUtils.convertNumericKeyedObjectToArray(testObject);

            expect(result).toStrictEqual({
                "a": {
                    "amount": 0,
                    "updatedAt": 1722885375860,
                    "updatedByPersonId": "Mb9qcdGBNWdvvSe3F",
                    "voidedAt": 1722885375860,
                    "voidedByPersonId": "Mb9qcdGBNWdvvSe3F"
                }
            });
        });
        it('should not convert an object with mixed numeric and non-numeric keys to an array', () => {
            const testObject = {
                "0": {
                    "amount": 0,
                    "updatedAt": 1722885375860,
                    "updatedByPersonId": "Mb9qcdGBNWdvvSe3F",
                    "voidedAt": 1722885375860,
                    "voidedByPersonId": "Mb9qcdGBNWdvvSe3F"
                },
                "a": {
                    "amount": 0,
                    "updatedAt": 1722885375860,
                    "updatedByPersonId": "Mb9qcdGBNWdvvSe3F",
                    "voidedAt": 1722885375860,
                    "voidedByPersonId": "Mb9qcdGBNWdvvSe3F"
                }
            };

            const result = MiscUtils.convertNumericKeyedObjectToArray(testObject);

            expect(result).toStrictEqual({
                "0": {
                    "amount": 0,
                    "updatedAt": 1722885375860,
                    "updatedByPersonId": "Mb9qcdGBNWdvvSe3F",
                    "voidedAt": 1722885375860,
                    "voidedByPersonId": "Mb9qcdGBNWdvvSe3F"
                },
                "a": {
                    "amount": 0,
                    "updatedAt": 1722885375860,
                    "updatedByPersonId": "Mb9qcdGBNWdvvSe3F",
                    "voidedAt": 1722885375860,
                    "voidedByPersonId": "Mb9qcdGBNWdvvSe3F"
                }
            });
        });
    });

    describe('isNotEmptyArray', () => {
        it('should return true for an array with elements', () => {
            expect(MiscUtils.isNotEmptyArray([1, 2, 3])).toBe(true);
            expect(MiscUtils.isNotEmptyArray(['a', 'b', 'c'])).toBe(true);
            expect(MiscUtils.isNotEmptyArray([{}])).toBe(true);
        });

        it('should return false for an empty array', () => {
            expect(MiscUtils.isNotEmptyArray([])).toBe(false);
        });

        it('should return false for non-array values', () => {
            expect(MiscUtils.isNotEmptyArray(123)).toBe(false);
            expect(MiscUtils.isNotEmptyArray('string')).toBe(false);
            expect(MiscUtils.isNotEmptyArray({})).toBe(false);
            expect(MiscUtils.isNotEmptyArray(true)).toBe(false);
        });

        it('should return false for null or undefined values', () => {
            expect(MiscUtils.isNotEmptyArray(null)).toBe(false);
            expect(MiscUtils.isNotEmptyArray(undefined)).toBe(false);
        });
    });
});
