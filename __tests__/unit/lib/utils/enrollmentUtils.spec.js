import { mockCollection } from '../../../helpers/collectionMock';
import { EnrollmentUtils } from '../../../../lib/util/enrollmentUtils';
import moment from 'moment-timezone';
import { AVAILABILITIES_PLACEHOLDER } from '../../../../lib/constants/enrollmentConstants';
import { People } from '../../../../lib/collections/people';
import { Groups } from '../../../../lib/collections/groups';
import { Reservations } from '../../../../lib/collections/reservations';

describe('enrollmentUtils', () => {
    it('mapReservation maps a reservation', async () => {
        const peopleFindOneMock = People.findOneAsync;
        peopleFindOneMock.mockImplementationOnce(() => {
            return {defaultGroupId: "yvDMGuRGobm9YMnuX"}
        });
        const groupsFindOneMock = Groups.findOneAsync;
        groupsFindOneMock.mockImplementationOnce(() => {
            return {_id: 'yvDMGuRGobm9YMnuX', name: "After Care"}
        });
        const reservation = {
            scheduledDate: 1704949200000,
            selectedPerson: "7c9EQRBihrbbnhjJE",
            groupId: "yvDMGuRGobm9YMnuX",
            scheduledEndDate: 1705467600000,
            linkedPlan: () => {
                return {
                  _id: 'plan1',
                  planDetails: {
                    details: {
                      selectiveWeeks: [
                        ['04/07/2025', '04/11/2025'],
                        ['04/14/2025', '04/18/2025'],
                        ['04/21/2025', '04/25/2025'],
                        ['04/28/2025', '05/02/2025'],
                        ['05/05/2025', '05/09/2025'],
                        ['05/12/2025', '05/16/2025'],
                        ['05/19/2025', '05/23/2025'],
                        ['05/26/2025', '05/30/2025']
                      ]
                    }
                  },
                  masterPlanInfo: {
                    description: 'After Care',
                    programDetails: 'details'
                  }
                };
            }
        }
        const defaultGroupId = "yvDMGuRGobm9YMnuX";
        const timezone = "America/New_York";
        const todayStamp = moment().tz(timezone).startOf("day").valueOf();
        const result = await EnrollmentUtils.mapReservation(reservation, defaultGroupId, timezone);
        expect(peopleFindOneMock).toHaveBeenCalledWith("7c9EQRBihrbbnhjJE");
        expect(groupsFindOneMock).toHaveBeenCalledWith("yvDMGuRGobm9YMnuX");
        expect(result).toEqual({
            scheduledDate: 1704949200000,
            selectedPerson: '7c9EQRBihrbbnhjJE',
            groupId: 'yvDMGuRGobm9YMnuX',
            scheduledEndDate: 1705467600000,
            linkedPlan: reservation.linkedPlan,
            targetGroupName: 'After Care',
            isSelectiveWeeks: true,
            planId: "plan1",
            planDescription: 'After Care',
            programDetails: 'details',
            selectiveWeeks:
                [
                    ['04/07/2025', '04/11/2025'],
                    ['04/14/2025', '04/18/2025'],
                    ['04/21/2025', '04/25/2025'],
                    ['04/28/2025', '05/02/2025'],
                    ['05/05/2025', '05/09/2025'],
                    ['05/12/2025', '05/16/2025'],
                    ['05/19/2025', '05/23/2025'],
                    ['05/26/2025', '05/30/2025']
                ],
            current: reservation.scheduledDate <= todayStamp && (!reservation.scheduledEndDate || reservation.scheduledEndDate >= todayStamp)
        });
    });

    it('groupSelectedWeeksPlans groups and returns selected weeks', () => {
        const reservations = [
            {
                scheduledDate: 1743998400000,
                selectedPerson: '7c9EQRBihrbbnhjJE',
                groupId: 'yvDMGuRGobm9YMnuX',
                scheduledEndDate: 1744344000000,
                targetGroupName: 'After Care',
                isSelectiveWeeks: true,
                planId: "plan1",
                recurringDays: ['mon', 'tue', 'wed', 'thu', 'fri'],
                scheduleType: 'scheduleType123',
                planDescription: 'After Care',
                programDetails: 'details',
                selectiveWeeks:
                    [
                        ['04/07/2025', '04/11/2025'],
                        ['04/14/2025', '04/18/2025'],
                        ['04/21/2025', '04/25/2025'],
                        ['04/28/2025', '05/02/2025'],
                        ['05/05/2025', '05/09/2025'],
                        ['05/12/2025', '05/16/2025'],
                        ['05/19/2025', '05/23/2025'],
                        ['05/26/2025', '05/30/2025']
                    ],
                current: true
            }
        ]
        const expected =   [
            {
                planId: 'plan1',
                planDescription: 'After Care',
                programDetails: 'details',
                reservations: [{
                    week: 1,
                    startDate: '04/07/2025',
                    endDate: '04/11/2025',
                    recurringDays: [ 'mon', 'tue', 'wed', 'thu', 'fri' ],
                    targetGroupName: 'After Care',
                    scheduleType: 'scheduleType123'
                }]
            }
        ]
        const result = EnrollmentUtils.groupSelectedWeeksPlans(reservations, "America/New_York");
        expect(result).toEqual(expected);
    });

    it('formatReservation returns formatted reservation', () => {
        const reservation = {
            scheduledDate: 1743998400000,
            selectedPerson: '7c9EQRBihrbbnhjJE',
            groupId: 'yvDMGuRGobm9YMnuX',
            scheduledEndDate: 1744344000000,
            targetGroupName: 'After Care',
            isSelectiveWeeks: true,
            planId: "plan1",
            recurringDays: ['mon', 'tue', 'wed', 'thu', 'fri'],
            scheduleType: 'scheduleType123',
            planDescription: 'After Care',
            selectiveWeeks:
                [
                    ['04/07/2025', '04/11/2025'],
                    ['04/14/2025', '04/18/2025'],
                    ['04/21/2025', '04/25/2025'],
                    ['04/28/2025', '05/02/2025'],
                    ['05/05/2025', '05/09/2025'],
                    ['05/12/2025', '05/16/2025'],
                    ['05/19/2025', '05/23/2025'],
                    ['05/26/2025', '05/30/2025']
                ],
            current: true
        }

        const timezone = "America/New_York";
        const result = EnrollmentUtils.formatReservation(reservation, timezone);
        expect(result).toEqual({
            week: 1,
            startDate: '04/07/2025',
            endDate: '04/11/2025',
            recurringDays: [ 'mon', 'tue', 'wed', 'thu', 'fri' ],
            targetGroupName: 'After Care',
            scheduleType: 'scheduleType123'
        });
    });

    describe('getDefaultAvailabilities', () => {
        it('should return default availabilities for weekdays only', () => {
            const result = EnrollmentUtils.getDefaultAvailabilities(false);
            expect(result).toEqual({
                1: AVAILABILITIES_PLACEHOLDER,
                2: AVAILABILITIES_PLACEHOLDER,
                3: AVAILABILITIES_PLACEHOLDER,
                4: AVAILABILITIES_PLACEHOLDER,
                5: AVAILABILITIES_PLACEHOLDER,
            });
        });

        it('should return default availabilities for weekdays and weekends', () => {
            const result = EnrollmentUtils.getDefaultAvailabilities(true);
            expect(result).toEqual({
                1: AVAILABILITIES_PLACEHOLDER,
                2: AVAILABILITIES_PLACEHOLDER,
                3: AVAILABILITIES_PLACEHOLDER,
                4: AVAILABILITIES_PLACEHOLDER,
                5: AVAILABILITIES_PLACEHOLDER,
                6: AVAILABILITIES_PLACEHOLDER,
                0: AVAILABILITIES_PLACEHOLDER,
            });
        });
    });

    describe('adjustAvailabilitiesBasedOnSelectedDays', () => {
        it('should decrement availabilities for selected weekdays', () => {
            const availabilities = {
                1: 5,
                2: 5,
                3: 5,
                4: 5,
                5: 5,
            };
            const selectedDays = ['monday', 'wednesday'];
            EnrollmentUtils.adjustAvailabilitiesBasedOnSelectedDays(selectedDays, availabilities, false);
            expect(availabilities).toEqual({
                1: 4, // Monday decremented
                2: 5,
                3: 4, // Wednesday decremented
                4: 5,
                5: 5,
            });
        });

        it('should decrement availabilities for selected weekdays and weekends', () => {
            const availabilities = {
                1: 5,
                2: 5,
                3: 5,
                4: 5,
                5: 5,
                6: 5, // Saturday
                0: 5, // Sunday
            };
            const selectedDays = ['monday', 'sunday'];
            EnrollmentUtils.adjustAvailabilitiesBasedOnSelectedDays(selectedDays, availabilities, true);
            expect(availabilities).toEqual({
                1: 4, // Monday decremented
                2: 5,
                3: 5,
                4: 5,
                5: 5,
                6: 5, // Saturday remains unchanged
                0: 4, // Sunday decremented
            });
        });

        it('should not modify availabilities if no days are selected', () => {
            const availabilities = {
                1: 5,
                2: 5,
                3: 5,
                4: 5,
                5: 5,
            };
            EnrollmentUtils.adjustAvailabilitiesBasedOnSelectedDays([], availabilities, false);
            expect(availabilities).toEqual({
                1: 5,
                2: 5,
                3: 5,
                4: 5,
                5: 5,
            });
        });

        it('should handle no weekend logic when hasWeekends is false', () => {
            const availabilities = {
                1: 5,
                2: 5,
                3: 5,
                4: 5,
                5: 5,
                6: 5,
                0: 5,
            };
            const selectedDays = ['saturday', 'sunday'];
            EnrollmentUtils.adjustAvailabilitiesBasedOnSelectedDays(selectedDays, availabilities, false);
            expect(availabilities).toEqual({
                1: 5,
                2: 5,
                3: 5,
                4: 5,
                5: 5,
                6: 5, // Saturday remains unchanged
                0: 5, // Sunday remains unchanged
            });
        });
    });
    describe('getItemSchedules', () => {
        it('marks past schedules as striked and keeps future ones', async () => {
          // Setup
          const timezone = 'UTC';
          
          // Create dates relative to today
          const pastDate = moment().subtract(2, 'days').valueOf();
          const futureDate = moment().add(2, 'days').valueOf();
          
          const items = [{
            _id: "1",
            originalItem: { _id: 'item1' }
          }];
          
          const nonRecurringReservations = [
            {
              _id: 'schedule1',
              scheduledDate: pastDate,
              generatedFromBillingCharge: 'item1',
              enrolledItemId: "1"
            },
            {
              _id: 'schedule2',
              scheduledDate: futureDate,
              generatedFromBillingCharge: 'item1',
              enrolledItemId: "1"
            },
            {
              _id: 'schedule3',
              scheduledDate: futureDate,
              generatedFromBillingCharge: 'item1'
            }
          ];
          
          // Call the method with the array
          const result = await EnrollmentUtils.getItemSchedules(items, timezone, nonRecurringReservations);
          
          // Assertions
          expect(result[0].schedules.length).toBe(2);
          expect(result[0].schedules[0].isStriked).toBe(true); // Past date should be striked
          expect(result[0].schedules[1].isStriked).toBeUndefined(); // Future date should not be striked
        });
    
        it('clears schedules when all dates are in the past', async () => {
          const timezone = 'UTC';
          
          // All dates in the past
          const pastDate1 = moment().subtract(10, 'days').valueOf();
          const pastDate2 = moment().subtract(5, 'days').valueOf();
          
          const items = [{
            originalItem: { _id: 'item1' }
          }];
          
          const nonRecurringReservations = [
            {
              _id: 'schedule1',
              scheduledDate: pastDate1,
              generatedFromBillingCharge: 'item1'
            },
            {
              _id: 'schedule2',
              scheduledDate: pastDate2,
              generatedFromBillingCharge: 'item1'
            }
          ];
          
          // Call the method
          const result = await EnrollmentUtils.getItemSchedules(items, timezone, nonRecurringReservations);
          
          // All dates are in the past, so schedules should be empty
          expect(result[0].schedules).toEqual([]);
        });
    
        it('correctly sorts schedules by date', async () => {
          const timezone = 'UTC';
          
          // Dates in mixed order
          const date1 = moment().add(5, 'days').valueOf();
          const date2 = moment().add(2, 'days').valueOf();
          const date3 = moment().add(10, 'days').valueOf();
          
          const items = [{
            originalItem: { _id: 'item1' }
          }];
          
          // Schedules purposely out of order
          const nonRecurringReservations = [
            {
              _id: 'schedule1',
              scheduledDate: date1, // Middle date
              generatedFromBillingCharge: 'item1'
            },
            {
              _id: 'schedule2',
              scheduledDate: date3, // Latest date
              generatedFromBillingCharge: 'item1'
            },
            {
              _id: 'schedule3',
              scheduledDate: date2, // Earliest date
              generatedFromBillingCharge: 'item1'
            }
          ];
          
          const result = await EnrollmentUtils.getItemSchedules(items, timezone, nonRecurringReservations);
          
          // Verify sorting
          expect(result[0].schedules.length).toBe(3);
          expect(result[0].schedules[0].scheduledDate).toBe(date2); // Earliest first
          expect(result[0].schedules[1].scheduledDate).toBe(date1); // Middle second
          expect(result[0].schedules[2].scheduledDate).toBe(date3); // Latest last
        });
    
        it('handles multiple items with their respective schedules', async () => {
          const timezone = 'UTC';
          
          const futureDate1 = moment().add(1, 'days').valueOf();
          const futureDate2 = moment().add(2, 'days').valueOf();
          
          const items = [
            { originalItem: { _id: 'item1' } },
            { originalItem: { _id: 'item2' } }
          ];
          
          const nonRecurringReservations = [
            {
              _id: 'schedule1',
              scheduledDate: futureDate1,
              generatedFromBillingCharge: 'item1'
            },
            {
              _id: 'schedule2',
              scheduledDate: futureDate2,
              generatedFromBillingCharge: 'item2'
            }
          ];
          
          const result = await EnrollmentUtils.getItemSchedules(items, timezone, nonRecurringReservations);
          
          // Verify each item has its own schedules
          expect(result.length).toBe(2);
          expect(result[0].schedules.length).toBe(1);
          expect(result[1].schedules.length).toBe(1);
          expect(result[0].schedules[0].generatedFromBillingCharge).toBe('item1');
          expect(result[1].schedules[0].generatedFromBillingCharge).toBe('item2');
        });
    
        it('clears schedules when they are all in the past', async () => {
            const timezone = 'Pacific/Honolulu';
            
            // Get today at start of day in the target timezone
            const todayInTimezone = moment.tz(timezone).startOf('day');
            
            // Create a date that's definitely yesterday in this timezone
            const yesterdayDate = todayInTimezone.subtract(1, 'hours').valueOf();
            
            const items = [{
            originalItem: { _id: 'item1' }
            }];
            
            const nonRecurringReservations = [
            {
                _id: 'schedule1',
                scheduledDate: yesterdayDate,
                generatedFromBillingCharge: 'item1'
            }
            ];
        
            // All schedules are in the past, so the function should empty the schedules array
            const result = await EnrollmentUtils.getItemSchedules(items, timezone, nonRecurringReservations);
            
            // When all schedules are in the past, the function should set schedules to []
            expect(result[0].schedules).toEqual([]);
        });
        
        it('keeps current day schedules unmarked', async () => {
            const timezone = 'UTC';
            
            // Get today at start of day in UTC
            const todayStart = moment.tz(timezone).startOf('day');
            
            // Add a few hours to ensure it's definitely today
            const todayDate = todayStart.add(5, 'hours').valueOf();
            
            const items = [{
            originalItem: { _id: 'item1' }
            }];
            
            const nonRecurringReservations = [
            {
                _id: 'schedule1',
                scheduledDate: todayDate,
                generatedFromBillingCharge: 'item1'
            }
            ];
        
            const result = await EnrollmentUtils.getItemSchedules(items, timezone, nonRecurringReservations);
            
            // Today's schedule should not be striked
            expect(result[0].schedules[0].isStriked).toBeUndefined();
        });
    
        it('handles empty arrays gracefully', async () => {
          const timezone = 'UTC';
          const items = [{ originalItem: { _id: 'item1' } }];
          const nonRecurringReservations = [];
          
          const result = await EnrollmentUtils.getItemSchedules(items, timezone, nonRecurringReservations);
          
          expect(result.length).toBe(1);
          expect(result[0].schedules).toEqual([]);
        });
    
        it('handles invalid timezone gracefully', async () => {
          const invalidTimezone = 'Non-existent/Timezone';
          const futureDate = moment().add(2, 'days').valueOf();
          
          const items = [{ originalItem: { _id: 'item1' } }];
          const nonRecurringReservations = [
            {
              _id: 'schedule1',
              scheduledDate: futureDate,
              generatedFromBillingCharge: 'item1'
            }
          ];
          
          // Should not throw an error with invalid timezone
          const result = await EnrollmentUtils.getItemSchedules(items, invalidTimezone, nonRecurringReservations);
          
          // Function should complete and return items
          expect(result.length).toBe(1);
        });
    });
});