import { EmailUtils } from '../../../../lib/util/emailUtils';
import { expect } from '@jest/globals';

describe('EmailUtils', () => {
    describe('getCleanEmail', () => {
        it('should return a clean email address with special characters escaped', () => {
            const email = '<EMAIL>';
            const cleanedEmail = EmailUtils.getCleanEmail(email);

            // Ensure special characters are escaped
            expect(cleanedEmail).toBe('user\\+name@example\\.com');
        });

        it('should return a clean email address with leading and trailing whitespaces removed', () => {
            const email = '  <EMAIL>  ';
            const cleanedEmail = EmailUtils.getCleanEmail(email);

            // Ensure leading and trailing whitespaces are removed
            expect(cleanedEmail).toBe('user@example\\.com');
        });

        it('should return a clean email address in lowercase', () => {
            const email = '<EMAIL>';
            const cleanedEmail = EmailUtils.getCleanEmail(email);

            // Ensure email address is in lowercase
            expect(cleanedEmail).toBe('user@example\\.com');
        });
    });
});