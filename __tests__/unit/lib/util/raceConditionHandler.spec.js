import {
  resolveRaceConditions,
  resolveCheckInRaceConditions,
  resolveCheckOutRaceConditions,
  handleRaceConditions
} from '../../../../lib/util/raceConditionHandler';

describe('Race Condition Handler', () => {
  describe('resolveRaceConditions', () => {
    it('should return empty arrays when no moments are provided', () => {
      const result = resolveRaceConditions(null, {});
      expect(result.keptMoments).toEqual([]);
      expect(result.discardedMoments).toEqual([]);
    });

    it('should return the single moment when only one is provided', () => {
      const moment = { _id: '1', momentType: 'checkin', owner: 'person1', sortStamp: 100 };
      const result = resolveRaceConditions([moment], {});
      expect(result.keptMoments).toEqual([moment]);
      expect(result.discardedMoments).toEqual([]);
    });

    it('should use the provided priority function to determine which moment to keep', () => {
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'person1', sortStamp: 100 },
        { _id: '2', momentType: 'checkin', owner: 'person1', sortStamp: 200 }
      ];

      const priorityHandler = jest.fn().mockReturnValue(moments[1]);

      const result = resolveRaceConditions(moments, {
        momentType: 'checkin',
        priorityHandler
      });

      expect(priorityHandler).toHaveBeenCalledWith(moments);
      expect(result.keptMoments).toEqual([moments[1]]);
      expect(result.discardedMoments).toEqual([moments[0]]);
    });
  });

  describe('resolveCheckInRaceConditions', () => {
    it('should keep the earliest kiosk check-in when there are consecutive check-ins', () => {
      // This represents a real sequence: checkin, checkin, checkin (race condition)
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'person1', sortStamp: 100, usedPin: true },
        { _id: '2', momentType: 'checkin', owner: 'person1', sortStamp: 200, usedPin: true },
        { _id: '3', momentType: 'checkin', owner: 'person1', sortStamp: 300 }
      ];

      const result = resolveCheckInRaceConditions(moments);

      expect(result.keptMoments).toEqual([moments[0]]); // Earliest kiosk check-in
      expect(result.discardedMoments).toEqual([moments[1], moments[2]]);
    });

    it('should keep the earliest non-kiosk check-in when no kiosk check-ins are available in consecutive sequence', () => {
      // This represents a real sequence: checkin, checkin, checkin (race condition, no kiosk)
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'person1', sortStamp: 100 },
        { _id: '2', momentType: 'checkin', owner: 'person1', sortStamp: 200 },
        { _id: '3', momentType: 'checkin', owner: 'person1', sortStamp: 300 }
      ];

      const result = resolveCheckInRaceConditions(moments);

      expect(result.keptMoments).toEqual([moments[0]]); // Earliest non-kiosk check-in
      expect(result.discardedMoments).toEqual([moments[1], moments[2]]);
    });

    it('should handle multiple people correctly with consecutive check-ins', () => {
      // This represents consecutive check-ins for each person
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'person1', sortStamp: 100 },
        { _id: '2', momentType: 'checkin', owner: 'person1', sortStamp: 200 },
        { _id: '3', momentType: 'checkin', owner: 'person2', sortStamp: 300, usedPin: true },
        { _id: '4', momentType: 'checkin', owner: 'person2', sortStamp: 400 }
      ];

      const result = resolveCheckInRaceConditions(moments);

      // Should keep earliest non-kiosk for person1 and earliest kiosk for person2
      expect(result.keptMoments).toContainEqual(moments[0]); // person1's earliest
      expect(result.keptMoments).toContainEqual(moments[2]); // person2's kiosk
      expect(result.keptMoments.length).toBe(2);

      expect(result.discardedMoments).toContainEqual(moments[1]); // person1's later
      expect(result.discardedMoments).toContainEqual(moments[3]); // person2's non-kiosk
      expect(result.discardedMoments.length).toBe(2);
    });
  });

  describe('resolveCheckOutRaceConditions', () => {
    it('should keep the earliest kiosk check-out when there are consecutive check-outs', () => {
      // This represents a real sequence: checkout, checkout, checkout (race condition)
      const moments = [
        { _id: '1', momentType: 'checkout', owner: 'person1', sortStamp: 100, usedPin: true },
        { _id: '2', momentType: 'checkout', owner: 'person1', sortStamp: 200, usedPin: true },
        { _id: '3', momentType: 'checkout', owner: 'person1', sortStamp: 300 }
      ];

      const result = resolveCheckOutRaceConditions(moments);

      expect(result.keptMoments).toEqual([moments[0]]); // Earliest kiosk check-out
      expect(result.discardedMoments).toEqual([moments[1], moments[2]]);
    });

    it('should keep the earliest non-kiosk check-out when no kiosk check-outs are available in consecutive sequence', () => {
      // This represents a real sequence: checkout, checkout, checkout (race condition, no kiosk)
      const moments = [
        { _id: '1', momentType: 'checkout', owner: 'person1', sortStamp: 100 },
        { _id: '2', momentType: 'checkout', owner: 'person1', sortStamp: 200 },
        { _id: '3', momentType: 'checkout', owner: 'person1', sortStamp: 300 }
      ];

      const result = resolveCheckOutRaceConditions(moments);

      expect(result.keptMoments).toEqual([moments[0]]); // Earliest non-kiosk check-out
      expect(result.discardedMoments).toEqual([moments[1], moments[2]]);
    });
  });

  describe('handleRaceConditions', () => {
    it('should handle a sequence of check-ins and check-outs correctly', () => {
      // Example from the ticket
      const moments = [
        { _id: '1', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T01:03:00').valueOf() }, // 1:03 AM Check Out, Non-Kiosk
        { _id: '2', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:34:00').valueOf(), usedPin: true }, // 8:34 AM Check In, Kiosk
        { _id: '3', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:36:00').valueOf(), usedPin: true }, // 8:36 AM Check In, Kiosk
        { _id: '4', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T09:01:00').valueOf() }, // 9:01 AM Check In, Non-Kiosk
        { _id: '5', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T11:58:00').valueOf() }, // 11:58 AM Check Out, Non-Kiosk
        { _id: '6', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T12:01:00').valueOf() }, // 12:01 PM Check Out, Non-Kiosk
        { _id: '7', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T12:57:00').valueOf() }, // 12:57 PM Check In, Non-Kiosk
        { _id: '8', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T12:58:00').valueOf(), usedPin: true }, // 12:58 PM Check In, Kiosk
        { _id: '9', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T17:15:00').valueOf(), usedPin: true }, // 5:15 PM Check Out, Kiosk
        { _id: '10', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T17:17:00').valueOf(), usedPin: true }, // 5:17 PM Check Out, Kiosk
        { _id: '11', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T21:00:00').valueOf() } // 9:00 PM Check Out, Non-Kiosk
      ];

      const result = handleRaceConditions(moments);

      // According to the ticket, we should keep:
      // - 1:03 AM Check Out (moments[0]) - single checkout (no race condition)
      // - 8:34 AM Check In (moments[1]) - earliest kiosk checkin in consecutive series
      // - 11:58 AM Check Out (moments[4]) - earliest non-kiosk checkout in consecutive series
      // - 12:58 PM Check In (moments[7]) - earliest kiosk checkin in consecutive series
      // - 5:15 PM Check Out (moments[8]) - earliest kiosk checkout in consecutive series

      expect(result.keptMoments).toContainEqual(moments[0]);
      expect(result.keptMoments).toContainEqual(moments[1]);
      expect(result.keptMoments).toContainEqual(moments[4]);
      expect(result.keptMoments).toContainEqual(moments[7]);
      expect(result.keptMoments).toContainEqual(moments[8]);
      expect(result.keptMoments.length).toBe(5);

      expect(result.discardedMoments.length).toBe(6);
    });

    it('should NOT discard moments when there are no race conditions (alternating check-ins/check-outs)', () => {
      // Test the fix for the first item in the Jira comment:
      // "The logic for ignoring/deleting should only kick in when there are two or more check-ins in a row, or two or more check-outs in a row"
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:00:00').valueOf() },
        { _id: '2', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T12:00:00').valueOf() },
        { _id: '3', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T13:00:00').valueOf() },
        { _id: '4', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T17:00:00').valueOf() }
      ];

      const result = handleRaceConditions(moments);

      // All moments should be kept since there are no race conditions
      expect(result.keptMoments).toEqual(moments);
      expect(result.discardedMoments).toEqual([]);
    });

    it('should NOT discard single check-ins or check-outs', () => {
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:00:00').valueOf() }
      ];

      const result = handleRaceConditions(moments);

      // Single moment should always be kept
      expect(result.keptMoments).toEqual(moments);
      expect(result.discardedMoments).toEqual([]);
    });

    it('should only apply race condition logic when there are 2+ consecutive moments of same type', () => {
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:00:00').valueOf() },
        { _id: '2', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:05:00').valueOf(), usedPin: true }, // Race condition here
        { _id: '3', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T12:00:00').valueOf() }, // Single checkout - should be kept
        { _id: '4', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T13:00:00').valueOf() } // Single checkin - should be kept
      ];

      const result = handleRaceConditions(moments);

      // Should keep: earliest kiosk check-in (moment 2), single checkout (moment 3), single checkin (moment 4)
      expect(result.keptMoments).toContainEqual(moments[1]); // Kiosk check-in wins
      expect(result.keptMoments).toContainEqual(moments[2]); // Single checkout kept
      expect(result.keptMoments).toContainEqual(moments[3]); // Single checkin kept
      expect(result.keptMoments.length).toBe(3);

      // Should discard: first check-in (moment 1)
      expect(result.discardedMoments).toContainEqual(moments[0]);
      expect(result.discardedMoments.length).toBe(1);
    });

    it('should keep both sets of check-in/check-out pairs when done offline', () => {
      // This reproduces the issue: two sets of check-in/out while offline should both be kept
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:00:00').valueOf() },
        { _id: '2', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T12:00:00').valueOf() },
        { _id: '3', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T13:00:00').valueOf() },
        { _id: '4', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T17:00:00').valueOf() }
      ];

      const result = handleRaceConditions(moments);

      // All moments should be kept since there are no race conditions
      // Pattern: checkin -> checkout -> checkin -> checkout (no consecutive same types)
      expect(result.keptMoments).toEqual(moments);
      expect(result.discardedMoments).toEqual([]);
      expect(result.keptMoments.length).toBe(4);
    });

    it('should simulate the exact offline sync scenario step by step', () => {
      // Simulate what happens during offline sync:
      // 1. First check-in syncs (only 1 check-in exists)
      // 2. First check-out syncs (1 check-in, 1 check-out exists)
      // 3. Second check-in syncs (2 check-ins, 1 check-out exists)
      // 4. Second check-out syncs (2 check-ins, 2 check-outs exists)

      const personId = 'child1';

      // Step 1: First check-in syncs
      let allMoments = [
        { _id: '1', momentType: 'checkin', owner: personId, sortStamp: new Date('2025-05-08T08:00:00').valueOf() }
      ];
      let result = handleRaceConditions(allMoments);
      expect(result.keptMoments.length).toBe(1);
      expect(result.discardedMoments.length).toBe(0);

      // Step 2: First check-out syncs
      allMoments = [
        { _id: '1', momentType: 'checkin', owner: personId, sortStamp: new Date('2025-05-08T08:00:00').valueOf() },
        { _id: '2', momentType: 'checkout', owner: personId, sortStamp: new Date('2025-05-08T12:00:00').valueOf() }
      ];
      result = handleRaceConditions(allMoments);
      expect(result.keptMoments.length).toBe(2);
      expect(result.discardedMoments.length).toBe(0);

      // Step 3: Second check-in syncs
      allMoments = [
        { _id: '1', momentType: 'checkin', owner: personId, sortStamp: new Date('2025-05-08T08:00:00').valueOf() },
        { _id: '2', momentType: 'checkout', owner: personId, sortStamp: new Date('2025-05-08T12:00:00').valueOf() },
        { _id: '3', momentType: 'checkin', owner: personId, sortStamp: new Date('2025-05-08T13:00:00').valueOf() }
      ];
      result = handleRaceConditions(allMoments);
      expect(result.keptMoments.length).toBe(3);
      expect(result.discardedMoments.length).toBe(0);

      // Step 4: Second check-out syncs
      allMoments = [
        { _id: '1', momentType: 'checkin', owner: personId, sortStamp: new Date('2025-05-08T08:00:00').valueOf() },
        { _id: '2', momentType: 'checkout', owner: personId, sortStamp: new Date('2025-05-08T12:00:00').valueOf() },
        { _id: '3', momentType: 'checkin', owner: personId, sortStamp: new Date('2025-05-08T13:00:00').valueOf() },
        { _id: '4', momentType: 'checkout', owner: personId, sortStamp: new Date('2025-05-08T17:00:00').valueOf() }
      ];
      result = handleRaceConditions(allMoments);
      expect(result.keptMoments.length).toBe(4);
      expect(result.discardedMoments.length).toBe(0);

      // All moments should be kept since there are no consecutive same types
      expect(result.keptMoments).toEqual(allMoments);
    });
  });
});
