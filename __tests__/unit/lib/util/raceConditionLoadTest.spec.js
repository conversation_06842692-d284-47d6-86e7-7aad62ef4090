import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { Moments } from '../../../../lib/collections/moments';
import { resolveCheckInOutRaceConditions } from '../../../../lib/util/checkInOutRaceConditionHandler';
import { handleRaceConditions } from '../../../../lib/util/raceConditionHandler';

// Mock the Moments collection
jest.mock('../../../../lib/collections/moments', () => ({
  Moments: {
    insertAsync: jest.fn(),
    removeAsync: jest.fn(),
    find: jest.fn(),
    findOneAsync: jest.fn()
  }
}));

describe('Race Condition Handler - Load Testing', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  /**
   * Creates test moments with race conditions for testing
   * @param {string} personId - Person ID for the moments
   * @param {number} count - Number of moments to create
   * @param {string} momentType - Type of moments ('checkin' or 'checkout')
   * @param {boolean} includeKiosk - Whether to include kiosk moments
   * @returns {Array} Array of test moments
   */
  const createTestMoments = (personId, count, momentType, includeKiosk = true) => {
    const moments = [];
    const baseTime = Date.now();

    for (let i = 0; i < count; i++) {
      const moment = {
        _id: `${personId}-${momentType}-${i}`,
        momentType,
        owner: personId,
        sortStamp: baseTime + (i * 1000), // 1 second apart - creates consecutive moments
        createdAt: new Date(baseTime + (i * 1000)),
        orgId: 'test-org-id',
        taggedPeople: [personId]
      };

      // Add kiosk properties - make first moment kiosk to ensure predictable behavior
      if (includeKiosk && i === 0) {
        moment.usedPin = true;
        moment.isKioskMode = true;
      } else if (includeKiosk && Math.random() < 0.3) {
        moment.usedPin = true;
        moment.isKioskMode = true;
      }

      moments.push(moment);
    }

    return moments;
  };

  describe('Small Scale Race Condition Tests', () => {
    it('should handle consecutive check-ins correctly', () => {
      const personId = 'test-person-1';
      const moments = createTestMoments(personId, 3, 'checkin', true);

      const result = handleRaceConditions(moments);

      // Should keep only one moment (the earliest kiosk one)
      expect(result.keptMoments).toHaveLength(1);
      expect(result.discardedMoments).toHaveLength(2);
      expect(result.keptMoments[0].usedPin).toBe(true);
    });

    it('should handle consecutive check-outs correctly', () => {
      const personId = 'test-person-2';
      const moments = createTestMoments(personId, 4, 'checkout', true);

      const result = handleRaceConditions(moments);

      // Should keep only one moment (the earliest kiosk one)
      expect(result.keptMoments).toHaveLength(1);
      expect(result.discardedMoments).toHaveLength(3);
    });

    it('should handle mixed check-ins and check-outs without race conditions', () => {
      const personId = 'test-person-3';
      const baseTime = Date.now();

      const moments = [
        {
          _id: 'checkin-1',
          momentType: 'checkin',
          owner: personId,
          sortStamp: baseTime,
          usedPin: true
        },
        {
          _id: 'checkout-1',
          momentType: 'checkout',
          owner: personId,
          sortStamp: baseTime + 60000 // 1 minute later
        },
        {
          _id: 'checkin-2',
          momentType: 'checkin',
          owner: personId,
          sortStamp: baseTime + 120000 // 2 minutes later
        }
      ];

      const result = handleRaceConditions(moments);

      // Should keep all moments as there are no consecutive same-type moments
      expect(result.keptMoments).toHaveLength(3);
      expect(result.discardedMoments).toHaveLength(0);
    });
  });

  describe('Performance Benchmarks', () => {
    it('should process race conditions within acceptable time limits - 50 check-ins and 50 check-outs', () => {
      const personId = 'performance-test-person';
      const baseTime = Date.now();

      // Create moments that will definitely have race conditions
      // All check-ins will be consecutive (race condition)
      // All check-outs will be consecutive (race condition)
      const allMoments = [];

      // Create 50 consecutive check-ins (should keep only 1)
      for (let i = 0; i < 50; i++) {
        allMoments.push({
          _id: `${personId}-checkin-${i}`,
          momentType: 'checkin',
          owner: personId,
          sortStamp: baseTime + (i * 1000),
          createdAt: new Date(baseTime + (i * 1000)),
          orgId: 'test-org-id',
          taggedPeople: [personId],
          usedPin: i === 0 // First one is kiosk
        });
      }

      // Create 50 consecutive check-outs (should keep only 1)
      for (let i = 0; i < 50; i++) {
        allMoments.push({
          _id: `${personId}-checkout-${i}`,
          momentType: 'checkout',
          owner: personId,
          sortStamp: baseTime + 50000 + (i * 1000), // After all check-ins
          createdAt: new Date(baseTime + 50000 + (i * 1000)),
          orgId: 'test-org-id',
          taggedPeople: [personId],
          usedPin: i === 0 // First one is kiosk
        });
      }

      const startTime = Date.now();
      const result = handleRaceConditions(allMoments);
      const executionTime = Date.now() - startTime;

      // Performance assertions
      expect(executionTime).toBeLessThan(1000); // Should complete within 1 second
      expect(result.keptMoments.length).toBeLessThan(allMoments.length);
      expect(result.discardedMoments.length).toBeGreaterThan(0);

      // Verify correctness - should keep only 2 moments (1 checkin, 1 checkout)
      const keptCheckIns = result.keptMoments.filter(m => m.momentType === 'checkin');
      const keptCheckOuts = result.keptMoments.filter(m => m.momentType === 'checkout');

      expect(keptCheckIns).toHaveLength(1);
      expect(keptCheckOuts).toHaveLength(1);

      // Verify the correct moments were kept (the kiosk ones)
      expect(keptCheckIns[0].usedPin).toBe(true);
      expect(keptCheckOuts[0].usedPin).toBe(true);

      // Verify we discarded the expected number
      expect(result.discardedMoments).toHaveLength(98); // 49 + 49 discarded
    });

    it('should handle the ticket example scenario correctly', () => {
      // Example from EN-453 ticket
      const moments = [
        { _id: '1', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T01:03:00').valueOf() }, // 1:03 AM Check Out, Non-Kiosk
        { _id: '2', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:34:00').valueOf(), usedPin: true }, // 8:34 AM Check In, Kiosk
        { _id: '3', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T08:36:00').valueOf(), usedPin: true }, // 8:36 AM Check In, Kiosk
        { _id: '4', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T09:01:00').valueOf() }, // 9:01 AM Check In, Non-Kiosk
        { _id: '5', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T11:58:00').valueOf() }, // 11:58 AM Check Out, Non-Kiosk
        { _id: '6', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T12:01:00').valueOf() }, // 12:01 PM Check Out, Non-Kiosk
        { _id: '7', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T12:57:00').valueOf() }, // 12:57 PM Check In, Non-Kiosk
        { _id: '8', momentType: 'checkin', owner: 'child1', sortStamp: new Date('2025-05-08T12:58:00').valueOf(), usedPin: true }, // 12:58 PM Check In, Kiosk
        { _id: '9', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T17:15:00').valueOf(), usedPin: true }, // 5:15 PM Check Out, Kiosk
        { _id: '10', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T17:17:00').valueOf(), usedPin: true }, // 5:17 PM Check Out, Kiosk
        { _id: '11', momentType: 'checkout', owner: 'child1', sortStamp: new Date('2025-05-08T21:00:00').valueOf() } // 9:00 PM Check Out, Non-Kiosk
      ];

      const result = handleRaceConditions(moments);

      // Expected kept moments based on the ticket criteria:
      // - 1:03 AM Check Out (only checkout at start)
      // - 8:34 AM Check In (earliest kiosk in the check-in series)
      // - 11:58 AM Check Out (earliest in the checkout series)
      // - 12:58 PM Check In (earliest kiosk in the second check-in series)
      // - 5:15 PM Check Out (earliest kiosk in the final checkout series)

      expect(result.keptMoments).toHaveLength(5);
      expect(result.discardedMoments).toHaveLength(6);

      // Verify specific moments are kept
      const keptIds = result.keptMoments.map(m => m._id);
      expect(keptIds).toContain('1'); // 1:03 AM Check Out
      expect(keptIds).toContain('2'); // 8:34 AM Check In (earliest kiosk)
      expect(keptIds).toContain('5'); // 11:58 AM Check Out (earliest)
      expect(keptIds).toContain('8'); // 12:58 PM Check In (kiosk)
      expect(keptIds).toContain('9'); // 5:15 PM Check Out (earliest kiosk)
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty moment arrays', () => {
      const result = handleRaceConditions([]);

      expect(result.keptMoments).toHaveLength(0);
      expect(result.discardedMoments).toHaveLength(0);
    });

    it('should handle single moments without race conditions', () => {
      const moment = {
        _id: 'single-moment',
        momentType: 'checkin',
        owner: 'test-person',
        sortStamp: Date.now()
      };

      const result = handleRaceConditions([moment]);

      expect(result.keptMoments).toHaveLength(1);
      expect(result.discardedMoments).toHaveLength(0);
      expect(result.keptMoments[0]).toEqual(moment);
    });

    it('should handle moments with missing properties gracefully', () => {
      const moments = [
        { _id: '1', momentType: 'checkin', owner: 'test', sortStamp: 100 },
        { _id: '2', momentType: 'checkin', owner: 'test', sortStamp: 200 }, // Missing usedPin
        { _id: '3', momentType: 'checkin', owner: 'test', sortStamp: 300, usedPin: false }
      ];

      const result = handleRaceConditions(moments);

      // Should still work and keep the earliest moment
      expect(result.keptMoments).toHaveLength(1);
      expect(result.discardedMoments).toHaveLength(2);
      expect(result.keptMoments[0]._id).toBe('1');
    });
  });
});
