import moment from 'moment-timezone';
import { LedgerDetailServiceUtils } from '../../../../lib/util/ledgerDetailServiceUtils';
import { AllocationAmountTypes, AllocationTypes } from '../../../../lib/discountTypes';

describe('LedgerDetailServiceUtils.getDiscountActionDate', () => {
    let org;
    const timezone = 'America/New_York';

    beforeEach(() => {
        org = {
            getTimezone: jest.fn().mockReturnValue(timezone),
            hasCustomization: jest.fn(),
        };
    });

    it('returns the invoice date if the discount has no createdAt timestamp', () => {
        const discount = {};
        const invoice = { invoiceDate: '1/01/2024' };

        const result = LedgerDetailServiceUtils.getDiscountActionDate(discount, invoice, org);
        expect(result).toBe(invoice.invoiceDate);
    });

    it('returns the created at date if it has one', () => {
        const discount = { createdAt: moment.tz('2023-11-13', timezone).valueOf() }; // 11/13/2023
        const invoice = { invoiceDate: '1/01/2024' };
        org.hasCustomization.mockReturnValue(false);

        const result = LedgerDetailServiceUtils.getDiscountActionDate(discount, invoice, org);
        expect(result).toBe('11/13/2023');
    });

    it('returns the voided date if isVoided is true', () => {
        const discount = { voidedAt: moment.tz('2023-11-26', timezone).valueOf(), createdAt: moment.tz('2023-11-13', timezone).valueOf(), _id: 'discount1' }; // Voided on 11/26/2023
        const invoice = { invoiceDate: '1/01/2024', lineItems: [] };
        org.hasCustomization.mockReturnValue(false);

        const result = LedgerDetailServiceUtils.getDiscountActionDate(discount, invoice, org, true);
        expect(result).toBe('11/26/2023');
    });
});

describe('LedgerDetailServiceUtils.pushAccountReceivableEntry', () => {
    let offsetEntry, target, arInvoiceEntry;

    beforeEach(() => {
        offsetEntry = {
            groupName: 'Group A',
            accountName: 'Account 123',
            creditAmount: 200,
            debitAmount: 100,
            cashOffset: 'Cash123',
            linkedDetails: [
                { amount: 50, date: '2024-12-01' },
                { amount: 75, date: '2024-12-02' },
            ],
        };

        arInvoiceEntry = {
            linkedDetails: [],
        };
    });

    it('should create an AR entry with debit target and add it to arInvoiceEntry', () => {
        target = 'debit';
        LedgerDetailServiceUtils.pushAccountReceivableEntry(offsetEntry, target, arInvoiceEntry);

        expect(arInvoiceEntry.linkedDetails).toHaveLength(1);
        const arEntry = arInvoiceEntry.linkedDetails[0];

        expect(arEntry.description).toBe('Group A - Account 123');
        expect(arEntry.debitAmount).toBe(200);
        expect(arEntry.creditAmount).toBe(0);
        expect(arEntry.cashOffset).toBe('Cash123');
        expect(arEntry.subLinkedDetails).toHaveLength(2);

        const [firstSubEntry, secondSubEntry] = arEntry.subLinkedDetails;
        expect(firstSubEntry.debitAmount).toBe(50);
        expect(firstSubEntry.creditAmount).toBe(0);
        expect(firstSubEntry.cashOffset).toBe('Cash123');
        expect(firstSubEntry.date).toBe('2024-12-01');

        expect(secondSubEntry.debitAmount).toBe(75);
        expect(secondSubEntry.creditAmount).toBe(0);
        expect(secondSubEntry.cashOffset).toBe('Cash123');
        expect(secondSubEntry.date).toBe('2024-12-02');
    });

    it('should create an AR entry with credit target and add it to arInvoiceEntry', () => {
        target = 'credit';
        LedgerDetailServiceUtils.pushAccountReceivableEntry(offsetEntry, target, arInvoiceEntry);

        expect(arInvoiceEntry.linkedDetails).toHaveLength(1);
        const arEntry = arInvoiceEntry.linkedDetails[0];

        expect(arEntry.description).toBe('Group A - Account 123');
        expect(arEntry.debitAmount).toBe(0);
        expect(arEntry.creditAmount).toBe(100);
        expect(arEntry.cashOffset).toBe('Cash123');
        expect(arEntry.subLinkedDetails).toHaveLength(2);

        const [firstSubEntry, secondSubEntry] = arEntry.subLinkedDetails;
        expect(firstSubEntry.debitAmount).toBe(0);
        expect(firstSubEntry.creditAmount).toBe(50);
        expect(firstSubEntry.cashOffset).toBe('Cash123');
        expect(firstSubEntry.date).toBe('2024-12-01');

        expect(secondSubEntry.debitAmount).toBe(0);
        expect(secondSubEntry.creditAmount).toBe(75);
        expect(secondSubEntry.cashOffset).toBe('Cash123');
        expect(secondSubEntry.date).toBe('2024-12-02');
    });

    it('should set the date of arEntry to the first sub-entry date', () => {
        target = 'debit';
        LedgerDetailServiceUtils.pushAccountReceivableEntry(offsetEntry, target, arInvoiceEntry);

        const arEntry = arInvoiceEntry.linkedDetails[0];
        expect(arEntry.date).toBe('2024-12-01');
    });

    it('should handle an empty linkedDetails array in offsetEntry gracefully', () => {
        offsetEntry.linkedDetails = [];
        target = 'debit';
        LedgerDetailServiceUtils.pushAccountReceivableEntry(offsetEntry, target, arInvoiceEntry);

        expect(arInvoiceEntry.linkedDetails).toHaveLength(1);
        const arEntry = arInvoiceEntry.linkedDetails[0];
        expect(arEntry.subLinkedDetails).toHaveLength(0);
    });

    it('should not modify arInvoiceEntry if offsetEntry is invalid', () => {
        offsetEntry = null;
        target = 'debit';
        expect(() => LedgerDetailServiceUtils.pushAccountReceivableEntry(offsetEntry, target, arInvoiceEntry)).toThrow();

        expect(arInvoiceEntry.linkedDetails).toHaveLength(0);
    });

    it('should create an AR entry with both debit and credit amounts and add it to arInvoiceEntry', () => {
        const target = 'both';
        LedgerDetailServiceUtils.pushAccountReceivableEntry(offsetEntry, target, arInvoiceEntry);

        expect(arInvoiceEntry.linkedDetails).toHaveLength(1);
        const arEntry = arInvoiceEntry.linkedDetails[0];

        expect(arEntry.description).toBe('Group A - Account 123');
        expect(arEntry.debitAmount).toBe(200); // Debit is allowed, so creditAmount from offsetEntry is used
        expect(arEntry.creditAmount).toBe(100); // Credit is allowed, so debitAmount from offsetEntry is used
        expect(arEntry.cashOffset).toBe('Cash123');
        expect(arEntry.subLinkedDetails).toHaveLength(2);

        const [firstSubEntry, secondSubEntry] = arEntry.subLinkedDetails;
        expect(firstSubEntry.debitAmount).toBe(50); // Debit allowed
        expect(firstSubEntry.creditAmount).toBe(50); // Credit allowed
        expect(firstSubEntry.cashOffset).toBe('Cash123');
        expect(firstSubEntry.date).toBe('2024-12-01');

        expect(secondSubEntry.debitAmount).toBe(75); // Debit allowed
        expect(secondSubEntry.creditAmount).toBe(75); // Credit allowed
        expect(secondSubEntry.cashOffset).toBe('Cash123');
        expect(secondSubEntry.date).toBe('2024-12-02');
    });

    it('should handle an empty linkedDetails array gracefully with "both" target', () => {
        offsetEntry.linkedDetails = [];
        const target = 'both';
        LedgerDetailServiceUtils.pushAccountReceivableEntry(offsetEntry, target, arInvoiceEntry);

        expect(arInvoiceEntry.linkedDetails).toHaveLength(1);
        const arEntry = arInvoiceEntry.linkedDetails[0];
        expect(arEntry.debitAmount).toBe(200);
        expect(arEntry.creditAmount).toBe(100);
        expect(arEntry.subLinkedDetails).toHaveLength(0);
    });
});

describe('LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts', () => {
    it('should calculate display amount for dollar-based discounts without modifications', () => {
        const discount = {
            amount: 1000,
            originalAllocation: {
                amount: 1500,
                amountType: 'dollars',
            },
        };
        const lineItem = {};
        const isAmountPercentBased = false;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(1500);
    });

    it('should calculate display amount for percentage-based discounts without modifications', () => {
        const discount = {
            amount: 2000,
            originalAllocation: {
                amount: 100,
                amountType: 'percent',
            },
        };
        const lineItem = { amount: 2000 };
        const isAmountPercentBased = true;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(2000);
    });

    it('if voided, should calculate amount based on line-item if percentage-based and without modifications', () => {
        const discount = {
            amount: 0,
            voidedAt: 1,
            originalAllocation: {
                amount: 10,
                amountType: 'percent',
            },
        };
        const lineItem = { amount: 2000 };
        const isAmountPercentBased = true;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(200);
    });

    it('should calculate the discount amount even if current amount is zero based on modifications', () => {
        const discount = {
            amount: 0,
            modifiedDiscount: -200,
            originalAllocation: {
                amount: 10,
                amountType: 'percent',
            },
        };
        const lineItem = { amount: 2000 };
        const isAmountPercentBased = true;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(200);
    });

    it('should include modifiedDiscount for percentage-based discounts', () => {
        const discount = {
            amount: 2000,
            modifiedDiscount: 500,
            originalAllocation: {
                amount: 100,
                amountType: 'percent',
            },
        };
        const lineItem = { amount: 2000 };
        const isAmountPercentBased = true;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(1500); // 2000 + 500
    });

    it('should ignore modifiedDiscount for dollar-based discounts', () => {
        const discount = {
            amount: 500,
            modifiedDiscount: -500,
            originalAllocation: {
                amount: 1000,
                amountType: 'dollars',
            },
        };
        const lineItem = {};
        const isAmountPercentBased = false;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(1000); // modifiedDiscount is ignored
    });

    it('should fallback to 0 if discount amount is invalid', () => {
        const discount = {
            amount: null,
            originalAllocation: {
                amount: null,
                amountType: 'dollars',
            },
        };
        const lineItem = {};
        const isAmountPercentBased = false;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(0);
    });

    it('should fallback to 0 if originalAllocation is missing', () => {
        const discount = {
            amount: null,
        };
        const lineItem = {};
        const isAmountPercentBased = false;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(0);
    });

    it('should handle percentage-based discounts with missing originalAllocation gracefully', () => {
        const discount = {
            amount: 1500,
        };
        const lineItem = { amount: 2000 };
        const isAmountPercentBased = true;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(1500); // Uses discount amount directly
    });

    it('should calculate absolute values for dollar-based discounts', () => {
        const discount = {
            amount: -500,
            originalAllocation: {
                amount: -1000,
                amountType: 'dollars',
            },
        };
        const lineItem = {};
        const isAmountPercentBased = false;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(1000); // Absolute value of -1000
    });

    it('should add values for negative modifiedDiscounts in percentage-based discounts', () => {
        const discount = {
            amount: 2000,
            modifiedDiscount: -300,
            originalAllocation: {
                amount: 100,
                amountType: 'percent',
            },
        };
        const lineItem = { amount: 2000 };
        const isAmountPercentBased = true;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(2300); // 2000 + 300 (absolute value of modifiedDiscount)
    });

    it('should subtract values for positive modifiedDiscounts in percentage-based discounts', () => {
        const discount = {
            amount: 2000,
            modifiedDiscount: 300,
            originalAllocation: {
                amount: 100,
                amountType: 'percent',
            },
        };
        const lineItem = { amount: 2000 };
        const isAmountPercentBased = true;

        const result = LedgerDetailServiceUtils.getDisplayAmountForPlanDiscounts(discount, lineItem, isAmountPercentBased);
        expect(result).toBe(1700); // 2000 - 300 (absolute value of modifiedDiscount)
    });
});

describe('LedgerDetailServiceUtils', () => {
    const options = {
        startDate: 1710000000000, // Example start date
        endDate: 1720000000000, // Example end date
    };

    describe('getCreditAmount', () => {
        it('should return voidedAmount if credit is voided and has a valid creditReason', () => {
            const credit = {
                voidedAt: 1712345678901,
                creditReason: "manual_payment",
                voidedAmount: 50,
                amount: 100
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(50);
        });

        it('should return voidedAmount for reimbursable creditReason', () => {
            const credit = {
                voidedAt: 1712345678901,
                creditReason: "reimbursable",
                voidedAmount: 30,
                amount: 100
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(30);
        });

        it('should return voidedAmount for payroll_deduction creditReason', () => {
            const credit = {
                voidedAt: 1712345678901,
                creditReason: "payroll_deduction",
                voidedAmount: 40,
                amount: 100
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(40);
        });

        it('should return voidedAmount for other creditReason', () => {
            const credit = {
                voidedAt: 1712345678901,
                creditReason: "other",
                voidedAmount: 60,
                amount: 100
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(60);
        });

        it('should return originalAmount if there are adjustments and an original amount', () => {
            const credit = {
                adjustments: [{ adjustment: -10 }],
                originalAmount: 120,
                amount: 100
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(120);
        });

        it('should return refundedAmount if credit is refunded and has manual_payment creditReason', () => {
            const credit = {
                refundedAt: 1712345678901,
                creditReason: "manual_payment",
                refundedAmount: 75,
                amount: 100
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(75);
        });

        it('should return amount if no special conditions apply', () => {
            const credit = {
                amount: 200
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(200);
        });

        it('should return amount if credit has an unknown reason', () => {
            const credit = {
                creditReason: "unknown_reason",
                amount: 300
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(300);
        });

        it('should return amount if voidedAt is present but creditReason is invalid', () => {
            const credit = {
                voidedAt: 1712345678901,
                creditReason: "invalid_reason",
                voidedAmount: 50,
                amount: 400
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(400);
        });

        it('should return amount if adjustments exist but originalAmount is missing', () => {
            const credit = {
                adjustments: [{ adjustment: -10 }],
                amount: 500
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(500);
        });

        it('should return amount if refundedAt is present but creditReason is not manual_payment', () => {
            const credit = {
                refundedAt: 1712345678901,
                creditReason: "other_reason",
                refundedAmount: 50,
                amount: 600
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(600);
        });

        it('should return amount if refundedAt is present but refundedAmount is missing', () => {
            const credit = {
                refundedAt: 1712345678901,
                creditReason: "manual_payment",
                amount: 700
            };
            expect(LedgerDetailServiceUtils.getCreditAmount(credit)).toBe(700);
        });
    });

    describe('isRefundedManualCreditInRange', () => {
        it('should return true for a refunded manual payment within range', () => {
            const credit = { refundedAt: 1715000000000, creditReason: "manual_payment", refundedAmount: 100 };
            expect(LedgerDetailServiceUtils.isRefundedManualCreditInRange(credit, options)).toBe(true);
        });

        it('should return false if refundedAt is out of range', () => {
            const credit = { refundedAt: 1705000000000, creditReason: "manual_payment", refundedAmount: 100 };
            expect(LedgerDetailServiceUtils.isRefundedManualCreditInRange(credit, options)).toBe(false);
        });

        it('should return false if creditReason is not "manual_payment"', () => {
            const credit = { refundedAt: 1715000000000, creditReason: "other", refundedAmount: 100 };
            expect(LedgerDetailServiceUtils.isRefundedManualCreditInRange(credit, options)).toBe(false);
        });

        it('should return true if refundedAt exists but range is missing', () => {
            const credit = { refundedAt: 1715000000000, creditReason: "manual_payment", refundedAmount: 100 };
            expect(LedgerDetailServiceUtils.isRefundedManualCreditInRange(credit)).toBe(true);
        });
    });

    describe('isVoidedOtherCreditInRange', () => {
        it('should return true for a voided credit with reason "other" within range', () => {
            const credit = { voidedAt: 1715000000000, creditReason: "other", voidedAmount: 50 };
            expect(LedgerDetailServiceUtils.isVoidedOtherCreditInRange(credit, options)).toBe(true);
        });

        it('should return false if voidedAt is out of range', () => {
            const credit = { voidedAt: 1705000000000, creditReason: "other", voidedAmount: 50 };
            expect(LedgerDetailServiceUtils.isVoidedOtherCreditInRange(credit, options)).toBe(false);
        });

        it('should return false if creditReason is not "other"', () => {
            const credit = { voidedAt: 1715000000000, creditReason: "manual_payment", voidedAmount: 50 };
            expect(LedgerDetailServiceUtils.isVoidedOtherCreditInRange(credit, options)).toBe(false);
        });

        it('should return true if voidedAt exists but range is missing', () => {
            const credit = { voidedAt: 1715000000000, creditReason: "other", voidedAmount: 50 };
            expect(LedgerDetailServiceUtils.isVoidedOtherCreditInRange(credit)).toBe(true);
        });
    });

    describe('isCreditVoidedWithinReportRange', () => {
        it('should return true if voidedAt is within range and voidedAmount is present', () => {
            const credit = { voidedAt: 1715000000000, voidedAmount: 50 };
            expect(LedgerDetailServiceUtils.isCreditVoidedWithinReportRange(credit, options)).toBe(true);
        });

        it('should return false if voidedAt is out of range', () => {
            const credit = { voidedAt: 1705000000000, voidedAmount: 50 };
            expect(LedgerDetailServiceUtils.isCreditVoidedWithinReportRange(credit, options)).toBe(false);
        });

        it('should return false if voidedAmount is missing', () => {
            const credit = { voidedAt: 1715000000000 };
            expect(LedgerDetailServiceUtils.isCreditVoidedWithinReportRange(credit, options)).toBe(false);
        });
    });

    describe('processModifiedCreditLinkedDetail', () => {
        it('should return a correctly formatted linked detail object', () => {
            const invoice = { invoiceNumber: 'INV-001', _id: 'inv123' };
            const history = { modifiedAt: new Date('2024-01-15'), amountModified: 50 };
            const result = LedgerDetailServiceUtils.processModifiedCreditLinkedDetail(invoice, history, 'Test Descriptor', 'Default Group');

            expect(result).toEqual({
                description: 'Test Descriptor Credit modified on inv # INV-001',
                date: new moment(history.modifiedAt).format("M/D/YYYY"),
                targetType: 'invoice',
                target: 'inv123',
                amount: 50,
                modifiedAmount: 50,
                personDefaultGroupName: 'Default Group'
            });
        });
    });

    describe('modifiedCreditIsWithinReportRange', () => {
        it('should return true if modifiedAt is within the date range', () => {
            expect(LedgerDetailServiceUtils.modifiedCreditIsWithinReportRange(new Date('2024-01-15'), new Date('2024-01-01'), new Date('2024-02-01'))).toBe(true);
        });

        it('should return false if modifiedAt is before the start date', () => {
            expect(LedgerDetailServiceUtils.modifiedCreditIsWithinReportRange(new Date('2023-12-31'), new Date('2024-01-01'), new Date('2024-02-01'))).toBe(false);
        });

        it('should return false if modifiedAt is on or after the end date', () => {
            expect(LedgerDetailServiceUtils.modifiedCreditIsWithinReportRange(new Date('2024-02-01'), new Date('2024-01-01'), new Date('2024-02-01'))).toBe(false);
        });
    });
});

describe('LedgerDetailServiceUtils.getMappedLedgerEntryForDeposit', () => {
    it('should correctly map a deposit with positive amounts only', () => {
        const ig = {
            linkedDetails: [
                { amount: 1000, description: 'Deposit 1', date: '2024-01-15' },
                { amount: 500, description: 'Deposit 2', date: '2024-01-15' }
            ],
            transactions: [],
            ledgerAccount: {
                accountName: 'Manual Deposits',
                offsetAccountName: 'Bank Account'
            }
        };

        const entry = {};
        const result = LedgerDetailServiceUtils.getMappedLedgerEntryForDeposit(ig, entry, false, 'Default Bank');

        expect(entry).toEqual({
            creditAmount: 1500,
            cashOffset: true,
            cashOffsetExclude: false
        });

        expect(result).toEqual({
            accountName: 'Bank Account',
            accountDescription: 'Bank Account',
            debitAmount: 1500,
            creditAmount: 0,
            description: 'Bank Deposits',
            groupName: 'Other',
            transactions: [],
            glImportIgnore: true,
            linkedDetails: [
                {
                    amount: 1000,
                    description: 'Deposit 1',
                    date: '2024-01-15'
                },
                {
                    amount: 500,
                    description: 'Deposit 2',
                    date: '2024-01-15'
                }
            ],
            cashOffset: true
        });
    });

    it('should handle deposits with adjustments', () => {
        const ig = {
            linkedDetails: [
                { amount: 1000, description: 'Initial Deposit', date: '2024-01-15' },
                { amount: -200, description: 'Adjustment 1', date: '2024-01-16' }
            ],
            transactions: [],
            ledgerAccount: {
                accountName: 'Manual Deposits',
                offsetAccountName: 'Bank Account'
            }
        };

        const entry = {};
        const result = LedgerDetailServiceUtils.getMappedLedgerEntryForDeposit(ig, entry, false, 'Default Bank');

        expect(entry).toEqual({
            creditAmount: 1000,
            debitAmount: 200,
            cashOffset: true,
            cashOffsetExclude: false,
            linkedDetails: [
                {
                    amount: 1000,
                    creditAmount: 1000,
                    date: '2024-01-15',
                    debitAmount: 0,
                    description: 'Initial Deposit'
                },
                {
                    amount: 200,
                    creditAmount: 0,
                    date: '2024-01-16',
                    debitAmount: 200,
                    description: 'Adjustment 1'
                }
            ]
        });

        expect(result.debitAmount).toBe(1000);
        expect(result.creditAmount).toBe(200);
        expect(result.linkedDetails).toHaveLength(2);
    });

    it('should handle cash only deposits', () => {
        const ig = {
            linkedDetails: [
                { amount: 1000, description: 'Cash Deposit', date: '2024-01-15' }
            ],
            transactions: [],
            ledgerAccount: {
                accountName: 'Cash Deposits',
                offsetAccountName: 'Cash Account'
            }
        };

        const entry = {};
        const result = LedgerDetailServiceUtils.getMappedLedgerEntryForDeposit(ig, entry, true, 'Cash Account');

        expect(entry).toEqual({
            creditAmount: 1000,
            cashOffset: true,
            cashOffsetExclude: true
        });

        expect(result.accountName).toBe('Cash Deposits');
        expect(result.accountDescription).toBe('Cash Deposits');
    });

    it('should use default cashAccountName when offsetAccountName is not provided', () => {
        const ig = {
            linkedDetails: [
                { amount: 1000, description: 'Deposit', date: '2024-01-15' }
            ],
            transactions: [],
            ledgerAccount: {
                accountName: 'Manual Deposits'
            }
        };

        const entry = {};
        const result = LedgerDetailServiceUtils.getMappedLedgerEntryForDeposit(ig, entry, false, 'Default Bank');

        expect(result.accountName).toBe('Default Bank');
        expect(result.accountDescription).toBe('Default Bank');
    });
});

describe('LedgerDetailServiceUtils.calculateDiscount', () => {
    it('returns correctly calculated percent-based discount amount when no void and amounts match', () => {
        const discount = {
            amount: 10,
            originalAllocation: {
                amount: 20,
                amountType: AllocationAmountTypes.PERCENT
            }
        };
        const lineItem = { amount: 50 };

        // 20% of 50 = 10
        const result = LedgerDetailServiceUtils.calculateDiscount(discount, lineItem);
        expect(result).toBe(10);
    });

    it('returns discount.amount instead of calculated amount if not voided and amounts differ', () => {
        const discount = {
            amount: 11, // does not match 20% of 50 = 10
            originalAllocation: {
                amount: 20,
                amountType: AllocationAmountTypes.PERCENT
            }
        };
        const lineItem = { amount: 50 };

        const result = LedgerDetailServiceUtils.calculateDiscount(discount, lineItem);
        expect(result).toBe(11);
    });

    it('returns calculated amount if discount is voided, even if discount.amount differs', () => {
        const discount = {
            amount: 11, // mismatched
            voidedAt: new Date(), // voided
            originalAllocation: {
                amount: 20,
                amountType: AllocationAmountTypes.PERCENT
            }
        };
        const lineItem = { amount: 50 };

        const result = LedgerDetailServiceUtils.calculateDiscount(discount, lineItem);
        expect(result).toBe(10); // 20% of 50
    });

    it('returns flat amount from originalAllocation if amountType is not percent', () => {
        const discount = {
            amount: 30,
            originalAllocation: {
                amount: 25,
                amountType: AllocationAmountTypes.DOLLARS
            }
        };
        const lineItem = { amount: 100 };

        const result = LedgerDetailServiceUtils.calculateDiscount(discount, lineItem);
        expect(result).toBe(25); // Use originalAllocation.amount
    });

    it('falls back to discount.amount if originalAllocation is missing', () => {
        const discount = {
            amount: 40
        };
        const lineItem = { amount: 100 };

        const result = LedgerDetailServiceUtils.calculateDiscount(discount, lineItem);
        expect(result).toBe(40);
    });

    it('returns 0 if neither discount.amount nor originalAllocation.amount exist', () => {
        const discount = {};
        const lineItem = { amount: 100 };

        const result = LedgerDetailServiceUtils.calculateDiscount(discount, lineItem);
        expect(result).toBe(0);
    });

    it('returns full discount.amount for reimbursable-with-copay allocation type with dollar amount type', () => {
        const discount = {
            type: AllocationTypes.COPAY,
            amount: 1500,
            originalAllocation: {
                allocationType: AllocationTypes.COPAY,
                amount: 500,
                amountType: AllocationAmountTypes.DOLLARS
            }
        };
        const lineItem = { amount: 2000 };

        const result = LedgerDetailServiceUtils.calculateDiscount(discount, lineItem);
        expect(result).toBe(1500);
    });

    it('returns full discount.amount for reimbursable-with-copay allocation type with percent amount type', () => {
        const discount = {
            type: AllocationTypes.COPAY,
            amount: 1500,
            originalAllocation: {
                allocationType: AllocationTypes.COPAY,
                amount: 25,
                amountType: AllocationAmountTypes.PERCENT
            }
        };
        const lineItem = { amount: 2000 };

        const result = LedgerDetailServiceUtils.calculateDiscount(discount, lineItem);
        expect(result).toBe(1500);
    });
});
