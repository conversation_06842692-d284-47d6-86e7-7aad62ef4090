import { AdminUtils } from '../../../../lib/util/adminUtils';
import { People } from '../../../../lib/collections/people';

describe('AdminUtils.validateOrganizationTags', () => {
    it('returns no errors for unique, non-empty names', () => {
        const input = [
            { _id: '1', name: 'Finance', archived: false },
            { _id: '2', name: 'HR', archived: false },
        ];

        const result = AdminUtils.validateOrganizationTags(input);

        expect(result.hasErrors).toBe(false);
        expect(result.tags).toEqual([
            { _id: '1', name: 'Finance', archived: false, validationError: null },
            { _id: '2', name: 'HR', archived: false, validationError: null },
        ]);
    });

    it('trims whitespace from names', () => {
        const input = [
            { _id: '1', name: '  Accounting  ', archived: false }
        ];

        const result = AdminUtils.validateOrganizationTags(input);

        expect(result.tags[0].name).toBe('Accounting');
        expect(result.tags[0].validationError).toBeNull();
        expect(result.hasErrors).toBe(false);
    });

    it('flags empty tag names', () => {
        const input = [
            { _id: '1', name: '', archived: false },
            { _id: '2', name: '   ', archived: true }
        ];

        const result = AdminUtils.validateOrganizationTags(input);

        expect(result.hasErrors).toBe(true);
        expect(result.tags[0].validationError).toBe('Tag name is required.');
        expect(result.tags[1].validationError).toBe('Tag name is required.');
    });

    it('flags duplicate tag names (case-insensitive)', () => {
        const input = [
            { _id: '1', name: 'Finance', archived: false },
            { _id: '2', name: 'finance', archived: false },
        ];

        const result = AdminUtils.validateOrganizationTags(input);

        expect(result.hasErrors).toBe(true);
        expect(result.tags[0].validationError).toBeNull(); // first occurrence okay
        expect(result.tags[1].validationError).toBe('Tag name must be unique.');
    });

    it('handles mix of valid, empty, and duplicate names', () => {
        const input = [
            { _id: '1', name: 'IT', archived: false },
            { _id: '2', name: '', archived: false },
            { _id: '3', name: 'it', archived: false },
            { _id: '4', name: 'HR', archived: false },
        ];

        const result = AdminUtils.validateOrganizationTags(input);

        expect(result.hasErrors).toBe(true);
        expect(result.tags[0].validationError).toBeNull();
        expect(result.tags[1].validationError).toBe('Tag name is required.');
        expect(result.tags[2].validationError).toBe('Tag name must be unique.');
        expect(result.tags[3].validationError).toBeNull();
    });
});

describe('AdminUtils.getPersonProfileFields', () => {
    it('returns all non-query profile fields when no specific question is provided', () => {
        const mockFields = [
            { type: 'text', name: 'name' },
            { type: 'query', name: 'query1' },
            { type: 'date', name: 'birthdate' },
            { type: 'select', name: 'country', multi: 'false' }
        ];
        
        jest.spyOn(People, 'getAllProfileFieldInputFields').mockReturnValue(mockFields);
        
        const result = AdminUtils.getPersonProfileFields(null);
        expect(result).toHaveLength(3);
        expect(result.find(f => f.type === 'query')).toBeUndefined();
    });

    it('filters text fields when yesTextIfNotSpecific is true', () => {
        const mockFields = [
            { type: 'text', name: 'name' },
            { type: 'string', name: 'description' },
            { type: 'date', name: 'birthdate' },
            { type: 'select', name: 'country', multi: 'false' }
        ];
        
        jest.spyOn(People, 'getAllProfileFieldInputFields').mockReturnValue(mockFields);
        
        const result = AdminUtils.getPersonProfileFields(null, true);
        expect(result).toHaveLength(2);
        expect(result.every(f => f.type === 'text' || f.type === 'string')).toBe(true);
    });

    it('filters date fields for date type conditional questions', () => {
        const mockFields = [
            { type: 'text', name: 'name' },
            { type: 'date', name: 'birthdate' },
            { type: 'date', name: 'startDate' },
            { type: 'select', name: 'country', multi: 'false' }
        ];
        
        jest.spyOn(People, 'getAllProfileFieldInputFields').mockReturnValue(mockFields);
        
        const result = AdminUtils.getPersonProfileFields({ type: 'date' });
        expect(result).toHaveLength(2);
        expect(result.every(f => f.type === 'date')).toBe(true);
    });

    it('filters single select fields for selectSingle type conditional questions', () => {
        const mockFields = [
            { type: 'select', name: 'country', multi: 'false' },
            { type: 'select', name: 'languages', multi: 'true' },
            { type: 'text', name: 'name' }
        ];
        
        jest.spyOn(People, 'getAllProfileFieldInputFields').mockReturnValue(mockFields);
        
        const result = AdminUtils.getPersonProfileFields({ type: 'selectSingle' });
        expect(result).toHaveLength(1);
        expect(result[0].multi).toBe('false');
    });

    it('filters multi select fields for selectMultiple type conditional questions', () => {
        const mockFields = [
            { type: 'select', name: 'country', multi: 'false' },
            { type: 'select', name: 'languages', multi: 'true' },
            { type: 'select', name: 'skills', multi: 'true' }
        ];
        
        jest.spyOn(People, 'getAllProfileFieldInputFields').mockReturnValue(mockFields);
        
        const result = AdminUtils.getPersonProfileFields({ type: 'selectMultiple' });
        expect(result).toHaveLength(2);
        expect(result.every(f => f.multi === 'true')).toBe(true);
    });

    it('filters text fields for checkbox type conditional questions', () => {
        const mockFields = [
            { type: 'text', name: 'name' },
            { type: 'string', name: 'description' },
            { type: 'select', name: 'country', multi: 'false' }
        ];
        
        jest.spyOn(People, 'getAllProfileFieldInputFields').mockReturnValue(mockFields);
        
        const result = AdminUtils.getPersonProfileFields({ type: 'checkbox' });
        expect(result).toHaveLength(2);
        expect(result.every(f => f.type === 'text' || f.type === 'string')).toBe(true);
    });
});
