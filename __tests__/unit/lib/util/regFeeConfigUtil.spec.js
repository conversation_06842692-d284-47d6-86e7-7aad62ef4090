import { Orgs } from "../../../../lib/collections/orgs";
import { RegFeeConfigUtil } from "../../../../lib/util/regFeeConfigUtil";
import { mockCollection } from "../../../helpers/collectionMock";

describe('regFeeConfigUtil', () => {
    it('should calculate default reg fee config', () => {
        const billingObject = {
            plansAndItems: [
                { description: 'Some other item' },
                { description: 'Registration Fee', _id: '123' },
            ]
        };
        const result = RegFeeConfigUtil.getDefaultConfig(billingObject);
        expect(result).toEqual({
            enabled: true,
            feeId: '123',
            perChild: false,
            enableMaxPerFamily: false,
            maxPerFamily: 1
        });
        expect(RegFeeConfigUtil.getDefaultConfig(null)).toStrictEqual({
            enabled: false,
            feeId: undefined,
            perChild: false,
            enableMaxPerFamily: false,
            maxPerFamily: 1
        });
    })
    it('should set all default configs', async () => {
        const orgsMock = Orgs;
        orgsMock.find.mockImplementationOnce(() => {
            return ({
                fetchAsync:jest.fn().mockImplementation(()=>[
                    { _id: '1', billing: { plansAndItems: [] } },
                    { _id: '2', billing: { plansAndItems: [{ description: 'Registration Fee', _id: '123' }] } },
                    { _id: '3', billing: { plansAndItems: [{ description: 'Registration Fee', _id: '456' }], regFeeConfig: { enabled: true } } },
                ])
            })
        });
        await RegFeeConfigUtil.setDefaultConfigs();
        expect(orgsMock.updateAsync.mock.calls.length).toBe(2);
        expect(orgsMock.updateAsync.mock.calls[0]).toEqual(['1', { $set: { 'billing.regFeeConfig': { enabled: false, feeId: undefined, perChild: false, enableMaxPerFamily: false, maxPerFamily: 1 } } }]);
        expect(orgsMock.updateAsync.mock.calls[1]).toEqual(['2', { $set: { 'billing.regFeeConfig': { enabled: true, feeId: '123', perChild: false, enableMaxPerFamily: false, maxPerFamily: 1 } } }]);
    });
});