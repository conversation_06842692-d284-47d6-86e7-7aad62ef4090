import { Orgs } from '../../../lib/collections/orgs';
import { Log } from '../../../lib/util/log';
import { GradesServedImporter } from '../../../server/importers/GradeServedImporter';

jest.mock('../../../lib/collections/orgs', () => ({
  Orgs: {
    updateAsync: jest.fn(),
  }
}));

jest.mock('../../../lib/util/log', () => ({
  Log: {
    error: jest.fn(),
  }
}));

const mockOrgs = [
  {
    _id: 'org123',
    name: 'MikeCare',
    valueOverrides: {
      profileFields: [
        { name: 'studentGrade', values: ['K'] }
      ]
    }
  }
];

describe('GradesServedImporter', () => {
  let importer;

  beforeEach(() => {
    importer = new GradesServedImporter({ allOrgs: mockOrgs });
  });

  it('should initialize with allOrgs', () => {
    expect(importer.allOrgs).toEqual(mockOrgs);
  });

  it('should validate a correct org name', () => {
    const validator = importer.fieldValidators['Site/Organization Name'];
    expect(validator('MikeCare')).toBe(true);
    expect(validator('FakeOrg')).toBe('Site/Organization Name must exist within the hierarchy.');
  });

  it('should validate boolean fields correctly', () => {
    const validator = importer.fieldValidators['Grades Served - K'];
    expect(validator('true')).toBe(true);
    expect(validator('false')).toBe(true);
    expect(validator('yes')).toBe('Entry must be “true” or “false”.');
  });

  it('should transform a row to overrideValue with correct grades', async () => {
    const input = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': 'true',
      'Grades Served - 1': 'false',
      'Grades Served - 2': 'TRUE',
      'Grades Served - 3': '',
    };

    const result = await importer.transformRow(input);
    expect(result).toEqual({
      siteName: 'MikeCare',
      overrideValue: {
        name: 'studentGrade',
        description: 'Student Grades',
        type: 'select',
        values: ['K', '2']
      }
    });
  });

  it('should update existing studentGrade field', async () => {
    const row = {
      siteName: 'MikeCare',
      overrideValue: {
        name: 'studentGrade',
        description: 'Student Grades',
        type: 'select',
        values: ['K', '1']
      }
    };
    await importer.insertRow(row);
    expect(Orgs.updateAsync).toHaveBeenCalledWith(
      { _id: 'org123' },
      { $set: { 'valueOverrides.profileFields.$[elem].values': ['K', '1'] } },
      { arrayFilters: [{ 'elem.name': 'studentGrade' }] }
    );
  });

  it('should push new studentGrade field if not found', async () => {
    const newOrg = {
      _id: 'org456',
      name: 'NewOrg',
      valueOverrides: { profileFields: [] }
    };
    const newImporter = new GradesServedImporter({ allOrgs: [newOrg] });

    const row = {
      siteName: 'NewOrg',
      overrideValue: {
        name: 'studentGrade',
        description: 'Student Grades',
        type: 'select',
        values: ['K', '2']
      }
    };

    await newImporter.insertRow(row);

    expect(Orgs.updateAsync).toHaveBeenCalledWith(
      { _id: 'org456' },
      {
        $push: {
          'valueOverrides.profileFields': {
            name: 'studentGrade',
            description: 'Student Grades',
            type: 'select',
            values: ['K', '2']
          }
        }
      }
    );
  });

  it('should log and collect error if org not found', async () => {
    const badImporter = new GradesServedImporter({ allOrgs: [] });
    const row = {
      siteName: 'UnknownOrg',
      overrideValue: {
        name: 'studentGrade',
        description: 'Student Grades',
        type: 'select',
        values: ['K']
      }
    };

    await badImporter.insertRow(row);

    expect(Log.error).toHaveBeenCalledWith("Could not find matching organization for 'UnknownOrg'");
    expect(badImporter.errors).toContain("Could not find matching organization for 'UnknownOrg'");
  });

  it('should ignore unrelated fields in transformRow', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': 'true',
      'Random Field': 'abc',
    };
    const result = await importer.transformRow(row);
    expect(result).toEqual({
      siteName: 'MikeCare',
      overrideValue: {
        name: 'studentGrade',
        description: 'Student Grades',
        type: 'select',
        values: ['K']
      }
    });
  });

  it('should return empty grades if none are marked true', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': 'false',
      'Grades Served - 1': '',
    };
    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual([]);
  });

  it('should handle Grades Served with whitespace or mixed casing', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': '  TrUe  ',
      'Grades Served - 1': '  FaLsE ',
    };
    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual(['K']);
  });

  it('should avoid duplicate grades in update', async () => {
    const row = {
      siteName: 'MikeCare',
      overrideValue: {
        name: 'studentGrade',
        description: 'Student Grades',
        type: 'select',
        values: ['K', '1', 'K']
      }
    };

    await importer.insertRow(row);

    expect(Orgs.updateAsync).toHaveBeenCalledWith(
      { _id: 'org123' },
      {
        $set: {
          'valueOverrides.profileFields.$[elem].values': ['K', '1']
        }
      },
      { arrayFilters: [{ 'elem.name': 'studentGrade' }] }
    );
  });


  it('should support Grades Served - 11 and 12 if marked true', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - 11': 'true',
      'Grades Served - 12': 'true'
    };

    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual(['11', '12']);
  });

  it('should ignore undefined or null values in grade fields', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': null,
      'Grades Served - 1': undefined,
      'Grades Served - 2': 'true'
    };

    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual(['2']);
  });

  it('should collect all grade levels marked true in a single row', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': 'true',
      'Grades Served - 1': 'true',
      'Grades Served - 2': 'true',
      'Grades Served - 3': 'true'
    };

    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual(['K', '1', '2', '3']);
  });

  it('should correctly handle multiple rows with the same site name', async () => {
    const row1 = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': 'true',
    };
    const row2 = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - 1': 'true',
    };

    const result1 = await importer.transformRow(row1);
    const result2 = await importer.transformRow(row2);

    expect(result1.overrideValue.values).toEqual(['K']);
    expect(result2.overrideValue.values).toEqual(['1']);
    expect(Orgs.updateAsync).toHaveBeenCalledTimes(3); // Two separate updates for same org
  });

  it('should return empty array for grades with "false" values', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': 'false',
      'Grades Served - 1': 'false',
      'Grades Served - 2': 'false',
    };

    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual([]);
  });

  it('should handle mixed "true" and "false" values correctly', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': 'true',
      'Grades Served - 1': 'false',
      'Grades Served - 2': 'true',
    };

    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual(['K', '2']);
  });

  it('should ignore non-grade related columns in the row', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Some Non-Grade Column': 'value',
      'Grades Served - 3': 'true',
    };

    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual(['3']);
  });

  it('should handle rows with a large number of grade columns without breaking', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - K': 'true',
      'Grades Served - 1': 'true',
      'Grades Served - 2': 'true',
      'Grades Served - 3': 'true',
      'Grades Served - 4': 'true',
      'Grades Served - 5': 'true',
      'Grades Served - 6': 'true',
      'Grades Served - 7': 'true',
      'Grades Served - 8': 'true',
      'Grades Served - 9': 'true',
      'Grades Served - 10': 'true',
    };

    const result = await importer.transformRow(row);
    expect(result.overrideValue.values).toEqual(['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10']);
  });

  it('should validate that all grade values are of type string', async () => {
    const row = {
      'Site/Organization Name': 'MikeCare',
      'Grades Served - 1': 'true',
      'Grades Served - 2': 'true',
    };

    const result = await importer.transformRow(row);
    result.overrideValue.values.forEach(value => {
      expect(typeof value).toBe('string'); // Ensure all grade values are strings
    });
  });

});
