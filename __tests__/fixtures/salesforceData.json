{"classrooms": [{"Id": "sf_class_1", "Center__r": {"attributes": {"type": "Account", "url": "/services/data/v42.0/sobjects/Account/0013g00000XqlsXAAR"}, "Name": "Salesforce Location"}, "Name": "Room - 1", "Age_Group__r": {"attributes": {"type": "Age_Group__c", "url": "/services/data/v42.0/sobjects/Age_Group__c/a2F3g000000DLNXEA4"}, "Lowest_Age_months__c": 0, "Highest_Allowed_months__c": 12}, "Maximum_classroom_capacity__c": 10, "Total_Number_of_Active_Classroom_Teacher__c": 2, "Adjusted_Max_Capacity__c": 8, "Unavailable_Seats__c": 0, "Room__r": {"attributes": {"type": "Room__c", "url": "/services/data/v42.0/sobjects/Room__c/a2N3g000000XmAGEA0"}, "Name": "Room 1"}}], "children": [{"attributes": {"type": "Account", "url": "/services/data/v42.0/sobjects/Account/0013g00000dPwZtAAK"}, "Id": "0013g00000dPwZtAAK", "Name": "<PERSON>", "Child_First_Name__c": "<PERSON>", "Child_Last_Name__c": "<PERSON>", "Child_Birthdate__c": "2022-11-16", "cfg_Guardian_Notes_Comments__c": null, "BillingStreet": "123 Main St.", "BillingCity": "Indianapolis", "BillingState": "Indiana", "BillingPostalCode": "46220", "Photo_Permission_Status__c": "Photo/video for Family Hub and center use only", "Primary_Guardian__c": "0013g00000VTbnZAAT", "Secondary_Guardian__c": "0013g00000VThq9AAD", "Number_of_Siblings_Enrolled__c": 0, "Preferred_Language__c": "English", "Gender__c": "Male", "Race__c": "White", "Ethnicity__c": "Not Hispanic or Latino", "Preferred_Hospital__c": null, "Preferred_Hospital_Phone_Number__c": null, "Dentist_Name__c": "Dr. <PERSON>", "Dentist_s_Phone_Number__c": "************", "Child_Play_Preference__c": null, "Child_s_bedtime__c": "19:30:00.000", "Favorite_Toys_Play__c": null, "Childs_Appetite__c": "Good", "Imaginary_Playmates__c": null, "Hungriest_During_which_meal__c": "Dinner", "Does_Child_Nap__c": true, "Pet_details__c": null, "How_Long_Child_Naps__c": "30 - 90 mins", "Special_Dietary_Needs__c": null, "Injuries_Operations_Conditions__c": null, "Common_Symptoms_of_Illness__c": "Unsure", "Concerns_about_present_Behavior__c": "No", "cfg_Methods_of_Discipline__pc": null, "How_is_present_behavior_addressed__c": null, "Does_Child_Share_Room__c": true, "Child_Sleep_Habits__c": "Naps about 80 mins of awake time and sleeps mostly through the night but wakes to feed 1-2 times.", "Does_Child_have_fears__c": "No", "How_Child_is_Comforted__c": "Holding, gently talking to him, some bouncing/rocking, and he sucks on my pinky finger", "Number_of_Times_Child_Has_Moved__c": 0, "Bathroom_reminder_Needed__c": "N/A - Child is in diapers", "How_Often_Child_Sees_Out_of_Home_Parent__c": "N/A", "Terminology_for_bathroom__c": null, "Development_Would_You_Like_to_See__c": null, "Guardian_Additional_Notes_Comments_HH__c": null, "Does_Child_Use_Pacifier__c": "No", "Does_Child_enjoy_swing__c": true, "Nipple_Size_Used__c": null, "Signals_when_hungry__c": "Cries and roots", "Activities_Songs_Toys_Infant_Enjoys__c": "Toys with lights and sounds, mirrors, some books, soft things to chew on", "Methods_of_Disciplines__c": "None (he is a newborn)", "Additional_Methods_to_soothe_child__c": null, "cfg_Acct_Case_Safe_ID__c": "0013g00000dPwZtAAK", "RecordTypeId": "0124P0000012OZbQAM", "Child_Billing_ID__c": "0013g00000dPwZtAAK", "cfg_Home_Type__c": "House", "cfg_Family_Activities_Details__c": null, "cfg_Most_Meaningful_Holidays__c": "The above", "cfg_Family_Inside_Activities__c": "Playing toys and games, reading", "cfg_Holidays_Celebrated__c": "Christmas, Easter, Thanksgiving", "cfg_Family_Outside_Activities__c": "Hiking, sports, sidewalk chalk", "cfg_Not_to_Learn_at_DEL_Preferences__c": "No", "cfg_Food_Family_Enjoys__c": "All types", "cfg_Unique_Cultural_Practices__c": "No", "cfg_Familys_Favorite_Restaurant__c": "Burgers", "Additional_Helpful_Family_Details__c": "None", "Favorite_Place_for_Fun__c": "Unsure", "cfg_Neat_Snack_Ideas__c": "No", "Relationships_RelatedAccount__r": {"totalSize": 2, "done": true, "records": [{"attributes": {"type": "Relationship__c", "url": "/services/data/v42.0/sobjects/Relationship__c/a2M3g000000U9lNEAS"}, "Id": "a2M3g000000U9lNEAS", "Account__r": {"attributes": {"type": "Account", "url": "/services/data/v42.0/sobjects/Account/0013g00000VTbnZAAT"}, "Id": "0013g00000VTbnZAAT", "Name": "Parent One", "FirstName": "Parent", "LastName": "One", "PersonMobilePhone": "************", "npe01__WorkPhone__pc": null, "BillingAddress": {"city": "Indianapolis", "country": "United States", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "46202", "state": "Indiana", "street": "111 Main St"}, "npe01__Preferred_Email__pc": "Work", "cfg_Customer_Email__pc": "<EMAIL>", "npe01__HomeEmail__pc": "<EMAIL>", "npe01__WorkEmail__pc": "<EMAIL>", "npe01__AlternateEmail__pc": null, "npe01__PreferredPhone__pc": "Home", "PersonHomePhone": "************", "PersonOtherPhone": null, "PersonDoNotCall": false, "Primary_Address_Type__c": null, "Secondary_Address_Type__c": null, "PersonMailingAddress": {"city": null, "country": null, "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "46202", "state": null, "street": null}, "cfg_Invoiced_Customer_Url__pc": "https://app.invoiced.com/customers/1711974", "cfg_Con_Case_Safe_ID__pc": "0033g00000uuZ56AAE", "RecordTypeId": "0123g000000TWGhAAO", "cfg_Invoiced_Customer_Id__pc": "1711974"}, "RelatedContact__c": null, "Role__c": "a2L3g000004UfxuEAC", "Start_Date__c": "2023-02-08", "Emergency_Contact__c": true, "Legal_Guardian__c": true, "Pick_up_Status__c": "Yes", "Relationship_Type__c": "Guardian", "Role__r": {"attributes": {"type": "ReciprocalRole__c", "url": "/services/data/v42.0/sobjects/ReciprocalRole__c/a2L3g000004UfxuEAC"}, "Name": "Guardian"}, "Can_discuss_education_behavior__c": false}, {"attributes": {"type": "Relationship__c", "url": "/services/data/v42.0/sobjects/Relationship__c/a2M3g000000U9lSEAS"}, "Id": "a2M3g000000U9lSEAS", "Account__r": {"attributes": {"type": "Account", "url": "/services/data/v42.0/sobjects/Account/0013g00000VThq9AAD"}, "Id": "0013g00000VThq9AAD", "Name": "Parent Two", "FirstName": "Second", "LastName": "Parent", "PersonMobilePhone": "************", "npe01__WorkPhone__pc": null, "BillingAddress": {"city": "Indianapolis", "country": "United States", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "46202", "state": "Indiana", "street": "1236 N New Jersey St"}, "npe01__Preferred_Email__pc": "Alternate", "cfg_Customer_Email__pc": "<EMAIL>", "npe01__HomeEmail__pc": "<EMAIL>", "npe01__WorkEmail__pc": "<EMAIL>", "npe01__AlternateEmail__pc": "<EMAIL>", "npe01__PreferredPhone__pc": "Mobile", "PersonHomePhone": null, "PersonOtherPhone": null, "PersonDoNotCall": false, "Primary_Address_Type__c": null, "Secondary_Address_Type__c": null, "PersonMailingAddress": {"city": null, "country": null, "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "46202", "state": null, "street": null}, "cfg_Invoiced_Customer_Url__pc": null, "cfg_Con_Case_Safe_ID__pc": "0033g00000uufJMAAY", "RecordTypeId": "0123g000000TWGhAAO", "cfg_Invoiced_Customer_Id__pc": null}, "RelatedContact__c": null, "Role__c": "a2L3g000004UfxuEAC", "Start_Date__c": "2023-02-08", "Emergency_Contact__c": true, "Legal_Guardian__c": true, "Pick_up_Status__c": "Yes", "Relationship_Type__c": "Pick Up Contact", "Role__r": {"attributes": {"type": "ReciprocalRole__c", "url": "/services/data/v42.0/sobjects/ReciprocalRole__c/a2L3g000004UfxuEAC"}, "Name": "FamilyFriend"}, "Can_discuss_education_behavior__c": true}]}, "Allergies__r": null, "Enrollments__r": {"totalSize": 1, "done": true, "records": [{"attributes": {"type": "Enrollment_eli__c", "url": "/services/data/v42.0/sobjects/Enrollment_eli__c/a2J3g000001tfiQEAQ"}, "Id": "a2J3g000001tfiQEAQ", "First_Attendance_Date__c": "2023-02-20", "Expected_Last_Attendance_Date__c": "2024-04-22", "Classroom__c": "a2H3g000000gx05EAA", "Classroom_Name__c": "Room - 1", "Center_Name__c": "Salesforce Location", "Enrollment_Status__c": "Active"}]}}], "expectedNewChild": {"firstName": "<PERSON>", "lastName": "<PERSON>", "type": "person", "orgId": "org1", "salesforce": {"accountId": "0013g00000dPwZtAAK", "accountName": "<PERSON>"}, "inActive": false, "defaultGroupId": "group1", "createdAt": *************, "createdBy": "SYSTEM_IMPORT", "profileData": {"birthday": *************, "enrollmentDate": *************, "withdrawDate": *************, "notesPrivate": null, "householdInformation": {"childStreetAddress": "123 Main St.", "childCity": "Indianapolis", "childState": "Indiana", "childZip": "46220"}, "mediaRequirements": {"noMediaAllowed": false}, "childDetails": {"numberOfSiblingsEnrolled": 0, "preferredLanguage": "English"}, "childDemographics": {"gender": "Male", "racialIdentity": "White", "ethnicIdentity": "Not Hispanic or Latino", "billingAddress": "123 Main St."}, "preferredEmergencyProviders": {"preferredHospitalName": null, "preferredHospitalPhone": null, "preferredDentistName": "Dr. <PERSON>", "preferredDentistPhone": "************"}, "childPreferences": {"childPlayPreference": null, "childsBedtime": "07:30 pm", "childsFavoriteToys": null, "childsAppetite": "Good", "imaginaryPlaymates": null, "childHungriestDuringMeal": "Dinner", "doesChildNap": true, "petDetails": null, "howLongChildNaps": "30 - 90 mins"}, "familyBackground": {"familyActivityDetails": null, "meaningfulHolidays": "The above", "familyInsideActivities": "Playing toys and games, reading", "holidaysCelebrated": "Christmas, Easter, Thanksgiving", "familyOutsideActivities": "Hiking, sports, sidewalk chalk", "notToLearn": "No", "foodFamilyEnjoys": "All types", "uniqueCulturalPractices": "No", "familyFavoriteRestaurant": "Burgers", "additionalHelpfulFamilyDetails": "None", "favoritePlaceForFun": "Unsure", "nextSnackIdeas": "No"}, "healthBackground": {"specialDietaryNeeds": null, "injuriesOperationsConditions": null, "commonSymptomsOfIllness": "Unsure"}, "childBehavior": {"concernsAboutPresentBehavior": "No", "methodsOfDisciplines": "None (he is a newborn)", "howIsPresentBehaviorAddressed": null}, "additionalBackground": {"shareRoom": true, "sleepHabits": "Naps about 80 mins of awake time and sleeps mostly through the night but wakes to feed 1-2 times.", "childFears": "No", "childComforted": "Holding, gently talking to him, some bouncing/rocking, and he sucks on my pinky finger", "numberOfMoves": 0, "bathroomReminders": "N/A - Child is in diapers", "seesOutOfHome": "N/A", "terminologyBathroom": null, "developmentYouWouldLikeToSee": null, "guardianNotesComments": null}, "infantSpecificDetails": {"pacifier": "No", "swinging": true, "nippleSizeUsed": null, "signalsWhenHungry": "Cries and roots", "activitiesEnjoyed": "Toys with lights and sounds, mirrors, some books, soft things to chew on", "soothingMethods": null}, "systemSettingsSalesForce": {"childBillingID": "0013g00000dPwZtAAK", "accountingCaseSafeID": "0013g00000dPwZtAAK"}}}, "expectedNewGuardians": [{"firstName": "Parent", "lastName": "One", "type": "family", "orgId": "org1", "inActive": false, "salesforce": {"accountId": "0013g00000VTbnZAAT", "accountName": "Parent One"}, "profileEmailAddress": "<EMAIL>", "profileData": {"phonePrimary": "************", "phoneNumberPrimary": "************", "phoneNumberWork": null, "householdInformation": {"parentStreetAddress": "111 Main St", "parentCity": "Indianapolis", "parentState": "Indiana", "parentZip": "46202"}, "contactInformation": {"accountName": "Parent One", "preferredEmail": "Work", "homeEmail": "<EMAIL>", "workEmail": "<EMAIL>", "alternateEmail": null, "phoneNumberOther": null, "doNotCall": false}, "preferredPhone": "Home", "phoneNumberMobile": "************", "phoneNumberHome": "************", "parentAddress": {"primaryAddressTypes": null, "secondaryAddressTypes": null, "parentBillingStreetAddress": "111 Main St", "parentBillingCity": "Indianapolis", "parentBillingState": "Indiana", "parentBillingZip": "46202", "parentMailingStreetAddress": null, "parentMailingCity": null, "parentMailingState": null, "parentMailingZip": "46202"}, "familyBackground": {}, "familySystemStats": {"conCaseSafeID": "0033g00000uuZ56AAE"}}}, {"firstName": "Second", "lastName": "Parent", "type": "family", "orgId": "org1", "inActive": false, "salesforce": {"accountId": "0013g00000VThq9AAD", "accountName": "Parent Two"}, "profileEmailAddress": "<EMAIL>", "profileData": {"phonePrimary": "************", "phoneNumberPrimary": "************", "phoneNumberWork": null, "householdInformation": {"parentStreetAddress": "1236 N New Jersey St", "parentCity": "Indianapolis", "parentState": "Indiana", "parentZip": "46202"}, "contactInformation": {"accountName": "Parent Two", "preferredEmail": "Alternate", "homeEmail": "<EMAIL>", "workEmail": "<EMAIL>", "alternateEmail": "<EMAIL>", "phoneNumberOther": null, "doNotCall": false}, "preferredPhone": "Mobile", "phoneNumberMobile": "************", "phoneNumberHome": null, "parentAddress": {"primaryAddressTypes": null, "secondaryAddressTypes": null, "parentBillingStreetAddress": "1236 N New Jersey St", "parentBillingCity": "Indianapolis", "parentBillingState": "Indiana", "parentBillingZip": "46202", "parentMailingStreetAddress": null, "parentMailingCity": null, "parentMailingState": null, "parentMailingZip": "46202"}, "familyBackground": {}, "familySystemStats": {"conCaseSafeID": "0033g00000uufJMAAY"}}}], "staff": [{"attributes": {"type": "Classroom__c", "url": "/services/data/v42.0/sobjects/Classroom__c/a2H3g000000gx05EAA"}, "Name": "Room - 1", "Center__r": {"attributes": {"type": "Account", "url": "/services/data/v42.0/sobjects/Account/0013g00000XqlsXAAR"}, "Name": "Salesforce Location"}, "Id": "a2H3g000000gx05EAA", "Classroom_Teachers__r": {"totalSize": 2, "done": true, "records": [{"attributes": {"type": "Classroom_Teacher__c", "url": "/services/data/v42.0/sobjects/Classroom_Teacher__c/a2G3g0000014CsWEAU"}, "Name": "Teacher One", "Type__c": "Lead", "Account__r": {"attributes": {"type": "Account", "url": "/services/data/v42.0/sobjects/Account/0013g00000iL88xAAC"}, "Name": "Teacher One", "npe01__PreferredPhone__pc": "Mobile", "FirstName": "Teacher", "LastName": "One", "Id": "0013g00000iL88xAAC", "cfg_Employer_Start_Date__pc": null, "cfg_Employment_Years_Discount__pc": 0, "PersonTitle": "Lead <PERSON><PERSON><PERSON>", "Health_Insurance_Primary_Coverage__pc": null, "npe01__Preferred_Email__pc": "Work", "npe01__HomeEmail__pc": null, "npe01__WorkEmail__pc": "<EMAIL>", "npe01__AlternateEmail__pc": null, "PersonMobilePhone": "************", "PersonHomePhone": null, "npe01__WorkPhone__pc": null}}, {"attributes": {"type": "Classroom_Teacher__c", "url": "/services/data/v42.0/sobjects/Classroom_Teacher__c/a2G3g0000014CsaEAE"}, "Name": "Teacher 2", "Type__c": "CSP", "Account__r": {"attributes": {"type": "Account", "url": "/services/data/v42.0/sobjects/Account/0013g00000iL891AAC"}, "Name": "Teacher Two", "npe01__PreferredPhone__pc": "Mobile", "FirstName": "Teacher", "LastName": "Two", "Id": "0013g00000iL891AAC", "cfg_Employer_Start_Date__pc": null, "cfg_Employment_Years_Discount__pc": 0, "PersonTitle": null, "Health_Insurance_Primary_Coverage__pc": null, "npe01__Preferred_Email__pc": "Alternate", "npe01__HomeEmail__pc": null, "npe01__WorkEmail__pc": "<EMAIL>", "npe01__AlternateEmail__pc": "<EMAIL>", "PersonMobilePhone": "************", "PersonHomePhone": null, "npe01__WorkPhone__pc": null}}]}}], "expectedNewStaff": [{"firstName": "Teacher", "lastName": "One", "type": "staff", "orgId": "org1", "inActive": false, "defaultGroupId": "group1", "salesforce": {"accountId": "0013g00000iL88xAAC", "accountName": "Teacher One"}, "profileData": {"phonePrimary": "************"}, "profileEmailAddress": "<EMAIL>"}, {"firstName": "Teacher", "lastName": "Two", "type": "staff", "orgId": "org1", "inActive": false, "defaultGroupId": "group1", "salesforce": {"accountId": "0013g00000iL891AAC", "accountName": "Teacher Two"}, "profileData": {"phonePrimary": "************"}, "profileEmailAddress": "<EMAIL>"}]}