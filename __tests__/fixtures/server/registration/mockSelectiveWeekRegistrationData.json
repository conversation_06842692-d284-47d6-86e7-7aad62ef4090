{"children": [{"firstName": "Child", "lastName": "Registration", "birthday": "12/04/2023", "studentGrade": "1", "gender": "Male", "nickname": "", "billingNotesValue": "", "restrictedPickup1": "", "restrictedPickup2": "", "registrationConditionals": {"yes": [], "no": []}}], "contacts": [{"firstName": "Parent", "lastName": "Registration", "relationshipDescription": "parent", "phonePrimary": "(*************", "phoneAlt": "(*************", "profileEmailAddress": "<EMAIL>", "address": "123 main st", "city": "City", "state": "State", "zip": "4532123", "primaryCaregiver": "Yes", "authorizedPickup": "Yes", "emergencyContact": "Yes", "copyToChildren": "Yes", "childIndex": 0}, {"firstName": "Parent2", "lastName": "Registration", "relationshipDescription": "parent", "phonePrimary": "(*************", "phoneAlt": "(*************", "profileEmailAddress": "<EMAIL>", "address": "123 main st", "city": "City", "state": "State", "zip": "4532123", "primaryCaregiver": "Yes", "authorizedPickup": "Yes", "emergencyContact": "Yes", "copyToChildren": "Yes", "childIndex": 0}], "plans": [[{"_id": "2pGz2jJqd7w6xrJRY", "description": "Selective Weeks Plan 1", "type": "plan", "program": "XFSZXPXkGgbnyHdir", "frequency": "weekly", "category": "tuition", "amount": 50, "scaledAmounts": [], "ledgerAccountName": "********", "details": {"startTime": "8:00 am", "endTime": "5:00 pm", "regStartDate": *************, "regEndDate": *************, "dateType": "timePeriod", "timePeriod": "J4DxrJ8GR9RxyvWFr", "selectiveWeeks": [["03/25/2051", "03/29/2051"], ["04/01/2051", "04/05/2051"], ["04/22/2051", "04/26/2051"], ["04/29/2051", "05/03/2051"]]}, "isRequiredAdvanceNotice": true, "selectedWeeks": [0, 2], "selectedDays": ["monday", "tuesday", "wednesday", "thursday", "friday"], "planTotal": 50, "allocations": []}, {"_id": "kZPSwsXvH5qzeuqoT", "description": "Before Care", "amount": 300, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0009", "program": "XFSZXPXkGgbnyHdir", "scaledAmounts": [], "details": {}, "isRequiredAdvanceNotice": true, "selectedDays": ["monday", "tuesday", "wednesday", "thursday", "friday"], "allocations": [], "startDate": "12/18/2023", "planTotal": 300}]], "registrationFee": {"_id": "fpCPrtbMrCrDGNifP", "description": "Registration Fee", "type": "item", "amount": 50, "scaledAmounts": [], "ledgerAccountName": "15968", "planTotal": 50, "allocations": []}}