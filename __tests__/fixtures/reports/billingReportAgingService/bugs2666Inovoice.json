{"_id": "Wz2BsHDxnizDSqiKA", "invoiceDate": "9/26/2023", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "owTWxoHFuvxDcmdEu", "description": "Subsidy 2 year olds", "type": "plan", "program": "", "frequency": "monthly", "category": "tuition", "amount": 608, "scaledAmounts": [], "ledgerAccountName": "430060", "enrolledPlan": {"_id": "owTWxoHFuvxDcmdEu", "planDetails": {"_id": "owTWxoHFuvxDcmdEu", "description": "Subsidy 2 year olds", "type": "plan", "program": "", "frequency": "monthly", "category": "tuition", "amount": 801, "scaledAmounts": [], "ledgerAccountName": "430060"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 615, "amountType": "dollars", "reimbursementType": "DSS", "allocationDescription": "Reimbursable: DSS", "id": "TSkqBZMmptkyB9ZES"}], "createdAt": *************, "overrideRate": 608, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": "", "expirationDate": *************, "updatedAt": *************, "updatedBy": "sL3ohMgg5tdQz4cBF"}, "coversPeriodDesc": "Monthly period beginning 10/01/2023", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "DSS", "amount": 608, "originalAllocation": {"allocationType": "reimbursable", "amount": 615, "amountType": "dollars", "reimbursementType": "DSS", "allocationDescription": "Reimbursable: DSS", "id": "TSkqBZMmptkyB9ZES"}, "modifiedDiscount": -7, "updatedAt": *************, "updatedByPersonId": "XxRpGnsTXPDY27Rpp"}]}], "orgId": "rHbeKAToJ2ANfxvrz", "personId": "FRKgEf3Aoap7jJhHJ", "type": "planInvoice", "invoiceNumber": "2728", "openAmount": 0, "originalAmount": 7, "discountAmount": 615, "dueDate": *************, "batchStamp": 1695713381947, "openPayerAmounts": {"DSS": 608}, "updatedAt": *************, "updatedByPersonId": "XxRpGnsTXPDY27Rpp", "credits": [{"type": "credit", "amount": 7, "createdAt": *************, "creditedBy": "XxRpGnsTXPDY27Rpp", "creditReason": "other", "creditNote": "Agency Adjustment: Wrong reimbursement amount set", "creditLineItemIndex": 0, "creditLineItemOriginal": {"_id": "owTWxoHFuvxDcmdEu", "description": "Subsidy 2 year olds", "type": "plan", "program": "", "frequency": "monthly", "category": "tuition", "amount": 608, "scaledAmounts": [], "ledgerAccountName": "430060", "enrolledPlan": {"_id": "owTWxoHFuvxDcmdEu", "planDetails": {"_id": "owTWxoHFuvxDcmdEu", "description": "Subsidy 2 year olds", "type": "plan", "program": "", "frequency": "monthly", "category": "tuition", "amount": 801, "scaledAmounts": [], "ledgerAccountName": "430060"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 615, "amountType": "dollars", "reimbursementType": "DSS", "allocationDescription": "Reimbursable: DSS", "id": "TSkqBZMmptkyB9ZES"}], "createdAt": *************, "overrideRate": 608, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": "", "expirationDate": *************, "updatedAt": *************, "updatedBy": "sL3ohMgg5tdQz4cBF"}, "coversPeriodDesc": "Monthly period beginning 10/01/2023", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "DSS", "amount": 608, "originalAllocation": {"allocationType": "reimbursable", "amount": 615, "amountType": "dollars", "reimbursementType": "DSS", "allocationDescription": "Reimbursable: DSS", "id": "TSkqBZMmptkyB9ZES"}, "modifiedDiscount": -7, "updatedAt": *************, "updatedByPersonId": "XxRpGnsTXPDY27Rpp"}]}}, {"type": "credit", "amount": 608, "payerReconciliationBatchLabel": "2023-11-27T13:48:01-05:00 - <PERSON>", "createdAt": *************, "creditedBy": "XxRpGnsTXPDY27Rpp", "creditReason": "reimbursable", "creditPayerSource": "DSS", "checkNumber": "11/15/23", "payerOverpaymentAmount": 277, "payerOverpaymentDestination": "adjustment-account", "payerTotalReceiptAmount": 885, "depositBatchId": "Ap9nZ5tnhxR7pxe6L", "depositBatchIdAssignedAt": *************}]}