[{"_id": "nTvbx24M2dbM9w6tu", "name": "321-Mariposa Local", "parentOrgId": "GtAoTHqGeLk9BR8iw", "createdAt": 1541702400000, "registrationSource": "app", "registrationNeeds": ["Family Communication", "Attendance", "Billing & Payments", "Curriculum Management", "Menu Planning", "Waitlists", "Calendar", "Real-time Alerts"], "curriculumStandard": ["CCN"], "availableAssessmentLevels": [{"label": "Not Measured", "value": 0}, {"label": "Introduced", "value": 1}, {"label": "Meets", "value": 2}, {"label": "Exceeds", "value": 3}], "registrationIndustry": "childcare", "registrationChildcareType": "single-center", "registrationRole": "provider", "customizations": {"moments/ouch/enabled": true, "moments/incident/enabled": true, "people/pinCodeCheckin/enabled": true, "people/familyCheckin/enabled": true, "moments/supplies/enabled": true, "moments/checkin/autocheckout": true, "moments/activity/enabled": true, "moments/alert/enabled": true, "people/multipleCheckin/enabled": true, "people/nametoface/requiresCompletedBy": true, "moments/food/infantGroupOptions": "true", "moments/illness/enabled": true, "messages/administrativeVisibility/enabled": true, "reports/billingAdminSummaryReport/enabled": true, "inquiries/registration/enabled": true, "people/types/customerSpecificInquiryProfileFields": true, "modules/curriculum/ageGroups": true, "modules/curriculum/hideMaterials": true, "modules/curriculum/hideHomework": true, "modules/curriculum/requireThemes": true, "moments/portfolio/enabled": true, "moments/medical/enabled": true, "moments/medical/useProfileMedications": true, "moments/checkin/notifyWithoutReservation": true, "moments/potty/enabled": true, "moments/food/enabled": true, "moments/sleep/enabled": true, "moments/checkin/staffLockdown": false, "billing/enabled": true, "reservations/enabled": true, "inquiries/enabled": true, "messages/suppressStaffMessageCenterNotifications/enabled": true, "people/immunizationAlerts/enabled": true, "report/classList/enabled": true, "moments/mood/enabled": true, "moments/covidHealthCheck/enabled": true, "moments/covidHealth/enabled": true, "billing/requireLedgerAccountName/enabled": true, "timeCards/enabled": true, "people/staffPay/enabled": true, "people/staffRequiredPinCodeCheckin/enabled": false, "moments/alert/adminOnly": false, "moments/checkout/showStaffCertificationMessage": true, "moments/safety/enabled": false, "moments/behaviorChild/enabled": true, "moments/mood/adminOnly": false, "report/californiaSpecific/enabled": true, "report/classListSchedule/enabled": true, "moments/wonderlandToileting/enabled": true, "moments/ouch/adminOnly": true, "people/requireRoles": true, "billing/configuration/postUsingPeriodStart": true, "curriculumBank/activities": true, "curriculumBank/management": true, "billing/queueAutopayments/enabled": false, "billing/disableCards/enabled": false, "billing/requirePaymentMethod/enabled": false, "billing/autoProrateByDate/enabled": true, "people/qrCodeCheckin/enabled": true, "report/waitList/enabled": true, "curriculumBank/globalAndLocal": true, "billing/configuration/preventManualInvoicePriorPeriod": false, "billing/payments/preventDebitCards": false, "mpsurvey/enabled": false, "modules/curriculum/hidden": false, "integrations/dreambox/enabled": true, "registrationFlow": true, "people/types/customerSpecificProfileFields": true, "billing/configuration/couponCodes": true, "billing/configuration/bundles": true, "billing/configuration/heldfunds/enabled": true, "billing/configuration/punchCards": true, "billing/configuration/payerCashPostingLedger": true, "billing/frequency/monthlyRateBilledWeekly": true, "billing/plans/variableMonthlySubsidies": true}, "enabledMomentTypes": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "safety", "wonderlandToileting", "safetyEducare"], "registrationGuided": true, "temporary": true, "language": "translationsEnChildCare", "metrics": {"momentsPosted": 2122, "messagesSent": 11985}, "engagementCounts": [{"dayTimestamp": *************, "providerCount": 1, "familyCount": 0}], "onboardingData": {"currentTrack": "Organize"}, "billing": {"enabled": true, "scheduling": {"generateMonthDay": "26", "generateWhen": "advance", "generateDay": "friday", "lateFee": "0", "gracePeriodDays": "10", "generateBiWeeklyDate": "04/05/2021", "disablePaymentsBeforeDueDate": false, "monthlyPlanDueDay": null, "weeklyPlanDueDay": "sunday", "assessLateFee": true, "assessLateFeeDays": 1, "assessLateFeeItemId": "XbDtiJaE2EnZxbX2d", "missedInvoiceInterval": "3"}, "plansAndItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, {"_id": "z7KPhPhdotTddng4h", "description": "Toddlers 1-2", "amount": 450, "type": "plan", "frequency": "weekly"}, {"_id": "XvBB4ygskn3MAuLQL", "description": "Enrollment Feez", "amount": 75, "type": "item", "suspendUntil": null, "scaledAmounts": [], "program": "w7AHNy2MGDjqecmGs", "ledgerAccountName": "9988", "details": {"regStartDate": *************, "regEndDate": *************, "grades": ["Preschool", "K", "1", "2", "3"], "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": *************, "serviceEndDate": *************}}, {"_id": "T5yECT8ia9e8ujDjg", "description": "Tech Fee", "amount": 100, "type": "item"}, {"_id": "4fQK63BxDdnXexd7g", "description": "Preschool 3-5", "amount": 175, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1234"}, {"_id": "Tvog42xz3eTNWFiP2", "description": "Field Trip", "amount": 0, "type": "item", "scaledAmounts": [], "program": "wDRo5Lia3bLaYYhsD", "ledgerAccountName": "123456", "details": {}, "dropInDailyRate": true}, {"_id": "JrKxL4xEXYPK9CS9D", "description": "Late Pickup Fee", "amount": 10, "type": "item"}, {"_id": "ekBHMveB9zjPBMTwR", "description": "Late Payment Fee", "amount": 10, "type": "item", "ledgerAccountName": "1877", "refundableDeposit": true}, {"_id": "9b26ETQdjCWzaQb8B", "description": "Regular Day 2 day plan (toddlers) ", "amount": 415, "type": "plan", "frequency": "monthly"}, {"_id": "R3DdyKE2tG3xe4svN", "description": "Regular Day, full week", "amount": 590, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "2323"}, {"_id": "ygEHvddnej9yccg6p", "description": "Stay and Play drop in", "amount": 50, "type": "item"}, {"_id": "SeYtDsyEmnQjSNNBS", "description": "Regular Day 3 day plan (toddlers)", "amount": 490, "type": "plan", "frequency": "monthly"}, {"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 215, "type": "plan", "frequency": "semimonthly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1234", "scaledAmounts": [], "program": ""}, {"_id": "9AfMyqvGT27WdRhMG", "description": "Frog<PERSON> (2-3)", "amount": 100, "type": "plan", "frequency": "monthly", "scaledAmounts": [], "category": "", "program": "fCiFANMAx9i5Ht7St", "ledgerAccountName": "9874"}, {"_id": "MnxXa3ay6YnJtQf48", "description": "Past Due amount as of 4/3/19", "amount": 1, "type": "item"}, {"_id": "w3djzJDQyPafET8Jy", "description": "Threes (Weekly)", "amount": 150, "type": "plan", "frequency": "weekly"}, {"_id": "d5RWqdJobwsuRuM6m", "description": "After Care", "amount": 275, "type": "plan", "frequency": "weekly", "suspendUntil": *************, "ledgerAccountName": "1111", "scaledAmounts": [], "program": "ucNZFMEB9esij3hZ4", "details": {"startTime": "4:00 pm", "endTime": "8:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["Preschool", "K", "1", "2", "3"], "scheduleType": "5ATwY9i6nPmKP8Fac", "dateType": "timePeriod", "timePeriod": "yWSHbfwBArcLRG4dz"}}, {"_id": "JuKT8L7EDKm77KtX9", "description": "Supplies", "amount": 10, "type": "item"}, {"_id": "wKMPjFHWcdMGy8SdN", "description": "Infants - Daily", "amount": 100, "type": "plan", "frequency": "daily"}, {"_id": "ezqJzT97NkiRstem7", "description": "Pre-K Day Rate", "amount": 30, "type": "plan", "frequency": "daily"}, {"_id": "RNcoK6BJuD5TxyBqZ", "description": "Infant Tuition - Weekly", "amount": 100, "type": "item"}, {"_id": "WpXFKwQf8hFupDLbK", "description": "Toddler - Full (5 Day) (monthly)", "amount": 1200, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1111"}, {"_id": "DzmykiRh7vrpw3iyJ", "description": "Infants Full Time ", "amount": 1100, "type": "plan", "frequency": "monthly", "category": "tuition", "suspendUntil": null, "ledgerAccountName": "1234"}, {"_id": "cpbgS7fqYus4nMnoo", "description": "New Weekly Plan", "amount": 1250, "type": "plan", "frequency": "weekly", "category": "tuition"}, {"_id": "SMAx9jf7ZyDddPc76", "description": "Past Due Balance Transfer", "amount": 0, "type": "item"}, {"_id": "zfNcJzCjCuDQkMkGA", "description": "UnenrollmentPlan", "amount": 1000, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1234", "suspendUntil": *************, "archived": true}, {"_id": "5zs3e4Dc5FNvrzKve", "description": "Tsting plan not tuition", "amount": 4500, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1234"}, {"_id": "WJd5NNC7HF4PM5d7v", "description": "<PERSON><PERSON> - 3 day full time", "amount": 800, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0975"}, {"_id": "Jr72s2mPyok7m5hQw", "description": "Supply Fee", "amount": 50, "type": "item", "ledgerAccountName": "1000"}, {"_id": "uzbRmNEnPzNMStD8Q", "description": "<PERSON>les - 2 day", "amount": 450, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0282"}, {"_id": "bbQzFffBY5KtBoPXP", "description": "Piano Class", "amount": 125, "type": "item", "ledgerAccountName": "0090", "suspendUntil": *************, "scaledAmounts": [], "program": "w7AHNy2MGDjqecmGs", "details": {}}, {"_id": "2BvYj3oEMhgSHrNvd", "description": "Bimonthly Plan", "amount": 1100, "type": "plan", "frequency": "semimonthly", "category": "tuition", "ledgerAccountName": "9999"}, {"_id": "kZPSwsXvH5qzeuqoT", "description": "Before Care Edited", "amount": 300, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0009", "scaledAmounts": [], "program": "ucNZFMEB9esij3hZ4", "details": {"startTime": "4:00 am", "endTime": "8:00 am", "regStartDate": *************, "regEndDate": *************, "grades": ["Preschool", "K", "1", "2", "3"], "scheduleType": "WAayXcBzyaRbFxuDd"}}, {"_id": "2CoNRQkwhnDXr44Xg", "description": "Tuition Plan", "amount": 500, "type": "plan", "frequency": "biweekly", "category": "tuition", "ledgerAccountName": "3333"}, {"_id": "yFqynAWPFm7t6gXKw", "description": "Todd<PERSON> (Bi-weekly)", "amount": 500, "type": "plan", "frequency": "biweekly", "category": "tuition", "ledgerAccountName": "3693"}, {"_id": "XbDtiJaE2EnZxbX2d", "description": "Advanced Tuition", "amount": 0, "type": "item", "ledgerAccountName": "1234"}, {"_id": "8qNxxfASYcePBi4XA", "description": "PreK Full Time", "amount": 1250, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1000"}, {"_id": "fa5XsPjDR4fHH3i3g", "description": "Infant - Full Time ", "amount": 686.19, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4321", "scaledAmounts": [], "program": "ucNZFMEB9esij3hZ4", "details": {}}, {"_id": "A4jJMWWqboB4hiXZo", "description": "Plan no category ", "amount": 500, "type": "plan", "frequency": "monthly", "category": "", "ledgerAccountName": "0000"}, {"_id": "okLkLXi8wizLypeoi", "description": "Plan w category ", "amount": 500, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0000"}, {"_id": "utXYN39S8tQkTSBc6", "description": "Infant 3-day ", "amount": 1000, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4029"}, {"_id": "xzYXzMw9aozb3fFKy", "description": "Security Deposit", "amount": 0, "type": "item", "refundableDeposit": true, "ledgerAccountName": "4455"}, {"_id": "SCuWtdyYpwJjBuFxT", "description": "Enrollment Feez", "amount": 10, "type": "item", "ledgerAccountName": "1234", "archived": true}, {"_id": "GuKoeioeS8MeMkeZq", "description": "Enrollment Feez", "amount": 10, "type": "item", "ledgerAccountName": "1234"}, {"_id": "eDRpegQvzJiFe8J5y", "description": "ts", "amount": 0, "type": "plan", "frequency": "hourly", "category": "tuition", "ledgerAccountName": "1234"}, {"_id": "57GZroDnrv8c3jAgK", "description": "Registration Fee", "amount": 20, "type": "item", "ledgerAccountName": "123456", "scaledAmounts": [], "program": "", "details": {}}, {"_id": "4zhgyGdxptJiB2F94", "description": "scaled1", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "fCiFANMAx9i5Ht7St", "amount": 50, "scaledAmounts": [10, 20, 30, 40, 50], "ledgerAccountName": "1234", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["Preschool", "K", "1", "2"], "scheduleType": "WAayXcBzyaRbFxuDd", "dateType": "timePeriod", "timePeriod": "yWSHbfwBArcLRG4dz"}}, {"_id": "i4euWgE5DGm2whzYp", "description": "scaled2", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "fCiFANMAx9i5Ht7St", "amount": 50, "scaledAmounts": [10, 20, 30, 40, 50], "ledgerAccountName": "4321", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["Preschool", "K", "1", "2"], "dateType": "timePeriod", "timePeriod": "yWSHbfwBArcLRG4dz"}}, {"_id": "KXEizkw8Kce4rx7SP", "type": "bundle", "plans": ["4zhgyGdxptJiB2F94", "i4euWgE5DGm2whzYp"], "description": "scaled1 and scaled2", "scaledAmounts": [[5, 10, 15, 20, 25], [10, 15, 20, 25, 30], [15, 20, 25, 30, 35], [20, 25, 30, 35, 40], [25, 30, 35, 40, 42]]}, {"_id": "NSjFJbgF4hrmziqWB", "description": "scaled3", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "fCiFANMAx9i5Ht7St", "amount": 50, "scaledAmounts": [10, 20, 30, 40, 50], "ledgerAccountName": "7894"}, {"_id": "mruo7hgTv94xWgJY5", "description": "scaled4", "type": "plan", "frequency": "scaledWeekly", "category": "tuition", "program": "fCiFANMAx9i5Ht7St", "amount": 50, "scaledAmounts": [10, 20, 30, 40, 50], "ledgerAccountName": "4789"}, {"_id": "Ex8AtHLNLT8jbNBps", "type": "bundle", "plans": ["NSjFJbgF4hrmziqWB", "mruo7hgTv94xWgJY5"], "description": "scaled3 and scaled4", "scaledAmounts": [[5, 10, 15, 20, 25], [10, 15, 20, 25, 30], [15, 20, 25, 30, 35], [20, 25, 30, 35, 40], [25, 30, 35, 40, 45]]}, {"_id": "GrtNDheEdMuFuMFAh", "description": "New plan 1234", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 10, "scaledAmounts": [], "ledgerAccountName": "123456"}, {"_id": "Sy3Znw6t7mwQsHMoZ", "description": "New Plan 34", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 20, "scaledAmounts": [], "ledgerAccountName": "789456"}, {"_id": "Q8xt2wbC2t9qLT7yb", "description": "New Plan 27", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 20, "scaledAmounts": [], "ledgerAccountName": "1234"}, {"_id": "c8YTRiR5Y9yCGc55i", "description": "New Plan 4", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "**********"}, {"_id": "uEx9h5EipQY7Dqoup", "description": "Test Item", "type": "item", "amount": 55, "scaledAmounts": [], "ledgerAccountName": "987654"}, {"_id": "8bv4eERy7t75Nnyxb", "description": "TimeZonePLan", "type": "plan", "program": "", "frequency": "monthly", "category": "tuition", "amount": 1, "scaledAmounts": [], "expires": *************, "ledgerAccountName": "777", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "dateType": "timePeriod", "timePeriod": "yWSHbfwBArcLRG4dz"}}, {"_id": "KKbMYuSmcYFgdnhGc", "description": "TimeZoneTest2", "type": "item", "program": "fCiFANMAx9i5Ht7St", "amount": 1, "scaledAmounts": [], "expires": *************, "ledgerAccountName": "778", "details": {"regStartDate": *************, "regEndDate": *************, "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": *************, "serviceEndDate": *************}}, {"_id": "arEzHvY6ex6AMgSr5", "description": "Propgate Test", "type": "item", "program": "fCiFANMAx9i5Ht7St", "amount": 99, "scaledAmounts": [], "ledgerAccountName": "7777", "details": {"startTime": "5:00 am", "endTime": "5:30 am", "regStartDate": *************, "regEndDate": *************, "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": *************, "serviceEndDate": *************}}, {"_id": "qMxdv5oagiWEz8FDS", "description": "Punch Card", "type": "punchcard", "program": "ucNZFMEB9esij3hZ4", "numberOfDays": "10", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "1234"}, {"_id": "spoSKDX4HpNfbp3iq", "description": "4 weekly payments", "type": "plan", "program": "", "frequency": "weekly_four_in_month", "category": "tuition", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "8888", "details": {}}, {"_id": "wJbfLytZWwDQm9dx5", "description": "Monthly Scaled", "type": "plan", "program": "ucNZFMEB9esij3hZ4", "frequency": "scaledMonthly", "category": "tuition", "amount": 50, "scaledAmounts": [10, 20, 30, 40, 50], "expires": *************, "ledgerAccountName": "7894", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["K", "1", "2", "3"], "dateType": "timePeriod", "timePeriod": "yWSHbfwBArcLRG4dz"}}, {"_id": "Kd2tBJBy4Hni7uGNT", "description": "DailyBilledWeekly", "type": "plan", "program": "ucNZFMEB9esij3hZ4", "frequency": "weekly_scheduled_daily", "category": "tuition", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "987987", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["Preschool", "K", "1", "2", "3"], "dateType": "timePeriod", "timePeriod": "yWSHbfwBArcLRG4dz"}}, {"_id": "6HpFTzneqhQLcDBED", "description": "DailyChargedMonthly", "type": "plan", "program": "w7AHNy2MGDjqecmGs", "frequency": "charged_daily_invoiced_monthly", "category": "tuition", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "8888", "details": {"startTime": "7:00 am", "endTime": "7:00 pm", "regStartDate": *************, "regEndDate": *************, "grades": ["Preschool", "K", "1", "2", "3"], "scheduleType": "5GsFf2wAFTgWnyo5r", "dateType": "timePeriod", "timePeriod": "yWSHbfwBArcLRG4dz"}}, {"_id": "Hs3ZrH5j5m5LnGtXS", "description": "TestUnVoid", "type": "plan", "program": "ucNZFMEB9esij3hZ4", "amount": 1280, "scaledAmounts": [], "ledgerAccountName": "987987", "details": {}, "frequency": "monthly", "category": "tuition"}, {"_id": "Jv7yf52e8zM4daYiw", "description": "ScaledWeekly", "type": "plan", "program": "", "frequency": "scaledWeekly", "category": "tuition", "amount": 25, "scaledAmounts": [100, 75, 50, 40, 25], "ledgerAccountName": "**********", "details": {}}], "stats": {"totalEnrollmentCount": 773}, "legalEntity": {"business_name": "MomentPath", "business_tax_id": "U2FsdGVkX19LTTclvlvESRh9rHFIVdN76YcL+HqTTJA=", "address": "15639 Hawks <PERSON>", "city": "Carmel", "state": "IN", "zipcode": "46033"}, "billingMaps": {"manualPaymentDeposits": {"accountName": "1111"}, "manualOtherCredits": {"accountName": "4000"}, "accountsReceivable": {"accountName": "1205"}, "settlementFees": {"accountName": "6045"}, "otherPayerDiscounts": {"accountName": "1000"}, "securityDepositsLiability": {"accountName": "4455"}, "customerLiabilityPayable": {"accountName": "6555"}, "badDebt": {"accountName": "8844"}, "creditMemos": {"accountName": "1205"}, "payrollDeduction": {"accountName": "2180"}, "onlinePaymentDeposits": {"accountName": "9877"}, "undepositedFunds": {"accountName": "2222"}, "unappliedCashApplied": {"accountName": ""}, "unappliedCash": {"accountName": ""}}, "passthroughFees": false, "stripeAccountIdLegacy": "acct_1EYcIhKLALOt8Szt", "stripeInfoLegacy": {"id": "acct_1EYcIhKLALOt8Szt", "object": "account", "bank_accounts": {"object": "list", "data": [{"id": "ba_1EYcIhKLALOt8SztHEsqqunZ", "object": "bank_account", "account": "acct_1EYcIhKLALOt8Szt", "account_holder_name": null, "account_holder_type": null, "bank_name": "JPMORGAN CHASE", "country": "US", "currency": "usd", "default_for_currency": true, "fingerprint": "oRxCiSzkYWN9sLm2", "last4": "9332", "metadata": {}, "name": null, "routing_number": "*********", "status": "new"}], "has_more": false, "total_count": 1, "url": "/v1/accounts/acct_1EYcIhKLALOt8Szt/bank_accounts"}, "business_logo": null, "business_logo_large": null, "business_name": "MomentPath", "business_primary_color": null, "business_url": null, "charges_enabled": true, "country": "US", "created": **********, "currencies_supported": ["usd", "aed", "afn", "all", "amd", "ang", "aoa", "ars", "aud", "awg", "azn", "bam", "bbd", "bdt", "bgn", "bif", "bmd", "bnd", "bob", "brl", "bsd", "bwp", "bzd", "cad", "cdf", "chf", "clp", "cny", "cop", "crc", "cve", "czk", "djf", "dkk", "dop", "dzd", "egp", "etb", "eur", "fjd", "fkp", "gbp", "gel", "gip", "gmd", "gnf", "gtq", "gyd", "hkd", "hnl", "hrk", "htg", "huf", "idr", "ils", "inr", "isk", "jmd", "jpy", "kes", "kgs", "khr", "kmf", "krw", "kyd", "kzt", "lak", "lbp", "lkr", "lrd", "lsl", "mad", "mdl", "mga", "mkd", "mmk", "mnt", "mop", "mro", "mur", "mvr", "mwk", "mxn", "myr", "mzn", "nad", "ngn", "nio", "nok", "npr", "nzd", "pab", "pen", "pgk", "php", "pkr", "pln", "pyg", "qar", "ron", "rsd", "rub", "rwf", "sar", "sbd", "scr", "sek", "sgd", "shp", "sll", "sos", "srd", "std", "szl", "thb", "tjs", "top", "try", "ttd", "twd", "tzs", "uah", "ugx", "uyu", "uzs", "vnd", "vuv", "wst", "xaf", "xcd", "xof", "xpf", "yer", "zar", "zmw"], "debit_negative_balances": false, "decline_charge_on": {"avs_failure": false, "cvc_failure": false}, "default_currency": "usd", "details_submitted": true, "display_name": null, "email": "<EMAIL>", "external_accounts": {"object": "list", "data": [{"id": "ba_1EYcIhKLALOt8SztHEsqqunZ", "object": "bank_account", "account": "acct_1EYcIhKLALOt8Szt", "account_holder_name": null, "account_holder_type": null, "bank_name": "JPMORGAN CHASE", "country": "US", "currency": "usd", "default_for_currency": true, "fingerprint": "oRxCiSzkYWN9sLm2", "last4": "9332", "metadata": {}, "name": null, "routing_number": "*********", "status": "new"}], "has_more": false, "total_count": 1, "url": "/v1/accounts/acct_1EYcIhKLALOt8Szt/external_accounts"}, "legal_entity": {"additional_owners": [], "address": {"city": "Carmel", "country": "US", "line1": "15639 Hawks <PERSON>", "line2": null, "postal_code": "46033", "state": "IN"}, "address_kana": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null, "town": null}, "address_kanji": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null, "town": null}, "business_name": "MomentPath", "business_name_kana": null, "business_name_kanji": null, "business_tax_id_provided": true, "business_vat_id_provided": false, "dob": {"day": 4, "month": 10, "year": 1976}, "first_name": "<PERSON><PERSON><PERSON>", "first_name_kana": null, "first_name_kanji": null, "gender": null, "last_name": "<PERSON><PERSON><PERSON>", "last_name_kana": null, "last_name_kanji": null, "maiden_name": null, "personal_address": {"city": null, "country": "US", "line1": null, "line2": null, "postal_code": null, "state": null}, "personal_address_kana": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null, "town": null}, "personal_address_kanji": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null, "town": null}, "personal_email": null, "personal_id_number_provided": false, "personal_phone_number": null, "phone_number": null, "ssn_last_4_provided": true, "type": "company", "verification": {"details": null, "details_code": null, "document": null, "document_back": null, "status": "pending"}}, "managed": true, "mcc": null, "metadata": {}, "product_description": null, "statement_descriptor": "", "support_address": null, "support_email": null, "support_phone": null, "support_url": null, "timezone": "Etc/UTC", "tos_acceptance": {"date": **********, "ip": "*************", "user_agent": null}, "transfer_schedule": {"delay_days": 2, "interval": "daily"}, "transfer_statement_descriptor": null, "transfers_enabled": true, "type": "custom", "verification": {"contacted": false, "disabled_reason": null, "due_by": null, "fields_needed": []}}, "toplinePercentDiscounts": false, "paymentFees": {"cardFee": 0.1, "cardRate": 0.025, "achFee": 0.25, "achRate": 0.25}, "adyenInfo": {"accountCode": "****************"}, "netSuite": {"exportEnabled": true, "subsidiary": "Top Company : LSP : Franchise : 2100 Oxford Glen, LLC", "suppressLocation": false}, "ledgerAccountCodes": [{"description": "1205 · Accounts Receivable-MomentPath", "accountName": "Other Current asset", "accountNumber": "1205"}, {"description": "2020 · Customer Liability Exchange", "accountName": "Other Current Liability", "accountNumber": "2020"}, {"description": "2180 · Section 125 Payable", "accountName": "Other Current Liability", "accountNumber": "2180"}, {"description": "2181 · Deferred Revenue", "accountName": "Other Current Liability", "accountNumber": "2181"}, {"description": "2210 · Security Deposits", "accountName": "Other Current Liability", "accountNumber": "2210"}, {"description": "4000 · Income", "accountName": "Income", "accountNumber": "4000"}, {"description": "4000 · Income:4200 · Essential Worker Tuition", "accountName": "Income", "accountNumber": "4200"}, {"description": "4010 · Registration Fees", "accountName": "Income", "accountNumber": "4010"}, {"description": "4020 · Tuition", "accountName": "Income", "accountNumber": "4020"}, {"description": "4021 · Before & After Care Tuition", "accountName": "Income", "accountNumber": "4021"}, {"description": "4023 · Infant Tuition", "accountName": "Income", "accountNumber": "4023"}, {"description": "4024 · Kindergarten Tuition", "accountName": "Income", "accountNumber": "4024"}, {"description": "4027 · Nursery Tuition", "accountName": "Income", "accountNumber": "4027"}, {"description": "4029 · Pre-K Tuition", "accountName": "Income", "accountNumber": "4029"}, {"description": "4031 · Summer Camp Activity Fee", "accountName": "Income", "accountNumber": "4031"}, {"description": "4032 · Open House Discount", "accountName": "Income", "accountNumber": "4032"}, {"description": "4033 · CAP Discount", "accountName": "Income", "accountNumber": "4033"}, {"description": "4034 · Sibling Discount", "accountName": "Income", "accountNumber": "4034"}, {"description": "4036 · Employee Discount", "accountName": "Income", "accountNumber": "4036"}, {"description": "4039 · Loyalty Program Discount", "accountName": "Income", "accountNumber": "4039"}, {"description": "4040 · Other Program Income", "accountName": "Income", "accountNumber": "4040"}, {"description": "4040 · Other Program Income:4038 · Covid-19 Discount", "accountName": "Income", "accountNumber": "4038"}, {"description": "4040 · Other Program Income:4046 · Music", "accountName": "Income", "accountNumber": "4046"}, {"description": "4040 · Other Program Income:4063 · Parents Night Out", "accountName": "Income", "accountNumber": "4063"}, {"description": "4040 · Other Program Income:4066 · Summer Enrichment Program", "accountName": "Income", "accountNumber": "4066"}, {"description": "4040 · Other Program Income:4072 · Cot Sheet", "accountName": "Income", "accountNumber": "4072"}, {"description": "4040 · Other Program Income:4210 ·SupplementalSchool-Age Program", "accountName": "Income", "accountNumber": "4210"}, {"description": "4041 · Late Payment Charge", "accountName": "Income", "accountNumber": "4041"}, {"description": "4042 · Late Pick Up Fee", "accountName": "Income", "accountNumber": "4042"}, {"description": "4043 · Retnd. Check Charge", "accountName": "Income", "accountNumber": "4043"}, {"description": "4044 · Seized Security Deposits", "accountName": "Income", "accountNumber": "4044"}, {"description": "4045 · ParentView Service", "accountName": "Income", "accountNumber": "4045"}, {"description": "4047 · Transportation", "accountName": "Income", "accountNumber": "4047"}, {"description": "4048 · Dance Classes", "accountName": "Income", "accountNumber": "4048"}, {"description": "4049 · Karate", "accountName": "Income", "accountNumber": "4049"}, {"description": "4050 · Soccer/Amazing Athletes", "accountName": "Income", "accountNumber": "4050"}, {"description": "4051 · Art Classes", "accountName": "Income", "accountNumber": "4051"}, {"description": "4052 · Gymnastics/Exercise", "accountName": "Income", "accountNumber": "4052"}, {"description": "4053 · Hot Lunch Plan", "accountName": "Income", "accountNumber": "4053"}, {"description": "4054 · Extended Hours Service", "accountName": "Income", "accountNumber": "4054"}, {"description": "4055 · Pizza Parlor Fridays", "accountName": "Income", "accountNumber": "4055"}, {"description": "4057 · Computer Classes", "accountName": "Income", "accountNumber": "4057"}, {"description": "4058 · Breakfast Plan", "accountName": "Income", "accountNumber": "4058"}, {"description": "4060 · Piano Class", "accountName": "Income", "accountNumber": "4060"}, {"description": "4065 · Back Up Care", "accountName": "Income", "accountNumber": "4065"}, {"description": "4067 · Field Trip", "accountName": "Income", "accountNumber": "4067"}, {"description": "4068 · STEM Class", "accountName": "Income", "accountNumber": "4068"}, {"description": "4069 · Leadership Class", "accountName": "Income", "accountNumber": "4069"}, {"description": "4070 · Cooking Class", "accountName": "Income", "accountNumber": "4070"}, {"description": "4077 · Lil Sports", "accountName": "Income", "accountNumber": "4077"}, {"description": "4078 · Swim & Gym", "accountName": "Income", "accountNumber": "4078"}, {"description": "4500 · Miscellaneous Income", "accountName": "Income", "accountNumber": "4500"}, {"description": "4510 · Convenience Fee Income", "accountName": "Income", "accountNumber": "4510"}, {"description": "6045 · Merchant Fees", "accountName": "Expense", "accountNumber": "6045"}, {"description": "8000  Uncollectable Bad Debt Expense", "accountName": "Expense", "accountNumber": "8000"}], "sage": {"locationCode": "123"}, "legalEntityHistory": [{"business_name": "Ye Olde Mariposa", "business_tax_id": "U2FsdGVkX19flAjHaWP7Uo3jkCfjhNlKdkFUBIEV56M=", "address": "123 North Old Street", "city": "Oldhambria", "state": "IN", "zipcode": "44444", "endDate": "2022-02-01"}, {"business_name": "MariposaOld", "business_tax_id": "U2FsdGVkX1/FlqLJsEqWN6whPbnThyh7HILA1MZWLvU=", "address": "123 North Street", "city": "Indianapolis", "state": "IN", "zipcode": "46666", "startDate": "1999-01-01", "endDate": "2021-03-01"}], "programs": [{"_id": "ucNZFMEB9esij3hZ4", "name": "Before and After Care", "isActive": true, "isRequiredAdvanceNotice": true}, {"_id": "fCiFANMAx9i5Ht7St", "name": "Non-School Days", "isActive": true, "isRequiredAdvanceNotice": false}, {"_id": "w7AHNy2MGDjqecmGs", "name": "Electives", "isActive": true, "isRequiredAdvanceNotice": false}, {"_id": "wDRo5Lia3bLaYYhsD", "name": "Camps", "isActive": true, "isRequiredAdvanceNotice": false}], "allowDepositReportEdit": false, "excludedManualPayTypes": [], "couponCodes": [{"_id": "e8Bv68R2e4XzdsG4k", "code": "SPRING23", "description": "Spring program discount", "amountType": "dollars", "amount": 10, "regStartDate": 1678766400000, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "numberOfRegistrations": 32, "oneTimeCharges": ["57GZroDnrv8c3jAgK"]}, {"_id": "wLFSW8Q38NnTxwnZ8", "code": "TIMETEST23", "description": "testing time zones", "amountType": "dollars", "amount": 1, "regStartDate": *************, "regEndDate": *************, "expirationDate": *************, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "timePeriods": ["yWSHbfwBArcLRG4dz"]}, {"_id": "E4MJEsdyLTBEjPp38", "code": "ONEUSE23", "description": "One time use coupon", "amountType": "dollars", "amount": 100, "regStartDate": *************, "usedWithOtherCoupons": true, "useWithDiscounts": false, "regEndDate": *************, "expirationDate": *************, "ledgerCode": "9999", "numberOfRegistrations": 2, "usedBy": [{"childId": "seKb5oRawvKW7xabM", "familyId": ["XHZL9GHiDnFQaj28Q", "XHZL9GHiDnFQaj28Q", "XHZL9GHiDnFQaj28Q"], "invoice": "zfkpKuocBE3sLhAK8"}], "usedWithDiscounts": true, "isSingleInstallmentCoupon": true, "useCouponInBundles": "no"}, {"_id": "PC77rpvLWKJpidRwe", "code": "ONEBUNDLE23", "description": "This coupon is used on only most expensive bundle", "amountType": "dollars", "amount": 10, "regStartDate": *************, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "usedBy": [], "ledgerCode": "987987", "numberOfRegistrations": 1, "useCouponInBundles": "yes-most", "regEndDate": *************, "expirationDate": 1703998800000}, {"_id": "rg9SzTSqKtATeNaYm", "code": "ALLBUNDLE23", "description": "", "amountType": "dollars", "amount": 5, "useCouponInBundles": "yes-all", "regStartDate": 1691467200000, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "usedBy": [], "ledgerCode": "987987"}, {"_id": "YFKp52whMp2ahFBTQ", "code": "100OFF", "description": "all free", "amountType": "percent", "amount": 100, "useCouponInBundles": "yes-all", "regStartDate": 1692590400000, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "timePeriods": [], "billingPlans": [], "oneTimeCharges": [], "isSingleInstallmentCoupon": false, "usedBy": [], "ledgerCode": "654987", "numberOfRegistrations": 1}, {"_id": "NoxbKRd9Bx6HdsahK", "code": "ALLGOOD", "description": "All Good", "amountType": "percent", "amount": 10, "useCouponInBundles": "yes-all", "regStartDate": 1690862400000, "usedWithOtherCoupons": true, "usedWithDiscounts": true, "timePeriods": [], "billingPlans": [], "oneTimeCharges": [], "isSingleInstallmentCoupon": false, "usedBy": [], "regEndDate": *************, "expirationDate": *************, "ledgerCode": "6546546574"}], "timePeriods": [{"_id": "yWSHbfwBArcLRG4dz", "name": "Summer", "startDate": *************, "endDate": *************}]}, "counters": {"invoices": 2625}, "planDetails": {}, "valueOverrides": {"inquiryProfileFields": [{"name": "birthday", "description": "Date of birth", "type": "date"}, {"name": "startDate", "description": "Date of birth", "type": "date"}, {"name": "family<PERSON><PERSON>bers", "description": "Family Member", "type": "fieldGroup", "multiple": true, "fields": [{"name": "firstName", "description": "First Name", "type": "string"}, {"name": "lastName", "description": "Last Name", "type": "string"}, {"name": "parentStreetAddress", "description": "Street Address", "type": "string"}, {"name": "parentCity", "description": "City", "type": "string"}, {"name": "parentState", "description": "State", "type": "string"}, {"name": "parentZip", "description": "Zipcode", "type": "string"}, {"name": "phoneNumberHome", "description": "Phone", "type": "string"}, {"name": "email", "description": "Email", "type": "string"}]}], "profileFields": [{"name": "primaryFamily", "description": "Primary Family", "type": "query", "source": "primaryFamily"}, {"name": "enrollmentDate", "description": "Enrollment Date", "type": "date"}, {"name": "anticipatedStartDate", "description": "Anticipated Start Date", "type": "date"}, {"name": "withdrawDate", "description": "Withdraw Date", "type": "date"}, {"name": "nickname", "description": "Nickname", "type": "text", "visibleOnlyToRoles": ["admin"]}, {"name": "studentGrade", "description": "Grade", "type": "select", "values": ["Preschool", "K", "1", "2", "3", "4", "5", "6", "7", "8"]}, {"name": "homeSchool", "description": "What school does your child attend?", "type": "string"}, {"name": "subsidyCode", "description": "Subsidy Code", "type": "string"}, {"name": "agencyIdentifier", "description": "Subsidy Program", "type": "string"}, {"name": "payer", "description": "Payer", "type": "select", "values": ["", "A&D Waiver", "Choice", "CICOA", "Private Pay", "Scholarship", "TBI Waiver", "Veteran"]}, {"name": "payer2", "description": "Payer Two", "type": "select", "multi": "True", "values": ["", "You", "Me", "<PERSON><PERSON>"]}, {"name": "sex", "description": "Sex", "type": "select", "isFamilyEditable": "True", "values": ["Female", "Male"]}, {"name": "gender", "description": "Gender", "type": "select", "isFamilyEditable": "True", "values": ["Female", "Male"]}, {"name": "birthday", "description": "Birthday", "isFamilyEditable": "True", "type": "date"}, {"name": "allergies", "description": "Allergies", "type": "string"}, {"name": "annualPhysicalDate", "description": "Physical Date", "isFamilyEditable": "True", "type": "text"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Physician Name", "isFamilyEditable": "True", "type": "text"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Physician Phone", "isFamilyEditable": "True", "type": "text"}, {"name": "notes", "description": "Notes", "isFamilyEditable": "True", "type": "text"}, {"name": "siblings", "description": "<PERSON><PERSON>s", "type": "text"}, {"name": "cacfpSubsidy", "description": "CACFP Subsidy", "isFamilyEditable": "True", "type": "select", "values": ["Free", "Reduced", "Paid"]}, {"name": "notesPrivate", "description": "Notes (Private)", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "curriculum", "description": "Curriculum", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "backUpCare", "description": "Back Up Care", "visibleOnlyToRoles": ["admin", "Staff"], "type": "select", "values": ["Yes", "No", ""]}, {"name": "backUpCareReferringPartner", "description": "Back Up Care Referring Partner", "visibleOnlyToRoles": ["admin", "Staff"], "type": "select", "values": ["Find This Information", "Another Option"]}, {"name": "quickList", "description": "Quick List", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "billingNotes", "description": "Billing Notes", "type": "fieldGroup", "visibleOnlyToRoles": ["admin"], "fields": [{"name": "Billing Notes", "description": "Billing Notes", "visibleOnlyToRoles": ["admin"], "type": "text"}]}, {"name": "scannedDocuments", "description": "Scanned Documents", "type": "fieldGroup", "visibleOnlyToRoles": ["admin"], "fields": [{"name": "enrollmentPaperwork", "description": "Enrollment Paperwork", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "stateLicensingForms", "description": "State Licensing Forms", "type": "file", "visibleOnlyToRoles": ["admin"]}, {"name": "medicalInformation", "description": "Medical Information", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "courtDocuments", "description": "Court Documents", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "accidentReports", "description": "Accident Reports", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "incidentReports", "description": "Incident Reports", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "illnessReleaseForms", "description": "Illness Release Forms", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "portfolioProgressReport", "description": "Portfolio/Progress Report", "type": "attachments", "visibleOnlyToRoles": ["admin"]}]}, {"name": "preferredSchedule", "description": "Preferred Schedule", "type": "string"}], "familyProfileFields": [{"name": "phoneNumberHome", "description": "Phone Number (home)", "type": "string", "isFamilyEditable": true}, {"name": "phoneNumberWork", "description": "Phone Number (work)", "type": "string", "isFamilyEditable": true}, {"name": "phoneAlt", "description": "Phone Number (alternative)", "type": "string", "isFamilyEditable": true}, {"name": "parentBirthday", "description": "Parent Birthday", "type": "date", "isFamilyEditable": true}, {"name": "parentCapDiscount", "description": "Parent CAP Discount", "type": "string"}, {"name": "parentCapCompany", "description": "Parent CAP Company", "type": "string"}, {"name": "parentSubsidy", "description": "Parent Subsidy", "type": "string"}, {"name": "notesPrivate", "description": "Notes (Private)", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "householdInformation", "description": "Household Information", "type": "fieldGroup", "fields": [{"name": "parentStreetAddress", "description": "<PERSON><PERSON>'s Street Address", "type": "string", "isFamilyEditable": true}, {"name": "parentCity", "description": "Parent's City", "type": "string", "isFamilyEditable": true}, {"name": "parentState", "description": "Parent's State", "type": "string", "isFamilyEditable": true}, {"name": "parentZip", "description": "Parent Zip Code", "type": "string", "isFamilyEditable": true}, {"name": "addressType", "description": "Is this the parent's mailing or physical address?", "isFamilyEditable": true, "type": "select", "values": ["Mailing Only", "Physical Only", "Mailing and Physical Address"]}, {"fieldType": "attachments", "label": "Driver's License", "dataId": "driversLicense", "inheritAction": ""}]}], "payerSources": [{"type": "onmywayprek", "description": "On My Way Pre-K", "ledgerAccountName": "6352", "cashLedgerAccountName": ""}, {"type": "unitedway", "description": "United Way"}, {"type": "stateSubsidy", "description": "State Subsidy"}, {"type": "gaCaps", "description": "GA Caps"}, {"type": "payrollDeduction", "description": "Payroll Deduction", "ledgerAccountName": "1234"}, {"type": "elrc", "description": "ELRC", "ledgerAccountName": "1234"}, {"type": "cde", "description": "CDE", "ledgerAccountName": "1234"}, {"type": "collections", "description": "Collections", "ledgerAccountName": "1234"}, {"type": "blah blah", "description": "blah", "ledgerAccountName": "12345"}, {"type": "srs bus", "description": "Serious Business", "ledgerAccountName": "0000", "cashLedgerAccountName": "9999"}], "discountTypes": [{"type": "multipleFamily", "description": "Multiple Family ", "amount": null, "amountType": "dollars", "expiresWithGracePeriod": false}, {"type": "scholarship", "description": "Scholarship"}, {"type": "other", "description": "Other"}, {"type": "parishionerRate", "description": "Parishioner Rate", "amount": 50, "amountType": "percent", "expiresWithGracePeriod": false, "overrideSingleDiscount": false, "ledgerAccountName": "1111"}, {"type": "zoneDiscount", "description": "ZoneDiscount", "amount": 1, "amountType": "dollars"}, {"type": "staffDiscount", "description": "Staff Discount", "amount": 75, "amountType": "dollars", "archived": false}, {"type": "DepositApplication", "description": "DepositApplication", "amount": 350, "amountType": "dollars", "ledgerAccountName": "2081"}, {"type": "advancedTuition", "description": "Advanced Tuition", "overrideSingleDiscount": true, "ledgerAccountName": "1234", "archived": true}, {"type": "districtDiscount", "description": "District Employee Discount", "amount": 50, "amountType": "dollars", "ledgerAccountName": "5555"}], "curriculumTypesOld": [{"label": "Assessment of Child Progress"}, {"label": "Community Relationships"}, {"label": "Curriculum"}, {"label": "Families"}, {"label": "Health"}, {"label": "Leadership and Management"}, {"label": "Physical Environment"}, {"label": "Relationship"}, {"label": "Self Awareness"}, {"label": "Teachers"}, {"label": "Teaching"}], "curriculumTypes": [{"label": "Infant: Change to Daily Routine"}, {"label": "Infant: Child Goal"}, {"label": "Infant: Cognitive"}, {"label": "Infant: Family or Community Involvement"}, {"label": "Infant: Fine Motor"}, {"label": "Infant: Indoor Opportunities"}, {"label": "Infant: Large Motor"}, {"label": "Infant: Outdoor Opportunities"}, {"label": "Infant: Special Activities"}, {"label": "Preschool: Art"}, {"label": "Preschool: Blocks"}, {"label": "Preschool: Child Goal"}, {"label": "Preschool: Cooking"}, {"label": "Preschool: Discovery"}, {"label": "Preschool: Dramatic Play"}, {"label": "Preschool: Family or Community Involvement"}, {"label": "Preschool: Gross Motor"}, {"label": "Preschool: Large Group"}, {"label": "Preschool: Library"}, {"label": "Preschool: Music and Movement"}, {"label": "Preschool: Outdoor Activities"}, {"label": "Preschool: Sand and Water"}, {"label": "Preschool: Small Group Activities"}, {"label": "Preschool: Special Activities"}, {"label": "Preschool: Technology"}, {"label": "Preschool: Toys and Games"}, {"label": "Toddler:  Gross Motor"}, {"label": "Toddler: Child Goal"}, {"label": "<PERSON><PERSON>: Connecting with Music and Movement"}, {"label": "<PERSON><PERSON>: Creating with Art"}, {"label": "<PERSON><PERSON>: Enjoying Stories and Books"}, {"label": "Toddler: Exploring Sand and Water"}, {"label": "Toddler: Family or Community Involvement"}, {"label": "<PERSON><PERSON>: Imitating and Pretending"}, {"label": "Toddler: Large Group"}, {"label": "Toddler: Outdoor Activities"}, {"label": "<PERSON><PERSON>: Playing with Toys"}, {"label": "Toddler: Small Group Activities"}, {"label": "<PERSON><PERSON>: Special Activities"}, {"label": "Toddler: Tasting and Preparing Food"}], "staffProfileFields": [{"name": "defaultGroup", "description": "Default Group/Classroom", "type": "string"}, {"name": "notes", "description": "Notes", "type": "string"}, {"name": "staff<PERSON><PERSON><PERSON>", "description": "Staff's Street Address", "type": "string"}, {"name": "staffAddressCity", "description": "Staff's City", "type": "string"}, {"name": "staffAddressState", "description": "Staff's State", "type": "string"}, {"name": "staffAddressZip", "description": "Staff's Zip Code", "type": "string"}, {"name": "payRate", "description": "Hourly Pay Rate", "type": "string"}, {"name": "employeeClassification", "description": "Employee Classification", "visibleOnlyToRoles": ["admin"], "type": "select", "values": ["", "Exempt", "Non-Exempt"]}], "pinCodeCheckinFields": [{"dataId": "temperatureChild", "label": "Child's Temperature", "fieldType": "string", "required": true}, {"dataId": "temperatureParent", "label": "<PERSON><PERSON>'s Temperature", "fieldType": "string", "required": true}, {"dataId": "<PERSON><PERSON><PERSON><PERSON>", "label": "Is Temperature 100.4 or higher?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "feverReducingMedication", "label": "Was Fever Reducing Medication Administered?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "experiencingTwoSymptoms", "label": "Is your child experiencing at least 2 of the following: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>, Diarrhea, <PERSON>igue, <PERSON>ges<PERSON>/<PERSON><PERSON>?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "experiencingOneSymptom", "label": "Is your child experiencing at least 1 of the following: <PERSON>ugh, Shortness of Breath, Diffic<PERSON>y Breathing, New Loss of Taste or Smell", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "closeContact", "label": "Has your child had close contact (within 6 feet for at least 10 minutes) with a person with confirmed COVID-19 in the past 14 days?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "householdExperiencingSymptoms", "label": "Is there Someone in the household that has symptoms of COVID-19 or is diagnosed with COVID-19?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"fieldType": "timePicker", "label": "Overnight Sleep", "dataId": "overnightSleep"}, {"fieldType": "timePicker", "label": "Last potty?", "dataId": "lastPotty"}, {"fieldType": "timePicker", "label": "Last food?", "dataId": "lastFood"}, {"fieldType": "string", "label": "Naps today?", "dataId": "napsToday"}, {"fieldType": "text", "label": "Changes in normal care?", "dataId": "changeInCare"}, {"fieldType": "string", "label": "Best number to reach you at today?", "dataId": "guardian<PERSON>each<PERSON><PERSON><PERSON><PERSON>"}, {"fieldType": "text", "label": "Any medications today (type, dose, time, administered by)?", "dataId": "medicationsToday"}], "scheduleTypes": [{"_id": "WAayXcBzyaRbFxuDd", "type": "Before Care", "endTime": "8:00 AM", "startTime": "6:00 AM", "sortStart": "06:00", "sortEnd": "08:00", "overlaps": ["yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4", "5GsFf2wAFTgWnyo5r"], "fteCount": ".25", "hideInForecasting": false, "maxEnrollment": {"1": "9999", "2": "99999", "3": "99999", "4": "99999", "5": "9999"}}, {"_id": "yWZBSobtZXX3iJsJY", "type": "Primary Care", "endTime": false, "fteCount": "1", "startTime": false}, {"_id": "5ATwY9i6nPmKP8Fac", "type": "After Care", "startTime": "4:00 PM", "endTime": "6:30 PM", "fteCount": ".25", "sortStart": "16:00", "sortEnd": "18:30", "overlaps": ["yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4"]}, {"_id": "5GsFf2wAFTgWnyo5r", "type": "Part-Time", "startTime": "8:00 AM", "endTime": "12:00 PM", "fteCount": ".5", "sortStart": "07:00", "sortEnd": "12:00", "overlaps": ["WAayXcBzyaRbFxuDd", "yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4"]}, {"_id": "d25gztrQDdmNLfSZb", "type": "Ignore Me", "fteCount": "1.0", "endTime": "10:20 AM", "hideInForecasting": true, "startTime": "10:00 AM"}], "availablePermissionsContexts": ["activities", "activities/curriculumBank", "activities/themeBank", "announcements", "billing/configuration/plans", "billing/configuration/system", "billing/creditMemos/create", "billing/invoices", "billing/invoices/itemCharges", "billing/invoices/planAssignments", "billing/invoices/resend", "billing/invoices/void", "billing/invoices/modify", "billing/payments", "billing/payments/create", "billing/payments/creditBadDebt", "billing/payments/creditPayrollDeduction", "billing/payments/manageBankDeposits", "billing/payments/manageChargebacks", "billing/payments/void", "billing/payments/refund", "documents", "food", "groups", "integrations/airslate", "moments", "org/propagateSettings", "people/manageAllRoles", "people/movement", "people/profile/allergyImmunization", "people/profile", "people/relationships", "people/roleAssignments", "reports/classList", "reports/standard"], "holidays": [{"_id": "3CgH4BRvtAZEvHGPi", "name": "<PERSON>", "date": "2021-10-05"}, {"_id": "4yAEhtPb68Kt3iPnw", "name": "Another Day", "date": "2021-09-30"}, {"_id": "orJnupxkcb3dhE7jD", "name": "Another Big Day", "date": "2021-10-18"}, {"_id": "QMjyxWLM8LAG7aJDY", "name": "<PERSON>", "date": "2022-01-25"}, {"_id": "RsrnYiidRkCyah6Wu", "name": "<PERSON>", "date": "2022-01-25"}, {"_id": "kuAmuzkQovDqNQGzx", "name": "<PERSON><PERSON><PERSON>", "date": "2022-01-25"}, {"_id": "CjGAEMvcvrsRd2ZGi", "name": "Sara<PERSON><PERSON>", "date": "2022-01-26"}, {"_id": "GPEeTu9hbQbMoPm7m", "name": "Valentine's Day", "date": "2022-02-14"}], "creditMemoTypesARCHIVED": [{"type": "manualCard", "description": "Manual Credit Card", "paymentOffset": true}, {"type": "check", "description": "Check", "paymentOffset": true}, {"type": "cash", "description": "Cash", "paymentOffset": true}, {"type": "refund", "description": "Refund"}, {"type": "creditBalanceForward", "description": "Credit Balance Forward"}, {"type": "payrollDeduction", "description": "Payroll Deduction"}], "designations": ["Wait List"], "customerOrganizationID": "32", "alternateServiceChargeFeeDescription": "For credit card transactions, a convenience fee of 2.50% + $0.10 will be charged. For electronic checks, a convenience fee of $0.25, will be charged. This convenience fee will automatically be applied to your payment amount. Debit Cards will not be accepted. \n\nTo cover the cost of processing a credit or charge card transaction, and pursuant to section 5-2-212, Colorado Revised Statutes, a seller or lessor may impose a processing surcharge in an amount not to exceed the merchant discount fee that the seller or lessor incurs in processing the sales or lease transaction. A seller or lessor shall not impose a processing surcharge on payments made by use of cash, a check, or a debit card or redemption of a gift card.", "chargebackInvoiceEmail": "<EMAIL>", "foodUnits": {"babyFood": "oz."}, "availablePermissionsRoles": ["aaaRole", "sshAssistantAdmin", "sshCACFP", "sshLocalAdmin", "sshEnterpriseAccounting", "sshMasterAdmin", "sshEnterpriseExecutive", "lspHeadTeacher", "lspHomeOfficeOps", "sshHumanResources", "sshStaffLeaderLocal", "lspMasterAdmin", "sshEducation", "sshRegionalDirector", "<PERSON><PERSON><PERSON><PERSON>", "lightbridgeAdminLocal", "aTestUsers", "sshThirdAdmin", "sshVPOperations"]}, "kioskPinCode": "1234", "documentDefinitions": [{"_id": "9iyS3mqRcSEMKrL5f", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": *************, "name": "Enrollment Form", "section": "Note", "repositoryKey": "nTvbx24M2dbM9w6tu/fKriuC2Y3FrWJD2uR/wfUPdxku3ryoRVEphsnO", "deletedAt": *************, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "veW5TkxEjdcKYrA3d", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": *************, "repositoryKey": "nTvbx24M2dbM9w6tu/fKriuC2Y3FrWJD2uR/gGizRaiHEewU9F11BNNn", "name": "Preord<PERSON>", "section": "Enrollment Forms", "deletedAt": *************, "deletedByPersonId": "9W9bZ5DJGF7LWaLmJ", "deletedByUserId": "djB2aHFjG3KjJbazZ"}, {"_id": "JgdABjAgtG3gmH9zw", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1565102201184, "repositoryKey": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/WtQIAxihibvoESE9qlcn", "name": "Allergy Action Plan", "section": "Allergy Action Plan", "deletedAt": 1599238512908, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "vn8YmpM5qZkn6mt2D", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1565102225681, "repositoryKey": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/aXTspcEkd9UPZ2opU7mU", "name": "Registration Packet", "section": "Registration Packet ", "deletedAt": 1599238515046, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "jDRyHfDs27ufKwMFy", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1565102239233, "repositoryKey": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/LkVwgIcVGMXcTKNwVAm8", "name": "Universal Health Form", "section": "Universal Health Form", "deletedAt": 1599238517316, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "G7rXjQsvoS7N4NcyK", "createdByPersonId": "9W9bZ5DJGF7LWaLmJ", "createdAt": 1582141477553, "repositoryKey": "nTvbx24M2dbM9w6tu/djB2aHFjG3KjJbazZ/MHsfMXoFcxT8Ix6nBuxt", "name": "Health Form", "section": "Health Info", "deletedAt": 1599238520050, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "qnWQBg9jrQa4DzFfN", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1592918528787, "repositoryKey": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/fR7OIoLk8kH5fdZWv0OK", "name": "Infant Report", "section": "", "deletedAt": 1592918534161, "deletedByPersonId": "RYetWpMJhsC3BcPjE", "deletedByUserId": "v4x4cY4Tcv3NPGFH3"}, {"_id": "vmFSnBANrREvnqvJY", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1599238663236, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/wFg3pPpP8AsgrewKzeoL", "name": "Medical Authorization Form", "section": "Health Info", "deletedAt": 1599239030015, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "qJJSub5tdxfdQu3ys", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1599239331871, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/o75s72xpTUe99JxCwwga", "name": "Medication Authorization Form", "section": "Health Info", "templateOption": "signature", "selectedGroupIds": null, "assignmentType": "individuals"}, {"_id": "z37PmpJDb3oCzDkFv", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1603377558681, "name": "Testing Docs", "section": "", "deletedAt": 1603377563462, "deletedByPersonId": "RYetWpMJhsC3BcPjE", "deletedByUserId": "v4x4cY4Tcv3NPGFH3"}, {"_id": "SW9EKzRbsMHQd6Jh9", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1603920439618, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/QuLlWTR3V87wqAZHlCg2", "name": "School Guidelines", "section": "Information", "templateOption": "ack", "deletedAt": 1609817528931, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "CzeS4ptYuYoceRSyq", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1609817018532, "name": "Childs Schedule", "section": "Schedule", "templateOption": "signature", "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/rS7sbiwSefDHJzD6JM4O"}, {"_id": "qG4khezKsWNJgCKKA", "createdByPersonId": "kwSpWq6oomHbCskm5", "createdAt": 1611946899511, "name": "Temp Doc One", "section": "", "templateOption": "", "deletedAt": 1611946925137, "deletedByPersonId": "kwSpWq6oomHbCskm5", "deletedByUserId": "DSSp5AnemuNdFN3PH"}, {"_id": "eutGeMcS2Nmfq2kC5", "createdByPersonId": "kwSpWq6oomHbCskm5", "createdAt": 1611946960089, "name": "something new", "section": "YES", "templateOption": "signature", "deletedAt": 1611947346699, "deletedByPersonId": "kwSpWq6oomHbCskm5", "deletedByUserId": "DSSp5AnemuNdFN3PH"}, {"_id": "vqMJWhhyqKPNkfjLY", "createdByPersonId": "kwSpWq6oomHbCskm5", "createdAt": 1611949357928, "repositoryKey": "nTvbx24M2dbM9w6tu/DSSp5AnemuNdFN3PH/UNWKtT4w2D2KJtCMIZx8", "name": "asdf", "section": "", "templateOption": "", "deletedAt": 1611949488788, "deletedByPersonId": "kwSpWq6oomHbCskm5", "deletedByUserId": "DSSp5AnemuNdFN3PH"}, {"_id": "oDzLEboXFXSNJaAYx", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1612809473627, "name": "School Guideline", "section": "", "templateOption": "", "deletedAt": 1612809480149, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "MLNodR54ypneS6gnf", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1612905456940, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/qU15i9IIRZW3PZk2S2lg", "name": "Subsidy Programs", "section": "On My Way Pre-K Application Form", "templateOption": "signature"}, {"_id": "87h36MxtoCgbzJWxt", "createdByPersonId": "FfzHpuRYnX5nCayfS", "createdAt": 1613422715730, "repositoryKey": "nTvbx24M2dbM9w6tu/mt5Sofr3TFqX7fMct/THcVqt6q3SswdWwJVkic", "name": "Health Care Program", "section": "Enrollment", "templateOption": "signature", "deletedAt": 1613424001896, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "PGCGpWnC2pZSugaqK", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1613424033530, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/VcUXyeT8bT8TP4LkZ2xX", "name": "Health Care Programs", "section": "Enrollment", "templateOption": "signature"}, {"_id": "Nm5ERP35BAm6emp78", "createdByPersonId": "8jgMSvrFYadoNgRxY", "createdAt": 1617282143959, "name": "School Rules", "section": "", "templateOption": "ack", "deletedAt": 1622837036698, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "oye8RaitYXajgc5e4", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1622837061293, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/gsHptWFeTkfLLBFeuks1", "name": "School Guidelines 2021", "section": "", "templateOption": "ack", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "YhMprovyyT92eks4v", "createdByPersonId": "8jgMSvrFYadoNgRxY", "createdAt": 1627412043761, "name": "School Handbook", "section": "", "templateOption": "ack", "selectedGroupIds": null, "assignmentType": "all", "repositoryKey": "nTvbx24M2dbM9w6tu/nFeHjzmQQxYSAQgJh/4TUox0uBTt4evkkJtNAT", "deletedAt": 1627414210109, "deletedByPersonId": "8jgMSvrFYadoNgRxY", "deletedByUserId": "nFeHjzmQQxYSAQgJh"}, {"_id": "wNoztHs25ehFF2Zcm", "createdByPersonId": "8jgMSvrFYadoNgRxY", "createdAt": 1627416079008, "repositoryKey": "nTvbx24M2dbM9w6tu/nFeHjzmQQxYSAQgJh/gECd1OQxN567R3D3CgHx", "name": "Classroom Rules", "section": "Enrollment", "templateOption": "ack", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "8zehRYy9PGErbufk8", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1642036808799, "name": "ACH Form", "section": "Enrollment", "templateOption": "", "selectedGroupIds": null, "assignmentType": "all", "hideNotUploaded": true}, {"_id": "2gvJ9vu8Mv4gXXRLT", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1642036837157, "name": "Enrollment AS Form", "section": "Enrollment", "templateOption": "", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "9hufY7HbpKDZa67va", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1642036861002, "name": "Parent Handbook", "section": "Enrollment", "templateOption": "", "selectedGroupIds": null, "assignmentType": "all", "hideNotUploaded": true}, {"_id": "HKSygqg3EjbDcNwGx", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1643906578005, "name": "test", "section": "", "templateOption": "signature", "selectedGroupIds": null, "assignmentType": "all"}], "replyToAddress": "<EMAIL>", "stripeCustomerId": "cus_G8X1oy4afBIf3B", "timezone": "America/New_York", "immunizationDefinitions": [{"_id": "tNPDGNDQqhvX9mADH", "type": "Annual Physical", "description": "Annual Physical", "annual": false, "archived": true}, {"_id": "24xYFypuRxmhvB4rn", "type": "Eye Exam", "description": "Eye Exam", "annual": false, "archived": true}], "customStaffPaySettings": {"overtimeActive": false, "overtimeThreshold": "40", "types": [{"_id": "oiaivTHsCZBcSk8bn", "type": "Event", "rate": "12", "archived": false}, {"_id": "uX8fktXk5nKQQ4Hra", "type": "Unpaid", "rate": "0"}, {"_id": "HNyTmeAfCBmxWZZ7v", "type": "PTO", "rate": "", "staffProfileRate": true}, {"_id": "WuCXExQHYXxY2qWiN", "type": "Unpaid Time Off", "rate": "0", "staffProfileRate": false}, {"_id": "R5CgtQdJR2XSru5LY", "type": "Meeting", "rate": "8", "staffProfileRate": false}]}, "immunizationOverrides": [{"type": "UCHR", "exempt": false, "annual": true}, {"type": "HepB", "exempt": true, "annual": false}], "autoCheckoutTime": "12:00", "forecasting": {"idealRevenue": 95, "targetPayroll": 25}, "longName": "Mariposa Academy - Indianapolis Central", "curriculumBankId": "GtAoTHqGeLk9BR8iw", "enableSwitchOrg": true, "brands": [], "busRoutes": [{"_id": "iwuGSiG824WatDeEN", "name": "Test", "am": true, "pm": true}], "selectedBrand": "j25eAF5HDjeifSohQ", "familyRegistrationSettings": {"requiredContactsCount": "0", "questions": [], "autoGroupSelection": "zvWiPSxhkgGBvq7A6"}, "mediaRequirement": {"mediaReviewRequired": "Yes"}, "crmStatuses": [{"id": 1, "name": "New Family", "code": "New Fam", "child_only": false, "is_archive": false, "order": 0, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 2, "name": "Engaged", "code": "Engaged", "child_only": false, "is_archive": false, "order": 1, "is_optional": false, "is_sortable": false, "can_edit_logic": true, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 11, "name": "Tour Scheduled", "code": "Tour Sch", "child_only": false, "is_archive": false, "order": 2, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 3, "name": "Tour Completed", "code": "Tour Cmp", "child_only": false, "is_archive": false, "order": 3, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 4, "name": "Wait List", "code": "Wait List", "child_only": true, "is_archive": false, "order": 4, "is_optional": true, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 5, "name": "Registered", "code": "Registered", "child_only": true, "is_archive": false, "order": 5, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 6, "name": "Enrolled (Started)", "code": "Enrolled", "child_only": true, "is_archive": true, "order": 6, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 7, "name": "Temporary Leave", "code": "Temp Leave ", "child_only": true, "is_archive": true, "order": 7, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 8, "name": "Withdrawn", "code": "Withdrawn", "child_only": true, "is_archive": true, "order": 8, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 9, "name": "Lost Opportunity", "code": "Lost Op<PERSON>", "child_only": false, "is_archive": true, "order": 9, "is_optional": false, "is_sortable": true, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}]}, {"id": 10, "name": "Rejected", "code": "Rejected", "child_only": false, "is_archive": true, "order": 10, "is_optional": false, "is_sortable": false, "can_edit_logic": false, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}]}, {"id": 17, "name": "Aged Out", "code": "AgedOut", "child_only": true, "is_archive": true, "order": 11, "is_optional": false, "is_sortable": true, "can_edit_logic": true, "from_statuses": [{"id": 1, "values": {"name": "New Family"}, "links": [{"href": "/api/v3/statuses/1", "rel": "status", "type": "GET"}]}, {"id": 2, "values": {"name": "Engaged"}, "links": [{"href": "/api/v3/statuses/2", "rel": "status", "type": "GET"}]}, {"id": 11, "values": {"name": "Tour Scheduled"}, "links": [{"href": "/api/v3/statuses/11", "rel": "status", "type": "GET"}]}, {"id": 3, "values": {"name": "Tour Completed"}, "links": [{"href": "/api/v3/statuses/3", "rel": "status", "type": "GET"}]}, {"id": 4, "values": {"name": "Wait List"}, "links": [{"href": "/api/v3/statuses/4", "rel": "status", "type": "GET"}]}, {"id": 5, "values": {"name": "Registered"}, "links": [{"href": "/api/v3/statuses/5", "rel": "status", "type": "GET"}]}, {"id": 6, "values": {"name": "Enrolled (Started)"}, "links": [{"href": "/api/v3/statuses/6", "rel": "status", "type": "GET"}]}, {"id": 7, "values": {"name": "Temporary Leave"}, "links": [{"href": "/api/v3/statuses/7", "rel": "status", "type": "GET"}]}, {"id": 8, "values": {"name": "Withdrawn"}, "links": [{"href": "/api/v3/statuses/8", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}], "to_statuses": [{"id": 17, "values": {"name": "Aged Out"}, "links": [{"href": "/api/v3/statuses/17", "rel": "status", "type": "GET"}]}, {"id": 9, "values": {"name": "Lost Opportunity"}, "links": [{"href": "/api/v3/statuses/9", "rel": "status", "type": "GET"}]}, {"id": 10, "values": {"name": "Rejected"}, "links": [{"href": "/api/v3/statuses/10", "rel": "status", "type": "GET"}]}]}], "cancellationReasons": [{"_id": "vGwnsgqqgjhoeTGsZ", "reason": "Too Expensive", "archived": false, "order": 1}, {"_id": "CFFhXPpbSTw87ytCM", "reason": "Moving", "archived": false, "order": 2}, {"_id": "LsXKoPBWP9LThFQHf", "reason": "Transferring to another center", "archived": false, "order": 3}]}, {"_id": "AsfEDAaFTAeCCJMLy", "name": "142-<PERSON><PERSON><PERSON> Southside", "parentOrgId": "GtAoTHqGeLk9BR8iw", "createdAt": 1541702400000, "registrationSource": "app", "registrationNeeds": ["Family Communication", "Attendance", "Billing & Payments", "Curriculum Management", "Menu Planning", "Waitlists", "Calendar", "Real-time Alerts"], "curriculumStandard": ["Lightbridge"], "availableAssessmentLevels": [{"label": "Not Measured", "value": 0}, {"label": "Introduced", "value": 1}, {"label": "Meets", "value": 2}, {"label": "Exceeds", "value": 3}], "registrationIndustry": "childcare", "registrationChildcareType": "single-center", "registrationRole": "provider", "customizations": {"moments/ouch/enabled": true, "moments/incident/enabled": true, "people/pinCodeCheckin/enabled": true, "people/familyCheckin/enabled": true, "moments/supplies/enabled": true, "moments/checkin/autocheckout": true, "people/types/customerSpecificProfileFields": true, "moments/activity/enabled": true, "moments/alert/enabled": true, "people/multipleCheckin/enabled": true, "people/nametoface/requiresCompletedBy": true, "moments/food/infantGroupOptions": "true", "moments/illness/enabled": true, "messages/administrativeVisibility/enabled": true, "reports/billingAdminSummaryReport/enabled": true, "inquiries/registration/enabled": true, "people/types/customerSpecificInquiryProfileFields": true, "modules/curriculum/ageGroups": true, "modules/curriculum/hideMaterials": true, "modules/curriculum/hideHomework": true, "modules/curriculum/requireThemes": true, "moments/portfolio/enabled": true, "moments/medical/enabled": true, "moments/medical/useProfileMedications": true, "moments/checkin/notifyWithoutReservation": true, "moments/learning/enabled": true, "moments/potty/enabled": true, "moments/food/enabled": true, "moments/sleep/enabled": true, "billing/enabled": true, "reservations/enabled": true, "inquiries/enabled": true, "messages/suppressStaffMessageCenterNotifications/enabled": true, "people/immunizationAlerts/enabled": true, "people/types/customerSpecificStaffProfileFields": true, "report/classList/enabled": true, "moments/mood/enabled": true, "moments/covidHealthCheck/enabled": true, "moments/covidHealth/enabled": true, "billing/requireLedgerAccountName/enabled": true, "timeCards/enabled": true, "people/staffPay/enabled": true, "people/staffRequiredPinCodeCheckin/enabled": false, "moments/alert/adminOnly": false, "moments/checkout/showStaffCertificationMessage": true, "billing/plans/allowSingleDiscount": true, "moments/safety/enabled": false, "moments/behaviorChild/enabled": true, "moments/mood/adminOnly": false, "report/californiaSpecific/enabled": true, "report/classListSchedule/enabled": true, "moments/wonderlandToileting/enabled": true, "moments/ouch/adminOnly": true, "billing/configuration/postUsingPeriodStart": true, "curriculumBank/activities": true, "curriculumBank/management": true, "integrations/airslate/enabled": true, "billing/queueAutopayments/enabled": true, "billing/disableCards/enabled": true, "billing/requirePaymentMethod/enabled": false, "billing/autoProrateByDate/enabled": true, "people/requireRoles": true, "billing/configuration/couponCodes": true, "billing/configuration/bundles": true, "registrationFlow": true, "billing/configuration/punchCards": true}, "enabledMomentTypes": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "safety", "wonderlandToileting", "safetyEducare"], "registrationGuided": true, "temporary": true, "language": "translationsEnChildCare", "metrics": {"momentsPosted": 2057, "messagesSent": 10603}, "engagementCounts": [{"dayTimestamp": *************, "providerCount": 1, "familyCount": 0}], "onboardingData": {"currentTrack": "Organize"}, "billing": {"enabled": true, "scheduling": {"generateMonthDay": "4", "generateWhen": "advance", "generateDay": "monday", "lateFee": "0", "gracePeriodDays": "10", "generateBiWeeklyDate": "04/05/2021", "disablePaymentsBeforeDueDate": false, "monthlyPlanDueDay": null, "weeklyPlanDueDay": "sunday"}, "plansAndItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, {"_id": "z7KPhPhdotTddng4h", "description": "Toddlers 1-2", "amount": 450, "type": "plan", "frequency": "weekly"}, {"_id": "XvBB4ygskn3MAuLQL", "description": "Enrollment Fee", "amount": 75, "type": "item", "suspendUntil": null}, {"_id": "T5yECT8ia9e8ujDjg", "description": "Tech Fee", "amount": 100, "type": "item"}, {"_id": "4fQK63BxDdnXexd7g", "description": "Preschool 3-5", "amount": 175, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1234"}, {"_id": "Tvog42xz3eTNWFiP2", "description": "Field Trip", "amount": 5, "type": "item"}, {"_id": "JrKxL4xEXYPK9CS9D", "description": "Late Pickup Fee", "amount": 10, "type": "item"}, {"_id": "ekBHMveB9zjPBMTwR", "description": "Late Payment Fee", "amount": 10, "type": "item"}, {"_id": "9b26ETQdjCWzaQb8B", "description": "Regular Day 2 day plan (toddlers) ", "amount": 415, "type": "plan", "frequency": "monthly"}, {"_id": "R3DdyKE2tG3xe4svN", "description": "Regular Day, full week", "amount": 590, "type": "plan", "frequency": "monthly"}, {"_id": "ygEHvddnej9yccg6p", "description": "Stay and Play drop in", "amount": 50, "type": "item"}, {"_id": "SeYtDsyEmnQjSNNBS", "description": "Regular Day 3 day plan (toddlers)", "amount": 490, "type": "plan", "frequency": "monthly"}, {"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 215, "type": "plan", "frequency": "monthly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1234"}, {"_id": "9AfMyqvGT27WdRhMG", "description": "Frog<PERSON> (2-3)", "amount": 100, "type": "plan", "frequency": "monthly"}, {"_id": "MnxXa3ay6YnJtQf48", "description": "Past Due amount as of 4/3/19", "amount": 1, "type": "item"}, {"_id": "w3djzJDQyPafET8Jy", "description": "Threes (Weekly)", "amount": 150, "type": "plan", "frequency": "weekly"}, {"_id": "d5RWqdJobwsuRuM6m", "description": "After Care", "amount": 270, "type": "plan", "frequency": "weekly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1111"}, {"_id": "JuKT8L7EDKm77KtX9", "description": "Supplies", "amount": 10, "type": "item"}, {"_id": "wKMPjFHWcdMGy8SdN", "description": "Infants - Daily", "amount": 100, "type": "plan", "frequency": "daily"}, {"_id": "ezqJzT97NkiRstem7", "description": "Pre-K Day Rate", "amount": 30, "type": "plan", "frequency": "daily"}, {"_id": "RNcoK6BJuD5TxyBqZ", "description": "Infant Tuition - Weekly", "amount": 100, "type": "item"}, {"_id": "WpXFKwQf8hFupDLbK", "description": "Toddler - Full (5 Day) (monthly)", "amount": 1200, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1111"}, {"_id": "DzmykiRh7vrpw3iyJ", "description": "Infants Full Time ", "amount": 1100, "type": "plan", "frequency": "monthly", "category": "tuition", "suspendUntil": null, "ledgerAccountName": "1234"}, {"_id": "cpbgS7fqYus4nMnoo", "description": "New Weekly Plan", "amount": 1250, "type": "plan", "frequency": "weekly", "category": "tuition"}, {"_id": "SMAx9jf7ZyDddPc76", "description": "Past Due Balance Transfer", "amount": 0, "type": "item"}, {"_id": "zfNcJzCjCuDQkMkGA", "description": "UnenrollmentPlan", "amount": 1000, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1234", "suspendUntil": *************, "archived": true}, {"_id": "5zs3e4Dc5FNvrzKve", "description": "Tsting plan not tuition", "amount": 4500, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1234"}, {"_id": "WJd5NNC7HF4PM5d7v", "description": "<PERSON><PERSON> - 3 day full time", "amount": 800, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0975"}, {"_id": "Jr72s2mPyok7m5hQw", "description": "Supply Fee", "amount": 50, "type": "item", "ledgerAccountName": "1000"}, {"_id": "uzbRmNEnPzNMStD8Q", "description": "<PERSON>les - 2 day", "amount": 450, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0282"}, {"_id": "bbQzFffBY5KtBoPXP", "description": "Piano Class", "amount": 125, "type": "item", "ledgerAccountName": "0090", "suspendUntil": *************}, {"_id": "2BvYj3oEMhgSHrNvd", "description": "Bimonthly Plan", "amount": 1100, "type": "plan", "frequency": "semimonthly", "category": "tuition", "ledgerAccountName": "9999", "scaledAmounts": [], "program": "", "details": {"scheduleType": "yWZBSobtZXX3iJsJY"}}, {"_id": "kZPSwsXvH5qzeuqoT", "description": "Before Care", "amount": 300, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0009"}, {"_id": "2CoNRQkwhnDXr44Xg", "description": "Tuition Plan", "amount": 500, "type": "plan", "frequency": "biweekly", "category": "tuition", "ledgerAccountName": "3333"}, {"_id": "yFqynAWPFm7t6gXKw", "description": "Todd<PERSON> (Bi-weekly)", "amount": 500, "type": "plan", "frequency": "biweekly", "category": "tuition", "ledgerAccountName": "3693"}, {"_id": "XbDtiJaE2EnZxbX2d", "description": "Advanced Tuition", "amount": 0, "type": "item", "ledgerAccountName": "1234"}, {"_id": "8qNxxfASYcePBi4XA", "description": "PreK Full Time", "amount": 1250, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1000"}, {"_id": "fa5XsPjDR4fHH3i3g", "description": "Infant - Full Time ", "amount": 0, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4321"}, {"_id": "A4jJMWWqboB4hiXZo", "description": "Plan no category ", "amount": 500, "type": "plan", "frequency": "monthly", "category": "", "ledgerAccountName": "0000"}, {"_id": "okLkLXi8wizLypeoi", "description": "Plan w category ", "amount": 500, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0000"}, {"_id": "utXYN39S8tQkTSBc6", "description": "Infant 3-day ", "amount": 1000, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4029"}, {"_id": "xzYXzMw9aozb3fFKy", "description": "Security Deposit", "amount": 0, "type": "item", "refundableDeposit": true, "ledgerAccountName": "4455"}, {"_id": "SCuWtdyYpwJjBuFxT", "description": "Enrollment Fee", "amount": 10, "type": "item", "ledgerAccountName": "1234", "archived": true}, {"_id": "GuKoeioeS8MeMkeZq", "description": "Enrollment Fee", "amount": 10, "type": "item", "ledgerAccountName": "1234"}, {"_id": "GrtNDheEdMuFuMFAh", "description": "New plan 123", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 10, "scaledAmounts": [], "ledgerAccountName": "123456"}, {"_id": "td2qyxWg9sn3LMpf9", "description": "New Plan 2", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "654123"}, {"_id": "c8YTRiR5Y9yCGc55i", "description": "New Plan 4", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "**********"}, {"_id": "sYMBo3N7mfCmTTH4x", "description": "TimeZoneTest", "type": "plan", "program": "", "frequency": "monthly", "category": "tuition", "amount": 1, "scaledAmounts": [], "expires": *************, "ledgerAccountName": "777", "details": {"startTime": "6:00 am", "endTime": "6:00 pm", "regStartDate": *************, "regEndDate": *************, "dateType": "timePeriod", "timePeriod": "nE8DLtvQRxED3CC4t"}}, {"_id": "9Yct2rDfMHxbLFYQ9", "description": "TimeZoneTest2", "type": "item", "program": "LTqE6basmjP8Ey539", "amount": 0, "scaledAmounts": [], "ledgerAccountName": "778", "details": {"regStartDate": *************, "regEndDate": *************, "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": *************, "serviceEndDate": *************}}], "stats": {"totalEnrollmentCount": 212}, "legalEntity": {"business_name": "MomentPath", "business_tax_id": "U2FsdGVkX1+2lhUcPeRvCVtTqiJvo/7Usv+YfaaTUTA=", "address": "15639 Hawks <PERSON>", "city": "Carmel", "state": "IN", "zipcode": "46033"}, "billingMaps": {"manualPaymentDeposits": {"accountName": "1100"}, "manualOtherCredits": {"accountName": "4000"}, "accountsReceivable": {"accountName": "1205"}, "settlementFees": {"accountName": "6045"}, "otherPayerDiscounts": {"accountName": "1000"}, "securityDepositsLiability": {"accountName": "4455"}, "customerLiabilityPayable": {"accountName": "6555"}, "badDebt": {"accountName": "8844"}}, "passthroughFees": true, "stripeAccountIdLegacy": "acct_1EYcIhKLALOt8Szt", "stripeInfoLegacy": {"id": "acct_1EYcIhKLALOt8Szt", "object": "account", "bank_accounts": {"object": "list", "data": [{"id": "ba_1EYcIhKLALOt8SztHEsqqunZ", "object": "bank_account", "account": "acct_1EYcIhKLALOt8Szt", "account_holder_name": null, "account_holder_type": null, "bank_name": "JPMORGAN CHASE", "country": "US", "currency": "usd", "default_for_currency": true, "fingerprint": "oRxCiSzkYWN9sLm2", "last4": "9332", "metadata": {}, "name": null, "routing_number": "*********", "status": "new"}], "has_more": false, "total_count": 1, "url": "/v1/accounts/acct_1EYcIhKLALOt8Szt/bank_accounts"}, "business_logo": null, "business_logo_large": null, "business_name": "MomentPath", "business_primary_color": null, "business_url": null, "charges_enabled": true, "country": "US", "created": **********, "currencies_supported": ["usd", "aed", "afn", "all", "amd", "ang", "aoa", "ars", "aud", "awg", "azn", "bam", "bbd", "bdt", "bgn", "bif", "bmd", "bnd", "bob", "brl", "bsd", "bwp", "bzd", "cad", "cdf", "chf", "clp", "cny", "cop", "crc", "cve", "czk", "djf", "dkk", "dop", "dzd", "egp", "etb", "eur", "fjd", "fkp", "gbp", "gel", "gip", "gmd", "gnf", "gtq", "gyd", "hkd", "hnl", "hrk", "htg", "huf", "idr", "ils", "inr", "isk", "jmd", "jpy", "kes", "kgs", "khr", "kmf", "krw", "kyd", "kzt", "lak", "lbp", "lkr", "lrd", "lsl", "mad", "mdl", "mga", "mkd", "mmk", "mnt", "mop", "mro", "mur", "mvr", "mwk", "mxn", "myr", "mzn", "nad", "ngn", "nio", "nok", "npr", "nzd", "pab", "pen", "pgk", "php", "pkr", "pln", "pyg", "qar", "ron", "rsd", "rub", "rwf", "sar", "sbd", "scr", "sek", "sgd", "shp", "sll", "sos", "srd", "std", "szl", "thb", "tjs", "top", "try", "ttd", "twd", "tzs", "uah", "ugx", "uyu", "uzs", "vnd", "vuv", "wst", "xaf", "xcd", "xof", "xpf", "yer", "zar", "zmw"], "debit_negative_balances": false, "decline_charge_on": {"avs_failure": false, "cvc_failure": false}, "default_currency": "usd", "details_submitted": true, "display_name": null, "email": "<EMAIL>", "external_accounts": {"object": "list", "data": [{"id": "ba_1EYcIhKLALOt8SztHEsqqunZ", "object": "bank_account", "account": "acct_1EYcIhKLALOt8Szt", "account_holder_name": null, "account_holder_type": null, "bank_name": "JPMORGAN CHASE", "country": "US", "currency": "usd", "default_for_currency": true, "fingerprint": "oRxCiSzkYWN9sLm2", "last4": "9332", "metadata": {}, "name": null, "routing_number": "*********", "status": "new"}], "has_more": false, "total_count": 1, "url": "/v1/accounts/acct_1EYcIhKLALOt8Szt/external_accounts"}, "legal_entity": {"additional_owners": [], "address": {"city": "Carmel", "country": "US", "line1": "15639 Hawks <PERSON>", "line2": null, "postal_code": "46033", "state": "IN"}, "address_kana": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null, "town": null}, "address_kanji": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null, "town": null}, "business_name": "MomentPath", "business_name_kana": null, "business_name_kanji": null, "business_tax_id_provided": true, "business_vat_id_provided": false, "dob": {"day": 4, "month": 10, "year": 1976}, "first_name": "<PERSON><PERSON><PERSON>", "first_name_kana": null, "first_name_kanji": null, "gender": null, "last_name": "<PERSON><PERSON><PERSON>", "last_name_kana": null, "last_name_kanji": null, "maiden_name": null, "personal_address": {"city": null, "country": "US", "line1": null, "line2": null, "postal_code": null, "state": null}, "personal_address_kana": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null, "town": null}, "personal_address_kanji": {"city": null, "country": "JP", "line1": null, "line2": null, "postal_code": null, "state": null, "town": null}, "personal_email": null, "personal_id_number_provided": false, "personal_phone_number": null, "phone_number": null, "ssn_last_4_provided": true, "type": "company", "verification": {"details": null, "details_code": null, "document": null, "document_back": null, "status": "pending"}}, "managed": true, "mcc": null, "metadata": {}, "product_description": null, "statement_descriptor": "", "support_address": null, "support_email": null, "support_phone": null, "support_url": null, "timezone": "Etc/UTC", "tos_acceptance": {"date": **********, "ip": "*************", "user_agent": null}, "transfer_schedule": {"delay_days": 2, "interval": "daily"}, "transfer_statement_descriptor": null, "transfers_enabled": true, "type": "custom", "verification": {"contacted": false, "disabled_reason": null, "due_by": null, "fields_needed": []}}, "toplinePercentDiscounts": true, "paymentFees": {"cardFee": 0.25, "cardRate": 0.029, "achFee": 0.8}, "passthroughFeesAccountTypes": ["card"], "adyenInfo": {"accountCode": "****************"}, "ledgerAccountCodesX": [{"description": "1205 · Accounts Receivable-MomentPath", "accountName": "Other Current asset", "accountNumber": "1205"}, {"description": "2020 · Customer Liability Exchange", "accountName": "Other Current Liability", "accountNumber": "2020"}, {"description": "2180 · Section 125 Payable", "accountName": "Other Current Liability", "accountNumber": "2180"}, {"description": "2181 · Deferred Revenue", "accountName": "Other Current Liability", "accountNumber": "2181"}, {"description": "2210 · Security Deposits", "accountName": "Other Current Liability", "accountNumber": "2210"}, {"description": "4000 · Income", "accountName": "Income", "accountNumber": "4000"}, {"description": "4000 · Income:4200 · Essential Worker Tuition", "accountName": "Income", "accountNumber": "4200"}, {"description": "4010 · Registration Fees", "accountName": "Income", "accountNumber": "4010"}, {"description": "4020 · Tuition", "accountName": "Income", "accountNumber": "4020"}, {"description": "4021 · Before & After Care Tuition", "accountName": "Income", "accountNumber": "4021"}, {"description": "4023 · Infant Tuition", "accountName": "Income", "accountNumber": "4023"}, {"description": "4024 · Kindergarten Tuition", "accountName": "Income", "accountNumber": "4024"}, {"description": "4027 · Nursery Tuition", "accountName": "Income", "accountNumber": "4027"}, {"description": "4029 · Pre-K Tuition", "accountName": "Income", "accountNumber": "4029"}, {"description": "4031 · Summer Camp Activity Fee", "accountName": "Income", "accountNumber": "4031"}, {"description": "4032 · Open House Discount", "accountName": "Income", "accountNumber": "4032"}, {"description": "4033 · CAP Discount", "accountName": "Income", "accountNumber": "4033"}, {"description": "4034 · Sibling Discount", "accountName": "Income", "accountNumber": "4034"}, {"description": "4036 · Employee Discount", "accountName": "Income", "accountNumber": "4036"}, {"description": "4039 · Loyalty Program Discount", "accountName": "Income", "accountNumber": "4039"}, {"description": "4040 · Other Program Income", "accountName": "Income", "accountNumber": "4040"}, {"description": "4040 · Other Program Income:4038 · Covid-19 Discount", "accountName": "Income", "accountNumber": "4038"}, {"description": "4040 · Other Program Income:4046 · Music", "accountName": "Income", "accountNumber": "4046"}, {"description": "4040 · Other Program Income:4063 · Parents Night Out", "accountName": "Income", "accountNumber": "4063"}, {"description": "4040 · Other Program Income:4066 · Summer Enrichment Program", "accountName": "Income", "accountNumber": "4066"}, {"description": "4040 · Other Program Income:4072 · Cot Sheet", "accountName": "Income", "accountNumber": "4072"}, {"description": "4040 · Other Program Income:4210 ·SupplementalSchool-Age Program", "accountName": "Income", "accountNumber": "4210"}, {"description": "4041 · Late Payment Charge", "accountName": "Income", "accountNumber": "4041"}, {"description": "4042 · Late Pick Up Fee", "accountName": "Income", "accountNumber": "4042"}, {"description": "4043 · Retnd. Check Charge", "accountName": "Income", "accountNumber": "4043"}, {"description": "4044 · Seized Security Deposits", "accountName": "Income", "accountNumber": "4044"}, {"description": "4045 · ParentView Service", "accountName": "Income", "accountNumber": "4045"}, {"description": "4047 · Transportation", "accountName": "Income", "accountNumber": "4047"}, {"description": "4048 · Dance Classes", "accountName": "Income", "accountNumber": "4048"}, {"description": "4049 · Karate", "accountName": "Income", "accountNumber": "4049"}, {"description": "4050 · Soccer/Amazing Athletes", "accountName": "Income", "accountNumber": "4050"}, {"description": "4051 · Art Classes", "accountName": "Income", "accountNumber": "4051"}, {"description": "4052 · Gymnastics/Exercise", "accountName": "Income", "accountNumber": "4052"}, {"description": "4053 · Hot Lunch Plan", "accountName": "Income", "accountNumber": "4053"}, {"description": "4054 · Extended Hours Service", "accountName": "Income", "accountNumber": "4054"}, {"description": "4055 · Pizza Parlor Fridays", "accountName": "Income", "accountNumber": "4055"}, {"description": "4057 · Computer Classes", "accountName": "Income", "accountNumber": "4057"}, {"description": "4058 · Breakfast Plan", "accountName": "Income", "accountNumber": "4058"}, {"description": "4060 · Piano Class", "accountName": "Income", "accountNumber": "4060"}, {"description": "4065 · Back Up Care", "accountName": "Income", "accountNumber": "4065"}, {"description": "4067 · Field Trip", "accountName": "Income", "accountNumber": "4067"}, {"description": "4068 · STEM Class", "accountName": "Income", "accountNumber": "4068"}, {"description": "4069 · Leadership Class", "accountName": "Income", "accountNumber": "4069"}, {"description": "4070 · Cooking Class", "accountName": "Income", "accountNumber": "4070"}, {"description": "4077 · Lil Sports", "accountName": "Income", "accountNumber": "4077"}, {"description": "4078 · Swim & Gym", "accountName": "Income", "accountNumber": "4078"}, {"description": "4500 · Miscellaneous Income", "accountName": "Income", "accountNumber": "4500"}, {"description": "4510 · Convenience Fee Income", "accountName": "Income", "accountNumber": "4510"}, {"description": "6045 · Merchant Fees", "accountName": "Expense", "accountNumber": "6045"}, {"description": "8000  Uncollectable Bad Debt Expense", "accountName": "Expense", "accountNumber": "8000"}], "netSuite": {"exportEnabled": true, "subsidiary": "Top Company : LSP : Corporate : LSP Southside, LLC"}, "sage": {"locationCode": "145"}, "couponCodes": [{"_id": "B6YsPGZhNfWBeNzjJ", "code": "TIMETEST23", "description": "testing central timezone", "amountType": "dollars", "amount": 1, "regStartDate": *************, "regEndDate": *************, "expirationDate": *************, "usedWithOtherCoupons": false, "usedWithDiscounts": false, "timePeriods": ["nE8DLtvQRxED3CC4t"]}], "timePeriods": [{"_id": "nE8DLtvQRxED3CC4t", "name": "Summer 23", "startDate": *************, "endDate": *************}], "programs": [{"_id": "LTqE6basmjP8Ey539", "name": "Summer", "isActive": true, "isRequiredAdvanceNotice": false}]}, "counters": {"invoices": 1950}, "planDetails": {}, "valueOverrides": {"inquiryProfileFields": [{"name": "birthday", "description": "Date of birth", "type": "date"}, {"name": "startDate", "description": "Date of birth", "type": "date"}, {"name": "family<PERSON><PERSON>bers", "description": "Family Member", "type": "fieldGroup", "multiple": true, "fields": [{"name": "firstName", "description": "First Name", "type": "string"}, {"name": "lastName", "description": "Last Name", "type": "string"}, {"name": "parentStreetAddress", "description": "Street Address", "type": "string"}, {"name": "parentCity", "description": "City", "type": "string"}, {"name": "parentState", "description": "State", "type": "string"}, {"name": "parentZip", "description": "Zipcode", "type": "string"}, {"name": "phoneNumberHome", "description": "Phone", "type": "string"}, {"name": "email", "description": "Email", "type": "string"}]}], "profileFields": [{"name": "primaryFamily", "description": "Primary Family", "type": "query", "isFamilyEditable": "False", "source": "primaryFamily"}, {"name": "enrollmentDate", "description": "Enrollment Date", "type": "date"}, {"name": "withdrawDate", "description": "Withdraw Date", "type": "date"}, {"name": "nickname", "description": "Nickname", "type": "text", "visibleOnlyToRoles": ["admin"]}, {"name": "payer", "description": "Payer", "type": "select", "values": ["", "A&D Waiver", "Choice", "CICOA", "Private Pay", "Scholarship", "TBI Waiver", "Veteran"]}, {"name": "sex", "description": "Sex", "type": "select", "isFamilyEditable": "True", "values": ["Female", "Male"]}, {"name": "birthday", "description": "Birthday", "isFamilyEditable": "True", "type": "date"}, {"name": "allergies", "description": "Allergies", "type": "string"}, {"name": "annualPhysicalDate", "description": "Physical Date", "isFamilyEditable": "True", "type": "text"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Physician Name", "isFamilyEditable": "True", "type": "text"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Physician Phone", "isFamilyEditable": "True", "type": "text"}, {"name": "notes", "description": "Notes", "isFamilyEditable": "True", "type": "text"}, {"name": "siblings", "description": "<PERSON><PERSON>s", "type": "text"}, {"name": "cacfpSubsidy", "description": "CACFP Subsidy", "isFamilyEditable": "True", "type": "select", "values": ["Free", "Reduced", "Paid"]}, {"name": "notesPrivate", "description": "Notes (Private)", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "curriculum", "description": "Curriculum", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "backUpCare", "description": "Back Up Care", "visibleOnlyToRoles": ["admin", "Staff"], "type": "select", "values": ["Yes", "No", ""]}, {"name": "backUpCareReferringPartner", "description": "Back Up Care Referring Partner", "visibleOnlyToRoles": ["admin", "Staff"], "type": "select", "values": ["Find This Information", "Another Option"]}, {"name": "quickList", "description": "Quick List", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "billingNotes", "description": "Billing Notes", "type": "fieldGroup", "visibleOnlyToRoles": ["admin"], "fields": [{"name": "Billing Notes", "description": "Billing Notes", "visibleOnlyToRoles": ["admin"], "type": "text"}]}, {"name": "scannedDocuments", "description": "Scanned Documents", "type": "fieldGroup", "visibleOnlyToRoles": ["admin"], "fields": [{"name": "enrollmentPaperwork", "description": "Enrollment Paperwork", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "stateLicensingForms", "description": "State Licensing Forms", "type": "file", "visibleOnlyToRoles": ["admin"]}, {"name": "medicalInformation", "description": "Medical Information", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "courtDocuments", "description": "Court Documents", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "accidentReports", "description": "Accident Reports", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "incidentReports", "description": "Incident Reports", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "illnessReleaseForms", "description": "Illness Release Forms", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "portfolioProgressReport", "description": "Portfolio/Progress Report", "type": "attachments", "visibleOnlyToRoles": ["admin"]}]}, {"name": "preferredSchedule", "description": "Preferred Schedule", "type": "string"}], "familyProfileFields": [{"name": "phoneNumberHome", "description": "Phone Number (home)", "type": "string", "isFamilyEditable": true}, {"name": "phoneNumberWork", "description": "Phone Number (work)", "type": "string", "isFamilyEditable": true}, {"name": "parentBirthday", "description": "Parent Birthday", "type": "date", "isFamilyEditable": true}, {"name": "parentCapDiscount", "description": "Parent CAP Discount", "type": "string"}, {"name": "parentCapCompany", "description": "Parent CAP Company", "type": "string"}, {"name": "parentSubsidy", "description": "Parent Subsidy", "type": "string"}, {"name": "notesPrivate", "description": "Notes (Private)", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "householdInformation", "description": "Household Information", "type": "fieldGroup", "fields": [{"name": "parentStreetAddress", "description": "<PERSON><PERSON>'s Street Address", "type": "string", "isFamilyEditable": true}, {"name": "parentCity", "description": "Parent's City", "type": "string", "isFamilyEditable": true}, {"name": "parentState", "description": "Parent's State", "type": "string", "isFamilyEditable": true}, {"name": "parentZip", "description": "Parent Zip Code", "type": "string", "isFamilyEditable": true}, {"name": "addressType", "description": "Is this the parent's mailing or physical address?", "isFamilyEditable": true, "type": "select", "values": ["Mailing Only", "Physical Only", "Mailing and Physical Address"]}, {"fieldType": "attachments", "label": "Driver's License", "dataId": "driversLicense", "inheritAction": ""}]}], "payerSources": [{"type": "ccdf", "description": "CCDF"}, {"type": "onmywayprek", "description": "On My Way Pre-K", "ledgerAccountName": "1205"}, {"type": "unitedway", "description": "United Way"}, {"type": "stateSubsidy", "description": "State Subsidy"}, {"type": "gaCaps", "description": "GA Caps"}, {"type": "payrollDeduction", "description": "Payroll Deduction", "ledgerAccountName": "1234"}, {"type": "elrc", "description": "ELRC", "ledgerAccountName": "1234"}, {"type": "cde", "description": "CDE", "ledgerAccountName": "1234"}, {"type": "collections", "description": "Collections", "ledgerAccountName": "1234"}], "discountTypes": [{"type": "customerSpecific", "description": "Customer-Specific", "amount": null, "amountType": "dollars", "expiresWithGracePeriod": false}, {"type": "multipleFamily", "description": "Multiple Family ", "amount": null, "amountType": "dollars", "expiresWithGracePeriod": false}, {"type": "scholarship", "description": "Scholarship"}, {"type": "other", "description": "Other"}, {"type": "parishionerRate", "description": "Parishioner Rate", "amount": 50, "amountType": "percent", "expiresWithGracePeriod": false, "overrideSingleDiscount": false, "ledgerAccountName": "1111"}, {"type": "siblingDiscount", "description": "Sibling Discount", "amount": 15, "amountType": "dollars", "expiresWithGracePeriod": false}, {"type": "zoneDiscount", "description": "ZoneDiscount", "amount": 1, "amountType": "dollars"}, {"type": "staffDiscount", "description": "Staff Discount", "amount": 75, "amountType": "dollars"}, {"type": "DepositApplication", "description": "DepositApplication", "amount": 350, "amountType": "dollars", "ledgerAccountName": "2081"}, {"type": "advancedTuition", "description": "Advanced Tuition", "overrideSingleDiscount": true, "ledgerAccountName": "1234"}], "curriculumTypesOld": [{"label": "Assessment of Child Progress"}, {"label": "Community Relationships"}, {"label": "Curriculum"}, {"label": "Families"}, {"label": "Health"}, {"label": "Leadership and Management"}, {"label": "Physical Environment"}, {"label": "Relationship"}, {"label": "Self Awareness"}, {"label": "Teachers"}, {"label": "Teaching"}], "curriculumTypes": [{"label": "Infant: Change to Daily Routine"}, {"label": "Infant: Child Goal"}, {"label": "Infant: Cognitive"}, {"label": "Infant: Family or Community Involvement"}, {"label": "Infant: Fine Motor"}, {"label": "Infant: Indoor Opportunities"}, {"label": "Infant: Large Motor"}, {"label": "Infant: Outdoor Opportunities"}, {"label": "Infant: Special Activities"}, {"label": "Preschool: Art"}, {"label": "Preschool: Blocks"}, {"label": "Preschool: Child Goal"}, {"label": "Preschool: Cooking"}, {"label": "Preschool: Discovery"}, {"label": "Preschool: Dramatic Play"}, {"label": "Preschool: Family or Community Involvement"}, {"label": "Preschool: Gross Motor"}, {"label": "Preschool: Large Group"}, {"label": "Preschool: Library"}, {"label": "Preschool: Music and Movement"}, {"label": "Preschool: Outdoor Activities"}, {"label": "Preschool: Sand and Water"}, {"label": "Preschool: Small Group Activities"}, {"label": "Preschool: Special Activities"}, {"label": "Preschool: Technology"}, {"label": "Preschool: Toys and Games"}, {"label": "Toddler:  Gross Motor"}, {"label": "Toddler: Child Goal"}, {"label": "<PERSON><PERSON>: Connecting with Music and Movement"}, {"label": "<PERSON><PERSON>: Creating with Art"}, {"label": "<PERSON><PERSON>: Enjoying Stories and Books"}, {"label": "Toddler: Exploring Sand and Water"}, {"label": "Toddler: Family or Community Involvement"}, {"label": "<PERSON><PERSON>: Imitating and Pretending"}, {"label": "Toddler: Large Group"}, {"label": "Toddler: Outdoor Activities"}, {"label": "<PERSON><PERSON>: Playing with Toys"}, {"label": "Toddler: Small Group Activities"}, {"label": "<PERSON><PERSON>: Special Activities"}, {"label": "Toddler: Tasting and Preparing Food"}], "staffProfileFields": [{"name": "defaultGroup", "description": "Default Group/Classroom", "type": "string"}, {"name": "notes", "description": "Notes", "type": "string"}, {"name": "staff<PERSON><PERSON><PERSON>", "description": "Staff's Street Address", "type": "string"}, {"name": "staffAddressCity", "description": "Staff's City", "type": "string"}, {"name": "staffAddressState", "description": "Staff's State", "type": "string"}, {"name": "staffAddressZip", "description": "Staff's Zip Code", "type": "string"}, {"name": "payRate", "description": "Hourly Pay Rate", "type": "string"}, {"name": "employeeClassification", "description": "Employee Classification", "visibleOnlyToRoles": ["admin"], "type": "select", "values": ["", "Exempt", "Non-Exempt"]}], "pinCodeCheckinFields": [{"dataId": "temperatureChild", "label": "Child's Temperature", "fieldType": "string", "required": true}, {"dataId": "temperatureParent", "label": "<PERSON><PERSON>'s Temperature", "fieldType": "string", "required": true}, {"dataId": "<PERSON><PERSON><PERSON><PERSON>", "label": "Is Temperature 100.4 or higher?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "feverReducingMedication", "label": "Was Fever Reducing Medication Administered?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "experiencingTwoSymptoms", "label": "Is your child experiencing at least 2 of the following: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>, Diarrhea, <PERSON>igue, <PERSON>ges<PERSON>/<PERSON><PERSON>?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "experiencingOneSymptom", "label": "Is your child experiencing at least 1 of the following: <PERSON>ugh, Shortness of Breath, Diffic<PERSON>y Breathing, New Loss of Taste or Smell", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "closeContact", "label": "Has your child had close contact (within 6 feet for at least 10 minutes) with a person with confirmed COVID-19 in the past 14 days?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "householdExperiencingSymptoms", "label": "Is there Someone in the household that has symptoms of COVID-19 or is diagnosed with COVID-19?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"fieldType": "timePicker", "label": "Overnight Sleep", "dataId": "overnightSleep"}, {"fieldType": "timePicker", "label": "Last potty?", "dataId": "lastPotty"}, {"fieldType": "timePicker", "label": "Last food?", "dataId": "lastFood"}, {"fieldType": "string", "label": "Naps today?", "dataId": "napsToday"}, {"fieldType": "text", "label": "Changes in normal care?", "dataId": "changeInCare"}, {"fieldType": "string", "label": "Best number to reach you at today?", "dataId": "guardian<PERSON>each<PERSON><PERSON><PERSON><PERSON>"}, {"fieldType": "text", "label": "Any medications today (type, dose, time, administered by)?", "dataId": "medicationsToday"}], "scheduleTypes": [{"_id": "WAayXcBzyaRbFxuDd", "type": "Before Care", "endTime": "8:00 AM", "startTime": "6:00 AM", "sortStart": "06:00", "sortEnd": "08:00", "overlaps": ["yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4", "5GsFf2wAFTgWnyo5r"], "fteCount": ".25"}, {"_id": "yWZBSobtZXX3iJsJY", "type": "Primary Care", "endTime": false, "fteCount": "1", "startTime": false}, {"_id": "5ATwY9i6nPmKP8Fac", "type": "After Care", "startTime": "4:00 PM", "endTime": "6:30 PM", "fteCount": ".25", "sortStart": "16:00", "sortEnd": "18:30", "overlaps": ["yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4"]}, {"_id": "5GsFf2wAFTgWnyo5r", "type": "Part-Time", "startTime": "8:00 AM", "endTime": "12:00 PM", "fteCount": ".5", "sortStart": "07:00", "sortEnd": "12:00", "overlaps": ["WAayXcBzyaRbFxuDd", "yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4"]}, {"_id": "d25gztrQDdmNLfSZb", "type": "Ignore Me", "fteCount": "1.0", "endTime": "10:20 AM", "hideInForecasting": true, "startTime": "10:00 AM"}], "availablePermissionsContexts": ["activities", "activities/curriculumBank", "activities/themeBank", "announcements", "billing/configuration/plans", "billing/configuration/system", "billing/invoices", "billing/invoices/itemCharges", "billing/invoices/planAssignments", "billing/invoices/resend", "billing/invoices/void", "billing/payments", "billing/payments/create", "billing/payments/creditBadDebt", "billing/payments/void", "billing/payments/refund", "documents", "food", "groups", "moments", "org/propagateSettings", "people/addModifyUsers", "people/manageAllRoles", "people/movement", "people/profile/allergyImmunization", "people/profile", "people/relationships", "people/roleAssignments", "reports/classList", "reports/standard", "people/deactivateUsers"], "availablePermissionsRoles": ["sshAssistantAdmin", "invoicingPlanAssignments", "sshCACFP", "sshLocalAdmin", "sshEnterpriseAccounting", "sshMasterAdmin", "sshEnterpriseExecutive", "sshHumanResources", "sshStaffLeaderLocal", "sshEducation", "sshRegionalDirector", "<PERSON><PERSON><PERSON><PERSON>", "sshThirdAdmin", "sshVPOperations"], "creditMemoTypes": [{"type": "manualCard", "description": "Manual Credit Card"}, {"type": "check", "description": "Check"}, {"type": "cash", "description": "Cash"}, {"type": "refund", "description": "Refund"}, {"type": "creditBalanceForward", "description": "Credit Balance Forward"}, {"type": "payrollDeduction", "description": "Payroll Deduction"}], "holidays": [{"_id": "3CgH4BRvtAZEvHGPi", "name": "<PERSON>", "date": "2021-10-05"}, {"_id": "4yAEhtPb68Kt3iPnw", "name": "Another Day", "date": "2021-09-30"}, {"_id": "orJnupxkcb3dhE7jD", "name": "Another Big Day", "date": "2021-10-18"}, {"_id": "QMjyxWLM8LAG7aJDY", "name": "<PERSON>", "date": "2022-01-25"}, {"_id": "RsrnYiidRkCyah6Wu", "name": "<PERSON>", "date": "2022-01-25"}, {"_id": "kuAmuzkQovDqNQGzx", "name": "<PERSON><PERSON><PERSON>", "date": "2022-01-25"}, {"_id": "CjGAEMvcvrsRd2ZGi", "name": "Sara<PERSON><PERSON>", "date": "2022-01-26"}, {"_id": "GPEeTu9hbQbMoPm7m", "name": "Valentine's Day", "date": "2022-02-14"}], "customerOrganizationID": "34"}, "kioskPinCode": "1234", "documentDefinitions": [{"_id": "9iyS3mqRcSEMKrL5f", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": *************, "name": "Enrollment Form", "section": "Note", "repositoryKey": "nTvbx24M2dbM9w6tu/fKriuC2Y3FrWJD2uR/wfUPdxku3ryoRVEphsnO", "deletedAt": *************, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "veW5TkxEjdcKYrA3d", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": *************, "repositoryKey": "nTvbx24M2dbM9w6tu/fKriuC2Y3FrWJD2uR/gGizRaiHEewU9F11BNNn", "name": "Preord<PERSON>", "section": "Enrollment Forms", "deletedAt": *************, "deletedByPersonId": "9W9bZ5DJGF7LWaLmJ", "deletedByUserId": "djB2aHFjG3KjJbazZ"}, {"_id": "JgdABjAgtG3gmH9zw", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1565102201184, "repositoryKey": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/WtQIAxihibvoESE9qlcn", "name": "Allergy Action Plan", "section": "Allergy Action Plan", "deletedAt": 1599238512908, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "vn8YmpM5qZkn6mt2D", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1565102225681, "repositoryKey": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/aXTspcEkd9UPZ2opU7mU", "name": "Registration Packet", "section": "Registration Packet ", "deletedAt": 1599238515046, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "jDRyHfDs27ufKwMFy", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1565102239233, "repositoryKey": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/LkVwgIcVGMXcTKNwVAm8", "name": "Universal Health Form", "section": "Universal Health Form", "deletedAt": 1599238517316, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "G7rXjQsvoS7N4NcyK", "createdByPersonId": "9W9bZ5DJGF7LWaLmJ", "createdAt": 1582141477553, "repositoryKey": "nTvbx24M2dbM9w6tu/djB2aHFjG3KjJbazZ/MHsfMXoFcxT8Ix6nBuxt", "name": "Health Form", "section": "Health Info", "deletedAt": 1599238520050, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "qnWQBg9jrQa4DzFfN", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1592918528787, "repositoryKey": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/fR7OIoLk8kH5fdZWv0OK", "name": "Infant Report", "section": "", "deletedAt": 1592918534161, "deletedByPersonId": "RYetWpMJhsC3BcPjE", "deletedByUserId": "v4x4cY4Tcv3NPGFH3"}, {"_id": "vmFSnBANrREvnqvJY", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1599238663236, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/wFg3pPpP8AsgrewKzeoL", "name": "Medical Authorization Form", "section": "Health Info", "deletedAt": 1599239030015, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "qJJSub5tdxfdQu3ys", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1599239331871, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/o75s72xpTUe99JxCwwga", "name": "Medication Authorization Form", "section": "Health Info", "templateOption": "signature", "selectedGroupIds": null, "assignmentType": "individuals"}, {"_id": "z37PmpJDb3oCzDkFv", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1603377558681, "name": "Testing Docs", "section": "", "deletedAt": 1603377563462, "deletedByPersonId": "RYetWpMJhsC3BcPjE", "deletedByUserId": "v4x4cY4Tcv3NPGFH3"}, {"_id": "SW9EKzRbsMHQd6Jh9", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1603920439618, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/QuLlWTR3V87wqAZHlCg2", "name": "School Guidelines", "section": "Information", "templateOption": "ack", "deletedAt": 1609817528931, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "CzeS4ptYuYoceRSyq", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1609817018532, "name": "Childs Schedule", "section": "Schedule", "templateOption": "signature", "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/rS7sbiwSefDHJzD6JM4O"}, {"_id": "qG4khezKsWNJgCKKA", "createdByPersonId": "kwSpWq6oomHbCskm5", "createdAt": 1611946899511, "name": "Temp Doc One", "section": "", "templateOption": "", "deletedAt": 1611946925137, "deletedByPersonId": "kwSpWq6oomHbCskm5", "deletedByUserId": "DSSp5AnemuNdFN3PH"}, {"_id": "eutGeMcS2Nmfq2kC5", "createdByPersonId": "kwSpWq6oomHbCskm5", "createdAt": 1611946960089, "name": "something new", "section": "YES", "templateOption": "signature", "deletedAt": 1611947346699, "deletedByPersonId": "kwSpWq6oomHbCskm5", "deletedByUserId": "DSSp5AnemuNdFN3PH"}, {"_id": "vqMJWhhyqKPNkfjLY", "createdByPersonId": "kwSpWq6oomHbCskm5", "createdAt": 1611949357928, "repositoryKey": "nTvbx24M2dbM9w6tu/DSSp5AnemuNdFN3PH/UNWKtT4w2D2KJtCMIZx8", "name": "asdf", "section": "", "templateOption": "", "deletedAt": 1611949488788, "deletedByPersonId": "kwSpWq6oomHbCskm5", "deletedByUserId": "DSSp5AnemuNdFN3PH"}, {"_id": "oDzLEboXFXSNJaAYx", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1612809473627, "name": "School Guideline", "section": "", "templateOption": "", "deletedAt": 1612809480149, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "MLNodR54ypneS6gnf", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1612905456940, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/qU15i9IIRZW3PZk2S2lg", "name": "Subsidy Programs", "section": "On My Way Pre-K Application Form", "templateOption": "signature"}, {"_id": "87h36MxtoCgbzJWxt", "createdByPersonId": "FfzHpuRYnX5nCayfS", "createdAt": 1613422715730, "repositoryKey": "nTvbx24M2dbM9w6tu/mt5Sofr3TFqX7fMct/THcVqt6q3SswdWwJVkic", "name": "Health Care Program", "section": "Enrollment", "templateOption": "signature", "deletedAt": 1613424001896, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "PGCGpWnC2pZSugaqK", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1613424033530, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/VcUXyeT8bT8TP4LkZ2xX", "name": "Health Care Programs", "section": "Enrollment", "templateOption": "signature"}, {"_id": "Nm5ERP35BAm6emp78", "createdByPersonId": "8jgMSvrFYadoNgRxY", "createdAt": 1617282143959, "name": "School Rules", "section": "", "templateOption": "ack", "deletedAt": 1622837036698, "deletedByPersonId": "8uAYxXbT6urWxXg3k", "deletedByUserId": "3FhP46Kjmiro3bL7G"}, {"_id": "oye8RaitYXajgc5e4", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1622837061293, "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/gsHptWFeTkfLLBFeuks1", "name": "School Guidelines 2021", "section": "", "templateOption": "ack", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "YhMprovyyT92eks4v", "createdByPersonId": "8jgMSvrFYadoNgRxY", "createdAt": 1627412043761, "name": "School Handbook", "section": "", "templateOption": "ack", "selectedGroupIds": null, "assignmentType": "all", "repositoryKey": "nTvbx24M2dbM9w6tu/nFeHjzmQQxYSAQgJh/4TUox0uBTt4evkkJtNAT", "deletedAt": 1627414210109, "deletedByPersonId": "8jgMSvrFYadoNgRxY", "deletedByUserId": "nFeHjzmQQxYSAQgJh"}, {"_id": "wNoztHs25ehFF2Zcm", "createdByPersonId": "8jgMSvrFYadoNgRxY", "createdAt": 1627416079008, "repositoryKey": "nTvbx24M2dbM9w6tu/nFeHjzmQQxYSAQgJh/gECd1OQxN567R3D3CgHx", "name": "Classroom Rules", "section": "Enrollment", "templateOption": "ack", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "8zehRYy9PGErbufk8", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1642036808799, "name": "ACH Form", "section": "Enrollment", "templateOption": "", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "2gvJ9vu8Mv4gXXRLT", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1642036837157, "name": "Enrollment AS Form", "section": "Enrollment", "templateOption": "", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "9hufY7HbpKDZa67va", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1642036861002, "name": "Parent Handbook", "section": "Enrollment", "templateOption": "", "selectedGroupIds": null, "assignmentType": "all"}, {"_id": "HKSygqg3EjbDcNwGx", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1643906578005, "name": "test", "section": "", "templateOption": "signature", "selectedGroupIds": null, "assignmentType": "all"}], "replyToAddress": "<EMAIL>", "stripeCustomerId": "cus_G8X1oy4afBIf3B", "timezone": "America/Chicago", "immunizationDefinitions": [{"_id": "tNPDGNDQqhvX9mADH", "type": "Annual Physical", "description": "Annual Physical", "annual": false, "archived": true}, {"_id": "24xYFypuRxmhvB4rn", "type": "Eye Exam", "description": "Eye Exam", "annual": false, "archived": true}], "longName": "Mariposa Academy - Indianapolis Central", "customStaffPaySettings": {"overtimeActive": false, "overtimeThreshold": "40", "types": [{"_id": "oiaivTHsCZBcSk8bn", "type": "Event", "rate": "12", "archived": false}, {"_id": "uX8fktXk5nKQQ4Hra", "type": "Unpaid", "rate": "0"}, {"_id": "HNyTmeAfCBmxWZZ7v", "type": "PTO", "rate": "", "staffProfileRate": true}, {"_id": "WuCXExQHYXxY2qWiN", "type": "Unpaid Time Off", "rate": "0", "staffProfileRate": false}, {"_id": "R5CgtQdJR2XSru5LY", "type": "Meeting", "rate": "8", "staffProfileRate": false}]}, "immunizationOverrides": [{"type": "UCHR", "exempt": false, "annual": true}, {"type": "HepB", "exempt": true, "annual": false}], "autoCheckoutTime": "20:00", "forecasting": {"idealRevenue": 95, "targetPayroll": 25}, "enableSwitchOrg": true, "selectedBrand": "j25eAF5HDjeifSohQ"}, {"_id": "GtAoTHqGeLk9BR8iw", "createdAt": 1628178265865, "registrationSource": "app", "registrationIndustry": "childcare", "customizations": {"people/types/advancedProfileFields": false, "moments/ouch/enabled": true, "moments/incident/enabled": true, "people/pinCodeCheckin/enabled": true, "people/familyCheckin/enabled": true, "moments/supplies/enabled": true, "moments/checkin/autocheckout": true, "people/types/customerSpecificProfileFields": true, "moments/activity/enabled": true, "moments/alert/enabled": true, "people/multipleCheckin/enabled": true, "people/nametoface/requiresCompletedBy": true, "moments/food/infantGroupOptions": "true", "moments/illness/enabled": true, "messages/administrativeVisibility/enabled": true, "reports/billingAdminSummaryReport/enabled": true, "inquiries/registration/enabled": true, "people/types/customerSpecificInquiryProfileFields": true, "modules/curriculum/ageGroups": true, "modules/curriculum/hideMaterials": true, "modules/curriculum/hideHomework": true, "modules/curriculum/requireThemes": true, "moments/portfolio/enabled": true, "moments/medical/enabled": true, "moments/medical/useProfileMedications": true, "moments/checkin/notifyWithoutReservation": true, "moments/potty/enabled": true, "moments/food/enabled": true, "moments/sleep/enabled": true, "moments/checkin/staffLockdown": false, "billing/enabled": true, "reservations/enabled": true, "inquiries/enabled": true, "messages/suppressStaffMessageCenterNotifications/enabled": true, "people/immunizationAlerts/enabled": true, "people/types/customerSpecificStaffProfileFields": true, "report/classList/enabled": true, "moments/mood/enabled": true, "moments/covidHealthCheck/enabled": true, "moments/covidHealth/enabled": true, "billing/requireLedgerAccountName/enabled": true, "timeCards/enabled": true, "people/staffPay/enabled": true, "people/staffRequiredPinCodeCheckin/enabled": false, "moments/alert/adminOnly": false, "moments/checkout/showStaffCertificationMessage": true, "billing/plans/allowSingleDiscount": true, "moments/safety/enabled": false, "moments/behaviorChild/enabled": true, "moments/mood/adminOnly": false, "report/californiaSpecific/enabled": true, "report/classListSchedule/enabled": true, "moments/wonderlandToileting/enabled": true, "moments/ouch/adminOnly": true, "people/requireRoles": true, "billing/configuration/postUsingPeriodStart": true, "curriculumBank/activities": true, "curriculumBank/management": true, "billing/queueAutopayments/enabled": false, "billing/disableCards/enabled": false, "billing/requirePaymentMethod/enabled": false, "billing/autoProrateByDate/enabled": true, "people/qrCodeCheckin/enabled": true, "report/waitList/enabled": true, "curriculumBank/globalAndLocal": true, "billing/configuration/preventManualInvoicePriorPeriod": false, "billing/payments/preventDebitCards": false, "mpsurvey/enabled": false, "modules/curriculum/hidden": false, "integrations/dreambox/enabled": true, "registrationFlow": true}, "enabledMomentTypes": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "safety", "wonderlandToileting", "safetyEducare"], "language": "translationsEnChildCare", "valueOverrides": {"inquiryProfileFields": [{"name": "birthday", "description": "Date of birth", "type": "date"}, {"name": "startDate", "description": "Date of birth", "type": "date"}, {"name": "family<PERSON><PERSON>bers", "description": "Family Member", "type": "fieldGroup", "multiple": true, "fields": [{"name": "firstName", "description": "First Name", "type": "string"}, {"name": "lastName", "description": "Last Name", "type": "string"}, {"name": "parentStreetAddress", "description": "Street Address", "type": "string"}, {"name": "parentCity", "description": "City", "type": "string"}, {"name": "parentState", "description": "State", "type": "string"}, {"name": "parentZip", "description": "Zipcode", "type": "string"}, {"name": "phoneNumberHome", "description": "Phone", "type": "string"}, {"name": "email", "description": "Email", "type": "string"}]}], "profileFields": [{"name": "primaryFamily", "description": "Primary Family", "type": "query", "isFamilyEditable": "False", "source": "primaryFamily"}, {"name": "enrollmentDate", "description": "Enrollment Date", "type": "date"}, {"name": "anticipatedStartDate", "description": "Anticipated Start Date", "type": "date"}, {"name": "withdrawDate", "description": "Withdraw Date", "type": "date"}, {"name": "nickname", "description": "Nickname", "type": "text", "visibleOnlyToRoles": ["admin"]}, {"name": "payer", "description": "Payer", "type": "select", "values": ["", "A&D Waiver", "Choice", "CICOA", "Private Pay", "Scholarship", "TBI Waiver", "Veteran"]}, {"name": "sex", "description": "Sex", "type": "select", "isFamilyEditable": "True", "values": ["Female", "Male"]}, {"name": "birthday", "description": "Birthday", "isFamilyEditable": "True", "type": "date"}, {"name": "allergies", "description": "Allergies", "type": "string"}, {"name": "annualPhysicalDate", "description": "Physical Date", "isFamilyEditable": "True", "type": "text"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Physician Name", "isFamilyEditable": "True", "type": "text"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Physician Phone", "isFamilyEditable": "True", "type": "text"}, {"name": "notes", "description": "Notes", "isFamilyEditable": "True", "type": "text"}, {"name": "siblings", "description": "<PERSON><PERSON>s", "type": "text"}, {"name": "cacfpSubsidy", "description": "CACFP Subsidy", "isFamilyEditable": "True", "type": "select", "values": ["Free", "Reduced", "Paid"]}, {"name": "notesPrivate", "description": "Notes (Private)", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "curriculum", "description": "Curriculum", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "backUpCare", "description": "Back Up Care", "visibleOnlyToRoles": ["admin", "Staff"], "type": "select", "values": ["Yes", "No", ""]}, {"name": "backUpCareReferringPartner", "description": "Back Up Care Referring Partner", "visibleOnlyToRoles": ["admin", "Staff"], "type": "select", "values": ["Find This Information", "Another Option"]}, {"name": "quickList", "description": "Quick List", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "billingNotes", "description": "Billing Notes", "type": "fieldGroup", "visibleOnlyToRoles": ["admin"], "fields": [{"name": "Billing Notes", "description": "Billing Notes", "visibleOnlyToRoles": ["admin"], "type": "text"}]}, {"name": "scannedDocuments", "description": "Scanned Documents", "type": "fieldGroup", "visibleOnlyToRoles": ["admin"], "fields": [{"name": "enrollmentPaperwork", "description": "Enrollment Paperwork", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "stateLicensingForms", "description": "State Licensing Forms", "type": "file", "visibleOnlyToRoles": ["admin"]}, {"name": "medicalInformation", "description": "Medical Information", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "courtDocuments", "description": "Court Documents", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "accidentReports", "description": "Accident Reports", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "incidentReports", "description": "Incident Reports", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "illnessReleaseForms", "description": "Illness Release Forms", "type": "attachments", "visibleOnlyToRoles": ["admin"]}, {"name": "portfolioProgressReport", "description": "Portfolio/Progress Report", "type": "attachments", "visibleOnlyToRoles": ["admin"]}]}, {"name": "preferredSchedule", "description": "Preferred Schedule", "type": "string"}], "familyProfileFields": [{"name": "phoneNumberHome", "description": "Phone Number (home)", "type": "string", "isFamilyEditable": true}, {"name": "phoneNumberWork", "description": "Phone Number (work)", "type": "string", "isFamilyEditable": true}, {"name": "parentBirthday", "description": "Parent Birthday", "type": "date", "isFamilyEditable": true}, {"name": "parentCapDiscount", "description": "Parent CAP Discount", "type": "string"}, {"name": "parentCapCompany", "description": "Parent CAP Company", "type": "string"}, {"name": "parentSubsidy", "description": "Parent Subsidy", "type": "string"}, {"name": "notesPrivate", "description": "Notes (Private)", "visibleOnlyToRoles": ["admin", "Staff"], "type": "string"}, {"name": "householdInformation", "description": "Household Information", "type": "fieldGroup", "fields": [{"name": "parentStreetAddress", "description": "<PERSON><PERSON>'s Street Address", "type": "string", "isFamilyEditable": true}, {"name": "parentCity", "description": "Parent's City", "type": "string", "isFamilyEditable": true}, {"name": "parentState", "description": "Parent's State", "type": "string", "isFamilyEditable": true}, {"name": "parentZip", "description": "Parent Zip Code", "type": "string", "isFamilyEditable": true}, {"name": "addressType", "description": "Is this the parent's mailing or physical address?", "isFamilyEditable": true, "type": "select", "values": ["Mailing Only", "Physical Only", "Mailing and Physical Address"]}, {"fieldType": "attachments", "label": "Driver's License", "dataId": "driversLicense", "inheritAction": ""}]}], "payerSources": [{"type": "ccdf", "description": "CCDF", "ledgerAccountName": "1205", "archived": true}, {"type": "onmywayprek", "description": "On My Way Pre-K", "ledgerAccountName": "1155"}, {"type": "unitedway", "description": "United Way"}, {"type": "stateSubsidy", "description": "State Subsidy"}, {"type": "gaCaps", "description": "GA Caps"}, {"type": "payrollDeduction", "description": "Payroll Deduction", "ledgerAccountName": "1234"}, {"type": "elrc", "description": "ELRC", "ledgerAccountName": "1234"}, {"type": "cde", "description": "CDE", "ledgerAccountName": "1234"}, {"type": "collections", "description": "Collections", "ledgerAccountName": "1234"}, {"type": "blah blah", "description": "blah", "ledgerAccountName": "1234"}], "discountTypes": [{"type": "customerSpecific", "description": "Customer-Specific", "amount": null, "amountType": "dollars", "expiresWithGracePeriod": false, "overrideSingleDiscount": false, "ledgerAccountName": "1233", "archived": true}, {"type": "multipleFamily", "description": "Multiple Family ", "amount": null, "amountType": "dollars", "expiresWithGracePeriod": false}, {"type": "scholarship", "description": "Scholarship"}, {"type": "other", "description": "Other"}, {"type": "parishionerRate", "description": "Parishioner Rate", "amount": 50, "amountType": "percent", "expiresWithGracePeriod": false, "overrideSingleDiscount": false, "ledgerAccountName": "1111"}, {"type": "siblingDiscount", "description": "Sibling Discount", "amount": 15, "amountType": "dollars", "expiresWithGracePeriod": false, "archived": true}, {"type": "zoneDiscount", "description": "ZoneDiscount", "amount": 1, "amountType": "dollars"}, {"type": "staffDiscount", "description": "Staff Discount", "amount": 75, "amountType": "dollars"}, {"type": "DepositApplication", "description": "DepositApplication", "amount": 350, "amountType": "dollars", "ledgerAccountName": "2081"}, {"type": "advancedTuition", "description": "Advanced Tuition", "overrideSingleDiscount": true, "ledgerAccountName": "1234"}], "curriculumTypesOld": [{"label": "Assessment of Child Progress"}, {"label": "Community Relationships"}, {"label": "Curriculum"}, {"label": "Families"}, {"label": "Health"}, {"label": "Leadership and Management"}, {"label": "Physical Environment"}, {"label": "Relationship"}, {"label": "Self Awareness"}, {"label": "Teachers"}, {"label": "Teaching"}], "curriculumTypes": [{"label": "Infant: Change to Daily Routine"}, {"label": "Infant: Child Goal"}, {"label": "Infant: Cognitive"}, {"label": "Infant: Family or Community Involvement"}, {"label": "Infant: Fine Motor"}, {"label": "Infant: Indoor Opportunities"}, {"label": "Infant: Large Motor"}, {"label": "Infant: Outdoor Opportunities"}, {"label": "Infant: Special Activities"}, {"label": "Preschool: Art"}, {"label": "Preschool: Blocks"}, {"label": "Preschool: Child Goal"}, {"label": "Preschool: Cooking"}, {"label": "Preschool: Discovery"}, {"label": "Preschool: Dramatic Play"}, {"label": "Preschool: Family or Community Involvement"}, {"label": "Preschool: Gross Motor"}, {"label": "Preschool: Large Group"}, {"label": "Preschool: Library"}, {"label": "Preschool: Music and Movement"}, {"label": "Preschool: Outdoor Activities"}, {"label": "Preschool: Sand and Water"}, {"label": "Preschool: Small Group Activities"}, {"label": "Preschool: Special Activities"}, {"label": "Preschool: Technology"}, {"label": "Preschool: Toys and Games"}, {"label": "Toddler:  Gross Motor"}, {"label": "Toddler: Child Goal"}, {"label": "<PERSON><PERSON>: Connecting with Music and Movement"}, {"label": "<PERSON><PERSON>: Creating with Art"}, {"label": "<PERSON><PERSON>: Enjoying Stories and Books"}, {"label": "Toddler: Exploring Sand and Water"}, {"label": "Toddler: Family or Community Involvement"}, {"label": "<PERSON><PERSON>: Imitating and Pretending"}, {"label": "Toddler: Large Group"}, {"label": "Toddler: Outdoor Activities"}, {"label": "<PERSON><PERSON>: Playing with Toys"}, {"label": "Toddler: Small Group Activities"}, {"label": "<PERSON><PERSON>: Special Activities"}, {"label": "Toddler: Tasting and Preparing Food"}], "staffProfileFields": [{"name": "defaultGroup", "description": "Default Group/Classroom", "type": "string"}, {"name": "notes", "description": "Notes", "type": "string"}, {"name": "staff<PERSON><PERSON><PERSON>", "description": "Staff's Street Address", "type": "string"}, {"name": "staffAddressCity", "description": "Staff's City", "type": "string"}, {"name": "staffAddressState", "description": "Staff's State", "type": "string"}, {"name": "staffAddressZip", "description": "Staff's Zip Code", "type": "string"}, {"name": "payRate", "description": "Hourly Pay Rate", "type": "string"}, {"name": "employeeClassification", "description": "Employee Classification", "visibleOnlyToRoles": ["admin"], "type": "select", "values": ["", "Exempt", "Non-Exempt"]}], "pinCodeCheckinFields": [{"dataId": "temperatureChild", "label": "Child's Temperature", "fieldType": "string", "required": true}, {"dataId": "temperatureParent", "label": "<PERSON><PERSON>'s Temperature", "fieldType": "string", "required": true}, {"dataId": "<PERSON><PERSON><PERSON><PERSON>", "label": "Is Temperature 100.4 or higher?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "feverReducingMedication", "label": "Was Fever Reducing Medication Administered?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "experiencingTwoSymptoms", "label": "Is your child experiencing at least 2 of the following: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>, Diarrhea, <PERSON>igue, <PERSON>ges<PERSON>/<PERSON><PERSON>?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "experiencingOneSymptom", "label": "Is your child experiencing at least 1 of the following: <PERSON>ugh, Shortness of Breath, Diffic<PERSON>y Breathing, New Loss of Taste or Smell", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "closeContact", "label": "Has your child had close contact (within 6 feet for at least 10 minutes) with a person with confirmed COVID-19 in the past 14 days?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"dataId": "householdExperiencingSymptoms", "label": "Is there Someone in the household that has symptoms of COVID-19 or is diagnosed with COVID-19?", "fieldType": "select", "fieldValues": ["No", "Yes"], "required": true}, {"fieldType": "timePicker", "label": "Overnight Sleep", "dataId": "overnightSleep"}, {"fieldType": "timePicker", "label": "Last potty?", "dataId": "lastPotty"}, {"fieldType": "timePicker", "label": "Last food?", "dataId": "lastFood"}, {"fieldType": "string", "label": "Naps today?", "dataId": "napsToday"}, {"fieldType": "text", "label": "Changes in normal care?", "dataId": "changeInCare"}, {"fieldType": "string", "label": "Best number to reach you at today?", "dataId": "guardian<PERSON>each<PERSON><PERSON><PERSON><PERSON>"}, {"fieldType": "text", "label": "Any medications today (type, dose, time, administered by)?", "dataId": "medicationsToday"}], "scheduleTypes": [{"_id": "WAayXcBzyaRbFxuDd", "type": "Before Care", "endTime": "8:00 AM", "startTime": "6:00 AM", "sortStart": "06:00", "sortEnd": "08:00", "overlaps": ["yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4", "5GsFf2wAFTgWnyo5r"], "fteCount": ".25"}, {"_id": "yWZBSobtZXX3iJsJY", "type": "Primary Care", "endTime": false, "fteCount": "1", "startTime": false}, {"_id": "5ATwY9i6nPmKP8Fac", "type": "After Care", "startTime": "4:00 PM", "endTime": "6:30 PM", "fteCount": ".25", "sortStart": "16:00", "sortEnd": "18:30", "overlaps": ["yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4"]}, {"_id": "5GsFf2wAFTgWnyo5r", "type": "Part-Time", "startTime": "8:00 AM", "endTime": "12:00 PM", "fteCount": ".5", "sortStart": "07:00", "sortEnd": "12:00", "overlaps": ["WAayXcBzyaRbFxuDd", "yWZBSobtZXX3iJsJY", "5WSrHziD7hg347Yp4"]}, {"_id": "d25gztrQDdmNLfSZb", "type": "Ignore Me", "fteCount": "1.0", "endTime": "10:20 AM", "hideInForecasting": true, "startTime": "10:00 AM"}], "availablePermissionsContexts": ["activities", "activities/curriculumBank", "activities/themeBank", "admin/configuration/staffTimeKeeping/timeCardsLock", "announcements", "billing/configuration/plans", "billing/configuration/system", "billing/configuration/override", "billing/configuration/discounts", "billing/creditMemos/create", "billing/deposits/edit", "billing/invoices", "billing/invoices/billingNotes", "billing/invoices/itemCharges", "billing/invoices/planAssignments", "billing/invoices/resend", "billing/invoices/void", "billing/payments", "billing/payments/achGeneration", "billing/payments/create", "billing/payments/creditBadDebt", "billing/payments/creditPayrollDeduction", "billing/payments/manageBankDeposits", "billing/payments/manageChargebacks", "billing/payments/void", "billing/payments/refund", "billing/otherCredits/modify", "billing/reports", "documents", "food", "groups", "integrations/airslate", "moments", "org/propagateSettings", "people/addModifyUsers", "people/manageAdpStaffAndAdmins", "people/manageAllRoles", "people/movement", "people/profile/allergyImmunization", "people/profile", "people/profile/pay", "people/relationships", "people/roleAssignments", "people/editLockedTimeCards", "registrationFlowQuestions", "people/uploadAdp", "reports/classList", "reports/standard", "reservations", "people/deactivateUsers"], "holidays": [{"_id": "3CgH4BRvtAZEvHGPi", "name": "<PERSON>", "date": "2021-10-05"}, {"_id": "4yAEhtPb68Kt3iPnw", "name": "Another Day", "date": "2021-09-30"}, {"_id": "orJnupxkcb3dhE7jD", "name": "Another Big Day", "date": "2021-10-18"}, {"_id": "QMjyxWLM8LAG7aJDY", "name": "<PERSON>", "date": "2022-01-25"}, {"_id": "RsrnYiidRkCyah6Wu", "name": "<PERSON>", "date": "2022-01-25"}, {"_id": "kuAmuzkQovDqNQGzx", "name": "<PERSON><PERSON><PERSON>", "date": "2022-01-25"}, {"_id": "CjGAEMvcvrsRd2ZGi", "name": "Sara<PERSON><PERSON>", "date": "2022-01-26"}, {"_id": "GPEeTu9hbQbMoPm7m", "name": "Valentine's Day", "date": "2022-02-14"}], "creditMemoTypesARCHIVED": [{"type": "manualCard", "description": "Manual Credit Card", "paymentOffset": true}, {"type": "check", "description": "Check", "paymentOffset": true}, {"type": "cash", "description": "Cash", "paymentOffset": true}, {"type": "refund", "description": "Refund"}, {"type": "creditBalanceForward", "description": "Credit Balance Forward"}, {"type": "payrollDeduction", "description": "Payroll Deduction"}], "designations": ["Wait List"], "customerOrganizationID": "32", "alternateServiceChargeFeeDescription": "For credit card transactions, a convenience fee of 2.50% + $0.10 will be charged. For electronic checks, a convenience fee of $0.25, will be charged. This convenience fee will automatically be applied to your payment amount. Debit Cards will not be accepted. \n\nTo cover the cost of processing a credit or charge card transaction, and pursuant to section 5-2-212, Colorado Revised Statutes, a seller or lessor may impose a processing surcharge in an amount not to exceed the merchant discount fee that the seller or lessor incurs in processing the sales or lease transaction. A seller or lessor shall not impose a processing surcharge on payments made by use of cash, a check, or a debit card or redemption of a gift card.", "chargebackInvoiceEmail": "<EMAIL>", "foodUnits": {"babyFood": "oz."}, "availablePermissionsRoles": ["aaaRole", "buildingKidzAssistantAdministrator", "sshAssistantAdmin", "invoicingPlanAssignments", "sshCACFP", "sshLocalAdmin", "lspChef", "buildingKidzEnterpriseAccounting", "enterpriseAccounting", "lightbridgeAccountingGlobal", "sshEnterpriseAccounting", "buildingKidzEnterpriseAdministrator", "enterpriseAdministrator", "lightbridgeAdminGlobal", "sshMasterAdmin", "buildingKidzEnterpriseExecutive", "enterpriseExecutive", "lightbridgeAdminExecutive", "sshEnterpriseExecutive", "buildingKidzFranchiseOwner", "lspHeadTeacher", "lspHomeOfficeAccounting", "lspHomeOfficeOps", "lspHomeOfficeReadOnly", "sshHumanResources", "invoicingEdit", "invoicingReadOnly", "invoicingConfigurationEdit", "sshStaffLeaderLocal", "lspMasterAdmin", "otherCreditsEdit", "paymentsCreate", "paymentsEdit", "paymentsReadOnly", "sshEducation", "sshRegionalDirector", "invoicingResendInvoices", "<PERSON><PERSON><PERSON><PERSON>", "lspLocalAdmin", "buildingKidzSiteAdministrator", "lightbridgeAdminLocal", "siteAdministrator", "buildingKidzStaffLocal", "lightbridgeStaffLocal", "staffLocal", "lspStaffLocal", "aTestUsers", "sshThirdAdmin", "sshVPOperations"]}, "planDetails": {}, "name": "Mariposa Corporate", "dreambox": {"host": "asd", "dir": "asd", "username": "asd", "password": "asd", "staffTitle": "title", "staffLanguage": "language", "staffSchoolId": "schoolId", "staffSchoolAdmin": "isSchoolAdministrator", "staffDistrictAdmin": "isDistrictAdministrator", "childGrade": "grade", "childGender": "gender", "childLanguage": "language", "childSchoolId": "schoolId"}, "brands": [{"_id": "rECHRf3kc4MpAmbKs", "name": "Oruguitas"}, {"_id": "MkcjcHrhhQ3EMnLdH", "name": "AwesomeSauce2"}, {"_id": "j25eAF5HDjeifSohQ", "name": "new<PERSON>rand"}], "familyRegistrationSettings": {"requiredContactsCount": "0", "questions": [{"type": "date", "question": "Birthday", "mappedTo": "birthday", "includedLink": "", "isRequired": true, "choices": []}]}, "billing": {"plansAndItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, {"_id": "z7KPhPhdotTddng4h", "description": "Toddlers 1-2", "amount": 450, "type": "plan", "frequency": "weekly"}, {"_id": "XvBB4ygskn3MAuLQL", "description": "Enrollment Feez", "amount": 75, "type": "item", "suspendUntil": null}, {"_id": "T5yECT8ia9e8ujDjg", "description": "Tech Fee", "amount": 100, "type": "item"}, {"_id": "4fQK63BxDdnXexd7g", "description": "Preschool 3-5", "amount": 175, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1234"}, {"_id": "Tvog42xz3eTNWFiP2", "description": "Field Trip", "amount": 5, "type": "item"}, {"_id": "JrKxL4xEXYPK9CS9D", "description": "Late Pickup Fee", "amount": 10, "type": "item"}, {"_id": "ekBHMveB9zjPBMTwR", "description": "Late Payment Fee", "amount": 10, "type": "item", "ledgerAccountName": "1877", "refundableDeposit": true}, {"_id": "9b26ETQdjCWzaQb8B", "description": "Regular Day 2 day plan (toddlers) ", "amount": 415, "type": "plan", "frequency": "monthly"}, {"_id": "R3DdyKE2tG3xe4svN", "description": "Regular Day, full week", "amount": 590, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "2323"}, {"_id": "ygEHvddnej9yccg6p", "description": "Stay and Play drop in", "amount": 50, "type": "item"}, {"_id": "SeYtDsyEmnQjSNNBS", "description": "Regular Day 3 day plan (toddlers)", "amount": 490, "type": "plan", "frequency": "monthly"}, {"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 215, "type": "plan", "frequency": "monthly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1234"}, {"_id": "9AfMyqvGT27WdRhMG", "description": "Frog<PERSON> (2-3)", "amount": 100, "type": "plan", "frequency": "monthly"}, {"_id": "MnxXa3ay6YnJtQf48", "description": "Past Due amount as of 4/3/19", "amount": 1, "type": "item"}, {"_id": "w3djzJDQyPafET8Jy", "description": "Threes (Weekly)", "amount": 150, "type": "plan", "frequency": "weekly"}, {"_id": "d5RWqdJobwsuRuM6m", "description": "After Care", "amount": 270, "type": "plan", "frequency": "weekly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1111", "scaledAmounts": [], "program": "ucNZFMEB9esij3hZ4"}, {"_id": "JuKT8L7EDKm77KtX9", "description": "Supplies", "amount": 10, "type": "item"}, {"_id": "wKMPjFHWcdMGy8SdN", "description": "Infants - Daily", "amount": 100, "type": "plan", "frequency": "daily"}, {"_id": "ezqJzT97NkiRstem7", "description": "Pre-K Day Rate", "amount": 30, "type": "plan", "frequency": "daily"}, {"_id": "RNcoK6BJuD5TxyBqZ", "description": "Infant Tuition - Weekly", "amount": 100, "type": "item"}, {"_id": "WpXFKwQf8hFupDLbK", "description": "Toddler - Full (5 Day) (monthly)", "amount": 1200, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1111"}, {"_id": "DzmykiRh7vrpw3iyJ", "description": "Infants Full Time ", "amount": 1100, "type": "plan", "frequency": "monthly", "category": "tuition", "suspendUntil": null, "ledgerAccountName": "1234"}, {"_id": "cpbgS7fqYus4nMnoo", "description": "New Weekly Plan", "amount": 1250, "type": "plan", "frequency": "weekly", "category": "tuition"}, {"_id": "SMAx9jf7ZyDddPc76", "description": "Past Due Balance Transfer", "amount": 0, "type": "item"}, {"_id": "zfNcJzCjCuDQkMkGA", "description": "UnenrollmentPlan", "amount": 1000, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1234", "suspendUntil": *************, "archived": true}, {"_id": "5zs3e4Dc5FNvrzKve", "description": "Tsting plan not tuition", "amount": 4500, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1234"}, {"_id": "WJd5NNC7HF4PM5d7v", "description": "<PERSON><PERSON> - 3 day full time", "amount": 800, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0975"}, {"_id": "Jr72s2mPyok7m5hQw", "description": "Supply Fee", "amount": 50, "type": "item", "ledgerAccountName": "1000"}, {"_id": "uzbRmNEnPzNMStD8Q", "description": "<PERSON>les - 2 day", "amount": 450, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0282"}, {"_id": "bbQzFffBY5KtBoPXP", "description": "Piano Class", "amount": 125, "type": "item", "ledgerAccountName": "0090", "suspendUntil": *************}, {"_id": "2BvYj3oEMhgSHrNvd", "description": "Bimonthly Plan", "amount": 1100, "type": "plan", "frequency": "semimonthly", "category": "tuition", "ledgerAccountName": "9999"}, {"_id": "kZPSwsXvH5qzeuqoT", "description": "Before Care", "amount": 300, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0009", "scaledAmounts": [], "program": "ucNZFMEB9esij3hZ4"}, {"_id": "2CoNRQkwhnDXr44Xg", "description": "Tuition Plan", "amount": 500, "type": "plan", "frequency": "biweekly", "category": "tuition", "ledgerAccountName": "3333"}, {"_id": "yFqynAWPFm7t6gXKw", "description": "Todd<PERSON> (Bi-weekly)", "amount": 500, "type": "plan", "frequency": "biweekly", "category": "tuition", "ledgerAccountName": "3693"}, {"_id": "XbDtiJaE2EnZxbX2d", "description": "Advanced Tuition", "amount": 0, "type": "item", "ledgerAccountName": "1234"}, {"_id": "8qNxxfASYcePBi4XA", "description": "PreK Full Time", "amount": 1250, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1000"}, {"_id": "fa5XsPjDR4fHH3i3g", "description": "Infant - Full Time ", "amount": 0, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4321"}, {"_id": "A4jJMWWqboB4hiXZo", "description": "Plan no category ", "amount": 500, "type": "plan", "frequency": "monthly", "category": "", "ledgerAccountName": "0000"}, {"_id": "okLkLXi8wizLypeoi", "description": "Plan w category ", "amount": 500, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0000"}, {"_id": "utXYN39S8tQkTSBc6", "description": "Infant 3-day ", "amount": 1000, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4029"}, {"_id": "xzYXzMw9aozb3fFKy", "description": "Security Deposit", "amount": 0, "type": "item", "refundableDeposit": true, "ledgerAccountName": "4455"}, {"_id": "SCuWtdyYpwJjBuFxT", "description": "Enrollment Feez", "amount": 10, "type": "item", "ledgerAccountName": "1234", "archived": true}, {"_id": "GuKoeioeS8MeMkeZq", "description": "Enrollment Feez", "amount": 10, "type": "item", "ledgerAccountName": "1234"}, {"_id": "eDRpegQvzJiFe8J5y", "description": "ts", "amount": 0, "type": "plan", "frequency": "hourly", "category": "tuition", "ledgerAccountName": "1234"}, {"_id": "57GZroDnrv8c3jAgK", "description": "Registration Fee", "amount": 25, "type": "item", "ledgerAccountName": "123456"}, {"_id": "GrtNDheEdMuFuMFAh", "description": "New plan 1234", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 10, "scaledAmounts": [], "ledgerAccountName": "123456"}, {"_id": "c8YTRiR5Y9yCGc55i", "description": "New Plan 4", "type": "plan", "frequency": "weekly", "category": "tuition", "program": "", "amount": 100, "scaledAmounts": [], "ledgerAccountName": "**********"}, {"_id": "arEzHvY6ex6AMgSr5", "description": "Propgate Test", "type": "item", "program": "", "amount": 99, "scaledAmounts": [], "ledgerAccountName": "7777", "details": {"startTime": "5:00 am", "endTime": "5:30 am", "regStartDate": *************, "regEndDate": *************, "dateType": "date<PERSON><PERSON><PERSON>", "serviceStartDate": *************, "serviceEndDate": *************}}]}, "curriculumBankId": "GtAoTHqGeLk9BR8iw", "enableSwitchOrg": true}]