[{"_id": "z4Z36b78pD7p7fay7", "invoiceDate": "6/17/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "kZPSwsXvH5qzeuqoT", "description": "Before Care", "amount": 300, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0009", "enrolledPlan": {"_id": "kZPSwsXvH5qzeuqoT", "planDetails": {"_id": "kZPSwsXvH5qzeuqoT", "description": "Before Care", "amount": 300, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "0009"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 125, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "g8xNRS2KXvJuPMrE5"}], "createdAt": *************, "enrollmentForecastStartDate": *************}, "coversPeriodDesc": "Monthly period beginning 06/17/2022", "periodEndDate": *************, "manualInvoicePeriodStartDate": "2022-06-17", "periodStartDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 125, "originalAllocation": {"allocationType": "reimbursable", "amount": 125, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "g8xNRS2KXvJuPMrE5"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "QxTFrRwFCXF3pfaao", "type": "planInvoice", "invoiceNumber": "2074", "openAmount": 125, "originalAmount": 175, "discountAmount": 125, "dueDate": *************, "batchStamp": 1655487857229, "openPayerAmounts": {"ccdf": 125}, "credits": [{"type": "reimbursement", "payment_type": "credit_memo", "amount": 0, "createdAt": 1655487877285, "paidByDesc": "prepaid_ccdf", "creditMemoId": "YAMkCAkpnXsQcuMon", "creditNote": "", "creditMemoType": "prepaid_ccdf", "creditReason": "reimbursable", "creditPayerSource": "ccdf", "voidedAmount": 76, "voidedAt": 1655487892983, "voidedBy": "ZdkhmaEScuuboPFbH", "voidedByPersonId": "37NJEmYGY77sBci2P"}, {"type": "payment", "payment_type": "credit_memo", "amount": 0, "createdAt": 1655487911603, "paidBy": "37NJEmYGY77sBci2P", "paidByDesc": "<PERSON>", "creditMemoId": "yJSZGqAumdG5w8JcJ", "creditNote": "", "creditMemoType": "check", "voidedAmount": 50, "voidedAt": *************, "voidedBy": "ZdkhmaEScuuboPFbH", "voidedByPersonId": "37NJEmYGY77sBci2P"}], "payerName": "ccdf"}, {"_id": "nuddSeF39sPQuNXhk", "invoiceDate": "10/06/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "tnJqAeuSFdXvdqHfn", "description": "SSH Tuition Plan", "amount": 275, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "3654", "enrolledPlan": {"_id": "tnJqAeuSFdXvdqHfn", "planDetails": {"_id": "tnJqAeuSFdXvdqHfn", "description": "SSH Tuition Plan", "amount": 275, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "3654"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "auXZPGzqgAinx6PFN"}, {"allocationType": "discount", "amount": 100, "amountType": "dollars", "discountType": "payerDiscount", "allocationDescription": "Discount: Third-Party Payer Discount", "id": "R4CeyZxpsWMM5w4Mv"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "3FhP46Kjmiro3bL7G"}, "coversPeriodDesc": "Weekly period 10/10/2022 - 10/14/2022", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "stateSubsidy", "amount": 100, "originalAllocation": {"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "auXZPGzqgAinx6PFN"}}, {"type": "discount", "source": "payerDiscount", "amount": 100, "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "dollars", "discountType": "payerDiscount", "allocationDescription": "Discount: Third-Party Payer Discount", "id": "R4CeyZxpsWMM5w4Mv"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "WgtTaSFgA8LXY49KB", "type": "planInvoice", "invoiceNumber": "6732", "openAmount": 100, "originalAmount": 75, "discountAmount": 200, "dueDate": 1665201600000, "batchStamp": 1665029101323, "openPayerAmounts": {"stateSubsidy": 100}, "familySplits": {"eRgyHL4H7PERcatwc": 50, "7AucAgtXkEMyW3DuB": 50}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 37.5, "createdAt": 1666711129184, "paidBy": "eRgyHL4H7PERcatwc", "paidByDesc": "<PERSON>", "creditMemoId": "aAhxemLZTDpMQqKdo", "creditNote": "", "creditMemoType": "cash"}, {"type": "payment", "payment_type": "credit_memo", "amount": 37.5, "createdAt": *************, "paidBy": "7AucAgtXkEMyW3DuB", "paidByDesc": "<PERSON>", "creditMemoId": "aS2EYh5TEptnpmMRD", "creditNote": "", "creditMemoType": "cash"}], "payerName": "stateSubsidy"}, {"_id": "B7MDY7vbSzKB8LQBy", "invoiceDate": "10/13/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112", "enrolledPlan": {"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "JmaaWsMaMWhzvm5ax"}, {"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "c8jT2MzptuQHTpMQH"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "nFeHjzmQQxYSAQgJh"}, "coversPeriodDesc": "Weekly period 10/17/2022 - 10/21/2022", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 200, "originalAllocation": {"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "JmaaWsMaMWhzvm5ax"}}, {"type": "discount", "source": "siblingDiscount", "amount": 24, "originalAllocation": {"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "c8jT2MzptuQHTpMQH"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "agabuymN5Caxs8ruo", "type": "planInvoice", "invoiceNumber": "6752", "openAmount": 200, "originalAmount": 16, "discountAmount": 224, "dueDate": 1665806400000, "batchStamp": 1665633901376, "openPayerAmounts": {"ccdf": 200}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 16, "createdAt": *************, "paidBy": "CTx2PNHLaurPmmSnE", "paidByDesc": "<PERSON><PERSON>", "creditMemoId": "psewnLmqqa9amRBiE", "creditNote": "", "creditMemoType": "check"}], "payerName": "ccdf"}, {"_id": "m8axzZysqh2C54PSp", "invoiceDate": "11/03/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112", "enrolledPlan": {"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "JmaaWsMaMWhzvm5ax"}, {"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "c8jT2MzptuQHTpMQH"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "nFeHjzmQQxYSAQgJh"}, "coversPeriodDesc": "Weekly period 11/07/2022 - 11/11/2022", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 200, "originalAllocation": {"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "JmaaWsMaMWhzvm5ax"}}, {"type": "discount", "source": "siblingDiscount", "amount": 24, "originalAllocation": {"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "c8jT2MzptuQHTpMQH"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "agabuymN5Caxs8ruo", "type": "planInvoice", "invoiceNumber": "6864", "openAmount": 200, "originalAmount": 16, "discountAmount": 224, "dueDate": 1667620800000, "batchStamp": 1667448300547, "openPayerAmounts": {"ccdf": 200}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 16, "createdAt": *************, "paidBy": "Yg3rD3qe9FQD6wsEZ", "paidByDesc": "<PERSON><PERSON>", "creditMemoId": "BkwZHJkpxzhaLauZj", "creditNote": "", "creditMemoType": "manualCard"}], "payerName": "ccdf"}, {"_id": "3R62hQ5oqQb9JNctP", "invoiceDate": "3/21/2023", "createdAt": *************, "createdBy": "uqkBwPTry4NmnBAmt", "lineItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 80, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "42000", "suspendUntil": null, "enrolledPlan": {"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "42000", "suspendUntil": null}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "a7oRHDPzbW8XoTmL5"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "yWDbbnwaihEmMGD93"}, "periodEndDate": *************, "coversPeriodDesc": "Weekly period 03/17/2023 - 03/21/2023", "manualInvoicePeriodStartDate": "2023-03-17", "periodStartDate": *************, "proratedByDate": true, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 80, "originalAllocation": {"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "a7oRHDPzbW8XoTmL5"}, "updatedAt": *************, "updatedByPersonId": "uqkBwPTry4NmnBAmt"}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "uZJ6jzQR5KsB4kiDG", "type": "planInvoice", "invoiceNumber": "7252", "openAmount": 80, "originalAmount": 0, "discountAmount": 200, "dueDate": *************, "batchStamp": *************, "openPayerAmounts": {"ccdf": 80}, "updatedAt": *************, "updatedByPersonId": "uqkBwPTry4NmnBAmt", "payerName": "ccdf"}, {"_id": "YKkQBKfduGChYaW5g", "invoiceDate": "2/19/2023", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "8qNxxfASYcePBi4XA", "description": "Pre-K Full Time", "amount": 1500, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1000", "enrolledPlan": {"_id": "8qNxxfASYcePBi4XA", "planDetails": {"_id": "8qNxxfASYcePBi4XA", "description": "PreK Full Time", "amount": 1250, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1000"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 750, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "BcZpft4b3Eh767Q3v"}], "createdAt": *************, "enrollmentForecastStartDate": *************}, "coversPeriodDesc": "Monthly period beginning 03/01/2023", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 750, "originalAllocation": {"allocationType": "reimbursable", "amount": 750, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "BcZpft4b3Eh767Q3v"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "uHeWbNQmFmdZdGL6k", "type": "planInvoice", "invoiceNumber": "7143", "openAmount": 750, "originalAmount": 750, "discountAmount": 750, "dueDate": *************, "batchStamp": *************, "openPayerAmounts": {"ccdf": 750}, "payerName": "ccdf"}, {"_id": "QzH92gsBweBZSFC2q", "invoiceDate": "12/19/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "9AfMyqvGT27WdRhMG", "description": "Frog<PERSON> (2-3)", "amount": 100, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "8754", "enrolledPlan": {"_id": "9AfMyqvGT27WdRhMG", "planDetails": {"_id": "9AfMyqvGT27WdRhMG", "description": "Frog<PERSON> (2-3)", "amount": 100, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "8754"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 75, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "XTXWdvR9EeYXh7Lo5"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "mt5Sofr3TFqX7fMct"}, "coversPeriodDesc": "Monthly period beginning 01/01/2023", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "stateSubsidy", "amount": 75, "originalAllocation": {"allocationType": "reimbursable", "amount": 75, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "XTXWdvR9EeYXh7Lo5"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "R37M7tvesRJLcTskH", "type": "planInvoice", "invoiceNumber": "7003", "openAmount": 75, "originalAmount": 25, "discountAmount": 75, "dueDate": 1672290000000, "batchStamp": 1671426300759, "openPayerAmounts": {"stateSubsidy": 75}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 25, "createdAt": *************, "paidBy": "eRgyHL4H7PERcatwc", "paidByDesc": "<PERSON>", "creditMemoId": "AN9awJ438uenvnMTa", "creditNote": "", "creditMemoType": "manualCard"}], "payerName": "stateSubsidy"}, {"_id": "WaEHtMQPuHwxaRqZb", "invoiceDate": "12/01/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "tnJqAeuSFdXvdqHfn", "description": "SSH Tuition Plan", "amount": 275, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "3654", "enrolledPlan": {"_id": "tnJqAeuSFdXvdqHfn", "planDetails": {"_id": "tnJqAeuSFdXvdqHfn", "description": "SSH Tuition Plan", "amount": 275, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "3654"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "auXZPGzqgAinx6PFN"}, {"allocationType": "discount", "amount": 100, "amountType": "dollars", "discountType": "payerDiscount", "allocationDescription": "Discount: Third-Party Payer Discount", "id": "R4CeyZxpsWMM5w4Mv"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "3FhP46Kjmiro3bL7G"}, "coversPeriodDesc": "Weekly period 12/05/2022 - 12/09/2022", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "stateSubsidy", "amount": 100, "originalAllocation": {"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "auXZPGzqgAinx6PFN"}}, {"type": "discount", "source": "payerDiscount", "amount": 100, "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "dollars", "discountType": "payerDiscount", "allocationDescription": "Discount: Third-Party Payer Discount", "id": "R4CeyZxpsWMM5w4Mv"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "WgtTaSFgA8LXY49KB", "type": "planInvoice", "invoiceNumber": "6974", "openAmount": 100, "originalAmount": 75, "discountAmount": 200, "dueDate": 1670043600000, "batchStamp": 1669871100946, "openPayerAmounts": {"stateSubsidy": 100}, "familySplits": {"eRgyHL4H7PERcatwc": 50, "7AucAgtXkEMyW3DuB": 50}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 37.5, "createdAt": 1670265213632, "paidBy": "7AucAgtXkEMyW3DuB", "paidByDesc": "<PERSON>", "creditMemoId": "W6HvyKyLgi2mjL2ms", "creditNote": "", "creditMemoType": "manualCard"}, {"type": "payment", "payment_type": "credit_memo", "amount": 37.5, "createdAt": 1670265252711, "paidBy": "eRgyHL4H7PERcatwc", "paidByDesc": "<PERSON>", "creditMemoId": "AN9awJ438uenvnMTa", "creditNote": "", "creditMemoType": "manualCard"}], "payerName": "stateSubsidy"}, {"_id": "2S6Ci8fgQJri6qKn3", "invoiceDate": "10/26/2022", "createdAt": 1666800466832, "createdBy": "SYSTEM", "lineItems": [{"_id": "SeYtDsyEmnQjSNNBS", "description": "Regular Day 3 day plan (toddlers)", "amount": 490, "type": "plan", "frequency": "monthly", "enrolledPlan": {"_id": "SeYtDsyEmnQjSNNBS", "planDetails": {"_id": "SeYtDsyEmnQjSNNBS", "description": "Regular Day 3 day plan (toddlers)", "amount": 490, "type": "plan", "frequency": "monthly"}, "enrollmentDate": 1666670400000, "allocations": [{"allocationType": "reimbursable", "amount": 250, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": 1666497600000, "id": "cpZz9yGSvXCoFdawf"}], "createdAt": 1666800442107}, "coversPeriodDesc": "Monthly period beginning 10/26/2022", "periodEndDate": 1667188800000, "manualInvoicePeriodStartDate": "2022-10-26", "periodStartDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 250, "originalAllocation": {"allocationType": "reimbursable", "amount": 250, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": 1666497600000, "id": "cpZz9yGSvXCoFdawf"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "wGPChn8qMDa2Ayi4s", "type": "planInvoice", "invoiceNumber": "6817", "openAmount": 250, "originalAmount": 240, "discountAmount": 250, "dueDate": *************, "batchStamp": *************, "openPayerAmounts": {"ccdf": 250}, "payerName": "ccdf"}, {"_id": "255tgNeddPkcGdPgC", "invoiceDate": "2/03/2023", "createdAt": *************, "createdBy": "uqkBwPTry4NmnBAmt", "lineItems": [{"_id": "fa5XsPjDR4fHH3i3g", "description": "Infant - Full Time (Monthly)", "amount": 675, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4321", "enrolledPlan": {"_id": "fa5XsPjDR4fHH3i3g", "planDetails": {"_id": "fa5XsPjDR4fHH3i3g", "description": "Infant - Full Time (Monthly)", "amount": 750, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4321"}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "aynjrtwi9YM5NRcy2"}, {"allocationType": "reimbursable", "amount": 10, "amountType": "percent", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "eEfW9G5MztBGB3Z3c"}], "createdAt": *************, "enrollmentForecastStartDate": *************}, "coversPeriodDesc": "Monthly period beginning 02/01/2023", "periodEndDate": *************, "manualInvoicePeriodStartDate": "2023-02-01", "periodStartDate": *************, "proratedByDate": true, "appliedDiscounts": [{"type": "discount", "source": "siblingDiscount", "amount": 67.5, "originalAllocation": {"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "aynjrtwi9YM5NRcy2"}}, {"type": "reimbursable", "source": "ccdf", "amount": 67.5, "originalAllocation": {"allocationType": "reimbursable", "amount": 10, "amountType": "percent", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "eEfW9G5MztBGB3Z3c"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "uZJ6jzQR5KsB4kiDG", "type": "planInvoice", "invoiceNumber": "7116", "openAmount": 67.5, "originalAmount": 540, "discountAmount": 135, "dueDate": *************, "batchStamp": 1675453592234, "openPayerAmounts": {"ccdf": 67.5}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 352, "createdAt": 1675487103488, "paidBy": "fhGEBuZxsTBT4cNNf", "paidByDesc": "<PERSON>", "creditMemoId": "zQ3NdJXmqw5Ftt56a", "creditNote": "", "creditMemoType": "excess_stateSubsidy"}, {"type": "payment", "payment_type": "credit_memo", "amount": 5, "createdAt": 1675487103550, "paidBy": "fhGEBuZxsTBT4cNNf", "paidByDesc": "<PERSON>", "creditMemoId": "cjDRKiSossFDJmPf5", "creditNote": "", "creditMemoType": "excess_onmywayprek"}, {"type": "payment", "payment_type": "credit_memo", "amount": 183, "createdAt": *************, "paidBy": "fhGEBuZxsTBT4cNNf", "paidByDesc": "<PERSON>", "creditMemoId": "hwoNDMoToRrbTq9bd", "creditNote": "", "creditMemoType": "excess_onmywayprek"}], "payerName": "ccdf"}, {"_id": "5bNKSAwScAErYcBsM", "invoiceDate": "3/21/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 290, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112", "enrolledPlan": {"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "dollars", "discountType": "customerSpecific", "allocationDescription": "Discount: Customer-Specific", "id": "bgjeLJnkbEqbutDt9"}, {"allocationType": "reimbursable", "amount": 15, "amountType": "dollars", "reimbursementType": "onmywayprek", "allocationDescription": "Reimbursable: On My Way Pre-K", "payerEndDate": *************, "id": "oTmzEkSwbdTHhM868"}, {"allocationType": "reimbursable", "amount": 50, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": *************, "payerEndDate": *************, "id": "kKg89PqEfYtB2dnRa"}, {"allocationType": "reimbursable", "amount": 50, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": *************, "id": "WNNqbA3dBd5vjr7Sy"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": 290, "updatedAt": 1647874085245, "updatedBy": "ZdkhmaEScuuboPFbH"}, "coversPeriodDesc": "Weekly period 03/28/2022 - 04/01/2022", "periodStartDate": 1648440000000, "periodEndDate": *************, "appliedDiscounts": [{"type": "discount", "source": "customerSpecific", "amount": 10, "originalAllocation": {"allocationType": "discount", "amount": 10, "amountType": "dollars", "discountType": "customerSpecific", "allocationDescription": "Discount: Customer-Specific", "id": "bgjeLJnkbEqbutDt9"}}, {"type": "reimbursable", "source": "onmywayprek", "amount": 15, "originalAllocation": {"allocationType": "reimbursable", "amount": 15, "amountType": "dollars", "reimbursementType": "onmywayprek", "allocationDescription": "Reimbursable: On My Way Pre-K", "payerEndDate": *************, "id": "oTmzEkSwbdTHhM868"}}, {"type": "reimbursable", "source": "ccdf", "amount": 50, "originalAllocation": {"allocationType": "reimbursable", "amount": 50, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": *************, "payerEndDate": *************, "id": "kKg89PqEfYtB2dnRa"}}, {"type": "reimbursable", "source": "ccdf", "amount": 50, "originalAllocation": {"allocationType": "reimbursable", "amount": 50, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": *************, "id": "WNNqbA3dBd5vjr7Sy"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "MdKPhsKB4btnEZt6n", "type": "planInvoice", "invoiceNumber": "1965", "openAmount": 100, "originalAmount": 165, "discountAmount": 125, "dueDate": 1648353600000, "batchStamp": 1647876214663, "openPayerAmounts": {"onmywayprek": 15, "ccdf": 100}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 165, "createdAt": *************, "paidBy": "kkGCnfhZQ2o67NS8S", "paidByDesc": "<PERSON>is", "creditMemoId": "zykFJTRfjPACu6pbQ", "creditNote": "another", "creditMemoType": "manualCard"}], "payerName": "ccdf"}, {"_id": "nTktsEJebh8xeDk5M", "invoiceDate": "11/19/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "utXYN39S8tQkTSBc6", "description": "Infant 3-day ", "amount": 1000, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4029", "enrolledPlan": {"_id": "utXYN39S8tQkTSBc6", "planDetails": {"_id": "utXYN39S8tQkTSBc6", "description": "Infant 3-day ", "amount": 1000, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4029"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 400, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "8f38xeYJLewmyQE7k"}], "createdAt": *************, "enrollmentForecastStartDate": *************}, "coversPeriodDesc": "Monthly period beginning 12/01/2022", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 400, "originalAllocation": {"allocationType": "reimbursable", "amount": 400, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "8f38xeYJLewmyQE7k"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "yxgNpdqeceevhnDNz", "type": "planInvoice", "invoiceNumber": "6920", "openAmount": 400, "originalAmount": 600, "discountAmount": 400, "dueDate": *************, "batchStamp": *************, "openPayerAmounts": {"ccdf": 400}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 600, "createdAt": 1670265273095, "paidBy": "fhGEBuZxsTBT4cNNf", "paidByDesc": "<PERSON>", "creditMemoId": "hNv5Eup4nBCfgYgb5", "creditNote": "", "creditMemoType": "manualCard"}], "payerName": "ccdf"}, {"_id": "wYscxHth9AhtNx698", "invoiceDate": "9/22/2022", "createdAt": 1663819501726, "createdBy": "SYSTEM", "lineItems": [{"_id": "cpbgS7fqYus4nMnoo", "description": "New Weekly Plan", "amount": 1250, "type": "plan", "frequency": "weekly", "category": "tuition", "enrolledPlan": {"_id": "cpbgS7fqYus4nMnoo", "planDetails": {"_id": "cpbgS7fqYus4nMnoo", "description": "New Weekly Plan", "amount": 1250, "type": "plan", "frequency": "weekly", "category": "tuition"}, "enrollmentDate": 1656648000000, "allocations": [{"allocationType": "reimbursable", "amount": 750, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "PQ7x5touwgcqEB6cu"}], "createdAt": 1656701574742, "enrollmentForecastStartDate": 1656302400000, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": 1656701917314, "updatedBy": "3FhP46Kjmiro3bL7G"}, "coversPeriodDesc": "Weekly period 09/26/2022 - 09/30/2022", "periodStartDate": 1664164800000, "periodEndDate": 1664510400000, "appliedDiscounts": [{"type": "reimbursable", "source": "stateSubsidy", "amount": 750, "originalAllocation": {"allocationType": "reimbursable", "amount": 750, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "PQ7x5touwgcqEB6cu"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "pXNYtdGpKaturoFhF", "type": "planInvoice", "invoiceNumber": "6697", "openAmount": 750, "originalAmount": 500, "discountAmount": 750, "dueDate": 1663992000000, "batchStamp": 1663819501062, "openPayerAmounts": {"stateSubsidy": 750}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 500, "createdAt": *************, "paidBy": "Xxrnu24qhJW5XgDHQ", "paidByDesc": "<PERSON>", "creditMemoId": "7eHyHFAcFudpgCew4", "creditNote": "", "creditMemoType": "check"}], "payerName": "stateSubsidy"}, {"_id": "2JWEZ5gb9fACvBhfJ", "invoiceDate": "3/30/2023", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "42000", "suspendUntil": null, "enrolledPlan": {"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "42000", "suspendUntil": null}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "other", "allocationDescription": "Reimbursable: Other", "id": "TmNFjYeDvvQRLoCWc"}], "createdAt": *************, "enrollmentForecastStartDate": *************}, "coversPeriodDesc": "Weekly period 04/03/2023 - 04/07/2023", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "other", "amount": 100, "originalAllocation": {"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "other", "allocationDescription": "Reimbursable: Other", "id": "TmNFjYeDvvQRLoCWc"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "eQNegjBtSNijtsheD", "type": "planInvoice", "invoiceNumber": "7281", "openAmount": 100, "originalAmount": 140, "discountAmount": 100, "dueDate": *************, "batchStamp": *************, "openPayerAmounts": {"other": 100}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 140, "createdAt": *************, "paidBy": "P4R4p2Y8rfFHnA2tF", "paidByDesc": "<PERSON>", "creditMemoId": "GFkmXoZP4itZc4Xty", "creditNote": "", "creditMemoType": "manualCard"}], "payerName": "other"}, {"_id": "WkscoLPXpKeKqvpMM", "invoiceDate": "8/05/2021", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "d5RWqdJobwsuRuM6m", "description": "After Care", "amount": 202, "type": "plan", "frequency": "weekly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1111", "enrolledPlan": {"_id": "d5RWqdJobwsuRuM6m", "planDetails": {"_id": "d5RWqdJobwsuRuM6m", "description": "After Care", "amount": 270, "type": "plan", "frequency": "weekly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1111"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 121.2, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "tkbefjoHv5moyG9XJ"}], "createdAt": *************, "overrideRate": 202}, "coversPeriodDesc": "Weekly period 08/05/2021 - 08/09/2021", "manualInvoicePeriodStartDate": "2021-08-05", "periodStartDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 121.2, "originalAllocation": {"allocationType": "reimbursable", "amount": 121.2, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "tkbefjoHv5moyG9XJ"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "WgtTaSFgA8LXY49KB", "type": "planInvoice", "invoiceNumber": "1843", "openAmount": 121.2, "originalAmount": 80.8, "discountAmount": 121.2, "dueDate": *************, "batchStamp": *************, "openPayerAmounts": {"ccdf": 121.2}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 80.8, "createdAt": *************, "paidBy": "GEfm3tkx2DwsTktDt", "paidByDesc": "<PERSON>", "creditMemoId": "kjtD9SsRjZ3CEDXuT", "creditNote": "", "creditMemoType": "payrollDeduction"}], "payerName": "ccdf"}, {"_id": "hteSF7mr95iJsd9HJ", "invoiceDate": "10/20/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112", "enrolledPlan": {"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "JmaaWsMaMWhzvm5ax"}, {"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "c8jT2MzptuQHTpMQH"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "nFeHjzmQQxYSAQgJh"}, "coversPeriodDesc": "Weekly period 10/24/2022 - 10/28/2022", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 200, "originalAllocation": {"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "JmaaWsMaMWhzvm5ax"}}, {"type": "discount", "source": "siblingDiscount", "amount": 24, "originalAllocation": {"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "c8jT2MzptuQHTpMQH"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "agabuymN5Caxs8ruo", "type": "planInvoice", "invoiceNumber": "6809", "openAmount": 200, "originalAmount": 16, "discountAmount": 224, "dueDate": 1666411200000, "batchStamp": 1666238701046, "openPayerAmounts": {"ccdf": 200}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 16, "createdAt": *************, "paidBy": "Yg3rD3qe9FQD6wsEZ", "paidByDesc": "<PERSON><PERSON>", "creditMemoId": "BkwZHJkpxzhaLauZj", "creditNote": "", "creditMemoType": "manualCard"}], "payerName": "ccdf"}, {"_id": "P5wN9CyF98MHk5G5w", "invoiceDate": "8/05/2021", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 215, "type": "plan", "frequency": "monthly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1234", "enrolledPlan": {"_id": "2kKehRgyKp6WasstH", "planDetails": {"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 215, "type": "plan", "frequency": "monthly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1234"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 121.82, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "2vKs3r9nd3zMFrH2z"}], "createdAt": *************, "enrollmentForecastEndDate": null, "enrollmentForecastStartDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "ZdkhmaEScuuboPFbH"}, "coversPeriodDesc": "Monthly period beginning 08/05/2021", "manualInvoicePeriodStartDate": "2021-08-05", "periodStartDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 121.82, "originalAllocation": {"allocationType": "reimbursable", "amount": 121.82, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "2vKs3r9nd3zMFrH2z"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "WgtTaSFgA8LXY49KB", "type": "planInvoice", "invoiceNumber": "1860", "openAmount": 121.82, "originalAmount": 93.18, "discountAmount": 121.82, "dueDate": *************, "batchStamp": 1628215810079, "openPayerAmounts": {"ccdf": 121.82}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": null, "createdAt": 1628215827338, "paidBy": "GEfm3tkx2DwsTktDt", "paidByDesc": "<PERSON>", "creditMemoId": "zjxN2BcAHxgrXvif8", "creditNote": "", "creditMemoType": "manualCard"}, {"type": "payment", "payment_type": "credit_memo", "amount": 18.38, "createdAt": 1628215827354, "paidBy": "GEfm3tkx2DwsTktDt", "paidByDesc": "<PERSON>", "creditMemoId": "v3ZsZhD32WNPwoiuX", "creditNote": "", "creditMemoType": "manualCard"}], "payerName": "ccdf"}, {"_id": "z8P95LikF4NRAQyEZ", "invoiceDate": "2/28/2019", "createdAt": 1551344510359, "createdBy": "SYSTEM", "lineItems": [{"_id": "4fQK63BxDdnXexd7g", "description": "Preschool 3-5", "amount": 175, "type": "plan", "frequency": "weekly", "enrolledPlan": {"_id": "4fQK63BxDdnXexd7g", "planDetails": {"_id": "4fQK63BxDdnXexd7g", "description": "Preschool 3-5", "amount": 175, "type": "plan"}, "enrollmentDate": 1535846400000, "allocations": [{"allocationType": "discount", "discountType": "multipleFamily", "allocationDescription": "Discount: Multiple Family ", "amount": 10, "amountType": "percent", "id": "S737wGmRX5iKadmpY"}, {"allocationType": "reimbursable", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "amount": 50, "amountType": "dollars", "id": "JgDfBDCbPCTMvkqno"}], "createdAt": 1544116622548, "updatedAt": 1546530670812, "updatedBy": "dj7khLR8yA5JtEeYW"}, "coversPeriodDesc": "Weekly period beginning 2/28/2019", "appliedDiscounts": [{"type": "discount", "source": "multipleFamily", "amount": 12.5, "originalAllocation": {"allocationType": "discount", "discountType": "multipleFamily", "allocationDescription": "Discount: Multiple Family ", "amount": 10, "amountType": "percent", "id": "S737wGmRX5iKadmpY"}}, {"type": "reimbursable", "source": "ccdf", "amount": 50, "originalAllocation": {"allocationType": "reimbursable", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "amount": 50, "amountType": "dollars", "id": "JgDfBDCbPCTMvkqno"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "J8uD8wR7i9zycX3aR", "type": "planInvoice", "invoiceNumber": "80", "openAmount": 50, "originalAmount": 112.5, "discountAmount": 62.5, "dueDate": 1551675600000, "batchStamp": 1551344510304, "openPayerAmounts": {"ccdf": 50}, "credits": [{"type": "credit", "amount": 112.5, "createdAt": *************, "creditedBy": "vyxDKuFigFGRttBZS", "creditReason": "manual_payment", "creditNote": "", "paidBy": "tKBy9uK5ABtmQ5t5j", "paidByDesc": "<PERSON>", "creditManualPaymentMethod": "check"}], "payerName": "ccdf"}, {"_id": "3H3ah8RZ7pBESs9CQ", "invoiceDate": "9/15/2022", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "tnJqAeuSFdXvdqHfn", "description": "SSH Tuition Plan", "amount": 275, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "3654", "enrolledPlan": {"_id": "tnJqAeuSFdXvdqHfn", "planDetails": {"_id": "tnJqAeuSFdXvdqHfn", "description": "SSH Tuition Plan", "amount": 275, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "3654"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "auXZPGzqgAinx6PFN"}, {"allocationType": "discount", "amount": 100, "amountType": "dollars", "discountType": "payerDiscount", "allocationDescription": "Discount: Third-Party Payer Discount", "id": "R4CeyZxpsWMM5w4Mv"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "3FhP46Kjmiro3bL7G"}, "coversPeriodDesc": "Weekly period 09/19/2022 - 09/23/2022", "periodStartDate": *************, "periodEndDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "stateSubsidy", "amount": 100, "originalAllocation": {"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "stateSubsidy", "allocationDescription": "Reimbursable: State Subsidy", "id": "auXZPGzqgAinx6PFN"}}, {"type": "discount", "source": "payerDiscount", "amount": 100, "originalAllocation": {"allocationType": "discount", "amount": 100, "amountType": "dollars", "discountType": "payerDiscount", "allocationDescription": "Discount: Third-Party Payer Discount", "id": "R4CeyZxpsWMM5w4Mv"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "WgtTaSFgA8LXY49KB", "type": "planInvoice", "invoiceNumber": "6634", "openAmount": 100, "originalAmount": 75, "discountAmount": 200, "dueDate": 1663387200000, "batchStamp": 1663214701109, "openPayerAmounts": {"stateSubsidy": 100}, "familySplits": {"eRgyHL4H7PERcatwc": 50, "7AucAgtXkEMyW3DuB": 50}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 37.5, "createdAt": 1666711128093, "paidBy": "eRgyHL4H7PERcatwc", "paidByDesc": "<PERSON>", "creditMemoId": "aAhxemLZTDpMQqKdo", "creditNote": "", "creditMemoType": "cash"}, {"type": "payment", "payment_type": "credit_memo", "amount": 37.5, "createdAt": *************, "paidBy": "7AucAgtXkEMyW3DuB", "paidByDesc": "<PERSON>", "creditMemoId": "aS2EYh5TEptnpmMRD", "creditNote": "", "creditMemoType": "cash"}], "payerName": "stateSubsidy"}, {"_id": "meoc9T3hcuE9MYrmk", "invoiceDate": "11/02/2021", "createdAt": *************, "createdBy": "SYSTEM", "lineItems": [{"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 215, "type": "plan", "frequency": "monthly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1234", "enrolledPlan": {"_id": "2kKehRgyKp6WasstH", "planDetails": {"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 215, "type": "plan", "frequency": "monthly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1234"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 121.82, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "2vKs3r9nd3zMFrH2z"}], "createdAt": *************, "enrollmentForecastEndDate": null, "enrollmentForecastStartDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "ZdkhmaEScuuboPFbH"}, "coversPeriodDesc": "Monthly period beginning 11/02/2021", "manualInvoicePeriodStartDate": "2021-11-02", "periodStartDate": *************, "appliedDiscounts": [{"type": "reimbursable", "source": "ccdf", "amount": 121.82, "originalAllocation": {"allocationType": "reimbursable", "amount": 121.82, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "2vKs3r9nd3zMFrH2z"}}]}], "orgId": "nTvbx24M2dbM9w6tu", "personId": "WgtTaSFgA8LXY49KB", "type": "planInvoice", "invoiceNumber": "1878", "openAmount": 121.82, "originalAmount": 93.18, "discountAmount": 121.82, "dueDate": *************, "batchStamp": 1635863965069, "openPayerAmounts": {"ccdf": 121.82}, "credits": [{"type": "payment", "payment_type": "credit_memo", "amount": 37.64, "createdAt": 1636051145553, "paidBy": "eRgyHL4H7PERcatwc", "paidByDesc": "<PERSON>", "creditMemoId": "vdYLmX3gStotMfgwh", "creditNote": "", "creditMemoType": "manualCard"}, {"type": "payment", "payment_type": "credit_memo", "amount": 55.54, "createdAt": 1636051146341, "paidBy": "GEfm3tkx2DwsTktDt", "paidByDesc": "<PERSON>", "creditMemoId": "M58R9EcePmZixJCtj", "creditNote": "", "creditMemoType": "manualCard"}], "payerName": "ccdf"}]