[{"_id": "yxgNpdqeceevhnDNz", "firstName": "Easton", "lastName": "<PERSON>", "type": "person", "createdBy": "djB2aHFjG3KjJbazZ", "createdAt": 1564164542721, "orgId": "nTvbx24M2dbM9w6tu", "defaultGroupId": "", "deactivatedAt": 1564164826089, "deactivationReason": "", "inActive": true}, {"_id": "uZJ6jzQR5KsB4kiDG", "firstName": "<PERSON>", "lastName": "Shiner", "designations": [], "defaultGroupId": "8hQAoiyXZo5qpXYaq", "type": "person", "createdBy": "fCZTGnu8WkucfoGbG", "createdAt": *************, "orgId": "nTvbx24M2dbM9w6tu", "inActive": false, "billing": {"enrolledPlans": [{"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "42000", "suspendUntil": null}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "a7oRHDPzbW8XoTmL5"}, {"allocationType": "discount", "amount": 17, "amountType": "percent", "discountType": "one time", "allocationDescription": "Discount: deal", "id": "WwtjbCa5wEw4tLpE7"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "yWDbbnwaihEmMGD93"}], "pendingCharges": [], "lastInvoiced": *************, "billingNotes": "checking to see if I see this"}, "lastInformedArrival": {"source": "familyCheckin", "createdAt": *************, "checkedInById": "fhGEBuZxsTBT4cNNf", "fieldValues": {"attending": "Yes"}, "absent": false}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "uqkBwPTry4NmnBAmt", "checkedInOutTime": 1681836728744, "presenceLastGroupId": "8hQAoiyXZo5qpXYaq", "previousClassroomGroupId": null, "lastInteractionDate": *************, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "andrews name is first", "time": "6:52 pm", "date": "03/23/2023", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["uZJ6jzQR5KsB4kiDG", "agabuymN5Caxs8ruo"], "createdByPersonGroupId": "8hQAoiyXZo5qpXYaq", "_id": "oM8CpPZSd2rGKcWao", "orgName": "Mariposa Academy - Indy"}, "lastMomentByType": {"alert": {"attributionPersonId": null, "momentType": "alert", "comment": "test", "time": "2:12 pm", "date": "03/21/2023", "sortStamp": 1679422320000, "momentTypePretty": "Notification", "alertSendTypeEmail": false, "alertSendTypeText": false, "alertSendTypePush": true, "alertShowCheckin": false, "createdAt": 1679422379131, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["uZJ6jzQR5KsB4kiDG"], "createdByPersonGroupId": "8hQAoiyXZo5qpXYaq", "_id": "aCnFRZwTM7ieFq6zK", "orgName": "Mariposa Academy - Indy"}, "comment": {"attributionPersonId": null, "momentType": "comment", "comment": "andrews name is first", "time": "6:52 pm", "date": "03/23/2023", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["uZJ6jzQR5KsB4kiDG", "agabuymN5Caxs8ruo"], "createdByPersonGroupId": "8hQAoiyXZo5qpXYaq", "_id": "oM8CpPZSd2rGKcWao", "orgName": "Mariposa Academy - Indy"}, "food": {"attributionPersonId": null, "momentType": "food", "time": "6:51 pm", "date": "03/23/2023", "sortStamp": 1679611860000, "foodType": "Lunch", "foodItems": [{"name": "Grilled Cheese", "amount": "Most"}, {"name": "Green Beans", "amount": "Some"}, {"name": "Milk", "amount": "Some"}, {"name": "Peaches", "amount": "Some"}], "momentTypePretty": "Food", "createdAt": 1679611892110, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["agabuymN5Caxs8ruo", "uZJ6jzQR5KsB4kiDG"], "createdByPersonGroupId": "8hQAoiyXZo5qpXYaq", "_id": "wrB4WphFHSLT6ZGHe", "orgName": "Mariposa Academy - Indy"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "alert", "momentId": "aCnFRZwTM7ieFq6zK", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1679422379181, "targetPersonId": "uZJ6jzQR5KsB4kiDG", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "rb8Wy92F7juHcxacP"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "alert", "momentId": "aCnFRZwTM7ieFq6zK", "createdBy": "SYSTEM", "createdAt": 1679422379590, "targetPersonId": "uZJ6jzQR5KsB4kiDG", "sourcePersonId": "fhGEBuZxsTBT4cNNf", "_id": "LLY3q8rkEv5u7BNPn"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "jFDMMhjfyaKzGhtFm", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1679611749552, "targetPersonId": "uZJ6jzQR5KsB4kiDG", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "zHM9hQBrTrh9LYZ8b"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "comment", "momentId": "jFDMMhjfyaKzGhtFm", "createdBy": "SYSTEM", "createdAt": 1679611750144, "targetPersonId": "uZJ6jzQR5KsB4kiDG", "sourcePersonId": "fhGEBuZxsTBT4cNNf", "_id": "SdK8yjf5wvPXfzRpM"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "wrB4WphFHSLT6ZGHe", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1679611892225, "targetPersonId": "uZJ6jzQR5KsB4kiDG", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "aWw2J6hpZyt7JWpw7"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "oM8CpPZSd2rGKcWao", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1679611998223, "targetPersonId": "uZJ6jzQR5KsB4kiDG", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "Dap7C78xD9hfQZFwr"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "comment", "momentId": "oM8CpPZSd2rGKcWao", "createdBy": "SYSTEM", "createdAt": 1679611998682, "targetPersonId": "uZJ6jzQR5KsB4kiDG", "sourcePersonId": "fhGEBuZxsTBT4cNNf", "_id": "cEaeb8qTxmfQ9o9Ay"}], "waitlistAddedDate": 1678597200000, "allergies": "", "annualPhysicalDate": "", "backUpCare": "", "backUpCareReferringPartner": "", "billingNotes": {"Billing Notes": ""}, "cacfpSubsidy": "", "curriculum": "", "mediaRequirements": {"mediaRelease": "None", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "<PERSON>", "notes": "a shy kid", "notesPrivate": "", "payer": "", "physicianName": "", "physicianPhone": "", "primaryFamily": "", "quickList": "", "sex": "Male", "siblings": "", "standardOutlook": {"allergies": "", "importantNotes": ""}, "amBusRoute": "", "pmBusRoute": "", "school": "", "targetGroupId": ""}, {"_id": "pXNYtdGpKaturoFhF", "firstName": "<PERSON>", "lastName": "Montana", "defaultGroupId": "ATkKXtzby5gEZ6sWq", "type": "person", "createdBy": "fKriuC2Y3FrWJD2uR", "createdAt": *************, "orgId": "nTvbx24M2dbM9w6tu", "inActive": false, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "znZGcHB26SQpBqMtq", "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": "ATkKXtzby5gEZ6sWq", "billing": {"enrolledPlans": [{"_id": "9AfMyqvGT27WdRhMG", "planDetails": {"_id": "9AfMyqvGT27WdRhMG", "description": "Frog<PERSON> (2-3)", "amount": 100, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "8754"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 50, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "Kfj3RvmoWKaXzF92k"}], "createdAt": *************, "enrollmentForecastStartDate": *************}], "lastInvoiced": *************, "pendingCharges": []}, "lastInteractionDate": *************, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "We went out to recess", "time": "10:15 am", "date": "12/14/2022", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["ePB64hYk8NsrPxxRq", "GsXkcQ9izfhpNFoac", "bwmKASWnBTLrmSkoS", "5KMccvT6pukro9Ty5", "QcNdA3bpdZXmRWujH", "PwyemB257dXFc45n7", "pXNYtdGpKaturoFhF", "7kbfwHCbvySFB6KPE"], "createdByPersonGroupId": "BQ7KbinBaz3zdXRwF", "_id": "H9DnEwr9cR5ZizwK8", "orgName": "Mariposa Academy - Indy"}, "lastMomentByType": {"alert": {"attributionPersonId": null, "momentType": "alert", "comment": "test 2", "time": "4:17 pm", "date": "01/18/2022", "sortStamp": 1642540620000, "momentTypePretty": "Notification", "alertSendTypeEmail": false, "alertSendTypeText": false, "alertSendTypePush": true, "alertShowCheckin": false, "createdAt": 1642540677874, "createdBy": "v4x4cY4Tcv3NPGFH3", "attributionName": "<PERSON><PERSON><PERSON>", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["oswWok2yYE5Xy4eXt", "R37M7tvesRJLcTskH", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "vJHZ8P3KWxwRMak7a", "bwmKASWnBTLrmSkoS", "uHeWbNQmFmdZdGL6k", "5KMccvT6pukro9Ty5", "QcNdA3bpdZXmRWujH", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "pXNYtdGpKaturoFhF", "Fhrqwcq9Yw8S7FWxE", "iT9ZbCgL9ZbPwzyBS"], "_id": "Atjiii6hgYxSBE47y", "orgName": "Mariposa Academy - Indy"}, "sleep": {"attributionPersonId": null, "metaMomentId": "X3GES3LCxMfYkNiTq", "momentType": "sleep", "time": "11:56 am", "date": "9/14/2022", "sortStamp": 1663170960000, "endTime": "11:57 am", "distressedSleepCheck": true, "sleepCheckInterval": 3, "momentTypePretty": "Sleep", "sleepSatQuietly": true, "createdAt": 1663174639277, "createdBy": "47vfjpEDHaKKqoHdw", "attributionName": "<PERSON> (Staff)", "createdByPersonId": "q7wkAz3R9JhXxysxZ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["pXNYtdGpKaturoFhF"], "createdByPersonGroupId": "zvWiPSxhkgGBvq7A6", "createdByPersonCheckInGroupId": "zvWiPSxhkgGBvq7A6", "_id": "hJfrYpaDt8yJw7qSb", "orgName": "Mariposa Academy - Indy"}, "food": {"attributionPersonId": null, "momentType": "food", "time": "12:02 pm", "date": "12/14/2022", "sortStamp": 1671037320000, "foodType": "Lunch", "foodItems": [{"name": "Grilled Cheese", "amount": "Most"}, {"name": "Green Beans", "amount": "All"}, {"name": "Milk", "amount": "All"}, {"name": "Peaches", "amount": "None"}], "momentTypePretty": "Food", "createdAt": 1671040793859, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["ePB64hYk8NsrPxxRq", "GsXkcQ9izfhpNFoac", "bwmKASWnBTLrmSkoS", "5KMccvT6pukro9Ty5", "QcNdA3bpdZXmRWujH", "PwyemB257dXFc45n7", "pXNYtdGpKaturoFhF", "7kbfwHCbvySFB6KPE"], "createdByPersonGroupId": "BQ7KbinBaz3zdXRwF", "_id": "2Bbnb2Rog6w3eWeK4", "orgName": "Mariposa Academy - Indy"}, "comment": {"attributionPersonId": null, "momentType": "comment", "comment": "We went out to recess", "time": "10:15 am", "date": "12/14/2022", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["ePB64hYk8NsrPxxRq", "GsXkcQ9izfhpNFoac", "bwmKASWnBTLrmSkoS", "5KMccvT6pukro9Ty5", "QcNdA3bpdZXmRWujH", "PwyemB257dXFc45n7", "pXNYtdGpKaturoFhF", "7kbfwHCbvySFB6KPE"], "createdByPersonGroupId": "BQ7KbinBaz3zdXRwF", "_id": "H9DnEwr9cR5ZizwK8", "orgName": "Mariposa Academy - Indy"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2Bbnb2Rog6w3eWeK4", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1671040794159, "targetPersonId": "pXNYtdGpKaturoFhF", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "vHfWu5T4SnDPcCPpR"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "H9DnEwr9cR5ZizwK8", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1671040977793, "targetPersonId": "pXNYtdGpKaturoFhF", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "Z5W8ApZmesDyPePSM"}], "documentAssignments": ["Hw6wzD9XZo294St9p"], "medicationEntries": [{"_id": "5RkiW3g6MvfmGWstv", "name": "Sunscreen", "dosage": "", "frequencyType": "other", "durationType": "ongoing", "notes": "As needed"}], "deactivatedAt": 1668618011949, "deactivationReason": "Withdrawn - Relocation", "allergies": "", "annualPhysicalDate": "", "backUpCare": "", "backUpCareReferringPartner": "", "billingNotes": {"Billing Notes": ""}, "cacfpSubsidy": "", "curriculum": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "", "notes": "", "notesPrivate": "", "payer": "", "physicianName": "", "physicianPhone": "", "primaryFamily": "", "quickList": "", "sex": "", "siblings": "", "standardOutlook": {"allergies": "", "importantNotes": ""}}, {"_id": "oswWok2yYE5Xy4eXt", "firstName": "<PERSON>", "lastName": "Cho", "defaultGroupId": "ATkKXtzby5gEZ6sWq", "type": "person", "createdBy": "s3xrYq3pkm5Q7adkW", "orgId": "nTvbx24M2dbM9w6tu", "avatarPath": "nTvbx24M2dbM9w6tu/s3xrYq3pkm5Q7adkW/vg8n5sElaPIlLHTGOF7t", "avatarToken": "vg8n5sElaPIlLHTGOF7t", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "s9Dcs8ixrEWvwhwE2", "checkedInOutTime": 1627603201744, "lastInteractionDate": 1627561080000, "lastMoment": {"attributionPersonId": null, "momentType": "food", "comment": "Fruit and yogurt", "time": "8:18 am", "date": "07/29/2021", "sortStamp": 1627561080000, "foodType": "Breakfast", "foodAmount": "", "momentTypePretty": "Food", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "taggedPeople": ["hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "R37M7tvesRJLcTskH", "vJHZ8P3KWxwRMak7a", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "bwmKASWnBTLrmSkoS", "QcNdA3bpdZXmRWujH", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "vafkMjcEs8qLZyfTr", "R6fKP3hHEBafxeyuq", "Qhirjw7H7Lr9PccCK", "eQNegjBtSNijtsheD", "qTLvGki7YvdTQWp8Q", "5KMccvT6pukro9Ty5", "QxTFrRwFCXF3pfaao", "pCHFhtbNwq7dG4eHG", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "MdKPhsKB4btnEZt6n", "LjRaTQFhkYwbztoks", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "WgtTaSFgA8LXY49KB", "HMKtg6wfCM7b2bn8E"], "_id": "doLLNyz83JGDgdcgZ"}, "lastMomentByType": {"potty": {"momentType": "potty", "time": "4:34 pm", "date": "11/27/2019", "sortStamp": 1574890440000, "pottyType": "BM", "momentTypePretty": "<PERSON><PERSON>", "pottyTraining": "", "pottyAppliedOintment": false, "createdAt": 1574879696832, "createdBy": "fKriuC2Y3FrWJD2uR", "createdByPersonId": "dXKLkP4bNp5WofuQC", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["oswWok2yYE5Xy4eXt"], "_id": "WhaJfn7sZPquu4CLS", "orgName": "Mariposa Academy"}, "comment": {"momentType": "comment", "comment": "You all did great today!", "time": "4:12 pm", "date": "10/15/2019", "sortStamp": 1571170320000, "momentTypePretty": "Comment", "createdAt": 1571170500587, "createdBy": "FMAMv6kpgQZf4HS8x", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["Ao67WMf2Gi3GfW8Ro", "oswWok2yYE5Xy4eXt", "N5yn3DbpjCMdGgAJR", "pCHFhtbNwq7dG4eHG", "R37M7tvesRJLcTskH"], "_id": "s8oWK7xrmWWqAbojx", "orgName": "Bethlehem Township"}, "food": {"attributionPersonId": null, "momentType": "food", "comment": "Fruit and yogurt", "time": "8:18 am", "date": "07/29/2021", "sortStamp": 1627561080000, "foodType": "Breakfast", "foodAmount": "", "momentTypePretty": "Food", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "taggedPeople": ["hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "R37M7tvesRJLcTskH", "vJHZ8P3KWxwRMak7a", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "bwmKASWnBTLrmSkoS", "QcNdA3bpdZXmRWujH", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "vafkMjcEs8qLZyfTr", "R6fKP3hHEBafxeyuq", "Qhirjw7H7Lr9PccCK", "eQNegjBtSNijtsheD", "qTLvGki7YvdTQWp8Q", "5KMccvT6pukro9Ty5", "QxTFrRwFCXF3pfaao", "pCHFhtbNwq7dG4eHG", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "MdKPhsKB4btnEZt6n", "LjRaTQFhkYwbztoks", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "WgtTaSFgA8LXY49KB", "HMKtg6wfCM7b2bn8E"], "_id": "doLLNyz83JGDgdcgZ"}, "sleep": {"momentType": "sleep", "time": "2:06 pm", "date": "1/9/2020", "sortStamp": 1578596760000, "endTime": "3:06 pm", "momentTypePretty": "Sleep", "createdAt": 1578600389912, "createdBy": "FMAMv6kpgQZf4HS8x", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["oswWok2yYE5Xy4eXt"], "_id": "RyrdQx9pfNrNBKKru", "orgName": "Childcare Network - Phenix City (Gateway Drive)"}, "learning": {"momentType": "learning", "time": "1:40 pm", "date": "04/12/2019", "sortStamp": 1555090800000, "momentTypePretty": "Learning", "learningType": "", "learningCurriculumId": "MrpXLo7HjfT7ySnBM", "createdAt": 1555087755472, "createdBy": "fKriuC2Y3FrWJD2uR", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["J8uD8wR7i9zycX3aR", "MdKPhsKB4btnEZt6n", "vJHZ8P3KWxwRMak7a", "R6fKP3hHEBafxeyuq", "BaDDgKr4brvSJomZZ", "WtNhYoB3zDXNwSbMD", "JXMHjAYzsBx22h5CS", "Qhirjw7H7Lr9PccCK", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "tbhkmKDZnT7sFb6uD", "Ao67WMf2Gi3GfW8Ro", "hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "vafkMjcEs8qLZyfTr", "N5yn3DbpjCMdGgAJR", "pCHFhtbNwq7dG4eHG", "R37M7tvesRJLcTskH"], "_id": "WsWcwpprAPh9hXtfs", "orgName": "Premier"}, "activity": {"momentType": "activity", "comment": "Getting good and SILLY as we stretch!  ", "time": "9:30 am", "date": "02/08/2019", "sortStamp": 1549636200000, "activityType": "Exercise", "activityEngagement": "Active", "momentTypePretty": "Activity", "createdAt": 1549637831398, "createdBy": "s3xrYq3pkm5Q7adkW", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["tbhkmKDZnT7sFb6uD", "Ao67WMf2Gi3GfW8Ro", "oswWok2yYE5Xy4eXt", "vafkMjcEs8qLZyfTr", "N5yn3DbpjCMdGgAJR", "pCHFhtbNwq7dG4eHG", "R37M7tvesRJLcTskH"], "mediaFiles": [{"mediaUrl": null, "mediaToken": "lNi1J5yox91SUEfMi2Qw", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/s3xrYq3pkm5Q7adkW/lNi1J5yox91SUEfMi2Qw"}], "_id": "KcHQoMrmRFw8zAucC", "orgName": "Premier"}, "supplies": {"momentType": "supplies", "comment": "Please send a back-up set of clothes.  We sent his home with him last week.  Thanks!", "time": "11:00 am", "date": "01/09/2019", "sortStamp": 1547049600000, "supplyType": "Clothing", "momentTypePretty": "Supplies", "createdAt": 1547046067638, "createdBy": "s3xrYq3pkm5Q7adkW", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["oswWok2yYE5Xy4eXt"], "_id": "GRNABcxXfMK6seKGE"}, "alert": {"attributionPersonId": null, "momentType": "alert", "comment": "Reminder: We will be closed on Monday, July 19 for a teacher in-service day.", "time": "5:39 pm", "date": "07/16/2021", "sortStamp": 1626471540000, "momentTypePretty": "Notification", "alertSendTypeEmail": false, "alertSendTypeText": false, "alertSendTypePush": true, "alertShowCheckin": false, "createdAt": 1626471553840, "createdBy": "3FhP46Kjmiro3bL7G", "attributionName": "<PERSON>", "createdByPersonId": "8uAYxXbT6urWxXg3k", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["vyxDKuFigFGRttBZS", "RYetWpMJhsC3BcPjE", "69BREkBxEe8an4TbA", "9W9bZ5DJGF7LWaLmJ", "N7Yynvhs9pqBEiR92", "BgEJcyPDDPPGT97w5", "y6Lp9DaDAKZP6WZDr", "Ao67WMf2Gi3GfW8Ro", "pCHFhtbNwq7dG4eHG", "vafkMjcEs8qLZyfTr", "hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "N5yn3DbpjCMdGgAJR", "J8uD8wR7i9zycX3aR", "R37M7tvesRJLcTskH", "TKw3K4856H8Et7xNx", "4pPTmaevgLcoWExz2", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "MdKPhsKB4btnEZt6n", "JXMHjAYzsBx22h5CS", "Qhirjw7H7Lr9PccCK", "R6fKP3hHEBafxeyuq", "BaDDgKr4brvSJomZZ", "vJHZ8P3KWxwRMak7a", "LjRaTQFhkYwbztoks", "2papyMAsWKoebY55y", "GsXkcQ9izfhpNFoac", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "bwmKASWnBTLrmSkoS", "9zXMi5TWSNn2t3rYR", "veLKDqRsKQibwwtDj", "vyyGJsucZimKjkxJa", "wd39xoosTnzjsGb6c", "8uAYxXbT6urWxXg3k", "WgtTaSFgA8LXY49KB", "MZLqugrBbXT6b93aF", "Ko2dpdjhLtwTP6HHs", "fYEgtG9KzZakDD8Lq", "eQNegjBtSNijtsheD", "9ovZqAWdGKjaRgRu7", "XKs6nQpNv7q9RYEvK", "HMKtg6wfCM7b2bn8E", "xYhHHnxhiTXFTxscg", "XHjxC6SJmt34NMGzp", "Fhrqwcq9Yw8S7FWxE", "iT5PLBudyY9FKurF7", "pyYSwFc2PJjHJKrKW", "GkqcNHheuvn8SBXvn", "MyFepQyWoycBRmBCS", "LFuMMLpNGqaNR4v78", "7A4gspSfjvrGd2bSQ", "8jgMSvrFYadoNgRxY", "yT9Zeaj2EzH85MQoj", "ZoS2Y8wwSBNwrJkRs", "gHoZrjN9f6LHzEegS", "FfzHpuRYnX5nCayfS", "ALszCkiMER4Yzujmz", "EYCwPvkG5Zd23hsSZ", "nmQ5Lmv6S6aX8XhxC", "gijNLLWJbjzikeMWz", "qTLvGki7YvdTQWp8Q", "2Sd4utZGjpDQeut64", "wSTzopnnwsbYWwsri", "bgYPrFT6jnRbzwpZz", "xdPGHYnY4LDTW5uaK", "jcRkB6PBCFNRS9asn", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "ch2ZhHu3h7w35zSWB", "LjANQKbvYfGtThZs5", "q4cwWG8DpsDjmmnL6", "LBDaRNzLqg9NRGXW6", "5KMccvT6pukro9Ty5", "rAaxA8yPKFYDjrzJk", "CofTFp5PRggvz477E", "QcNdA3bpdZXmRWujH", "C89A4qgWiHB67qGR2", "DkDL5e5jELZBbv6PT", "ddYRmKZd2n79NiakF", "gGyQF3jYMbF7bp2cF", "TTbkpM3mxsnNEzeN6", "6aguBjjQNCMXPnse8", "J5wzhMne96f5uFxMY", "iXgWLbg5GDbdTB2Bw", "D5R4qkBKZkKejEKDB", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "XyD2Hw6RfqegfeMtY", "iT9ZbCgL9ZbPwzyBS", "GHhRHxXwKkAQPSTRH", "gvPNP3DyDif4opnia", "7rjCurgW62RebGjPY", "pyNcuF7Jm7GNehmPz", "u5s56Y8CswdDjh2GP", "f2kLwWFwL4mfWpJCK", "tyXnH6RLpnqNcHf6Y", "QxTFrRwFCXF3pfaao", "ACS7zGZzLsPv65y39", "PKNsmS5hNRZFvtNgp", "27WKMANJrmX6JwMXq", "HE5MvfRwX4wqrX3Tv"], "_id": "D5MDbP8P8XhjujeH7", "orgName": "Mariposa Academy - Indianapolis"}, "checkin": {"attributionPersonId": null, "momentType": "checkin", "comment": "Checked into Mighty Explorers Pre-K", "time": "7:47 am", "date": "07/26/2021", "sortStamp": 1627300020000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "taggedPeople": ["oswWok2yYE5Xy4eXt"], "_id": "kYRZphEcCmGvujCbY"}, "checkout": {"attributionPersonId": null, "momentType": "checkout", "comment": "Checked out of Mighty Explorers Pre-K", "time": "4:47 pm", "date": "07/26/2021", "sortStamp": 1627332420000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "taggedPeople": ["oswWok2yYE5Xy4eXt"], "_id": "FiRhuBgbPssDxxo86"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "zjAACMFvQXkX6EMQd", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627493004030, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "dxFvs358LD24XdG2K"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "LoJaEHspxqFPeanFH", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627493015295, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "YoyL6CfZMqqDTNDC5"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "pgScyzYZsFpXNZf9N", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627493030613, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "SZWZrksQYSaDd88Ya"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "hrkavJBhtNGhoJeXq", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627493043439, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "JJgExQaLC9KeL6Jkc"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "6a7GwJbeB2bjSErCr", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627493055861, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "vzKp2HfuG8AdHhZL2"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "F8LntQ4zjycnvRdAy", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627493070564, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "wif2hroPug6xDdrXF"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "kYRZphEcCmGvujCbY", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627493088588, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "BSj3xkEmPn5zkDkph"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "FiRhuBgbPssDxxo86", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627493103222, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "vk8cLGLhzkBLfA2Y7"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WHeb7RWraR9yH7rbL", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560284254, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "5rvg3AY8uFtMHjdZS"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WHeb7RWraR9yH7rbL", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627560331002, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "ttXbP4uT3j7gZEWcx"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560723350, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "eWTXKWYPRHy7BxjWo"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560758539, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "BDNb8bzRwA6urWpE4"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "qwwgzyRjPAEBXDBQz", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560835673, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "Pqqf79KnmG3LL9LiF"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560926540, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "J5ffM8BX67jt8Xjw3"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627560953046, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "u6Qpk3jvdbRdvEP5Z"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560964304, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "dYzcCFdXrTq3AEDyj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627561001993, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "M43jaEWLgdK5ALurr"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627561023031, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "9mkBH6mbc4wPrphxy"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561072900, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "dBiPHTCwDheCpWqQb"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Ks2fyxKMyceKLW9D7", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561113765, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "XS66PYqaabGSuhXuN"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561151030, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "5H8rHd8XSmmFrNQie"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "gXPAdm3ebZiHZGZwR", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561200487, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "mZBMLr7RF4KkQykC4"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "GkA8bk2yHPsvnz7dt", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561240664, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "7HLZ7efKqhwRYHAkn"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "czWazq2hpB4QkWsWn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561264021, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "3P5xW8NPr7WsXdKKg"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561303512, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "hN3oYgjFoA9Ng6cBj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561327449, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "sRb24RhqgFDfBCwqn"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NhEMjjJaA87tBxfbv", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561362936, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "Rmg9F3gxfLco8AbpK"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561402125, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "8vF2ib3axLxwvWTR8"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "uAGjsoQiokpbJGZn8", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561430218, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "iz6K9w5TdLo2Zzt2J"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "9J6WH7kvJpsWv28gw", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561450611, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "PjFbKuQLtmHba7p5y"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NsAdQkWc3azSa4Rvu", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561493262, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "FPMdxbGX53BCf9Pdx"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561522939, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "nTECqXqjhWLg82AwX"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "27MLf4ccSxNHMoy9Z", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561557230, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "jpFqmRmChjCyfqWxj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627561579087, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "DA8M2HnNmXg4kmTpW"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561595773, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "4dwQCwsW9M5TcFTuv"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561630969, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "rAK9YtfxQfAuBuk87"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "rRuGo5tmTEX7f9hwT", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561674644, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "LmncqX5dC36imeaDi"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561704732, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "E7Xg4gM2zEh2S7Qyy"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "9J6WH7kvJpsWv28gw", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627561708515, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "M9yxAobpgjeHx3Xre"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CC2cC5Q9XAt2BeSQC", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561738947, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "Kdto996XzQcXWpG56"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "gXPAdm3ebZiHZGZwR", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566820881, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "u5tzCBmHFrz8GKEQy"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NsAdQkWc3azSa4Rvu", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566866171, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "mi8nnBYFS68AfPbf2"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566935891, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "TeJTexorjSLdEPRqX"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "qwwgzyRjPAEBXDBQz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566966038, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "GnTPpYpYhTXfummdS"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567044171, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "d27TPiMcjJ2dy2ybQ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567063667, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "juH6M6nAnCBr6ofxD"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CC2cC5Q9XAt2BeSQC", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567080342, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "uPLWdJRf3op5deJDL"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "uAGjsoQiokpbJGZn8", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567100390, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "K8S4NsGXgCcqQWhRH"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567116054, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "GQGdWz88k7yqakKSE"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567133829, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "7rAynwngb6q2SyzR4"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567148942, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "ZFuKvZu5whX4FnLN4"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "rRuGo5tmTEX7f9hwT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567166281, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "y5ohhZ7Jy8GajPfMZ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567185647, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "6JBchmScMJ7D46PA3"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567202482, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "pR4jGzMD5riDtXCG5"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567217648, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "CX65ZsCaZ4hLQPqLS"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567234752, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "treqAkfCknSBDoXgN"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567248475, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "N9k3i4xdfjkN3PGiY"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567264795, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "J2be5vnvAwrvJP3dj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567293005, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "PFazhjd5pMoNkhzsv"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "27MLf4ccSxNHMoy9Z", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567310326, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "dvN6HfeqvZh9sHEY5"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "czWazq2hpB4QkWsWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567327309, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "RgfnsWLbDbwQijftN"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567445484, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "DfYm52dpipP9hjPvi"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567455428, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "HqJPY7KdQKzWFpbEu"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567466064, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "CzZoH9ZK2xcFY9ZGP"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567475981, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Laiw7Wd6MQ2KQTQH9"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567481619, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "zR5SeMn4vL8LCim3H"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567489122, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "ABogbaE6NZNMBzaPe"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567511161, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "j2NQFHB82fRGkdwfW"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567557618, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "HeDWNxrSCdoRZpn4T"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567567348, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "JZqTs4pd5wxeJAea5"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567578325, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "cKqWifS6iSYMnHz3W"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567584678, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "uQmvcGF33X9fGpRRn"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567598468, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "M4WZP6omp7CwuT2dp"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567614132, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "hsA26rXLNxkmhWtRC"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567622267, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "66QkZKebNznbTobyn"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": *************, "targetPersonId": "oswWok2yYE5Xy4eXt", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "za23B7erzyx3e2xTz"}], "cacfpSubsidy": "Paid", "billing": {"enrolledPlans": [{"_id": "utXYN39S8tQkTSBc6", "planDetails": {"_id": "utXYN39S8tQkTSBc6", "description": "Infant 3-day ", "amount": 1000, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4029"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 100, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": *************, "payerEndDate": *************, "id": "sbqrNPNvmNukfYHDf"}, {"allocationType": "reimbursable", "amount": 50, "amountType": "dollars", "reimbursementType": "onmywayprek", "allocationDescription": "Reimbursable: On My Way Pre-K", "payerStartDate": *************, "payerEndDate": *************, "id": "Ztmk5a79YBKLTCnJH"}, {"allocationType": "reimbursable", "amount": 85, "amountType": "dollars", "reimbursementType": "unitedway", "allocationDescription": "Reimbursable: United Way", "payerStartDate": *************, "id": "iaZYS6c3E9LLZDoZK"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "ZdkhmaEScuuboPFbH"}], "lastInvoiced": 1691685868755, "pendingCharges": []}, "enrollmentDate": 1592193600000, "classList": {"defaultDays": {"M": "1", "T": "1", "W": "1", "R": "1", "F": "1"}, "transitionDays": {"M": 0, "T": 0, "W": 0, "R": 0, "F": 0}}, "transitionGroupDate": null, "transitionGroupId": "", "inActive": false, "allergies": "", "annualPhysicalDate": "", "backUpCare": "", "backUpCareReferringPartner": "", "billingNotes": {"Billing Notes": ""}, "birthday": 1514782800000, "curriculum": "", "nickname": "", "notes": "", "notesPrivate": "", "payer": "", "physicianName": "", "physicianPhone": "", "primaryFamily": "", "quickList": "", "sex": "", "siblings": "", "previousClassroomGroupId": "ATkKXtzby5gEZ6sWq"}, {"_id": "cFT79Bw9Y85CfEeRv", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "designations": [], "type": "person", "createdBy": "v4x4cY4Tcv3NPGFH3", "createdAt": *************, "orgId": "nTvbx24M2dbM9w6tu", "inActive": false, "billing": {"pendingCharges": [], "lastInvoiced": *************, "enrolledPlans": [{"_id": "8qNxxfASYcePBi4XA", "planDetails": {"_id": "8qNxxfASYcePBi4XA", "description": "PreK Full Time", "amount": 1250, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "1000"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 500, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "MLzey9kXQg3Wo2BaE"}], "createdAt": *************, "enrollmentForecastStartDate": *************}]}, "allergies": "", "annualPhysicalDate": "", "backUpCare": "", "backUpCareReferringPartner": "", "billingNotes": {"Billing Notes": ""}, "cacfpSubsidy": "", "curriculum": "", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "nickname": "", "notes": "", "notesPrivate": "", "payer": "", "physicianName": "", "physicianPhone": "", "primaryFamily": "", "quickList": "", "sex": "", "siblings": "", "standardOutlook": {"allergies": "", "importantNotes": ""}, "withdrawDate": *************, "deactivatedAt": *************, "deactivationReason": "Withdrawn - Behavior"}, {"_id": "agabuymN5Caxs8ruo", "firstName": "<PERSON>", "lastName": "Pickles", "designations": [], "defaultGroupId": "8hQAoiyXZo5qpXYaq", "type": "person", "createdBy": "nFeHjzmQQxYSAQgJh", "createdAt": *************, "orgId": "nTvbx24M2dbM9w6tu", "inActive": false, "avatarPath": "nTvbx24M2dbM9w6tu/nFeHjzmQQxYSAQgJh/EtOgXsCgVKdK8cNIEkBC", "avatarToken": "EtOgXsCgVKdK8cNIEkBC", "billing": {"enrolledPlans": [{"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 200, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "JmaaWsMaMWhzvm5ax"}, {"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "siblingDiscount", "allocationDescription": "Discount: Sibling Discount", "id": "c8jT2MzptuQHTpMQH"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "nFeHjzmQQxYSAQgJh"}, {"_id": "fa5XsPjDR4fHH3i3g", "planDetails": {"_id": "fa5XsPjDR4fHH3i3g", "description": "Infant - Full Time (Monthly)", "amount": 1500, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4321"}, "enrollmentDate": *************, "createdAt": *************}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "uqkBwPTry4NmnBAmt", "checkedInOutTime": *************, "presenceLastGroupId": null, "previousClassroomGroupId": "8hQAoiyXZo5qpXYaq", "lastInteractionDate": *************, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "andrews name is first", "time": "6:52 pm", "date": "03/23/2023", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["uZJ6jzQR5KsB4kiDG", "agabuymN5Caxs8ruo"], "createdByPersonGroupId": "8hQAoiyXZo5qpXYaq", "_id": "oM8CpPZSd2rGKcWao", "orgName": "Mariposa Academy - Indy"}, "lastMomentByType": {"food": {"attributionPersonId": null, "momentType": "food", "time": "6:51 pm", "date": "03/23/2023", "sortStamp": 1679611860000, "foodType": "Lunch", "foodItems": [{"name": "Grilled Cheese", "amount": "Most"}, {"name": "Green Beans", "amount": "Some"}, {"name": "Milk", "amount": "Some"}, {"name": "Peaches", "amount": "Some"}], "momentTypePretty": "Food", "createdAt": 1679611892110, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["agabuymN5Caxs8ruo", "uZJ6jzQR5KsB4kiDG"], "createdByPersonGroupId": "8hQAoiyXZo5qpXYaq", "_id": "wrB4WphFHSLT6ZGHe", "orgName": "Mariposa Academy - Indy"}, "activity": {"attributionPersonId": null, "momentType": "activity", "comment": "Today we are reading I know an old lady who swallowed a pie", "time": "1:30 pm", "date": "11/09/2022", "sortStamp": 1668018600000, "activityType": "Discussion/Reminisce/Read", "activityEngagement": "", "momentTypePretty": "Activity", "createdAt": 1668016550588, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n", "R6fKP3hHEBafxeyuq", "vJHZ8P3KWxwRMak7a", "jcRkB6PBCFNRS9asn", "agabuymN5Caxs8ruo", "qt9WuMuuxQT6oiPZx", "yE23pvXd3zkRze6Hw", "wTra3Pxtv5rfYrNeS"], "mediaFiles": [], "_id": "Rv3XB6gYJHzDnmEQ2", "orgName": "Mariposa Academy - Indy"}, "sleep": {"attributionPersonId": null, "momentType": "sleep", "time": "1:00 pm", "date": "11/17/2022", "sortStamp": 1668708000000, "endTime": "3:00 pm", "distressedSleepCheck": true, "momentTypePretty": "Sleep", "sleepDidNotSleep": false, "createdAt": 1668696386469, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n", "R6fKP3hHEBafxeyuq", "vJHZ8P3KWxwRMak7a", "jcRkB6PBCFNRS9asn", "agabuymN5Caxs8ruo", "qt9WuMuuxQT6oiPZx", "yE23pvXd3zkRze6Hw", "wTra3Pxtv5rfYrNeS"], "_id": "TXwvnobp96mmza2qA", "orgName": "Mariposa Academy - Indy"}, "potty": {"attributionPersonId": null, "momentType": "potty", "time": "12:30 pm", "date": "11/17/2022", "sortStamp": 1668706200000, "pottyType": "", "momentTypePretty": "<PERSON><PERSON>", "pottyTraining": "", "pottyAppliedOintment": false, "createdAt": 1668696352778, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n", "R6fKP3hHEBafxeyuq", "vJHZ8P3KWxwRMak7a", "jcRkB6PBCFNRS9asn", "agabuymN5Caxs8ruo", "qt9WuMuuxQT6oiPZx", "yE23pvXd3zkRze6Hw", "wTra3Pxtv5rfYrNeS"], "_id": "yCBfgnENKmehHBtfE", "orgName": "Mariposa Academy - Indy"}, "comment": {"attributionPersonId": null, "momentType": "comment", "comment": "andrews name is first", "time": "6:52 pm", "date": "03/23/2023", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "yWDbbnwaihEmMGD93", "attributionName": "<PERSON>", "createdByPersonId": "uqkBwPTry4NmnBAmt", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["uZJ6jzQR5KsB4kiDG", "agabuymN5Caxs8ruo"], "createdByPersonGroupId": "8hQAoiyXZo5qpXYaq", "_id": "oM8CpPZSd2rGKcWao", "orgName": "Mariposa Academy - Indy"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "jFDMMhjfyaKzGhtFm", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1679611749522, "targetPersonId": "agabuymN5Caxs8ruo", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "xeCMN9bNe8iGRaivQ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "wrB4WphFHSLT6ZGHe", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1679611892199, "targetPersonId": "agabuymN5Caxs8ruo", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "jR6pAHzT6XfYyppJ3"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "oM8CpPZSd2rGKcWao", "createdBy": "uqkBwPTry4NmnBAmt", "createdAt": 1679611998227, "targetPersonId": "agabuymN5Caxs8ruo", "sourcePersonId": "uqkBwPTry4NmnBAmt", "_id": "okgr9jpw4PWJMEtWB"}]}, {"_id": "WgtTaSFgA8LXY49KB", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "defaultGroupId": "zvWiPSxhkgGBvq7A6", "type": "person", "createdBy": "fKriuC2Y3FrWJD2uR", "createdAt": *************, "orgId": "nTvbx24M2dbM9w6tu", "avatarPath": "nTvbx24M2dbM9w6tu/fKriuC2Y3FrWJD2uR/gTCWEnkvSZeNQxn9TA4s", "avatarToken": "gTCWEnkvSZeNQxn9TA4s", "billing": {"enrolledPlans": [{"_id": "2kKehRgyKp6WasstH", "planDetails": {"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 215, "type": "plan", "frequency": "monthly", "suspendUntil": *************, "category": "", "ledgerAccountName": "1234"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 121.82, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "2vKs3r9nd3zMFrH2z"}], "createdAt": *************, "enrollmentForecastEndDate": null, "enrollmentForecastStartDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "ZdkhmaEScuuboPFbH"}], "lastInvoiced": *************, "pendingCharges": [], "lastInvoicedDaily": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": *************, "billingNotes": {"Billing Notes": "This is an admin only note"}, "lastInteractionDate": *************, "lastMoment": {"attributionPersonId": null, "metaMomentId": "MSKTWdR9QAoJwwx5A", "momentType": "sleep", "time": "10:07 am", "date": "7/29/2021", "sortStamp": *************, "sleepCheckInterval": 3, "momentTypePretty": "Sleep", "createdAt": 1627567663878, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["WgtTaSFgA8LXY49KB"], "_id": "eCriZRn6txQf2CkmJ", "orgName": "Mariposa Academy - Indianapolis"}, "lastMomentByType": {"alert": {"attributionPersonId": null, "momentType": "alert", "comment": "Reminder: We will be closed on Monday, July 19 for a teacher in-service day.", "time": "5:39 pm", "date": "07/16/2021", "sortStamp": 1626471540000, "momentTypePretty": "Notification", "alertSendTypeEmail": false, "alertSendTypeText": false, "alertSendTypePush": true, "alertShowCheckin": false, "createdAt": 1626471553840, "createdBy": "3FhP46Kjmiro3bL7G", "attributionName": "<PERSON>", "createdByPersonId": "8uAYxXbT6urWxXg3k", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["vyxDKuFigFGRttBZS", "RYetWpMJhsC3BcPjE", "69BREkBxEe8an4TbA", "9W9bZ5DJGF7LWaLmJ", "N7Yynvhs9pqBEiR92", "BgEJcyPDDPPGT97w5", "y6Lp9DaDAKZP6WZDr", "Ao67WMf2Gi3GfW8Ro", "pCHFhtbNwq7dG4eHG", "vafkMjcEs8qLZyfTr", "hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "N5yn3DbpjCMdGgAJR", "J8uD8wR7i9zycX3aR", "R37M7tvesRJLcTskH", "TKw3K4856H8Et7xNx", "4pPTmaevgLcoWExz2", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "MdKPhsKB4btnEZt6n", "JXMHjAYzsBx22h5CS", "Qhirjw7H7Lr9PccCK", "R6fKP3hHEBafxeyuq", "BaDDgKr4brvSJomZZ", "vJHZ8P3KWxwRMak7a", "LjRaTQFhkYwbztoks", "2papyMAsWKoebY55y", "GsXkcQ9izfhpNFoac", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "bwmKASWnBTLrmSkoS", "9zXMi5TWSNn2t3rYR", "veLKDqRsKQibwwtDj", "vyyGJsucZimKjkxJa", "wd39xoosTnzjsGb6c", "8uAYxXbT6urWxXg3k", "WgtTaSFgA8LXY49KB", "MZLqugrBbXT6b93aF", "Ko2dpdjhLtwTP6HHs", "fYEgtG9KzZakDD8Lq", "eQNegjBtSNijtsheD", "9ovZqAWdGKjaRgRu7", "XKs6nQpNv7q9RYEvK", "HMKtg6wfCM7b2bn8E", "xYhHHnxhiTXFTxscg", "XHjxC6SJmt34NMGzp", "Fhrqwcq9Yw8S7FWxE", "iT5PLBudyY9FKurF7", "pyYSwFc2PJjHJKrKW", "GkqcNHheuvn8SBXvn", "MyFepQyWoycBRmBCS", "LFuMMLpNGqaNR4v78", "7A4gspSfjvrGd2bSQ", "8jgMSvrFYadoNgRxY", "yT9Zeaj2EzH85MQoj", "ZoS2Y8wwSBNwrJkRs", "gHoZrjN9f6LHzEegS", "FfzHpuRYnX5nCayfS", "ALszCkiMER4Yzujmz", "EYCwPvkG5Zd23hsSZ", "nmQ5Lmv6S6aX8XhxC", "gijNLLWJbjzikeMWz", "qTLvGki7YvdTQWp8Q", "2Sd4utZGjpDQeut64", "wSTzopnnwsbYWwsri", "bgYPrFT6jnRbzwpZz", "xdPGHYnY4LDTW5uaK", "jcRkB6PBCFNRS9asn", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "ch2ZhHu3h7w35zSWB", "LjANQKbvYfGtThZs5", "q4cwWG8DpsDjmmnL6", "LBDaRNzLqg9NRGXW6", "5KMccvT6pukro9Ty5", "rAaxA8yPKFYDjrzJk", "CofTFp5PRggvz477E", "QcNdA3bpdZXmRWujH", "C89A4qgWiHB67qGR2", "DkDL5e5jELZBbv6PT", "ddYRmKZd2n79NiakF", "gGyQF3jYMbF7bp2cF", "TTbkpM3mxsnNEzeN6", "6aguBjjQNCMXPnse8", "J5wzhMne96f5uFxMY", "iXgWLbg5GDbdTB2Bw", "D5R4qkBKZkKejEKDB", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "XyD2Hw6RfqegfeMtY", "iT9ZbCgL9ZbPwzyBS", "GHhRHxXwKkAQPSTRH", "gvPNP3DyDif4opnia", "7rjCurgW62RebGjPY", "pyNcuF7Jm7GNehmPz", "u5s56Y8CswdDjh2GP", "f2kLwWFwL4mfWpJCK", "tyXnH6RLpnqNcHf6Y", "QxTFrRwFCXF3pfaao", "ACS7zGZzLsPv65y39", "PKNsmS5hNRZFvtNgp", "27WKMANJrmX6JwMXq", "HE5MvfRwX4wqrX3Tv"], "_id": "D5MDbP8P8XhjujeH7", "orgName": "Mariposa Academy - Indianapolis"}, "potty": {"attributionPersonId": null, "momentType": "potty", "time": "1:34 pm", "date": "07/28/2021", "sortStamp": 1627493640000, "pottyType": "", "momentTypePretty": "<PERSON><PERSON>", "pottyTraining": "Successful", "pottyAppliedOintment": false, "createdAt": 1627493688957, "createdBy": "nFeHjzmQQxYSAQgJh", "attributionName": "<PERSON>", "createdByPersonId": "8jgMSvrFYadoNgRxY", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n", "WgtTaSFgA8LXY49KB", "HMKtg6wfCM7b2bn8E", "QEWjwEM4nEnYPzqJ6"], "_id": "Ei9iwARz3EgkJbrdM", "orgName": "Mariposa Academy - Indianapolis"}, "food": {"attributionPersonId": null, "momentType": "food", "time": "10:00 am", "date": "07/29/2021", "sortStamp": 1627567200000, "foodType": "AM Snack", "foodItems": [{"name": "Cereal", "amount": "All"}, {"name": "Milk", "amount": "Most"}], "momentTypePretty": "Food", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "comment": "", "taggedPeople": ["pCHFhtbNwq7dG4eHG", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "MdKPhsKB4btnEZt6n", "LjRaTQFhkYwbztoks", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "WgtTaSFgA8LXY49KB", "HMKtg6wfCM7b2bn8E"], "_id": "Zq3sT7fG6W7QH22dP"}, "portfolio": {"attributionPersonId": null, "momentType": "portfolio", "time": "2:00 pm", "date": "3/3/2021", "sortStamp": 1614798000000, "momentTypePretty": "Portfolio", "portfolioCurriculumId": "Y3BjTpocFDX6PXvPk", "portfolioCurriculum": {"_id": "Y3BjTpocFDX6PXvPk", "headline": "Singing the Red Song", "message": "Singing the red song\r\n\r\nR-E-D Red\r\nR-E-D Red\r\nThat spells red\r\nThat spells red\r\nFiretrucks are red\r\nStop signs are red, too\r\nR-E-D\r\nR-E-D", "scheduledDate": 1614747600000, "selectedGroups": ["zvWiPSxhkgGBvq7A6"], "selectedStandards": ["Lightbridge|69c57e4ec976f86d7998f436c66c4c7ffde87dd4", "Lightbridge|f2a3f3a2327e343e77f13c587f2d2e255209c88a", "Lightbridge|e447bc5b42f4c1636f7ea0983d1746ece5338060", "Lightbridge|828748a7bf1be0f294527a10a47c56ac43f51adf"], "selectedTypes": ["Preschool: Music and Movement"], "selectedAgeGroup": "3 years - 4 years", "curriculumThemeId": "y5mtdCzrop7Nu6NhL", "originalCurriculumId": "hX2KYCiKsG2bPsv74", "orgId": "nTvbx24M2dbM9w6tu", "createdBy": "3FhP46Kjmiro3bL7G", "createdAt": 1614794010590, "mediaFiles": []}, "portfolioAssessments": [{"standardId": "69c57e4ec976f86d7998f436c66c4c7ffde87dd4", "value": 3}, {"standardId": "f2a3f3a2327e343e77f13c587f2d2e255209c88a", "value": 2}], "createdAt": 1614798502849, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["WgtTaSFgA8LXY49KB"], "_id": "EZnNLwBwSffRwnsFF", "orgName": "Mariposa Academy - Indianapolis"}, "learning": {"momentType": "learning", "time": "1:55 pm", "date": "11/15/2019", "sortStamp": 1573844100000, "momentTypePretty": "Learning", "learningType": "", "learningCurriculumId": "2cw3YZCdJYGyFzTC3", "createdAt": 1573844182298, "createdBy": "3hmtpPctvvSgNbM7E", "createdByPersonId": "2papyMAsWKoebY55y", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["WgtTaSFgA8LXY49KB"], "_id": "zQaRrTDX3YJsfrsWb", "orgName": "Mariposa Academy"}, "comment": {"attributionPersonId": null, "momentType": "comment", "comment": "<PERSON> is having a great day!", "time": "9:57 am", "date": "7/29/2021", "sortStamp": 1627567020000, "momentTypePretty": "Comment", "createdAt": 1627567189225, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["WgtTaSFgA8LXY49KB"], "_id": "SD3vT2PCFisgH3AZY", "orgName": "Mariposa Academy - Indianapolis"}, "sleep": {"attributionPersonId": null, "metaMomentId": "MSKTWdR9QAoJwwx5A", "momentType": "sleep", "time": "10:07 am", "date": "7/29/2021", "sortStamp": *************, "sleepCheckInterval": 3, "momentTypePretty": "Sleep", "createdAt": 1627567663878, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["WgtTaSFgA8LXY49KB"], "_id": "eCriZRn6txQf2CkmJ", "orgName": "Mariposa Academy - Indianapolis"}, "checkin": {"attributionPersonId": null, "momentType": "checkin", "comment": "Checked into Ready-2-<PERSON><PERSON>", "time": "7:56 am", "date": "07/29/2021", "sortStamp": 1627559760000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["WgtTaSFgA8LXY49KB"], "_id": "gCvAy8f5CvRvvJ5B3"}, "checkout": {"attributionPersonId": null, "momentType": "checkout", "time": "5:00 pm", "date": "07/27/2021", "sortStamp": 1627419600000, "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "comment": "", "taggedPeople": ["WgtTaSFgA8LXY49KB"], "_id": "z58nQAgRerygb98jE"}, "supplies": {"momentType": "supplies", "comment": "Supplies on my desk", "time": "1:20 pm", "date": "2/12/2020", "sortStamp": 1581531600000, "momentTypePretty": "Supplies", "createdAt": 1581531829678, "createdBy": "aZt73ZSQt7u7QQZk6", "createdByPersonId": "cBzPJLq3Fpgfy79cn", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["9W9bZ5DJGF7LWaLmJ", "WgtTaSFgA8LXY49KB", "LjRaTQFhkYwbztoks", "R37M7tvesRJLcTskH"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/otLZo2DypCHCmTPvy", "mediaToken": "otLZo2DypCHCmTPvy", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/otLZo2DypCHCmTPvy"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/DN7KX8ug4tpq6zKYf", "mediaToken": "DN7KX8ug4tpq6zKYf", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/DN7KX8ug4tpq6zKYf"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/kQcSZbm6vLNZM4Nbn", "mediaToken": "kQcSZbm6vLNZM4Nbn", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/kQcSZbm6vLNZM4Nbn"}], "_id": "ykRjWLQDKa9z9kumX", "orgName": "Mariposa Academy"}, "ouch": {"momentType": "ouch", "time": "4:14 pm", "date": "2/9/2020", "sortStamp": 1581282840000, "momentTypePretty": "Ouch", "ouchContactedParent": "Phone", "ouchCalledParentTime": "4:14 pm", "ouchContactedDoctor": "Phone", "ouchCalledDoctorTime": "4:14 pm", "createdAt": 1581282890325, "createdBy": "v4x4cY4Tcv3NPGFH3", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["pigJgqoqwFctEEtne", "iFtT4kfWnzd8vHan5", "WgtTaSFgA8LXY49KB"], "_id": "tXRMXaeRuXTvy56KL", "orgName": "Mariposa Academy"}, "medical": {"attributionPersonId": null, "momentType": "medical", "time": "2:40 pm", "date": "8/20/2020", "sortStamp": 1597948800000, "momentTypePretty": "Medical", "medicalMedicationId": "xHi5YXvSoRWJ7BwAw", "medicalMedication": {"_id": "xHi5YXvSoRWJ7BwAw", "name": "Benadryl ", "dosage": "1 chewable tablet", "frequencyType": "other", "durationType": "ongoing", "notes": "As need", "frequencyDescription": "Other", "durationDescription": "Ongoing", "medicationDescription": "Benadryl  (1 chewable tablet - Other)"}, "createdAt": 1597948868941, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["WgtTaSFgA8LXY49KB"], "_id": "NKuX8kcqYgrgBwbK7", "orgName": "Mariposa Academy - Indianapolis"}, "safety": {"attributionPersonId": null, "momentType": "safety", "time": "3:18 pm", "date": "6/8/2021", "sortStamp": 1623179880000, "momentTypePretty": "Safety", "isDynamic": true, "momentDefinitionId": "Ksrw9M733c4nHq9pL", "dynamicFieldValues": {"safetyActivityTwo": "Fire Drill"}, "createdAt": 1623179908986, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n", "LjRaTQFhkYwbztoks", "WgtTaSFgA8LXY49KB"], "_id": "AmGioGAEpKzjqWCCo", "orgName": "Mariposa Academy - Indianapolis"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "mazBgAvhBPxRQHGv2", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627491502164, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "2RqwZYtxbT7T8o5JN"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "mazBgAvhBPxRQHGv2", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627491829064, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "Aq2PGNHDbNezKtMKx"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "yPbmG38ZRSzj55jt9", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492518187, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "5EPyRMEuSGuKy8HAx"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "fQhKqgFAgvqBjizYC", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492530535, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "gjMJLFRax3Evd9PiL"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "uTP7WeKis3DWWCAoK", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492543907, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "XaaPfRWAnfZArEWaK"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "WPYBpb2PABXWnmacw", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492556739, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "AZvLbhrMRRpHPfKgm"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "j3Dnd2DbS37vNc7Ft", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492579788, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "NHe4YMi9NdByzDbrj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "eoA5o9SXusDtjpY6y", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492591830, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "xsHhhScNRTbSqnuzZ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "ZvkAdjo9nh4xQbdcw", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492606349, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "N3f7soCuypdyfd2Mj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "TCucaBEkLN9hdAjgm", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492618892, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "AjhxwgWjgeK7nYbHe"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "r5WpyFDJw9FGFKGW4", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492633055, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "mnKWotPhzrcEESAQk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "Erpscse6ng9HkdgDe", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492682078, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "Ehuo8kGkHXWHW4o8t"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "EGKE4w56hrdeX9oco", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492694826, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "NbZLYpYPYaGjbXzp3"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "4DszD3ALs4mAjJLTm", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492706460, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "9AZnWKEt64TPQ9djF"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "GAeChcjjwxjKYpDQc", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492727780, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "dKmiNPQsWECpMSaar"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "sh3z3gXnNmu5TCNYg", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492736779, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "F5DxFyduMqDPniLTt"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkout", "momentId": "z58nQAgRerygb98jE", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492745571, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "4XfCssGe976XiptJN"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "587RL8p35d7ctYQsZ", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492922933, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "Mm39JCgN52DXiuRJa"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "EDATzQGjjXmNtTsC6", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627492938074, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "nXmEDLDnK9oZnD83H"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "QWrpNgazCe7B5AKQ6", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627493629234, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "C5tCHeP4E7fn5ecqS"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "3i8gDLLadkyeNSSEy", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627493675281, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "4dzMBtwsAyP3fRKS9"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "food", "momentId": "3i8gDLLadkyeNSSEy", "createdBy": "SYSTEM", "createdAt": 1627493679287, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "eRgyHL4H7PERcatwc", "_id": "kXexyEkiQpm2BSar7"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "Ei9iwARz3EgkJbrdM", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627493689020, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "cnMLbnh4Bn75T3pMq"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "potty", "momentId": "Ei9iwARz3EgkJbrdM", "createdBy": "SYSTEM", "createdAt": 1627493690353, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "eRgyHL4H7PERcatwc", "_id": "o4A7753aT9JzKJaCZ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560723935, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "Y2CaQBNRaJRma4MPK"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560759179, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "GFD4SZ2fpD4Y3dfzk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "qwwgzyRjPAEBXDBQz", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560836308, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "c3nYdKxgyDAqfQG83"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560926893, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "r3EcSZ7JBJDbXmbuR"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627560953854, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "iZfsyXQmEgk6YKdTA"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560964859, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "pKxQHgEK9M9szJyTs"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627561002639, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Lebn3LXMN5xv3fRzo"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627561023669, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "xqbHdew3ibccmvjxa"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561073715, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "yELxmFZznuXXz2x9y"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Ks2fyxKMyceKLW9D7", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561114501, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "JptDF2JusK4ErdZ3N"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561151719, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "eTjhPjXpfNqwTLtJt"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "SYSTEM", "createdAt": 1627561158197, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "eRgyHL4H7PERcatwc", "_id": "Mvviq6dDt93HpdKa2"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "gXPAdm3ebZiHZGZwR", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561200875, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "q7swzcSp3qfEujcwP"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "GkA8bk2yHPsvnz7dt", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561241222, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "JZmp2LXtQJxuJ53kZ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "czWazq2hpB4QkWsWn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561264563, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "LXJdxE6kADeud579C"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561304068, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "p2SEjRkSepK5ethyq"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561328009, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "PJJJJMf5zmHLEaAQZ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NhEMjjJaA87tBxfbv", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561363829, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "TTK3DLy7LTY934BBH"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561402598, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "HcMgqqpRDiYWeL58k"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "uAGjsoQiokpbJGZn8", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561430767, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "GqnfzZS5nbFoWx6Bg"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "9J6WH7kvJpsWv28gw", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561451336, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "LqS5k3vAstBpESGGk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "food", "momentId": "9J6WH7kvJpsWv28gw", "createdBy": "SYSTEM", "createdAt": 1627561456670, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "eRgyHL4H7PERcatwc", "_id": "RXZ8nQmAML3qkMpB8"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NsAdQkWc3azSa4Rvu", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561493790, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "fWuMf7YEzRyjv2CFA"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561523258, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "Mteq9zzMoGE3qrSff"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "27MLf4ccSxNHMoy9Z", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561557794, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "vrF4jhKRPTemiEsT3"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627561580031, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "s4ZDy7YWa8AFPHvyw"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561596385, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "xBnBtx2gLZiJiwaBj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561631483, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "BXgX9gNc38zDx98yx"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "rRuGo5tmTEX7f9hwT", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561675216, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "t8xisZ7QSdDuZFD99"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561705336, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "6e6ThhyDD4dQja2Lx"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "9J6WH7kvJpsWv28gw", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627561709482, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "JtCfCHt8Sw57n95rG"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CC2cC5Q9XAt2BeSQC", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561739554, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "hKXx8BHAyxZPtnXqe"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "checkin", "momentId": "gCvAy8f5CvRvvJ5B3", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627562539720, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "qgj3y5spEpiShXy3y"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "gXPAdm3ebZiHZGZwR", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566821227, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "e2z9LEhwkwrTkcQo5"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NsAdQkWc3azSa4Rvu", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566866778, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "S5Dkne7hE7zzdakpY"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566936300, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "z5s477LQyXEgbzJL3"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "qwwgzyRjPAEBXDBQz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566966382, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "PzcXABtd27gxE99j8"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567044553, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "M6rHifpEbZmdWuNM9"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567064262, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "txr6fgcZhi94dQysH"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CC2cC5Q9XAt2BeSQC", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567080736, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "vxE9p8KskdconZQGk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "uAGjsoQiokpbJGZn8", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567100958, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "dSwiKGXi34H2tSQAJ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567116423, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "6syrGorsr8ZLDPKTf"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567134259, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "CdPCdxjBTa42JaPyr"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567149345, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "g4CsBhieniz4mzrtN"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "rRuGo5tmTEX7f9hwT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567166678, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "ThpkTkmBJmzmtjaY3"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567186053, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "9w69NhkaEpbQMZNoP"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "SD3vT2PCFisgH3AZY", "createdBy": "N7Yynvhs9pqBEiR92", "createdAt": 1627567189276, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "N7Yynvhs9pqBEiR92", "_id": "WW35D756qhKxx4tkc"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "comment", "momentId": "SD3vT2PCFisgH3AZY", "createdBy": "SYSTEM", "createdAt": 1627567189842, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "eRgyHL4H7PERcatwc", "_id": "JNurTmL95zqZRbwu6"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567203071, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Hvrg7P7mEcLSGTwEx"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567218212, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Hxyx5Wa4ESjSv48vE"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567235215, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "FNpqbLLAtcKPijGCe"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567248825, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "DvyqihLssJX9d999D"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567265381, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "JKm9ofFrunPiSM3Di"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567293412, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "f5MNNYXixwgxrYTdB"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Zq3sT7fG6W7QH22dP", "createdBy": "N7Yynvhs9pqBEiR92", "createdAt": 1627567306061, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "N7Yynvhs9pqBEiR92", "_id": "cDJmRg7E2nKxD9Mo9"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "notification", "subType": "realtime_push_sent", "detail": "food", "momentId": "Zq3sT7fG6W7QH22dP", "createdBy": "SYSTEM", "createdAt": 1627567310619, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "eRgyHL4H7PERcatwc", "_id": "P6wdhd6v54LKsmQNt"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "27MLf4ccSxNHMoy9Z", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567310927, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "xTXRuXpDA4PHhbPfr"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "czWazq2hpB4QkWsWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567327926, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "P7qCo3f7WoDx5RH44"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567446097, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Lz3RF5yv4kGfsoi4L"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567455997, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "k3Jj4RaKuqGfNGrwy"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567466418, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "JagmbjbwitWi3rz7d"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567476348, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "N45JZMQqQLmkmChHJ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567482172, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "W3ke7CtrHn9GRphHu"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567489446, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "moCJxeWjdJ9T6wkPf"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567511503, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "BpyK5mAJmgSvfEb5M"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Zq3sT7fG6W7QH22dP", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567545720, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "bdRSReHz8W6SvbSTq"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567558263, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "kAcgf92MisePD6tq4"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567567765, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "b6WrSnupGAwAThCKM"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567578701, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Jy42C2crMmMxdCfRb"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567585102, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "x8RfHkWoxv7ikKpka"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567598850, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "fS7dD2Gw5k6vpRB9e"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567614761, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "MBC5iSskPAJEJyRBJ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567622759, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Xhn7o2TJnN6Rdiqvk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567631215, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "LdnZWxpFbe6TxBrS6"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "sleep", "momentId": "eCriZRn6txQf2CkmJ", "createdBy": "N7Yynvhs9pqBEiR92", "createdAt": 1627567663911, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "N7Yynvhs9pqBEiR92", "_id": "e5MuSXigxrjduT9gW"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567749851, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "pn2JhYPjdPHXJ4uLP"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567763207, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "gWYkz5WLYCRYFegKC"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567772239, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Pcujx9r7X4N2Cj7zr"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567778569, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "DTt2TisvLL8QxMzp7"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567798119, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "N8qtNACfh5z7LXRFu"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567804698, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "bFwMaGtdA7zxaCua8"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567809297, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "uAng8YdF6JdPMR2hE"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567819423, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "taYLgBrxo295pvXeX"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567826529, "targetPersonId": "WgtTaSFgA8LXY49KB", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "3vabWnK8Z4Az5Rvv2"}], "checkedInById": "8jgMSvrFYadoNgRxY", "cacfpSubsidy": "Paid", "immunizationEntries": [], "medicationEntries": [{"_id": "xHi5YXvSoRWJ7BwAw", "name": "Benadryl ", "dosage": "1 chewable tablet", "frequencyType": "other", "durationType": "ongoing", "notes": "As need"}], "allergies": "Peanuts", "enrollmentDate": 1542949200000, "classList": {"defaultDays": {"M": "1", "T": "1", "W": "1", "R": "1", "F": "1"}, "transitionDays": {"M": 0, "T": 0, "W": 0, "R": 0, "F": 0}}, "transitionGroupDate": null, "transitionGroupId": "", "inActive": false, "previousClassroomGroupId": "zvWiPSxhkgGBvq7A6"}, {"_id": "R37M7tvesRJLcTskH", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>is", "defaultGroupId": "ATkKXtzby5gEZ6sWq", "type": "person", "createdBy": "s3xrYq3pkm5Q7adkW", "orgId": "nTvbx24M2dbM9w6tu", "avatarToken": "ieQiK5uNICeg6TBDneOU", "avatarPath": "nTvbx24M2dbM9w6tu/s3xrYq3pkm5Q7adkW/ieQiK5uNICeg6TBDneOU", "checkedIn": false, "checkedInOutTime": 1684427304072, "checkInGroupId": null, "checkInGroupName": null, "checkedInById": "69BREkBxEe8an4TbA", "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "edcEdRgPc7GDAuKQF", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1648727224456, "targetPersonId": "R37M7tvesRJLcTskH", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "v4nmukMgGmDtTKae3"}], "lastInteractionDate": 1648727160000, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "Test", "time": "7:46 am", "date": "3/31/2022", "sortStamp": 1648727160000, "momentTypePretty": "Comment", "createdAt": 1648727224369, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "edcEdRgPc7GDAuKQF", "orgName": "321-Mariposa Local"}, "lastMomentByType": {"comment": {"attributionPersonId": null, "momentType": "comment", "comment": "Test", "time": "7:46 am", "date": "3/31/2022", "sortStamp": 1648727160000, "momentTypePretty": "Comment", "createdAt": 1648727224369, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "edcEdRgPc7GDAuKQF", "orgName": "321-Mariposa Local"}, "learning": {"momentType": "learning", "time": "1:38 pm", "date": "11/26/2019", "sortStamp": 1574793480000, "momentTypePretty": "Learning", "learningType": "", "learningCurriculumId": "", "comment": "", "taggedPeople": ["R37M7tvesRJLcTskH", "MdKPhsKB4btnEZt6n", "R6fKP3hHEBafxeyuq", "Qhirjw7H7Lr9PccCK"], "_id": "5QmF5HMqhumDMLMsc"}, "sleep": {"attributionPersonId": null, "metaMomentId": "jDD378YEhKCngM8Dm", "momentType": "sleep", "time": "9:00 am", "date": "7/16/2021", "sortStamp": 1626440400000, "sleepCheckInterval": 3, "momentTypePretty": "Sleep", "createdAt": 1626440431808, "createdBy": "tpm2GGoo5T58WRXue", "attributionName": "<PERSON>", "createdByPersonId": "7A4gspSfjvrGd2bSQ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "XCLJSqhdFTrFoMRdf", "orgName": "Mariposa Academy - Indianapolis"}, "food": {"attributionPersonId": null, "momentType": "food", "comment": "Fruit and yogurt", "time": "8:18 am", "date": "07/29/2021", "sortStamp": 1627561080000, "foodType": "Breakfast", "foodAmount": "", "momentTypePretty": "Food", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "taggedPeople": ["hCL4wD9HqAbtttGzF", "R37M7tvesRJLcTskH", "vJHZ8P3KWxwRMak7a", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "bwmKASWnBTLrmSkoS", "QcNdA3bpdZXmRWujH", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "vafkMjcEs8qLZyfTr", "R6fKP3hHEBafxeyuq", "Qhirjw7H7Lr9PccCK", "eQNegjBtSNijtsheD", "qTLvGki7YvdTQWp8Q", "5KMccvT6pukro9Ty5", "QxTFrRwFCXF3pfaao", "pCHFhtbNwq7dG4eHG", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "MdKPhsKB4btnEZt6n", "LjRaTQFhkYwbztoks", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "WgtTaSFgA8LXY49KB", "HMKtg6wfCM7b2bn8E"], "_id": "doLLNyz83JGDgdcgZ"}, "potty": {"attributionPersonId": null, "momentType": "potty", "comment": "test", "time": "6:25 am", "date": "3/4/2022", "sortStamp": 1646393100000, "momentTypePretty": "<PERSON><PERSON>", "createdAt": 1646397594273, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/ZdkhmaEScuuboPFbH/kNpjWs8NszSy9pgdC", "mediaToken": "kNpjWs8NszSy9pgdC", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/ZdkhmaEScuuboPFbH/kNpjWs8NszSy9pgdC"}], "_id": "gdEyWygogumugoFXf", "orgName": "Mariposa Local"}, "supplies": {"momentType": "supplies", "time": "3:17 am", "date": "2/18/2020", "sortStamp": 1582013820000, "supplyType": "Diapers", "momentTypePretty": "Supplies", "createdAt": 1582013879388, "createdBy": "aZt73ZSQt7u7QQZk6", "createdByPersonId": "cBzPJLq3Fpgfy79cn", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "x6opodnqB8Qtn6d3F", "orgName": "Mariposa Academy"}, "ouch": {"momentType": "ouch", "time": "4:15 pm", "date": "2/9/2020", "sortStamp": 1581282900000, "momentTypePretty": "Ouch", "ouchContactedParent": "In Person", "ouchCalledParentTime": "4:15 pm", "ouchContactedDoctor": "In Person", "ouchCalledDoctorTime": "4:15 pm", "ouchNurseNotified": true, "ouchProfessionalMedication": true, "createdAt": 1581282936323, "createdBy": "v4x4cY4Tcv3NPGFH3", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["Qhirjw7H7Lr9PccCK", "9W9bZ5DJGF7LWaLmJ", "LjRaTQFhkYwbztoks", "R37M7tvesRJLcTskH"], "_id": "THSBXMhDPTxjNun3e", "orgName": "Mariposa Academy"}, "activity": {"momentType": "activity", "time": "10:26 am", "date": "12/02/2019", "sortStamp": 1575300360000, "activityType": "Gross Motor", "activityEngagement": "Active", "momentTypePretty": "Activity", "createdAt": 1575298941972, "createdBy": "dj7khLR8yA5JtEeYW", "createdByPersonId": "vyxDKuFigFGRttBZS", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH", "MdKPhsKB4btnEZt6n", "vJHZ8P3KWxwRMak7a", "R6fKP3hHEBafxeyuq", "WtNhYoB3zDXNwSbMD", "JXMHjAYzsBx22h5CS", "Qhirjw7H7Lr9PccCK", "pK6oxEhjkwXyqrRmY"], "mediaFiles": [{"mediaUrl": null, "mediaToken": "LPJPtZrpeyqTMTLPfze8", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/dj7khLR8yA5JtEeYW/LPJPtZrpeyqTMTLPfze8"}], "_id": "jRXFhRLJjTutXEHvQ", "orgName": "Mariposa Academy"}, "checkin": {"attributionPersonId": null, "momentType": "checkin", "comment": "Checked into Mighty Explorers Pre-K", "time": "8:26 pm", "date": "07/28/2021", "sortStamp": 1627518360000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "cFAFDZp8TbXM3dkeq"}, "behaviorChild": {"momentType": "<PERSON><PERSON><PERSON><PERSON>", "time": "4:05 pm", "date": "1/18/2020", "sortStamp": 1579381500000, "momentTypePretty": "Behavior", "isDynamic": true, "momentDefinitionId": "vkqzWHFHdKqvP86TE", "dynamicFieldValues": {"followedDirections": "mostoftheTime"}, "createdAt": 1579381579302, "createdBy": "v4x4cY4Tcv3NPGFH3", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks", "R37M7tvesRJLcTskH", "R6fKP3hHEBafxeyuq", "Qhirjw7H7Lr9PccCK", "MdKPhsKB4btnEZt6n"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/fPjxoi7uKvBGJd7zF", "mediaToken": "fPjxoi7uKvBGJd7zF", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/fPjxoi7uKvBGJd7zF"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/5hy7FsgAbBSCFXzp6", "mediaToken": "5hy7FsgAbBSCFXzp6", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/5hy7FsgAbBSCFXzp6"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/cKjkQYjiuJtbpesWv", "mediaToken": "cKjkQYjiuJtbpesWv", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/cKjkQYjiuJtbpesWv"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/x5uXiYTibK4skv24D", "mediaToken": "x5uXiYTibK4skv24D", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/x5uXiYTibK4skv24D"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/WRo5mTKuADtT5oMnu", "mediaToken": "WRo5mTKuADtT5oMnu", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/WRo5mTKuADtT5oMnu"}], "_id": "RoRWBo4oYeRMpXjAc", "orgName": "Mariposa Academy"}, "illness": {"momentType": "illness", "time": "10:17 am", "date": "11/12/2019", "sortStamp": 1573571820000, "momentTypePretty": "Illness", "illnessSymptoms": ["Fever (101 degrees or higher)"], "createdAt": 1573579086203, "createdBy": "fPsbbX4pjfL7SinqK", "createdByPersonId": "8zqrq2CEZfdEioAxR", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "vPhSs4a5ySMnWoxcY", "orgName": "Mariposa Academy"}, "incident": {"attributionPersonId": null, "momentType": "incident", "comment": "<PERSON><PERSON> fell and hurt her knee, there is a small scratch that we put a band-aid on and she is feeling better ", "time": "9:40 am", "date": "12/29/2020", "sortStamp": 1609252800000, "momentTypePretty": "Incident", "attributionName": "<PERSON>", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "YsmMyggYLaHqbzZdB"}, "alert": {"attributionPersonId": null, "momentType": "alert", "comment": "Reminder: We will be closed on Monday, July 19 for a teacher in-service day.", "time": "5:39 pm", "date": "07/16/2021", "sortStamp": 1626471540000, "momentTypePretty": "Notification", "alertSendTypeEmail": false, "alertSendTypeText": false, "alertSendTypePush": true, "alertShowCheckin": false, "createdAt": 1626471553840, "createdBy": "3FhP46Kjmiro3bL7G", "attributionName": "<PERSON>", "createdByPersonId": "8uAYxXbT6urWxXg3k", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["vyxDKuFigFGRttBZS", "RYetWpMJhsC3BcPjE", "69BREkBxEe8an4TbA", "9W9bZ5DJGF7LWaLmJ", "N7Yynvhs9pqBEiR92", "BgEJcyPDDPPGT97w5", "y6Lp9DaDAKZP6WZDr", "Ao67WMf2Gi3GfW8Ro", "pCHFhtbNwq7dG4eHG", "vafkMjcEs8qLZyfTr", "hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "N5yn3DbpjCMdGgAJR", "J8uD8wR7i9zycX3aR", "R37M7tvesRJLcTskH", "TKw3K4856H8Et7xNx", "4pPTmaevgLcoWExz2", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "MdKPhsKB4btnEZt6n", "JXMHjAYzsBx22h5CS", "Qhirjw7H7Lr9PccCK", "R6fKP3hHEBafxeyuq", "BaDDgKr4brvSJomZZ", "vJHZ8P3KWxwRMak7a", "LjRaTQFhkYwbztoks", "2papyMAsWKoebY55y", "GsXkcQ9izfhpNFoac", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "bwmKASWnBTLrmSkoS", "9zXMi5TWSNn2t3rYR", "veLKDqRsKQibwwtDj", "vyyGJsucZimKjkxJa", "wd39xoosTnzjsGb6c", "8uAYxXbT6urWxXg3k", "WgtTaSFgA8LXY49KB", "MZLqugrBbXT6b93aF", "Ko2dpdjhLtwTP6HHs", "fYEgtG9KzZakDD8Lq", "eQNegjBtSNijtsheD", "9ovZqAWdGKjaRgRu7", "XKs6nQpNv7q9RYEvK", "HMKtg6wfCM7b2bn8E", "xYhHHnxhiTXFTxscg", "XHjxC6SJmt34NMGzp", "Fhrqwcq9Yw8S7FWxE", "iT5PLBudyY9FKurF7", "pyYSwFc2PJjHJKrKW", "GkqcNHheuvn8SBXvn", "MyFepQyWoycBRmBCS", "LFuMMLpNGqaNR4v78", "7A4gspSfjvrGd2bSQ", "8jgMSvrFYadoNgRxY", "yT9Zeaj2EzH85MQoj", "ZoS2Y8wwSBNwrJkRs", "gHoZrjN9f6LHzEegS", "FfzHpuRYnX5nCayfS", "ALszCkiMER4Yzujmz", "EYCwPvkG5Zd23hsSZ", "nmQ5Lmv6S6aX8XhxC", "gijNLLWJbjzikeMWz", "qTLvGki7YvdTQWp8Q", "2Sd4utZGjpDQeut64", "wSTzopnnwsbYWwsri", "bgYPrFT6jnRbzwpZz", "xdPGHYnY4LDTW5uaK", "jcRkB6PBCFNRS9asn", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "ch2ZhHu3h7w35zSWB", "LjANQKbvYfGtThZs5", "q4cwWG8DpsDjmmnL6", "LBDaRNzLqg9NRGXW6", "5KMccvT6pukro9Ty5", "rAaxA8yPKFYDjrzJk", "CofTFp5PRggvz477E", "QcNdA3bpdZXmRWujH", "C89A4qgWiHB67qGR2", "DkDL5e5jELZBbv6PT", "ddYRmKZd2n79NiakF", "gGyQF3jYMbF7bp2cF", "TTbkpM3mxsnNEzeN6", "6aguBjjQNCMXPnse8", "J5wzhMne96f5uFxMY", "iXgWLbg5GDbdTB2Bw", "D5R4qkBKZkKejEKDB", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "XyD2Hw6RfqegfeMtY", "iT9ZbCgL9ZbPwzyBS", "GHhRHxXwKkAQPSTRH", "gvPNP3DyDif4opnia", "7rjCurgW62RebGjPY", "pyNcuF7Jm7GNehmPz", "u5s56Y8CswdDjh2GP", "f2kLwWFwL4mfWpJCK", "tyXnH6RLpnqNcHf6Y", "QxTFrRwFCXF3pfaao", "ACS7zGZzLsPv65y39", "PKNsmS5hNRZFvtNgp", "27WKMANJrmX6JwMXq", "HE5MvfRwX4wqrX3Tv"], "_id": "D5MDbP8P8XhjujeH7", "orgName": "Mariposa Academy - Indianapolis"}, "mood": {"momentType": "mood", "time": "2:12 pm", "date": "5/10/2019", "sortStamp": 1557511920000, "moodLevel": "SoSo", "momentTypePretty": "<PERSON><PERSON>", "createdAt": 1557511966846, "createdBy": "djB2aHFjG3KjJbazZ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "cJMhwD4MCcge4Hce9", "orgName": "Premier"}, "checkout": {"attributionPersonId": null, "momentType": "checkout", "comment": "Checked out of Mighty Explorers Pre-K", "time": "5:26 pm", "date": "07/28/2021", "sortStamp": 1627507560000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "znyYrF5WAAGBTBy3M"}, "portfolio": {"momentType": "portfolio", "time": "3:38 pm", "date": "2/18/2020", "sortStamp": 1582058280000, "momentTypePretty": "Portfolio", "portfolioCurriculumId": "oWCXwptBxGqSSHmjt", "portfolioCurriculum": {"_id": "oWCXwptBxGqSSHmjt", "headline": "Coloring Red Apples", "message": "We will color red apples", "scheduledDate": 1582002000000, "selectedGroups": ["3J2GoZsZb6DkSC9D5"], "selectedStandards": ["Lightbridge|db514c4057acc8e4a7b1b845fc93457591dd263f", "Lightbridge|d5c8b6e9efdff2685234684979670bfe243befd6"], "selectedTypes": ["<PERSON><PERSON>: Creating with Art", "Toddler: Exploring Sand and Water"], "selectedAgeGroup": "18 months - 27 months", "curriculumThemeId": "W8uqK3enyTJHogr4D", "originalCurriculumId": "PCwcPzgYSstGqmmF2", "orgId": "nTvbx24M2dbM9w6tu", "createdBy": "KMeF6L7rN33H8mpYS", "createdAt": 1582058050257, "mediaFiles": []}, "createdAt": 1582058310874, "createdBy": "3hmtpPctvvSgNbM7E", "createdByPersonId": "2papyMAsWKoebY55y", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH", "2papyMAsWKoebY55y"], "_id": "raLLASGzpigQypqPH", "orgName": "Mariposa Academy"}, "medical": {"momentType": "medical", "time": "9:32 pm", "date": "2/17/2020", "sortStamp": 1581993120000, "medicalAdministeredBy": "BgEJcyPDDPPGT97w5", "medicalAdministeredByPerson": {"fullName": "<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "momentTypePretty": "Medical", "medicalMedicationId": "Lh3nk4ZZt986YNxBX", "medicalMedication": {"_id": "Lh3nk4ZZt986YNxBX", "name": "Children's Claritan", "dosage": "1 tsp", "frequencyType": "everyhours", "durationType": "ongoing", "notes": "As needed", "frequencyAmount": 4, "frequencyDescription": "Every 4 hours", "durationDescription": "Ongoing", "medicationDescription": "Children's Claritan (1 tsp - Every 4 hours)"}, "createdAt": 1581993159487, "createdBy": "v4x4cY4Tcv3NPGFH3", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["R37M7tvesRJLcTskH"], "_id": "9YvztJjfBKoWpzdmD", "orgName": "Mariposa Academy"}}, "billing": {"enrolledPlans": [{"_id": "DzmykiRh7vrpw3iyJ", "planDetails": {"_id": "DzmykiRh7vrpw3iyJ", "description": "Infants Full Time ", "amount": 1000, "type": "plan", "frequency": "monthly", "category": "tuition", "suspendUntil": null}, "enrollmentDate": 1614661200000, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "dollars", "discountType": "customerSpecific", "allocationDescription": "Discount: Customer-Specific", "discountExpires": 1619755200000, "id": "ydGD5Lpfn5AcGf8bp"}, {"allocationType": "reimbursable", "amount": 500, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "LEJegegjb425ES9B3"}], "createdAt": 1614704174182, "enrollmentForecastStartDate": 1614574800000, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": 1620417527968, "updatedBy": "3FhP46Kjmiro3bL7G"}], "pendingCharges": [], "lastInvoiced": 1691685868831, "invoiced": 0, "lastBalanceNoticeSentAt": 1569433456206, "familySplits": {"kkGCnfhZQ2o67NS8S": 40, "eRgyHL4H7PERcatwc": 60}}, "lastInformedArrival": {"source": "familyCheckin", "createdAt": 1625248276038, "checkedInById": "kkGCnfhZQ2o67NS8S", "fieldValues": {"attending": "Yes", "temperatureChild": "98.6", "temperatureParent": "99", "temperatureHigher": "No", "feverReducingMedication": "No", "experiencingTwoSymptoms": "No", "experiencingOneSymptom": "No", "closeContact": "No", "householdExperiencingSymptoms": "No", "overnightSleep": "7:50 am", "lastPotty": "8:34 am", "lastFood": "8:42 am", "napsToday": "None", "changeInCare": "None ", "guardianReachedToday": "1234567890", "medicationsToday": "None"}, "absent": false}, "sex": "Female", "birthday": 1521604800000, "siblings": "<PERSON>; <PERSON>", "physicianName": "<PERSON> ", "physicianPhone": "************", "annualPhysicalDate": "12/18/2019", "notes": "Has an aunt with down syndrom, and she may stop in for visits now and then", "nickname": "<PERSON>'s nickname is EVI<PERSON>", "medicationEntries": [{"_id": "qG6C94mHNcxpWkpW6", "name": "Eczema Relief Cream", "dosage": "Pea sized dot of cream", "frequencyType": "other", "durationType": "ongoing", "notes": "As Needed"}, {"_id": "Lh3nk4ZZt986YNxBX", "name": "Children's Claritan", "dosage": "1 tsp", "frequencyType": "everyhours", "durationType": "ongoing", "notes": "As needed", "frequencyAmount": 4}], "immunizationEntries": [{"type": "DTaP", "date": 1463976000000}, {"type": "DTaP", "date": 1469073600000}, {"type": "DTaP", "date": 1474603200000}, {"type": "DTaP", "date": 1498017600000}, {"type": "DTaP", "date": 1458532800000}, {"type": "RV", "date": 1463976000000}, {"type": "RV", "date": 1469073600000}, {"type": "HepB", "date": 1458532800000}, {"type": "HepB", "date": 1490068800000}, {"type": "Hib", "date": 1463889600000}, {"type": "Hib", "date": 1490068800000}, {"type": "PCV13", "date": 1463889600000}, {"type": "PCV13", "date": 1469073600000}, {"type": "PCV13", "date": 1474430400000}, {"type": "PCV13", "date": 1490068800000}, {"type": "IPV", "date": 1463976000000}, {"type": "IPV", "date": 1469073600000}, {"type": "IPV", "date": 1490068800000}, {"type": "IIV", "date": 1490068800000}, {"type": "IIV", "date": 1521604800000}, {"type": "MMR", "date": 1490068800000}, {"type": "VAR", "date": 1490068800000}, {"type": "HepA", "date": 1498017600000}, {"type": "HepA", "date": 1516597200000}, {"type": "HepB", "date": 1461211200000}, {"type": "Hib", "date": 1461211200000}, {"_id": "KMPGppui3vLcKQTwh", "type": "H1N1", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1588697590110, "date": 1572843600000}, {"_id": "3ituG5yGvShfYz4Mc", "type": "H1N1", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1588697590110, "date": 1583211600000}, {"_id": "XSg8p9t83DPP8RTCN", "type": "H1N1", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1588697590110, "date": 1586750400000}, {"_id": "idwLvyXXWTSBu58km", "type": "RV", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804318685, "date": 1474430400000}, {"_id": "CG9ResS549fZB6AxT", "type": "Hib", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804334621, "date": 1498017600000}], "foodSubsidy": "Reduced", "cacfpSubsidy": "Reduced", "documentItems": {"G7rXjQsvoS7N4NcyK": {"createdAt": 1582141596949, "createdByPersonId": "eRgyHL4H7PERcatwc", "createdByUserId": "5EKMGhzhGcjY3R8za", "token": "VnQvACvpVGIbrvvyVmfq", "repositoryKey": "nTvbx24M2dbM9w6tu/5EKMGhzhGcjY3R8za/VnQvACvpVGIbrvvyVmfq", "approvedAt": 1582141662604, "approvedByPersonId": "9W9bZ5DJGF7LWaLmJ", "approvedByUserId": "djB2aHFjG3KjJbazZ"}, "oye8RaitYXajgc5e4": {"templateOptionResult": {"action": "ack", "date": 1623177248210, "personId": "kkGCnfhZQ2o67NS8S", "personName": "<PERSON>is"}}}, "classList": {"defaultDays": {"M": "1", "T": "1", "W": "1", "R": "1", "F": "1"}, "transitionDays": {"M": "0", "T": "0", "W": "1", "R": "0", "F": "0"}}, "transitionGroupDate": null, "transitionGroupId": "AeNhNMr5RBEN2omSo", "enrollmentDate": 1588564800000, "primaryFamily": "kkGCnfhZQ2o67NS8S", "payer": "", "billingNotes": {"Billing Notes": "None"}, "inActive": false, "allergies": "", "backUpCare": "", "backUpCareReferringPartner": "", "curriculum": "", "notesPrivate": "", "quickList": "", "previousClassroomGroupId": "ATkKXtzby5gEZ6sWq", "presenceLastGroupId": null, "timestamps": {"moveMomentTimestamp": {"momentId": "fNQ3NhQjineo7GpG9", "timestamp": 1684424177945, "groupId": "ATkKXtzby5gEZ6sWq", "oldGroupId": null}}}, {"_id": "PPerx9LKNChSDaHSw", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "defaultGroupId": "zvWiPSxhkgGBvq7A6", "type": "person", "createdBy": "fPsbbX4pjfL7SinqK", "orgId": "nTvbx24M2dbM9w6tu", "lastInteractionDate": *************, "lastMoment": {"attributionPersonId": null, "metaMomentId": "MSKTWdR9QAoJwwx5A", "momentType": "sleep", "time": "10:07 am", "date": "7/29/2021", "sortStamp": *************, "sleepCheckInterval": 3, "momentTypePretty": "Sleep", "createdAt": 1627567663755, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "_id": "Hh4r9fTYXA94P24M3", "orgName": "Mariposa Academy - Indianapolis"}, "lastMomentByType": {"alert": {"attributionPersonId": null, "momentType": "alert", "comment": "Reminder: We will be closed on Monday, July 19 for a teacher in-service day.", "time": "5:39 pm", "date": "07/16/2021", "sortStamp": 1626471540000, "momentTypePretty": "Notification", "alertSendTypeEmail": false, "alertSendTypeText": false, "alertSendTypePush": true, "alertShowCheckin": false, "createdAt": 1626471553840, "createdBy": "3FhP46Kjmiro3bL7G", "attributionName": "<PERSON>", "createdByPersonId": "8uAYxXbT6urWxXg3k", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["vyxDKuFigFGRttBZS", "RYetWpMJhsC3BcPjE", "69BREkBxEe8an4TbA", "9W9bZ5DJGF7LWaLmJ", "N7Yynvhs9pqBEiR92", "BgEJcyPDDPPGT97w5", "y6Lp9DaDAKZP6WZDr", "Ao67WMf2Gi3GfW8Ro", "pCHFhtbNwq7dG4eHG", "vafkMjcEs8qLZyfTr", "hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "N5yn3DbpjCMdGgAJR", "J8uD8wR7i9zycX3aR", "R37M7tvesRJLcTskH", "TKw3K4856H8Et7xNx", "4pPTmaevgLcoWExz2", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "MdKPhsKB4btnEZt6n", "JXMHjAYzsBx22h5CS", "Qhirjw7H7Lr9PccCK", "R6fKP3hHEBafxeyuq", "BaDDgKr4brvSJomZZ", "vJHZ8P3KWxwRMak7a", "LjRaTQFhkYwbztoks", "2papyMAsWKoebY55y", "GsXkcQ9izfhpNFoac", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "bwmKASWnBTLrmSkoS", "9zXMi5TWSNn2t3rYR", "veLKDqRsKQibwwtDj", "vyyGJsucZimKjkxJa", "wd39xoosTnzjsGb6c", "8uAYxXbT6urWxXg3k", "WgtTaSFgA8LXY49KB", "MZLqugrBbXT6b93aF", "Ko2dpdjhLtwTP6HHs", "fYEgtG9KzZakDD8Lq", "eQNegjBtSNijtsheD", "9ovZqAWdGKjaRgRu7", "XKs6nQpNv7q9RYEvK", "HMKtg6wfCM7b2bn8E", "xYhHHnxhiTXFTxscg", "XHjxC6SJmt34NMGzp", "Fhrqwcq9Yw8S7FWxE", "iT5PLBudyY9FKurF7", "pyYSwFc2PJjHJKrKW", "GkqcNHheuvn8SBXvn", "MyFepQyWoycBRmBCS", "LFuMMLpNGqaNR4v78", "7A4gspSfjvrGd2bSQ", "8jgMSvrFYadoNgRxY", "yT9Zeaj2EzH85MQoj", "ZoS2Y8wwSBNwrJkRs", "gHoZrjN9f6LHzEegS", "FfzHpuRYnX5nCayfS", "ALszCkiMER4Yzujmz", "EYCwPvkG5Zd23hsSZ", "nmQ5Lmv6S6aX8XhxC", "gijNLLWJbjzikeMWz", "qTLvGki7YvdTQWp8Q", "2Sd4utZGjpDQeut64", "wSTzopnnwsbYWwsri", "bgYPrFT6jnRbzwpZz", "xdPGHYnY4LDTW5uaK", "jcRkB6PBCFNRS9asn", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "ch2ZhHu3h7w35zSWB", "LjANQKbvYfGtThZs5", "q4cwWG8DpsDjmmnL6", "LBDaRNzLqg9NRGXW6", "5KMccvT6pukro9Ty5", "rAaxA8yPKFYDjrzJk", "CofTFp5PRggvz477E", "QcNdA3bpdZXmRWujH", "C89A4qgWiHB67qGR2", "DkDL5e5jELZBbv6PT", "ddYRmKZd2n79NiakF", "gGyQF3jYMbF7bp2cF", "TTbkpM3mxsnNEzeN6", "6aguBjjQNCMXPnse8", "J5wzhMne96f5uFxMY", "iXgWLbg5GDbdTB2Bw", "D5R4qkBKZkKejEKDB", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "XyD2Hw6RfqegfeMtY", "iT9ZbCgL9ZbPwzyBS", "GHhRHxXwKkAQPSTRH", "gvPNP3DyDif4opnia", "7rjCurgW62RebGjPY", "pyNcuF7Jm7GNehmPz", "u5s56Y8CswdDjh2GP", "f2kLwWFwL4mfWpJCK", "tyXnH6RLpnqNcHf6Y", "QxTFrRwFCXF3pfaao", "ACS7zGZzLsPv65y39", "PKNsmS5hNRZFvtNgp", "27WKMANJrmX6JwMXq", "HE5MvfRwX4wqrX3Tv"], "_id": "D5MDbP8P8XhjujeH7", "orgName": "Mariposa Academy - Indianapolis"}, "food": {"attributionPersonId": null, "momentType": "food", "time": "10:00 am", "date": "07/29/2021", "sortStamp": 1627567200000, "foodType": "AM Snack", "foodItems": [{"name": "Cereal", "amount": "All"}, {"name": "Milk", "amount": "Most"}], "momentTypePretty": "Food", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "comment": "", "taggedPeople": ["pCHFhtbNwq7dG4eHG", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "MdKPhsKB4btnEZt6n", "LjRaTQFhkYwbztoks", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "WgtTaSFgA8LXY49KB", "HMKtg6wfCM7b2bn8E"], "_id": "Zq3sT7fG6W7QH22dP"}, "comment": {"attributionPersonId": null, "momentType": "comment", "comment": "We had a great morning!", "time": "1:15 pm", "date": "07/23/2021", "sortStamp": 1627060500000, "momentTypePretty": "Comment", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["PPerx9LKNChSDaHSw", "nPdfN7LkoBQmcoujJ", "iFtT4kfWnzd8vHan5", "veLKDqRsKQibwwtDj"], "_id": "Kfam32uYR3AsZayvG"}, "potty": {"attributionPersonId": null, "momentType": "potty", "time": "12:21 pm", "date": "3/23/2021", "sortStamp": 1616516460000, "pottyType": "Wet", "momentTypePretty": "<PERSON><PERSON>", "createdAt": 1616516583051, "createdBy": "3hmtpPctvvSgNbM7E", "attributionName": "<PERSON>", "createdByPersonId": "2papyMAsWKoebY55y", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "_id": "A2z76MgNzog9w5EyE", "orgName": "Mariposa Academy - Indianapolis"}, "illness": {"momentType": "illness", "time": "5:07 pm", "date": "7/19/2019", "sortStamp": 1563570420000, "momentTypePretty": "Illness", "illnessSymptoms": ["Head Lice (children with lice will be referred for treatment)"], "createdAt": 1563581278134, "createdBy": "v4x4cY4Tcv3NPGFH3", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "_id": "YMsMXL2sg9TAsC5iX", "orgName": "Premier"}, "learning": {"attributionPersonId": null, "momentType": "learning", "comment": "Sitting up!", "time": "11:25 am", "date": "03/23/2021", "sortStamp": 1616513100000, "momentTypePretty": "Learning", "learningType": "", "learningCurriculumId": "", "createdAt": 1616513158452, "createdBy": "v4x4cY4Tcv3NPGFH3", "attributionName": "<PERSON><PERSON><PERSON>", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "mediaFiles": [{"mediaUrl": null, "mediaToken": "5arlFfAoCDIb18FO8ysb", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/5arlFfAoCDIb18FO8ysb", "fileName": "IMG_8189.JPG"}], "_id": "NjLRbtQCxZZv9uSJd", "orgName": "Mariposa Academy - Indianapolis"}, "sleep": {"attributionPersonId": null, "metaMomentId": "MSKTWdR9QAoJwwx5A", "momentType": "sleep", "time": "10:07 am", "date": "7/29/2021", "sortStamp": *************, "sleepCheckInterval": 3, "momentTypePretty": "Sleep", "createdAt": 1627567663755, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "_id": "Hh4r9fTYXA94P24M3", "orgName": "Mariposa Academy - Indianapolis"}, "activity": {"momentType": "activity", "time": "9:47 am", "date": "09/06/2019", "sortStamp": 1567777620000, "activityType": "", "activityEngagement": "", "momentTypePretty": "Activity", "createdAt": 1567777702103, "createdBy": "dj7khLR8yA5JtEeYW", "createdByPersonId": "vyxDKuFigFGRttBZS", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "mediaFiles": [{"mediaUrl": null, "mediaToken": "Pq8oAZ5VmuVnR8rhFTpv", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/dj7khLR8yA5JtEeYW/Pq8oAZ5VmuVnR8rhFTpv"}], "_id": "GsY55TgnXzF2gR3iM", "orgName": "Wonderland Montessori Academy"}, "move": {"momentType": "move", "comment": "Moved from Toddler Community 2 to Brown Bear", "time": "10:39 am", "date": "9/27/2019", "sortStamp": 1569595140000, "taggedPeople": ["PPerx9LKNChSDaHSw"], "_id": "hd6fYc3kbzmdBCByb"}, "medical": {"momentType": "medical", "time": "12:35 pm", "date": "12/20/2019", "sortStamp": 1576863300000, "medicalAdministeredBy": "vyxDKuFigFGRttBZS", "medicalAdministeredByPerson": {"fullName": "<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON>"}, "momentTypePretty": "Medical", "medicalMedicationId": "JmZ6RN4kkDY5C9p3q", "medicalMedication": {"_id": "JmZ6RN4kkDY5C9p3q", "name": "Tylenol", "dosage": "5ml", "frequencyType": "other", "durationType": "ongoing", "notes": "as needed", "frequencyDescription": "Other", "durationDescription": "Ongoing", "medicationDescription": "Tylenol (5ml - Other)"}, "createdAt": 1576863369701, "createdBy": "v4x4cY4Tcv3NPGFH3", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "_id": "SCBeX9BFWpPWhdCmS", "orgName": "Mariposa Academy"}, "portfolio": {"attributionPersonId": null, "momentType": "portfolio", "time": "3:31 pm", "date": "03/10/2021", "sortStamp": 1615408260000, "momentTypePretty": "Portfolio", "portfolioAssessments": null, "createdAt": 1615408293870, "createdBy": "v4x4cY4Tcv3NPGFH3", "attributionName": "<PERSON><PERSON><PERSON>", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "_id": "GzH96LeXX2wncDFP6", "orgName": "Mariposa Academy - Indianapolis"}, "mood": {"attributionPersonId": null, "momentType": "mood", "time": "11:25 am", "date": "03/23/2021", "sortStamp": 1616513100000, "moodLevel": "Happy", "momentTypePretty": "<PERSON><PERSON>", "createdAt": 1616513115961, "createdBy": "v4x4cY4Tcv3NPGFH3", "attributionName": "<PERSON><PERSON><PERSON>", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["PPerx9LKNChSDaHSw"], "_id": "wmYdLRvJBbctcAqLd", "orgName": "Mariposa Academy - Indianapolis"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "AeS7btH2BsAyed4j2", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627493297690, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "dvyfvh5NQ285TcCGd"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "AeS7btH2BsAyed4j2", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627493345723, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "Z3BxL43q8faJN46oi"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560723910, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "MqPs8nCMEjDdFGcYb"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560759136, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "CLBWiXwHKxTuT5zhA"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "qwwgzyRjPAEBXDBQz", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560836285, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "iZgTQEvDAa7uyz7fk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560926867, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "d3szeBwDr75HTKLPa"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627560953827, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "M26B9msDtbnMykcZm"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627560964839, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "hsZsBWBmzj6hDiqsd"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627561002611, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "XtgGvwaiLi9TgDrmN"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627561023633, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "34A4DTSqSwkXb6s3x"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561073680, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "PcrWPyugi2pgN4zbP"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "Kfam32uYR3AsZayvG", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627561089154, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "XRYBZ8oSxSNTsY6ne"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "Kfam32uYR3AsZayvG", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627561110420, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "iTAgnvfX7kn33nyiD"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Ks2fyxKMyceKLW9D7", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561114470, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "jqmoffSzRuWybjgfi"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561151700, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "XRKb3EFH5ZkwCNEHE"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "gXPAdm3ebZiHZGZwR", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561200843, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "gEBEYShELHSKKJ6Zf"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "GkA8bk2yHPsvnz7dt", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561241197, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "FmtEKFvB43NjE3Mh3"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "czWazq2hpB4QkWsWn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561264540, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "ysmpyASwRu4b2PhDM"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561304034, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "Wa4iMxrXScZS2ExHR"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561327984, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "9ym2xN6iCagAnRFoe"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NhEMjjJaA87tBxfbv", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561363483, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "iTSwk9hA7FxkYriLZ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561402573, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "SpKh9KBYNLgWpLegH"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "uAGjsoQiokpbJGZn8", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561430736, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "4Se4yhGWzkpygQxfH"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "9J6WH7kvJpsWv28gw", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561451316, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "noiZYHBPTGHtGEqJr"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "email", "subType": "realtime_view", "detail": "food", "momentId": "9J6WH7kvJpsWv28gw", "createdBy": "SYSTEM", "createdAt": 1627561459595, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "oXPPLWfjEK5YNAEM8", "_id": "QqZDGmPaFefB3ix77"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NsAdQkWc3azSa4Rvu", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561493755, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "Ww6XRoHZFFtf5h6kc"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561523241, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "BRgsowNTNu4usH2Sk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "27MLf4ccSxNHMoy9Z", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561557768, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "fZoxAaBuAfSqf68Xe"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627561580000, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "8nw6cgHaYhiZvdAGW"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561596355, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "v8dXHdbxPo5H7u4D4"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561631460, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "hh9EstFaxkDNEvqGX"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "rRuGo5tmTEX7f9hwT", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561675195, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "nwzDdtqW75RrAKZBj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561705315, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "TMzN4kZQBAXwfBYMt"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "9J6WH7kvJpsWv28gw", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1627561709435, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "jJwDRYM9N7RQHgcjK"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CC2cC5Q9XAt2BeSQC", "createdBy": "iT9ZbCgL9ZbPwzyBS", "createdAt": 1627561739532, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "iT9ZbCgL9ZbPwzyBS", "_id": "Lu3NNGeQHiGhY565D"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "gXPAdm3ebZiHZGZwR", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566821199, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "MbopREzcvzbPzszad"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "NsAdQkWc3azSa4Rvu", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566866749, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Mpobeqk27veJEX92R"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566936264, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "avMYnCJYfZC8ngmea"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "qwwgzyRjPAEBXDBQz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627566966362, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "EScWYS32iD6Hh8php"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567044531, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "GAQKcSArrggETSNnP"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567064237, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "mHNdXT3xCrhE8wY9y"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CC2cC5Q9XAt2BeSQC", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567080709, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "kxa29mwZKHasveqvd"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "uAGjsoQiokpbJGZn8", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567100930, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "w4w4P3wToQ24s2Cnh"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567116393, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "vm3zyTZQ3nKyY5kwC"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567134233, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "2umBnYi9kgA46G7wy"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567149307, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "Lbu8WYp9DFmZrMBhS"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "rRuGo5tmTEX7f9hwT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567166647, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "sTyLudvpyJMD6dM66"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "WhYpv6DTrgTtzqGWT", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567186025, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "YhZggztSbkk3679Cp"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567203047, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "TJ4v7EZWryeKiy58D"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567218192, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "mTZ4aoWn2wWCq9yYs"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567235193, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "nbudM9oc3Fvg34Lmt"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567248797, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "wSbrhHDBCfvEE2hKs"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567265352, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "y295Efv63u3uSqnge"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567293386, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "P7vBJjC7gvYvSu85t"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Zq3sT7fG6W7QH22dP", "createdBy": "N7Yynvhs9pqBEiR92", "createdAt": 1627567306020, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "N7Yynvhs9pqBEiR92", "_id": "uFarja2qgpiumJuit"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "27MLf4ccSxNHMoy9Z", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567310905, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "MfM49ahEbHF8r7nrE"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "email", "subType": "realtime_view", "detail": "food", "momentId": "Zq3sT7fG6W7QH22dP", "createdBy": "SYSTEM", "createdAt": 1627567314882, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "oXPPLWfjEK5YNAEM8", "_id": "afc8JAQFxX5rmhvhm"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "czWazq2hpB4QkWsWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567327901, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "AXDmWaAwfMiuHA9hK"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "4YG5to3k7uPcNRnTF", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567446064, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "vwYg9o4oZBptws9mq"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "bTGZeNknMXtK3vjBN", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567455967, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "mdxBwkgjRcTQoRHyk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BtHfALLuLPHyywh8G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567466391, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "yCdqFH2kMbM39vXgL"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567476327, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "XZf8CHHYsKmNThHcm"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567482140, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "TJkB6c9FQGRhQThhh"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567489419, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "A3H2hcP7MTyA9kX5x"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567511473, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "yXQknPac7hBtvEY8E"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Zq3sT7fG6W7QH22dP", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567545692, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "7TH5p7a5h6EtoRue8"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567558240, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "NSCnmCK9gQ4Lpwt92"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567567727, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "d4rhLi2zg5X2QyPtF"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567578667, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "A2qW6NirDLne4ekvc"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567585077, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "LpQAyswq47dDRH7bk"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567598807, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "oKp7jQx2hh4StYJ2g"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567614726, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "GyNfrdKsL4DuACCiJ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567622731, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "3ZcmiDnbvcPW8iEqZ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567631188, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "P9T6kk6vKftW7eNan"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "sleep", "momentId": "Hh4r9fTYXA94P24M3", "createdBy": "N7Yynvhs9pqBEiR92", "createdAt": 1627567663795, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "N7Yynvhs9pqBEiR92", "_id": "6NkLnXAnZHqijDZcc"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "doLLNyz83JGDgdcgZ", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567749816, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "mD9urCkqY4MsiPnEj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "XXyLhESwAfP9hoLbi", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567763187, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "A69dZGttCEtGLQGQM"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Yxtcq9BY7Aw9FXGdn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567772219, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "KRKa5jsXu5A9EYE2j"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "CuQq7dYTjQMuEqDWn", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567778543, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "yN6CAfbkpH8spvttb"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Qf4MGZr9CfcQcBF76", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567798104, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "HRuNjYWJaS5EK2hGp"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "pet7LgxamzHhBm3j5", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567804684, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "QgvQTX2Jbo8GYTuj4"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "cqpq48WkwvX7PMv2G", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567809250, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "n6RBXneqaDEB2K9eB"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2MLCREPjRSXvm6WNz", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567819384, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "LuzNHNKDEYKtzXEfZ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "Pe6fyFF6JwEMTmTyv", "createdBy": "s9Dcs8ixrEWvwhwE2", "createdAt": 1627567826506, "targetPersonId": "PPerx9LKNChSDaHSw", "sourcePersonId": "s9Dcs8ixrEWvwhwE2", "_id": "fWQiMdTb3Kq2jxxce"}], "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInOutTime": 1627603207066, "checkedInById": "7A4gspSfjvrGd2bSQ", "avatarPath": "nTvbx24M2dbM9w6tu/djB2aHFjG3KjJbazZ/3Agr0ToBOMDSG3uvOTEA", "avatarToken": "3Agr0ToBOMDSG3uvOTEA", "billing": {"enrolledPlans": [{"_id": "w3djzJDQyPafET8Jy", "planDetails": {"_id": "w3djzJDQyPafET8Jy", "description": "Threes (Weekly)", "amount": 150, "type": "plan", "frequency": "weekly"}, "enrollmentDate": 1587358800000, "allocations": [], "createdAt": 1595512905801, "expirationDate": 1592802000000}, {"_id": "2kKehRgyKp6WasstH", "planDetails": {"_id": "2kKehRgyKp6WasstH", "description": "Before School Care", "amount": 200, "type": "plan", "frequency": "monthly", "suspendUntil": *************}, "enrollmentDate": 1608094800000, "allocations": [{"allocationType": "reimbursable", "amount": 50, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "3DQgvmBXzNvBg26MG"}], "createdAt": 1608147165564}], "pendingCharges": [], "lastInvoiced": 1691685869298, "billingNotes": ""}, "lastInformedArrival": {"source": "familyCheckin", "createdAt": 1614794453798, "checkedInById": "2papyMAsWKoebY55y", "fieldValues": {"lastFood": "8:32 am", "napsToday": "", "changeInCare": "She seems a bit tired 😴"}, "absent": false}, "immunizationEntries": [{"type": "DTaP", "date": 1520658000000}, {"type": "DTaP", "date": 1512882000000}, {"type": "DTaP", "date": 1528603200000}, {"_id": "3MzLbSy7GmKZh9SkY", "type": "Annual Physical", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1588329390957, "immunizationDefinitionId": "tNPDGNDQqhvX9mADH", "date": 1540267200000, "archived": false}, {"_id": "jWwgcKJZ9Ed3YzY6q", "type": "DTaP", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1594133530746, "exemption": "Medical or religious exemptions only", "date": null}, {"_id": "TBAZcWTXjT8RTfFoq", "type": "HepB", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1605290916208, "date": 1161576000000}, {"_id": "cdh6FwrKM34wF8AuG", "type": "IIV", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1606232529101, "date": 1577941200000}, {"_id": "udQ4Hg6e7zhFqdWe2", "type": "Annual Physical", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1606232568271, "immunizationDefinitionId": "tNPDGNDQqhvX9mADH", "date": 1546318800000}, {"_id": "m4hDHkR8KEZDCoWAj", "type": "Annual Physical", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1606232568271, "immunizationDefinitionId": "tNPDGNDQqhvX9mADH", "date": 1522987200000}, {"_id": "szQEBPfFK2Y7u7HkR", "type": "HepB", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513223136, "date": 1615780800000}, {"_id": "tSDxRKAQm5h5iuHte", "type": "HepB", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513233387, "date": 1616385600000}, {"_id": "qq2MKcALF6ZBeiw9B", "type": "RV", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513244784, "date": 1615867200000}, {"_id": "yTihoKFmiqHbKBB8w", "type": "Annual Physical", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513251681, "immunizationDefinitionId": "tNPDGNDQqhvX9mADH", "date": 1616385600000}, {"_id": "uzPZCkDMHYeZoekjM", "type": "Annual Physical", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513259886, "immunizationDefinitionId": "tNPDGNDQqhvX9mADH", "exemption": "Medical or religious exemptions only", "date": null}, {"_id": "PxvRCQAZkW2NwFKAq", "type": "RV", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513272065, "exemption": "Medical or religious exemptions only", "date": null}, {"_id": "tYbiXimKKFb7t4QEf", "type": "Hib", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513281920, "exemption": "Medical or religious exemptions only", "date": null}, {"_id": "b2GR39JFJZajeYdy5", "type": "IPV", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513292730, "exemption": "Medical or religious exemptions only", "date": null}, {"_id": "F2RNWq5G728pGkK2j", "type": "HepA", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513301068, "exemption": "Medical or religious exemptions only", "date": null}, {"_id": "XS4TD3qMEY8inuwdb", "type": "VAR", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513310027, "exemption": "Medical or religious exemptions only", "date": null}, {"_id": "GDPMAjgnzxcPfXNMC", "type": "MMR", "createdByPersonId": "RYetWpMJhsC3BcPjE", "createdAt": 1616513324119, "exemption": "Medical or religious exemptions only", "date": null}], "medicationEntries": [{"_id": "JmZ6RN4kkDY5C9p3q", "name": "Tylenol", "dosage": "5ml", "frequencyType": "other", "durationType": "ongoing", "notes": "as needed"}], "birthday": 1161576000000, "classList": {"defaultDays": {"M": "1", "T": "1", "W": "1", "R": "1", "F": "1"}, "transitionDays": {"M": "1", "T": "1", "W": "1", "R": "0", "F": "0"}}, "transitionGroupDate": 1619841600000, "transitionGroupId": "yvDMGuRGobm9YMnuX", "deactivatedAt": 1594040508993, "deactivationReason": "", "inActive": false, "allergies": "Peanuts", "notesPrivate": "Notes", "nickname": "<PERSON><PERSON>", "notes": "More notes", "enrollmentDate": 1598846400000, "scannedDocuments": {"enrollmentPaperwork": [{"name": "PDF2.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/JVrhqyKKbbZTUxcuC2sW", "mediaToken": "JVrhqyKKbbZTUxcuC2sW", "mediaFileType": "application/pdf", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/JVrhqyKKbbZTUxcuC2sW"}], "medicalInformation": []}, "annualPhysicalDate": "", "backUpCare": "", "backUpCareReferringPartner": "", "billingNotes": {"Billing Notes": ""}, "cacfpSubsidy": "Paid", "curriculum": "", "payer": "", "physicianName": "", "physicianPhone": "", "primaryFamily": "", "quickList": "", "sex": "", "siblings": "", "previousClassroomGroupId": "zvWiPSxhkgGBvq7A6", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "preferredSchedule": "", "standardOutlook": {"allergies": "", "importantNotes": ""}, "withdrawDate": 1660795200000}, {"_id": "MdKPhsKB4btnEZt6n", "firstName": "<PERSON> ", "lastName": "<PERSON><PERSON>", "defaultGroupId": "zvWiPSxhkgGBvq7A6", "type": "person", "createdBy": "dj7khLR8yA5JtEeYW", "orgId": "nTvbx24M2dbM9w6tu", "avatarPath": "nTvbx24M2dbM9w6tu/dj7khLR8yA5JtEeYW/lcbOoITtT9u1DiN7cK50", "avatarToken": "lcbOoITtT9u1DiN7cK50", "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "69BREkBxEe8an4TbA", "checkedInOutTime": 1684427306093, "lastInteractionDate": 1660072200000, "lastMoment": {"attributionPersonId": null, "momentType": "sleep", "time": "3:10 pm", "date": "08/09/2022", "sortStamp": 1660072200000, "endTime": "3:11 pm", "momentTypePretty": "Sleep", "sleepDidNotSleep": false, "createdAt": 1660072235215, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "createdByPersonGroupId": "FWBPM7tW9BxEfTDaa", "createdByPersonCheckInGroupId": "FWBPM7tW9BxEfTDaa", "_id": "RkBa3Eshf26TcfX3Q", "orgName": "321-Mariposa Local"}, "lastMomentByType": {"food": {"attributionPersonId": null, "momentType": "food", "time": "3:09 pm", "date": "08/09/2022", "sortStamp": 1660072140000, "foodType": "Breakfast", "foodAmount": "All", "momentTypePretty": "Food", "createdAt": 1660072148092, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "createdByPersonGroupId": "FWBPM7tW9BxEfTDaa", "createdByPersonCheckInGroupId": "FWBPM7tW9BxEfTDaa", "_id": "DtKrHgmtyB2KrzS3T", "orgName": "321-Mariposa Local"}, "potty": {"attributionPersonId": null, "momentType": "potty", "time": "3:10 pm", "date": "08/09/2022", "sortStamp": 1660072200000, "pottyType": "Wet", "momentTypePretty": "<PERSON><PERSON>", "pottyTraining": "Tried", "pottyAppliedOintment": false, "createdAt": 1660072219062, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "createdByPersonGroupId": "FWBPM7tW9BxEfTDaa", "createdByPersonCheckInGroupId": "FWBPM7tW9BxEfTDaa", "_id": "Wacz4Lr7bsWZDTpf7", "orgName": "321-Mariposa Local"}, "activity": {"attributionPersonId": null, "momentType": "activity", "comment": "<PERSON> and <PERSON> followed the realistic steps you take when baking a cake!", "time": "1:40 pm", "date": "07/28/2021", "sortStamp": 1627494000000, "activityType": "Daily Living Skills", "activityEngagement": "Active", "momentTypePretty": "Activity", "createdAt": 1627494067703, "createdBy": "tpm2GGoo5T58WRXue", "attributionName": "<PERSON>", "createdByPersonId": "7A4gspSfjvrGd2bSQ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["HMKtg6wfCM7b2bn8E", "MdKPhsKB4btnEZt6n"], "_id": "JWMt4sLwKHk9XK9YD", "orgName": "Mariposa Academy - Indianapolis"}, "sleep": {"attributionPersonId": null, "momentType": "sleep", "time": "3:10 pm", "date": "08/09/2022", "sortStamp": 1660072200000, "endTime": "3:11 pm", "momentTypePretty": "Sleep", "sleepDidNotSleep": false, "createdAt": 1660072235215, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "createdByPersonGroupId": "FWBPM7tW9BxEfTDaa", "createdByPersonCheckInGroupId": "FWBPM7tW9BxEfTDaa", "_id": "RkBa3Eshf26TcfX3Q", "orgName": "321-Mariposa Local"}, "learning": {"attributionPersonId": null, "momentType": "learning", "comment": "Painting", "time": "1:27 pm", "date": "07/22/2021", "sortStamp": 1626974820000, "momentTypePretty": "Learning", "learningType": "Preschool: Art", "learningCurriculumId": "", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["MdKPhsKB4btnEZt6n", "HMKtg6wfCM7b2bn8E", "QEWjwEM4nEnYPzqJ6"], "_id": "JstRKpNkdLgkjBeKy"}, "comment": {"attributionPersonId": null, "momentType": "comment", "comment": "More fun coming!", "time": "3:10 pm", "date": "08/09/2022", "sortStamp": 1660072200000, "momentTypePretty": "Comment", "createdAt": 1660072212693, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "createdByPersonGroupId": "FWBPM7tW9BxEfTDaa", "createdByPersonCheckInGroupId": "FWBPM7tW9BxEfTDaa", "_id": "rZ6nPsfDsjxsu6zLi", "orgName": "321-Mariposa Local"}, "checkin": {"attributionPersonId": null, "momentType": "checkin", "comment": "Checked into Ready-2-<PERSON><PERSON>", "time": "8:00 am", "date": "07/29/2021", "sortStamp": 1627560000000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "2q36KJg9dAB9dGpbj"}, "mood": {"momentType": "mood", "time": "2:15 pm", "date": "5/10/2019", "sortStamp": 1557512100000, "moodLevel": "Happy", "momentTypePretty": "<PERSON><PERSON>", "createdAt": 1557512135323, "createdBy": "djB2aHFjG3KjJbazZ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "7mvttGq4BGbzQw9vZ", "orgName": "Premier"}, "alert": {"attributionPersonId": null, "momentType": "alert", "comment": "Reminder: We will be closed on Monday, July 19 for a teacher in-service day.", "time": "5:39 pm", "date": "07/16/2021", "sortStamp": 1626471540000, "momentTypePretty": "Notification", "alertSendTypeEmail": false, "alertSendTypeText": false, "alertSendTypePush": true, "alertShowCheckin": false, "createdAt": 1626471553840, "createdBy": "3FhP46Kjmiro3bL7G", "attributionName": "<PERSON>", "createdByPersonId": "8uAYxXbT6urWxXg3k", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["vyxDKuFigFGRttBZS", "RYetWpMJhsC3BcPjE", "69BREkBxEe8an4TbA", "9W9bZ5DJGF7LWaLmJ", "N7Yynvhs9pqBEiR92", "BgEJcyPDDPPGT97w5", "y6Lp9DaDAKZP6WZDr", "Ao67WMf2Gi3GfW8Ro", "pCHFhtbNwq7dG4eHG", "vafkMjcEs8qLZyfTr", "hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "N5yn3DbpjCMdGgAJR", "J8uD8wR7i9zycX3aR", "R37M7tvesRJLcTskH", "TKw3K4856H8Et7xNx", "4pPTmaevgLcoWExz2", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "MdKPhsKB4btnEZt6n", "JXMHjAYzsBx22h5CS", "Qhirjw7H7Lr9PccCK", "R6fKP3hHEBafxeyuq", "BaDDgKr4brvSJomZZ", "vJHZ8P3KWxwRMak7a", "LjRaTQFhkYwbztoks", "2papyMAsWKoebY55y", "GsXkcQ9izfhpNFoac", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "bwmKASWnBTLrmSkoS", "9zXMi5TWSNn2t3rYR", "veLKDqRsKQibwwtDj", "vyyGJsucZimKjkxJa", "wd39xoosTnzjsGb6c", "8uAYxXbT6urWxXg3k", "WgtTaSFgA8LXY49KB", "MZLqugrBbXT6b93aF", "Ko2dpdjhLtwTP6HHs", "fYEgtG9KzZakDD8Lq", "eQNegjBtSNijtsheD", "9ovZqAWdGKjaRgRu7", "XKs6nQpNv7q9RYEvK", "HMKtg6wfCM7b2bn8E", "xYhHHnxhiTXFTxscg", "XHjxC6SJmt34NMGzp", "Fhrqwcq9Yw8S7FWxE", "iT5PLBudyY9FKurF7", "pyYSwFc2PJjHJKrKW", "GkqcNHheuvn8SBXvn", "MyFepQyWoycBRmBCS", "LFuMMLpNGqaNR4v78", "7A4gspSfjvrGd2bSQ", "8jgMSvrFYadoNgRxY", "yT9Zeaj2EzH85MQoj", "ZoS2Y8wwSBNwrJkRs", "gHoZrjN9f6LHzEegS", "FfzHpuRYnX5nCayfS", "ALszCkiMER4Yzujmz", "EYCwPvkG5Zd23hsSZ", "nmQ5Lmv6S6aX8XhxC", "gijNLLWJbjzikeMWz", "qTLvGki7YvdTQWp8Q", "2Sd4utZGjpDQeut64", "wSTzopnnwsbYWwsri", "bgYPrFT6jnRbzwpZz", "xdPGHYnY4LDTW5uaK", "jcRkB6PBCFNRS9asn", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "ch2ZhHu3h7w35zSWB", "LjANQKbvYfGtThZs5", "q4cwWG8DpsDjmmnL6", "LBDaRNzLqg9NRGXW6", "5KMccvT6pukro9Ty5", "rAaxA8yPKFYDjrzJk", "CofTFp5PRggvz477E", "QcNdA3bpdZXmRWujH", "C89A4qgWiHB67qGR2", "DkDL5e5jELZBbv6PT", "ddYRmKZd2n79NiakF", "gGyQF3jYMbF7bp2cF", "TTbkpM3mxsnNEzeN6", "6aguBjjQNCMXPnse8", "J5wzhMne96f5uFxMY", "iXgWLbg5GDbdTB2Bw", "D5R4qkBKZkKejEKDB", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "XyD2Hw6RfqegfeMtY", "iT9ZbCgL9ZbPwzyBS", "GHhRHxXwKkAQPSTRH", "gvPNP3DyDif4opnia", "7rjCurgW62RebGjPY", "pyNcuF7Jm7GNehmPz", "u5s56Y8CswdDjh2GP", "f2kLwWFwL4mfWpJCK", "tyXnH6RLpnqNcHf6Y", "QxTFrRwFCXF3pfaao", "ACS7zGZzLsPv65y39", "PKNsmS5hNRZFvtNgp", "27WKMANJrmX6JwMXq", "HE5MvfRwX4wqrX3Tv"], "_id": "D5MDbP8P8XhjujeH7", "orgName": "Mariposa Academy - Indianapolis"}, "ouch": {"momentType": "ouch", "comment": "<PERSON> is just fine", "time": "7:48 am", "date": "07/24/2019", "sortStamp": 1563968880000, "momentTypePretty": "Ouch", "ouchDescription": "<PERSON> scraped his knee", "ouchCare": "We put a band aid on the cut and applied neosporin", "ouchContactedParent": "Phone", "ouchCalledParentTime": "7:30 am", "ouchContactedDoctor": "", "ouchNurseNotified": false, "ouchProfessionalMedication": false, "createdAt": 1563976198464, "createdBy": "fPsbbX4pjfL7SinqK", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "NukPYPBBnJLvZo8PG", "orgName": "Premier"}, "supplies": {"attributionPersonId": null, "momentType": "supplies", "time": "3:09 pm", "date": "08/09/2022", "sortStamp": 1660072140000, "supplyType": "Breast Milk", "momentTypePretty": "Supplies", "createdAt": 1660072159530, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "createdByPersonGroupId": "FWBPM7tW9BxEfTDaa", "createdByPersonCheckInGroupId": "FWBPM7tW9BxEfTDaa", "_id": "6aJLKpw3FqeafBvkL", "orgName": "321-Mariposa Local"}, "illness": {"momentType": "illness", "time": "7:50 am", "date": "07/24/2019", "sortStamp": 1563969000000, "momentTypePretty": "Illness", "illnessSymptoms": ["Fever (101 degrees or higher)"], "createdAt": 1563976228355, "createdBy": "fPsbbX4pjfL7SinqK", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "hjhRE5MRroThecqzX", "orgName": "Premier"}, "portfolio": {"momentType": "portfolio", "time": "8:18 pm", "date": "11/19/2019", "sortStamp": 1574212680000, "momentTypePretty": "Portfolio", "portfolioCurriculumId": "raP7ciX8f9kxLoD76", "portfolioCurriculum": {"_id": "raP7ciX8f9kxLoD76", "headline": "Imagination: This is my family", "message": "Children will take turns pretending to be different members of a family during imagination time. When the child pretends to be the parent of the group, they will work with their group to show different traditions their own family follows.", "scheduledDate": 1574139600000, "selectedGroups": ["3J2GoZsZb6DkSC9D5", "AeNhNMr5RBEN2omSo", "zvWiPSxhkgGBvq7A6", "fdaQtESnEfdiyjw9p", "aah6pMsvqyraeLK9E"], "selectedStandards": ["Lightbridge|e0e82916cf605577c0a9e8e48f97a780176dfe6b"], "selectedTypes": ["<PERSON><PERSON>: Imitating and Pretending"], "selectedAgeGroup": "18 months - 27 months", "curriculumThemeId": "7KX6oHKFBYHTtHWX3", "originalCurriculumId": "", "orgId": "nTvbx24M2dbM9w6tu", "createdBy": "FMAMv6kpgQZf4HS8x", "createdAt": 1574095504350, "mediaFiles": []}, "portfolioAssessments": [{"standardId": "e0e82916cf605577c0a9e8e48f97a780176dfe6b", "value": 2}], "createdAt": 1574212915560, "createdBy": "djB2aHFjG3KjJbazZ", "createdByPersonId": "9W9bZ5DJGF7LWaLmJ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "kWKweH5h8xKq3pC6f", "orgName": "Mariposa Academy"}, "medical": {"attributionPersonId": null, "momentType": "medical", "time": "3:08 pm", "date": "08/09/2022", "sortStamp": 1660072080000, "medicalAdministeredBy": "acmmmQ23uE8J243zz", "medicalAdministeredByPerson": {"fullName": "<PERSON>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, "momentTypePretty": "Medical", "medicalMedicationId": "rpCTpTsAcpESmCvhJ", "medicalMedication": {"_id": "rpCTpTsAcpESmCvhJ", "name": "Benadryl ", "dosage": "5mg", "frequencyType": "everyhours", "durationType": "ongoing", "notes": "As needed", "frequencyAmount": 4, "frequencyDescription": "Every 4 hours", "durationDescription": "Ongoing", "medicationDescription": "Benadryl  (5mg - Every 4 hours)"}, "createdAt": 1660072129624, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "createdByPersonGroupId": "FWBPM7tW9BxEfTDaa", "createdByPersonCheckInGroupId": "FWBPM7tW9BxEfTDaa", "_id": "b8AGntPXfz2GrMS3u", "orgName": "321-Mariposa Local"}, "behaviorChild": {"momentType": "<PERSON><PERSON><PERSON><PERSON>", "time": "4:05 pm", "date": "1/18/2020", "sortStamp": 1579381500000, "momentTypePretty": "Behavior", "isDynamic": true, "momentDefinitionId": "vkqzWHFHdKqvP86TE", "dynamicFieldValues": {"followedDirections": "mostoftheTime"}, "createdAt": 1579381579302, "createdBy": "v4x4cY4Tcv3NPGFH3", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks", "R37M7tvesRJLcTskH", "R6fKP3hHEBafxeyuq", "Qhirjw7H7Lr9PccCK", "MdKPhsKB4btnEZt6n"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/fPjxoi7uKvBGJd7zF", "mediaToken": "fPjxoi7uKvBGJd7zF", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/fPjxoi7uKvBGJd7zF"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/5hy7FsgAbBSCFXzp6", "mediaToken": "5hy7FsgAbBSCFXzp6", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/5hy7FsgAbBSCFXzp6"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/cKjkQYjiuJtbpesWv", "mediaToken": "cKjkQYjiuJtbpesWv", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/cKjkQYjiuJtbpesWv"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/x5uXiYTibK4skv24D", "mediaToken": "x5uXiYTibK4skv24D", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/x5uXiYTibK4skv24D"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/WRo5mTKuADtT5oMnu", "mediaToken": "WRo5mTKuADtT5oMnu", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/WRo5mTKuADtT5oMnu"}], "_id": "RoRWBo4oYeRMpXjAc", "orgName": "Mariposa Academy"}, "checkout": {"attributionPersonId": null, "momentType": "checkout", "comment": "Checked out of Ready-2-<PERSON><PERSON>", "time": "4:16 pm", "date": "07/27/2021", "sortStamp": 1627416960000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "4ZtFywLu6JQDbAM4m"}, "wonderlandToileting": {"attributionPersonId": null, "momentType": "wonderlandToileting", "time": "3:18 pm", "date": "6/8/2021", "sortStamp": 1623179880000, "momentTypePretty": "Toileting", "isDynamic": true, "momentDefinitionId": "6kssTbqNztFAvML6W", "dynamicFieldValues": {"details": "<PERSON><PERSON>", "additionalDetails": "In Diaper", "undressingDressing": "Changing Table Collaboration"}, "createdAt": 1623179895666, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "2SJsdKzWpMAcJJJz7", "orgName": "Mariposa Academy - Indianapolis"}, "safety": {"attributionPersonId": null, "momentType": "safety", "time": "4:01 pm", "date": "07/22/2021", "sortStamp": 1626984060000, "momentTypePretty": "Safety", "isDynamic": true, "momentDefinitionId": "Ksrw9M733c4nHq9pL", "dynamicFieldValues": {"safetyActivityTwo": "Lockdown Drill"}, "createdAt": 1626984093618, "createdBy": "3FhP46Kjmiro3bL7G", "attributionName": "<PERSON>", "createdByPersonId": "8uAYxXbT6urWxXg3k", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "ZFi25Sg6Gufg6Xb5x", "orgName": "Mariposa Academy - Indianapolis"}, "covidHealth": {"attributionPersonId": null, "momentType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "3:18 pm", "date": "6/8/2021", "sortStamp": 1623179880000, "momentTypePretty": "Health Check", "isDynamic": true, "momentDefinitionId": "hCsLem9rTw7FFa5KB", "dynamicFieldValues": {"temperature": "99", "noneAbove": true}, "createdAt": 1623179930396, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "fXJusnJyw85hXEzmn", "orgName": "Mariposa Academy - Indianapolis"}, "safetyEducare": {"attributionPersonId": null, "momentType": "safetyEducare", "time": "4:06 pm", "date": "07/22/2021", "sortStamp": 1626984360000, "momentTypePretty": "Safety", "isDynamic": true, "momentDefinitionId": "i5QhYytghW8PsToie", "dynamicFieldValues": {"safetyActivityFour": "Earthquake"}, "createdAt": 1626984411163, "createdBy": "3FhP46Kjmiro3bL7G", "attributionName": "<PERSON>", "createdByPersonId": "8uAYxXbT6urWxXg3k", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n"], "_id": "RmDBuKRMCyJijBSKf", "orgName": "Mariposa Academy - Indianapolis"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "BDyZCRhpFaKTpDdzt", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1659715006571, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "FdWvo8kYDPFk4ZurQ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "5MabsgsmEyANkbHWH", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1659715418411, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "xaPzWbb8S83XiKwDc"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "PeNmGBf6EmKtKT9H3", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072114169, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "t3Aju43LDwjY9izBf"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "PZwoLwneZnZGvJ9PL", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072121343, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "HB4kmn5gYnPYatYfJ"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "medical", "momentId": "b8AGntPXfz2GrMS3u", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072129638, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "pCR5SWGk8NwwbRmxs"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "KybHrtydHtKeejJRR", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072135158, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "98W8JPS5bbmh64GyT"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "YBAi4dL7WCXpdW4cB", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072139499, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "5wxz2Yv9wavpfMBv4"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "DtKrHgmtyB2KrzS3T", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072148122, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "osfPaotjQeQrR6nd5"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "supplies", "momentId": "6aJLKpw3FqeafBvkL", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072159549, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "r6n2qjx3EGp3ZR8Aj"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "jTqPLJELtxFE5ZiK7", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072163372, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "aSuN7CyyLb5nhqr9x"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "NeduqxwgcGnqTYFFP", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072170751, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "fTiD8Bbrk5mE5KZRM"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "2PmKYAAJRBJHSt49p", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072178151, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "DYaB5NGHky433o2WN"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "muzQjKszpsdSSXPCM", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072189905, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "uuSDwcrvFHbgZKRmv"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "rZ6nPsfDsjxsu6zLi", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072212721, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "RNY25M6C3XANvQvrx"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "Wacz4Lr7bsWZDTpf7", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072219094, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "DuwSbTr2skALJA9mh"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "mTDGE4sW5JXZ5b3kZ", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072227177, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "SBEEY3kHC6efy2YwC"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "sleep", "momentId": "RkBa3Eshf26TcfX3Q", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072235232, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "ABpqQQkiBL7B4aTHv"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "irxneQahaADapaA6z", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072246188, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "oHn5nJ4PdS7ArongP"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "yEKqjxRTqQgb78yvX", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1660072252291, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "izzdn9WHeJTDxfZm5"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "u2xJA9DLz875ihv8H", "createdBy": "69BREkBxEe8an4TbA", "createdAt": *************, "targetPersonId": "MdKPhsKB4btnEZt6n", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "6bcohYeziHHd5rpG4"}], "billing": {"enrolledPlans": [{"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "dollars", "discountType": "customerSpecific", "allocationDescription": "Discount: Customer-Specific", "id": "bgjeLJnkbEqbutDt9"}, {"allocationType": "reimbursable", "amount": 15, "amountType": "dollars", "reimbursementType": "onmywayprek", "allocationDescription": "Reimbursable: On My Way Pre-K", "payerEndDate": *************, "id": "oTmzEkSwbdTHhM868"}, {"allocationType": "reimbursable", "amount": 50, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "payerStartDate": *************, "payerEndDate": *************, "id": "kKg89PqEfYtB2dnRa"}, {"allocationType": "reimbursable-with-copay", "amount": 50, "amountType": "dollars", "reimbursementType": "elrc", "selectedResponsiblePartyId": "kkGCnfhZQ2o67NS8S", "allocationDescription": "Reimbursable: ELRC weekly, $50.00 copay by Chelsea Allis", "payerStartDate": 1648785600000, "id": "WNNqbA3dBd5vjr7Sy"}], "createdAt": 1627005594736, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": 290, "updatedAt": 1684262753696, "updatedBy": "ZdkhmaEScuuboPFbH"}], "lastInvoiced": 1691685868987, "pendingCharges": []}, "deactivatedAt": 1569419376035, "deactivationReason": "", "inActive": false, "medicationEntries": [{"_id": "rpCTpTsAcpESmCvhJ", "name": "Benadryl ", "dosage": "5mg", "frequencyType": "everyhours", "durationType": "ongoing", "notes": "As needed", "frequencyAmount": 4}, {"_id": "TagXTksv8H6WTiqsR", "name": "Tylenol", "dosage": "1.25mg", "frequencyType": "timesperday", "durationType": "ongoing", "notes": "dosage", "frequencyAmount": 2}], "cacfpSubsidy": "Free", "sex": "Male", "immunizationEntries": [{"type": "HepB", "date": 1577854800000}, {"_id": "qPJpStQPiWLiz8ePW", "type": "DTaP", "createdByPersonId": "69BREkBxEe8an4TbA", "createdAt": 1652731068098, "date": 1652068800000}], "nickname": "<PERSON>", "scannedDocuments": {"enrollmentPaperwork": [{"name": "CNA-Nicole-Hubbard.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/Mkp8P921k8HH8NdHzHkz", "mediaToken": "Mkp8P921k8HH8NdHzHkz", "mediaFileType": "application/pdf", "mediaPath": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/Mkp8P921k8HH8NdHzHkz"}], "medicalInformation": [], "courtDocuments": [{"name": "CNA-Jaimie-elwood.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/0DZ5VhnoKn6ciDABMbcU", "mediaToken": "0DZ5VhnoKn6ciDABMbcU", "mediaFileType": "application/pdf", "mediaPath": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/0DZ5VhnoKn6ciDABMbcU"}], "accidentReports": [{"name": "Companion-Tam<PERSON>-Whitehead.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/i5grnQOKuzbSwmHHxbKV", "mediaToken": "i5grnQOKuzbSwmHHxbKV", "mediaFileType": "application/pdf", "mediaPath": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/i5grnQOKuzbSwmHHxbKV"}], "incidentReports": [], "illnessReleaseForms": [{"name": "CNA-Courtney-Beard.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/EsGRMcpEW5Acu6CS9xPE", "mediaToken": "EsGRMcpEW5Acu6CS9xPE", "mediaFileType": "application/pdf", "mediaPath": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/EsGRMcpEW5Acu6CS9xPE"}]}, "allergies": "Tree Nuts", "annualPhysicalDate": "", "backUpCare": "", "backUpCareReferringPartner": "", "billingNotes": {"Billing Notes": ""}, "curriculum": "", "enrollmentDate": 1619409600000, "notes": "No Pictures", "notesPrivate": "No pictures ", "payer": "", "physicianName": "", "physicianPhone": "", "primaryFamily": "", "quickList": "No pictures", "siblings": "", "previousClassroomGroupId": "zvWiPSxhkgGBvq7A6", "documentItems": {"wNoztHs25ehFF2Zcm": {"templateOptionResult": {"action": "ack", "date": 1631732990409, "personId": "kkGCnfhZQ2o67NS8S", "personName": "<PERSON>is"}}}, "standardOutlook": {"allergies": "", "importantNotes": ""}, "withdrawDate": 1659499200000, "presenceLastGroupId": null, "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "preferredSchedule": "", "timestamps": {"moveMomentTimestamp": {"momentId": "BFRdFhw5gNiaP2qbi", "timestamp": 1684424174374, "groupId": "zvWiPSxhkgGBvq7A6", "oldGroupId": null}}}, {"_id": "LjRaTQFhkYwbztoks", "firstName": "Lincoln", "lastName": "<PERSON>is", "defaultGroupId": "zvWiPSxhkgGBvq7A6", "type": "person", "createdBy": "s3xrYq3pkm5Q7adkW", "orgId": "nTvbx24M2dbM9w6tu", "avatarToken": "9GiQWCnWUcqhFJqbtiAr", "avatarPath": "nTvbx24M2dbM9w6tu/s3xrYq3pkm5Q7adkW/9GiQWCnWUcqhFJqbtiAr", "checkedIn": false, "checkedInOutTime": 1648774920000, "checkInGroupId": null, "checkInGroupName": null, "checkedInById": "69BREkBxEe8an4TbA", "lastInteractionDate": 1648584660000, "lastMoment": {"attributionPersonId": null, "momentType": "comment", "comment": "Thing to like", "time": "4:11 pm", "date": "03/29/2022", "sortStamp": 1648584660000, "momentTypePretty": "Comment", "createdAt": 1648584733977, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["HMKtg6wfCM7b2bn8E", "LjRaTQFhkYwbztoks"], "_id": "jRu6M82az5g7xqr4r", "orgName": "321-Mariposa Local"}, "lastMomentByType": {"comment": {"attributionPersonId": null, "momentType": "comment", "comment": "Thing to like", "time": "4:11 pm", "date": "03/29/2022", "sortStamp": 1648584660000, "momentTypePretty": "Comment", "createdAt": 1648584733977, "createdBy": "ZdkhmaEScuuboPFbH", "attributionName": "<PERSON>", "createdByPersonId": "69BREkBxEe8an4TbA", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["HMKtg6wfCM7b2bn8E", "LjRaTQFhkYwbztoks"], "_id": "jRu6M82az5g7xqr4r", "orgName": "321-Mariposa Local"}, "incident": {"attributionPersonId": null, "momentType": "incident", "comment": "<PERSON> had a not so great day today. He pushed a classmate. ", "time": "4:06 pm", "date": "12/11/2020", "sortStamp": 1607720760000, "momentTypePretty": "Incident", "createdAt": 1607720805424, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "fAw6L8jgBry2Z8LMr", "orgName": "Mariposa Academy - Indianapolis"}, "food": {"attributionPersonId": null, "momentType": "food", "time": "10:00 am", "date": "07/29/2021", "sortStamp": 1627567200000, "foodType": "AM Snack", "foodItems": [{"name": "Cereal", "amount": "All"}, {"name": "Milk", "amount": "Most"}], "momentTypePretty": "Food", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "s9Dcs8ixrEWvwhwE2", "comment": "", "taggedPeople": ["pCHFhtbNwq7dG4eHG", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "MdKPhsKB4btnEZt6n", "LjRaTQFhkYwbztoks", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "WgtTaSFgA8LXY49KB", "HMKtg6wfCM7b2bn8E"], "_id": "Zq3sT7fG6W7QH22dP"}, "supplies": {"momentType": "supplies", "comment": "Supplies on my desk", "time": "1:20 pm", "date": "2/12/2020", "sortStamp": 1581531600000, "momentTypePretty": "Supplies", "createdAt": 1581531829678, "createdBy": "aZt73ZSQt7u7QQZk6", "createdByPersonId": "cBzPJLq3Fpgfy79cn", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["9W9bZ5DJGF7LWaLmJ", "WgtTaSFgA8LXY49KB", "LjRaTQFhkYwbztoks", "R37M7tvesRJLcTskH"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/otLZo2DypCHCmTPvy", "mediaToken": "otLZo2DypCHCmTPvy", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/otLZo2DypCHCmTPvy"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/DN7KX8ug4tpq6zKYf", "mediaToken": "DN7KX8ug4tpq6zKYf", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/DN7KX8ug4tpq6zKYf"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/kQcSZbm6vLNZM4Nbn", "mediaToken": "kQcSZbm6vLNZM4Nbn", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/aZt73ZSQt7u7QQZk6/kQcSZbm6vLNZM4Nbn"}], "_id": "ykRjWLQDKa9z9kumX", "orgName": "Mariposa Academy"}, "potty": {"attributionPersonId": null, "momentType": "potty", "time": "9:29 am", "date": "2/26/2021", "sortStamp": 1614349740000, "pottyType": "Wet", "momentTypePretty": "<PERSON><PERSON>", "createdAt": 1614353618635, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "WXZxpGoXRnKqhNaq7", "orgName": "Mariposa Academy - Indianapolis"}, "activity": {"attributionPersonId": null, "momentType": "activity", "time": "3:54 pm", "date": "3/22/2021", "sortStamp": 1616442840000, "activityType": "Daily Living Skills", "activityEngagement": "Active", "momentTypePretty": "Activity", "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "8uAYxXbT6urWxXg3k", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "jMq32Y2Y5AiHau2Sb"}, "sleep": {"attributionPersonId": null, "metaMomentId": "MSKTWdR9QAoJwwx5A", "momentType": "sleep", "time": "10:07 am", "date": "7/29/2021", "sortStamp": *************, "sleepCheckInterval": 3, "momentTypePretty": "Sleep", "createdAt": 1627567663472, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "gLHrtmjxmErXaZAyx", "orgName": "Mariposa Academy - Indianapolis"}, "mood": {"momentType": "mood", "comment": "Having trouble falling asleep ", "time": "10:40 am", "date": "3/13/2019", "sortStamp": 1552488000000, "moodLevel": "SoSo", "momentTypePretty": "<PERSON><PERSON>", "createdAt": 1552488045839, "createdBy": "djB2aHFjG3KjJbazZ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "N4zFMsd5nez4fj92c", "orgName": "Premier"}, "ouch": {"attributionPersonId": null, "momentType": "ouch", "time": "1:47 pm", "date": "06/04/2021", "sortStamp": 1622828820000, "momentTypePretty": "Ouch", "ouchDescription": "<PERSON> hit his head on a toy and has a small red spot in the middle of his forehead.", "ouchCare": "We applied ice to the wound", "ouchContactedParent": "Email", "ouchCalledParentTime": "1:50 pm", "ouchContactedDoctor": "", "ouchNurseNotified": true, "ouchProfessionalMedication": false, "createdAt": 1622829395979, "createdBy": "nFeHjzmQQxYSAQgJh", "attributionName": "<PERSON>", "createdByPersonId": "8jgMSvrFYadoNgRxY", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "sNcyQkxnZawdtxwte", "orgName": "Mariposa Academy - Indianapolis"}, "alert": {"attributionPersonId": null, "momentType": "alert", "comment": "Reminder: We will be closed on Monday, July 19 for a teacher in-service day.", "time": "5:39 pm", "date": "07/16/2021", "sortStamp": 1626471540000, "momentTypePretty": "Notification", "alertSendTypeEmail": false, "alertSendTypeText": false, "alertSendTypePush": true, "alertShowCheckin": false, "createdAt": 1626471553840, "createdBy": "3FhP46Kjmiro3bL7G", "attributionName": "<PERSON>", "createdByPersonId": "8uAYxXbT6urWxXg3k", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["vyxDKuFigFGRttBZS", "RYetWpMJhsC3BcPjE", "69BREkBxEe8an4TbA", "9W9bZ5DJGF7LWaLmJ", "N7Yynvhs9pqBEiR92", "BgEJcyPDDPPGT97w5", "y6Lp9DaDAKZP6WZDr", "Ao67WMf2Gi3GfW8Ro", "pCHFhtbNwq7dG4eHG", "vafkMjcEs8qLZyfTr", "hCL4wD9HqAbtttGzF", "oswWok2yYE5Xy4eXt", "N5yn3DbpjCMdGgAJR", "J8uD8wR7i9zycX3aR", "R37M7tvesRJLcTskH", "TKw3K4856H8Et7xNx", "4pPTmaevgLcoWExz2", "nPdfN7LkoBQmcoujJ", "pigJgqoqwFctEEtne", "pK6oxEhjkwXyqrRmY", "ePB64hYk8NsrPxxRq", "MdKPhsKB4btnEZt6n", "JXMHjAYzsBx22h5CS", "Qhirjw7H7Lr9PccCK", "R6fKP3hHEBafxeyuq", "BaDDgKr4brvSJomZZ", "vJHZ8P3KWxwRMak7a", "LjRaTQFhkYwbztoks", "2papyMAsWKoebY55y", "GsXkcQ9izfhpNFoac", "iFtT4kfWnzd8vHan5", "PPerx9LKNChSDaHSw", "bwmKASWnBTLrmSkoS", "9zXMi5TWSNn2t3rYR", "veLKDqRsKQibwwtDj", "vyyGJsucZimKjkxJa", "wd39xoosTnzjsGb6c", "8uAYxXbT6urWxXg3k", "WgtTaSFgA8LXY49KB", "MZLqugrBbXT6b93aF", "Ko2dpdjhLtwTP6HHs", "fYEgtG9KzZakDD8Lq", "eQNegjBtSNijtsheD", "9ovZqAWdGKjaRgRu7", "XKs6nQpNv7q9RYEvK", "HMKtg6wfCM7b2bn8E", "xYhHHnxhiTXFTxscg", "XHjxC6SJmt34NMGzp", "Fhrqwcq9Yw8S7FWxE", "iT5PLBudyY9FKurF7", "pyYSwFc2PJjHJKrKW", "GkqcNHheuvn8SBXvn", "MyFepQyWoycBRmBCS", "LFuMMLpNGqaNR4v78", "7A4gspSfjvrGd2bSQ", "8jgMSvrFYadoNgRxY", "yT9Zeaj2EzH85MQoj", "ZoS2Y8wwSBNwrJkRs", "gHoZrjN9f6LHzEegS", "FfzHpuRYnX5nCayfS", "ALszCkiMER4Yzujmz", "EYCwPvkG5Zd23hsSZ", "nmQ5Lmv6S6aX8XhxC", "gijNLLWJbjzikeMWz", "qTLvGki7YvdTQWp8Q", "2Sd4utZGjpDQeut64", "wSTzopnnwsbYWwsri", "bgYPrFT6jnRbzwpZz", "xdPGHYnY4LDTW5uaK", "jcRkB6PBCFNRS9asn", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "ch2ZhHu3h7w35zSWB", "LjANQKbvYfGtThZs5", "q4cwWG8DpsDjmmnL6", "LBDaRNzLqg9NRGXW6", "5KMccvT6pukro9Ty5", "rAaxA8yPKFYDjrzJk", "CofTFp5PRggvz477E", "QcNdA3bpdZXmRWujH", "C89A4qgWiHB67qGR2", "DkDL5e5jELZBbv6PT", "ddYRmKZd2n79NiakF", "gGyQF3jYMbF7bp2cF", "TTbkpM3mxsnNEzeN6", "6aguBjjQNCMXPnse8", "J5wzhMne96f5uFxMY", "iXgWLbg5GDbdTB2Bw", "D5R4qkBKZkKejEKDB", "8owtFFW4jjg5QQvRZ", "PwyemB257dXFc45n7", "XyD2Hw6RfqegfeMtY", "iT9ZbCgL9ZbPwzyBS", "GHhRHxXwKkAQPSTRH", "gvPNP3DyDif4opnia", "7rjCurgW62RebGjPY", "pyNcuF7Jm7GNehmPz", "u5s56Y8CswdDjh2GP", "f2kLwWFwL4mfWpJCK", "tyXnH6RLpnqNcHf6Y", "QxTFrRwFCXF3pfaao", "ACS7zGZzLsPv65y39", "PKNsmS5hNRZFvtNgp", "27WKMANJrmX6JwMXq", "HE5MvfRwX4wqrX3Tv"], "_id": "D5MDbP8P8XhjujeH7", "orgName": "Mariposa Academy - Indianapolis"}, "learning": {"attributionPersonId": null, "momentType": "learning", "comment": "<PERSON> is having so much fun with the play kitchen!", "time": "10:33 am", "date": "2/26/2021", "sortStamp": 1614353580000, "momentTypePretty": "Learning", "createdAt": 1614353681686, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/PFvFWq8uSbLZfBhCT/u6FwWcoBHCGpRMAkv", "mediaToken": "u6FwWcoBHCGpRMAkv", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/PFvFWq8uSbLZfBhCT/u6FwWcoBHCGpRMAkv"}], "_id": "ZEes6wuRxtpQYkJ9z", "orgName": "Mariposa Academy - Indianapolis"}, "illness": {"momentType": "illness", "time": "9:01 am", "date": "10/02/2019", "sortStamp": 1570021260000, "momentTypePretty": "Illness", "illnessSymptoms": ["Fever (101 degrees or higher)"], "createdAt": 1570028501393, "createdBy": "fPsbbX4pjfL7SinqK", "createdByPersonId": "8zqrq2CEZfdEioAxR", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "dvGYFEHEYBdf5pZGh", "orgName": "Bethlehem Township"}, "checkout": {"attributionPersonId": null, "momentType": "checkout", "comment": "Checked out of Ready-2-<PERSON><PERSON>", "time": "1:13 pm", "date": "07/27/2021", "sortStamp": 1627405980000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "sHt74vgNk88AJKvKk"}, "behaviorChild": {"momentType": "<PERSON><PERSON><PERSON><PERSON>", "time": "4:05 pm", "date": "1/18/2020", "sortStamp": 1579381500000, "momentTypePretty": "Behavior", "isDynamic": true, "momentDefinitionId": "vkqzWHFHdKqvP86TE", "dynamicFieldValues": {"followedDirections": "mostoftheTime"}, "createdAt": 1579381579302, "createdBy": "v4x4cY4Tcv3NPGFH3", "createdByPersonId": "RYetWpMJhsC3BcPjE", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks", "R37M7tvesRJLcTskH", "R6fKP3hHEBafxeyuq", "Qhirjw7H7Lr9PccCK", "MdKPhsKB4btnEZt6n"], "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/fPjxoi7uKvBGJd7zF", "mediaToken": "fPjxoi7uKvBGJd7zF", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/fPjxoi7uKvBGJd7zF"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/5hy7FsgAbBSCFXzp6", "mediaToken": "5hy7FsgAbBSCFXzp6", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/5hy7FsgAbBSCFXzp6"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/cKjkQYjiuJtbpesWv", "mediaToken": "cKjkQYjiuJtbpesWv", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/cKjkQYjiuJtbpesWv"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/x5uXiYTibK4skv24D", "mediaToken": "x5uXiYTibK4skv24D", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/x5uXiYTibK4skv24D"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/WRo5mTKuADtT5oMnu", "mediaToken": "WRo5mTKuADtT5oMnu", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/v4x4cY4Tcv3NPGFH3/WRo5mTKuADtT5oMnu"}], "_id": "RoRWBo4oYeRMpXjAc", "orgName": "Mariposa Academy"}, "checkin": {"attributionPersonId": null, "momentType": "checkin", "comment": "Checked into Ready-2-<PERSON><PERSON>", "time": "7:45 am", "date": "07/29/2021", "sortStamp": 1627559100000, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "kC4gKgWNzmKZc2HGm"}, "portfolio": {"attributionPersonId": null, "momentType": "portfolio", "time": "2:24 pm", "date": "11/12/2020", "sortStamp": 1605209040000, "momentTypePretty": "Portfolio", "portfolioCurriculumId": "uoh6QCXiGQYA7ttdm", "portfolioCurriculum": {"_id": "uoh6QCXiGQYA7ttdm", "headline": "Singing the Red Song", "message": "Singing the red song\r\n\r\nR-E-D Red\r\nR-E-D Red\r\nThat spells red\r\nThat spells red\r\nFiretrucks are red\r\nStop signs are red, too\r\nR-E-D\r\nR-E-D", "scheduledDate": 1605157200000, "selectedGroups": ["zvWiPSxhkgGBvq7A6"], "selectedStandards": ["Lightbridge|69c57e4ec976f86d7998f436c66c4c7ffde87dd4", "Lightbridge|f2a3f3a2327e343e77f13c587f2d2e255209c88a", "Lightbridge|e447bc5b42f4c1636f7ea0983d1746ece5338060", "Lightbridge|828748a7bf1be0f294527a10a47c56ac43f51adf"], "selectedTypes": ["Preschool: Music and Movement"], "selectedAgeGroup": "3 years - 4 years", "curriculumThemeId": "Q5N62uqHw6Gj26R2a", "originalCurriculumId": "hX2KYCiKsG2bPsv74", "orgId": "nTvbx24M2dbM9w6tu", "createdBy": "3FhP46Kjmiro3bL7G", "createdAt": 1605201499063, "mediaFiles": []}, "createdAt": 1605209108361, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "doXcwFg3nkLMzqZeR", "orgName": "Mariposa Academy - Indianapolis"}, "covidHealth": {"attributionPersonId": null, "momentType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "1:33 pm", "date": "6/26/2020", "sortStamp": 1593192780000, "momentTypePretty": "Health Check", "isDynamic": true, "momentDefinitionId": "hCsLem9rTw7FFa5KB", "dynamicFieldValues": {"temperature": "98.9", "newCough": false, "lossAppetite": false, "noneAbove": true}, "createdAt": 1593193068395, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "pEbejE6AtFJaiSR9C", "orgName": "Mariposa Academy - Indianapolis"}, "medical": {"attributionPersonId": null, "momentType": "medical", "time": "10:13 am", "date": "7/29/2021", "sortStamp": 1627567980000, "momentTypePretty": "Medical", "medicalMedicationId": "48JrKaxMx2oYvpN9B", "medicalMedication": {"_id": "48JrKaxMx2oYvpN9B", "name": "Antibiotic", "dosage": "1 tablet", "frequencyType": "everyhours", "durationType": "ongoing", "notes": "", "frequencyAmount": 4, "frequencyDescription": "Every 4 hours", "durationDescription": "Ongoing", "medicationDescription": "Antibiotic (1 tablet - Every 4 hours)"}, "createdAt": 1627568510525, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "eoj8HdA6oWRXnRkRc", "orgName": "Mariposa Academy - Indianapolis"}, "wonderlandToileting": {"attributionPersonId": null, "momentType": "wonderlandToileting", "time": "5:50 pm", "date": "4/19/2021", "sortStamp": 1618869000000, "momentTypePretty": "Toileting", "isDynamic": true, "momentDefinitionId": "6kssTbqNztFAvML6W", "dynamicFieldValues": {"details": "<PERSON><PERSON>", "additionalDetails": "In Diaper"}, "createdAt": 1618869026441, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["LjRaTQFhkYwbztoks"], "_id": "PLwyumuJ8B8wus4Wz", "orgName": "Mariposa Academy - Indianapolis"}, "safety": {"attributionPersonId": null, "momentType": "safety", "time": "3:18 pm", "date": "6/8/2021", "sortStamp": 1623179880000, "momentTypePretty": "Safety", "isDynamic": true, "momentDefinitionId": "Ksrw9M733c4nHq9pL", "dynamicFieldValues": {"safetyActivityTwo": "Fire Drill"}, "createdAt": 1623179908986, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["MdKPhsKB4btnEZt6n", "LjRaTQFhkYwbztoks", "WgtTaSFgA8LXY49KB"], "_id": "AmGioGAEpKzjqWCCo", "orgName": "Mariposa Academy - Indianapolis"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "jRu6M82az5g7xqr4r", "createdBy": "69BREkBxEe8an4TbA", "createdAt": 1648584734067, "targetPersonId": "LjRaTQFhkYwbztoks", "sourcePersonId": "69BREkBxEe8an4TbA", "_id": "xfsdPLgsBxBwdBdtt"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "timeline_like", "detail": "comment", "momentId": "jRu6M82az5g7xqr4r", "createdBy": "SYSTEM", "createdAt": *************, "targetPersonId": "LjRaTQFhkYwbztoks", "sourcePersonId": "R3WcHXYCh6zfrwc2o", "_id": "mY8FcFgLC2DHAcPTP"}], "billing": {"enrolledPlans": [{"_id": "Ggf47JteryXeZHujj", "planDetails": {"_id": "Ggf47JteryXeZHujj", "description": "Infant Full Time", "amount": 240, "type": "plan", "frequency": "weekly", "category": "tuition", "ledgerAccountName": "1112"}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "expirationDate": *************, "enrollmentForecastStartDate": *************}, {"_id": "w3djzJDQyPafET8Jy", "planDetails": {"_id": "w3djzJDQyPafET8Jy", "description": "Threes (Weekly)", "amount": 150, "type": "plan", "frequency": "weekly"}, "enrollmentDate": *************, "allocations": [], "createdAt": *************, "enrollmentForecastEndDate": null, "enrollmentForecastStartDate": null, "expirationDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "ZdkhmaEScuuboPFbH"}, {"_id": "9AfMyqvGT27WdRhMG", "planDetails": {"_id": "9AfMyqvGT27WdRhMG", "description": "Frog<PERSON> (2-3)", "amount": 100, "type": "plan", "frequency": "monthly"}, "enrollmentDate": *************, "allocations": [], "createdAt": *************}], "lastInvoiced": *************, "pendingCharges": [], "invoiced": 0, "lastBalanceNoticeSentAt": *************, "lastInvoicedDaily": 1584694959665, "billingNotes": "Security Deposit paid via Quickbooks on 2/1/2017 of $500 (Quickbooks invoice #123)"}, "lastInformedArrival": {"source": "familyCheckin", "createdAt": 1622644114380, "checkedInById": "kkGCnfhZQ2o67NS8S", "fieldValues": {"attending": "Yes", "temperatureChild": "99", "temperatureParent": "99", "temperatureHigher": "No", "feverReducingMedication": "No", "experiencingTwoSymptoms": "No", "experiencingOneSymptom": "No", "closeContact": "No", "householdExperiencingSymptoms": "No"}, "absent": false}, "foodSubsidy": "Free", "cacfpSubsidy": "Free", "documentItems": {"veW5TkxEjdcKYrA3d": {"createdAt": 1564075223196, "createdByPersonId": "kkGCnfhZQ2o67NS8S", "createdByUserId": "pAaSixvceRR8JpnMM", "token": "rD7ozMNb1nawK3Co9gKi", "repositoryKey": "nTvbx24M2dbM9w6tu/pAaSixvceRR8JpnMM/rD7ozMNb1nawK3Co9gKi", "approvedAt": 1564075286264, "approvedByPersonId": "dXKLkP4bNp5WofuQC", "approvedByUserId": "fKriuC2Y3FrWJD2uR"}, "9iyS3mqRcSEMKrL5f": {"createdAt": 1564075242032, "createdByPersonId": "kkGCnfhZQ2o67NS8S", "createdByUserId": "pAaSixvceRR8JpnMM", "token": "ioJdWrMde0JcWiF3pncH", "repositoryKey": "nTvbx24M2dbM9w6tu/pAaSixvceRR8JpnMM/ioJdWrMde0JcWiF3pncH"}, "JgdABjAgtG3gmH9zw": {"createdAt": 1566399481668, "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdByUserId": "fKriuC2Y3FrWJD2uR", "token": "60213mMXiHDtTeDwOhuH", "repositoryKey": "nTvbx24M2dbM9w6tu/fKriuC2Y3FrWJD2uR/60213mMXiHDtTeDwOhuH", "approvedAt": 1566399626919, "approvedByPersonId": "dXKLkP4bNp5WofuQC", "approvedByUserId": "fKriuC2Y3FrWJD2uR"}, "jDRyHfDs27ufKwMFy": {"createdAt": 1570637219950, "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdByUserId": "fKriuC2Y3FrWJD2uR", "token": "TG8qT2hN7DEgrr96aoJg", "repositoryKey": "nTvbx24M2dbM9w6tu/fKriuC2Y3FrWJD2uR/TG8qT2hN7DEgrr96aoJg", "approvedAt": 1576863956196, "approvedByPersonId": "RYetWpMJhsC3BcPjE", "approvedByUserId": "v4x4cY4Tcv3NPGFH3"}, "vmFSnBANrREvnqvJY": {"createdAt": 1599238807978, "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdByUserId": "3FhP46Kjmiro3bL7G", "token": "DQnkbEfLeUcGHaTgi2NU", "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/DQnkbEfLeUcGHaTgi2NU", "approvedAt": 1599238858291, "approvedByPersonId": "8uAYxXbT6urWxXg3k", "approvedByUserId": "3FhP46Kjmiro3bL7G"}, "qJJSub5tdxfdQu3ys": {"createdAt": 1612809543970, "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdByUserId": "3FhP46Kjmiro3bL7G", "token": "enwVHuCylZef4PE2zrXF", "repositoryKey": "nTvbx24M2dbM9w6tu/3FhP46Kjmiro3bL7G/enwVHuCylZef4PE2zrXF", "approvedAt": 1599239568116, "approvedByPersonId": "8uAYxXbT6urWxXg3k", "approvedByUserId": "3FhP46Kjmiro3bL7G"}, "SW9EKzRbsMHQd6Jh9": {"templateOptionResult": {"action": "ack", "date": 1603920549641, "personId": "kkGCnfhZQ2o67NS8S", "personName": "<PERSON>is"}}, "PGCGpWnC2pZSugaqK": {"createdAt": 1626965517600, "createdByPersonId": "kkGCnfhZQ2o67NS8S", "createdByUserId": "pAaSixvceRR8JpnMM", "repositoryKey": "nTvbx24M2dbM9w6tu/pAaSixvceRR8JpnMM/DiibrkP7UX3gu7rFpQn0", "token": "DiibrkP7UX3gu7rFpQn0"}}, "allergies": "Peanuts", "deactivatedAt": 1620064345310, "deactivationReason": "Withdrawn - Relocation", "inActive": false, "sex": "Male", "annualPhysicalDate": "", "physicianPhone": "5633702176", "nickname": "", "immunizationEntries": [{"_id": "6uoTmkiAxxFKrt72M", "type": "RV", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591803977773, "date": 1537761600000}, {"_id": "2YePiTALucfFMDHtG", "type": "RV", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591803999437, "date": 1543035600000}, {"_id": "YWqwowGYrEFWgBt8j", "type": "RV", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804013348, "date": 1548306000000}, {"_id": "jd7EyLgjsv6RaN9u8", "type": "DTaP", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804031916, "date": 1537761600000}, {"_id": "HRwfZoByhWyNEqdJX", "type": "DTaP", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804041565, "date": 1543035600000}, {"_id": "GgfWBX8acjf6BYgvY", "type": "DTaP", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804050113, "date": 1548306000000}, {"_id": "zWNzKcDZjGSfGG8Du", "type": "DTaP", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804060145, "date": 1579842000000}, {"_id": "veHMsSW6KdjdQvzMR", "type": "Hib", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804074653, "date": 1537761600000}, {"_id": "TJjpX2Dqgudsndk9v", "type": "Hib", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804083174, "date": 1543035600000}, {"_id": "zGF7z3nwme3jXQjjx", "type": "Hib", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804089908, "date": 1548306000000}, {"_id": "JmuNqZWfyA9QSx3Qb", "type": "Hib", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804101284, "date": 1571889600000}, {"_id": "nhmJDnj6R8cMB932u", "type": "PCV13", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804114945, "date": 1537761600000}, {"_id": "su4hQybwJPF8RaiJQ", "type": "PCV13", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804122426, "date": 1543035600000}, {"_id": "fYg2322ksx2Nn2tEv", "type": "PCV13", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804165169, "date": 1548306000000}, {"_id": "pz5zmXBCzaCCqKbsc", "type": "PCV13", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804174467, "date": 1571889600000}, {"_id": "oFqrZKfJ9KR2CNPqy", "type": "IPV", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804190833, "date": 1537761600000}, {"_id": "vrimefzeNLxQWQrDt", "type": "IPV", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804200117, "date": 1543035600000}, {"_id": "C3hgmoGZA26HvSAEb", "type": "MMR", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804215325, "date": 1571889600000}, {"_id": "rrGG9h3wSk7tEowQC", "type": "VAR", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804225840, "date": 1571889600000}, {"_id": "sM6wACSWLGFogbzmA", "type": "HepB", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804263707, "date": 1532491200000}, {"_id": "yBBFar64DhEGTB4wc", "type": "HepB", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1591804277280, "date": 1537761600000}, {"_id": "HzjQwkCcyrPk7EE2T", "type": "IIV", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1606168332219, "date": 1548306000000}, {"_id": "smFM5DLXtmnSTnJ4E", "type": "IIV", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1606168364576, "date": 1579842000000}, {"_id": "jbkPEXafDpdzrHz8k", "type": "UCHR", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1607454273689, "date": 1590984000000}, {"_id": "73ab4mCKxNmSxSxAo", "type": "Annual Physical", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1607454319315, "immunizationDefinitionId": "tNPDGNDQqhvX9mADH", "date": 1561953600000}, {"_id": "4RiSJCnXNYGWEQM3H", "type": "Annual Physical", "createdByPersonId": "dXKLkP4bNp5WofuQC", "createdAt": 1607454330329, "immunizationDefinitionId": "tNPDGNDQqhvX9mADH", "date": 1596254400000}, {"_id": "bmBAMfbzJn3xbpRBg", "type": "Annual Physical", "createdByPersonId": "8uAYxXbT6urWxXg3k", "createdAt": 1612560317561, "immunizationDefinitionId": "tNPDGNDQqhvX9mADH", "date": 1612501200000}], "classList": {"defaultDays": {"M": ".5", "T": ".5", "W": ".5", "R": ".5", "F": ".5"}, "transitionDays": {"M": "1", "T": "1", "W": "1", "R": "1", "F": "1"}}, "transitionGroupDate": 1617595200000, "transitionGroupId": "aah6pMsvqyraeLK9E", "medicationEntries": [{"_id": "Mr6PAkGCT2nSTznyM", "name": "Sunscreen", "dosage": "", "frequencyType": "other", "durationType": "ongoing", "notes": ""}, {"_id": "48JrKaxMx2oYvpN9B", "name": "Antibiotic", "dosage": "1 tablet", "frequencyType": "everyhours", "durationType": "ongoing", "notes": "", "frequencyAmount": 4}], "enrollmentDate": 1541044800000, "primaryFamily": "", "payer": "Scholarship", "backUpCare": "", "backUpCareReferringPartner": "", "billingNotes": {"Billing Notes": ""}, "curriculum": "", "notes": "", "notesPrivate": "", "physicianName": "", "quickList": "", "siblings": "", "previousClassroomGroupId": null, "birthday": 1549602000000, "documentExemptions": ["MLNodR54ypneS6gnf"], "documentAssignments": ["qJJSub5tdxfdQu3ys"], "presenceLastGroupId": "zvWiPSxhkgGBvq7A6", "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "preferredSchedule": "", "standardOutlook": {"allergies": "", "importantNotes": ""}, "withdrawDate": *************}, {"_id": "7EF3Cj2TuGb539fca", "firstName": "<PERSON><PERSON>", "lastName": "Day", "designations": [], "defaultGroupId": "zvWiPSxhkgGBvq7A6", "type": "person", "createdBy": "nFeHjzmQQxYSAQgJh", "createdAt": *************, "orgId": "nTvbx24M2dbM9w6tu", "inActive": false, "billing": {"enrolledPlans": [{"_id": "fa5XsPjDR4fHH3i3g", "planDetails": {"_id": "fa5XsPjDR4fHH3i3g", "description": "Infant - Full Time (Monthly)", "amount": 1400, "type": "plan", "frequency": "monthly", "category": "tuition", "ledgerAccountName": "4321"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 500, "amountType": "dollars", "reimbursementType": "ccdf", "allocationDescription": "Reimbursable: CCDF", "id": "fSD2gwbAt6pi5fr2X"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": null, "expirationDate": null, "overrideRate": null, "updatedAt": *************, "updatedBy": "3FhP46Kjmiro3bL7G"}], "pendingCharges": [], "lastInvoiced": *************}, "checkInGroupId": null, "checkInGroupName": null, "checkedIn": false, "checkedInById": "gDHs3CQxYCe7inBgR", "checkedInOutTime": *************, "lastInformedArrival": {"fieldValues": {"covidScreeningQuestion": "No"}, "source": "pinCode", "checkedInById": "Ga3v5wB2hno8DC9ho", "createdAt": *************}, "presenceLastGroupId": null, "previousClassroomGroupId": "zvWiPSxhkgGBvq7A6", "lastInteractionDate": *************, "lastMoment": {"attributionPersonId": null, "momentType": "food", "comment": "Kiddos are enjoying their lunch!", "time": "12:59 pm", "date": "12/28/2022", "sortStamp": *************, "foodType": "Lunch", "foodItems": [{"name": "Grilled Cheese", "amount": "All"}, {"name": "Green Beans", "amount": "All"}, {"name": "Milk", "amount": "All"}, {"name": "Peaches", "amount": "All"}], "momentTypePretty": "Food", "attributionName": "<PERSON> (Staff)", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["oswWok2yYE5Xy4eXt", "Qhirjw7H7Lr9PccCK", "LjRaTQFhkYwbztoks", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "7EF3Cj2TuGb539fca", "2b2FKLAi4vE8Ju5Nr"], "_id": "v9cdqqEbByrRMrD7w"}, "lastMomentByType": {"potty": {"attributionPersonId": null, "momentType": "potty", "time": "1:54 pm", "date": "11/8/2022", "sortStamp": 1667933640000, "momentTypePretty": "<PERSON><PERSON>", "createdAt": 1667916776893, "createdBy": "PFvFWq8uSbLZfBhCT", "attributionName": "<PERSON>", "createdByPersonId": "N7Yynvhs9pqBEiR92", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["oswWok2yYE5Xy4eXt", "N5yn3DbpjCMdGgAJR", "pigJgqoqwFctEEtne", "Qhirjw7H7Lr9PccCK", "LjRaTQFhkYwbztoks", "WgtTaSFgA8LXY49KB", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "7EF3Cj2TuGb539fca", "stZ4bfetCGQkpyRLz"], "createdByPersonGroupId": "zvWiPSxhkgGBvq7A6", "createdByPersonCheckInGroupId": "zvWiPSxhkgGBvq7A6", "_id": "orxZfYWYPwgBpzK78", "orgName": "Mariposa Academy - Indy"}, "sleep": {"attributionPersonId": null, "metaMomentId": "j9whMv2NFaEx74Suo", "momentType": "sleep", "time": "4:40 pm", "date": "12/5/2022", "sortStamp": 1670276400000, "distressedSleepCheck": true, "sleepCheckInterval": 3, "momentTypePretty": "Sleep", "createdAt": 1670276457240, "createdBy": "3hmtpPctvvSgNbM7E", "attributionName": "<PERSON>", "createdByPersonId": "2papyMAsWKoebY55y", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["7EF3Cj2TuGb539fca"], "createdByPersonGroupId": "zvWiPSxhkgGBvq7A6", "createdByPersonCheckInGroupId": "zvWiPSxhkgGBvq7A6", "_id": "eMWnB37E8rh29n8GJ", "orgName": "Mariposa Academy - Indy"}, "alert": {"attributionPersonId": null, "momentType": "alert", "comment": "Did you receive this attachment?", "time": "1:59 pm", "date": "05/06/2022", "sortStamp": 1651859940000, "momentTypePretty": "Notification", "alertSendTypeEmail": true, "alertSendTypeText": false, "alertSendTypePush": false, "alertShowCheckin": false, "createdAt": 1651859956969, "createdBy": "sJdMTQmmky9aYtk9J", "attributionName": "<PERSON>", "createdByPersonId": "r7CSA2F5CFiYGx7rK", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["N7Yynvhs9pqBEiR92", "pCHFhtbNwq7dG4eHG", "oswWok2yYE5Xy4eXt", "nPdfN7LkoBQmcoujJ", "bgYPrFT6jnRbzwpZz", "iQtKGb5uuQwFR4hsm", "RxMdtqczec7mxh48u", "Xe7b9HinsGX2tc2My", "eYjWqSK3JvpAsmrZA", "R7XXJMKznFG9eavht", "bP2eNPc3QMAnn3jbx", "FwNyYDmR5KGFXjYrr", "7EF3Cj2TuGb539fca", "8AGXmyDoDiCNrbXnY"], "mediaFiles": [{"mediaUrl": null, "mediaToken": "2KFoFystWGcM691Alf7I", "mediaFileType": "application", "mediaPath": "nTvbx24M2dbM9w6tu/sJdMTQmmky9aYtk9J/2KFoFystWGcM691Alf7I", "fileName": "School Closures_sample.pdf"}], "_id": "nCB3kpaDRXNfRihgj", "orgName": "Mariposa Academy - Indy"}, "food": {"attributionPersonId": null, "momentType": "food", "comment": "Kiddos are enjoying their lunch!", "time": "12:59 pm", "date": "12/28/2022", "sortStamp": *************, "foodType": "Lunch", "foodItems": [{"name": "Grilled Cheese", "amount": "All"}, {"name": "Green Beans", "amount": "All"}, {"name": "Milk", "amount": "All"}, {"name": "Peaches", "amount": "All"}], "momentTypePretty": "Food", "attributionName": "<PERSON> (Staff)", "modifiedByName": "<PERSON>", "modifiedBy": "8jgMSvrFYadoNgRxY", "taggedPeople": ["oswWok2yYE5Xy4eXt", "Qhirjw7H7Lr9PccCK", "LjRaTQFhkYwbztoks", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "7EF3Cj2TuGb539fca", "2b2FKLAi4vE8Ju5Nr"], "_id": "v9cdqqEbByrRMrD7w"}, "hydration": {"attributionPersonId": null, "momentType": "hydration", "time": "1:00 pm", "date": "10/19/2022", "sortStamp": 1666198800000, "momentTypePretty": "Hydration", "isDynamic": true, "momentDefinitionId": "kJzJszG9y6Shhfqzt", "dynamicFieldValues": {}, "createdAt": 1666202415457, "createdBy": "47vfjpEDHaKKqoHdw", "attributionName": "<PERSON> (Staff)", "createdByPersonId": "q7wkAz3R9JhXxysxZ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["7EF3Cj2TuGb539fca"], "createdByPersonGroupId": "zvWiPSxhkgGBvq7A6", "createdByPersonCheckInGroupId": "zvWiPSxhkgGBvq7A6", "_id": "pZWYNGs68jsSDhkMu", "orgName": "Mariposa Academy - Indy"}, "learning": {"attributionPersonId": null, "momentType": "learning", "comment": "<PERSON><PERSON> is having so much fun reading about water and “popping” the water droplets in her book!", "time": "2:10 pm", "date": "10/26/2022", "sortStamp": 1666807800000, "momentTypePretty": "Learning", "learningCurriculumId": "cNojFTiivaPf5WjGn", "learningCurriculum": {"_id": "cNojFTiivaPf5WjGn", "headline": "Water is important", "message": "Learnigng about why water is important", "scheduledDate": *************, "selectedGroups": ["zvWiPSxhkgGBvq7A6"], "selectedStandards": ["Lightbridge|1efcd950da10f5a35f10c20c87ce36c3123cc29a"], "selectedTypes": ["Infant: Outdoor Opportunities"], "selectedAgeGroup": "2.5 years - 3.5 years", "curriculumThemeId": "NsyCQvgmgANAAs3nX", "curriculumBankSourceId": "9263qHkkpA5v3qRe9", "internalNotes": "", "internalLink": "", "orgId": "nTvbx24M2dbM9w6tu", "createdBy": "3FhP46Kjmiro3bL7G", "createdAt": *************, "mediaFiles": []}, "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "N7Yynvhs9pqBEiR92", "taggedPeople": ["7EF3Cj2TuGb539fca"], "_id": "5yAGvwNZdCvRMBFBi"}, "comment": {"attributionPersonId": null, "momentType": "comment", "comment": "Having a great day!", "time": "2:40 pm", "date": "1/5/2023", "sortStamp": *************, "momentTypePretty": "Comment", "createdAt": *************, "createdBy": "3hmtpPctvvSgNbM7E", "attributionName": "<PERSON>", "createdByPersonId": "2papyMAsWKoebY55y", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["oswWok2yYE5Xy4eXt", "Qhirjw7H7Lr9PccCK", "LjRaTQFhkYwbztoks", "WgtTaSFgA8LXY49KB", "7EF3Cj2TuGb539fca", "stZ4bfetCGQkpyRLz"], "createdByPersonGroupId": "zvWiPSxhkgGBvq7A6", "createdByPersonCheckInGroupId": "zvWiPSxhkgGBvq7A6", "_id": "y8yFPpj9YcbW6okzf", "orgName": "Mariposa Academy - Indy"}, "activity": {"attributionPersonId": null, "momentType": "activity", "comment": "Practicing our hula hoop skills!", "time": "12:55 pm", "date": "12/5/2022", "sortStamp": 1670262900000, "activityType": "Gross Motor", "activityEngagement": "Active", "momentTypePretty": "Activity", "createdAt": 1670263010326, "createdBy": "47vfjpEDHaKKqoHdw", "attributionName": "<PERSON> (Staff)", "createdByPersonId": "q7wkAz3R9JhXxysxZ", "orgId": "nTvbx24M2dbM9w6tu", "taggedPeople": ["oswWok2yYE5Xy4eXt", "Qhirjw7H7Lr9PccCK", "LjRaTQFhkYwbztoks", "uHeWbNQmFmdZdGL6k", "QEWjwEM4nEnYPzqJ6", "7EF3Cj2TuGb539fca", "2b2FKLAi4vE8Ju5Nr"], "createdByPersonGroupId": "zvWiPSxhkgGBvq7A6", "createdByPersonCheckInGroupId": "zvWiPSxhkgGBvq7A6", "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/nTvbx24M2dbM9w6tu/47vfjpEDHaKKqoHdw/x87mtnqdgueCHv6Xs", "mediaToken": "x87mtnqdgueCHv6Xs", "mediaFileType": "image", "mediaPath": "nTvbx24M2dbM9w6tu/47vfjpEDHaKKqoHdw/x87mtnqdgueCHv6Xs"}], "_id": "XkpKAWFydX9AMfcSq", "orgName": "Mariposa Academy - Indy"}}, "engagements": [{"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "y8yFPpj9YcbW6okzf", "createdBy": "2papyMAsWKoebY55y", "createdAt": 1672947680091, "targetPersonId": "7EF3Cj2TuGb539fca", "sourcePersonId": "2papyMAsWKoebY55y", "_id": "dsZ4LryAQgXeesJqB"}, {"orgId": "nTvbx24M2dbM9w6tu", "type": "app", "subType": "created_moment", "detail": "food", "momentId": "v9cdqqEbByrRMrD7w", "createdBy": "8jgMSvrFYadoNgRxY", "createdAt": 1673230708871, "targetPersonId": "7EF3Cj2TuGb539fca", "sourcePersonId": "8jgMSvrFYadoNgRxY", "_id": "WCChfbAHza5dFy94R"}], "avatarPath": "nTvbx24M2dbM9w6tu/PFvFWq8uSbLZfBhCT/7xdaXPZgLpAsWNFSb", "avatarToken": "7xdaXPZgLpAsWNFSb"}]