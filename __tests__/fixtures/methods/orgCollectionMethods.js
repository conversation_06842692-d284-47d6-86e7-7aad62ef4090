import {
    BillingFrequencies,
    ledgerAccountTypes,
    lineItemTypes,
    SCALED_PLAN_FREQUENCIES
} from '../../../lib/constants/billingConstants';
import { AvailableCustomizations } from "../../../lib/customizations";
import { DiscountTypes } from "../../../lib/discountTypes";
import moment from 'moment-timezone';
import _ from '../../../lib/util/underscore';

export class OrgCollectionMethods {

    static getAccountLedgerCodes() {
        return this.billing && this.billing.ledgerAccountCodes;
    }

    static getTimezone() {
        return (this.timezone || 'America/New_York');
    }

    static hasCustomization(customizationName) {
        if (customizationName === "moments/potty/enabled" && (!this.customizations || this.customizations["moments/potty/enabled"] === undefined)) return true;
        if (customizationName === "moments/sleep/enabled" && (!this.customizations || this.customizations["moments/sleep/enabled"] === undefined)) return true;
        if (customizationName === "moments/food/enabled" && (!this.customizations || this.customizations["moments/food/enabled"] === undefined)) return true;
        if (customizationName === "moments/alert/enabled" && (!this.customizations || this.customizations["moments/alert/enabled"] === undefined)) return true;

        if (_.contains(["moments/sleep/showEndSleepButton"], customizationName)) return true;
        if (customizationName === "moments/learning/enabled" && this.customizations &&
            (this.customizations["moments/learning/enabled"] ||
                (this.customizations["moments/learning/enabled"] === undefined && (this.customizations["modules/curriculum/hidden"] === undefined || !this.customizations["modules/curriculum/hidden"]))
            )
        )
            return true;
        if (this.customizations && this.customizations[customizationName])
            return this.customizations[customizationName];
        else
            return false;
    }

    static availableDiscountTypes(showArchived, showNoManualEntry) {
        const discountTypes = [];
        if (this.hasCustomization(AvailableCustomizations.PLAN_BUNDLES) && showNoManualEntry) {
            discountTypes.push({ type: DiscountTypes.BUNDLE, description: 'Bundle', noManualEntry: true });
        }
        if (this.hasCustomization(AvailableCustomizations.COUPON_CODES) && showNoManualEntry) {
            discountTypes.push({ type: DiscountTypes.COUPON, description: 'Coupon', noManualEntry: true });
        }
        if (this.valueOverrides && this.valueOverrides.discountTypes) {
            discountTypes.push(...this.valueOverrides.discountTypes.filter(dt => showArchived || !dt.archived));
        } else {
            discountTypes.push(
                { type: "customerSpecific", description: "Customer-specific" },
                { type: "multipleFamily", description: "Multiple family" },
                { type: "scholarship", description: "Scholarship" },
                { type: "other", description: "Other" }
            );
        }
        return discountTypes;
    }

    static getMappedLedgerAccount(lineItemType, lineItem, modifiedDiscount) {
        const billingMaps = (this.billing && this.billing.billingMaps) || this.billingMaps();
        if (!billingMaps) return;

        const arAccountName = _.deep(this, "billing.billingMaps.accountsReceivable.accountName");

        if (lineItem?.originalItem?.reallocationSource) {
            const payerInfo = _.find(this.availablePayerSources(true), (p) => p.type == lineItem.originalItem.reallocationSource.source);
            return { accountName: payerInfo?.ledgerAccountName ?? '', type: ledgerAccountTypes.REVENUE, mapped: true };
        }
        else if (lineItemType === lineItemTypes.CHARGE_PLAN) {
            const planInfo = _.find(this.billing.plansAndItems, (p) => { return p._id == lineItem.enrolledPlan._id; });
            if (planInfo && planInfo.ledgerAccountName)
                return { accountName: planInfo.ledgerAccountName, type: ledgerAccountTypes.REVENUE, mapped: true };
            else if (billingMaps.otherPlanRevenue)
                return { accountName: billingMaps.otherPlanRevenue.accountName, type: ledgerAccountTypes.REVENUE };
            else
                return { accountName: "Plan Revenue", type: ledgerAccountTypes.REVENUE };
        }
        else if (lineItemType === lineItemTypes.CHARGE_ITEM) {
            const itemInfo = _.find(this.billing.plansAndItems, (p) => { return p._id == lineItem.originalItem._id; });
            if (itemInfo && itemInfo.ledgerAccountName)
                return { accountName: itemInfo.ledgerAccountName, type: ledgerAccountTypes.REVENUE, mapped: true };
            else if (billingMaps.otherPlanRevenue)
                return { accountName: billingMaps.otherItemRevenue.accountName, type: ledgerAccountTypes.REVENUE };
            else
                return { accountName: "Item Revenue", type: ledgerAccountTypes.REVENUE };
        }
        else if (lineItemType === lineItemTypes.DISCOUNT_PLAN) {
            const discountInfo = this.availableDiscountTypes(true, true).find(p => p.type === lineItem.source);
            const payerInfo = lineItem.type === "reimbursable" && this.availablePayerSources(true).find(p => p.type === lineItem.source);
            const matchedCouponCode = discountInfo?.type === "coupon" && this?.billing?.couponCodes.find(cc => cc.code === lineItem?.originalAllocation?.code);

            let discount = {};
            if (discountInfo?.ledgerAccountName) {
                discount = { accountName: discountInfo.ledgerAccountName, type: ledgerAccountTypes.DISCOUNT, mapped: true };
            } else if (lineItem.type === "reimbursable" && payerInfo?.ledgerAccountName) {
                discount = { accountName: payerInfo.ledgerAccountName, type: ledgerAccountTypes.DISCOUNT };
            } else if (lineItem.type === "reimbursable" && billingMaps.otherPayerDiscounts) {
                discount = { accountName: billingMaps.otherPayerDiscounts.accountName, type: ledgerAccountTypes.DISCOUNT };
            } else if (billingMaps.otherPlanDiscounts) {
                discount = { accountName: billingMaps.otherPlanDiscounts.accountName, type: ledgerAccountTypes.DISCOUNT };
            } else if (discountInfo?.type === "coupon" && matchedCouponCode?.ledgerCode) {
                discount = { accountName: matchedCouponCode.ledgerCode, type: ledgerAccountTypes.DISCOUNT };
            } else {
                discount = { accountName: "Plan Discounts", type: ledgerAccountTypes.DISCOUNT };
            }

            if (modifiedDiscount) {
                discount.type = "modifiedDiscount";
                discount.modifiedDiscount = lineItem.modifiedDiscount;
            }
            return discount;
        }
        else if (lineItemType === lineItemTypes.DISCOUNT_ITEM) {
            const discountInfo = _.find(this.availableDiscountTypes(true, true), (p) => { return p.type == lineItem.source; });
            if (discountInfo && discountInfo.ledgerAccountName)
                return { accountName: discountInfo.ledgerAccountName, type: ledgerAccountTypes.DISCOUNT, mapped: true };
            else if (billingMaps.otherItemDiscounts)
                return { accountName: billingMaps.otherItemDiscounts.accountName, type: ledgerAccountTypes.DISCOUNT };
            else
                return { accountName: "Item Discounts", type: ledgerAccountTypes.DISCOUNT };
        }
        else if (lineItemType === lineItemTypes.PAYMENT) {
            if (lineItem.payment_type == "card" || lineItem.payment_type == "bank_account")
                return { accountName: "Uncollected Funds", type: ledgerAccountTypes.PAYMENT };
            else
                return { accountName: "Credit Memo Payment", type: ledgerAccountTypes.PAYMENT_CREDIT_MEMO };
        }
        else if (lineItemType === lineItemTypes.SETTLEMENT) {
            const accountName = (billingMaps.onlinePaymentDeposits && billingMaps.onlinePaymentDeposits.accountName) ||
                (billingMaps.manualPaymentDeposits && billingMaps.manualPaymentDeposits.accountName) ||
                "Cash";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYMENT_SETTLEMENT_CASH };
        }
        else if (lineItemType === lineItemTypes.SETTLEMENT_FEE) {
            const accountName = (billingMaps.settlementFees && billingMaps.settlementFees.accountName) || "Settlement Fees";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYMENT_SETTLEMENT_FEE };
        }
        else if (lineItemType === lineItemTypes.CHARGEBACK_FEE) {
            const accountName = (billingMaps.settlementFees && billingMaps.settlementFees.accountName) || "Chargeback Fees";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYMENT_SETTLEMENT_FEE };
        }
        else if (lineItemType === lineItemTypes.PAYMENT_REFUNDED) {
            const accountName = (billingMaps.paymentRefunds && billingMaps.paymentRefunds.accountName) || "Payment Refunds";
            return { accountName: accountName, type: ledgerAccountTypes.PAYMENT_REFUNDED };
        }
        else if (lineItemType === lineItemTypes.PAYMENT_VOIDED) {
            const accountName = (billingMaps.manualPaymentDeposits && billingMaps.manualPaymentDeposits.accountName) || "Cash";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYMENT_VOIDED };
        }
        else if (lineItemType === lineItemTypes.MANUAL_DEPOSITS) {
            const accountName = (billingMaps.undepositedFunds && billingMaps.undepositedFunds.accountName) || "Undeposited Funds";
            return {glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.MANUAL_DEPOSITS, offsetAccountName: lineItem?.cashAccountName ?? null};
        }
        else if (lineItemType === lineItemTypes.CREDIT || lineItemType === lineItemTypes.PAYMENT_ADJUSTED) {
            let ledger = {};

            const payerInfo =
                (lineItem.creditReason === "reimbursable" && _.find(this.availablePayerSources(true), (p) => p.type == lineItem.creditPayerSource)) ||
                (lineItem.creditReason === "reallocation_to_payer" && _.find(this.availablePayerSources(true), (p) => p.type == lineItem.payerDestination));

            switch (lineItem.creditReason) {
                case "manual_payment":
                    ledger = { glImportIgnore: true, accountName: (billingMaps.undepositedFunds && billingMaps.undepositedFunds.accountName) || (billingMaps.manualPaymentDeposits && billingMaps.manualPaymentDeposits.accountName) || "Cash", type: ledgerAccountTypes.PAYMENT_MANUAL };
                    break;
                case "security_deposit_refund":
                    ledger = { accountName: (billingMaps.securityDepositRefunds && billingMaps.securityDepositRefunds.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.SECURITY_DEPOSIT_REFUND };
                    break;
                case "bad_debt":
                    ledger = { accountName: (billingMaps.badDebt && billingMaps.badDebt.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.WRITE_OFF_BAD_DEBT };
                    if (!billingMaps.badDebt) {
                        ledger.glImportIgnore = true;
                    }
                    break;
                case "agency_write_off":
                    ledger = { accountName: (billingMaps.agencyWriteOff && billingMaps.agencyWriteOff.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.WRITE_OFF_AGENCY };
                    if (!billingMaps.agencyWriteOff) {
                        ledger.glImportIgnore = true;
                    }
                    break;
                case "collections_write_off":
                    ledger = { accountName: (billingMaps.collectionsWriteOff && billingMaps.collectionsWriteOff.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.WRITE_OFF_COLLECTIONS };
                    if (!billingMaps.collectionsWriteOff) {
                        ledger.glImportIgnore = true;
                    }
                    break;
                case "payroll_deduction":
                    ledger = { accountName: (billingMaps.payrollDeduction && billingMaps.payrollDeduction.accountName) || (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits", type: ledgerAccountTypes.PAYROLL_DEDUCTION };
                    break;
                case "other":
                    if (lineItem.hasOwnProperty("creditLineItemIndex") && lineItem.creditLineItemOriginal) {
                        const creditLineItemOriginal = lineItem.creditLineItemOriginal;
                        const lineItemDef = _.find(this.billing.plansAndItems, (p) => {
                            return ((creditLineItemOriginal.type === "plan" && p._id == creditLineItemOriginal.enrolledPlan._id) ||
                                (creditLineItemOriginal.type === "item" && p._id == creditLineItemOriginal.originalItem._id));
                        });

                        if (lineItemDef && lineItemDef.ledgerAccountName) {
                            ledger = { accountName: lineItemDef.ledgerAccountName, type: ledgerAccountTypes.DISCOUNT };
                        }
                    } else {
                        const accountName = (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits";
                        ledger = { accountName: accountName, type: ledgerAccountTypes.DISCOUNT };
                    }
                    break;
                case "reimbursable":
                    if (payerInfo && payerInfo.ledgerAccountName) {
                        ledger = { accountName: payerInfo.ledgerAccountName, type: ledgerAccountTypes.PAYMENT_PAYER, offsetAccountName: payerInfo.cashLedgerAccountName ?? null };
                    } else if (billingMaps.otherPayerDiscounts) {
                        ledger = { accountName: billingMaps.otherPayerDiscounts.accountName, type: ledgerAccountTypes.PAYMENT_PAYER };
                    }
                    ledger.glImportIgnore = true;
                    break;
                case "reallocation_to_payer":
                    if (payerInfo && payerInfo.ledgerAccountName) {
                        ledger = { accountName: payerInfo.ledgerAccountName, type: ledgerAccountTypes.REALLOCATION_TO_PAYER };
                    }
                    break;
                default:
                    ledger = { accountName: "Deferred Revenue", type: ledgerAccountTypes.CREDIT };
                    break;
            }

            if (lineItemType === lineItemTypes.PAYMENT_ADJUSTED) {
                ledger.type = ledgerAccountTypes.PAYMENT_ADJUSTED;
            }

            return ledger;
        }

        else if (lineItemType === lineItemTypes.CREDIT_MEMO) {
            const accountName = (billingMaps.creditMemos && billingMaps.creditMemos.accountName) || "Credit Memos";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.CREDIT_MEMO };
        }
        else if (lineItemType === lineItemTypes.SECURITY_DEPOSIT_APPLIED) {
            const accountName = (billingMaps.securityDepositsLiability && billingMaps.securityDepositsLiability.accountName) || "Security Deposits Applied";
            return { accountName: accountName, type: ledgerAccountTypes.SECURITY_DEPOSIT_APPLIED };
        }
        else if (lineItemType === lineItemTypes.SECURITY_DEPOSIT_REFUND_AUTO) {
            const accountName = (billingMaps.securityDepositsLiability && billingMaps.securityDepositsLiability.accountName) || "Security Deposits Refunded";
            return { accountName: accountName, type: ledgerAccountTypes.SECURITY_DEPOSIT_REFUND_AUTO };
        }
        else if (lineItemType === lineItemTypes.UNAPPLIED_CASH_APPLIED) {
            const accountName = (billingMaps.unappliedCashApplied && billingMaps.unappliedCashApplied.accountName) || "Unapplied Cash Applied";
            return { accountName: accountName, type: ledgerAccountTypes.UNAPPLIED_CASH_APPLIED };
        }
        else if (lineItemType === lineItemTypes.CREDIT_MEMO_REFUNDED) {
            const accountName = (billingMaps.customerLiabilityPayable && billingMaps.customerLiabilityPayable.accountName) || "Customer Liability/Payable";
            return { accountName: accountName, type: ledgerAccountTypes.CREDIT_MEMO_REFUNDED };
        }
        else if (lineItemType === lineItemTypes.CREDIT_MEMO_VOID) {
            const accountName = (billingMaps.creditMemos && billingMaps.creditMemos.accountName) || "Credit Memos";
            return { glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.CREDIT_MEMO_VOID };
        }
        else if (lineItemType === lineItemTypes.WRITE_OFF_BAD_DEBT) {
            const accountName = (billingMaps.badDebt && billingMaps.badDebt.accountName) ||
                    (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits",
                payerInfo = lineItem.source && _.find(this.availablePayerSources(true), (p) => p.type == lineItem.source);
            return { accountName, type: ledgerAccountTypes.WRITE_OFF_BAD_DEBT, offsetAccountName: payerInfo?.ledgerAccountName || null };
        }
        else if (lineItemType === lineItemTypes.WRITE_OFF_AGENCY) {
            const accountName = (billingMaps.agencyWriteOff && billingMaps.agencyWriteOff.accountName) ||
                    (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits",
                payerInfo = lineItem.source && _.find(this.availablePayerSources(true), (p) => p.type == lineItem.source);
            return { accountName, type: ledgerAccountTypes.WRITE_OFF_AGENCY, offsetAccountName: payerInfo?.ledgerAccountName || null };
        }
        else if (lineItemType === lineItemTypes.WRITE_OFF_COLLECTIONS) {
            const accountName = (billingMaps.collectionsWriteOff && billingMaps.collectionsWriteOff.accountName) ||
                    (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits",
                payerInfo = lineItem.source && _.find(this.availablePayerSources(true), (p) => p.type == lineItem.source);
            return { accountName, type: ledgerAccountTypes.WRITE_OFF_COLLECTIONS, offsetAccountName: payerInfo?.ledgerAccountName || null };
        } else if (lineItemType === lineItemTypes.WRITE_OFF_BAD_DEBT_REVERSAL) {
            const accountName = (billingMaps.badDebt && billingMaps.badDebt.accountName) ||
                    (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits",
                payerInfo = lineItem.source && _.find(this.availablePayerSources(true), (p) => p.type == lineItem.source);
            return { accountName, type: ledgerAccountTypes.WRITE_OFF_BAD_DEBT_REVERSAL, offsetAccountName: payerInfo?.ledgerAccountName || null };
        }
        else if (lineItemType === lineItemTypes.WRITE_OFF_AGENCY_REVERSAL) {
            const accountName = (billingMaps.agencyWriteOff && billingMaps.agencyWriteOff.accountName) ||
                    (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits",
                payerInfo = lineItem.source && _.find(this.availablePayerSources(true), (p) => p.type == lineItem.source);
            return { accountName, type: ledgerAccountTypes.WRITE_OFF_AGENCY_REVERSAL, offsetAccountName: payerInfo?.ledgerAccountName || null };
        }
        else if (lineItemType === lineItemTypes.WRITE_OFF_COLLECTIONS_REVERSAL) {
            const accountName = (billingMaps.collectionsWriteOff && billingMaps.collectionsWriteOff.accountName) ||
                    (billingMaps.manualOtherCredits && billingMaps.manualOtherCredits.accountName) || "Other Credits",
                payerInfo = lineItem.source && _.find(this.availablePayerSources(true), (p) => p.type == lineItem.source);
            return { accountName, type: ledgerAccountTypes.WRITE_OFF_COLLECTIONS_REVERSAL, offsetAccountName: payerInfo?.ledgerAccountName || null };
        } else if (lineItemType === lineItemTypes.REALLOCATION_TO_PAYER_REVERSAL) {
            const payerInfo = _.find(this.availablePayerSources(true), (p) => p.type === lineItem.payerDestination);
            return { accountName: payerInfo?.ledgerAccountName || "Other Credits", type: ledgerAccountTypes.REALLOCATION_TO_PAYER_REVERSAL }
        } else if (lineItemType === lineItemTypes.PAYMENT_MANUAL_REFUNDED) {
            const accountName = (billingMaps.customerLiabilityPayable && billingMaps.customerLiabilityPayable.accountName) || "Customer Liability/Payable";
            return { accountName: accountName, type: ledgerAccountTypes.PAYMENT_MANUAL_REFUNDED };
        }
        else if (lineItemType === lineItemTypes.PAYER_OVERPAYMENT) {
            if (lineItem.payerOverpaymentDestination === "adjustment-account") {
                const accountName = (billingMaps.agencyOverpayment && billingMaps.agencyOverpayment.accountName) || "Agency Overpayment";
                return {accountName: accountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT};
            } else if (lineItem.payerOverpaymentDestination === "agency-refund-account") {
                const accountName = (billingMaps.agencyOverpayment && billingMaps.agencyOverpayment.accountName) || "Agency Refund";
                return {accountName: accountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT};
            } else if (lineItem.payerOverpaymentDestination === "unapplied-cash-account") {
                const accountName = (billingMaps.unappliedCash && billingMaps.unappliedCash.accountName) || "Unapplied Cash";
                return {accountName: accountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT, glImportIgnore: true };
            } else if (lineItem.payerOverpaymentDestination === "payer-credit-memo") {
                const payerInfo = lineItem.creditReason === "reimbursable" && _.find(this.availablePayerSources(true), (p) => p.type === lineItem.creditPayerSource);
                return {glImportIgnore: true, accountName: payerInfo.ledgerAccountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT_CREDIT_MEMO, offsetAccountName: payerInfo.cashLedgerAccountName ?? null};
            } else if (lineItem.payerOverpaymentDestination === "family-credit-memo") {
                const accountName = (billingMaps.creditMemos && billingMaps.creditMemos.accountName) || "Credit Memos";
                return {glImportIgnore: true, accountName: accountName, type: ledgerAccountTypes.PAYER_OVERPAYMENT_CREDIT_MEMO};
            }
        }
    }

    static availableCreditMemoTypes() {
        let creditMemoTypes = [];
        if (this.valueOverrides && this.valueOverrides.creditMemoTypes)
            creditMemoTypes = creditMemoTypes.concat(this.valueOverrides.creditMemoTypes);
        else {
            const excl = this.billing?.excludedManualPayTypes || [];
            if (!excl.includes('credit_card')) {
                creditMemoTypes.push({ type: "manualCard", description: "Manual Credit Card", paymentOffset: true });
            }
            if (!excl.includes('check')) {
                creditMemoTypes.push({ type: "check", description: "Check", paymentOffset: true });
            }
            if (!excl.includes('cash')) {
                creditMemoTypes.push({ type: "cash", description: "Cash", paymentOffset: true });
            }
            creditMemoTypes.push({ type: "refund", description: "Refund" });
            if (!excl.includes('ach')) {
                creditMemoTypes.push({ type: "manualAch", description: "Manual ACH", paymentOffset: true });
            }
            if (!excl.includes('payroll_deduction')) {
                creditMemoTypes.push({type: "payrollDeduction", description: "Payroll Deduction", paymentOffset: true});
            }
            creditMemoTypes = creditMemoTypes.concat([
                { type: "securityDepositAuto", description: "Security Deposit", paymentOffset: false }
            ]);
        }
        creditMemoTypes = creditMemoTypes.concat(_.map(this.availablePayerSources(), (ps) => {
            return { type: "excess_" + ps.type, description: "Excess/Overpayment " + ps.description, paymentOffset: true };
        }));
        creditMemoTypes = creditMemoTypes.concat(_.map(this.availablePayerSources(), (ps) => {
            return { type: "prepaid_" + ps.type, description: "Prepaid " + ps.description, paymentOffset: true };
        }));
        creditMemoTypes.push({ type: "systemOverpayment", description: "Overpayment" });
        creditMemoTypes.push({ type: "other", description: "Other" });
        if (this.isLedgerExportable())
            creditMemoTypes = creditMemoTypes.filter(cmt => cmt.paymentOffset);
        return creditMemoTypes;
    }

    static isLedgerExportable() {
        return this.getAccountLedgerCodes() || _.deep(this, "billing.netSuite") || _.deep(this, "billing.sage") || this.hasCustomization("billing/requireLedgerAccountName/enabled");
    }

    static availablePayerSources (showArchived) {
        if (this.valueOverrides && this.valueOverrides.payerSources)
            return this.valueOverrides.payerSources.filter(ps => showArchived || !ps.archived);
        else
            return [
                { type: "ccdf", description: "CCDF" },
                { type: "onmywayprek", description: "On My Way Pre-K" },
                { type: "unitedway", description: "United Way" }
            ];
    }

    static billingCardProviderName () {
        if (this.billing && this.billing.enabled) {
            if (this.billing.adyenInfo)
                return "adyen";
            if (this.billing.stripeInfo)
                return "stripe";
        }
    }

    static getTimezone () {
        return (this.timezone || "America/New_York");
    }

    static availableBillingPlans(includeScaledPricing = true, showArchived = false, requirePrograms = false, includeItems = false) {
        const todayDate = new moment.tz(this.getTimezone()).startOf("day").valueOf();
        const planData = JSON.parse(JSON.stringify(this.billing?.plansAndItems || []));
        return planData.filter((plan) => {
                return (includeItems ? plan.type !== 'bundle' : plan.type === 'plan') &&
                    (!plan.expires || (plan.expires > todayDate)) &&
                    (includeScaledPricing || !SCALED_PLAN_FREQUENCIES.includes(plan.frequency)) &&
                    (showArchived || !plan.archived) &&
                    (!requirePrograms || !!plan.program);
            })
            .sort((a, b) => a.description.toLowerCase() - b.description.toLowerCase());

    }

    static availablePrograms(showInactive = false) {
        return (this.billing?.programs ?? []).filter(program => showInactive || program.isActive);
    }

    static availableBundles(showArchived = false) {
        return (this.billing?.plansAndItems ?? []).filter(plan => plan.type === 'bundle' && (showArchived || !plan.archived));
    }

    static availableBillingFrequencies() {
        const frequencies = [
            { type: BillingFrequencies.DAILY, description: 'Daily - Flat Rate' },
            { type: BillingFrequencies.WEEKLY, description: 'Weekly - Flat Rate' },
            { type: BillingFrequencies.SCALED_WEEKLY, description: 'Weekly - Scaled Pricing' },
            { type: BillingFrequencies.WEEKLY_SCHEDULED_DAILY, description: 'Weekly - Charged Daily by Schedule' },
            { type: BillingFrequencies.BIWEEKLY, description: 'Bi-weekly - Flat Rate' },
            { type: BillingFrequencies.SCALED_BIWEEKLY, description: 'Bi-weekly - Scaled Pricing' },
            { type: BillingFrequencies.SEMIMONTHLY, description: 'Semi-monthly (due 1st and 15th) - Flat Rate' },
            { type: BillingFrequencies.MONTHLY, description: 'Monthly - Flat Rate' },
            { type: BillingFrequencies.SCALED_MONTHLY, description: 'Monthly - Scaled Pricing' }
        ];
        if (this.hasCustomization(AvailableCustomizations.VARIABLE_MONTHLY_SUBSIDIES)) {
            frequencies.push({
                type: BillingFrequencies.DAILY_CHARGED_MONTHLY,
                description: 'Monthly - Charged Daily by Schedule'
            });
        }
        frequencies.push(
            { type: BillingFrequencies.BIMONTHLY, description: 'Bi-monthly - Flat Rate' }
        );
        return frequencies;
    }
}