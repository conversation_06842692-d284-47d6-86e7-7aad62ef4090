[{"_id": "BrXg7SJsyPhyEAdii", "firstName": "<PERSON>i", "lastName": "SoAndSo", "type": "family", "profileEmailAddress": "<EMAIL>", "createdBy": "fKriuC2Y3FrWJD2uR", "createdAt": 1571925358734, "defaultGroupId": "9oXsrrZhgC5mLuKhA", "orgId": "fuQK4wqfE2yiZqKt6", "profileData": {"phonePrimary": "*********", "phoneNumberHome": "*********", "phoneNumberWork": "12233445566", "phoneNumberMobile": "66554433221", "parentCapDiscount": "50%", "parentCapCompany": "Company Name", "parentSubsidy": "Yes", "notesPrivate": "This is a private note for only <PERSON><PERSON> and Teachers to see", "householdInformation": {"parentStreetAddress": "1234 Main Street", "parentCity": "Carmel", "parentState": "IN", "parentZip": "46000", "addressType": "Mailing and Physical Address"}, "primaryPhone": "1112223333", "standardOutlook": {"importantNotes": ""}}, "inActive": false}, {"_id": "UUQsTltHyXfuGToGG", "firstName": "<PERSON>", "lastName": "<PERSON>", "defaultGroupId": "9oXsrrZhgC5mLuKhA", "type": "person", "orgId": "fuQK4wqfE2yiZqKt6", "createdBy": "SYSTEM_IMPORT", "createdAt": {"$numberLong": "1571941847811"}, "deactivatedAt": 1574906199352, "deactivationReason": "Related Person Was Withdrawn", "inActive": true, "birthday": 1574906199352}, {"_id": "vkdZLPi76uYbxLmrv", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "designations": [], "defaultGroupId": "iB9G2BMWH6vpgjzeJ", "type": "person", "createdBy": "9WAbmoxg4HqTzY5SC", "createdAt": 1675095355108, "orgId": "fuQK4wqfE2yiZqKt6", "inActive": false, "profileData": {"allowMedia": "Yes", "backUpCare": "", "backUpCareReferringPartner": "", "birthday": 1606021200000, "curriculum": "", "enrollmentDate": *************, "gender": "Male", "householdDescription": "", "householdInformation": {"city": "<PERSON><PERSON>", "guardianFirstName": "<PERSON><PERSON><PERSON>", "guardianLastName": "<PERSON><PERSON><PERSON><PERSON>", "state": "nj", "streetAddress": "9 Marys CT", "zip": "08822"}, "mediaRequirements": {"mediaRelease": "", "mediaReviewRequired": "", "noMediaAllowed": ""}, "medicalInfo": "", "notesPrivate": "", "notesPublic": "", "quickList": "", "restrictedPickUp": {"restrictedDescription": "", "restrictedFirstName": "", "restrictedLastName": ""}, "standardOutlook": {"allergies": "", "importantNotes": "", "specialNeeds": ""}, "scannedDocuments": {"enrollmentPaperwork": [{"name": "Aguanno Reg form.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/fuQK4wqfE2yiZqKt6/9WAbmoxg4HqTzY5SC/gvFdmnUgpyHCvRdLwoNO", "mediaToken": "gvFdmnUgpyHCvRdLwoNO", "mediaFileType": "application/pdf", "mediaPath": "fuQK4wqfE2yiZqKt6/9WAbmoxg4HqTzY5SC/gvFdmnUgpyHCvRdLwoNO"}], "medicalInformation": [{"name": "Aguano UHR Immunizations.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/fuQK4wqfE2yiZqKt6/qig3WCYKkgdpjCCeR/JB14kH34GfAT7zIByhOA", "mediaToken": "JB14kH34GfAT7zIByhOA", "mediaFileType": "application/pdf", "mediaPath": "fuQK4wqfE2yiZqKt6/qig3WCYKkgdpjCCeR/JB14kH34GfAT7zIByhOA"}, {"name": "Aguano Motrion Form.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/fuQK4wqfE2yiZqKt6/qig3WCYKkgdpjCCeR/EHKHgi11nBHs6KUbgKXh", "mediaToken": "EHKHgi11nBHs6KUbgKXh", "mediaFileType": "application/pdf", "mediaPath": "fuQK4wqfE2yiZqKt6/qig3WCYKkgdpjCCeR/EHKHgi11nBHs6KUbgKXh"}], "accidentReports": [{"name": "Aguano Accident Repot 9.7.23.pdf", "mediaUrl": "https://cfm.momentpath.com/uploads/fuQK4wqfE2yiZqKt6/qig3WCYKkgdpjCCeR/tmEkdRC2E36Sfs42zQ2B", "mediaToken": "tmEkdRC2E36Sfs42zQ2B", "mediaFileType": "application/pdf", "mediaPath": "fuQK4wqfE2yiZqKt6/qig3WCYKkgdpjCCeR/tmEkdRC2E36Sfs42zQ2B"}]}}, "lastInteractionDate": 1697032080000, "lastMoment": {"attributionPersonId": null, "momentType": "activity", "time": "9:48 am", "date": "10/11/2023", "sortStamp": 1697032080000, "momentTypePretty": "Activity", "createdAt": 1697032145441, "createdBy": "BWCGm9dRDJNhGGiA6", "attributionName": "<PERSON>", "createdByPersonId": "pbFrLJltcuqWdQA1B", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["vkdZLPi76uYbxLmrv"], "createdByPersonGroupId": "mHQeHXQfCM9Wh8Wj8", "createdByPersonCheckInGroupId": "mHQeHXQfCM9Wh8Wj8", "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/nw4K5vT45qfnCgcdv", "mediaToken": "nw4K5vT45qfnCgcdv", "mediaFileType": "image", "mediaPath": "fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/nw4K5vT45qfnCgcdv"}], "_id": "FRMbkP6WTfBuEzYik", "orgName": "<PERSON><PERSON>"}, "lastMomentByType": {"alert": {"attributionPersonId": null, "momentType": "alert", "comment": "Good morning! Just a reminder that if you would like to sign your child up for Jr. All Sports we need the attached form filled out and returned to the office by Friday October 13th. Please reach out to the administrative team if you have any questions! ", "time": "10:03 am", "date": "10/10/2023", "sortStamp": 1696946580000, "momentTypePretty": "Notification", "alertSendTypeEmail": true, "alertSendTypeText": false, "alertSendTypePush": false, "alertShowCheckin": false, "createdAt": 1696946581850, "createdBy": "qig3WCYKkgdpjCCeR", "attributionName": "<PERSON>", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["y7Wk9XTt2qzycRWzH", "Le3ASCPtry34htFtF", "fGcqrcYr9AQDTG4Fz", "N9DJaEJAmdsiuQXEK", "QmRE7ZuGbxSxRKxbz", "YptRyzEtL8cb7JpLW", "v3DP2JvvRN7fQYNaE", "DYLYLxNpMPWf2sDgx", "ciLM998Q7cG7qSSbz", "HXoh6bfM6j9HKLnBL", "sTyva2Lg6sNqgmLFL", "mKmP5ANptArXvw4iY", "nGqRBEjzR528a87Er", "bRB3EEpyQTnsZ6ZTM", "C2J46jekRHqbZajgr", "ahedzHbSPZqD3hB65", "o2yNRyKNssat7agrc", "MxoG9W7wHBp2gucDb", "ux2bSQwwWdtrKkvnR", "adWRE2mMTLFX9sRXr", "YqtosvipvhCWHki2v", "AKbSX36azqAgDtjWT", "veo4tb4jqkQN8tS2D", "wxfboTpewneB8DTX3", "BkDBTj6FL7fY2cawF", "CEdqsi3A7LLFnySpp", "AwagAsWKLwSG6GpKj", "ANkndNsAgxTjqWpmi", "Dsx9yADExKPzwFp88", "GAy3zp7XZ9soCBQKC", "zjoZfjnZB476iXc33", "ngc74mWXr79oPzK3J", "Z57JByibodYwRxqTi", "9aisD75qCQBa42RpD", "vkdZLPi76uYbxLmrv", "QTcNNGEtgRQutsgHP", "euSSTTsEi8hjZpSrC", "pbFrLJltcuqWdQA1B", "RWeZBN4gc7nYsMNjr", "uCMHqGGizeSzrAyPH", "sNoaEcBCLpswPBfpR", "FbmHEMwquoTsNShgu", "E6poRsSLrnwRtnqdi", "3nhrYp3xCBFtriejm", "Qj7EWZYW74emhFdkG", "rhAZbsPcpGer7ARiR", "yJEwPrmoNhrzeAdsu", "SpkJ9HubbkvFvJNgC", "B2uqi69PT6ihF337h", "kFuWkjLKmnftp7ZKN", "asnZPxQJDMJEYjjiF", "gaZN3xtqEzpNRkcsk", "gwJG9BEKmnhsbHejz", "kEhsX3eEgg6pe69jF", "bFSFoW2sdzq7TW6Mj", "a8XQdDamm9FHtvZZT", "wsdqbT6JFaCFRgXaX", "cxxkyL2d5WSTiYchL", "XwY32J7Z7NeA7wMYy", "djiyqAzDZMRP8DE7d"], "mediaFiles": [{"mediaUrl": null, "mediaToken": "2kCBlEQDAGgctwPVexa2", "mediaFileType": "application", "mediaPath": "fuQK4wqfE2yiZqKt6/qig3WCYKkgdpjCCeR/2kCBlEQDAGgctwPVexa2", "fileName": "Junior All Sports Fall 23.pdf"}], "_id": "ZGT5eGmeuwW4iw7uY", "orgName": "<PERSON><PERSON>"}, "food": {"attributionPersonId": null, "momentType": "food", "time": "3:18 pm", "date": "10/10/2023", "sortStamp": 1696965480000, "foodType": "PM S<PERSON>k", "foodItems": [{"name": "Fresh Fruit or Vegetable "}], "momentTypePretty": "Food", "createdAt": 1696965530603, "createdBy": "BWCGm9dRDJNhGGiA6", "attributionName": "<PERSON>", "createdByPersonId": "pbFrLJltcuqWdQA1B", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["vkdZLPi76uYbxLmrv", "euSSTTsEi8hjZpSrC", "o2yNRyKNssat7agrc", "Dsx9yADExKPzwFp88", "zjoZfjnZB476iXc33", "QTcNNGEtgRQutsgHP", "9aisD75qCQBa42RpD", "GAy3zp7XZ9soCBQKC", "ngc74mWXr79oPzK3J", "Z57JByibodYwRxqTi", "ANkndNsAgxTjqWpmi"], "createdByPersonGroupId": "iB9G2BMWH6vpgjzeJ", "createdByPersonCheckInGroupId": "iB9G2BMWH6vpgjzeJ", "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/mSGLHwgmDbrCdefHX", "mediaToken": "mSGLHwgmDbrCdefHX", "mediaFileType": "image", "mediaPath": "fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/mSGLHwgmDbrCdefHX"}, {"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/psGm6ncMSDbMKpamF", "mediaToken": "psGm6ncMSDbMKpamF", "mediaFileType": "image", "mediaPath": "fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/psGm6ncMSDbMKpamF"}], "_id": "YbfAm9FRn6S2TQjyt", "orgName": "<PERSON><PERSON>"}, "potty": {"attributionPersonId": null, "momentType": "potty", "comment": "Nd", "time": "9:14 am", "date": "10/11/2023", "sortStamp": 1697030040000, "pottyType": "Dry", "momentTypePretty": "<PERSON><PERSON>", "pottyTraining": "Successful", "createdAt": 1697030100505, "createdBy": "BWCGm9dRDJNhGGiA6", "attributionName": "<PERSON>", "createdByPersonId": "pbFrLJltcuqWdQA1B", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["vkdZLPi76uYbxLmrv"], "createdByPersonGroupId": "iB9G2BMWH6vpgjzeJ", "createdByPersonCheckInGroupId": "iB9G2BMWH6vpgjzeJ", "_id": "z8ZmHQrxeKk4ePsj2", "orgName": "<PERSON><PERSON>"}, "activity": {"attributionPersonId": null, "momentType": "activity", "time": "9:48 am", "date": "10/11/2023", "sortStamp": 1697032080000, "momentTypePretty": "Activity", "createdAt": 1697032145441, "createdBy": "BWCGm9dRDJNhGGiA6", "attributionName": "<PERSON>", "createdByPersonId": "pbFrLJltcuqWdQA1B", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["vkdZLPi76uYbxLmrv"], "createdByPersonGroupId": "mHQeHXQfCM9Wh8Wj8", "createdByPersonCheckInGroupId": "mHQeHXQfCM9Wh8Wj8", "mediaFiles": [{"mediaUrl": "https://s3.amazonaws.com/tendlymruploads/fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/nw4K5vT45qfnCgcdv", "mediaToken": "nw4K5vT45qfnCgcdv", "mediaFileType": "image", "mediaPath": "fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/nw4K5vT45qfnCgcdv"}], "_id": "FRMbkP6WTfBuEzYik", "orgName": "<PERSON><PERSON>"}, "sleep": {"attributionPersonId": null, "metaMomentId": "o8r2G8rWgDojrBH4s", "momentType": "sleep", "time": "12:44 pm", "date": "10/10/2023", "sortStamp": 1696956240000, "sleepCheckInterval": 10, "momentTypePretty": "Sleep", "createdAt": 1696956284183, "createdBy": "BWCGm9dRDJNhGGiA6", "attributionName": "<PERSON>", "createdByPersonId": "pbFrLJltcuqWdQA1B", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["vkdZLPi76uYbxLmrv"], "createdByPersonGroupId": "iB9G2BMWH6vpgjzeJ", "createdByPersonCheckInGroupId": "iB9G2BMWH6vpgjzeJ", "_id": "GDCT8WBf6srsdSmDC", "orgName": "<PERSON><PERSON>"}, "comment": {"attributionPersonId": null, "momentType": "comment", "comment": "Just a friendly reminder that to please send your child in appropriate shoes to school.  Unfortunately, we can not have the children wear crocs as these are not safe to wear, especially while outside on the climber due to children slipping.  Sorry for any inconvenience this may cause. Thank you again for your patience, understanding and cooperation. \nNicole", "time": "1:01 pm", "date": "10/9/2023", "sortStamp": 1696870860000, "momentTypePretty": "Comment", "createdAt": 1696871083090, "createdBy": "BWCGm9dRDJNhGGiA6", "attributionName": "<PERSON>", "createdByPersonId": "pbFrLJltcuqWdQA1B", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["ANkndNsAgxTjqWpmi", "Dsx9yADExKPzwFp88", "GAy3zp7XZ9soCBQKC", "zjoZfjnZB476iXc33", "ngc74mWXr79oPzK3J", "Z57JByibodYwRxqTi", "vkdZLPi76uYbxLmrv", "QTcNNGEtgRQutsgHP", "euSSTTsEi8hjZpSrC"], "createdByPersonGroupId": "iB9G2BMWH6vpgjzeJ", "createdByPersonCheckInGroupId": "iB9G2BMWH6vpgjzeJ", "_id": "6nRPjkJnBm64R8kuk", "orgName": "<PERSON><PERSON>"}, "portfolio": {"attributionPersonId": null, "momentType": "portfolio", "time": "1:52 pm", "date": "10/4/2023", "sortStamp": 1696441920000, "momentTypePretty": "Portfolio", "portfolioAssessments": [{"standardId": "0e6c9baa4f5444d6abd205ab975ed91ed4d2ab93", "value": 2}, {"standardId": "bb7bb1655d986d1981ff7b05278c1276c34094d9", "value": 2}, {"standardId": "3d322854a8e80300ea001ac1ced31765cc978cd0", "value": 2}], "attributionName": "<PERSON>", "modifiedByName": "<PERSON>", "modifiedBy": "pbFrLJltcuqWdQA1B", "taggedPeople": ["vkdZLPi76uYbxLmrv"], "_id": "nPmQ9ohP5iybH2gyb"}, "supplies": {"attributionPersonId": null, "momentType": "supplies", "comment": "Please send in more pull-ups. We have a few left and will need more for tomorrow. Thank you ☺️ ", "time": "4:43 pm", "date": "10/5/2023", "sortStamp": 1696538580000, "supplyType": "<PERSON>ull Ups", "supplyTypeRaw": "<PERSON>ull Ups", "momentTypePretty": "Supplies", "createdAt": 1696538666684, "createdBy": "BWCGm9dRDJNhGGiA6", "attributionName": "<PERSON>", "createdByPersonId": "pbFrLJltcuqWdQA1B", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["vkdZLPi76uYbxLmrv"], "createdByPersonGroupId": "iB9G2BMWH6vpgjzeJ", "createdByPersonCheckInGroupId": "iB9G2BMWH6vpgjzeJ", "_id": "wnGrhjAPjGwPbCNpq", "orgName": "<PERSON><PERSON>"}, "safety": {"attributionPersonId": null, "momentType": "safety", "comment": "Good morning! We just completed our monthly fire drill. All the children and staff are back safe in sound in the building! Everyone did a wonderful job! Enjoy the rest of your day! ", "time": "10:45 am", "date": "10/05/2023", "sortStamp": 1696517100000, "momentTypePretty": "Safety", "isDynamic": true, "momentDefinitionId": "Ksrw9M733c4nHq9pL", "dynamicFieldValues": {"safetyActivityTwo": "Fire Drill"}, "createdAt": 1696517249033, "createdBy": "qig3WCYKkgdpjCCeR", "attributionName": "<PERSON>", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "orgId": "fuQK4wqfE2yiZqKt6", "taggedPeople": ["kwPq5zZBu7jJ6AsDv", "LNjvMZuMQSAKJMcHB", "Zc2zvrqxMRmQth9GL", "7YZ3cd8jSFdwSFLM6", "RPReGsc72GoAJ3hTR", "Ev7nx94ox4sJcMYXT", "YptRyzEtL8cb7JpLW", "DYLYLxNpMPWf2sDgx", "ciLM998Q7cG7qSSbz", "y7Wk9XTt2qzycRWzH", "HXoh6bfM6j9HKLnBL", "sTyva2Lg6sNqgmLFL", "mKmP5ANptArXvw4iY", "nGqRBEjzR528a87Er", "bRB3EEpyQTnsZ6ZTM", "C2J46jekRHqbZajgr", "ahedzHbSPZqD3hB65", "o2yNRyKNssat7agrc", "MxoG9W7wHBp2gucDb", "fGcqrcYr9AQDTG4Fz", "ux2bSQwwWdtrKkvnR", "adWRE2mMTLFX9sRXr", "YqtosvipvhCWHki2v", "ANkndNsAgxTjqWpmi", "Dsx9yADExKPzwFp88", "GAy3zp7XZ9soCBQKC", "zjoZfjnZB476iXc33", "ngc74mWXr79oPzK3J", "Z57JByibodYwRxqTi", "9aisD75qCQBa42RpD", "vkdZLPi76uYbxLmrv", "QTcNNGEtgRQutsgHP", "AwagAsWKLwSG6GpKj", "avdgsXZvvyoXdgbHc", "G9ennATdEcTpgK2dq", "u9cdWNDgmTb7REfrr", "mXFDyKYDnXCDHXkfy", "hBJy4Bq5qKGtXdvZe", "Ekxk4BR5cnyAmC953", "SFn9dtDE55edYwtMz", "mQgbgKo5ggvGSsjfB", "w6b22dvf6YdMNRZnJ", "v6qAkSoT8Qc8xfMNv", "pLMToAyM5DGBFosFG", "DG5KpPZH36Yid4aTx", "jAMPjSZvJA5ScKXMn", "za43GaHAHzHwuHFDw", "xrEjorY2isvT52roA", "96thJZhCfBW9SJe7Q", "5XG2kojNBXDSM93Lk", "ZXtGXWAi4vGTiGmT9", "Fj5egEwsSedR4f2BE", "W8RzSvekFiFcdAAEt", "PyeEDRneX5E9NEtZm", "V8GF5WfYpwPcSVoLr", "x9CJIj56fnz2vDBjM", "wG2c1hNS2TdRUFpT5", "BoCEKdFKt2AxPZMdS", "BimwxpxsvyJyEhLxN", "K7de3RjF2acZqWDwc", "YS7Pk7gv3Rvg53Nsk", "nZzosZcrmdgRQNKJs", "rLuemJ58tFFpk4KET", "Bca6i5WX78eGg8o7s", "gQYc26vQD2MKvaqLj", "QGkqmRWgfit9MEmzu", "VadgL59UDvoEoq5t3", "Eqn0lT93xLczDQH5r", "EC5FuccssLZApzZXB", "ePwmnXCFzrYWmDEdE", "tdSxGYJqJRTkDkEGJ", "rJWmvEiWRPuiPGw83", "RZMgFyqdH4JnWhJmD", "9eKLxPx2cmrztLthu", "hMBPKdCspvtXW5fWJ", "QAh95T4mtFiWwF2z3", "pXphySKMRiYfYXu8s", "K3ECeRfnfnh3FiyHp"], "createdByPersonGroupId": "85KQW22RzfSgstmis", "createdByPersonCheckInGroupId": "85KQW22RzfSgstmis", "_id": "6ESz9j8RoEaKuqKcp", "orgName": "<PERSON><PERSON>"}}, "engagements": [{"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "cR3iG5KgzycKaLJgq", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696427478361, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "Qc2yaoEPkYxuhxzQ5"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "food_with_media", "momentId": "wPqqAMPLsszoLYqr3", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696435389019, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "b2acLHSYrHDAo4jD8"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "gjeJKrBrjpDG7gRyB", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696435689411, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "KHoWzQf5aNWjJT9Bf"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "8oNRAPnQCd4ttbarc", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696437944725, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "zkWcwj9otEDckFPxu"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "sleep", "momentId": "qr8DdYHZ3PjDhGpbZ", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696438526337, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "aJhfBdtKXKbWBnuss"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "alert_with_media", "momentId": "w4qn7YWzam7qNPQ85", "createdBy": "zsSkrGQvaPXFva8Cn", "createdAt": 1696440341642, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "zsSkrGQvaPXFva8Cn", "_id": "Kigd3KmM2hhJxsJG3"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "portfolio", "momentId": "nPmQ9ohP5iybH2gyb", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696442116542, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "amtDqbRstP8BHQMkM"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "email", "subType": "summary_media_view", "createdBy": "SYSTEM", "createdAt": 1696442715417, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "2twhAvL399AssHuqE"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "AjaKh7c3x9YpY9CEd", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696445216820, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "JkFRTcN7nF8Jpn3X5"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "food_with_media", "momentId": "vcyrekn97zTL8XZK3", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696447449155, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "8ZJSHKNSfrWPQnEJN"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "8G8rQS4pHf3WufXTd", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696450772255, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "Qzti9WQB6HcbNooY8"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "email", "subType": "summary_media_view", "createdBy": "SYSTEM", "createdAt": 1696464547949, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "GraeXLuqz3N8x2db9"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "food_with_media", "momentId": "zFjAxSTXB3SaT9srW", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696510783860, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "KWQ9N2kymHzZgfzAE"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "NQ3sy5JgknasJxRPd", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696511592138, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "QrcxnKFWmQfL8ZCp9"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "SZYrjyd4M4bmfjw8q", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696513311352, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "fThoN8Ly829uFgiBi"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "Gvm5NAdF4zxynhXaM", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696513437780, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "z9a3gBaJ6LZizkeLa"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "safety", "momentId": "6ESz9j8RoEaKuqKcp", "createdBy": "zsSkrGQvaPXFva8Cn", "createdAt": 1696517257053, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "zsSkrGQvaPXFva8Cn", "_id": "TGYjZszpe89Jo2c2D"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "7WYcBbPAKY72dXu3n", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696519917251, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "J42wvCNNsJPhHQjxk"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "food_with_media", "momentId": "CMewjELnzrDwEt2Gu", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696522087724, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "GKAwceFgohTtuHwqX"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "pPzcuRyGsh5hzh7iq", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696522781727, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "XsPMai56NRgwHaGwh"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "sleep", "momentId": "jK6RF9gRd5iaki9CD", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696525634685, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "3gQ6tzugnxQkm3Pm7"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "portfolio", "momentId": "nPmQ9ohP5iybH2gyb", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696526112862, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "BBxFhWqLcYQh99wJZ"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "alert", "momentId": "X7DYtDXnQiAzySojk", "createdBy": "zsSkrGQvaPXFva8Cn", "createdAt": 1696530069398, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "zsSkrGQvaPXFva8Cn", "_id": "aw6yswcS5YYRejKes"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "Q9RSuFFTepN6gturA", "createdBy": "ji33W92jvEpvhdMGf", "createdAt": 1696532434224, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "ji33W92jvEpvhdMGf", "_id": "GrdEvKj38LbEyuwzf"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "Dxvp5gLHp4gFnviy8", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696534280158, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "fMa9Ftxvmja76bwbS"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "anvqHy6WHRERSkyDx", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696534391461, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "hKvTh2WEa2b6c2FSA"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "MxcuGwRH6gQFGAYjN", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696534815686, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "mF7e45XQnDwJk4wbN"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "C6c26Ehy7Ri5p2nqW", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696536572821, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "ZKtnXeRHJPzYfF5qT"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "2ZACBLmmYRYESz8tW", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696538162156, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "FDWYiR662yEwj9AT8"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "supplies", "momentId": "wnGrhjAPjGwPbCNpq", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696538666713, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "giKP2S7RgYqpq8rzy"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "9eCLTwHhEa8gAesfR", "createdBy": "zsSkrGQvaPXFva8Cn", "createdAt": 1696596330553, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "zsSkrGQvaPXFva8Cn", "_id": "Fgt7NfocN56HjXCXu"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "EE3AR79xwAiXnE7YM", "createdBy": "zsSkrGQvaPXFva8Cn", "createdAt": 1696596587099, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "zsSkrGQvaPXFva8Cn", "_id": "LN4zymkDKzB7sPpFo"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "JEMaTajZAjaJH7m2Y", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696597537615, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "kvH64ht4bPxKduprc"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "MhjDWoWkTpAQQiJie", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696598298087, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "pAZJfLebuG2Nvk8bY"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "fXBjZZGAh95FBnMxf", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696599653426, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "SxEhG79joWbX4dfRe"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "85fANmm8qXByCWZx5", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696601836396, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "ZuzZmnbqKHsReR3co"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "timeline_like", "detail": "activity", "momentId": "JEMaTajZAjaJH7m2Y", "createdBy": "SYSTEM", "createdAt": 1696602872526, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "adpZHHvonRtDZjsZi"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "timeline_like", "detail": "activity", "momentId": "fXBjZZGAh95FBnMxf", "createdBy": "SYSTEM", "createdAt": 1696602875593, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "Ht8Av8prZJJKygPom"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "timeline_like", "detail": "comment", "momentId": "EE3AR79xwAiXnE7YM", "createdBy": "SYSTEM", "createdAt": 1696602883449, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "rgfufsXrBRAsikMbT"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "timeline_like", "detail": "comment", "momentId": "9eCLTwHhEa8gAesfR", "createdBy": "SYSTEM", "createdAt": 1696602929360, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "zdvE96guGA2CTBQSQ"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "EzaLcPZAHALToLjW8", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696607467075, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "9R2LezA9kjT4AQLie"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "znSikrfZWn673YstM", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696607566183, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "vkkk4aj9kTAc49GCt"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "345d899mkc2ZtfGYb", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696607769586, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "qw7XPZENsS9wBiXqk"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity", "momentId": "345d899mkc2ZtfGYb", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696607848613, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "79DhNCJemRTGFDXGW"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "food_with_media", "momentId": "BvFrJSRXxNmGRycPg", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696608793416, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "YKe9uswoqoCc9KRtE"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "7zMZuwypXjsX8CNdM", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696608920774, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "LC8fqLdyBWFSdqx9T"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "sleep", "momentId": "sjQsv2DWwLksB8ZGY", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696611522965, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "PzrpGGj596o8geSy4"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "yRGcSJZ3tHHgkvoFu", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696618626638, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "4jQQJ4DRmQBXsEAEX"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "food_with_media", "momentId": "GgssbSC4kCXP9bqrr", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696620563540, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "YTH6dygvT2fwBPXgL"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "comment_with_media", "momentId": "FWdGuiNZNyXqy52jX", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696856521705, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "CQYxSTKA5SaGgTcJm"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "pA8N5vJn67kY6e5Mk", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696858798956, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "quAq483ANTXNFQZDG"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "2tYqe9PCfnm5ug6Ni", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696859278953, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "fi7qdDvydf5bCCoRB"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "food_with_media", "momentId": "QfFxdK24QsdBNTkhM", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696868137378, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "tDCkokSEZj7akzq2S"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "WSa68LtHbQ46YGK2f", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696868749947, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "ETg6qvAaZPRAfs7F6"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "LwDZWiN8BoKSynMeb", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696870826814, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "wctjr9CR3HaFrj35K"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "comment", "momentId": "6nRPjkJnBm64R8kuk", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696871083631, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "j2SeCRbP5PNwocFQf"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "sleep", "momentId": "476ybb5YW9xWc7LAt", "createdBy": "DbJsDNcmuh5eNM6h6", "createdAt": 1696871850682, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "DbJsDNcmuh5eNM6h6", "_id": "2k2JT3GRkanjhDL44"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "gQ4Jw333LLnsvg5Ck", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696879436669, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "LhaiwNvRGxvfB2fsS"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "2QkYySWQH8sdP2anK", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696879582465, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "CFqtcMAv6ZY5SoBPi"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "gjNtru2kMh26DARGF", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696881617763, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "TSPxqdSmsrsNRE2x4"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "qnqz2goNY3wgTAGMx", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696882373952, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "TTTucFmk5TX2ddpNT"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "9zJpmNRwcZTACwjKr", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696882506054, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "egquCFuSQuxF7SyBW"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "i8rZQFueAPQ8Ty8b8", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696884207761, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "msrg6uuYfeH6XB2Ni"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "YrbzRp2PhS29oCTJS", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696943884868, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "xaYsQzhs64QBzKaZb"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "YuvXsyJAwGXmdSNAB", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696945742026, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "uiYjjjKuT6zpXvucS"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "alert_with_media", "momentId": "ZGT5eGmeuwW4iw7uY", "createdBy": "zsSkrGQvaPXFva8Cn", "createdAt": 1696946586919, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "zsSkrGQvaPXFva8Cn", "_id": "uF5Ajb2X3nknKGoti"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "email", "subType": "summary_media_view", "createdBy": "SYSTEM", "createdAt": 1696947233772, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "W6uu274DGkoZJz3hc"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "sleep", "momentId": "GDCT8WBf6srsdSmDC", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696956284254, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "TvnrHMivGboi4f54i"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "ytrHynbMZ9xD94dwc", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696956776108, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "9m6KdS9FoeqsvZXhz"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "DLSfvPguRJ8ps5Ao4", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696960871729, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "RkQKzopAFMBg3fTyR"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "food_with_media", "momentId": "YbfAm9FRn6S2TQjyt", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696965530653, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "kX8s7cw3RET67NY4h"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "uJdSd8z7o2H7L5y7R", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696966369541, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "frvDbvWZALNfSoP74"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "r9K8ymYXdxbixJeMD", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1696969089420, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "EbiE79LxiMHJ7uLzC"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "timeline_like", "detail": "activity", "momentId": "r9K8ymYXdxbixJeMD", "createdBy": "SYSTEM", "createdAt": 1697019400857, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "jw752k5xnLAYtNQjh"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "timeline_like", "detail": "food", "momentId": "YbfAm9FRn6S2TQjyt", "createdBy": "SYSTEM", "createdAt": 1697019414842, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "i3gYvu2fbWmmy2JG9"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "timeline_like", "detail": "activity", "momentId": "DLSfvPguRJ8ps5Ao4", "createdBy": "SYSTEM", "createdAt": 1697019428693, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "L5Nqw54qPLiPyQMHc", "_id": "rdfRKtpLcCgYCkgq9"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "potty", "momentId": "z8ZmHQrxeKk4ePsj2", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": 1697030100537, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "42qTtNaBDeCp5TMoZ"}, {"orgId": "fuQK4wqfE2yiZqKt6", "type": "app", "subType": "created_moment", "detail": "activity_with_media", "momentId": "FRMbkP6WTfBuEzYik", "createdBy": "pbFrLJltcuqWdQA1B", "createdAt": *************, "targetPersonId": "vkdZLPi76uYbxLmrv", "sourcePersonId": "pbFrLJltcuqWdQA1B", "_id": "h6oq2mcbicEBFZok6"}], "billing": {"pendingCharges": [], "lastInvoiced": *************, "billingNotes": "Dad is a state trooper. 10% CAP", "enrolledPlans": [{"_id": "5xgvaKE727bA9mMBc", "planDetails": {"_id": "5xgvaKE727bA9mMBc", "description": "<PERSON><PERSON> - 5 day (2023)", "type": "plan", "frequency": "monthly", "category": "tuition", "program": "", "amount": 1546, "scaledAmounts": [], "ledgerAccountName": "4027"}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 10, "amountType": "percent", "discountType": "capDiscount", "allocationDescription": "Discount: CAP Discount", "id": "mFzN3YAep4kduqanZ"}], "createdAt": *************, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************, "expirationDate": *************, "overrideRate": null, "updatedAt": *************, "updatedBy": "ZdkhmaEScuuboPFbG"}, {"_id": "C86du9aopH8qWbWk2", "planDetails": {"_id": "C86du9aopH8qWbWk2", "description": "Parent View", "amount": 35, "type": "plan", "frequency": "monthly", "ledgerAccountName": "4045", "scaledAmounts": [], "program": ""}, "enrollmentDate": *************, "allocations": [{"allocationType": "discount", "amount": 50, "amountType": "dollars", "discountType": "employeeDiscount", "allocationDescription": "Discount: Employee Discount", "discountExpires": *************, "id": "LEyvpNRXvs7Hnivjk"}], "createdAt": *************, "enrollmentForecastEndDate": null, "enrollmentForecastStartDate": null, "expirationDate": *************, "overrideRate": 10, "updatedAt": *************, "updatedBy": "ZdkhmaEScuuboPFbG"}, {"_id": "6Rfu2MYzq4RpD523J", "planDetails": {"_id": "6Rfu2MYzq4RpD523J", "description": "Pizza Fridays", "amount": 20, "type": "plan", "frequency": "monthly", "category": "", "ledgerAccountName": "4055"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable-with-copay", "amount": 5, "amountType": "dollars", "reimbursementType": "NORWESCAP", "selectedResponsiblePartyId": "L5Nqw54qPLiPyQMHc", "allocationDescription": "Reimbursable: NORWESCAP undefined", "id": "anfbxbyZcGwarE8cP"}], "createdAt": *************, "enrollmentForecastEndDate": null, "enrollmentForecastStartDate": null, "expirationDate": *************, "overrideRate": 5, "updatedAt": *************, "updatedBy": "ZdkhmaEScuuboPFbG"}, {"_id": "WMfN8RY7vx69oEQHd", "planDetails": {"_id": "WMfN8RY7vx69oEQHd", "description": "Infant - 2 Day (2023)", "type": "plan", "frequency": "monthly", "category": "tuition", "program": "", "amount": 930, "scaledAmounts": [], "ledgerAccountName": "4023"}, "enrollmentDate": *************, "allocations": [{"allocationType": "reimbursable", "amount": 10, "amountType": "dollars", "reimbursementType": "Subsidy TEST", "allocationDescription": "Reimbursable: Subsidy TEST", "payerStartDate": *************, "payerEndDate": *************, "id": "3My2qwScusScdBohn"}], "createdAt": *************, "expirationDate": *************, "overrideRate": 10, "enrollmentForecastStartDate": *************, "enrollmentForecastEndDate": *************}]}, "waitlistAddedDate": null, "lastInformedArrival": {"source": "familyCheckin", "createdAt": *************, "checkedInById": "L5Nqw54qPLiPyQMHc", "fieldValues": {"attending": "Yes"}, "absent": false}, "checkInGroupId": "mHQeHXQfCM9Wh8Wj8", "checkInGroupName": "Outdoor Learning Environment", "checkedIn": true, "checkedInOutTime": *************, "presenceLastGroupId": "iB9G2BMWH6vpgjzeJ", "previousClassroomGroupId": "mHQeHXQfCM9Wh8Wj8", "timestamps": {"moveMomentTimestamp": {"momentId": "BQWFDXeFKbYJzscvL", "timestamp": 1697031624330, "groupId": "mHQeHXQfCM9Wh8Wj8", "oldGroupId": "iB9G2BMWH6vpgjzeJ"}, "nameToFaceMomentTimestamp": {"momentId": "SG3LKF6j5sX9PjQkt", "timestamp": 1697031743431, "groupId": "mHQeHXQfCM9Wh8Wj8"}}, "avatarPath": "fuQK4wqfE2yiZqKt6/BWCGm9dRDJNhGGiA6/D9YDF544aJ2L6oRgC", "avatarToken": "D9YDF544aJ2L6oRgC", "immunizationEntries": [{"_id": "BPrwXNNFbLmKZScju", "type": "UCHR", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118373086, "date": 1693454400000}, {"_id": "kNptyQ27R57t92bJd", "type": "DTaP", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118461668, "date": 1611723600000}, {"_id": "tCKotDPHuskkR5f9A", "type": "DTaP", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118467637, "date": 1616644800000}, {"_id": "j4jFa542D8Wk7hwz3", "type": "DTaP", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118476851, "date": 1621915200000}, {"_id": "DwbcChmcpezfttGyP", "type": "DTaP", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118490643, "date": 1656561600000}, {"_id": "7iMJ73NapD3T2SGbh", "type": "HepA", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118502044, "date": 1640062800000}, {"_id": "CFi6Jvy7H89wu9D6v", "type": "HepA", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118508189, "date": 1656561600000}, {"_id": "fbNB6Exqa8xwfdTWd", "type": "HepB", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118517786, "date": 1611723600000}, {"_id": "P7DHhybWgy5x2zuCY", "type": "HepB", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118524975, "date": 1616644800000}, {"_id": "HpMpM2pxb56FNZEQ5", "type": "HepB", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118556203, "date": 1621915200000}, {"_id": "Ph8atjo4icENSXb5t", "type": "Hib", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118593821, "date": 1611723600000}, {"_id": "rpNnQwzJAnpSJ2dFK", "type": "Hib", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118602655, "date": 1616644800000}, {"_id": "rx7ueydaET4qLKiM5", "type": "Hib", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118619336, "date": 1621915200000}, {"_id": "C9PQpow79pAETGbhj", "type": "Hib", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118630939, "date": 1625025600000}, {"_id": "2WsDSagejRCAcRXgb", "type": "IIV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118639854, "date": 1631073600000}, {"_id": "oBE5qZjLjszpNBuda", "type": "IIV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118647016, "date": 1641531600000}, {"_id": "iaWva8g6W28XcrqKZ", "type": "IIV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118656125, "date": 1672203600000}, {"_id": "KgXnmb8npvYPhWr6B", "type": "IIV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118684980, "date": 1693454400000}, {"_id": "G4TdJoAhqQc8vhHeQ", "type": "MMR", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118696262, "date": 1641531600000}, {"_id": "WFWb2kgpimmfEdeRh", "type": "PCV13", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118705164, "date": 1611723600000}, {"_id": "XcJjgsEgd2QrvGS4i", "type": "PCV13", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118711533, "date": 1616644800000}, {"_id": "6PyjMc7zrp69vakpT", "type": "PCV13", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118719116, "date": 1621915200000}, {"_id": "gx7W6foX9k5vpKW4h", "type": "PCV13", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118726049, "date": 1648008000000}, {"_id": "YP5eLCC4WxQNbMLWk", "type": "IPV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118734449, "date": 1611723600000}, {"_id": "48dkGi5K9RC9AQrdn", "type": "IPV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118744414, "date": 1616644800000}, {"_id": "3vsk8uMKPScGB2ikM", "type": "IPV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118755904, "date": 1621915200000}, {"_id": "cb5vs9WqMPJExxTwe", "type": "IPV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118765025, "date": 1656561600000}, {"_id": "6mSQsBE9x8qbBxs5K", "type": "RV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118783463, "date": 1611723600000}, {"_id": "jwSXtFuwaaEh4ifrn", "type": "RV", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118789848, "date": 1616644800000}, {"_id": "uFNSPotZ8SkvomBWH", "type": "VAR", "createdByPersonId": "zsSkrGQvaPXFva8Cn", "createdAt": 1694118798377, "date": 1641531600000}], "familyCheckOut": {}}]