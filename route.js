import { Meteor } from 'meteor/meteor';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

import { USER_TYPES } from './lib/constants/profileConstants';
import { PeopleTypes } from './lib/constants/peopleConstants';
import { urls } from './lib/constants/urlConstants';
import { AvailableCustomizations } from './lib/customizations';
import { processPermissions } from './lib/permissions';
import { MethodDataManager } from './lib/methodDataManager';
import { People } from './lib/collections/people';
import { OrgsUtil } from './lib/util/orgsUtil';
import { Orgs } from './lib/collections/orgs.js';
import { Foods } from './lib/collections/food.js';
import { CurriculumTheme } from './lib/collections/curriculumThemes.js';
import { Curriculum } from './lib/collections/curriculum.js';
import { Moments } from './lib/collections/moments.js';
import _ from './lib/util/underscore.js';

import './client/layout/not_authorized.html';
import './client/layout/loading';
import './client/layout/printableLayout.html';
import './client/layout/application/partials/_bare_application.js';

import './client/app/site/notFound.html';
import './client/app/main';
import './client/layout/loading';
import './client/app/dashboard/dashboardSite.js';
import './client/app/dashboard/dashboard';
import './client/app/dashboard/mediaGallery.js';
import './client/app/dashboard/mediaReview.js';
import './client/app/dashboard/expressDriveUp.js';
import './client/app/dashboard/dashboardFamilyAdvanced/dashboardFamilyAdvanced.js';
import './client/app/inquiries/inquiries.js';
import './client/app/food/food.js';
import './client/app/calendar/calendar.js';
import './client/app/groups/groups.js';
import './client/app/admin/data-entry/paymentTypeImporter.js';
import './client/app/admin/data-entry/quickEntry.js';
import './client/app/admin/importer/importer.js';
import './client/app/admin/org.js';
import './client/app/admin/documents.js';
import './client/app/admin/integrations/integrations.js';
import './client/app/billing/billingAdyenOnboarding.js';
import './client/app/billing/billingAdyenBalancePlatformOnboarding.js';
import './client/app/billing/billingAdminContainer.js';
import './client/app/billing/billingAdminReports.js';
import './client/app/billing/invoiceDetail.js';
import './client/app/billing/billingAdminConfiguration.js';
import './client/app/billing/payerReconciliation.js';
import './client/app/billing/deposits.js';
import './client/app/billing/chargebacks.js';
import './client/app/billing/refunds.js';
import './client/app/billing/recentEnrollments.js';
import './client/app/billing/districtEmployeeReview.js';
import './client/app/billing/securityDeposits.js';
import './client/app/billing/scheduleChanges.js';
import './client/app/dashboard/explore/explore.js';
import './client/app/announcements/announcements.js';
import './client/app/timeCards/timeCards.js';
import './client/app/curriculum/curriculum.js';
import './client/app/curriculum/curriculumContainer';
import './client/app/curriculum/curriculumReview';
import './client/app/curriculum/curriculumBuilder';
import './client/app/curriculum/themeBank/review';
import './client/app/messages/messages.js'
import './client/app/messages/message.js'
import './client/app/registrationFlow/registrationFlow.js'
import './client/app/registrationFlow/registrationFlowCompleted.js'
import './client/app/reservations/reservations.js'
import './client/app/paymentStatements/paymentStatements.js'
import './client/app/dashboard/explore/explore.js'
import './client/app/reports/reportsContainer.js';
import './client/app/reports/reportAttendance.js';
import './client/app/reports/reportAdp.js';
import './client/app/reports/attendanceGridByDate.js';
import './client/app/reports/reportAttendanceWeekly.js';
import './client/app/reports/reportBillingExpanded.js';
import './client/app/reports/reportBusRoster.js';
import './client/app/reports/reportCaliforniaAttendanceAndFiscal.js';
import './client/app/reports/reportCaliforniaEnrollmentAttendanceRegister.js';
import './client/app/reports/reportCaliforniaPopulation.js';
import './client/app/reports/reportCaliforniaRoster.js';
import './client/app/reports/reportCheckInOutNoPinCode.js';
import './client/app/reports/reportClassList.js';
import './client/app/reports/reportClassListSchedule.js';
import './client/app/reports/reportCurriculum.js';
import './client/app/reports/reportDocumentStatus.js';
import './client/app/reports/reportEnrollmentStatusV2.js';
import './client/app/reports/reportGeneralAttendance.js';
import './client/app/reports/reportGroupEnrollments.js';
import './client/app/reports/reportImmunizations.js';
import './client/app/reports/reportLaborUtilization.js';
import './client/app/reports/reportMeals.js';
import './client/app/reports/reportMealsDetail.js';
import './client/app/reports/reportMoments.js';
import './client/app/reports/reportPayrollRatio.js';
import './client/app/reports/reportProfile.js';
import './client/app/reports/reportReservations.js';
import './client/app/reports/reportRoster.js';
import './client/app/reports/reportScheduling.js';
import './client/app/reports/reportsCustomBuilderContainer.js';
import './client/app/reports/reportsSavedList.js';
import './client/app/reports/reportSignInGrid.js';
import './client/app/reports/reportStaffPay.js';
import './client/app/reports/reportStaffTime.js';
import './client/app/reports/reportSubsidy.js';
import './client/app/reports/reportWaitList.js';
import './client/layout/printableLayout.html';
import './client/app/people/person.js';
import './client/app/people/people.js';
import './client/app/people/peopleContainer.js';
import './client/app/people/dataValidationBuilder.js';
import './client/app/people/summaryReport.js';
import './client/app/people/personPortfolio.js';
import './client/app/people/pinCodeCheckin.js';
import './client/layout/not_found.html';
import './client/app/superadmin/superAdminIndex.js';
import './client/app/superadmin/userAccountDebug.js';
import './client/app/superadmin/campaigns/campaigns.js';
import './client/app/superadmin/customers.js';
import './client/app/superadmin/customerDetail.js';
import './client/app/superadmin/_profileDefinitionEditor.js';
import './client/app/superadmin/childcareCrm.js';
import './client/app/superadmin/_childcareCrmDefinitionEditor.js';
import './client/app/superadmin/dreambox.js';
import './client/app/superadmin/superAdminInvoiceManagement.html';
import './client/app/superadmin/superAdminFixLineItemsWithoutVoid.js';
import './client/app/superadmin/_momentDefinitions.js';
import './client/app/superadmin/_momentDefinitionEditor.js';
import './client/app/superadmin/performanceMonitoring.js';
import './client/app/superadmin/permissionsIndex.js';
import './client/app/superadmin/permission.js';
import './client/app/superadmin/switchOrg.js';
import './client/app/superadmin/fixWithdrawn.js';
import './client/app/superadmin/fixWithdrawnDetails.js';
import './client/app/superadmin/fixes/fixes.js';
import './client/app/superadmin/fixes/fixAdyenReports.js';
import './client/app/superadmin/fixes/backparseAdyen.js';
import './client/app/superadmin/fixes/generateChargebackInvoices.js';
import './client/app/superadmin/fixes/chargebackInvoicesTransferFunds.js';
import './client/app/superadmin/fixes/runInvoicesNow.js';
import './client/app/superadmin/fixes/runGroupSyncNow.js';
import './client/app/superadmin/fixes/runDeferredEmailsNow.js';
import './client/app/superadmin/reports/superAdminReports.js';
import './client/app/superadmin/reports/superAdminReportsFailedAutoPay.js';
import './client/app/superadmin/reports/superAdminReportsFailedInvoicing.js';
import './client/app/superadmin/resetPins.js';
import './client/app/dashboard/dashboardFamily.js';
import './client/app/dashboard/dashboardStaff.html';
import './client/app/moments/moment.js';
import './client/app/search/search.js'
import './client/app/people/_addRelationship.js';
import './client/app/people/person.js';
import './client/app/people/people.js';
import './client/app/people/peopleContainer.js';
import './client/app/people/dataValidationBuilder.js';
import './client/app/people/pinCodeCheckin.js';
import './client/layout/kiosk.html';
import './client/app/food/foodDetail.js';
import './client/app/people/activations.js';
import './client/app/people/_personCaptivePaymentMethods.js';
import './client/layout/mobileCaptive.html';
import './client/layout/emptyLayout.html';
import './client/layout/help.js';
import './client/layout/helpRedirect.js';
import './client/app/superadmin/benchmarkStats.js';
import './client/app/superadmin/manualMigrations.js';
import './client/app/curriculum/curriculumTheme.js';
import './client/app/billing/recentEnrollmentsAirSlate.js';
import './client/app/billing/queuedPayments.js';
import './client/app/superadmin/testZkTecoRetries.js';
import './client/app/superadmin/fixes/testAdyenChunk.js';
import './client/app/billing/reports/billingReportQbAuditLog.js';
import './client/app/external/mediaLike.html';
import './client/app/inquiries/registration.js';
import './client/app/inquiries/registrationBrightside.js';
import './client/app/inquiries/registrationLightbridge.js';
import { Session } from 'meteor/session';


var subsCache = new SubsCache(-1, -1);
var globalLoggedInSubs = function () {
  return [subsCache.subscribe("userData"), subsCache.subscribe("theOrg"), subsCache.subscribe("theGroups"), subsCache.subscribe("theMomentDefinitions"), subsCache.subscribe("theLoggedinPeople")];
};

// Temporary solution for BUGS-2580, uses Restivus Meteor package
if (Meteor.isServer) {
  // Global API configuration
  let restApi = new Restivus({
    prettyJson: true
  });

  restApi.addRoute('switchable-sites', {authRequired: false}, {
    get: async function () {


      let params = this.queryParams
      let person = await People.findOneAsync({_id: params.person})
      let org = await Orgs.findOneAsync({_id: params.org})
      return await OrgsUtil.getSwitchableSites(org, person)
    },
  });
}

const hooksExceptions = [
  'login',
  'loginLegacy',
  'loginCognito',
  'loginCognitoReset',
  'register',
  'registrationFlow',
  'registrationFlowCompleted',
  'forgotpassword',
  'code',
  'mediaLike',
  'restCreateUser',
  'restResendInvitation',
  'restMediaRedirect',
  'restEmailOpenImage',
  'restCreatePendingMoment',
  'restRealtimeEmailOpenImage',
  'kiosk-pin-code-checkin',
  'createAccountWizard',
  'verify-email',
  'inquiryRegistration',
  'redirector',
  "restCashnetTransaction",
  "cashnetSignout",
  "restQbTransactionsBatch",
  "restQbTransactionsAudit",
  "restAdyenStandardNotification",
  "onboarding",
  "restAppDownload"
];

Accounts.onLoginFailure(function (loginAttempt) {
  if (loginAttempt && loginAttempt.error && loginAttempt.error.message) {
    if (loginAttempt.error.message.indexOf("logged out by the server") != -1) {
      FlowRouter.go("/login");
    } else if (loginAttempt.error.message === "Incorrect password" || loginAttempt.error.message === "User not found") {
      subsCache.clear();
      Session.clear();
      localStorage.clear();
      console.log("Login failed. Please try again.");
    }
  }
});

// BeforeHooks
// Show login screen unless signed in
var FlowBeforeHooks = {
  isLoggedIn: function (context, redirect) {
    // if (context && context.route && context.route.name && hooksExceptions.indexOf(context.route.name) >= 0) {
    //   return true;
    // }
    const user = Meteor.user();
    // console.log(context);
    // console.log(context.route.name)
    if (!user && !Meteor.loggingIn()) {
      console.log("going to redirect")
      if (Session.get('isIdpLogin')) {
        Meteor.callAsync('getSsoUnauthorizedUrl')
        .then((data)=>{
          Session.set('isIdpLogin', false);
          window.location.href = data;
        });
        return;
      }
      redirect('/login');
    } else if (user && user.pending && context.route.name != "paymentStatementDashboard") {
      console.log("going to redirect cause name is wrong")
      Meteor.logout(function (err) {
        if (err) {
          console.log('Error logging out: ' + err);
        } else {
          Session.set('needIdpLogout', true);
          FlowRouter.go("/login");
        }
      });
    } else {
      // TODO: validate the kios section setup
      const kioskLocation = Session.get("kioskLocation");
      if (kioskLocation && kioskLocation != "") {
        redirect('/kiosk/' + kioskLocation);
      } else {
        // this.next();
        // TODO: pretty sure do nothing
      }
    }
  }
}

function waitOnSubs(subs) {
  if (Meteor.status().connected) {
    return subs;
  }
  else {
    this.render();
  }
}
function loadingAction() {
  if (!Meteor.loggingIn() && this.ready()) {
    this.render();
  } else {
    this.render('loading');
  }
}

// FlowRouter.globals.push({
//   waitOn() {
//     if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
//     console.log("global waitOn")
//     return globalLoggedInSubs();
//   }
// });

// FlowRouter.onBeforeAction(IR_BeforeHooks.isLoggedIn, {except: hooksExceptions});
// FlowRouter.onRun(IR_BeforeHooks.isLoggedIn, {except: hooksExceptions});

// TODO: model in exceptions!!!
FlowRouter.triggers.enter([FlowBeforeHooks.isLoggedIn], { except: hooksExceptions });

FlowRouter.route('*', {
  action: function () {
    FlowRouter.go("/");
  }
});



// Routes
// NOTE: the waitOne hook runs first- to implement routing based login logic we are checking the meteor status of the user and falling into the trigger
// IF we add any hooks that fall between waitOn and triggersEnter then you will need to replicate the fall through logic https://github.com/VeliovGroup/flow-router/tree/master/docs

FlowRouter.route('/', {
  name: "dashboard",
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs()
  },
  triggersEnter: [(context, redirect) => {
    redirect("/my-site");
  }],
});

FlowRouter.route('/payment-statements', {
  name: 'paymentStatementDashboard',
  whileWaiting() {
    this.render('_bare_application', 'loading', {})
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return import('./client/app/paymentStatements/paymentStatements.js');
  },
  action(params) {
    this.render('_bare_application', 'paymentStatements', params)
  }
});

FlowRouter.route('/overview', {
  name: 'dashboardOverview',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg","theLoggedinPeople", "theInvoices"];
    return allRequiredSubscription(requiredSubs).concat([
      subsCache.subscribe("thePerson"),
      import('./client/app/dashboard/dashboard'),
      import('./client/app/dashboard/dashboardFamily.js'),
      import('./client/app/dashboard/dashboardStaff.html'),
    ])
  },
  triggersEnter: [(context, redirect) => {
  }],
  action(params) {
    var userType = Meteor.user() && Meteor.user().fetchPerson() ? Meteor.user().fetchPerson().type : "notFound";
    let template = 'loading';

    if (userType === "staff"){
      FlowRouter.redirect('/groups');
      return;
    }
    else if (userType === PeopleTypes.FAMILY && Orgs.current().hasCustomization(AvailableCustomizations.FAMILY_ADVANCED_DASHBOARD)) {
      FlowRouter.redirect("/overview-family");
    }

    switch (userType) {
      case "staff":
        template = 'dashboardStaff';
        break;
      case "family":
        template = 'dashboardFamily';
        break;
      case "admin":
        template = 'dashboard';
        break;
    }
    this.render('application', template, params);

  },
});

FlowRouter.route('/overview-family', {
  name: 'familyAdvancedDashboard',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    MethodDataManager.fetchRelationships();
    const requiredSubs = ["userData", "theOrg","theLoggedinPeople", "theInvoices", "theReservations"];
    return allRequiredSubscription(requiredSubs).concat([
      subsCache.subscribe("thePerson")
    ])
  },
  triggersEnter: [(context, redirect) => {
    var user = Meteor.user();
    if (user) {
      var person = user.fetchPerson();
      if (!(Orgs.current().hasCustomization(AvailableCustomizations.FAMILY_ADVANCED_DASHBOARD)) || (person && person.type !== PeopleTypes.FAMILY)) {
        redirect("/overview");
      }
    }
  }],
  action(params) {
    this.render('application', 'dashboardFamilyAdvanced', params);
  },
});

FlowRouter.route('/explore', {
  name: 'explore',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/dashboard/explore/explore.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["admin", "staff"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/");
    }
  }],
  action(params) {
    this.render('application', "explore", params);
  },
});

FlowRouter.route('/my-site', {
  name: 'dashboardSite',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    const requiredSubs = [
      "theCheckInRatio",
      "userData",
      "theOrg",
      "theGroups",
      "theUsers",
      "theInvoices",
      "theLoggedinPeople",
      "theCheckinStatsCount",
      "theAbsentPeople",
      "expressDriveUpPeople"
    ];

    return allRequiredSubscription(requiredSubs).concat([
      Meteor.subscribe('dashboardMomentsWithOptions', {showAll: true}),
      Meteor.subscribe("theReservations", { includeCancellations: true }),
      import('./client/app/dashboard/dashboardSite.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    // we load my-site by default and redirect to dashboard for non-admins
    if (_.indexOf(["admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/overview");
    }
  }],
  action() {
    this.render('application', 'dashboardSite', {});
  },
});

FlowRouter.route('/media-gallery', {
  name: 'mediaGallery',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/dashboard/mediaGallery.js'));
  },
  triggersEnter: [(context, redirect) => {
    // we load my-site by default and redirect to dashboard for non-admins
    if (_.indexOf(["admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/");
    }
  }],
  action(params) {
    this.render('application', 'mediaGallery', params);
  },
});

FlowRouter.route('/media-review', {
  name: 'mediaReview',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      subsCache.subscribe("theMediaReview"),
      import('./client/app/dashboard/mediaReview.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    // we load my-site by default and redirect to dashboard for non-admins
    if (_.indexOf(["admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/");
    }
  }],
  action(params) {
    this.render('application', 'mediaReview', params);
  },
});

FlowRouter.route('/express-drive-up', {
  name: 'expressDriveUp',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople", "theGroups", "theMomentDefinitions", "expressDriveUpPeople"];
    return allRequiredSubscription(requiredSubs).concat(import('./client/app/dashboard/expressDriveUp.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/");
    }
  }],
  action(params) {
    this.render('application', 'expressDriveUp', params);
  },
});

FlowRouter.route('/not_authorized', {
  name: 'not_authorized',
  action(params) {
    this.render('application', 'not_authorized');
  }
});

FlowRouter.route('/login/legacy', {
  name: 'loginLegacy',
  conf: {
    forceReRender: true
  },
  waitOn() {
    return import('./client/app/login/loginLegacy.js');
  },
  action(params) {
    subsCache.clear();
    this.render('loginLegacy', params);
  }
});

FlowRouter.route('/login/cognito', {
  name: 'loginCognito',
  conf: {
    forceReRender: true,
  },
  waitOn() {
    return import('./client/app/login/loginCognito.js');
  },
  action(params) {
    subsCache.clear();
    this.render('loginCognito', params);
  }
});

FlowRouter.route('/login/cognito-reset', {
  name: 'loginCognitoReset',
  conf: {
    forceReRender: true,
  },
  waitOn() {
    return import('./client/app/login/loginCognito.js');
  },
  action(params) {
    subsCache.clear();
    this.render('loginCognito', params);
  }
});

FlowRouter.route('/login', {
  name: 'login',
  conf: {
    forceReRender: true,
  },
  triggersEnter: [() => {
    // Clear any cached subscriptions
    subsCache.clear();
    // Clear any session data
    Session.clear();

    if (Session.get('needIdpLogout') ?? false) {
      Meteor.callAsync("getSsoLogoutUrl", window.location)
      .then((data)=>{
        window.location.href = data;
      })
      .catch((error)=>{
        console.log('Could not redirect to the IDP for logout');
      });
      Session.set('needIdpLogout', false);
      return;
    }

    // Check if user is already logged in
    if (!Meteor.userId()) {
      Meteor.callAsync("getSsoAuthData", window.location)
      .then((data)=>{
        localStorage.setItem('oauthState', data.oauthState);
        localStorage.setItem('oauthCodeVerifier', data.oauthCodeVerifier);
        window.location.href = data.url;
      })
      .catch((error)=>{
        console.log('Could not redirect to the IDP for login');
      });
    } else {
      FlowRouter.go('dashboardSite');
    }
  }]
});

FlowRouter.route('/sign-out', {
  name: 'signOut',
  action(params, queryParams) {
    this.render('application', 'loading', {});

    if (typeof subsCache !== 'undefined') {
      subsCache.clear();
    }

    // Clear session data
    Session.clear();
    Session.set('needIdpLogout', true);
    localStorage.clear();

    Accounts._unstoreLoginToken();

    Meteor.logout((err) => {
      if (err) {
        console.log('Error logging out: ' + err);
        FlowRouter.go("/login");
        return;
      }

      const logoutPromise = Meteor.callAsync("getSsoLogoutUrl", window.location);

      const timeoutPromise = new Promise((resolve) => {
        setTimeout(() => {
          resolve(null);
        }, 3000);
      });

      Promise.race([logoutPromise, timeoutPromise])
        .then((data) => {
          if (data) {
            window.location.href = data;
          } else {
            FlowRouter.go("/login");
          }
        })
        .catch((error) => {
          console.log('Could not redirect to the IDP for logout:', error);
          FlowRouter.go("/login");
        });
    });
  }
});

FlowRouter.route('/forgotpassword',
  {
    name: 'forgotpassword',
    conf: {
      forceReRender: true,
    },
    triggersEnter: [() => {
      if (!Meteor.settings.public.allowLegacyLogin) {
        FlowRouter.go("/login");
      }
    }],
    waitOn() {
      return import('./client/app/login/loginLegacy.js');
    },
    action(params, qs) {
      this.render('loginLegacy', qs);
    }
  });


FlowRouter.route('/register/:invitationToken', {
  name: 'register',
  conf: {
    forceReRender: true,
  },
  waitOn() {
    return import('./client/app/login/loginLegacy.js');
  },
  action(params) {
    this.render('loginLegacy', params);
  }
});

FlowRouter.route('/loading', {
  name: 'loading',
  action(params) {
    subsCache.clear();
    this.render('loading', params);
  }
});

FlowRouter.route('/code', {
  name: 'code',
  triggersEnter: [(context, redirect) => {
    const options = {
      oauthState: localStorage.getItem('oauthState'),
      oauthCodeVerifier: localStorage.getItem('oauthCodeVerifier'),
      queryParams: FlowRouter.current().queryParams,
      location: window.location
    };
    VerifySsoCode(options);
  }]
});


// FlowRouter.route('/now', {
//   name: 'dashboardEngagement',
//   layoutTemplate: "application",
//   template: 'dashboardEngagement',
//   yieldRegions: {
//     'dashboardHeader': {to: 'header'}
//   },
//   onBeforeAction: function () {
//       if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
//         this.render("403");
//       } else {
//        this.next();
//       }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat());
//   }
// });
//
FlowRouter.route('/people', {
  name: 'people',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params,qs) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    let requiredSubs =[];
    const subs = Meteor.connection._subscriptions; //all the subscriptions that have been subscribed.
    for (sub of Object.values(subs)) {
      if (sub.name == "thePeople" || sub.name == "thePeopleDirectory") {
        subsCache.clear(sub.name);
      }
    }
    requiredSubs = ["userData", "theOrg", "theGroups", "theMomentDefinitions", "theUsers", "thePeopleDataValidation", "theLoggedinPeople","thePeopleStaffAndAdminForOrg"];
    return allRequiredSubscription(requiredSubs).concat([
      import('./client/app/people/people.js'),
      import('./client/app/people/peopleContainer.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    Session.set("goBackRoute", "/people");
  }],
  action(params) {
    if (subsCache.ready())
      this.render('application', 'peopleContainer', params);
  },
});

FlowRouter.route('/people/data-validation/:_id?', {
  name: 'people.dataValidationBuilder',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    let activitySubs = [];
    if (params._id) {
      activitySubs.push(
        Meteor.subscribe('thePeopleDataValidation', { _id: params._id })
      );
    }
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople", "theGroups", "theMomentDefinitions"];
    return allRequiredSubscription(requiredSubs).concat(activitySubs).concat(import('./client/app/people/dataValidationBuilder.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'dataValidationBuilder', params);
  },
});

FlowRouter.route('/inquiries', {
  name: 'inquiries',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg",  "theGroups", "theMomentDefinitions", "theLoggedinPeople","thePeopleStaffAndAdminForOrg"];
    return allRequiredSubscription(requiredSubs).concat(import('./client/app/inquiries/inquiries.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'inquiries', params);
  },
});
//
// FlowRouter.route('/inquiries/campaigns',  {
//   name: 'inquiries.campaigns',
//   prettyName: 'Inquiries - Campaigns',
//   layoutTemplate: "application",
//   template: 'campaigns',
//   onBeforeAction: function () {
//     if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
//       this.render("403");
//     } else {
//      this.next();
//     }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat());
//   }
// });
//
FlowRouter.route('/activations', {
  name: 'activations',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params, qs, ready) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/people/activations.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["staff", "admin"], userType) == -1) {
      redirect("/not_authorized");
    }
  }],
  action(params, qs) {
    this.render('application', 'activations', params);
  }
});

FlowRouter.route('/calendar', {
  name: 'calendar',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params, qs, ready) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    return globalLoggedInSubs().concat(import('./client/app/calendar/calendar.js'));
  },
  action(params) {
    let layoutTemplate = 'application';
    if (FlowRouter.current().queryParams.view === "print") {
      layoutTemplate = "printableLayout";
    }
    this.render(layoutTemplate, 'calendar', params);
  }
});

FlowRouter.route('/calendar/print', {
  name: 'calendar/print',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params, qs, ready) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    return globalLoggedInSubs().concat([
      subsCache.subscribe("theAnnouncements"),
      subsCache.subscribe("theFood"),
      subsCache.subscribe("theReservations"),
      import('./client/app/calendar/calendar.js')
    ]);
  },
  action(params) {
    this.render('calendar', params);
  }
});

FlowRouter.route('/people/:_id', {
  name: 'person',
  whileWaiting() {
    let layoutTemplate = FlowRouter.current().queryParams.printable == 'true' ? 'printableLayout' : 'application';
    this.render(layoutTemplate, 'loading', {});
  },
  waitOn(params, qs, ready) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    Meteor.defer(() => {
      // Use a throttled version to prevent multiple calls
      _.debounce(() => MethodDataManager.fetchRelationships(), MethodDataManager.RELATIONSHIP_DELAY);
    });

    const essentialSubs = ['userData', 'theOrg', 'theLoggedinPeople'];
    const essentialSubscriptions = allRequiredSubscription(essentialSubs).concat([
      subsCache.subscribe('thePerson', { personById: params._id }),
      import('./client/app/people/person.js'),
      import('./client/layout/not_found.html')
    ]);

    Meteor.defer(() => {
      const nonEssentialSubs = [
        'theGroups',
        'theUsers',
        'theAnnouncements',
        'theReservations',
        'scopedOrgs',
        'thePeopleStaffAndAdminForOrg'
      ];

      nonEssentialSubs.forEach((sub) => {
        subsCache.subscribe(sub);
      });
    });

    return essentialSubscriptions;
  },
  data(params, qs) {
    return People.findOne({ _id: params._id });
  },
  triggersEnter: [(context, redirect) => {}],
  onNoData(params, qs) {
    this.render('application', 'not_found');
  },
  action(params, qs, person) {
    let layoutTemplate = FlowRouter.current().queryParams.printable == 'true' ? 'printableLayout' : 'application';

    this.render(layoutTemplate, 'person', person);
  }
});

FlowRouter.route('/people/:_id/summaryReport', {
  name: 'people.summaryReport',
  whileWaiting() {
    this.render('loading');
  },
  waitOn(params, qs, ready) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople", "theGroups", "theMomentDefinitions"];
    return allRequiredSubscription(requiredSubs).concat([
      subsCache.subscribe("thePerson", { personById: params._id }),
      subsCache.subscribe("theUsers"),
      import('./client/app/people/summaryReport.js')
    ])
    
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["staff", "admin"], userType) == -1) {
      redirect("/not_authorized");
    }
  }],
  action(params, qs, person) {
    this.render('summaryReport');
  },
});

FlowRouter.route('/people/:_id/portfolio', {
  name: 'people.portfolio',
  whileWaiting() {
    let layoutTemplate = 'application';
    if (FlowRouter.current().queryParams.view == "print") {
      layoutTemplate = "printableLayout";
    }
    this.render(layoutTemplate, 'loading', {});
  },
  waitOn(params, qs, ready) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople", "theGroups", "theMomentDefinitions"];
    return allRequiredSubscription(requiredSubs).concat([
      subsCache.subscribe("thePerson", { personById: params._id }),
      import('./client/app/people/personPortfolio.js')
    ])
  },
  action(params, qs, person) {
    let layoutTemplate = 'application';
    if (FlowRouter.current().queryParams.view == "print") {
      layoutTemplate = "printableLayout";
    }
    this.render(layoutTemplate, 'personPortfolio', params);
  }
});

FlowRouter.route('/people/:_id/captive-payment-methods', {
  name: 'people.paymentMethods',
  waitOn(params,qs) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople", "theGroups", "theMomentDefinitions"];
    return allRequiredSubscription(requiredSubs).concat([
      subsCache.subscribe('thePerson', { personById: params._id }),
      import('./client/app/people/_personCaptivePaymentMethods.js'),
      import('./client/layout/mobileCaptive.html')
    ]);
  },
  action(params) {
    this.render("mobileCaptive", "personCaptivePaymentMethods", params);
  },
});

FlowRouter.route('/groups', {
  name: 'groups',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople", "theGroups", "theMomentDefinitions"];
    return allRequiredSubscription(requiredSubs).concat([
      subsCache.subscribe("theFood"),
      Meteor.subscribe("theReservations", { includeCancellations: true }),
      import('./client/app/groups/groups.js')
    ])
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'groups', params);
  }
});
// //
// FlowRouter.route('/groups/:_id', {
//   name: 'group',
//   waitOn(params) {
//     if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
//     return globalLoggedInSubs().concat([
//       Meteor.subscribe("theRelationships"), Meteor.subscribe("theCurriculum"), Meteor.subscribe("theFood"), Meteor.subscribe("theReservations", {includeCancellations: true})
//     ])
//   },
//   async data(params) {
//     return await Groups.findOneAsync({_id: params._id});
//   },
//   triggersEnter: [(context, redirect, stop, data) => {
//     console.log(data);
//     if (!data) redirect("/not_found");
//     if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
//       redirect("/not_authorized");
//     }
//   }],
//   action(params) {
//     this.render('application', 'group', params);
//   },
// });
//
FlowRouter.route("/search", {
  name: 'search',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/search/search.js'));
  },
  action(params) {
    this.render('application', 'search', params);
  },
});

FlowRouter.route('/activities', {
  name: 'activities',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg", "theGroups",  "theUsers", "theLoggedinPeople"];
    return allRequiredSubscription(requiredSubs).concat(import('./client/app/curriculum/curriculumContainer'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('curriculumId', "");
    }
  }],
  action(params) {
    this.render('application', 'curriculumContainer', params);
  },
});

FlowRouter.route('/activities/review/:_id', {
  name: 'activities.review',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/curriculum/curriculumReview'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('curriculumId', "");
    }
  }],
  action(params) {
    this.render('application', 'curriculumReview', params);
  },
});

FlowRouter.route('/activities/theme/review/:_id', {
  name: 'activities.theme.review',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/curriculum/themeBank/review'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('curriculumId', "");
    }
  }],
  action(params) {
    this.render('application', 'curriculumThemeBankReview', params);
  },
});

FlowRouter.route('/activities/builder/:_id?', {
  name: 'activities.builder',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn() {
    return globalLoggedInSubs().concat(import('./client/app/curriculum/curriculumBuilder'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('curriculumId', "");
    }
  }],
  action(params) {
    this.render('application', 'curriculumBuilder', params);
  },
});

FlowRouter.route('/activities/themes/:_id', {
  name: 'activities.theme.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn() {
    return globalLoggedInSubs().concat(import('./client/app/curriculum/curriculumTheme.js'));
  },
  async data(params, qs) {
    var curriculumTheme;
    await Meteor.callAsync('getCurriculumThemeById', params._id).then((res) => {
      curriculumTheme = new CurriculumTheme(res);
    });
    return curriculumTheme;
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('curriculumId', "");
    }
  }],
  action(params, qs, curriculumTheme) {
    this.render('application', 'curriculumTheme', curriculumTheme);
  },
});


FlowRouter.route('/activities/:_id', {
  name: 'activities.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn() {
    return globalLoggedInSubs().concat(import('./client/app/curriculum/curriculum.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('curriculumId', "");
    }
  }],
  action(params) {
    this.render('application', 'curriculum');
  },
});




FlowRouter.route('/scheduling', {
  name: 'scheduling',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg", "theGroups", "theMomentDefinitions", "theLoggedinPeople"];
    return allRequiredSubscription(requiredSubs).concat(import('./client/app/reservations/reservations.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('reservationId', "");
    }
  }],
  action(params) {
    this.render('application', 'reservations', params);
  },
});
//
// FlowRouter.route('/reservations/:_id', {
//   name: 'reservations.show',
//   prettyName: 'Scheduling',
//   layoutTemplate: "application",
//   template: 'reservation',
//   goBackable: true,
//   goBackRoute: "/reservations/",
//   onBeforeAction: function () {
//       if (_.indexOf(["staff", "admin"], Meteor.user() && Meteor.user().fetchPerson().type) == -1) {
//         this.render("403");
//       } else {
//        this.next();
//       }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat([
//       Meteor.subscribe("theReservations", {reservationId: this.params._id})
//     ]));
//   },
//   data: function() {
//     if (this.ready()) {
//
//       var reservation = Reservations.findOne({orgId: Meteor.user() && Meteor.user().orgId, _id:this.params._id});
//       console.log(reservation);
//       if (!reservation)
//         this.render("notFound");
//       else
//         return reservation;
//     }
//   }
// });
//
FlowRouter.route('/announcements', {
  name: 'announcements',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/announcements/announcements.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('announcementId', "");
    }
  }],
  action(params) {
    this.render('application', 'announcements', params);
  },
});
//
// FlowRouter.route('/forms', {
//   name: 'forms',
//   prettyName: 'Forms',
//   layoutTemplate: "application",
//   template: 'forms',
//   onBeforeAction: function () {
//       if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
//         this.render("403");
//       } else {
//        this.next();
//       }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat());
//   }
// });
//
// FlowRouter.route('/forms/:id', {
//   name: 'form.show',
//   prettyName: 'Forms',
//   layoutTemplate: "application",
//   onBeforeAction: function () {
//       if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
//         this.render("403");
//       } else {
//        this.next();
//       }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat());
//   },
//   loadingTemplate: "loading",
//   action: function() {
//     var formId = this.params.id;
//     if (!formId)
//       this.render("notFound");
//     else {
//       this.render(formId);
//       this.render('formsHeader', {to:"header"});
//     }
//   }
// });
//
// FlowRouter.route('/moments/new', {
//   name: 'momentEntry',
//   layoutTemplate: "application",
//   template: "momentEntry",
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat([
//       Meteor.subscribe("theRelationships"),
//       Meteor.subscribe("theFood")
//     ]));
//   }
// });
//
FlowRouter.route('/moments/:id', {
  name: 'moment.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubs = ["userData", "theOrg", "theGroups", "theMomentDefinitions", "theLoggedinPeople"];
    return allRequiredSubscription(requiredSubs).concat([
      Meteor.subscribe("dashboardMoments", 1, true, params.id)
    ]).concat(import('./client/app/moments/moment.js'));
  },
  data(params, qs) {
    var moment = Moments.findOne({ _id: params.id });
    subsCache.subscribe("thePeopleListByIds", { peopleIds: moment["taggedPeople"] })
    return moment;
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    }
  }],
  action(params, qs, moment) {
    this.render('application', 'moment', moment);
  },
});
//
// FlowRouter.route('/reports', {
//   name: 'reports',
//   prettyName: 'Reports',
//   layoutTemplate: "application",
//   template: 'reports',
//   onBeforeAction: function () {
//       const allowed = processPermissions({
//         assertions: [{ context: "reports/standard", action: "read" }],
//         evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
//       });
//       if (!allowed) {
//         this.render("403");
//       } else {
//        this.next();
//       }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat());
//   }
// });
FlowRouter.route('/reports/:reportName?', {
  name: 'reports',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    return [
      subsCache.subscribe('userData'),
      subsCache.subscribe('theOrg'),
      subsCache.subscribe('theLoggedinPeople'),
      subsCache.subscribe('theGroups'),
      subsCache.subscribe('theMomentDefinitions'),
      import('./client/app/reports/reportsContainer.js'),
      import('./client/app/reports/reportAttendance.js'),
      import('./client/app/reports/reportAdp.js'),
      import('./client/app/reports/attendanceGridByDate.js'),
      import('./client/app/reports/reportAttendanceWeekly.js'),
      import('./client/app/reports/reportBillingExpanded.js'),
      import('./client/app/reports/reportBusRoster.js'),
      import('./client/app/reports/reportCaliforniaAttendanceAndFiscal.js'),
      import('./client/app/reports/reportCaliforniaEnrollmentAttendanceRegister.js'),
      import('./client/app/reports/reportCaliforniaPopulation.js'),
      import('./client/app/reports/reportCaliforniaRoster.js'),
      import('./client/app/reports/reportCheckInOutNoPinCode.js'),
      import('./client/app/reports/reportClassList.js'),
      import('./client/app/reports/reportClassListSchedule.js'),
      import('./client/app/reports/reportCurriculum.js'),
      import('./client/app/reports/reportDocumentStatus.js'),
      import('./client/app/reports/reportEnrollmentStatusV2.js'),
      import('./client/app/reports/reportGeneralAttendance.js'),
      import('./client/app/reports/reportGroupEnrollments.js'),
      import('./client/app/reports/reportImmunizations.js'),
      import('./client/app/reports/reportLaborUtilization.js'),
      import('./client/app/reports/reportMeals.js'),
      import('./client/app/reports/reportMealsDetail.js'),
      import('./client/app/reports/reportMoments.js'),
      import('./client/app/reports/reportPayrollRatio.js'),
      import('./client/app/reports/reportProfile.js'),
      import('./client/app/reports/reportReservations.js'),
      import('./client/app/reports/reportRoster.js'),
      import('./client/app/reports/reportScheduling.js'),
      import('./client/app/reports/reportsCustomBuilderContainer.js'),
      import('./client/app/reports/reportSignInGrid.js'),
      import('./client/app/reports/reportStaffPay.js'),
      import('./client/app/reports/reportStaffTime.js'),
      import('./client/app/reports/reportSubsidy.js'),
      import('./client/app/reports/reportWaitList.js'),
    ];
  },
  triggersEnter: [(context, redirect) => {
    const allowed = processPermissions({
      assertions: [{ context: "reports/standard", action: "read" }],
      evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff"
    });

    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["staff", "admin"], userType) == -1 || !allowed) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    let layoutTemplate = 'application';
    let layoutContainer = 'reportsContainer';

    if (FlowRouter.current().queryParams.printable == "true") {
      layoutTemplate = "printableLayout";
      layoutContainer = params.reportName;
    }

    this.render(layoutTemplate, layoutContainer, params);
  },
});

FlowRouter.route('/reports-custom-builder/:reportName?/:savedReportId?', {
  name: 'reports-custom',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    return [
      subsCache.subscribe('userData'),
      subsCache.subscribe('theOrg'),
      subsCache.subscribe('theGroups'),
      subsCache.subscribe('theMomentDefinitions'),
      subsCache.subscribe('theLoggedinPeople'),
      import('./client/app/reports/reportsCustomBuilderContainer.js'),
    ];
  },
  triggersEnter: [(context, redirect) => {
    const allowed = processPermissions({
      assertions: [{ context: "reports/standard", action: "read" }],
      evaluator: (thisPerson) => thisPerson.type === USER_TYPES.ADMIN || thisPerson.type === USER_TYPES.STAFF
    });
    
   const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf([USER_TYPES.STAFF, USER_TYPES.ADMIN], userType) === -1 || !allowed) {
      redirect(urls.NOT_AUTH);
    }
  }],
  action(params) {
    let layoutTemplate = 'application';
    let layoutContainer = 'reportsCustomBuilderContainer';

    this.render(layoutTemplate, layoutContainer, params);
  },
});

FlowRouter.route('/reports-custom-saved', {
  name: 'reports-custom-saved',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    return [subsCache.subscribe("userData"), subsCache.subscribe("theOrg"), subsCache.subscribe("theLoggedinPeople")];
  },
  triggersEnter: [(context, redirect) => {
    const allowed = processPermissions({
      assertions: [{ context: "reports/standard", action: "read" }],
      evaluator: (thisPerson) => thisPerson.type === USER_TYPES.ADMIN || thisPerson.type === USER_TYPES.STAFF
    });

    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf([USER_TYPES.STAFF, USER_TYPES.ADMIN], userType) === -1 || !allowed) {
      redirect(urls.NOT_AUTH);
    }
  }],
  action(params) {
    let layoutTemplate = 'application';
    let layoutContainer = 'reportsSavedList';

    this.render(layoutTemplate, layoutContainer, params);
  },
});
// FlowRouter.route('/reports/:id', {
//   name: 'report.show',
//   prettyName: 'Reports',
//   layoutTemplate: function() {
//     if (FlowRouter.current().params.query.printable == "true")
//       return "printableLayout";
//     else
//       return "application";
//   },
//   goBackable: true,
//   goBackRoute: "/reports/",
//   onBeforeAction: function () {
//       const allowed = processPermissions({
//         assertions: [{ context: "reports/standard", action: "read" }],
//         evaluator: (thisPerson) => thisPerson.type=="admin" || thisPerson.type=="staff"
//       });
//       if (!allowed) {
//         this.render("403");
//       } else {
//        this.next();
//       }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat());
//   },
//   loadingTemplate: "loading",
//   action: function() {
//     var formId = this.params.id;
//     if (!formId)
//       this.render("notFound");
//     else {
//       this.render(formId);
//       this.render('reportsHeader', {to:"header"});
//     }
//   }
// });
//
//
FlowRouter.route('/food', {
  name: 'food',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/food/food.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('foodId', "");
    }
  }],
  action(params) {
    this.render('application', 'food', params);
  },
});

FlowRouter.route('/food/:_id', {
  name: 'food.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('theFood', { foodId: params._id }),
      import('./client/app/food/foodDetail.js')
    ]);
  },
  data(params, qs) {
    var food = Foods.findOne({ orgId: Meteor.user() && Meteor.user().orgId, _id: params._id });
    return food;
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    } else {
      Session.set('foodId', "");
    }
  }],
  action(params, qs, food) {
    this.render('application', 'foodDetail', food);
  },
});
//
// FlowRouter.route('/more', {
//   name: 'more',
//   layoutTemplate:"application",
//   template:"more",
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat());
//   }
// });
//
// FlowRouter.route('/upload-queue', {
//   name: 'upload-queue',
//   layoutTemplate:"application",
//   template:"uploadQueue",
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat());
//   }
// });
//
FlowRouter.route('/pinCodeCheckin', {
  name: 'pin-code-checkin',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople", "theGroups", "theMomentDefinitions"];
    return allRequiredSubscription(requiredSubs).concat([
      subsCache.subscribe('theAnnouncements'),
      import('./client/app/people/pinCodeCheckin.js')
    ]);
  },
  action(params) {
    this.render('application', 'pinCodeCheckin', params);
  },
});
//
FlowRouter.route('/kiosk/pinCodeCheckin', {
  name: 'kiosk-pin-code-checkin',
  whileWaiting() {
    this.render("loading", {})
  },
  waitOn(params) {
    MethodDataManager.fetchRelationships();
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople", "theGroups", "theMomentDefinitions"];
    return allRequiredSubscription(requiredSubs).concat([
      import('./client/app/people/pinCodeCheckin.js'),
      import('./client/layout/kiosk.html')
    ]);
  },
  action(params) {
    console.log("rendering with params: ", params);
    this.render('kiosk', 'pinCodeCheckin', params);
  },
});
//
// FlowRouter.route('/billingAdmin', {
//   name: 'billingAdmin',
//   prettyName: 'Billing',
//   layoutTemplate: 'application',
//   template: 'billingAdmin',
//   onBeforeAction: function() {
//
//       if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
//         this.render("403");
//       } else {
//         if (Orgs.current() && !Orgs.current().billing)
//           this.redirect('/billingSetup');
//
//         this.next();
//       }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat([Meteor.subscribe("theRelationships")]) );
//   }
// });
//
FlowRouter.route('/billing/admin', {
  name: 'billingAdminContainer',
  whileWaiting() {
    this.render('application', 'billingAdminContainer', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    const requiredSubs = ["userData", "theOrg", "theLoggedinPeople"]; 
    return allRequiredSubscription(requiredSubs);
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["staff", "admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }]
});
FlowRouter.route('/billing/admin/reports/:reportName?', {
  name: 'billingAdminReports',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    const requiredSubs = ["userData", "theOrg", "theGroups", "theMomentDefinitions", "theLoggedinPeople"];
    return allRequiredSubscription(requiredSubs).concat(import('./client/app/billing/billingAdminReports.js'));
  },
  triggersEnter: [(context, redirect) => {
    const allowed = processPermissions({
      assertions: [{ context: "billing/reports", action: "read" }, { context: "billing/invoices", action: "read" }],
      evaluator: (thisPerson) => thisPerson.type == "admin"
    });

    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["staff", "admin"], userType) == -1 || !allowed) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'billingAdminReports', params);
  },
});

FlowRouter.route('/billing/admin/configuration', {
  name: 'billingAdminConfiguration',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubscriptions = ["userData", "theOrg", "theGroups", "theMomentDefinitions","theLoggedinPeople"];
    return allRequiredSubscription(requiredSubscriptions).concat(import('./client/app/billing/billingAdminConfiguration.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["staff", "admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'billingAdminConfiguration', params);
  },
});

FlowRouter.route('/billing/admin/payer-reconciliation', {
  name: 'billingAdmin.payer-reconciliation',
  template: 'payerReconciliation',
  triggersEnter: function () {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["staff", "admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  },
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn: function () {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    const requiredSubs = ["userData", "theOrg", "theGroups", "theMomentDefinitions", "theLoggedinPeople"];
    return allRequiredSubscription(requiredSubs).concat(import('./client/app/billing/payerReconciliation.js'));
  },
  action(params) {
    this.render('application', 'payerReconciliation');
  }
});

FlowRouter.route('/billing/invoices/:id', {
  name: 'billingAdmin.invoices.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params,qs) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    const requiredSubs = ["userData", "theOrg", "theGroups", "theLoggedinPeople", "theMomentDefinitions"];
    return allRequiredSubscription(requiredSubs).concat([
      subsCache.subscribe("theInvoices", { invoiceId: params.id }),
      import('./client/app/billing/invoiceDetail.js')
    ]); 
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["staff", "admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'invoiceDetail', params);
  },
});

FlowRouter.route('/billing/admin/chargebacks', {
  name: 'billingAdmin.chargebacks.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
return globalLoggedInSubs().concat(import('./client/app/billing/chargebacks.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'chargebacks', params);
  },
});

FlowRouter.route('/billing/admin/refunds', {
  name: 'billingAdmin.refunds.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/billing/refunds.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'refunds', params);
  },
});

FlowRouter.route('/billing/admin/scheduleChanges', {
  name: 'billingAdmin.scheduleChanges.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/billing/scheduleChanges.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'scheduleChanges', params);
  },
});

FlowRouter.route('/billing/admin/registration-status', {
  name: 'billingAdmin.recentEnrollments.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      import('./client/app/billing/recentEnrollments.js'),
      import('./client/app/billing/recentEnrollmentsAirSlate.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    if (Orgs.current().hasCustomization("integrations/airslate/enabled")) {
      this.render('application', 'recentEnrollmentsAirSlate', params);
    } else {
      this.render('application', 'recentEnrollments', params);
    }
  },
});

FlowRouter.route('/billing/admin/district-employee-review', {
  name: 'billingAdmin.districtEmployeeReview.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/billing/districtEmployeeReview.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'districtEmployeeReview', params);
  },
});

FlowRouter.route('/billing/admin/security-deposits', {
  name: 'billingAdmin.securityDeposits.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubscriptions = ["userData", "theOrg", "theGroups", "theMomentDefinitions","theLoggedinPeople"];
    return allRequiredSubscription(requiredSubscriptions).concat(import('./client/app/billing/securityDeposits.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'securityDeposits', params);
  },
});

FlowRouter.route('/billing/admin/bank-deposits/:depositId?', {
  name: 'billingAdmin.deposits.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/billing/deposits.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'deposits', params);
  },
});

FlowRouter.route('/billing/admin/batch-payments', {
  name: 'billingAdmin.queuedPayments.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/billing/queuedPayments.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    if (_.indexOf(["admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'queuedPayments', params);
  },
});

FlowRouter.route('/billing/admin/hosted-setup', {
  name: 'billingAdminAdyenSetup',
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/billing/billingAdyenOnboarding.js'));
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    console.log("userType", userType);
    if (_.indexOf(["staff", "admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'billingAdyenOnboarding', params);
  }
});

FlowRouter.route('/billing/admin/hosted-balance-platform-setup', {
  name: 'billingAdminAdyenBalancePlatformSetup',
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([]);
  },
  triggersEnter: [(context, redirect) => {
    const userType = (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type) || "";
    console.log("userType", userType);
    if (_.indexOf(["staff", "admin"], userType) == -1) {
      redirect("/not_authorized");
    } else {
      if (Orgs.current() && !Orgs.current().billing) {
        redirect('/billingSetup');
      }
    }
  }],
  action(params) {
    this.render('application', 'billingAdyenBalancePlatformOnboarding', params);
  }
});
//
// FlowRouter.route('/billingSetup', {
//   name: 'billingSetup',
//   prettyName: 'Billing',
//   layoutTemplate: 'application',
//   template: 'billingSetup',
//   onBeforeAction: function () {
//     if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
//       this.render("403");
//     } else {
//      this.next();
//     }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat() );
//   }
// });
//
//
// FlowRouter.route('/billing/admin/payer-reconciliation', {
//   name: 'billingAdmin.payer-reconciliation',
//   prettyName: 'Billing',
//   layoutTemplate:"application",
//   template: 'payerReconciliation',
//   goBackable: true,
//   onBeforeAction: function () {
//     if (_.indexOf(["staff", "admin"], Meteor.user().fetchPerson().type) == -1) {
//       this.render("403");
//     } else {
//      this.next();
//     }
//   },
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat([
//       Meteor.subscribe("theRelationships")
//     ]));
//   },
//   loadingTemplate: "loading"
// });
//
// FlowRouter.route('/billing/cashnet/signout', {
//   name: 'cashnetSignout',
//   layoutTemplate:null,
//   template:'billingCashnetSignout'
// });
//
//
// FlowRouter.route('/welcome', {
//   name: 'welcome',
//   prettyName: 'Welcome',
//   layoutTemplate:'application',
//   template:'dashboardOnboarding',
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat() );
//   }
// });
//
FlowRouter.route("/help", {
  name: 'help',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/layout/help.js'));
  },
  action(params) {
    this.render('application', 'help', params);
  },
});

FlowRouter.route('/superadmin/switch-org', {
  name: 'switchOrg',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/superadmin/switchOrg.js'))
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || (person.type != "admin" && !person.superAdmin)) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'switchOrg', params);
  },
});

FlowRouter.route('/superadmin/manual-migrations', {
  name: 'manualMigrations',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/superadmin/manualMigrations.js'))
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || (person.type !== "admin" && !person.superAdmin)) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'manualMigrations', params);
  },
});

FlowRouter.route('/superadmin/reset-pins', {
  name: 'resetPins',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/superadmin/resetPins.js'))
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || (person.type != "admin" && !person.superAdmin)) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'resetPins', params);
  },
});

FlowRouter.route('/superadmin/moment-definitions/:id', {
  name: 'moment-definition-editor',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('theMomentDefinitions'),
      import('./client/app/superadmin/_momentDefinitionEditor.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'momentDefinitionEditor', params);
  },
});

FlowRouter.route('/superadmin/moment-definitions', {
  name: 'moment-definitions',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('theMomentDefinitions'),
      import('./client/app/superadmin/_momentDefinitions.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'momentDefinitions', params);
  },
});

FlowRouter.route('/superadmin/invoice-management', {
  name: 'superAdminInvoiceManagement',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/superadmin/superAdminInvoiceManagement.html'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'superAdminInvoiceManagement', params);
  },
});

FlowRouter.route('/superadmin/invoice-management/line-items-without-void', {
  name: 'superAdminFixLineItemsWithoutVoid',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/superadmin/superAdminFixLineItemsWithoutVoid.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'superAdminFixLineItemsWithoutVoid', params);
  },
});

FlowRouter.route('/superadmin/invoice-management/redirect/:_id', {
  name: 'superAdminFixLineItemsWithoutVoid.invoices.show',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs();
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    // Call adminSwitchOrgForInvoiceId:
    Meteor.callAsync('adminSwitchOrgForInvoiceId', params._id)
    .then((result)=>{
      location.replace("/loading?setLoc=redirect&desturl=/billing/invoices/" + params._id);
    })
    .catch((error)=>{
      mpSwal.fire("Error", error.reason, "error");
      this.render('application', 'notFound', params);
    });
  },
});

FlowRouter.route('/superadmin/childcareCrm', {
  name: 'childcareCrm',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allChildcareCrmAccounts'),
      import('./client/app/superadmin/childcareCrm.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'childcareCrm', params);
  },
});

FlowRouter.route('/superadmin/childcareCrm/:id', {
  name: 'childcareCrmEditor',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allChildcareCrmAccounts'),
      import('./client/app/superadmin/_childcareCrmDefinitionEditor.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'childcareCrmDefinitionEditor', params);
  },
});

FlowRouter.route('/superadmin/dreambox', {
  name: 'dreambox',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([Meteor.subscribe('allOrgs'), import('./client/app/superadmin/dreambox.js')]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'dreambox', params);
  },
});

FlowRouter.route('/superadmin/benchmark-stats', {
  name: 'fixWithdrawn',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs'),
      import('./client/app/superadmin/benchmarkStats.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'benchmarkStats', params);
  },
});

FlowRouter.route('/superadmin/test-zkteco-retries', {
  name: 'testZkTecoRetries',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs'),
      import('./client/app/superadmin/testZkTecoRetries.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'testZkTecoRetries', params);
  },
});


FlowRouter.route('/superadmin/fix-withdrawn-statuses', {
  name: 'fixWithdrawn',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs'),
      import('./client/app/superadmin/fixWithdrawn.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'fixWithdrawn', params);
  },
});

FlowRouter.route('/superadmin/fix-withdrawn-statuses/:id', {
  name: 'fixWithdrawnDetails',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs'),
      import('./client/app/superadmin/fixWithdrawnDetails.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'fixWithdrawnDetails', params);
  },
});

FlowRouter.route('/superadmin/fixes', {
  name: 'superAdminFixes',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/fixes.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'fixes', params);
  },
});

FlowRouter.route('/superadmin/fixes/adyen-reports', {
  name: 'superAdminFixAdyenReports',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/fixAdyenReports.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'fixAdyenReports', params);
  },
});

FlowRouter.route('/superadmin/fixes/parse-adyen', {
  name: 'superAdminParseAdyen',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/backparseAdyen.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'backparseAdyen', params);
  },
});

FlowRouter.route('/superadmin/fixes/chunk-adyen', {
  name: 'superAdminChunkAdyen',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/testAdyenChunk.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'testAdyenChunk', params);
  },
});

FlowRouter.route('/superadmin/fixes/generate-chargeback-invoices', {
  name: 'superAdminFixGenerateChargebackInvoices',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/generateChargebackInvoices.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type !== 'admin' || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'generateChargebackInvoices', params);
  },
});

FlowRouter.route('/superadmin/fixes/chargeback-invoices-transfer-funds', {
  name: 'superAdminFixChargebackInvoicesTransferFunds',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/chargebackInvoicesTransferFunds.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type !== 'admin' || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'chargebackInvoicesTransferFunds', params);
  },
});
FlowRouter.route('/superadmin/fixes/run-invoices-now', {
  name: 'superAdminFixRunInvoicesNow',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/runInvoicesNow.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type !== 'admin' || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'runInvoicesNow', params);
  },
});

FlowRouter.route('/superadmin/fixes/run-group-sync-now', {
  name: 'superAdminFixRunDefaultGroupSyncNow',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/runGroupSyncNow.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type !== 'admin' || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'runGroupSyncNow', params);
  },
});

FlowRouter.route('/superadmin/fixes/run-deferred-emails-now', {
  name: 'superAdminFixRunDeferredEmailsJobNow',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/fixes/runDeferredEmailsNow.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type !== 'admin' || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'runDeferredEmailsNow', params);
  },
});

FlowRouter.route('/superadmin/reports', {
  name: 'superAdminReports',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/reports/superAdminReports.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type !== "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'superAdminReports', params);
  },
});

FlowRouter.route('/superadmin/reports/failed-auto-pay', {
  name: 'superAdminReportsFailedAutoPay',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/reports/superAdminReportsFailedAutoPay.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type !== "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'superAdminReportsFailedAutoPay', params);
  },
});

FlowRouter.route('/superadmin/reports/failed-invoicing', {
  name: 'superAdminReportsFailedInvoicing',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) {
      return new Promise((resolve) => resolve());
    }
    return globalLoggedInSubs().concat(import('./client/app/superadmin/reports/superAdminReportsFailedInvoicing.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'superAdminReportsFailedInvoicing', params);
  },
});

FlowRouter.route('/superadmin/monitoring', {
  name: 'performanceMonitoring',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([])
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || (person.type != "admin" && !person.superAdmin)) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'performanceMonitoring', params);
  },
});

FlowRouter.route('/superadmin/customers', {
  name: 'customers',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/superadmin/customers.js'))
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'customers', params);
  },
});

FlowRouter.route('/superadmin/customers/:id', {
  name: 'customerDetail',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs', { orgId: params.id }),
      import('./client/app/superadmin/customerDetail.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'customerDetail', params);
  },
});

FlowRouter.route('/superadmin/customers/:id/reports/qb-audit-log', {
  name: 'superadminQbAuditLog',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs'),
      import('./client/app/billing/reports/billingReportQbAuditLog.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'billingReportQbAuditLog', params);
  },
});

//
// FlowRouter.route('/superadmin/customers/:id/careplan', {
//   name: 'customerCareplanDefinition',
//   layoutTemplate:"application",
//   template:"carePlanDefinitionEditor",
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat([Meteor.subscribe("allOrgs")]));
//   },
//   onBeforeAction: function() {
//     if (Meteor.user() && Meteor.user().fetchPerson() && Meteor.user().fetchPerson().type=='admin' && Meteor.user().fetchPerson().superAdmin)
//       this.next();
//     else
//       this.render("403");
//   },
//   loadingTemplate: "loading"
// });
//
FlowRouter.route('/superadmin/customers/:id/profile', {
  name: 'customerProfileDefinition',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs', { orgId: params.id }),
      subsCache.subscribe('thePeopleStaffAndAdminForOrg'),
      import('./client/app/superadmin/_profileDefinitionEditor.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'profileDefinitionEditor', params);
  },
});

FlowRouter.route('/superadmin/customers/:id/import', {
  name: 'customerImport',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs', { orgId: params.id }),
      import('./client/app/admin/importer/importer.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'importer', params);
  },
});

FlowRouter.route('/superadmin/account-debug', {
  name: 'userAccountDebug',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/superadmin/userAccountDebug.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'userAccountDebug', params);
  },
});

FlowRouter.route('/superadmin/campaigns/:id?/:action?', {
  name: 'campaigns',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      Meteor.subscribe('allOrgs'),
      import('./client/app/superadmin/campaigns/campaigns.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'campaigns', params);
  },
});

FlowRouter.route('/superadmin', {
  name: 'superAdminIndex',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/superadmin/superAdminIndex.js'));
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'superAdminIndex', params);
  },
});

FlowRouter.route('/superadmin/roles/:roleId?', {
  name: 'permissionsIndex',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat([
      import('./client/app/superadmin/permissionsIndex.js'),
      import('./client/app/superadmin/permission.js')
    ]);
  },
  triggersEnter: [(context, redirect) => {
    const user = Meteor.user();
    const person = user && user.fetchPerson();
    if (!person || person.type != "admin" || !person.superAdmin) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    const destination = params?.roleId ? 'permission' : 'permissionsIndex';
    this.render('application', destination, params);
  },
});

FlowRouter.route('/admin/org', {
  name: 'org',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/admin/org.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (Meteor.user().fetchPerson()?.type != 'admin') {
      redirect("/not_authorized")
    }

    if (!processPermissions({
      assertions: [{ context: "admin/configuration", action: "read" }],
      evaluator: (thisPerson) => thisPerson.type == "admin"
    })) {
      redirect("/not_authorized")
    }

  }],
  action(params) {
    this.render('application', 'org', params);
  },
});

FlowRouter.route('/admin/quick-entry', {
  name: 'quickEntry',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();
    const requiredSubscriptions = ["userData", "theOrg", "theGroups", "theMomentDefinitions","theLoggedinPeople"];
    return allRequiredSubscription(requiredSubscriptions).concat(import('./client/app/admin/data-entry/quickEntry.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (Meteor.user().fetchPerson().type != 'admin') {
      redirect("/not_authorized")
    }

    if (!processPermissions({
      assertions: [{ context: "admin/configuration", action: "read" }],
      evaluator: (thisPerson) => thisPerson.type == "admin"
    })) {
      redirect("/not_authorized")
    }
  }],
  action(params) {
    this.render('application', 'quickEntry', params);
  },
});


FlowRouter.route('/admin/payment-type-import', {
  name: 'quickEntry',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/admin/data-entry/paymentTypeImporter.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (Meteor.user().fetchPerson().type != 'admin') {
      redirect("/not_authorized")
    }

    if (!processPermissions({
      assertions: [{ context: "admin/configuration", action: "read" }],
      evaluator: (thisPerson) => thisPerson.type == "admin"
    })) {
      redirect("/not_authorized")
    }
  }],
  action(params) {
    this.render('application', 'paymentTypeImporter', params);
  },
});


FlowRouter.route('/admin/integrations/:integration?/:component?', {
  name: 'org',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/admin/integrations/integrations.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (Meteor.user().fetchPerson().type != 'admin') {
      redirect("/not_authorized")
    }

    if (!processPermissions({
      assertions: [{ context: "admin/configuration", action: "read" }],
      evaluator: (thisPerson) => thisPerson.type == "admin"
    })) {
      redirect("/not_authorized")
    }
  }],
  action(params) {
    this.render('application', 'integrations', params);
  },
});

FlowRouter.route('/documents', {
  name: 'documents',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/app/admin/documents.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (Meteor.user().fetchPerson().type != 'admin') {
      redirect("/not_authorized")
    }
  }],
  action(params) {
    this.render('application', 'documents', params);
  },
});

FlowRouter.route('/messages/:id?', {
  name: 'messages',
  waitOn(params,qs) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    MethodDataManager.fetchRelationships();

    let requiredSubs =[];
    requiredSubs = ["userData", "theOrg", "theGroups", "theMomentDefinitions","theLoggedinPeople"];
    const templates = params.id
      ? [import('./client/app/messages/messages.js'), import('./client/app/messages/message.js')]
      : import('./client/app/messages/messages.js');

    return allRequiredSubscription(requiredSubs).concat(templates);
  },
  action(params) {
    this.render('application', 'messages', params);
  },
  triggersEnter: [(context, redirect) => {
    const org = Orgs.current();
    if (Meteor.user().fetchPerson().type === 'staff' && org.hasCustomization('messages/disableStaffMessages/enabled')) {
      redirect('/my-site');
    }
  }]
});
//
// FlowRouter.route("/messages/:id", {
//   name: 'message.show',
//   prettyName: 'Messages',
//   layoutTemplate: "application",
//   template: "message",
//   goBackable: true,
//   goBackRoute: "/messages",
//   waitOn: function() {
//     return waitOnSubs.call(this, globalLoggedInSubs().concat([
//       Meteor.subscribe("thePeopleDirectory"),
//       Meteor.subscribe("theMessages", {messageId: this.params.id})
//     ]));
//   },
//   data: function() {
//     if (this.ready()) {
//       var message = Messages.findOne({orgId: Meteor.user() && Meteor.user().orgId, _id:this.params.id});
//       if (!message)
//         this.render("notFound");
//       else
//         return message;
//     }
//   }
// });
//
FlowRouter.route('/time', {
  name: 'time',
  whileWaiting() {
    this.render('application', 'loading', {});
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    const requiredSubscriptions = ["userData", "theOrg", "theGroups", "theMomentDefinitions","theLoggedinPeople"];
    return allRequiredSubscription(requiredSubscriptions).concat(import('./client/app/timeCards/timeCards.js'));
  },
  triggersEnter: [(context, redirect) => {
    if (_.indexOf(["admin"], Meteor.user().fetchPerson().type) == -1) {
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('application', 'timeCards', params);
  },
});
//
// FlowRouter.route('/getting-started/:stage?', {
//   name: 'onboarding',
//   prettyName: 'Get Started',
//   layoutTemplate: "application",
//   template: "getStarted",
//   onBeforeAction: function() {
//     this.next();
//   },
//   waitOn: function() {
//     return Meteor.user() ? waitOnSubs.call(this, globalLoggedInSubs().concat([ ])) : null;
//   },
//
// });
// FlowRouter.route('/get-started', function () {
//   this.redirect('/getting-started');
// });
//
//
// //offline stub
// FlowRouter.route('/offline', {
//
// });
//
//
//
//

FlowRouter.route("/helpRedirect", {
  name: 'helpRedirect',
  whileWaiting() {
    this.render('loading');
  },
  waitOn(params) {
    if (!Meteor.user() && !Meteor.loggingIn()) return new Promise((resolve) => resolve());
    return globalLoggedInSubs().concat(import('./client/layout/helpRedirect.js'));
  },
  action(params) {
    this.render('helpRedirect', params);
  },
});

// //non-loggedin functions
// Theres a very weird race condition with the Accounts.onLoginFailure callback
// that fubars the context when a user logs-out and then tries this function from a resume token
// on mobile. Storing the destingation request on the conf object to redirect appropriately since
// the Meteor.loginWithToken function succeeds
FlowRouter.route('/loginWithRedirect', {
  name: 'redirector',
  conf: {
    loggedIn: false,
    dest: null
  },
  whileWaiting() {
    this.render('loading');
  },
  waitOn(params, qs) {
    const token = qs.t;
    const dest = qs.dest;
    var theThis = this;
    if (token) {
      return new Promise((resolve, reject) => {
        Meteor.loginWithToken(token, (err, res) => {
          if (err) {
            console.log("Login error:", err.reason);
          } else {
            theThis.conf.loggedIn = true;
            theThis.conf.dest = dest;
          }
          resolve();
        });
      });
    } else {
      return new Promise((resolve) => resolve());
    }

  },
  data() {
    return { loggedIn: this.conf.loggedIn, dest: this.conf.dest };
  },
  triggersEnter: [(context, redirect, stop, data) => {
    if (data.loggedIn) {
      const qString = new URLSearchParams(context.queryParams).toString()
      let dest = data.dest
      if(qString){
        dest = data.dest+'?'+qString
      }
      redirect(dest);
    } else {
      if (window.ReactNativeWebView) window.ReactNativeWebView.postMessage("close");
      redirect("/not_authorized");
    }
  }],
  action(params) {
    this.render('loading', params);
  }
});

FlowRouter.route('/external/like/:momentId/:personId/:recipientId', {
  name: 'mediaLike',
  whileWaiting() {
    this.render('loading');
  },
  waitOn(params) {
    return [
      new Promise((resolve, reject) => {
        Meteor.callAsync('trackMediaLike', {
          momentId: params.momentId,
          personId: params.personId,
          recipientId: params.recipientId
        }).finally(() => {
          resolve();
        });
      }),
      import('./client/app/external/mediaLike.html')
    ];
  },
  action(params) {
    this.render('mediaLike');
  },
});
//
// FlowRouter.route("/verify-email/:token", {
//   name: 'verify-email',
//   action(params) {
//     Accounts.verifyEmail(this.params.token, (error) => {
//       if (error) {
//         mpSwal.fire(error.reason);
//       } else {
//         FlowRouter.go("/");
//         mpSwal.fire("Email verified! Thank you.");
//       }
//     })
//   }
// });
FlowRouter.route('/inquiries/register', {
  name: 'inquiryRegistration',
  waitOn(params, queryParams) {
    const orgId = queryParams["orgId"];
    const templateName = registrationTemplateForOrgId(orgId) || "registration";

    const templates = [import('./client/layout/emptyLayout.html')]
    switch (templateName) {
      case "registration":
        templates.push(import('./client/app/inquiries/registration.js'))
        break;
      case "registrationBrightside":
        templates.push(import('./client/app/inquiries/registrationBrightside.js'))
        break;
      case "registrationLightbridge":
        templates.push(import('./client/app/inquiries/registrationLightbridge.js'))
        break;
    }

    return templates;
  },
  action(params, queryParams) {
    var orgId = queryParams["orgId"];
    templateName = registrationTemplateForOrgId(orgId) || "registration";
    this.render('emptyLayout', templateName, params);
  }
});

FlowRouter.route('/registration', {
  name: 'registrationFlow',
  waitOn(params, queryParams) {
    if (queryParams['orgId']) {
      return import('./client/app/registrationFlow/registrationFlow.js');
    }
  },
  action(params, queryParams) {
    if (queryParams['orgId']) {
      this.render('registrationFlow');
    } else {
      // Render "Not Found" as text:
      this.render("notFound");
    }
  }
});

FlowRouter.route('/registration-completed', {
  name: 'registrationFlowCompleted',
  waitOn() {
    return import('./client/app/registrationFlow/registrationFlowCompleted.js')
  },
  action(params) {
    this.render('registrationFlowCompleted');
  }
});

FlowRouter.route('/add-relationship', {
  name: 'addRelationship',
  waitOn() {
    return import('./client/app/people/_addRelationship.js');
  },
  action(params) {
    this.render('application', '_addRelationship');
  }
});

//
// var tokenString = function() {
//   var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
//   var string_length = 20;
//   var randomstring = '';
//   for (var i=0; i<string_length; i++) {
//     var rnum = Math.floor(Math.random() * chars.length);
//     randomstring += chars.substring(rnum,rnum+1);
//   }
//   return randomstring;
// };
//
// function getQueryVariable(variable) {
//   var query = window.location.search.substring(1);
//   var vars = query.split('&');
//   for (var i = 0; i < vars.length; i++) {
//       var pair = vars[i].split('=');
//       if (decodeURIComponent(pair[0]) == variable) {
//           return decodeURIComponent(pair[1]);
//       }
//   }
// }
//
function registrationTemplateForOrgId(orgId) {
  if (_.contains(["zDY3f9RD9LhHdyNL6", "nTvbx24M2dbM9w6tu", "MAv35gErDNsai8ZHM"], orgId)) return "registrationLightbridge";
  if (orgId == "rFYExP5WGX6nTNrH8") return "registrationBrightside";
  else return "registration";
}

    /**
     * Gives a list of all the subscribed subscriptions
     *
     * @param requiredSubscriptions
     * @returns {Array} - Array of subscribed subscriptions.
     */
    function allRequiredSubscription(requiredSubscriptions) {
      const subs = Meteor.connection._subscriptions; //all the subscriptions that have been subscribed.
      const allSubscriptions = [];
      const existingSubscriptions = [];
      for(sub of Object.values(subs)) {
        allSubscriptions.push(sub.name);
      }
      for(reqSub of requiredSubscriptions) {
          if(!allSubscriptions.includes(reqSub)) {
              existingSubscriptions.push(subsCache.subscribe(reqSub));
          }
      }
      return existingSubscriptions;
  }
// FlowRouter.route('/users/:_id', {
//   name: 'user.show',
//   template: 'user',
//   yieldRegions: {
//     'userHeader': {to: 'header'}
//   },
//   subscriptions: function() {
//     this.subscribe("theUsers");
//     this.subscribe("theMoments");
//   },
//   data: function() {
//     var user = Meteor.users.findOne({_id:this.params._id});
//     if (!user)
//       this.render("notFound");
//     else
//       return users;
//   }
// });
//
// FlowRouter.route("/roles", {
//   name: 'roles',
//   template: 'roles',
//   yieldRegions: {
//     'rolesHeader': {to: 'header'}
//   },
//   subscriptions: function() {
//     this.subscribe("theRoles");
//   }
// });
// */
