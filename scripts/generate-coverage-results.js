const fs = require('fs');
const path = require('path');
const { createObjectCsvWriter } = require('csv-writer');


const coverageSummaryPath = path.resolve('coverage', 'coverage-summary.json');
const testResultsPath = path.resolve('test-results.json');

const outputCsvPath = path.resolve('coverage-results.csv');

async function generateCoverageResults() {
  try {
    const coverageSummary = JSON.parse(fs.readFileSync(coverageSummaryPath, 'utf8')).total;

    const testResults = JSON.parse(fs.readFileSync(testResultsPath, 'utf8'));

    const data = {
      app: 'momentpathmobile',
      executionDate: new Date().toISOString(),
      statements: coverageSummary.statements.pct,
      branches: coverageSummary.branches.pct,
      functions: coverageSummary.functions.pct,
      lines: coverageSummary.lines.pct,
      suites: testResults.numTotalTestSuites,
      totalNumberTest: testResults.numTotalTests,
      time: (Date.now() - testResults.startTime) / 1000
    };

    const csvWriter = createObjectCsvWriter({
      path: outputCsvPath,
      header: [
        { id: 'app', title: 'App Name' },
        { id: 'executionDate', title: 'Execution Date' },
        { id: 'statements', title: 'Statements Coverage (%)' },
        { id: 'branches', title: 'Branches Coverage (%)' },
        { id: 'functions', title: 'Functions Coverage (%)' },
        { id: 'lines', title: 'Lines Coverage (%)' },
        { id: 'suites', title: 'Suites' },
        { id: 'totalNumberTest', title: 'Total Number of Tests' },
        { id: 'time', title: 'Execution Time (s)' }
      ]
    });

    await csvWriter.writeRecords([data]);
    console.log('Coverage results written to:', outputCsvPath);
  } catch (err) {
    console.error('Error generating coverage results:', err);
  }
}

generateCoverageResults();
