require('dotenv').config();
const {MongoClient} = require('mongodb');
const moment = require('moment');
const {v4: uuidv4} = require('uuid');

const MONGODB_URI = process.env.MONGODB_URI;
const NUM_INVOICES = 8;

async function createInvoices(childId) {
  const client = await MongoClient.connect(MONGODB_URI);
  const db = client.db();

  const invoices = Array.from({length: NUM_INVOICES}, (_, i) => {
    const invoiceId = uuidv4();
    const formattedInvoiceId = invoiceId.substring(0, 8);
    const randomNum = Math.random() * 9000;
    const formattedRandomNum = Math.floor(randomNum);

    return {
      _id: formattedInvoiceId,
      invoiceDate: moment().format('M/D/YYYY'),
      createdAt: moment().valueOf(),
      createdBy: 'TEST_SETUP',
      lineItems: [
        {
          _id: `testItem${i}`,
          originalItem: {
            _id: `origItem${i}`,
            description: `Test Tuition ${i + 1}`,
            amount: 50,
            type: 'item',
            ledgerAccountName: '9809',
            suspendUntil: null,
            scaledAmounts: [],
            program: '',
          },
          createdAt: moment().valueOf(),
          price: 50,
          quantity: '1',
          notes: '',
          type: 'item',
          amount: 50,
          uid: `uid${i}`,
        },
      ],
      personId: childId,
      orgId: '7StYMhaeJTMHo6cqa',
      type: 'planInvoice',
      invoiceNumber: formattedRandomNum,
      openAmount: 50,
      originalAmount: 50,
      discountAmount: 0,
      dueDate: moment().valueOf(),
      dueDateString: moment().format('MM/DD/YYYY'),
      batchStamp: moment().valueOf(),
      openPayerAmounts: {},
      familySplits: null,
    };
  });

  await db
    .collection('invoices')
    .insertMany(invoices)
    .then(() => console.log('Invoices created'))
    .catch(console.error);
  console.log(`Created ${NUM_INVOICES} test invoices`);

  await client.close();
}

if (require.main === module) {
  createInvoices()
    .then(() => console.log('Done!'))
    .catch(console.error);
}

module.exports = {createInvoices};
