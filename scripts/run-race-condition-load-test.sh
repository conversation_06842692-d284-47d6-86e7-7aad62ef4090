#!/bin/bash

# Race Condition Load Test Pipeline Script
# This script runs comprehensive load tests for the race condition handler
# in a Bitbucket pipeline environment

set -e  # Exit on any error

echo "🚀 Starting Race Condition Load Test Pipeline..."
echo "Timestamp: $(date)"
echo "Environment: ${BITBUCKET_DEPLOYMENT_ENVIRONMENT:-pipeline}"

# Function to check if Meteor is running
check_meteor_status() {
    if pgrep -f "meteor" > /dev/null; then
        echo "✅ Meteor is running"
        return 0
    else
        echo "❌ Meteor is not running"
        return 1
    fi
}

# Function to wait for Meteor to be ready
wait_for_meteor() {
    echo "⏳ Waiting for Meteor to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            echo "✅ Meteor is ready (attempt $attempt)"
            return 0
        fi
        echo "⏳ Waiting for Meteor... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ Meteor failed to start within expected time"
    return 1
}

# Function to run the load test via Meteor shell
run_load_test() {
    echo "🧪 Running Race Condition Load Tests..."

    # Create a temporary script file for Meteor shell
    echo "📝 Creating Meteor shell command script..."
    cat > /tmp/load_test_commands.js << 'EOF'
console.log('🔧 Meteor shell connected, starting load test script...');

// Load the test script
console.log('📂 Loading test script: scripts/testRaceConditionLoadTest.js');
try {
    load('scripts/testRaceConditionLoadTest.js');
    console.log('✅ Test script loaded successfully');
} catch (error) {
    console.error('❌ Failed to load test script:', error.message);
    process.exit(1);
}

console.log('🚀 Starting complete load test suite...');

// Run the complete test suite
runLoadTestSuite().then(results => {
    console.log('\n📊 PIPELINE TEST RESULTS:');
    console.log('='.repeat(50));
    console.log(`Total Tests: ${results.summary.totalTests}`);
    console.log(`Passed: ${results.summary.passedTests}`);
    console.log(`Failed: ${results.summary.failedTests}`);
    console.log(`Execution Time: ${(results.summary.totalExecutionTime / 1000).toFixed(2)}s`);

    // Exit with appropriate code
    if (results.summary.failedTests === 0) {
        console.log('\n🎉 All load tests PASSED!');
        process.exit(0);
    } else {
        console.log('\n❌ Some load tests FAILED!');
        process.exit(1);
    }
}).catch(error => {
    console.error('\n💥 Load test suite failed:', error.message);
    console.error(error.stack);
    process.exit(1);
});
EOF

    echo "📄 Created command script at /tmp/load_test_commands.js"
    echo "📋 Script contents:"
    echo "----------------------------------------"
    head -10 /tmp/load_test_commands.js
    echo "... (truncated)"
    echo "----------------------------------------"

    # Run the test via Meteor shell with verbose output
    echo "📝 Executing load test commands via Meteor shell..."
    echo "⏱️  Timeout set to 300 seconds (5 minutes)"
    echo "🔄 Starting Meteor shell execution..."

    # Use a different approach - execute commands one by one
    echo "📡 Connecting to Meteor shell..."
    echo "🔌 Executing commands via meteor shell..."

    # Try a simpler approach using echo and pipe
    timeout 300 bash -c '
        {
            echo "console.log(\"🔧 Meteor shell connected, starting load test script...\");"
            echo "console.log(\"📂 Loading test script: scripts/testRaceConditionLoadTest.js\");"
            echo "try {"
            echo "  load(\"scripts/testRaceConditionLoadTest.js\");"
            echo "  console.log(\"✅ Test script loaded successfully\");"
            echo "} catch (error) {"
            echo "  console.error(\"❌ Failed to load test script:\", error.message);"
            echo "  process.exit(1);"
            echo "}"
            echo "console.log(\"🚀 Starting complete load test suite...\");"
            echo "runLoadTestSuite().then(results => {"
            echo "  console.log(\"\\n📊 PIPELINE TEST RESULTS:\");"
            echo "  console.log(\"=\" + \"=\".repeat(49));"
            echo "  console.log(\`Total Tests: \${results.summary.totalTests}\`);"
            echo "  console.log(\`Passed: \${results.summary.passedTests}\`);"
            echo "  console.log(\`Failed: \${results.summary.failedTests}\`);"
            echo "  console.log(\`Execution Time: \${(results.summary.totalExecutionTime / 1000).toFixed(2)}s\`);"
            echo "  if (results.summary.failedTests === 0) {"
            echo "    console.log(\"\\n🎉 All load tests PASSED!\");"
            echo "    process.exit(0);"
            echo "  } else {"
            echo "    console.log(\"\\n❌ Some load tests FAILED!\");"
            echo "    process.exit(1);"
            echo "  }"
            echo "}).catch(error => {"
            echo "  console.error(\"\\n💥 Load test suite failed:\", error.message);"
            echo "  console.error(error.stack);"
            echo "  process.exit(1);"
            echo "});"
        } | meteor shell 2>&1 | while IFS= read -r line; do
            echo "[METEOR] $line"
        done
    '
    local exit_code=$?

    echo "📊 Meteor shell exit code: $exit_code"

    # Check if timeout occurred
    if [ $exit_code -eq 124 ]; then
        echo "⏰ Load test timed out after 300 seconds"
        return 1
    fi

    # Clean up temporary file
    echo "🧹 Cleaning up temporary files..."
    rm -f /tmp/load_test_commands.js
    echo "✅ Cleanup completed"

    return $exit_code
}

# Function to generate test report
generate_report() {
    local exit_code=$1
    echo ""
    echo "📋 RACE CONDITION LOAD TEST REPORT"
    echo "=================================="
    echo "Timestamp: $(date)"
    echo "Pipeline: ${BITBUCKET_BUILD_NUMBER:-local}"
    echo "Branch: ${BITBUCKET_BRANCH:-unknown}"
    echo "Commit: ${BITBUCKET_COMMIT:-unknown}"
    
    if [ $exit_code -eq 0 ]; then
        echo "Status: ✅ PASSED"
        echo "Result: All race condition load tests completed successfully"
    else
        echo "Status: ❌ FAILED"
        echo "Result: One or more load tests failed"
    fi
    
    echo ""
    echo "Test Coverage:"
    echo "- ✅ Performance Benchmark (100+ moments)"
    echo "- ✅ EN-453 Ticket Scenario Validation"
    echo "- ✅ Large Scale Load Test (20 users, 25 actions each)"
    echo "- ✅ Race Condition Detection & Resolution"
    echo "- ✅ Kiosk Prioritization Logic"
    echo ""
}

# Main execution
main() {
    echo "🔧 Race Condition Load Test Pipeline Starting..."
    echo "📅 Start time: $(date)"
    echo "📂 Working directory: $(pwd)"
    echo "👤 User: $(whoami)"
    echo "🖥️  System: $(uname -a)"

    # Check if we're in the right directory
    echo "🔍 Checking for required files..."
    if [ ! -f "scripts/testRaceConditionLoadTest.js" ]; then
        echo "❌ Error: Load test script not found. Are we in the right directory?"
        echo "Current directory: $(pwd)"
        echo "Contents: $(ls -la scripts/ 2>/dev/null || echo 'scripts directory not found')"
        exit 1
    fi
    echo "✅ Load test script found: scripts/testRaceConditionLoadTest.js"

    # Check for other required files
    if [ ! -f "meteor.development.local.settings" ]; then
        echo "⚠️  Warning: meteor.development.local.settings not found"
        echo "📋 Available settings files:"
        ls -la *.settings* 2>/dev/null || echo "No settings files found"
    else
        echo "✅ Settings file found: meteor.development.local.settings"
    fi

    # Start Meteor in the background if not already running
    echo "🔍 Checking Meteor status..."
    if ! check_meteor_status; then
        echo "🚀 Starting Meteor..."
        echo "📋 Meteor command: METEOR_ALLOW_SUPERUSER=true meteor --settings meteor.development.local.settings"

        # Start Meteor with output redirection for debugging
        METEOR_ALLOW_SUPERUSER=true meteor --settings meteor.development.local.settings > /tmp/meteor.log 2>&1 &
        METEOR_PID=$!
        echo "✅ Meteor started with PID: $METEOR_PID"
        echo "📄 Meteor logs will be written to: /tmp/meteor.log"

        # Show initial Meteor output
        echo "📋 Initial Meteor output:"
        sleep 3
        tail -20 /tmp/meteor.log 2>/dev/null || echo "No log output yet"

        # Wait for Meteor to be ready
        echo "⏳ Waiting for Meteor to be ready..."
        if ! wait_for_meteor; then
            echo "❌ Failed to start Meteor"
            echo "📄 Last 50 lines of Meteor log:"
            tail -50 /tmp/meteor.log 2>/dev/null || echo "No log file found"
            exit 1
        fi
        echo "✅ Meteor is ready and responding"
    else
        echo "✅ Meteor is already running"
    fi

    # Show system status before running tests
    echo "📊 System status before tests:"
    echo "  Memory: $(free -h 2>/dev/null | grep Mem || echo 'Memory info not available')"
    echo "  Disk: $(df -h . 2>/dev/null | tail -1 || echo 'Disk info not available')"
    echo "  Processes: $(ps aux | grep meteor | grep -v grep | wc -l) meteor processes running"

    # Run the load tests
    echo "🧪 Executing race condition load tests..."
    echo "⏱️  Test start time: $(date)"

    if run_load_test; then
        echo "✅ Load tests completed successfully"
        echo "⏱️  Test end time: $(date)"
        generate_report 0
        exit 0
    else
        echo "❌ Load tests failed"
        echo "⏱️  Test end time: $(date)"
        echo "📄 Last 30 lines of Meteor log:"
        tail -30 /tmp/meteor.log 2>/dev/null || echo "No log file found"
        generate_report 1
        exit 1
    fi
}

# Handle script interruption
trap 'echo "🛑 Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"
