import SetupTests from "../setupTests";
import { resetDatabase } from 'meteor/xolvio:cleaner';
import { MelUtils } from "../../server/melUtils";
import '../..//lib/collections/childcareCrmAccounts.js';
import expect from 'expect';
import { ChildcareCRM } from "../../server/childcareCrmService";
import sinon from 'sinon';
import { ChildcareCrmFamilyService } from "../../server/childcareCrmFamilyService";

describe('MelUtils', function() {
    const setupTests = new SetupTests()

    const tokenStub = sinon.stub(ChildcareCRM, 'login').resolves('tok');
    const manageIdStub = sinon.stub(ChildcareCrmFamilyService, 'getManageIntegrationId').resolves(32);
    const nonDocsStub = sinon.stub(ChildcareCRM, 'getNonChildDocuments');
    const docsStub = sinon.stub(ChildcareCRM, 'getChildDocumentsById');
    const guardianStub = sinon.stub(ChildcareCRM, 'getGuardianById');
    const familyStub = sinon.stub(ChildcareCRM, 'getFamily');
    const updateStub = sinon.stub(ChildcareCRM, 'updateEnrollEntity');
    const schedStub = sinon.stub(ChildcareCRM, 'syncChildStatusFromSchedules');
    const crmUpdateStub = sinon.stub(ChildcareCRM, 'doFamilyUpdate');
    describe('canEditManualLink', function () {
        beforeEach(function () {
            resetDatabase();
        });
        it('returns false for vacuous conditions', async function () {
            expect(await MelUtils.canEditManualEnrollLink('p1')).toBe(false);
            await People.insertAsync({ _id: 'p1', orgId: 'o1' });
            expect(await MelUtils.canEditManualEnrollLink('p1')).toBe(false);
        });
        it('return false for no crm', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1' });
            await Orgs.insertAsync({ _id: 'o1' });
            expect(await MelUtils.canEditManualEnrollLink('p1')).toBe(false);
        });
        it('returns true for superadmins', async function () {
            People.insert({ _id: 'p1', orgId: 'o1', superAdmin: true });
            await Orgs.insertAsync({ _id: 'o1' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1' }] });
            expect(await MelUtils.canEditManualEnrollLink('p1')).toBe(true);
        });
        it('returns false for non admins', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1', type: 'staff' });
            await Orgs.insertAsync({ _id: 'o1' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1' }] });
            expect(await MelUtils.canEditManualEnrollLink('p1')).toBe(false);
        });
        it('returns false for non-corp admins', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1', type: 'admin' });
            await Orgs.insertAsync({ _id: 'o1' , parentOrgId: 'rootOrg' });
            await Orgs.insertAsync({_id: 'rootOrg' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1' }] });
            expect(await MelUtils.canEditManualEnrollLink('p1')).toBe(false);
        });
        it('returns false for non-corp admins', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1', type: 'admin', topmostOrgId: 'rootOrg' });
            await Orgs.insertAsync({ _id: 'o1' , parentOrgId: 'rootOrg' });
            await Orgs.insertAsync({_id: 'rootOrg' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1' }] });
            expect(await MelUtils.canEditManualEnrollLink('p1')).toBe(true);
        });
    });
    describe('formatManageRecord', function () {
        beforeEach(function () {
            resetDatabase();
        });
        it('returns a formatted record', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1', firstName: 'John', lastName: 'Doe',
                profileData: { phonePrimary: '************', birthday: new Date('2019-01-05 12:00:00').valueOf() },
                profileEmailAddress: '<EMAIL>'
            });
            await Orgs.insertAsync({ _id: 'o1', timezone: 'America/New_York' });
            const person = await People.findOneAsync('p1');
            expect(await MelUtils.formatManageRecord(person)).toStrictEqual({
                _id: 'p1',
                firstName: 'John',
                lastName: 'Doe',
                birthday: '01/05/2019',
                email: '<EMAIL>',
                phone: '************',
            });
        });
    });
    describe('retrieveNewEnrollData', function () {
        beforeEach(function () {
            resetDatabase();
            sinon.reset();
        });
        it('fetches and ignores a guardian record with the wrong center', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1',
                type: 'family',
                childcareCrm: { familyId: 1, guardianId: 123 }
            });
            await Orgs.insertAsync({ _id: 'o1', timezone: 'America/New_York' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1', id: 5 }] });
            const enrollData = {
                id: 123,
                first_name: 'Jane',
                last_name: 'Doe',
                email: '<EMAIL>',
                primary_phone: { number: '************' },
                family: { id: 1 }
            }
            guardianStub.resolves(enrollData);
            familyStub.resolves({ id: 1, center: { id: 4 }});
            const formatted = await MelUtils.retrieveEnrollData('p1', { guardianId: 987 });
            expect(formatted).toBeFalsy();
        });
        it('fetches and formats a guardian record with the right center', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1',
                type: 'family',
                childcareCrm: { familyId: 1, guardianId: 123 }
            });
            await Orgs.insertAsync({ _id: 'o1', timezone: 'America/New_York' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1', id: 5 }] });
            const enrollData = {
                id: 123,
                first_name: 'Jane',
                last_name: 'Doe',
                email: '<EMAIL>',
                primary_phone: { number: '************' },
                family: { id: 1 }
            }
            guardianStub.resolves(enrollData);
            familyStub.resolves({ id: 1, center: { id: 5 }});
            nonDocsStub.resolves([{ id: 1 }, { id: 2 }])
            const formatted = await MelUtils.retrieveEnrollData('p1', { guardianId: 987 });
            expect(formatted).toStrictEqual({
                id: 123,
                firstName: 'Jane',
                lastName: 'Doe',
                email: '<EMAIL>',
                birthday: 'n/a',
                phone: '************',
                familyId: 1,
                docCount: 2
            });
        });
    });
    describe('setManualLink', function () {
        beforeEach(function() {
            resetDatabase();
            sinon.reset();
        });
        it('updates an existing link to a new one', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1',
                type: 'family',
                childcareCrm: { familyId: 1, guardianId: 123 }
            });
            await Orgs.insertAsync({ _id: 'o1', timezone: 'America/New_York' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1', id: 5 }] });
            const newEnroll = {
                familyId: 2, id: 234
            }
            manageIdStub.resolves(32);
            tokenStub.resolves('tok');
            await MelUtils.setManualLink('p1', 'guardian', newEnroll);
            sinon.assert.calledTwice(updateStub);
            sinon.assert.calledOnce(crmUpdateStub);
            const calls = updateStub.getCalls().map(({args}) => args);
            expect(calls).toStrictEqual([
                ['tok', 1, 'guardians', 123, { id: 123, integrations: [ { id: null, integration_partner_id: 32 } ] }],
                ['tok', 2, 'guardians', 234, { id: 234, integrations: [ { id: 'p1', integration_partner_id: 32 } ] }],
            ]);
            const newPerson = await People.findOneAsync('p1');
            expect(newPerson.childcareCrm).toStrictEqual({ familyId: 2, guardianId: 234 });
        });
        it('can delete an existing link', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1',
                type: 'family',
                childcareCrm: { familyId: 1, guardianId: 123 }
            });
            await Orgs.insertAsync({ _id: 'o1', timezone: 'America/New_York' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1', id: 5 }] });
            const newEnroll = null;
            manageIdStub.resolves(32);
            tokenStub.resolves('tok');
            await MelUtils.setManualLink('p1', 'guardian', newEnroll);
            sinon.assert.calledOnce(updateStub);
            sinon.assert.notCalled(crmUpdateStub);
            const calls = updateStub.getCalls().map(({args}) => args);
            expect(calls).toStrictEqual([
                ['tok', 1, 'guardians', 123, { id: 123, integrations: [ { id: null, integration_partner_id: 32 } ] }],
            ]);
            const newPerson = await People.findOneAsync('p1');
            expect(newPerson.childcareCrm).toBeUndefined();
        });
        it('createe a new link', async function () {
            await People.insertAsync({ _id: 'p2', orgId: 'o1',
                type: 'person'
            });
            await Orgs.insertAsync({ _id: 'o1', timezone: 'America/New_York' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1', id: 5 }] });
            const newEnroll = {
                familyId: 3, id: 345
            }
            manageIdStub.resolves(32);
            tokenStub.resolves('tok');
            await MelUtils.setManualLink('p2', 'child', newEnroll);
            sinon.assert.calledOnce(updateStub);
            sinon.assert.calledOnce(crmUpdateStub);
            sinon.assert.calledOnce(schedStub);
            const calls = updateStub.getCalls().map(({args}) => args);
            expect(calls).toStrictEqual([
                ['tok', 3, 'children', 345, { id: 345, integrations: [ { id: 'p2', integration_partner_id: 32 } ] }],
            ]);
            const newPerson = await People.findOneAsync('p2');
            expect(newPerson.childcareCrm).toStrictEqual({ familyId: 3, childId: 345 });
        });
    });
    describe('retrieveEnrollData', function () {
        beforeEach(function () {
            resetDatabase();
            sinon.reset();
        });
        it('fetches and formats a guardian record', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1',
                type: 'family',
                childcareCrm: { familyId: 1, guardianId: 123 }
            });
            await Orgs.insertAsync({ _id: 'o1', timezone: 'America/New_York' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1', id: 5 }] });
            const enrollData = {
                id: 123,
                first_name: 'Jane',
                last_name: 'Doe',
                email: '<EMAIL>',
                primary_phone: { number: '************' },
                family: { id: 1 }
            }
            guardianStub.resolves(enrollData);
            nonDocsStub.resolves([{ id: 1 }, { id: 2 }]);
            const formatted = await MelUtils.retrieveEnrollData('p1');
            expect(formatted).toStrictEqual({
                id: 123,
                firstName: 'Jane',
                lastName: 'Doe',
                email: '<EMAIL>',
                birthday: 'n/a',
                phone: '************',
                familyId: 1,
                docCount: 2
            });
            guardianStub.restore();
        });
        it('fetches and formats a child record', async function () {
            await People.insertAsync({ _id: 'p1', orgId: 'o1',
                type: 'person',
                childcareCrm: { familyId: 1, childId: 12 }
            });
            await Orgs.insertAsync({ _id: 'o1', timezone: 'America/New_York' });
            ChildcareCrmAccounts.insert({ 'centers': [{ orgId: 'o1', id: 5 }] });
            const enrollData = {
                id: 123,
                first_name: 'Jane',
                last_name: 'Doe',
                date_of_birth: '2024-03-01',
                family: { id: 1 }
            }
            sinon.stub(ChildcareCRM, 'getChildById').resolves(enrollData);
            docsStub.resolves([{ id: 3 }]);
            const formatted = await MelUtils.retrieveEnrollData('p1');
            expect(formatted).toStrictEqual({
                id: 123,
                firstName: 'Jane',
                lastName: 'Doe',
                email: 'n/a',
                familyId: 1,
                birthday: '03/01/2024',
                phone: 'n/a',
                docCount: 1
            });
        });
    });
});