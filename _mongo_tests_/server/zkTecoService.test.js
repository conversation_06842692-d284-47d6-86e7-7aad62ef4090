import sinon from 'sinon';
import { resetDatabase } from 'meteor/xolvio:cleaner';
import '../../lib/collections/childcareCrmAccounts.js';
import '../../server/collections/ZkTecoBatchResults.js';
import '../../server/collections/ZkTecoErrors.js';
import SetupTests from '../setupTests.js';
import expect from 'expect';
import { ZkTecoService } from '../../server/zkTecoService';
import { ZkTecoApi } from '../../server/zkTecoApi';
import { AvailableCustomizations } from '../../lib/customizations';

const setupTests = new SetupTests();

describe('zkTecoService', function () {
    beforeEach(() => {
        resetDatabase();
    });

    afterEach(() => {
        sinon.restore();
    });

    describe('retryBatches', () => {
        it('properly retries batches', async function () {
            // Arrange
            await ZkTecoErrors.insertAsync({
                _id: 'e1',
                orgId: 'o1',
                params: [{ pin: 'p1' }],
                code: 503,
                tries: 0,
                retried: false,
                timestamp: new Date(),
            });
            await ZkTecoErrors.insertAsync({
                _id: 'e2',
                orgId: 'o2',
                params: [{ pin: 'p2' }],
                code: 503,
                tries: 3, // Max retries reached
                retried: false,
                timestamp: new Date(),
            });
            await ZkTecoErrors.insertAsync({
                _id: 'e3',
                orgId: 'o3',
                params: [{ pin: 'p3' }],
                code: 503,
                tries: 2,
                retried: false,
                timestamp: new Date(),
            });
            await ZkTecoErrors.insertAsync({
                _id: 'e4',
                orgId: 'o4',
                params: [{ pin: 'p4' }],
                code: 400, // Different error code
                tries: 0,
                retried: false,
                timestamp: new Date(),
            });

            const batchApiStub = sinon.stub(ZkTecoApi, 'batchPersonApi');
            batchApiStub.resolves({
                message: 'success',
                data: { data: { errorList: [] } },
            });

            // Act
            await ZkTecoService.retryBatches();

            // Assert
            sinon.assert.calledTwice(batchApiStub);
            const calls = batchApiStub.getCalls().map(({ args }) => args);

            expect(calls).toStrictEqual([
                [[{ pin: 'p1' }], 'o1', 1, true],
                [[{ pin: 'p3' }], 'o3', 3, true],
            ]);

            // Verify errors were marked as retried
            const retriedErrors = await ZkTecoErrors.find({ retried: true }).fetchAsync();
            expect(retriedErrors.length).toBe(2);
        });

        it('skips old errors', async function () {
            // Arrange
            const oldDate = new Date();
            oldDate.setMinutes(oldDate.getMinutes() - 45);

            await ZkTecoErrors.insertAsync({
                _id: 'e1',
                orgId: 'o1',
                params: [{ pin: 'p1' }],
                code: 503,
                tries: 0,
                retried: false,
                timestamp: oldDate,
            });

            const batchApiStub = sinon.stub(ZkTecoApi, 'batchPersonApi');

            // Act
            await ZkTecoService.retryBatches();

            // Assert
            sinon.assert.notCalled(batchApiStub);
        });
    });

    describe('updateOrg', () => {
        it('properly processes everybody in an org', async function () {
            // Arrange
            await Orgs.insertAsync({
                _id: 'o1',
                customizations: { 'integrations/doorlock/zkTeco': true },
                zkTecoValues: { areaId: 'a1', accLevelId: 'level1' },
            });

            await People.insertAsync({
                orgId: 'o1',
                type: 'family',
                _id: 'p1',
                firstName: 'f1',
                lastName: 'l1',
                pinCode: '123',
                doorCode: 987,
                inActive: false,
            });
            await People.insertAsync({
                orgId: 'o1',
                type: 'prospect',
                _id: 'p2',
            });
            await People.insertAsync({
                orgId: 'o1',
                type: 'staff',
                _id: 'p3',
                firstName: 'f3',
                lastName: 'l3',
                pinCode: '456',
                doorCode: 654,
                inActive: false,
            });

            const batchApiStub = sinon.stub(ZkTecoApi, 'batchPersonApi');
            batchApiStub.resolves({
                message: 'success',
                data: {
                    data: {
                        errorList: []
                    }
                },
            });

            // Act
            await ZkTecoService.updateOrg('o1', false, 1);

            // Assert
            sinon.assert.calledTwice(batchApiStub);
            const calls = batchApiStub.getCalls().map(({ args }) => args);

            const expectedRequest1 = [
                {
                    pin: 'p1',
                    accLevelIds: 'level1',
                    authAreaId: 'a1',
                    name: 'f1',
                    lastName: 'l1',
                    isDisabled: true,
                    email: '',
                    cardNo: 987,
                    personPwd: '123',
                },
            ];
            const expectedRequest2 = [
                {
                    pin: 'p3',
                    accLevelIds: 'level1',
                    authAreaId: 'a1',
                    name: 'f3',
                    lastName: 'l3',
                    isDisabled: false,
                    email: '',
                    cardNo: 654,
                    personPwd: '456',
                },
            ];

            expect(calls).toStrictEqual([
                [expectedRequest1, 'o1', 0, false],
                [expectedRequest2, 'o1', 0, false],
            ]);

            // Verify org updates
            const org = await Orgs.findOneAsync('o1');
            expect(org.zkTecoValues.lastBatchUpdate).toStrictEqual(expect.any(Date));
            expect(org.zkTecoValues.lastUpdate).toStrictEqual(expect.any(Date));

            // Verify cache updates
            const p1 = await People.findOneAsync('p1');
            expect(p1.zkTecoCache).toStrictEqual(expectedRequest1[0]);

            const p3 = await People.findOneAsync('p3');
            expect(p3.zkTecoCache).toStrictEqual(expectedRequest2[0]);
        });

        it('skips org without ZkTeco customization', async function () {
            // Arrange
            await Orgs.insertAsync({
                _id: 'o1',
                customizations: {},
            });

            const batchApiStub = sinon.stub(ZkTecoApi, 'batchPersonApi');

            // Act
            await ZkTecoService.updateOrg('o1');

            // Assert
            sinon.assert.notCalled(batchApiStub);
        });

        it('handles API errors gracefully', async function () {
            // Arrange
            await Orgs.insertAsync({
                _id: 'o1',
                customizations: { 'integrations/doorlock/zkTeco': true },
                zkTecoValues: { areaId: 'a1', accLevelId: 'level1' },
            });

            await People.insertAsync({
                orgId: 'o1',
                type: 'staff',
                _id: 'p1',
                firstName: 'f1',
                lastName: 'l1',
                pinCode: '123',
                doorCode: 987,
            });

            const batchApiStub = sinon.stub(ZkTecoApi, 'batchPersonApi');
            batchApiStub.resolves(false); // Simulate API error

            // Act
            await ZkTecoService.updateOrg('o1', false, 1);

            // Assert
            sinon.assert.called(batchApiStub);

            // Verify no cache updates on error
            const p1 = await People.findOneAsync('p1');
            expect(p1.zkTecoCache).toBeUndefined();

            // Verify org updates
            const org = await Orgs.findOneAsync('o1');
            expect(org.zkTecoValues.lastBatchUpdate).toBeUndefined();
        });

        it('processes batch error responses correctly', async function () {
            // Arrange
            await Orgs.insertAsync({
                _id: 'o1',
                customizations: { 'integrations/doorlock/zkTeco': true },
                zkTecoValues: { areaId: 'a1', accLevelId: 'level1' },
            });

            await People.insertAsync({
                orgId: 'o1',
                type: 'staff',
                _id: 'p1',
                firstName: 'f1',
                lastName: 'l1',
                pinCode: '123',
                doorCode: 987,
            });

            const batchApiStub = sinon.stub(ZkTecoApi, 'batchPersonApi');
            batchApiStub.resolves({
                message: 'partial success',
                data: {
                    data: {
                        errorList: [{ pin: 'p1', message: 'Invalid data' }]
                    }
                },
            });

            // Act
            await ZkTecoService.updateOrg('o1', false, 1);

            // Assert
            sinon.assert.called(batchApiStub);

            // Verify no cache update for error response
            const p1 = await People.findOneAsync('p1');
            expect(p1.zkTecoCache).toBeUndefined();

            // Verify batch results were stored
            const batchResults = await ZkTecoBatchResults.findOneAsync({ orgId: 'o1' });
            expect(batchResults).toBeTruthy();
            expect(batchResults.results.data.data.errorList.length).toBe(1);
        });
    });

    describe('cleanName', () => {
        it('removes special characters from names', function () {
            expect(ZkTecoService.cleanName("John O'Connor")).toBe('John OConnor');
            expect(ZkTecoService.cleanName("María-José")).toBe('MaraJos');
            expect(ZkTecoService.cleanName("Smith & Jones")).toBe('Smith  Jones');
            expect(ZkTecoService.cleanName(null)).toBe('');
        });
    });
    
});