import '../../lib/collections/childcareCrmAccounts.js';
import { resetDatabase } from 'meteor/xolvio:cleaner';
import expect from 'expect';
import { Meteor } from 'meteor/meteor';
import { UsersAndPeople } from '../../server/usersAndPeople';

describe('UsersAndPeople', function () {
    describe('insertPersonCreateUser', () => {
        beforeEach(() => {
            resetDatabase();
            Factory.Cache.clear();
        });
        it('links people when emails match case insensitive', async () => {
            const userCount0 = await Meteor.users.find().count()
            expect(userCount0).toBe(0);
            await Meteor.users.insertAsync({ emails: [{ address: '<EMAIL>' }], personId: 'p1', orgId: 'o1', membership: [
                    { orgId: 'o1', personId: 'p1' }
                ] });
            const userCount01 = await Meteor.users.find().count()
            expect(userCount01).toBe(1);
            const personData = {
                _id: 'p2',
                firstName: 'foo',
                lastName: 'bar',
                emailAddress: '<EMAIL>',
                orgId: 'o2',
                type: 'family'
            };
            const data = { personData, newPersonId: personData._id, orgId: 'o2' };
            await UsersAndPeople.insertPersonCreateUser(data);
            const users = await Meteor.users.find().fetchAsync();
            expect(users?.length).toBe(1);
            expect(users[0].membership).toHaveLength(2);
            expect(users[0].membership).toStrictEqual([{ orgId: 'o1', personId: 'p1' }, { orgId: 'o2', personId: 'p2' }]);
        });
    })
});