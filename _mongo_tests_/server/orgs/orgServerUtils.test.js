import { resetDatabase } from 'meteor/xolvio:cleaner';
import expect from 'expect';
import { PeopleTypes } from '../../../lib/constants/peopleConstants';
import { OrgServerUtils } from '../../../server/orgs/orgServerUtils';

describe('OrgServerUtils', () => {
    describe('getOrgsFromPersonOrg', () => {
        let person;
        after(async () => {
            resetDatabase();
            Factory.Cache.clear();
        });
        beforeEach(async () => {
            resetDatabase();
            Factory.Cache.clear();
        });
        it('returns empty when just 1 org in hierarchy', async () => {
            const orgId = await Orgs.insertAsync({ name: 'Org 1' });
            const org = await Orgs.findOneAsync({ _id: orgId });
            const personId = await People.insertAsync({
                orgId: orgId,
                type: PeopleTypes.STAFF,
            });
            person = await People.findOneAsync({ _id: personId });
            const results = await OrgServerUtils.getOrgsFromPersonOrg(org, person);
            expect(results).toStrictEqual([]);
        });
        it('works on child org in hierarchy', async () => {
            const org1Id = await Orgs.insertAsync({ name: 'Org 1' });
            const org2Id = await Orgs.insertAsync({ name: 'Org 2', parentOrgId: org1Id });
            const org1 = await Orgs.findOneAsync({ _id: org1Id });
            const org2 = await Orgs.findOneAsync({ _id: org2Id });
            const personId = await People.insertAsync({
                orgId: org2Id,
                type: PeopleTypes.STAFF,
            });
            person = await People.findOneAsync({ _id: personId });
            const results = await OrgServerUtils.getOrgsFromPersonOrg(org2, person);
            expect(results).toStrictEqual(
                [
                    {
                        _id: org1Id,
                        edge: expect.any(Boolean),
                        enableSwitchOrg: undefined,
                        fullName: org1.name,
                        name: org1.name,
                        parentOrgId: undefined,
                        topLevel: true
                    },
                    {
                        _id: org2Id,
                        edge: expect.any(Boolean),
                        enableSwitchOrg: undefined,
                        fullName: 'Org 1>Org 2',
                        name: org2.name,
                        parentOrgId: org1._id,
                        topLevel: false
                    }
                ]
            );
        });
        it('returns all visible even when not in immediate org in hierarchy', async () => {
            const org1Id = await Orgs.insertAsync({ name: 'Org 1' });
            const org2Id = await Orgs.insertAsync({ name: 'Org 2', parentOrgId: org1Id });
            const org3Id = await Orgs.insertAsync({ name: 'Org 3', parentOrgId: org1Id });
            const org4Id = await Orgs.insertAsync({ name: 'Org 4', parentOrgId: org2Id });
            const org1 = await Orgs.findOneAsync({ _id: org1Id });
            const org2 = await Orgs.findOneAsync({ _id: org2Id });
            const org3 = await Orgs.findOneAsync({ _id: org3Id });
            const org4 = await Orgs.findOneAsync({ _id: org4Id });
            const personId = await People.insertAsync({
                orgId: org2Id,
                type: PeopleTypes.STAFF,
            });
            person = await People.findOneAsync({ _id: personId });
            const results = await OrgServerUtils.getOrgsFromPersonOrg(org2, person);
            expect(results).toStrictEqual(
                [
                    {
                        _id: org1Id,
                        edge: expect.any(Boolean),
                        enableSwitchOrg: undefined,
                        fullName: org1.name,
                        name: org1.name,
                        parentOrgId: undefined,
                        topLevel: true
                    },
                    {
                        _id: org2Id,
                        edge: expect.any(Boolean),
                        enableSwitchOrg: undefined,
                        fullName: 'Org 1>Org 2',
                        name: org2.name,
                        parentOrgId: org1._id,
                        topLevel: false
                    },
                    {
                        _id: org3Id,
                        edge: expect.any(Boolean),
                        enableSwitchOrg: undefined,
                        fullName: 'Org 1>Org 3',
                        name: org3.name,
                        parentOrgId: org1._id,
                        topLevel: false
                    },
                    {
                        _id: org4Id,
                        edge: expect.any(Boolean),
                        enableSwitchOrg: undefined,
                        fullName: 'Org 1>Org 2>Org 4',
                        name: org4.name,
                        parentOrgId: org2._id,
                        topLevel: false
                    }
                ]
            );
        });
    });
});
