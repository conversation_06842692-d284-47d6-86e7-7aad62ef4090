// Mock dependencies for testing
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
}));

jest.mock('expo-localization', () => ({
  locale: 'en-US',
  locales: ['en-US'],
}));

import i18n from '../i18n';

describe('i18n Configuration - Basic Tests', () => {
  beforeAll(async () => {
    // Ensure i18n is initialized
    if (!i18n.isInitialized) {
      await i18n.init();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    test('should be initialized', () => {
      expect(i18n.isInitialized).toBe(true);
    });

    test('should have a language set', () => {
      expect(i18n.language).toBeDefined();
      expect(typeof i18n.language).toBe('string');
    });

    test('should be able to translate a simple key', () => {
      const result = i18n.t('authentication.signIn');
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });
  });

  describe('Language Switching', () => {
    test('should switch to Spanish', async () => {
      await i18n.changeLanguage('es');
      expect(i18n.language).toBe('es');
    });

    test('should switch back to English', async () => {
      await i18n.changeLanguage('en');
      expect(i18n.language).toBe('en');
    });
  });

  describe('Translation Quality', () => {
    test('should provide different translations for different languages', async () => {
      await i18n.changeLanguage('en');
      const englishText = i18n.t('authentication.signIn');

      await i18n.changeLanguage('es');
      const spanishText = i18n.t('authentication.signIn');

      expect(englishText).not.toBe(spanishText);
      expect(englishText).toBeDefined();
      expect(spanishText).toBeDefined();
    });
  });

  describe('MomentFields Translations', () => {
    test('should translate momentFields.labels.type in English', async () => {
      await i18n.changeLanguage('en');
      const result = i18n.t('momentFields.labels.type');
      expect(result).toBe('Type');
    });

    test('should translate momentFields.labels.type in Spanish', async () => {
      await i18n.changeLanguage('es');
      const result = i18n.t('momentFields.labels.type');
      expect(result).toBe('Tipo');
    });

    test('should translate momentFields.values.moodLevels.happy in English', async () => {
      await i18n.changeLanguage('en');
      const result = i18n.t('momentFields.values.moodLevels.happy');
      expect(result).toBe('Happy');
    });

    test('should translate momentFields.values.moodLevels.happy in Spanish', async () => {
      await i18n.changeLanguage('es');
      const result = i18n.t('momentFields.values.moodLevels.happy');
      expect(result).toBe('Feliz');
    });

    test('should translate momentFields.values.foodTypes.breakfast in English', async () => {
      await i18n.changeLanguage('en');
      const result = i18n.t('momentFields.values.foodTypes.breakfast');
      expect(result).toBe('Breakfast');
    });

    test('should translate momentFields.values.foodTypes.breakfast in Spanish', async () => {
      await i18n.changeLanguage('es');
      const result = i18n.t('momentFields.values.foodTypes.breakfast');
      expect(result).toBe('Desayuno');
    });
  });
});