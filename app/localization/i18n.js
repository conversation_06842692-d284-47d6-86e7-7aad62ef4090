import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import translation files
import enTranslation from './locales/en/translation.json';
import enBusiness from './locales/en/business.json';
import enAlerts from './locales/en/alerts.json';
import esTranslation from './locales/es/translation.json';
import esBusiness from './locales/es/business.json';
import esAlerts from './locales/es/alerts.json';

const resources = {
  en: {
    translation: enTranslation,
    business: enBusiness,
    alerts: enAlerts,
  },
  es: {
    translation: esTranslation,
    business: esBusiness,
    alerts: esAlerts,
  },
};

const initI18n = async () => {
  let savedLanguage = await AsyncStorage.getItem('userLanguage');
  
  // Check if user has a saved preference, otherwise use device locale
  if (!savedLanguage) {
    const deviceLocale = Localization.locale;
    // Support es-ES, es-MX, es-US, etc. -> 'es'
    savedLanguage = deviceLocale.startsWith('es') ? 'es' : 'en';
  }

  await i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: savedLanguage,
      fallbackLng: 'en',
      compatibilityJSON: 'v3',
      interpolation: {
        escapeValue: false, // React Native handles escaping
      },
      // Namespace support for organization
      defaultNS: 'translation',
      ns: ['translation', 'business', 'alerts'],
      returnKeyIfNotFound: true,
      debug: __DEV__, // Enable debug in development
    });

  return savedLanguage;
};

// Initialize immediately
initI18n();

export default i18n;

// Helper functions for your existing org.translate() system
export const setLanguage = async (language) => {
  await AsyncStorage.setItem('userLanguage', language);
  await i18n.changeLanguage(language);
};

export const getCurrentLanguage = () => i18n.language;

// Bridge function to work with existing org.translate() method
export const bridgeTranslate = (key, defaultValue, namespace = 'translation') => {
  // Try with namespace first, then without namespace for backward compatibility
  let translatedValue = i18n.t(`${namespace}:${key}`, { defaultValue });

  // If the key wasn't found with namespace, try without namespace
  if (translatedValue === key || translatedValue === `${namespace}:${key}`) {
    translatedValue = i18n.t(key, { defaultValue });
  }

  // If still not found, return the default value
  return translatedValue !== key ? translatedValue : defaultValue;
};