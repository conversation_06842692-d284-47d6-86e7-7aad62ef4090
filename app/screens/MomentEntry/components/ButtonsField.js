import React from 'react';
import { View, Text, ScrollView, Appearance} from 'react-native';
import { Button, Icon } from 'native-base';
import _ from 'lodash';
import colors from '../../../config/colors.json';
import { styles } from './styles';

export default (props) => {
  const { field } = props;
  const filteredFieldValues = _.get(field, 'filteredFieldValues', []);
  const requiredLabel = field.required ? (<Text style={{color: colors.darkGray}}> *required</Text>) : " ";

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {field.label}
        {requiredLabel}
      </Text>
      <ScrollView horizontal style={{flex: 1}}>
        {
          filteredFieldValues.map((fieldValueItem) => {
            const fieldValueLabel = typeof fieldValueItem === "object" ? fieldValueItem.fieldValue : fieldValueItem;
            const isBordered = props.getMomentField(field.dataId) != fieldValueLabel;
            const buttonStyle = {
              marginRight: 6,
              paddingHorizontal: 10,
              borderColor: colors.primaryA,
              backgroundColor: isBordered ? colors.white : colors.primaryA
            };

            return (
              <Button
                testID='button-field'
                bordered={isBordered}
                onPress={() => { props.setMomentField(field.dataId, fieldValueLabel) }}
                style={buttonStyle}>
                  {
                    (typeof fieldValueItem === 'object' && fieldValueItem.fieldIcon) ?
                    ( <Icon type="FontAwesome" name={fieldValueItem.fieldIcon}/> ) :
                    ( <Text style={{fontSize: 16, fontWeight: 'bold'}}>{fieldValueLabel}</Text> )
                  }
              </Button>
            )
          })
        }
      </ScrollView>
    </View>
  )
}
