import _ from 'underscore';
import { bridgeTranslate} from "../localization/i18n";

// Define translation function at module level so it's accessible everywhere
const t = (key, defaultValue) => bridgeTranslate(key, defaultValue);

export default class MomentDefinitions {
	constructor(momentType, options = {}) {

		this._momentType = momentType, this.options = options;
		const availableMomentTypes = options.org && this.constructor.availableMomentTypes( options.org);
		const forceBlockEDU = options.org && options.org.customizations && options.org.customizations["people/expressDriveUpBlock/enabled"];

		if (standardMomentTypes[momentType])
			this._momentDefinition = standardMomentTypes[momentType];
		else if ( options.org && _.find( availableMomentTypes, (def) => { return def.type == momentType; })) {
			this._momentDefinition = _.find( availableMomentTypes, (def) => { return def.type == momentType; });
		}
		else if (momentType === "informedArrival") {
			this._momentDefinition = {
				prettyName: "Informed Arrival",
				overrideTitle: t('momentEntry.overrideTitle'),
				fields: [
					{ fieldType: "text", label: t('momentFields.labels.notesForStaff', 'Notes for Staff'), dataId: "notes"},
					{ 	fieldType: "buttons", label: t('momentFields.labels.mood', 'Mood'), dataId:"mood",
						fieldValues: [
							{fieldValue: t('momentFields.values.moodLevels.happy', 'Happy'), fieldIcon: "smile-o"},
							{fieldValue: t('momentFields.values.moodLevels.soSo', 'SoSo'), fieldIcon: "meh-o"},
							{fieldValue: t('momentFields.values.moodLevels.sad', 'Sad'), fieldIcon: "frown-o"}
						]
					}
				],
				hideComment: true,
				hideDatePicker: true,
				hideTimePicker: true,
				hideMomentPicker: true,
				hidePeopleChooser: true,
				hideMediaPicker: true,
				hideStringPrompt: true
			};
			const attendingField = {
				fieldType: "buttons",
				label: t('momentFields.labels.attendingToday', 'Attending Today'),
				dataId: "attending",
				defaultFieldValue: t('momentFields.values.attendingOptions.yes', 'Yes'),
				fieldValues: [
					t('momentFields.values.attendingOptions.yes', 'Yes'),
					t('momentFields.values.attendingOptions.noSick', 'No - sick'),
					t('momentFields.values.attendingOptions.noVacation', 'No - vacation'),
					t('momentFields.values.attendingOptions.noOther', 'No - other')
				]
			};
			const expressDriveUpFields = {
				fieldType: "select",
				label: t('momentFields.labels.estimatedArrivalTime', 'Estimated Arrival Time'),
				dataId: "dropOffTimeEstimate",
				fieldValues: [
					t('momentFields.values.arrivalTimes.tenMinutes', '~10 minutes away'),
					t('momentFields.values.arrivalTimes.twentyMinutes', '~20 minutes away'),
					t('momentFields.values.arrivalTimes.thirtyMinutes', '~30 minutes away'),
					t('momentFields.values.arrivalTimes.thirtyPlusMinutes', '30+ minutes away'),
					t('momentFields.values.arrivalTimes.removeArrivalTime', 'Remove arrival time')
				]
			};
			const absentReasonFields = {
                fieldType: "text", label: t('momentFields.labels.reasonNotAttending', 'Reason not attending'), dataId: "absentComment"
            };
			const pinCodeFields = options && options.org && options.org.valueOverrides && options.org.valueOverrides.pinCodeCheckinFields;
			if (pinCodeFields) this._momentDefinition.fields = pinCodeFields;
			if (!forceBlockEDU) {
				this._momentDefinition.fields = [ attendingField, ...this._momentDefinition.fields, expressDriveUpFields,absentReasonFields];
			} else {
				this._momentDefinition.fields = [ attendingField, ...this._momentDefinition.fields,absentReasonFields];
			}
		}
	}

	getDefinition() {
		return this._momentDefinition;
	}

	static getOfflineMomentType(type) {
		return standardMomentTypes[type];
	}

	static staticDefinitions() {
		return  _.map( standardMomentTypes, (v, k) => {
			v.name = v.prettyName;
			v.type = k;
			return v;
		});
	}

	static availableMomentTypes(org) {
		const staticDefs = this.staticDefinitions(),
			findDef = (defType) => { return _.find(staticDefs, (sds) => { return sds.type==defType; }); };
		let types = [ findDef("comment") ];
		if (!org) return types;
		if (org.hasCustomization("moments/potty/enabled")) {
			const d = findDef("potty");
			d.adminOnly = org.hasCustomization("moments/potty/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/food/enabled")) {
			const d = findDef("food")
			d.adminOnly = org.hasCustomization("moments/food/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/sleep/enabled")) {
			const d = findDef("sleep");
			d.adminOnly = org.hasCustomization("moments/sleep/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/activity/enabled")) {
			const d = findDef("activity");
			d.adminOnly = org.hasCustomization("moments/activity/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/medical/enabled")) {
			const d = findDef("medical");
			d.adminOnly = org.hasCustomization("moments/medical/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/mood/enabled")) {
			const d = findDef("mood");
			d.adminOnly = org.hasCustomization("moments/mood/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/incident/enabled")) {
			const d = findDef("incident");
			d.adminOnly = org.hasCustomization("moments/incident/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/alert/enabled")) {
			const d = findDef("alert");
			d.adminOnly = org.hasCustomization("moments/alert/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/supplies/enabled")) {
			const d = findDef("supplies");
			d.adminOnly = org.hasCustomization("moments/supplies/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/learning/enabled")) {
			const d = findDef("learning");
			d.adminOnly = org.hasCustomization("moments/learning/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/illness/enabled")) {
			const d = findDef("illness");
			d.adminOnly = org.hasCustomization("moments/illness/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/ouch/enabled")) {
			const d = findDef("ouch");
			d.adminOnly = org.hasCustomization("moments/ouch/adminOnly");
			types.push(d);
		}
		if (org.hasCustomization("moments/portfolio/enabled")) {
			const d = findDef("portfolio");
			d.adminOnly = org.hasCustomization("moments/portfolio/adminOnly");
			types.push(d);
		}

		if (org && org.availableDynamicMomentTypes()) {
			org.availableDynamicMomentTypes().forEach( (def) => {
				let pushType = true;
				if (org.customizationExists(`moments/${def.momentType}/enabled`)) pushType = org.hasCustomization(`moments/${def.momentType}/enabled`);
				if (pushType) {
					const newDef = {
						prettyName: def.momentTypePretty,
						icon: {uri: def.icon ? def.icon : "mt_comment_icon_sm"},
						listIcon: {uri: def.listIcon ? def.listIcon : "mt_comment_icon_purple"},
						fields: def.momentFields,
						name: def.momentTypePretty,
						type: def.momentType,
						isDynamic: true,
						multi: def.multi,
						adminOnly: org.hasCustomization(`moments/${def.momentType}/adminOnly`),
					};
					types.push( newDef );
				}
			});
		}

		types.forEach( (def) => {
			def.name = org.translate("momentTypes." + def.type, def.name);
		});
		return types;
	}
}

const standardMomentTypes = {
	"comment": {
		prettyName: "Comment",
		icon: {uri: "mt_comment_icon_sm"},
		listIcon: {uri: "mt_comment_icon_purple"},
		fields: [

		],
		hideComment: false,
		hideTimePicker: false,
		hideDatePicker: false
	},
	"checkin": {
		prettyName: "Check In",
		icon: {uri: "mt_notification_icon_sm"},
		listIcon: {uri: "mt_notification_icon_purple"},
		disableTagging: true
	},
	"checkout": {
		prettyName: "Check Out",
		icon: {uri: "mt_notification_icon_sm"},
		listIcon: {uri: "mt_notification_icon_purple"},
		disableTagging: true
	},
	"move": {
		prettyName: "Move",
		icon: {uri: "mt_notification_icon_sm"},
		listIcon: {uri: "mt_notification_icon_purple"},
		disableTagging: true
	},
	"potty": {
		prettyName: "Potty",
		icon: {uri: "mt_potty_icon_sm"},
		listIcon: {uri: "mt_potty_icon_purple"},
		fields: [
			{
				dataId: "pottyType",
				label: t('momentFields.labels.type', 'Type'),
				fieldType: "buttons",
				fieldValues: [
					{fieldValue: t('momentFields.values.pottyTypes.wet', 'Wet')},
					{fieldValue: t('momentFields.values.pottyTypes.bm', 'BM')},
					{fieldValue: t('momentFields.values.pottyTypes.wetBm', 'Wet+BM')},
					{fieldValue: t('momentFields.values.pottyTypes.dry', 'Dry')},
				]
			},
			{
				dataId: "pottyTypeContinence",
				label: t('momentFields.labels.continence', 'Continence'),
				fieldType: "buttons",
				fieldValues: [
					{fieldValue: t('momentFields.values.continenceTypes.continent', 'Continent')},
					{fieldValue: t('momentFields.values.continenceTypes.incontinent', 'Incontinent')},
					{fieldValue: t('momentFields.values.continenceTypes.refused', 'Refused')},
				],
				customization: "moments/potty/showContinence"
			},
			{
				dataId: "pottyAppliedOintment",
				label: t('momentFields.labels.appliedOintment', 'Applied Ointment'),
				fieldType: "checkbox",
				languages: ["", "translationsEnChildCare"]
			},
			{
				dataId: "pottyTraining",
				label: t('momentFields.labels.training', 'Training'),
				fieldType: "buttons",
				fieldValues: [
					{fieldValue: t('momentFields.values.pottyTraining.tried', 'Tried')},
					{fieldValue: t('momentFields.values.pottyTraining.successful', 'Successful')},
					{fieldValue: t('momentFields.values.pottyTraining.accident', 'Accident')},
				],
				languages: ["", "translationsEnChildCare"]
			}
		],
	},
	"offlineFood": {
			prettyName: "Food",
			icon: {uri: "mt_food_icon_sm"},
			listIcon: {uri: "mt_food_icon_purple"},
			fields: [
				{
					dataId:"foodType",

					label: t('momentFields.labels.type', 'Type'),
					fieldType: "buttons",
					fieldValues: [
						{fieldValue: t('momentFields.values.foodTypes.breakfast', 'Breakfast')},
						{fieldValue: t('momentFields.values.foodTypes.amSnack', 'AM Snack')},
						{fieldValue: t('momentFields.values.foodTypes.lunch', 'Lunch')},
						{fieldValue: t('momentFields.values.foodTypes.pmSnack', 'PM Snack')},
						{fieldValue: t('momentFields.values.foodTypes.lateSnack', 'Late Snack')},
						{fieldValue: t('momentFields.values.foodTypes.dinner', 'Dinner')},
						{fieldValue: t('momentFields.values.foodTypes.bottle', 'Bottle'), displayRule:"showBottleType"}
					]
				},
				{
					dataId: "foodAmount",
					label: t('momentFields.labels.amount', 'Amount'),
					fieldType: "buttons",
					fieldValues: [
						t('momentFields.values.foodAmounts.all', 'All'),
						t('momentFields.values.foodAmounts.most', 'Most'),
						t('momentFields.values.foodAmounts.some', 'Some'),
						t('momentFields.values.foodAmounts.none', 'None'),
						t('momentFields.values.foodAmounts.notOffered', 'Not Offered')
					]
				},
				{
					dataId: "foodBottleAmountBreastmilkOffered",
					label: t('momentFields.labels.breastmilkOffered', 'Breastmilk Offered'),
					fieldType: "amount",
					amountUnits:"oz",
					displayRule: "showBottleAmount"
				},
				{
					dataId: "foodBottleAmountBreastmilkConsumed",
					label: t('momentFields.labels.breastmilkConsumed', 'Breastmilk Consumed'),
					fieldType: "amount",
					amountUnits:"oz",
					displayRule: "showBottleAmount"
				},
				{
					dataId: "foodBottleAmountFormulaOffered",
					label: t('momentFields.labels.formulaOffered', 'Formula Offered'),
					fieldType: "amount",
					amountUnits:"oz",
					displayRule: "showBottleAmount"
				},
				{
					dataId: "foodBottleAmountFormulaConsumed",
					label: t('momentFields.labels.formulaConsumed', 'Formula Consumed'),
					fieldType: "amount",
					amountUnits:"oz",
					displayRule: "showBottleAmount"
				},
				{
					dataId: "foodBottleAmountMilkOffered",
					label: t('momentFields.labels.milkOffered', 'Milk Offered'),
					fieldType: "amount",
					amountUnits:"oz",
					displayRule: "showBottleAmount"
				},
				{
					dataId: "foodBottleAmountMilkConsumed",
					label: t('momentFields.labels.milkConsumed', 'Milk Consumed'),
					fieldType: "amount",
					amountUnits:"oz",
					displayRule: "showBottleAmount"
				}
			]
		},
	"food": {
		prettyName: "Food",
		icon: {uri: "mt_food_icon_sm"},
		listIcon: {uri: "mt_food_icon_purple"},
		fields: [
			{
				dataId:"foodType",

				label: t('momentFields.labels.type', 'Type'),
				fieldType: "buttons",
				fieldValues: [
					{fieldValue: t('momentFields.values.foodTypes.breakfast', 'Breakfast')},
					{fieldValue: t('momentFields.values.foodTypes.amSnack', 'AM Snack')},
					{fieldValue: t('momentFields.values.foodTypes.lunch', 'Lunch')},
					{fieldValue: t('momentFields.values.foodTypes.pmSnack', 'PM Snack')},
					{fieldValue: t('momentFields.values.foodTypes.lateSnack', 'Late Snack')},
					{fieldValue: t('momentFields.values.foodTypes.dinner', 'Dinner')},
					{fieldValue: t('momentFields.values.foodTypes.bottle', 'Bottle'), displayRule:"showBottleType"},
					{fieldValue: t('momentFields.values.foodTypes.babyFood', 'Baby Food'), displayRule:"showBabyFoodType"},
					{fieldValue: t('momentFields.values.foodTypes.cereal', 'Cereal'), displayRule:"showCerealType"},
					{fieldValue: t('momentFields.values.foodTypes.tube', 'Tube'), customization:"moments/food/showTube"}
				]
			},
			{
				dataId: "foodItems",
				fieldType: "stateSourcedFieldList",
				stateSource: "foodMomentItems",
				fieldDefinition: {
					itemName: "name",
					itemValueName: "amount",
					fieldType: "buttons",
					fieldValues: [
						t('momentFields.values.foodAmounts.all', 'All'),
						t('momentFields.values.foodAmounts.most', 'Most'),
						t('momentFields.values.foodAmounts.some', 'Some'),
						t('momentFields.values.foodAmounts.none', 'None'),
						t('momentFields.values.foodAmounts.notOffered', 'Not Offered')
					]
				}
			},
			{
				dataId: "foodTubeAmount",
				label: t('momentFields.labels.amount', 'Amount'),
				fieldType: "select",
				rightLabel: "ml",
				fieldValues: ["0", "60", "120", "180", "240", "300", "360", "420", "480", "540"],
				displayRule: "showFoodTubeAmount"
			},
			{
				dataId: "foodBottleAmountBreastmilkOffered",
				label: t('momentFields.labels.breastmilkOffered', 'Breastmilk Offered'),
				fieldType: "amount",
				amountUnits:"oz",
				displayRule: "showBottleAmount"
			},
			{
				dataId: "foodBottleAmountBreastmilkConsumed",
				label: t('momentFields.labels.breastmilkConsumed', 'Breastmilk Consumed'),
				fieldType: "amount",
				amountUnits:"oz",
				displayRule: "showBottleAmount"
			},
			{
				dataId: "foodBottleAmountFormulaOffered",
				label: t('momentFields.labels.formulaOffered', 'Formula Offered'),
				fieldType: "amount",
				amountUnits:"oz",
				displayRule: "showBottleAmount"
			},
			{
				dataId: "foodBottleAmountFormulaConsumed",
				label: t('momentFields.labels.formulaConsumed', 'Formula Consumed'),
				fieldType: "amount",
				amountUnits:"oz",
				displayRule: "showBottleAmount"
			},
			{
				dataId: "foodBottleAmountMilkOffered",
				label: t('momentFields.labels.milkOffered', 'Milk Offered'),
				fieldType: "amount",
				amountUnits:"oz",
				displayRule: "showBottleAmount"
			},
			{
				dataId: "foodBottleAmountMilkConsumed",
				label: t('momentFields.labels.milkConsumed', 'Milk Consumed'),
				fieldType: "amount",
				amountUnits:"oz",
				displayRule: "showBottleAmount"
			},
			{
				dataId: "foodBabyFoodType",
				label: t('momentFields.labels.foodType', 'Food Type'),
				fieldType: "buttons",
				fieldValues: [
					t('momentFields.values.babyFoodTypes.fruit', 'Fruit'),
					t('momentFields.values.babyFoodTypes.vegetable', 'Vegetable')
				],
				displayRule: "showBabyFoodAmount"
			},
			{
				dataId: "foodBottleAmountBabyFoodConsumed",
				label: t('momentFields.labels.amountEaten', 'Amount Eaten'),
				fieldType: "amount",
				amountUnits:"selectedBabyFoodUnits",
				displayRule: "showBabyFoodAmount"
			},
			{
				dataId: "foodBottleAmountCerealConsumed",
				label: t('momentFields.labels.amountEaten', 'Amount Eaten'),
				fieldType: "amount",
				amountUnits: "selectedBabyCerealUnits",
				displayRule: "showCerealAmount"
			},
			{
				dataId: "foodAmountPercent",
				label: t('momentFields.labels.amount', 'Amount'),
				fieldType: "select",
				fieldValues: ["100%", "90%", "80%", "70%", "60%", "50%", "40%", "30%", "20%", "10%", "0%"],
				displayRule: "showFoodAmountPercent"
			},
			{
				dataId: "foodAmount",
				label: t('momentFields.labels.amount', 'Amount'),
				fieldType: "buttons",
				fieldValues: [
					'All',
					t('momentFields.values.foodAmounts.most', 'Most'),
					t('momentFields.values.foodAmounts.some', 'Some'),
					t('momentFields.values.foodAmounts.none', 'None'),
					t('momentFields.values.foodAmounts.notOffered', 'Not Offered')
				],
				displayRule: "showFoodAmount"
			}
		]
	},
	"sleep": {
		prettyName: "Sleep",
		icon: {uri: "mt_sleep_icon_sm"},
		listIcon: {uri: "mt_sleep_icon_purple"},
		hideTimePicker: true,
		fields: [
			{
				dataId: "time",
				label: t('momentFields.labels.startTime', 'Start Time'),
				fieldType: "timePicker",
			},
			{
				dataId: "endTime",
				label: t('momentFields.labels.endTime', 'End Time'),
				fieldType: "timePicker"
			},
			{
				dataId: "sleepDidNotSleep",
				label: t('momentFields.labels.didNotSleep', 'Did Not Sleep'),
				fieldType: "checkbox"
			},
			{
				dataId: "sleepSatQuietly",
				label: t('momentFields.labels.satQuietly', 'Sat Quietly'),
				fieldType: "checkbox"
			}
		]
	},
	"activity": {
		prettyName: "Activity",
		icon: {uri: "mt_activity_icon_sm"},
		listIcon: {uri: "mt_activity_icon_purple"},
		fields: [
			{
				dataId: "activityEngagement",
				label: t('momentFields.labels.engagement', 'Engagement'),
				fieldType: "buttons",
				fieldValues: [
					t('momentFields.values.activityEngagement.active', 'Active'),
					t('momentFields.values.activityEngagement.passive', 'Passive'),
					t('momentFields.values.activityEngagement.refused', 'Refused'),
					t('momentFields.values.activityEngagement.outOfCenter', 'Out of Center')
				],
				displayRule: "showActivityEngagement"
			},
			{
				dataId: "activityType",
				label: t('momentFields.labels.type', 'Type'),
				fieldType: "select",
				fieldValuesQuery: "activityTypes"
			}
		]
	},
	"mood": {
		prettyName: "Mood",
		icon: {uri: "mt_mood_icon_sm"},
		listIcon: {uri: "mt_mood_icon_purple"},
		fields: [
			{
				dataId: "moodLevel",
				label: t('momentFields.labels.mood', 'Mood'),
				fieldType: "buttons",
				fieldValues: [
					{fieldValue: t('momentFields.values.moodLevels.happy', 'Happy'), fieldIcon: "smile-o"},
					{fieldValue: t('momentFields.values.moodLevels.soSo', 'SoSo'), fieldIcon: "meh-o"},
					{fieldValue: t('momentFields.values.moodLevels.sad', 'Sad'), fieldIcon: "frown-o"}
				]
			}
		]
	},
	"illness": {
		prettyName: "Illness",
		icon: {uri: "mt_illness_icon_sm"},
		listIcon: {uri: "mt_illness_icon_purple"},
		fields: [
			{
				dataId: "illnessSymptoms",
				label: t('momentFields.labels.symptomsObserved', 'Symptoms Observed'),
				fieldType: "select",
				fieldValuesQuery: "illnessSymptoms",
			}
		]
	},
	"supplies": {
		prettyName: "Supplies",
		icon: {uri: "mt_supplies_icon_sm"},
		listIcon: {uri: "mt_supplies_icon_purple"},
		fields: [
			{
				dataId: "supplyType",
				label: t('momentFields.labels.type', 'Type'),
				fieldType: "select",
				fieldValuesQuery: "supplyTypes"
			}
		]
	},
	"learning": {
		prettyName: "Learning",
		icon: {uri: "mt_learning_icon_sm"},
		listIcon: {uri: "mt_learning_icon_purple"},
		fields: [
			{
				dataId: "learningCurriculumId",
				label: t('momentFields.labels.curriculum', 'Curriculum'),
				fieldType: "select",
				fieldValuesQuery: "curriculums"
			},
			{
				dataId: "learningType",
				label: t('momentFields.labels.type', 'Type'),
				fieldType: "select",
				fieldValuesQuery: "curriculumTypes"
			}
		]
	},
	"portfolio": {
		prettyName: "Portfolio",
		icon: {uri: "mt_portfolio_icon_sm"},
		listIcon: {uri: "mt_portfolio_icon_purple"},
		fields: [
			{
				dataId: "SelectMilestoneId",
				label: t('momentFields.labels.selectMilestone', 'Select a milestone'),
				fieldType: "checkbox"
			},
			{
				dataId: "portfolioAgeRangeId",
				savedValue: "portfolioCurriculum",
				savedValueLabel: "headline",
				label: t('momentFields.labels.ageRange', 'Age Range'),
				fieldType: "select",
				fieldValuesQuery: "portfolioAgeRange",
				editLocked: true,
			},
			{
				dataId: "portfolioCurriculumId",
				savedValue: "portfolioCurriculum",
				savedValueLabel: "headline",
				label: t('momentFields.labels.activity', 'Activity'),
				fieldType: "select",
				fieldValuesQuery: "curriculums",
				editLocked: true
			},
			{
				dataId: "portfolioAssessments",
				label: t('momentFields.labels.observations', 'Observations'),
				fieldType: "assessments"
			}
		]
	},
	"incident": {
		prettyName: "Incident",
		icon: {uri: "mt_incident_icon_sm"},
		listIcon: {uri: "mt_incident_icon_purple"},
		fields: [
			{
				dataId: "incidentNature",
				label: t('momentFields.labels.natureOfIncident', 'Nature of Incident'),
				fieldType:"string",
				customization: "moments/incident/extraIncidentFields"
			},
			{
				dataId: "incidentActionTaken",
				label: t('momentFields.labels.actionTaken', 'Action Taken'),
				fieldType:"string",
				customization: "moments/incident/extraIncidentFields"
			},
			{
				dataId: "incidentLocation",
				label: t('momentFields.labels.incidentLocation', 'Incident Location'),
				fieldType:"string",
				customization: "moments/incident/extraIncidentFields"
			}
		]
	},
	"alert": {
		prettyName: "Notification",
		icon: {uri: "mt_notification_icon_sm"},
		listIcon: {uri: "mt_notification_icon_purple"},
		validationRules: ["alertValidation", "smsTextValidation"],
		fields: [
			{
				dataId: "tagOnlyCheckins",
				label: t('momentFields.labels.includeOnlyCheckedIn', 'Include only if checked in now'),
				fieldType: "checkbox"
			},
			{
				dataId: "tagEntireOrg",
				label: t('momentFields.labels.tagEveryone', 'Tag everyone (admins, staff, families)'),
				fieldType: "checkbox"
			},
			{
				dataId: "alertSendTypeEmail",
				label: t('momentFields.labels.sendViaEmail', 'Send via Email'),
				fieldType: "checkbox"
			},
			{
				dataId: "alertSendTypeText",
				label: t('momentFields.labels.sendViaSMS', 'Send via SMS (text)'),
				fieldType: "checkbox"
			},
			{
				dataId: "alertSendTypePush",
				label: t('momentFields.labels.sendViaPush', 'Send via Push (app)'),
				fieldType: "checkbox"
			},
		]
	},
	"ouch": {
		prettyName: "Ouch",
		icon: {uri: "mt_ouch_icon_sm"},
		listIcon: {uri: "mt_ouch_icon_purple"},
		fields: [
			{
				dataId: "ouchDescription",
				label: t('momentFields.labels.description', 'Description'),
				fieldType: "text"
			},
			{
				dataId: "ouchCare",
				label: t('momentFields.labels.careProvided', 'Care Provided'),
				fieldType: "string"
			},
			{
				dataId: "ouchContactedParent",
				label: t('momentFields.labels.contactedParent', 'Contacted Parent'),
				fieldType: "buttons",
				fieldValues: [
					t('momentFields.values.contactMethods.phone', 'Phone'),
					t('momentFields.values.contactMethods.email', 'Email'),
					t('momentFields.values.contactMethods.inPerson', 'In Person'),
					t('momentFields.values.contactMethods.didNotNotify', 'Did Not Notify')
				]
			},
			{
				dataId: "ouchCalledParentTime",
				label: t('momentFields.labels.time', 'Time'),
				fieldType: "timePicker"
			},
			{
				dataId: "ouchContactedDoctor",
				label: t('momentFields.labels.contactedDoctor', 'Contacted Doctor'),
				fieldType: "buttons",
				fieldValues: [
					t('momentFields.values.contactMethods.phone', 'Phone'),
					t('momentFields.values.contactMethods.email', 'Email'),
					t('momentFields.values.contactMethods.inPerson', 'In Person'),
					t('momentFields.values.contactMethods.didNotNotify', 'Did Not Notify')
				]
			},
			{
				dataId: "ouchCalledDoctorTime",
				label: t('momentFields.labels.time', 'Time'),
				fieldType: "timePicker"
			},
			{
				dataId: "ouchNurseNotified",
				label: t('momentFields.labels.nurseNotified', 'Nurse Notified'),
				fieldType: "checkbox"
			},
			{
				dataId: "ouchProfessionalMedication",
				label: t('momentFields.labels.professionalMedicationNecessary', 'Professional Medication Necessary?'),
				fieldType: "checkbox"
			}
		]
	},
	"medical": {
		prettyName: "Medical",
		icon: {uri: "mt_medical_icon_sm"},
		listIcon: {uri: "mt_medical_icon_purple"},
		fields: [
			{
				dataId: "medicalMedicineType",
				label: t('momentFields.labels.type', 'Type'),
				fieldType: "select",
				fieldValuesQuery: "medicineTypes",
				displayRule: "medicalAllowFreeEntry"
			},
			{
				dataId: "medicalMedicationName",
				label: t('momentFields.labels.medicationName', 'Medication Name'),
				fieldType: "string",
				displayRule: "medicalAllowFreeEntry"
			},
			{
				dataId: "medicalDoctorName",
				label: t('momentFields.labels.doctorName', 'Doctor Name'),
				fieldType: "string",
				displayRule: "medicalAllowFreeEntry"
			},
			{
				dataId: "medicalMedicationAmount",
				label: t('momentFields.labels.doctorName', 'Doctor Name'),
				fieldType: "string",
				displayRule: "medicalAllowFreeEntry"
			},
			{
				dataId: "medicalMedicationId",
				label: t('momentFields.labels.medication', 'Medication'),
				fieldType: "select",
				fieldValuesQuery: "medications",
				customization: "moments/medical/useProfileMedications",
				editLocked: true,
				savedValue: "medicalMedication",
				savedValueLabel: "medicationDescription",
				validation: [{
					mandatory: true,
					message: "Please select a Medication"
				}]
			},
			{
				dataId: "medicalAdministeredBy",
				label: t('momentFields.labels.administeredBy', 'Administered By'),
				fieldType: "select",
				fieldValuesQuery: "staffPeople",
				editLocked: true,
				savedValue: "medicalAdministeredByPerson",
				savedValueLabel: "fullName",
				validation: [{
					mandatory: true,
					message: "Please select a Administered By"
				}]
			}

		]
	}
};
