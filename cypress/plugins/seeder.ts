import path from 'path';
import fs from 'fs-extra';
import { Seeder } from 'mongo-seeding';
import { CypressConfig, DefaultDBChange } from '../../interfaces/seeder';
import { SeederCollection } from 'mongo-seeding/dist/common';
import { MongoClient, MongoClientOptions } from 'mongodb';
import { MongoCommandOptions } from './mongo-types';

const documentCaches = {};
const uri = 'mongodb://127.0.0.1:27017/meteor';
const config = {
  database: uri,
  dropDatabase: false,
  dropCollections: false,
  removeAllDocuments: true
};

const seeder = new Seeder(config);
const defaultPath = './cypress/plugins/collections';

module.exports = (on: any, config: CypressConfig) => {
  on('task', {
    async seedDefaultDB() {
      for await (const [collection, changes] of Object.entries(REQUIRED_CHANGES_FOR_DEFAULT_DB)) {
        const path = buildCollectionPath(collection, defaultPath);

        for await (const change of changes) {
          await updateRequiredDocuments(path, change.property, change.value);
        }
      }

      const defaultCollections = getDefaultCollection();

      return await seedDB(defaultCollections);
    },

    async seedCustomDB({ params }) {
      return await seedCustomDB(params);
    },

    async executeMongoCommand(params: MongoCommandOptions) {
      if (!params || !params.collection || !params.operation) {
        return null;
      }
      
      const { collection, operation, query = {}, update = {}, options: opOptions = {} } = params;
      
      let client;
      try {
        // Add explicit connection options
        const mongoOptions: MongoClientOptions = {
          useUnifiedTopology: true,
          useNewUrlParser: true
        };
        
        // Try using a more explicitly formatted connection string
        client = new MongoClient(uri, mongoOptions);
        await client.connect();
        
        const db = client.db();
        const coll = db.collection(collection);
        
        let result;
        switch (operation) {
          case 'find':
            result = await coll.find(query, opOptions).toArray();
            break;
          case 'findOne':
            result = await coll.findOne(query, opOptions);
            break;
          case 'updateOne':
            result = await coll.updateOne(query, update, opOptions);
            break;
          case 'updateMany':
            result = await coll.updateMany(query, update, opOptions);
            break;
          case 'deleteOne':
            result = await coll.deleteOne(query, opOptions);
            break;
          case 'deleteMany':
            result = await coll.deleteMany(query, opOptions);
            break;
          case 'insertOne':
            result = await coll.insertOne(query, opOptions);
            break;
          case 'insertMany':
            result = await coll.insertMany(query, opOptions);
            break;
          case 'count':
            result = await coll.countDocuments(query, opOptions);
            break;
          default:
            result = { error: `Unsupported operation: ${operation}` };
        }
        
        return result;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 
          (typeof error === 'string' ? error : 'Unknown error occurred');
        console.error(`MongoDB operation failed: ${errorMessage}`);
        return { error: errorMessage };
      } finally {
        if (client) {
          await client.close();
        }
      }
    },

    clearCache() {
      return clearCache();
    }
  });
  return config;
};

function getDefaultCollection(): SeederCollection[] {
  return seeder.readCollectionsFromPath(path.resolve(defaultPath));
}

async function seedDB(collections: SeederCollection[]) {
  await seeder.import(collections);
  return null;
}

async function seedCustomDB(params) {
  if (!params) {
    return null;
  }

  const customPath = './cypress/plugins/custom/collections';
  await createAndSeedCollections(params.collections, customPath);

  const updatedCollections = seeder.readCollectionsFromPath(path.resolve(customPath));

  return await seedDB(updatedCollections);
}

async function createAndSeedCollections(collections, customPath) {
  for (const collection of collections) {
    await createCustomCollection(collection.name, collection.documents, customPath);
  }
}

async function createCustomCollection(collection, documents, customPath) {
  const collectionPath = buildCollectionPath(collection, customPath);
  await ensureCollectionPathExists(collectionPath);

  let existingDocuments = await getExistingDocuments(collectionPath, collection);

  const defaultDocumentsPath = path.resolve(`${defaultPath}/${collection}/${collection}.json`);
  const defaultDocuments = await getExistingDocuments(defaultDocumentsPath, collection);

  existingDocuments = [...existingDocuments, ...defaultDocuments];

  if (updateRequired(documents, collection, existingDocuments)) {
    await fs.writeJson(collectionPath, existingDocuments);
  }
}

function buildCollectionPath(collection, customPath): string {
  return path.resolve(`${customPath}/${collection}/${collection}.json`);
}

async function ensureCollectionPathExists(collectionPath): Promise<void> {
  const dir = path.dirname(collectionPath);
  await fs.ensureDir(dir);
  await fs.ensureFile(collectionPath);
}

async function getExistingDocuments(collectionPath, collection): Promise<any> {
  let existingDocuments = await fs.readJson(collectionPath, { throws: false });

  if (!existingDocuments || existingDocuments.length === 0) {
    existingDocuments = [{}];
    await fs.writeJson(collectionPath, existingDocuments);
    resetDocumentCache(collection);
  }

  return existingDocuments;
}

function resetDocumentCache(collection) {
  documentCaches[collection] = {};
}

function updateRequired(documents, collectionName, existingDocuments): boolean {
  let update = false;

  documents.forEach((document) => {
    if (shouldAddDocument(document, collectionName)) {
      existingDocuments.push(document);
      update = true;
    }
  });

  return update;
}

function shouldAddDocument(document: Record<string, unknown>, collectionName: string): boolean {
  if (!documentCaches[collectionName]) {
    documentCaches[collectionName] = {};
  }

  const documentExist = documentCaches[collectionName][document._id] !== undefined;
  if (documentExist) {
    return false;
  }

  documentCaches[collectionName][document._id] = true;
  return true;
}

function clearCache() {
  Object.keys(documentCaches).forEach((collection) => {
    documentCaches[collection] = {};
  });
  return null;
}

async function updateRequiredDocuments(path: string, property: string, value: unknown): Promise<void> {
  const documents = await modifyDocuments(path, property, value);

  await fs.writeJson(path, documents);
}

async function modifyDocuments(path: string, property: string, value: unknown): Promise<unknown> {
  const documents = await fs.readJson(path, { throws: false });

  if (documents.length) {
    const modifiedDocuments = documents.map((document) => {
      document[property] = value;
      return document;
    });

    return modifiedDocuments;
  }

  return documents;
}

const REQUIRED_CHANGES_FOR_DEFAULT_DB: Record<string, DefaultDBChange[]> = {
  reservations: [
    {
      property: 'scheduledDate',
      value: new Date().getTime()
    },
    {
      property: 'cancellationDate',
      value: new Date().getTime()
    }
  ]
};
