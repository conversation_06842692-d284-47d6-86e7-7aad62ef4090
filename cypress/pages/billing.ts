class Billing {
  public get billingUpdateButton() {
    return cy.get(`[data-cy=auto-pay-update-btn]`);
  }

  public get includeWaitList() {
    return cy.get(`[data-cy=include-wait-list]`);
  }

  public get reportDetails() {
    return cy.get(`[data-cy=view-report-details]`);
  }

  public get autoPayReportTable() {
    return cy.get(`[data-cy=auto-pay-report-table]`);
  }

  public get billingTabParents() {
    return cy.get(`[data-cy=billing-header-nav]`);
  }

  public get parentPaymentDetails() {
    return cy.get(`[data-cy=parent-payment-info]`);
  }

  public get enrollmentStatus() {
    return cy.get(`[data-cy=enrollment-status]`);
  }

  public get cardLastFourDigits() {
    return cy.get(`[data-cy=card-last-four-digits]`);
  }

  public get transactionsReportTable() {
    return cy.get(`[data-cy=transactions-report-table]`);
  }

  public get transactionsTypes() {
    return cy.get(`[data-cy=transactions-type-select]`);
  }

  public get transactionEndDateInput() {
    return cy.get(`[data-cy=transactions-end-date-inp]`);
  }

  public get transactionStartDateInput() {
    return cy.get(`[data-cy=transactions-start-date-inp]`);
  }

  public get transactionUpdateButton() {
    return cy.get(`[data-cy=transaction-update-btn]`);
  }

  public get transactionExportButton() {
    return cy.get(`[data-cy=transaction-export-btn]`);
  }

  public get transactionCardBody() {
    return cy.get(`[data-cy=transactions-card-body]`);
  }

  public get totalCreditBalanceAmount() {
    return cy.get(`[data-cy=total-credit-balance-amount]`);
  }

  public get depositeDateRange() {
    return cy.get(`[data-cy=deposits-date-range]`);
  }

  public get manageBankDepositsUpdateBtn() {
    return cy.get(`[data-cy=mbd-report-update-btn]`);
  }

  public get manageBankDepositeApplyBtn() {
    return cy.get('.applyBtn');
  }

  public get depositsReportDateColumn() {
    return cy.get(`[data-cy=deposits-payment-table-date]`);
  }

  public get depositsReportTable() {
    return cy.get(`[data-cy=deposite-manage-report-table]`, { timeout: 10000 });
  }

  public get depositsReportPayerByName() {
    return cy.get(`[data-cy=payer-by-name]`);
  }

  public get depositsReportPaymentTypes() {
    return cy.get(`[data-cy=payment-method-types]`);
  }

  public get depositsReportSelectAll() {
    return cy.get(`[data-cy=select-all-deposits]`);
  }

  public get depositsReportPaymentAmount() {
    return cy.get(`[data-cy=deposits-report-amount]`);
  }

  public get depositsReportTotalAmount() {
    return cy.get(`[data-cy=deposits-total-cell]`);
  }

  public get pastDepositsBtn() {
    return cy.get(`[data-cy=past-deposits-tab]`);
  }

  public get pastDepositsDateRange() {
    return cy.get(`[data-cy=past-deposits-date-range]`);
  }

  public get pastDepositsUpdateBtn() {
    return cy.get(`[data-cy=past-deposits-update-btn]`);
  }

  public get pastDepositsDateColumn() {
    return cy.get(`[data-cy=past-deposits-date-column]`);
  }

  public get pastDepositsAmountColumn() {
    return cy.get(`[data-cy=past-deposits-total-amount-column]`);
  }

  public get pastDepositsReportTable() {
    return cy.get(`[data-cy=past-deposits-report-table]`);
  }

  public get pastDepositsActionColumn() {
    return cy.get(`[data-cy="past-deposits-action-column"]`);
  }

  public get updateChargebacksBtton() {
    return cy.get(`[data-cy=update-chargeback-btn]`);
  }

  public get startDateChargebackInput() {
    return cy.get(`[data-cy=start-date-chargeback-inp]`);
  }

  public get endDateChargebackInput() {
    return cy.get(`[data-cy=end-date-chargeback-inp]`);
  }

  public get showResolvedCheckbox() {
    return cy.get(`[data-cy=show-resolved-chkbox]`);
  }

  public get actionDropdownButon() {
    return cy.get(`[data-cy=action-dropdown-btn]`);
  }

  public get actionDropmenuItems() {
    return cy.get(`[data-cy=action-menu-items]`);
  }

  public get billingTableRows() {
    return cy.get(`[data-cy="billing-table-rows"]`);
  }

  public get actionsTableColumn() {
    return cy.get(`[data-cy="actions-column"]`);
  }

  public get chargebackStatusColumn() {
    return cy.get(`[data-cy=chargeback-status-col]`);
  }

  public get familyBalanceUpdateButton() {
    return cy.get(`[data-cy=family-balance-update-btn]`);
  }

  public get familyBalanceStartDateInput() {
    return cy.get(`[data-cy=family-balance-start-date-inp]`);
  }

  public get familyBalancePaginationPreviousButton() {
    return cy.get(`[data-cy=family-balance-pagination-prev-btn]`);
  }

  public get familyBalancePaginationNextButton() {
    return cy.get(`[data-cy=family-balance-pagination-next-btn]`);
  }

  public get familyBalancePaginationCount() {
    return cy.get(`[data-cy=fb-pagination-records-count]`, { timeout: 5000 });
  }

  public get familyBalanceExportCsv() {
    return cy.get(`[data-cy=fb-export-csv-btn]`);
  }

  public get chargebackReportTable() {
    return cy.get(`[data-cy=chargeback-billing-table]`);
  }

  public get showExpiredPlans() {
    return cy.get(`[data-cy=show-expired-plans]`);
  }

  public get btnManualPlanInvoices() {
    return cy.get(`[data-cy=btn-manual-plan-invoices]`);
  }

  public get btnAddPlan() {
    return cy.get(`[data-cy=btn-add-plan]`);
  }

  public get planDescription() {
    return cy.get(`[data-cy=plan-description]`);
  }

  public get planName() {
    return cy.get(`[data-cy=plan-name]`);
  }

  public get planDatesFormatted() {
    return cy.get(`[data-cy=plan-dates-formatted]`);
  }

  public get editBillingNotes() {
    return cy.get(`[data-cy=edit-billing-notes]`);
  }

  public get billingNotes() {
    return cy.get(`[data-cy=billing-notes]`);
  }

  public get noBillingNotes() {
    return cy.get(`[data-cy=no-billing-notes]`);
  }

  public get addChargeBtn() {
    return cy.get(`[data-cy=add-charge-btn]`);
  }

  public get manualInvoiceCharge() {
    return cy.get(`[data-cy=manual-invoice-charge]`);
  }

  public get pendingChargeDate() {
    return cy.get(`[data-cy=pending-charge-date]`);
  }

  public get pendingChargeItem() {
    return cy.get(`[data-cy=pending-charge-item]`);
  }

  public get pendingChargeDescription() {
    return cy.get(`[data-cy=pending-charge-description]`);
  }

  public get pendingChargeAmount() {
    return cy.get(`[data-cy=pending-charge-amount]`);
  }

  public get noPendingCharges() {
    return cy.get(`[data-cy=no-pending-charges]`);
  }

  public get noSecurityDeposits() {
    return cy.get(`[data-cy=no-security-deposits]`);
  }

  public get noHeldFunds() {
    return cy.get(`[data-cy=no-held-funds]`);
  }

  public get billingHistoryLogs() {
    return cy.get(`[data-cy=billing-history-logs]`);
  }

  public get billingHistoryDate() {
    return cy.get(`[data-cy=billing-history-date]`);
  }

  public get billingHistoryAction() {
    return cy.get(`[data-cy=billing-history-action]`);
  }

  public get billingHistoryPerformedBy() {
    return cy.get(`[data-cy=billing-history-performed-by]`);
  }

  public get invoiceNumber() {
    return cy.get(`[data-cy=invoice-number]`);
  }

  public get selectPlanName() {
    return cy.get(`[data-cy=select-plan-name]`);
  }

  public get includeEnrollmentForecastingDate() {
    return cy.get(`[data-cy=include-enrollment-forecasting-date]`);
  }

  public get selectCharge() {
    return cy.get(`[data-cy=select-charge]`);
  }

  public get addPlanBtn() {
    return cy.get(`[data-cy=add-plan-btn]`);
  }

  public get addPlanDescription() {
    return cy.get(`[data-cy=add-plan-description]`);
  }

  public get addPlanCategory() {
    return cy.get(`[data-cy=add-plan-category]`);
  }

  public get addPlanProgram() {
    return cy.get(`[data-cy=add-plan-program]`);
  }

  public get addPunchCardNumberOfDays() {
    return cy.get(`[data-cy=punch-card-number-of-days]`);
  }

  public get addPlanFrequency() {
    return cy.get(`[data-cy=add-plan-frequency]`);
  }

  public get addPlanAmount() {
    return cy.get(`[data-cy=add-plan-amount]`);
  }

  public get addPlanExpiration() {
    return cy.get(`[data-cy=add-plan-expiration]`);
  }

  public get addPlanLedgerAccount() {
    return cy.get(`[data-cy=add-plan-ledger-account]`, { timeout: 2000 });
  }

  public get addPlanDateType() {
    return cy.get(`[data-cy=add-plan-date-type]`);
  }

  public get recurringStartDate() {
    return cy.get(`[data-cy=recurring-start-date]`);
  }

  public get recurringFrequency() {
    return cy.get(`[data-cy=recurring_frequency]`);
  }

  public get recurringOccurrences() {
    return cy.get(`[data-cy=recurring_occurrences]`);
  }

  /**
   * Gets a Cypress chain for recurring day checkboxes
   * @param days Array of day abbreviations ('sun', 'mon', 'tue', etc.) or a single day
   * @returns Cypress chain for the specified day(s)
   *
   * @example
   * // Check a single day
   * this.recurringDays('mon').check();
   *
   * @example
   * // Check multiple days
   * this.recurringDays(['mon', 'wed', 'fri']).each(checkbox => {
   *   cy.wrap(checkbox).check();
   * });
   */
  public recurringDays(days: string | string[]) {
    // Handle both single day string and array of days
    const dayArray = Array.isArray(days) ? days : [days];

    if (dayArray.length === 1) {
      // For a single day, return the direct selector
      return cy.get(`[data-cy=recurring_${dayArray[0].toLowerCase()}]`);
    } else {
      // For multiple days, create an array of selectors and use each() to iterate
      const selectors = dayArray.map((day) => `[data-cy=recurring_${day.toLowerCase()}]`);
      return cy.get(selectors.join(', '));
    }
  }

  public get addPlanServiceDates() {
    return cy.get(`[data-cy=add-plan-service-dates]`);
  }

  public get addPlanStartTime() {
    return cy.get(`[data-cy=add-plan-start-time]`);
  }

  public get addPlanEndTime() {
    return cy.get(`[data-cy=add-plan-end-time]`);
  }

  public get addPlanRegStartDate() {
    return cy.get(`[data-cy=add-plan-reg-start-date]`);
  }

  public get addPlanRegEndDate() {
    return cy.get(`[data-cy=add-plan-reg-end-date]`);
  }

  public get addPlanGrades() {
    return cy.get(`[data-cy=add-plan-grades]`);
  }

  public get addPlanScheduleType() {
    return cy.get(`[data-cy=add-plan-schedule-type]`);
  }

  public get addPlanRegFreeExempt() {
    return cy.get(`[data-cy=add-plan-reg-free-exempt]`);
  }

  public get billingTable() {
    return cy.get(`[data-cy=billing-table]`, { timeout: 2000 });
  }

  public get searchByName() {
    return cy.get(`[data-cy=search-by-name]`);
  }

  public get searchBtnClear() {
    return cy.get(`[data-cy=search-btn-clear]`);
  }

  public get planDesc() {
    return cy.get(`[data-cy=plan-desc]`);
  }

  public suspensionBtn(planId: string) {
    return cy.get(`[data-cy=suspend-button-${planId}]`);
  }

  public archiveBtn(planId: string) {
    return cy.get(`[data-cy=archive-button-${planId}]`);
  }

  public propagateBtn(planId: string) {
    return cy.get(`[data-cy=propagate-button-${planId}]`);
  }

  public get showArchivedPlans() {
    return cy.get(`[data-cy=show-archived-plans]`);
  }

  public get type() {
    return cy.get(`[data-cy=type]`);
  }

  public get planFrequency() {
    return cy.get(`[data-cy=plan-frequency]`);
  }

  public get planCategory() {
    return cy.get(`[data-cy=plan-category]`);
  }

  public get planLedgerAccount() {
    return cy.get(`[data-cy=plan-ledger-account]`);
  }

  public get planExpirationDate() {
    return cy.get(`[data-cy=plan-expiration-date]`);
  }

  public get checkItem() {
    return cy.get(`[data-cy=check-item]`);
  }

  public get checkPunchCard() {
    return cy.get(`[data-cy=check-punch-card]`);
  }

  public get checkRefundable() {
    return cy.get(`[data-cy=refundable-deposit]`);
  }

  public get addServiceDatePicker() {
    return cy.get(`[data-cy=service-datepicker]`);
  }

  public get addDaysBtn() {
    return cy.get(`[data-cy=add-days-btn]`);
  }

  public get checkBundle() {
    return cy.get(`[data-cy=check-bundle]`);
  }

  public bundlePlanNumber(id: string) {
    return cy.get(`[data-cy=bundle-plan-${id}]`);
  }

  public bundleScaledAmount(row: string, column: string) {
    return cy.get(`[data-cy=scaled-amount-${row}-${column}]`);
  }

  public get couponTab() {
    return cy.get(`[data-cy=coupon-tab]`);
  }

  public get couponTable() {
    return cy.get(`[data-cy=coupon-table]`);
  }

  public get addCouponBtn() {
    return cy.get(`[data-cy=add-coupon-btn]`);
  }

  public get couponCode() {
    return cy.get(`[data-cy=coupon-code]`);
  }

  public get couponDesc() {
    return cy.get(`[data-cy=coupon-description]`);
  }

  public get couponAmount() {
    return cy.get(`[data-cy=coupon-amount]`);
  }

  public get couponStartDate() {
    return cy.get(`[data-cy=coupon-start-date]`);
  }

  public get couponEndDate() {
    return cy.get(`[data-cy=coupon-end-date]`);
  }

  public editCouponBtn(id: string) {
    return cy.get(`[data-cy=edit-coupon-button-${id}]`);
  }

  public suspendCouponBtn(id: string) {
    return cy.get(`[data-cy=suspend-coupon-button-${id}]`);
  }

  public archiveCouponBtn(id: string) {
    return cy.get(`[data-cy=archive-coupon-button-${id}]`);
  }

  public get addCouponCode() {
    return cy.get(`[data-cy=add-coupon-code]`, { timeout: 3000 });
  }

  public get addCouponDesc() {
    return cy.get(`[data-cy=add-coupon-desc]`);
  }

  public get addCouponAmount() {
    return cy.get(`[data-cy=add-coupon-amount]`);
  }

  public get addAmountType() {
    return cy.get(`[data-cy=add-coupon-amount-type]`);
  }

  public get addRegEndDate() {
    return cy.get(`[data-cy=add-reg-end-date]`);
  }

  public get addCouponExpirationDate() {
    return cy.get(`[data-cy=add-coupon-exp-date]`);
  }

  public get addMaxNumberRegistration() {
    return cy.get(`[data-cy=add-max-number-registration]`);
  }

  public get addUsedWithOthersCoupons() {
    return cy.get(`[data-cy=add-used-with-others]`);
  }

  public get addUserWithDiscounts() {
    return cy.get(`[data-cy=add-used-with-discounts]`);
  }

  public get addRestrictedForTimePeriods() {
    return cy.get(`[data-cy=add-restricted-for-time-periods]`);
  }

  public get addRestricBillingPlan() {
    return cy.get(`[data-cy=add-restrict-billing-plan]`);
  }

  public get addCouponBillingPlan() {
    return cy.get(`[data-cy=add-coupon-billing-plan]`);
  }

  public get addLedgerCode() {
    return cy.get(`[data-cy=add-ledger-code]`);
  }

  public get addUseCouponInBundles() {
    return cy.get(`[data-cy=add-use-in-bundles]`);
  }

  public get searchByCode() {
    return cy.get(`[data-cy=search-by-code]`);
  }

  public get searchCuponBtnClear() {
    return cy.get(`[data-cy=search-code-btn-clear]`);
  }

  public get showArchivedCoupons() {
    return cy.get(`[data-cy=show-archived-coupons]`);
  }

  public get overrideRate() {
    return cy.get(`[data-cy=override-rate]`);
  }

  public get enrollmentForecastEnd() {
    return cy.get(`[data-cy=enrollment-forecast-end]`);
  }

  public get addAllocationBtn() {
    return cy.get(`[data-cy=add-allocation-btn]`);
  }

  public get allocationType() {
    return cy.get(`[data-cy=allocation-type]`);
  }

  public get allocationCouponCode() {
    return cy.get(`[data-cy=allocation-coupon-code]`);
  }

  public get applyBtn() {
    return cy.get(`[data-cy=apply-btn]`);
  }

  public editPlanById(planId: string) {
    return cy.get(`[data-cy=edit-button-${planId}]`);
  }

  public get allocationDiscountType() {
    return cy.get(`[data-cy=allocation-discount-type]`);
  }

  public get allocationReimbursementType() {
    return cy.get(`[data-cy=allocation-reimbursement-type]`);
  }

  public get discountAmount() {
    return cy.get(`[data-cy=discount-amount]`);
  }

  public get familyAmount() {
    return cy.get(`[data-cy=family-amount]`);
  }

  public get scaledAmount() {
    return cy.get(`[data-cy=scaled-amount]`);
  }

  public get bankAccount() {
    return cy.get(`[data-cy=bank-account]`, { timeout: 2000 });
  }

  public get lastFourBankAccount() {
    return cy.get(`[data-cy=last-four-bank-account]`);
  }

  public get statusBankAccount() {
    return cy.get(`[data-cy=status-bank-account]`);
  }

  public get verifiedBankAccount() {
    return cy.get(`[data-cy=verified-bank-account]`);
  }

  public get replacePaymentMethodBankAccount() {
    return cy.get(`[data-cy=replace-payment-method-bank-account]`);
  }

  public get removePaymentMethodBankAccount() {
    return cy.get(`[data-cy=remove-payment-method-bank-account]`);
  }

  public get creditCard() {
    return cy.get(`[data-cy=credit-card]`, { timeout: 2000 });
  }

  public get statusCreditCard() {
    return cy.get(`[data-cy=status-credit-card]`);
  }

  public get replacePaymentMethodCreditCard() {
    return cy.get(`[data-cy=replace-payment-method-credit-card]`);
  }

  public get removePaymentMethodCreditCard() {
    return cy.get(`[data-cy=remove-payment-method-credit-card]`);
  }

  public get addCreditCardBtn() {
    return cy.get(`[data-cy=add-credit-card-btn]`);
  }

  public get addBankAccountBtn() {
    return cy.get(`[data-cy=add-bank-account-btn]`);
  }

  public get setupAutopayBtn() {
    return cy.get(`[data-cy=setup-autopay-btn]`);
  }

  public get updateAutopayBtn() {
    return cy.get(`[data-cy=update-autopay-btn]`);
  }

  public get disableAutopayBtn() {
    return cy.get(`[data-cy=disable-autopay-btn]`);
  }

  public get autopayStatusBankAccount() {
    return cy.get(`[data-cy=autopay-status-bank-account]`);
  }

  public get autopayStatusCreditCard() {
    return cy.get(`[data-cy=autopay-status-credit-card]`);
  }

  public get childName() {
    return cy.get(`[data-cy=child-name]`);
  }

  public get depositCheckbox() {
    return cy.get(`[data-cy=deposit-checkbox]`);
  }

  public get openPaymentsTab() {
    return cy.get(`[data-cy=open-payments-tab]`);
  }

  public get creditAmountInput() {
    return cy.get(`[data-cy=credit-amount-input]`);
  }

  public get creditReasonSelect() {
    return cy.get(`[data-cy=credit-reason-select]`);
  }

  public get creditNoteInput() {
    return cy.get(`[data-cy=credit-note-input]`);
  }

  public get creditLineItemSelect() {
    return cy.get(`[data-cy=credit-line-item-select]`);
  }

  public get createDepositBtn() {
    return cy.get(`[data-cy=create-deposit-btn]`);
  }

  public get updateCreateDepositBtn() {
    return cy.get(`[data-cy=update-create-deposit-btn]`);
  }

  public get dateRangeInput() {
    return cy.get(`[data-cy=date-range-input]`);
  }

  public get invoiceActionsBtn() {
    return cy.get(`[data-cy=invoice-actions-btn]`);
  }

  public get resendInvoiceBtn() {
    return cy.get(`[data-cy=re-send-invoice-btn]`);
  }

  public get creditInvoiceBtn() {
    return cy.get(`[data-cy=credit-invoice-btn]`);
  }

  public get voidInvoiceBtn() {
    return cy.get(`[data-cy=void-invoice-btn]`);
  }

  public get issueRefundBtn() {
    return cy.get(`[data-cy=issue-refund-btn]`);
  }

  public get voidLineItemBtn() {
    return cy.get(`[data-cy=void-line-item-btn]`);
  }

  public get modifyLineItemBtn() {
    return cy.get(`[data-cy=modify-line-item-btn]`);
  }

  public get manualRefundLineItemBtn() {
    return cy.get(`[data-cy=manual-refund-line-item-btn]`);
  }

  public get reverseLineItemBtn() {
    return cy.get(`[data-cy=reverse-line-item-btn]`);
  }

  public get adjustLineItemBtn() {
    return cy.get(`[data-cy=adjust-line-item-btn]`);
  }

  public get applyToACreditMemo() {
    return cy.get(`[data-cy=apply-to-a-credit-memo]`);
  }

  public get applyToAnInvoice() {
    return cy.get(`[data-cy=apply-to-an-invoice]`);
  }

  public get adjustPaymentSaveBtn() {
    return cy.get(`[data-cy=adjust-payment-save-btn]`);
  }

  public get adjustPaymentCancelBtn() {
    return cy.get(`[data-cy=adjust-payment-cancel-btn]`);
  }

  public get paymentType() {
    return cy.get(`[data-cy=payment-type]`);
  }

  public get paymentTypeSelect() {
    return cy.get(`[data-cy=payment-type-select]`);
  }

  public get searchEntries() {
    return cy.get(`[data-cy=search-entries]`);
  }

  public get includePayouts() {
    return cy.get(`[data-cy=include-payouts-check]`);
  }

  public get dateInvoiceCredit() {
    return cy.get(`[data-cy=date-invoice-credit]`);
  }

  public get typeInvoiceCredit() {
    return cy.get(`[data-cy=type-invoice-credit]`);
  }

  public get descriptionInvoiceCredit() {
    return cy.get(`[data-cy=description-invoice-credit]`);
  }

  public get forInvoiceCredit() {
    return cy.get(`[data-cy=for-invoice-credit]`);
  }

  public get checkNumberInvoice() {
    return cy.get(`[data-cy=check-number-invoice]`);
  }

  public get periodInvoiceCredit() {
    return cy.get(`[data-cy=period-inovice-credit]`);
  }

  public get invoiceCreditAmount() {
    return cy.get(`[data-cy=invoice-credit-amount]`);
  }

  public get totalAmountInvoiceCredit() {
    return cy.get(`[data-cy=total-amount-invoice-credit]`, { timeout: 5000 });
  }

  public get invoiceDate() {
    return cy.get(`[data-cy=invoice-date]`);
  }

  public get invoiceDescription() {
    return cy.get(`[data-cy=invoice-description]`);
  }

  public get invoiceAmount() {
    return cy.get(`[data-cy=invoice-amount]`);
  }

  public get invoiceBalance() {
    return cy.get(`[data-cy=invoice-balance]`);
  }

  public get invoiceDateLabel() {
    return cy.get(`[data-cy=invoice-date-label]`);
  }

  public get invoiceNumberLabel() {
    return cy.get(`[data-cy=invoice-number-label]`);
  }

  public get dueDate() {
    return cy.get(`[data-cy=due-date-label]`);
  }

  public get invoiceFor() {
    return cy.get(`[data-cy=invoice-for-label]`);
  }

  public get invoiceCovers() {
    return cy.get(`[data-cy=invoice-covers-label]`);
  }

  public get invoiceDateLedger() {
    return cy.get(`[data-cy=invoice-date-ledger]`);
  }

  public get invoiceDescriptionLedger() {
    return cy.get(`[data-cy=invoice-date-description-ledger]`);
  }

  public get invoiceLedgerDebit() {
    return cy.get(`[data-cy=invoice-ledger-debit]`);
  }

  public get invoiceLedgerCredit() {
    return cy.get(`[data-cy=invoice-ledger-credit]`);
  }

  public get invoiceLedgerBalance() {
    return cy.get(`[data-cy=invoice-ledger-balance]`);
  }

  public get openPayerBalancesOnly() {
    return cy.get(`[data-cy=open-payer-balances-only]`);
  }

  public get filterByPayer() {
    return cy.get(`[data-cy=filter-by-payer]`);
  }

  public get familyPaymentStatus() {
    return cy.get(`[data-cy=family-payment-status]`);
  }

  public get upcomingInvoiceDate() {
    return cy.get(`[data-cy=upcoming-invoice-date]`);
  }

  public get upcomingInvoiceDescription() {
    return cy.get(`[data-cy=upcoming-invoice-description]`);
  }

  public get upcomingInvoicePersonName() {
    return cy.get(`[data-cy=upcoming-invoice-person-name]`);
  }

  public get upcomingInvoiceDueDate() {
    return cy.get(`[data-cy=upcoming-invoice-due-date]`);
  }

  public get upcomingInvoiceOpenAmount() {
    return cy.get(`[data-cy=upcoming-invoice-open-amount]`);
  }

  public get noResultsToDisplay() {
    return cy.get(`[data-cy=no-results-to-display]`);
  }

  public get receiptDate() {
    return cy.get(`[data-cy=receipt-date]`);
  }

  public get receiptType() {
    return cy.get(`[data-cy=receipt-type]`);
  }

  public get receiptPerson() {
    return cy.get(`[data-cy=receipt-person]`);
  }

  public get receiptDescription() {
    return cy.get(`[data-cy=receipt-description]`);
  }

  public get receiptInvoiceNumber() {
    return cy.get(`[data-cy=receipt-invoice-number]`);
  }

  public get receiptAmount() {
    return cy.get(`[data-cy=receipt-amount]`);
  }

  public get receiptTotalAmount() {
    return cy.get(`[data-cy=receipt-total-amount]`);
  }

  public get checkNumberInput() {
    return cy.get(`[data-cy=check-number-input]`);
  }
}
export default new Billing();
