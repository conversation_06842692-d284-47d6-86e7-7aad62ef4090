import navigation from './navigation';
import { PORTFOLIO_SELECTORS } from '../support/constants/portfolio';

class PersonPortfolio {
  // Portfolio generate form elements
  public get generatePortfolioTitle() {
    return cy.contains('Generate Portfolio');
  }

  public get dateRangeInput() {
    return cy.get(PORTFOLIO_SELECTORS.DATE_RANGE_INPUT);
  }

  public get ageGroupFilter() {
    return cy.get(PORTFOLIO_SELECTORS.AGE_GROUP_FILTER);
  }

  public get generateButton() {
    return cy.get(PORTFOLIO_SELECTORS.GENERATE_BUTTON);
  }

  public get dateRangeApplyButton() {
    // Target only the visible date range picker's apply button
    return cy.get('.daterangepicker:visible .applyBtn');
  }

  // Portfolio content elements
  public get portfolioTitle() {
    return cy.contains(PORTFOLIO_SELECTORS.PORTFOLIO_TITLE);
  }

  public get milestoneHighlights() {
    return cy.contains('Milestone Highlights');
  }

  public get adjustMeasurementButtons() {
    return cy.get('.adjust-measurement');
  }

  public get portfolioBackgroundColors() {
    return cy.get('.portfolio-bg-color');
  }

  // Modal elements
  public get adjustObservationTitle() {
    return cy.contains('Adjust Observation');
  }

  public get categoryLabel() {
    return cy.get(PORTFOLIO_SELECTORS.CATEGORY_LABEL);
  }

  public get benchmarkLabel() {
    return cy.get(PORTFOLIO_SELECTORS.BENCHMARK_LABEL);
  }

  public get currentMeasurementLabel() {
    return cy.get(PORTFOLIO_SELECTORS.CURRENT_MEASUREMENT_LABEL);
  }

  public get adjustedMeasurementLabel() {
    return cy.get(PORTFOLIO_SELECTORS.ADJUSTED_MEASUREMENT_LABEL);
  }

  public get adjustedMeasurementSelect() {
    return cy.get('[name="adjusted-measurement"]');
  }

  public get adjustmentNotesInput() {
    return cy.get('[name="adjustment-notes"]');
  }

  public get sourceDocumentSelect() {
    return cy.get('[name="source-document"]');
  }

  public get saveButton() {
    return cy.get(PORTFOLIO_SELECTORS.SAVE_BUTTON);
  }

  public get closeModalButton() {
    return cy.get('[data-cy="close-modal"]');
  }

  public get cancelButton() {
    return cy.get(PORTFOLIO_SELECTORS.CANCEL_BUTTON);
  }

  // Methods
  /**
   * Navigate to a person's portfolio page
   * @param personId - The ID of the person whose portfolio to visit
   */
  public navigateToPortfolio(personId: string) {
    cy.visit(`/people/${personId}/portfolio`);
    // Wait for the generate portfolio form with a generous timeout
    cy.contains('Generate Portfolio', { timeout: 15000 }).should('be.visible');
    navigation.loadingSpinner.should('not.exist');
    return this;
  }

  /**
   * Generate a portfolio with the given date range and age group
   * @param dateRange - The date range in format MM/DD/YYYY - MM/DD/YYYY
   * @param ageGroupIndex - The index of the age group to select (defaults to 1)
   */
  public generatePortfolio(dateRange: string, ageGroupIndex: number = 1) {
    cy.intercept('**/generatePortfolio**').as('portfolioData');
    
    this.dateRangeInput.should('be.visible')
      .clear()
      .type(dateRange, { delay: 100 });
    
    cy.get('.daterangepicker').should('be.visible');
    cy.get('.daterangepicker:visible .applyBtn').first().click();
    
    try {
      this.ageGroupFilter.select(ageGroupIndex);
    } catch (e) {
      cy.log('Could not select age group, continuing with default value');
    }
    
    this.generateButton.should('be.visible').click();
    navigation.loadingSpinner.should('not.exist');
    this.portfolioTitle.should('be.visible', { timeout: 10000 });
    
    return this;
  }

  /**
   * Complete end-to-end flow: navigate to portfolio and generate it
   * @param personId - The ID of the person whose portfolio to visit
   * @param dateRange - The date range in format MM/DD/YYYY - MM/DD/YYYY
   * @param ageGroupIndex - The index of the age group to select (defaults to 1)
   */
  public navigateAndGeneratePortfolio(personId: string, dateRange: string, ageGroupIndex: number = 1) {
    this.navigateToPortfolio(personId);
    this.generatePortfolio(dateRange, ageGroupIndex);
    return this;
  }

  /**
   * Access portfolio via dropdown menu from person profile
   * @param firstName - The first name of the person (for targeted selection)
   */
  public accessPortfolioViaDropdown(firstName?: string) {
    // First try to use the data-cy attribute
    cy.get('[data-cy="dropdown-menu"]').then($menu => {
      if ($menu.length > 0) {
        // Click the toggle within the specific dropdown with data-cy
        cy.get('[data-cy="dropdown-menu"] [data-toggle="dropdown"]').click({ force: true });
        
        // Look for portfolio option in this specific dropdown
        cy.get('[data-cy="dropdown-menu"] .dropdown-menu').should('be.visible');
      } else if (firstName) {
        // If we have a first name, try to find the person's card
        cy.contains(firstName).closest('.card-body').within(() => {
          cy.get('[data-toggle="dropdown"]').click({ force: true });
        });
      } else {
        // Last resort - try the first dropdown
        cy.get('[data-toggle="dropdown"]').first().click({ force: true });
      }
    });
    
    // Wait for any dropdown menu to be visible with the show class
    cy.get('.dropdown-menu.show').should('be.visible');
    
    // Check if portfolio option exists
    cy.get('.dropdown-menu.show').then($menu => {
      if ($menu.find('#btnViewPortfolio').length > 0) {
        cy.get('#btnViewPortfolio').click();
        cy.contains('Generate Portfolio', { timeout: 15000 }).should('be.visible');
      } else {
        throw new Error("Portfolio option not available in dropdown menu");
      }
    });
    
    return this;
  }

  public openAdjustmentModal() {
    // Add a wait before clicking to ensure page is stabilized
    cy.wait(1000);
    this.adjustMeasurementButtons.first().should('be.visible').click({ force: true });
    this.adjustObservationTitle.should('be.visible', { timeout: 10000 });
    return this;
  }

  public makeAdjustment(measurementIndex: number, notes: string) {
    // Set up intercept before adjustments
    cy.intercept('**/adjustAssessment**').as('adjustAssessment');
    
    this.adjustedMeasurementSelect.should('be.visible').select(measurementIndex);
    this.adjustmentNotesInput.should('be.visible').type(notes);
    return this;
  }

  public saveAdjustment() {
    this.saveButton.should('be.visible').click();
    cy.wait('@adjustAssessment', { timeout: 15000 });
    return this;
  }

  public closeModal() {
    this.closeModalButton.should('be.visible').click();
    this.adjustObservationTitle.should('not.exist');
    return this;
  }

  public completeSocialStudiesInteraction() {
    cy.get(PORTFOLIO_SELECTORS.SOCIAL_STUDIES_CONTAINER).click();
    cy.get(PORTFOLIO_SELECTORS.MODAL_HEADER).click();
    cy.get(PORTFOLIO_SELECTORS.MODAL_BODY).click();

    this.categoryLabel.should('be.visible');
    this.benchmarkLabel.should('be.visible');
    this.currentMeasurementLabel.should('be.visible');
    this.adjustedMeasurementLabel.should('be.visible');
    
    this.saveButton.should('be.visible');
    this.cancelButton.should('be.visible');

    this.cancelButton.click();
    return this;
  }
}

export default new PersonPortfolio(); 