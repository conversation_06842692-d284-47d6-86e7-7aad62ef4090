class Dashboard {
  public get expressDriveUp() {
    return cy.get('[data-cy=express-drive-up]');
  }

  public get expressDriveUpViewAllButton() {
    return cy.get('[data-cy=express-drive-up-view-all-button]');
  }

  public get checkInOutButton() {
    return cy.get('[data-cy=check-in-out-btn]');
  }

  public get checkInButton() {
    return cy.get('[data-cy=check-in-button]');
  }

  public peopleExpressDriveUp(personId: string) {
    return cy.get(`[data-cy=people-express-drive-up-item-${personId}]`);
  }

  public get latestActivitySection() {
    return cy.get('[data-cy=latest-activity]');
  }

  public get pastDueAccountSection() {
    return cy.get('[data-cy=past-due-account]'); 
  }

  public get checkInRatiosSection() {
    return cy.get('[data-cy=check-in-ratios]');
  }

  public get activationsPie<PERSON>hart() {
    return cy.get('[data-cy=activations-pie-chart]');
  }

  public get documentsOustandingWidget() {
    return cy.get('[data-cy=documents-outstanding-widget]');
  }

  public get mediaRequirementsSection() {
    return cy.get('[data-cy=media-requirements]');
  }

  public get thisWeeksSchedule() {
    return cy.get('[data-cy=this-weeks-schedule]');
  }

  public get legacyThisWeeksSchedule() {
    return cy.get('[data-cy=this-weeks-schedule-legacy]');
  }

  public listItemLabel(personId: string) {
    return cy.get(`[data-cy=dashboard-list-data-item-label-${personId}]`);
  }

  public listItemValue(personId: string) {
    return cy.get(`[data-cy=dashboard-list-data-item-value-${personId}]`);
  }

  public get dashboardItemLabel() {
    return cy.get('[data-cy=dashboard-list-data-item-label-]');
  }

  public get dashboardItemValue() {
    return cy.get('[data-cy=dashboard-list-data-item-value-]');
  }

  public get organizationName() {
    return cy.get('[data-cy=organization-name]');
  }

  public get mySiteHeader() {
    return cy.get('[data-cy=my-site-header]');
  }

  public get overviewHeader() {
    return cy.get('[data-cy=overview-header]');
  }

  public get mediaReviewHeader() {
    return cy.get('[data-cy=media-review-header]');
  }

  public get mediaGalleryHeader() {
    return cy.get('[data-cy=media-gallery-header]');
  }

  public get expressDriveUpHeader() {
    return cy.get('[data-cy=express-drive-up-header]');
  }

  public get dataExplorerHeader() {
    return cy.get('[data-cy=data-explorer-header]');
  }

  public get childDocumentItem() {
    return cy.get('[data-cy=child-document-item]');
  }

  public get childNameItem() {
    return cy.get('[data-cy=child-name-item]');
  }
}

export default new Dashboard();
