class MessageCenter {
  public get btnMessageBack() {
    return cy.get(`[data-cy=btn-messages-back]`);
  }

  public get btnArchiveMessage() {
    return cy.get(`[data-cy=btn-archive-message]`);
  }

  public get messageTitleOpen() {
    return cy.get(`[data-cy=message-title-open]`);
  }

  public get messageSubtitleOpen() {
    return cy.get(`[data-cy=message-subtitle-open]`);
  }
  public get senderName() {
    return cy.get(`[data-cy=sender-name]`);
  }

  public get messageDate() {
    return cy.get(`[data-cy=message-date]`);
  }

  public get fullMessage() {
    return cy.get(`[data-cy=full-message]`);
  }

  public get replyMessage() {
    return cy.get(`[data-cy=reply-message]`);
  }

  public get sendReplyBtn() {
    return cy.get(`[data-cy=send-reply-btn]`);
  }

  public get selectAllMessages() {
    return cy.get(`[data-cy=select-all-messages]`);
  }

  public get archiveAllMessages() {
    return cy.get(`[data-cy=archive-all-messages]`);
  }

  public get searchTerm() {
    return cy.get(`[data-cy=search-term]`);
  }

  public get recordsPerPage() {
    return cy.get(`[data-cy=records-per-page]`);
  }

  public get previousPage() {
    return cy.get(`[data-cy=previous-page]`);
  }

  public get nextPage() {
    return cy.get(`[data-cy=next-page]`);
  }

  public get messageCheckbox() {
    return cy.get(`[data-cy=message-checkbox]`);
  }

  public get initials() {
    return cy.get(`[data-cy=initials]`);
  }

  public get messagePerson() {
    return cy.get(`[data-cy=message-person]`);
  }

  public get messageTitle() {
    return cy.get(`[data-cy=message-title]`);
  }

  public get messageSummary() {
    return cy.get(`[data-cy=message-summary]`);
  }

  public get messageTime() {
    return cy.get(`[data-cy=message-time]`);
  }

  public get newMessageBtn() {
    return cy.get(`[data-cy=new-message-btn]`);
  }

  public get inboxSection() {
    return cy.get(`[data-cy=inbox-section]`);
  }

  public get allMailSection() {
    return cy.get(`[data-cy=all-mail-section]`);
  }

  public get sentSection() {
    return cy.get(`[data-cy=sent-section]`);
  }

  public get adminViewSection() {
    return cy.get(`[data-cy=admin-view-section]`);
  }

  public get expandModal() {
    return cy.get(`[data-cy=expand-modal]`);
  }

  public get closeModal() {
    return cy.get(`[data-cy=close-modal]`);
  }

  public get recipientList() {
    return cy.get(`[data-cy=recipient-list]`);
  }

  public get recipientSelector() {
    return cy.get(`[data-cy=recipient-selector]`);
  }

  public get messageSubject() {
    return cy.get(`[data-cy=message-subject]`);
  }

  public get messageBody() {
    return cy.get(`[data-cy=message-body]`);
  }

  public get sendMessageBtn() {
    return cy.get(`[data-cy=send-message-btn]`);
  }
}

export default new MessageCenter();
