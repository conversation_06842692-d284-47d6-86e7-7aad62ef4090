class Activities {
  public get allActivitiesHeader() {
    return cy.get(`[data-cy=all-activities-header]`);
  }

  public get allThemesHeader() {
    return cy.get(`[data-cy=all-themes-header]`);
  }

  public get scheduledActivitiesHeader() {
    return cy.get(`[data-cy=scheduled-activities-header]`);
  }

  public get configureHeader() {
    return cy.get(`[data-cy=configure-header]`);
  }

  public get paginationPreviousButton() {
    return cy.get(`[data-cy=pagination-previous-btn]`);
  }

  public get paginationNextButton() {
    return cy.get(`[data-cy=pagination-next-btn]`);
  }

  public get paginationRecordCount() {
    return cy.get(`[data-cy=pagination-records-count]`);
  }

  public get filterPublishedCheckbox() {
    return cy.get(`[data-cy=filter-publish-chkbox]`);
  }

  public get filterDraftCheckbox() {
    return cy.get(`[data-cy=filter-draft-chkbox]`);
  }

  public get filterApprovalCheckbox() {
    return cy.get(`[data-cy=filter-approval-chkbox]`);
  }

  public get createNewActivityBtn() {
    return cy.get(`[data-cy=create-new-activity-btn]`, { timeout: 60000 });
  }

  public get searchActivityBtn() {
    return cy.get(`[data-cy=search-activity-btn]`);
  }

  public get inputActivitySearch() {
    return cy.get(`[data-cy=input-activity-search]`);
  }

  public get ageActivitySelector() {
    return cy.get(`[data-cy=age-activity-selector]`);
  }

  public get activityTitle() {
    return cy.get(`[data-cy=activity-title]`);
  }

  public get activityAgeGroup() {
    return cy.get(`[data-cy=activity-age-group]`);
  }

  public get activitySource() {
    return cy.get(`[data-cy=activity-source]`);
  }

  public get createdByName() {
    return cy.get(`[data-cy=created-by-name]`);
  }

  public get createdByOrg() {
    return cy.get(`[data-cy=created-by-org]`);
  }

  public get modifiedByName() {
    return cy.get(`[data-cy=modified-by-name]`);
  }

  public get modifiedByOrg() {
    return cy.get(`[data-cy=modified-by-org]`);
  }

  public get updateRequestedByName() {
    return cy.get(`[data-cy=update-requested-by-name]`);
  }

  public get updateRequestedByOrg() {
    return cy.get(`[data-cy=update-requested-by-org]`);
  }

  public get approvedByName() {
    return cy.get(`[data-cy=approved-by-name]`);
  }

  public get approvedByOrg() {
    return cy.get(`[data-cy=approved-by-org]`);
  }

  public get dropdownActivityOptions() {
    return cy.get(`[data-cy=dropdown-activity-options]`);
  }

  public get reviewActivity() {
    return cy.get(`[data-cy=review-activity]`);
  }

  public get lockedActivity() {
    return cy.get(`[data-cy=locked-activity]`);
  }

  public get editActivity() {
    return cy.get(`[data-cy=edit-activity]`);
  }

  public get publishActivity() {
    return cy.get(`[data-cy=publish-activity]`);
  }

  public get unpublishActivity() {
    return cy.get(`[data-cy=unpublish-activity]`);
  }

  public get deleteActivity() {
    return cy.get(`[data-cy=delete-activity]`);
  }

  public get approveReviewBtn() {
    return cy.get(`[data-cy=approve-review-btn]`);
  }

  public get rejectReviewBtn() {
    return cy.get(`[data-cy=reject-review-btn]`);
  }

  public get editUpdateReviewBtn() {
    return cy.get(`[data-cy=edit-update-review-btn]`);
  }

  public get createNewThemeBtn() {
    return cy.get(`[data-cy=create-new-theme-btn]`, { timeout: 60000 });
  }

  public get searchThemeBtn() {
    return cy.get(`[data-cy=search-theme-btn]`);
  }

  public get inputThemeSearch() {
    return cy.get(`[data-cy=input-theme-search]`, { timeout: 2000 });
  }

  public get themeTitle() {
    return cy.get(`[data-cy=theme-title]`);
  }

  public get themeCreatedByName() {
    return cy.get(`[data-cy=theme-created-by-name]`);
  }

  public get themeCreatedByOrg() {
    return cy.get(`[data-cy=theme-created-by-org]`);
  }

  public get themeModifiedByName() {
    return cy.get(`[data-cy=theme-modified-by-name]`);
  }

  public get themeModifiedByOrg() {
    return cy.get(`[data-cy=theme-modified-by-org]`);
  }

  public get themeUpdateRequestedByName() {
    return cy.get(`[data-cy=theme-update-requested-by-name]`);
  }

  public get themeUpdateRequestedByOrg() {
    return cy.get(`[data-cy=theme-update-requested-by-org]`);
  }

  public get themeApprovedByName() {
    return cy.get(`[data-cy=theme-approved-by-name]`);
  }

  public get themeApprovedByOrg() {
    return cy.get(`[data-cy=theme-approved-by-org]`);
  }

  public get dropdownThemeOptions() {
    return cy.get(`[data-cy=dropdown-theme-options]`);
  }

  public get reviewTheme() {
    return cy.get(`[data-cy=review-theme]`);
  }

  public get lockedTheme() {
    return cy.get(`[data-cy=locked-theme]`);
  }

  public get editTheme() {
    return cy.get(`[data-cy=edit-theme]`);
  }

  public get publishTheme() {
    return cy.get(`[data-cy=publish-theme]`);
  }

  public get unpublishTheme() {
    return cy.get(`[data-cy=unpublish-theme]`);
  }

  public get copyToNewTheme() {
    return cy.get(`[data-cy=copy-to-new-theme]`);
  }

  public get deleteTheme() {
    return cy.get(`[data-cy=delete-theme]`);
  }

  public get saveTheme() {
    return cy.get(`[data-cy=save-theme]`);
  }

  public get closeTheme() {
    return cy.get(`[data-cy=close-theme]`);
  }

  public get themeApproveReviewBtn() {
    return cy.get(`[data-cy=theme-approve-review-btn]`);
  }

  public get themeRejectReviewBtn() {
    return cy.get(`[data-cy=theme-reject-review-btn]`);
  }

  public get scheduledActivitiesBtn() {
    return cy.get(`[data-cy=scheduled-activities-btn]`, { timeout: 1000 });
  }

  public get scheduledActivitiesGroupFilter() {
    return cy.get(`[data-cy=scheduled-activities-group-filter]`);
  }

  public get startDateActivities() {
    return cy.get(`[data-cy=start-date-activities]`);
  }

  public get endDateActivities() {
    return cy.get(`[data-cy=end-date-activities]`);
  }

  public get searchTextActivities() {
    return cy.get(`[data-cy=search-text-activities]`);
  }

  public get groupByDates() {
    return cy.get(`[data-cy=group-by-date]`);
  }

  public get groupByThemes() {
    return cy.get(`[data-cy=group-by-theme]`);
  }

  public get themeName() {
    return cy.get(`[data-cy=theme-name]`);
  }

  public get descriptionTheme() {
    return cy.get(`[data-cy=description-theme]`);
  }

  public get selectGroups() {
    return cy.get(`[data-cy=select-groups]`);
  }

  public get btnCreateTheme() {
    return cy.get(`[data-cy=btn-create-theme]`);
  }

  public get btnUpdateTheme() {
    return cy.get(`[data-cy=btn-update-theme]`);
  }

  public get btnDeleteTheme() {
    return cy.get(`[data-cy=btn-delete-theme]`);
  }

  public get dropdownScheduledDay() {
    return cy.get(`[data-cy=dropdown-scheduled-day]`, { timeout: 10000 });
  }

  public get optionMove() {
    return cy.get(`[data-cy=option-move]`);
  }

  public get optionEdit() {
    return cy.get(`[data-cy=option-edit]`);
  }

  public get optionDelete() {
    return cy.get(`[data-cy=option-delete]`);
  }

  public get scheduledThemeName() {
    return cy.get(`[data-cy=scheduled-theme-name]`);
  }

  public get scheduledDate() {
    return cy.get(`[data-cy=scheduled-date]`);
  }

  public get recipients() {
    return cy.get(`[data-cy=recipients]`);
  }

  public get scheduledActivitiesDropdown() {
    return cy.get(`[data-cy=scheduled-activities-dropdown]`);
  }

  public get scheduledNameOfTheme() {
    return cy.get(`[data-cy=scheduled-name-of-theme]`);
  }

  public get manageTheme() {
    return cy.get(`[data-cy=manage-theme]`);
  }

  public get themeDate() {
    return cy.get(`[data-cy=theme-date]`);
  }

  public get removeDay() {
    return cy.get(`[data-cy=remove-day]`);
  }

  public get activityScheduledName() {
    return cy.get(`[data-cy=activity-scheduled-name]`, { timeout: 1000 });
  }

  public get activityHomework() {
    return cy.get(`[data-cy=activivy-homework]`);
  }

  public get activityMaterials() {
    return cy.get(`[data-cy=activity-materials]`);
  }

  public get activityTeacherNotes() {
    return cy.get(`[data-cy=activity-teacher-notes]`);
  }

  public get activityInternalNotes() {
    return cy.get(`[data-cy=activity-internal-notes]`);
  }

  public get activityInternalLink() {
    return cy.get(`[data-cy=activity-internal-link]`);
  }

  public get addActivityNew() {
    return cy.get(`[data-cy=add-activity-new]`);
  }

  public get addDay() {
    return cy.get(`[data-cy=add-day]`);
  }

  public get addSelectedDays() {
    return cy.get(`[data-cy=btn-add-selected-days]`);
  }

  public get addNextWeekday() {
    return cy.get(`[data-cy=add-next-weekday]`);
  }

  public get copyExistingActivity() {
    return cy.get(`[data-cy=select-copy-existing-activity]`);
  }

  public get copyExistingActivityBtn() {
    return cy.get(`[data-cy=copy-btn]`);
  }

  public get curriculumBankActivityFormSave() {
    return cy.get(`[data-cy=curriculum-bank-activity-form-save]`);
  }

  public get curriculumBankActivityFormClose() {
    return cy.get(`[data-cy=curriculum-bank-activity-form-close]`);
  }

  public get curriculumTags() {
    return cy.get(`[data-cy=curriculum-tags]`);
  }

  public get addRootBtn() {
    return cy.get(`[data-cy=add-root-btn]`);
  }

  public get addChildBtn() {
    return cy.get(`[data-cy=add-child-btn]`);
  }

  public get renameTagBtn() {
    return cy.get(`[data-cy=rename-btn]`);
  }

  public get deleteTagBtn() {
    return cy.get(`[data-cy=delete-btn]`);
  }

  public get availableStandardsTab() {
    return cy.get(`[data-cy=available-standards-tab]`);
  }

  public get standardsTable() {
    return cy.get(`[data-cy=standards-table]`);
  }

  public get standardId() {
    return cy.get(`[data-cy=standard-id]`);
  }

  public get standardBenchmark() {
    return cy.get(`[data-cy=standard-benchmark]`);
  }

  public get standardDetail() {
    return cy.get(`[data-cy=standard-detail]`);
  }

  public get themeContainer() {
    return cy.get(`[data-cy=theme-container]`);
  }

  public get themeNameInput() {
    return cy.get(`[data-cy=theme-name-input]`, { timeout: 60000 });
  }

  public get themeDescriptionInput() {
    return cy.get(`[data-cy=theme-description-input]`);
  }

  public get saveThemeBank() {
    return cy.get(`[data-cy=save-theme-bank]`);
  }

  public get addActivityTheme() {
    return cy.get(`[data-cy=add-activity-theme]`);
  }

  public get existingActivities() {
    return cy.get(`[data-cy=existing-activities]`);
  }

  public get copyActivityBtn() {
    return cy.get(`[data-cy=copy-activity-btn]`);
  }

  public get addDaysBtn() {
    return cy.get(`[data-cy=add-days-btn]`);
  }

  public get themeItem() {
    return cy.get(`[data-cy=theme-item]`);
  }

  public get addActivity() {
    return cy.get(`[data-cy=add-activity-btn]`);
  }

  public get curriculumTitle() {
    return cy.get(`[data-cy=curriculum-title]`);
  }

  public get curriculumDescription() {
    return cy.get(`[data-cy=curriculum-description]`);
  }

  public get curriculumMaterials() {
    return cy.get(`[data-cy=curriculum-materials]`);
  }

  public get curriculumHomework() {
    return cy.get(`[data-cy=curriculum-homework]`);
  }

  public get curriculumInternalNotes() {
    return cy.get(`[data-cy=curriculum-internal-notes]`);
  }

  public get curriculumInternalLink() {
    return cy.get(`[data-cy=curriculum-internal-link]`);
  }

  public get scheduleToday() {
    return cy.get(`[data-cy=schedule-today]`);
  }

  public get scheduleFuture() {
    return cy.get(`[data-cy=schedule-future]`);
  }

  public get scheduleDatePicker() {
    return cy.get(`[data-cy=schedule-date-picker]`);
  }

  public get sendToAllGroups() {
    return cy.get(`[data-cy=send-to-all-groups]`);
  }

  public get sendToSelectedGroups() {
    return cy.get(`[data-cy=send-to-selected-groups]`);
  }

  public get selectedGroupsDropdown() {
    return cy.get(`[data-cy=selected-groups-dropdown]`);
  }

  public get attachMediaButton() {
    return cy.get(`[data-cy=attach-media-btn]`);
  }

  public get attachMediaInput() {
    return cy.get(`[data-cy=attach-media-input]`);
  }

  public get mediaFileLabel() {
    return cy.get(`[data-cy=media-file-label]`);
  }

  public get saveActivity() {
    return cy.get(`[data-cy=save-activity]`);
  }
}

export default new Activities();
