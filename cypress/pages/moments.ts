class Moments {
  public get momentFilesInput() {
    return cy.get("[data-cy='moment-input-files']");
  }

  public get saveMomentButton() {
    return cy.get("[data-cy='save-moment-btn']", { timeout: 5000 });
  }

  public get closeMomentButton() {
    return cy.get("[data-cy='close-moment-btn']");
  }

  public get usersTagListInput() {
    return cy.get("[data-cy='tag-users-list']");
  }

  public get tagPeopleInputLabel() {
    return cy.get("[data-cy='tag-people-input']");
  }

  public get tagPeopleDiv() {
    return cy.get("[data-cy='tag-people']", { timeout: 2000 });
  }

  public get momentType() {
    return cy.get("[data-cy='moment-type-selection']", { timeout: 5000 });
  }

  public get momentDetails() {
    return cy.get("[data-cy='moment-details']");
  }

  public get momentDateTimePicker() {
    return cy.get("[data-cy='moment-date-time-picker']");
  }

  public get momentDatePicker() {
    return cy.get("[data-cy='moment-date-picker']");
  }

  public get closeMomentModal() {
    return cy.get("[data-cy='close-modal']", { timeout: 5000 });
  }

  public get sleepStartTime() {
    return cy.get("[data-cy='sleep-start-time']");
  }

  public get sleepEndTime() {
    return cy.get("[data-cy='sleep-end-time']");
  }

  public get sleepEndTimeBtn() {
    return cy.get("[data-cy='sleep-end-time-btn']");
  }

  public get personInitialsMoment() {
    return cy.get("[data-cy='person-initials-moment']");
  }

  public get momentTypeName() {
    return cy.get("[data-cy='moment-type-name']");
  }

  public get postedByMoment() {
    return cy.get("[data-cy='posted-by-moment']");
  }

  public get momentDateTime() {
    return cy.get("[data-cy='moment-date-time']");
  }

  public get momentDescription() {
    return cy.get("[data-cy='moment-description']");
  }

  public get foodMomentDinner() {
    return cy.get("[data-cy='food-moment-dinner']");
  }

  public get foodMomentBreakfast() {
    return cy.get("[data-cy='food-moment-breakfast']");
  }

  public get foodMomentAmSnack() {
    return cy.get("[data-cy='food-moment-am-snack']");
  }

  public get foodMomentPmSnack() {
    return cy.get("[data-cy='food-moment-pm-snack']");
  }

  public get foodMomentLunch() {
    return cy.get("[data-cy='food-moment-lunch']");
  }

  public get foodMomentLateSnack() {
    return cy.get("[data-cy='food-moment-late-snack']");
  }

  public get foodMomentAll() {
    return cy.get("[data-cy='food-moment-all']");
  }

  public get foodMomentMost() {
    return cy.get("[data-cy='food-moment-most']");
  }

  public get foodMomentSome() {
    return cy.get("[data-cy='food-moment-some']");
  }

  public get foodMomentNone() {
    return cy.get("[data-cy='food-moment-none']");
  }

  public get foodMomentAllItems() {
    return cy.get("[data-cy='food-moment-all-items']");
  }

  public get foodMomentMostItems() {
    return cy.get("[data-cy='food-moment-Most-items']");
  }

  public get foodMomentSomeItems() {
    return cy.get("[data-cy='food-moment-Some-items']");
  }

  public get foodMomentNoneItems() {
    return cy.get("[data-cy='food-moment-None-items']");
  }

  public get foodMomentNotOffered() {
    return cy.get("[data-cy='food-moment-not-offered']");
  }

  public get selectActivityMoment() {
    return cy.get("[data-cy='select-activity-moment']");
  }

  public get momentComment() {
    return cy.get("[data-cy='moment-comment']");
  }

  public get didNotSleepCheck() {
    return cy.get("[data-cy='did-not-sleep-btn']");
  }

  public get selectActivityPortfolio() {
    return cy.get("[data-cy='select-activity-portfolio']");
  }

  public get selectAgeRange() {
    return cy.get("[data-cy='select-age-range']");
  }

  public get showAllStandardsBtn() {
    return cy.get("[data-cy='show-all-standards-btn']");
  }

  public get selectStandards() {
    return cy.get("[data-cy='select-standards']");
  }

  public get assesmentBtn() {
    return cy.get("[data-cy='assessment-btn']");
  }

  public get addBtn() {
    return cy.get("[data-cy='add-btn']");
  }

  public get selectActivityLearning() {
    return cy.get("[data-cy='select-activity-learning']");
  }

  public get selectLearningType() {
    return cy.get("[data-cy='select-learning-type']");
  }

  public get selectMedicineType() {
    return cy.get("[data-cy='select-medicine-type']");
  }

  public get administeredBy() {
    return cy.get("[data-cy='administered-by']");
  }

  public get incidentNatureInput() {
    return cy.get("[data-cy='incident-nature-input']");
  }

  public get actionTakenInput() {
    return cy.get("[data-cy='action-taken-input']");
  }

  public get incidentLocationInput() {
    return cy.get("[data-cy='incident-location-input']");
  }

  public get momentListDropdown() {
    return cy.get("[data-cy='moment-list-dropdown']");
  }

  public get modifyMoment() {
    return cy.get("[data-cy='modify-moment']");
  }

  public get deleteMoment() {
    return cy.get("[data-cy='delete-moment']");
  }

  public get viewMomentData() {
    return cy.get("[data-cy='view-moment-data']");
  }

  public get peopleDescriptionMoment() {
    return cy.get("[data-cy='people-description-moment']");
  }
}

export default new Moments();
