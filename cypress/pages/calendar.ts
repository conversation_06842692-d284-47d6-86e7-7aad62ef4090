class Calendar {
  public get calendarView() {
    return cy.get('[data-cy="calendar-view"]');
  }

  public get calendarFilterGroup() {
    return cy.get('[data-cy="calendar-filter-group"]');
  }

  public get filteReservationSort() {
    return cy.get('[data-cy="filter-reservation-sort"]');
  }

  public get calendarPrevious() {
    return cy.get('[data-cy="calendar-previous"]');
  }

  public get calendarNext() {
    return cy.get('[data-cy="calendar-next"]');
  }

  public get calendarBtn() {
    return cy.get('[data-cy="calendar-btn"]');
  }

  public get calendarDate() {
    return cy.get('[data-cy="calendar-date"]');
  }

  public get calendarToday() {
    return cy.get('[data-cy="calendar-today"]');
  }

  public get calendarTodayGroup() {
    return cy.get('[data-cy="calendar-group"]');
  }

  public get calendarTodayGroupName() {
    return cy.get('[data-cy="calendar-group-${group-name}"]');
  }

  public get calendarWeekGroup() {
    return cy.get('[data-cy="calendar-group-${group-name}"]');
  }

  public get calendarDetailModal() {
    return cy.get('[data-cy="calendar-detail-modal"]');
  }

  public get detailTarget() {
    return cy.get('[data-cy="detail-target"]');
  }

  public get detailModalClose() {
    return cy.get('[data-cy="detail-close"]');
  }

  public get schedulingEventStats() {
    return cy.get('[data-cy="event-stats"]');
  }

  public get schedulingEventStat() {
    return cy.get('[data-cy="event-stat"]');
  }

  public get schedulingEventValue() {
    return cy.get('[data-cy="event-stat-value"]');
  }

  public get schedulingEventName() {
    return cy.get('[data-cy="event-stat-name"]');
  }

  public get schedulingExpandBtn() {
    return cy.get('[data-cy="item-expand-btn"]');
  }

  public get calendarGridCol() {
    return cy.get('[data-cy="calendar-grid-col"]');
  }

  public get calendarPrintPdfBtn() {
    return cy.get('[data-cy="print-pdf-btn"]');
  }

  public get calendarDayItem() {
    return cy.get('[data-cy="calendar-day-item"]');
  }

  public get calendarItemTitle() {
    return cy.get('[data-cy="title"]');
  }

  public get calendarWeekItem() {
    return cy.get('[data-cy="calendar-week-item"]');
  }

  public get calendarMonthItem() {
    return cy.get('[data-cy="calendar-month-item"]');
  }

  public get calendarItem() {
    return cy.get('[data-cy="calendar-item"]');
  }

  public get btnPrintMenu() {
    return cy.get('[data-cy="btn-print-menu"]');
  }

  public get calendarDetailTable() {
    return cy.get('[data-cy="calendar-detail-table"]');
  }

  public get calendarDetailTitle() {
    return cy.get('[data-cy="modal-detail-title"]');
  }

  public get detailGoToBtn() {
    return cy.get('[data-cy="detail-go-to-btn"]');
  }
}
export default new Calendar();
