class MediaGallery {
  public get mediaGalleryPageLabel() {
    return cy.get('[data-cy=media-gallery-page-label]');
  }

  public get mediaGalleryStartDateLabel() {
    return cy.get('[data-cy=start-date-label]');
  }

  public get mediaGalleryStartDate() {
    return cy.get('[data-cy=start-date]');
  }

  public get mediaGalleryEndDateLabel() {
    return cy.get('[data-cy=end-date-label]');
  }

  public get mediaGalleryEndDate() {
    return cy.get('[data-cy=end-date]');
  }

  public get mediaGalleryTypeLabel() {
    return cy.get('[data-cy=media-type-label]');
  }

  public get mediaGalleryType() {
    return cy.get('[data-cy=select-media-type]');
  }

  public get mediaGalleryUpdateBtn() {
    return cy.get('[data-cy=update-btn]');
  }

  public get imageValidation() {
    return cy.get('[data-cy=img-validation]');
  }
}

export default new MediaGallery();
