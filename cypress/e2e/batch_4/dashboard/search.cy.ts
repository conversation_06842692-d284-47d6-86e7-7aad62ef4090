import search from 'cypress/pages/search';
import { SEARCH_DATA } from 'cypress/support/constants';

context('Search Content', () => {
  beforeEach(() => {
    cy.login();
  });

  it('should verify search content ', () => {
    search.searchPeopleIcon.click();

    search.searchPeopleInp.clear().type('chi', { timeout: 10000 });
    search.userPromptMessage.invoke('text').should('equal', SEARCH_DATA.USER_PROMPT);
    search.orgName.first().then(($org) => {
      expect(SEARCH_DATA.ORGANIZATIONS).to.include($org.text().trim());
    });

    search.entityType.each(($entity) => {
      expect(SEARCH_DATA.ENTITY_TYPES).to.include($entity.text().trim());
    });
    search.expandSearchLabel.should('be.visible');
    search.expandSearchLabel.click({ force: true });
    search.includeInactiveLabel.should('be.visible');
    search.includeInactiveLabel.click({ force: true });

    search.expandSearchLabel
      .then(($expandSearch) => {
        return $expandSearch.text().trim();
      })
      .should('equal', SEARCH_DATA.EXPAND_SEARCH_LABEL);

    search.includeInactiveLabel
      .then(($includeInactive) => {
        return $includeInactive.text().trim();
      })
      .should('equal', SEARCH_DATA.INCLUDE_INACTIVE_LABEL);

    search.name.then(($names) => {
      const personNames = $names.toArray().map((el) => el.textContent.trim());
      expect(hasNoDuplicates(personNames)).to.be.true;
    });
    search.searchPeopleInp.clear().type(SEARCH_DATA.EMAIL_SEARCH, { timeout: 10000 });
    search.name.should('have.text', SEARCH_DATA.NAME_LABEL);
  });
});

const hasNoDuplicates = (personNames: string[]): boolean => {
  const uniqueNames = new Set(personNames);
  return uniqueNames.size === personNames.length;
};
