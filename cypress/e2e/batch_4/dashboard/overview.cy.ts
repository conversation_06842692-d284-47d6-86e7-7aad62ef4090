import dashboard from 'cypress/pages/dashboard';
import navigation from 'cypress/pages/navigation';
import Overview from 'cypress/pages/overview';
import documents from 'cypress/pages/documents';
import header from 'cypress/pages/header';
import people from 'cypress/pages/people';
import Relationships from 'cypress/pages/relationships';
import {
  AXIS_VALUES_REVENUE,
  BAR_VALUES_REVENUE,
  FTE_GROUPS,
  GREETING_DATA,
  WIDGET_OVERVIEW,
  BASE_URLS,
  PARENT_DOCUMENTS_DATA,
  PARENT_LOGIN_DATA,
  PARENT_OVERVIEW_DATA
} from 'cypress/support/constants';
import 'cypress-real-events';

context('Dashboard -> Overview ', () => {
  it('Verify overview page details and widgets', () => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.OVERVIEW);

    greetingValidation();
    widgetNameValuesValidation();
    widgetRevenuePerformance();
    widgetAmountInvoiced();
    widgetAllOustandingInvoices();
    cy.get('.apexcharts-svg').eq(1).should('exist');
    cy.get('.apexcharts-svg').eq(2).should('exist');
    cy.get('.apexcharts-svg').eq(3).should('exist');
    // commenting below tests as on hover done manually
    // during simulation of test gets passed.
    // widgetAverageMomentsPerTeacher();
    // widgetMonthlyActiveUsers();
    // widgetDailySheetSaved();
    widgetFTEQuickView();
    cy.visit(BASE_URLS.PEOPLE_DIRECTORY);
    navigation.loadingSpinner.should('not.exist');
    addChildren();
    addDocuments();
    cy.performLogout();
  });

  it('Verify parent account', () => {
    cy.login(BASE_URLS.DEFAULT, 'Parent');
    checkDocumentAndChild();
    cy.performLogout();
  });

  it('Header updating while navigating application', () => {
    cy.login(BASE_URLS.DEFAULT, 'Staff');
    navigation.contentNav.click();
    navigation.activitiesNav.click();
    navigation.allActivitiesHeaderNavMenu.should('be.visible');
    navigation.allThemesHeaderNavMenu.should('be.visible');
    navigation.scheduledActivitiesHeaderNavMenu.should('be.visible');
    navigation.manageNav.click();
    navigation.peopleNav.click();
    navigation.directoryHeaderNavMenu.should('be.visible');
    navigation.timeNav.click();
    navigation.schedulingTabOption.click();
    navigation.calendarTabHeaderNav.should('be.visible');
    navigation.listTabHeaderNav.should('be.visible');
    navigation.staffTabHeaderNav.should('be.visible');
  });
});

const greetingValidation = () => {
  Overview.greetingMessage.invoke('text').then((greetingMessage) => {
    const isMessageValid = GREETING_DATA.TYPE.includes(greetingMessage);
    expect(isMessageValid).to.be.true;
  });
  Overview.orgName.should('include.text', 'MikeCare');
};

const widgetNameValuesValidation = () => {
  for (let n = 0; n < 7; n++) {
    Overview.widgetDetails.eq(n).should('include.text', WIDGET_OVERVIEW[n].name);
    Overview.overviewValues.eq(n).should('contain', WIDGET_OVERVIEW[n].value);
  }
};

const widgetRevenuePerformance = () => {
  cy.get('.apexcharts-svg').eq(0).should('exist');
  for (let n = 0; n < 2; n++) {
    cy.get('.apexcharts-text.apexcharts-yaxis-label')
      .eq(n)
      .should('include.text', AXIS_VALUES_REVENUE.YAXIS.MONTHLY[n].value);
  }
  for (let n = 0; n < 4; n++) {
    cy.get('.apexcharts-text.apexcharts-xaxis-label')
      .eq(n)
      .should('include.text', AXIS_VALUES_REVENUE.XAXIS.MONTHLY[n].value);
  }
  for (let n = 0; n < 4; n++) {
    cy.get('.apexcharts-data-labels').eq(n).should('include.text', BAR_VALUES_REVENUE.MONTHLY[n].value);
  }
  Overview.viewDropdown.scrollIntoView().click();
  Overview.timeIntervals.contains('Weekly').click();
  Overview.overviewValues.eq(0).should('contain', '$600');
  for (let n = 0; n < 3; n++) {
    cy.get('.apexcharts-text.apexcharts-yaxis-label')
      .eq(n)
      .should('include.text', AXIS_VALUES_REVENUE.YAXIS.WEEKLY[n].value);
  }
  for (let n = 0; n < 4; n++) {
    cy.get('.apexcharts-text.apexcharts-xaxis-label')
      .eq(n)
      .should('include.text', AXIS_VALUES_REVENUE.XAXIS.WEEKLY[n].value);
  }
  for (let n = 0; n < 4; n++) {
    cy.get('.apexcharts-data-labels').eq(n).should('include.text', BAR_VALUES_REVENUE.WEEKLY[n].value);
  }
  Overview.viewDropdown.scrollIntoView().click();
  Overview.timeIntervals.contains('Yearly').click();
  Overview.overviewValues.eq(0).should('contain', '$4,800');
  Overview.footerBtn.contains('Explore').click();
  cy.url().should('include', 'explore');
  dashboard.overviewHeader.click();
  navigation.loadingSpinner.should('not.exist');
};

const widgetAmountInvoiced = () => {
  Overview.overviewValues.should('contain', 'Average of $400 per child');
};

const widgetAllOustandingInvoices = () => {
  Overview.overviewValues.should('contain', 'Average of $279 per invoice');
  Overview.footerBtn.contains('View Aging Report').click();
  cy.url().should('include', 'billing/admin/reports/aging');
  navigation.dashboardNav.click();
  dashboard.overviewHeader.click();
  navigation.loadingSpinner.should('not.exist');
};

const widgetAverageMomentsPerTeacher = () => {
  cy.get('.apexcharts-svg').eq(1).should('exist');
  cy.get('.apexcharts-tooltip-title').eq(1).realHover({ position: 'bottomLeft' });
  cy.wait(2000);
  cy.get('.apexcharts-tooltip-title').eq(1).should('be.visible');
  cy.get('.apexcharts-tooltip-title').eq(1).should('include.text', '1/17/24');
  cy.get('.apexcharts-tooltip-text-value').eq(1).should('include.text', '3');

  cy.get('.apexcharts-tooltip-title').eq(1).realHover({ position: 'bottomRight' });
  cy.get('.apexcharts-tooltip-title').eq(1).should('be.visible');
  cy.get('.apexcharts-tooltip-title').eq(1).should('include.text', '1/25/24');
  cy.get('.apexcharts-tooltip-text-value').eq(1).should('include.text', '4');
};

const widgetMonthlyActiveUsers = () => {
  cy.get('.apexcharts-svg').eq(2).should('exist');
  cy.get('.apexcharts-tooltip-title').eq(2).realHover({ position: 'bottomLeft' });
  cy.wait(2000);
  cy.get('.apexcharts-tooltip-title').eq(2).should('be.visible');
  cy.get('.apexcharts-tooltip-title').eq(2).should('include.text', '11/30/23');
  cy.get('.apexcharts-tooltip-text-value').eq(2).should('include.text', '6');

  cy.get('.apexcharts-tooltip-title').eq(2).realHover({ position: 'bottomRight' });
  cy.get('.apexcharts-tooltip-title').eq(2).should('be.visible');
  cy.get('.apexcharts-tooltip-title').eq(2).should('include.text', '12/28/23');
  cy.get('.apexcharts-tooltip-text-value').eq(2).should('include.text', '8');
};

const widgetDailySheetSaved = () => {
  cy.get('.apexcharts-svg').eq(3).should('exist');
  cy.get('.apexcharts-tooltip-title').eq(3).realHover({ position: 'bottomLeft' });
  cy.get('.apexcharts-tooltip-title').eq(3).should('be.visible');
  cy.get('.apexcharts-tooltip-title').eq(3).should('include.text', '1/17/24');
  cy.get('.apexcharts-tooltip-text-value').eq(3).should('include.text', '2');

  cy.get('.apexcharts-tooltip-title').eq(3).realHover({ position: 'bottomRight' });
  cy.get('.apexcharts-tooltip-title').eq(3).should('be.visible');
  cy.get('.apexcharts-tooltip-title').eq(3).should('include.text', '1/25/24');
  cy.get('.apexcharts-tooltip-text-value').eq(3).should('include.text', '5');
};

const widgetFTEQuickView = () => {
  Overview.organizationFTE.should('include.text', 'MikeCare');
  Overview.currentFTE.should('include.text', '10');
  Overview.varianceFTE.should('include.text', '5');
  Overview.FTEplus.should('include.text', '7.5');
  for (let n = 0; n < 2; n++) {
    Overview.groupFTE.eq(n).should('include.text', FTE_GROUPS[n].name);
    Overview.groupCurrentFTE.eq(n).should('include.text', FTE_GROUPS[n].fte);
    Overview.groupVarianceFTE.eq(n).should('include.text', FTE_GROUPS[n].variance);
    Overview.groupFTEPlus.eq(n).should('include.text', FTE_GROUPS[n].ftePlus);
    Overview.groupFTEGoal.eq(n).should('include.text', FTE_GROUPS[n].fteGoal);
  }
};

const addChildren = () => {
  people.addPersonButton.should('be.visible').click();
  people.firstNameInput.type(PARENT_OVERVIEW_DATA.CHILD_FIRST_NAME);
  people.lastNameInput.type(PARENT_OVERVIEW_DATA.CHILD_FIRST_NAME);
  people.modalSaveButton.click();
  people.firstNameLastName.should('be.visible');
  cy.wait(500);
  Relationships.relationshipHeaderNavigation.click();
  people.newRelationship.click();
  cy.contains('Associate Existing').click();
  Relationships.existingPersonSelect.click();
  Relationships.existingPersonSearch.select(PARENT_LOGIN_DATA.FULLNAME, { force: true });
  Relationships.relationshipType
    .find('option')
    .eq(0)
    .then((option) => {
      Relationships.relationshipType.select(option.val(), { force: true });
    });

  Relationships.chkPrimaryCaregiver.check({ force: true });
  Relationships.addRelationshipDesc.type(PARENT_OVERVIEW_DATA.RELATIONSHIP_DESC);
  Relationships.addRelationshipSaveBtn.click();
  cy.wait(500);
  people.firstNameLastName.should('be.visible');
};

const addDocuments = () => {
  cy.visit(BASE_URLS.DOCUMENTS);
  documents.addDocumentBtn.click();
  documents.documentNameInput.type(PARENT_DOCUMENTS_DATA[0].name);
  documents.templateOptionsInput.select(PARENT_DOCUMENTS_DATA[0].templateOptions);
  documents.groupingInput.type(PARENT_DOCUMENTS_DATA[0].section);
  documents.saveDocumentBtn.click();
  cy.wait(500);

  documents.addDocumentBtn.click();
  documents.documentNameInput.type(PARENT_DOCUMENTS_DATA[1].name);
  documents.templateOptionsInput.select(PARENT_DOCUMENTS_DATA[1].templateOptions);
  documents.groupingInput.type(PARENT_DOCUMENTS_DATA[1].section);
  documents.saveDocumentBtn.click();
  cy.wait(500);
};

const checkDocumentAndChild = () => {
  dashboard.childDocumentItem.each(($container, index) => {
    cy.wrap($container).within(() => {
      dashboard.childNameItem.should(
        'contain',
        index === 0 ? PARENT_OVERVIEW_DATA.CHILD_NAME_A : PARENT_OVERVIEW_DATA.CHILD_NAME_B
      );
    });
    dashboard.childDocumentItem.eq(0).within(() => {
      Overview.overviewDocRepoItem.each(($row, index) => {
        cy.wrap($row).within(() => {
          Overview.overviewDocSection.should('exist').then(($section) => {
            expect($section.text().trim()).to.equal(PARENT_DOCUMENTS_DATA[index].section);
          });

          Overview.overviewDocName.should('exist').then(($name) => {
            expect($name.text().trim()).to.equal(PARENT_DOCUMENTS_DATA[index].name);
          });
          if (index === 0) {
            Overview.overviewDocSignature.should('have.text', PARENT_OVERVIEW_DATA.SIGNATURE);
          } else if (index === 1) {
            Overview.overviewAcknowledgeDoc.should('have.text', PARENT_OVERVIEW_DATA.ACKNOWLEDGE);
            Overview.overviewAcknowledgeDoc.eq(0).click({ force: true });
          }
        });
      });
    });
    cy.wait(500);
    cy.get('button.swal2-confirm', { timeout: 10000 }).should('be.visible').click({ force: true });
  });
};
