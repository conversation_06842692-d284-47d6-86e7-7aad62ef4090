import checkinCheckout from 'cypress/pages/checkin-checkout';
import dashboard from 'cypress/pages/dashboard';
import expressDriveUp from 'cypress/pages/express-drive-up';
import navigation from 'cypress/pages/navigation';
import { BASE_URLS, CHILD_DATA, CHILD_DATA_ARRIVED, EXPRESS_DRIVE_UP } from 'cypress/support/constants';

import * as sinon from 'sinon';

context('Express Drive Up', () => {
  let clock: sinon.SinonFakeTimers;

  beforeEach(() => {
    cy.clearAndSeedCustomDatabase();
    cy.login(BASE_URLS.EXPRESS_DRIVEUP_URL);
  });

  afterEach(() => {
    if (clock) {
      clock.restore();
      clock = undefined;
    }
  });

  it('should verify express drive up layout', () => {
    // Check the layout
    navigation.headerNavMenu.invoke('text').should('equal', EXPRESS_DRIVE_UP.NAV_LABEL);

    expressDriveUp.arrivedReportTitle.invoke('text').should('equal', EXPRESS_DRIVE_UP.ARRIVED_REPORT_TITLE);

    expressDriveUp.checkInOutButton.each(($text) => {
      expect($text.text().trim().replace('/', ' ')).eq(EXPRESS_DRIVE_UP.CHECK_IN_OUT_LABEL);
    });

    expressDriveUp.onTheWayReportTitle.invoke('text').should('equal', EXPRESS_DRIVE_UP.ON_THE_WAY_REPORT_TITLE);

    const arrivedTableColumnHeadings = EXPRESS_DRIVE_UP.ARRIVED_COLUMN_HEADINGS;

    expressDriveUp.arrivedReportTable.find('th').each(($colHead) => {
      expect(arrivedTableColumnHeadings).to.include($colHead.text());
    });

    const onTheWayTableColumnHeadings = EXPRESS_DRIVE_UP.ON_THE_WAY_COLUMN_HEADINGS;

    expressDriveUp.onTheWayReportTable.find('th').each(($colHead) => {
      expect(onTheWayTableColumnHeadings).to.include($colHead.text());
    });
    //Checks arrived children
    expressDriveUp.arrivedReportTable.find('tr').then(($rows) => {
      if ($rows.length > EXPRESS_DRIVE_UP.MIN_ROW_COUNT) {
        expressDriveUp.arrivalFullNameColumn.each(($cell) => {
          cy.wrap($cell).should('contain', 'Y1 Y1');
          cy.wrap($cell).should('not.contain', 'Y2 Y1');
        });
        expressDriveUp.arrivalTimeColumn.each(($cell) => {
          cy.wrap($cell).should('not.be.empty');
        });
        expressDriveUp.groupArrival.each(($cell) => {
          cy.wrap($cell).should('contain', 'Toddler');
        });
        expressDriveUp.waitingForApproval.each(($div) => {
          expect($div.text().trim()).eq(EXPRESS_DRIVE_UP.WAITING_FOR_CHECKIN);
        });
      }
    });
    //Checks on the way children
    expressDriveUp.onTheWayReportTable.find('tr').then(($rows) => {
      if ($rows.length > EXPRESS_DRIVE_UP.MIN_ROW_COUNT) {
        expressDriveUp.onTheWayFullNameColumn.each(($cell) => {
          cy.wrap($cell).should('contain', 'child_name child_last_name');
        });
        expressDriveUp.estimatedTimeColumn.each(($cell) => {
          cy.wrap($cell).should('not.be.empty');
        });
        expressDriveUp.groupOnTheWay.each(($cell) => {
          cy.wrap($cell).should('contain', 'Toddler');
        });
        expressDriveUp.waitingForApproval.each(($div) => {
          expect($div.text().trim()).eq(EXPRESS_DRIVE_UP.WAITING_FOR_CHECKIN);
        });
      }
    });
  });

  it('should verify express drive-up workflow weekdays', () => {
    clock = sinon.useFakeTimers(new Date(2024, 4, 28).getTime());
    verifyExpressDriveUpAfterCheckIn();
  });

  it('should verify express drive-up workflow weekends', () => {
    clock = sinon.useFakeTimers(new Date(2024, 5, 1).getTime());
    verifyExpressDriveUpAfterCheckIn();
  });
});

const verifyExpressDriveUpAfterCheckIn = () => {
  expressDriveUp.waitingForApproval.eq(0).click();
  checkinCheckout.checkinModalSave.click();
  cy.containsOkAndClick();
  expressDriveUp.arrivalFullNameColumn.should('not.exist');

  expressDriveUp.waitingForApproval.eq(0).click();
  checkinCheckout.checkinModalSave.click();
  cy.containsOkAndClick();
  expressDriveUp.onTheWayFullNameColumn.should('not.exist');

  dashboard.peopleExpressDriveUp(CHILD_DATA.ID).should('not.exist');
  dashboard.peopleExpressDriveUp(CHILD_DATA_ARRIVED.ID).should('not.exist');
};
