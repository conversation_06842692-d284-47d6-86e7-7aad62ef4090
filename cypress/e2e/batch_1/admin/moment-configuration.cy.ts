import {
  BASE_URLS,
  GROUPS_DATA,
  MOMENT_TYPES,
  MOMENT_TYPES_EDITED,
  MOMENT_TYPES_STAFF,
  MOMENTS_DETAILS
} from 'cypress/support/constants';
import Admin from 'cypress/pages/admin';
import header from 'cypress/pages/header';
import people from 'cypress/pages/people';
import checkinCheckout from 'cypress/pages/checkin-checkout';
import navigation from 'cypress/pages/navigation';
import moments from 'cypress/pages/moments';
import groups from 'cypress/pages/groups';
import roster from 'cypress/pages/roster';
import MySite from 'cypress/pages/my-site';

context('Admin - Moment Configuration', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.ADMIN);
  });

  it('Moment Configuration', () => {
    checkAvailableMomentTypes();
    completeNameToFace();
    checkinNeededForCreation();
    checkAvailableMomentTypesForStaff();
  });
});

const checkAvailableMomentTypes = () => {
  navigation.newMomentNavMenu.click();
  MOMENT_TYPES.forEach((option) => {
    moments.momentType.find(`option:contains('${option}')`).should('exist');
  });
  moments.momentType.select(MOMENTS_DETAILS.ACTIVITY.type);
  cy.wait(500);
  moments.tagPeopleDiv.type('Matt Coffman');
  moments.saveMomentButton.click();
  cy.wait(500);

  Admin.viewMomentTypes.click({ force: true });
  for (let n = 0; n < 8; n++) {
    Admin.momentTypeSlider.eq(n).click({ force: true });
  }
  cy.wait(500);
  navigation.newMomentNavMenu.click();
  MOMENT_TYPES_EDITED.forEach((option) => {
    moments.momentType.find(`option:contains('${option}')`).should('exist');
  });
  moments.momentType.select(MOMENTS_DETAILS.LEARNING.type);
  cy.wait(500);
  moments.tagPeopleDiv.type('Matt Coffman');
  moments.saveMomentButton.click();
};

const completeNameToFace = () => {
  Admin.completeNameToFaceCheck.click({ force: true });
  navigation.manageNav.click({ force: true });
  navigation.groupsNav.click({ force: true });
  navigation.loadingSpinner.should('not.exist');

  //Adding new group
  groups.addGroupBtn.click();
  groups.addGroupName.type('A New Group');
  groups.saveGroupButton.click();
  cy.containsOkAndClick();

  //Adding new children
  navigation.peopleNav.should('be.visible').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.addPersonButton.should('be.visible').click();
  people.firstNameInput.type('A1');
  people.lastNameInput.type('B1');
  people.defaultGroupInput.select('A New Group');
  people.modalSaveButton.click();
  people.firstNameLastName.should('be.visible');
  cy.wait(500);

  //Adding new children
  navigation.peopleNav.should('be.visible').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.addPersonButton.should('be.visible').click();
  people.firstNameInput.type('B1');
  people.lastNameInput.type('A1');
  people.defaultGroupInput.select('A New Group');
  people.modalSaveButton.click();
  people.firstNameLastName.should('be.visible');
  cy.wait(500);

  header.quickUserToggle.click();
  header.userProfileName.click();
  people.firstNameLastName.should('be.visible');
  header.quickUserClose.click();

  people.moveBtn.click();
  people.switchDestinationGroup.select(GROUPS_DATA[0].NAME);
  cy.wait(500);
  people.switchGroupBtn.click();

  navigation.manageNav.click({ force: true });
  navigation.groupsNav.click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  groups.groupRosterBtn.first().click();
  roster.selectMultipleBtn.click();
  roster.initials.click({ multiple: true });
  roster.multipleCheckIn.click();
  cy.contains('Checked in 2', { timeout: 5000 });
  cy.containsOkAndClick();

  //Successful name to face
  roster.nameToFaceBtn.click();
  roster.initials.first().click();
  roster.saveNameToFaceBtn.click();
  cy.get('input[type="radio"][value="Potty"]').click({ force: true });
  cy.contains('button.swal2-confirm', 'Save').click();
  cy.get('body').contains('Name to Face saved');
  cy.containsOkAndClick();
  MySite.closeRosterModal.click();

  navigation.adminNav.click();
  Admin.viewMomentTypes.click({ force: true });
  Admin.completeNameToFaceCheck.click({ force: true });

  //Unsuccessful name to face
  navigation.manageNav.click({ force: true });
  navigation.groupsNav.click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  groups.groupRosterBtn.first().click();
  roster.nameToFaceBtn.click();
  roster.initials.first().click();
  roster.saveNameToFaceBtn.click();
  cy.contains('Missing the following checked in person(s) for a successful name-to-face check');
  cy.containsOkAndClick();
  MySite.closeRosterModal.click();
};

const checkinNeededForCreation = () => {
  header.quickUserToggle.click();
  header.userProfileName.click();
  header.quickUserClose.click();
  people.newCheckoutButton.click();
  checkinCheckout.checkoutModalSave.click();
  cy.containsOkAndClick();

  cy.visit(BASE_URLS.ADMIN);
  navigation.loadingSpinner.should('not.exist');
  Admin.viewMomentTypes.click({ force: true });
  navigation.newMomentNavMenu.click();
  cy.contains('Staff and admins must be checked in to post moments. Please visit your profile to checkin.', {
    timeout: 2000
  });
  cy.containsOkAndClick();
  Admin.staffCheckinRequired.click({ force: true });

  for (let n = 0; n < 7; n++) {
    Admin.adminOnlySlider.eq(n).click({ force: true });
  }
  cy.performLogout();
};

const checkAvailableMomentTypesForStaff = () => {
  cy.login(BASE_URLS.DEFAULT, 'Staff');
  navigation.newMomentNavMenu.click();

  MOMENT_TYPES_STAFF.forEach((option) => {
    moments.momentType.find(`option:contains('${option}')`).should('exist');
  });

  moments.momentType.select(MOMENTS_DETAILS.COMMENT.type);
  cy.wait(500);
  moments.tagPeopleDiv.type('MattStaff C');
  moments.saveMomentButton.click();
};
