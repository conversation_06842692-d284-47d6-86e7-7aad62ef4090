import navigation from 'cypress/pages/navigation';
import people from 'cypress/pages/people';
import reports from 'cypress/pages/reports';
import { BASE_URLS, GROUPS, NEW_CHILD_DATA, SECOND_CHILD_DATA } from 'cypress/support/constants';
import checkinCheckout from '../../../pages/checkin-checkout';
import moment from 'moment';
import billing from 'cypress/pages/billing';

context('Attendance grid by date tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login();
  });

  it('Check Attendance grid by date', () => {
    createPersonWithAttendanceData();
    checkAttendanceGridByDate();
    GroupByScheduleTypeCheckBox();
  });

  const createPersonWithAttendanceData = () => {
    navigation.manageNav.click({ force: true });
    navigation.peopleNav.should('be.visible').click({ force: true });
    people.addPersonButton.should('be.visible').click();
    people.firstNameInput.type(NEW_CHILD_DATA.FIRST_NAME);
    people.middleNameInput.type(NEW_CHILD_DATA.MIDDLE_NAME);
    people.lastNameInput.type(NEW_CHILD_DATA.LAST_NAME);
    people.preferredNameInput.type(NEW_CHILD_DATA.PREFERRED_NAME);
    people.newPersonInputType.should('include.text', NEW_CHILD_DATA.TYPE);
    people.defaultGroupInput.select(NEW_CHILD_DATA.DEFAULT_GROUP);
    people.modalSaveButton.click();
    people.firstNameLastName.should('be.visible');
    people.enrollmentDate.eq(3).type('01/01/2024');
    people.childGender.select('Male', { force: true });
    people.savePersonProfileButton.click({ force: true });
    cy.containsOkAndClick();
    addScheduleToFirstChild();
    people.firstNameInput.should('have.value', NEW_CHILD_DATA.FIRST_NAME);
    people.lastNameInput.should('have.value', NEW_CHILD_DATA.LAST_NAME);
    people.newCheckinButton.click();
    checkinCheckout.checkinTime.type(NEW_CHILD_DATA.NEW_CHECKIN_TIME);
    checkinCheckout.checkinModalSave.click();
    cy.containsOkAndClick();
    people.newCheckoutButton.click();
    checkinCheckout.checkoutTime.type(NEW_CHILD_DATA.NEW_CHECKOUT_TIME);
    checkinCheckout.checkoutModalSave.click();
    cy.containsOkAndClick();

    //create second child with different group
    navigation.peopleNav.should('be.visible').click({ force: true });
    people.addPersonButton.should('be.visible').click();
    people.firstNameInput.type(SECOND_CHILD_DATA.FIRST_NAME);
    people.middleNameInput.type(SECOND_CHILD_DATA.MIDDLE_NAME);
    people.lastNameInput.type(SECOND_CHILD_DATA.LAST_NAME);
    people.preferredNameInput.type(SECOND_CHILD_DATA.PREFERRED_NAME);
    people.newPersonInputType.should('include.text', SECOND_CHILD_DATA.TYPE);
    people.defaultGroupInput.select(SECOND_CHILD_DATA.DEFAULT_GROUP);
    people.modalSaveButton.click();
    people.firstNameLastName.should('be.visible');
    people.enrollmentDate.eq(3).type('04/04/2024');
    people.childGender.select('Male', { force: true });
    people.savePersonProfileButton.click({ force: true });
    cy.containsOkAndClick();
    addSchedule();
    people.firstNameLastName.should('be.visible');
    people.firstNameInput.should('have.value', SECOND_CHILD_DATA.FIRST_NAME);
    people.lastNameInput.should('have.value', SECOND_CHILD_DATA.LAST_NAME);
    people.newCheckinButton.click();
    checkinCheckout.checkinTime.type(SECOND_CHILD_DATA.NEW_CHECKIN_TIME);
    checkinCheckout.checkinModalSave.click();
    cy.containsOkAndClick();
    people.newCheckoutButton.click();
    checkinCheckout.checkoutTime.type(SECOND_CHILD_DATA.NEW_CHECKOUT_TIME);
    checkinCheckout.checkoutModalSave.click();
    cy.containsOkAndClick();
  };

  const addSchedule = () => {
    //Add schedule After care
    people.schedulingTab.click();
    people.newScheduleItem.click();
    people.scheduleTargetGroup.select(SECOND_CHILD_DATA.DEFAULT_GROUP);
    people.scheduleDate.clear();
    people.scheduleDate.type(moment().format('MM/DD/YYYY'), { force: true });
    people.scheduleType.select('After Care');
    people.scheduleSaturday.check({ force: true });
    people.scheduleSunday.check({ force: true });
    billing.selectPlanName.scrollIntoView();
    billing.selectPlanName.parent().find('.select2-selection').click();
    cy.get('.select2-results__option').eq(1).click();
    people.saveScheduleEntry.click();
    cy.containsOkAndClick();

    //Add Schedule Before care
    people.newScheduleItem.click();
    people.scheduleTargetGroup.select(SECOND_CHILD_DATA.DEFAULT_GROUP);
    people.scheduleDate.clear();
    people.scheduleDate.type(moment().format('MM/DD/YYYY'), { force: true });
    people.scheduleType.select('Before Care');
    people.scheduleSaturday.check({ force: true });
    people.scheduleSunday.check({ force: true });
    billing.selectPlanName.scrollIntoView();
    billing.selectPlanName.parent().find('.select2-selection').click();
    cy.get('.select2-results__option').eq(1).click();
    people.saveScheduleEntry.click();
    cy.containsOkAndClick();

    //Add Schedule full time
    people.newScheduleItem.click();
    people.scheduleTargetGroup.select(SECOND_CHILD_DATA.DEFAULT_GROUP);
    people.scheduleDate.clear();
    people.scheduleDate.type(moment().format('MM/DD/YYYY'), { force: true });
    people.scheduleType.select('Full-time');
    people.scheduleSaturday.check({ force: true });
    people.scheduleSunday.check({ force: true });
    billing.selectPlanName.scrollIntoView();
    billing.selectPlanName.parent().find('.select2-selection').click();
    cy.get('.select2-results__option').eq(1).click();
    people.saveScheduleEntry.click();
    cy.containsOkAndClick();
  };

  const addScheduleToFirstChild = () => {
    //Add Schedule Half day AM after selecting a new child record
    navigation.peopleNav.should('be.visible').click({ force: true });
    people.peopleFilter.click();
    people.selectPeopleCheckbox.check({ force: true });
    people.selectFamilyCheckbox.uncheck({ force: true });
    cy.contains('.card', `${NEW_CHILD_DATA.PREFERRED_NAME} ${NEW_CHILD_DATA.LAST_NAME}`).should('be.visible').click();
    people.schedulingTab.click();
    people.newScheduleItem.click();
    people.scheduleTargetGroup.select(NEW_CHILD_DATA.DEFAULT_GROUP);
    people.scheduleDate.clear();
    people.scheduleDate.type(moment().format('MM/DD/YYYY'), { force: true });
    people.scheduleType.select('Half Day AM');
    people.scheduleSaturday.check({ force: true });
    people.scheduleSunday.check({ force: true });
    billing.selectPlanName.scrollIntoView();
    billing.selectPlanName.parent().find('.select2-selection').click();
    cy.get('.select2-results__option').eq(1).click();
    people.saveScheduleEntry.click();
    cy.containsOkAndClick();

    //Add Schedule Half day PM
    people.newScheduleItem.click();
    people.scheduleTargetGroup.select(NEW_CHILD_DATA.DEFAULT_GROUP);
    people.scheduleDate.clear();
    people.scheduleDate.type(moment().add(8, 'days').format('MM/DD/YYYY'), { force: true });
    people.scheduleType.select('Half Day PM');
    people.scheduleSaturday.check({ force: true });
    people.scheduleSunday.check({ force: true });
    billing.selectPlanName.scrollIntoView();
    billing.selectPlanName.parent().find('.select2-selection').click();
    cy.get('.select2-results__option').eq(1).click();
    people.saveScheduleEntry.click();
    cy.containsOkAndClick();
  };

  const checkAttendanceGridByDate = () => {
    cy.visit(BASE_URLS.ATTENDANCE_GRID_BY_DATE);

    //Verify with incorrect date
    reports.weekOfInput.clear().type(moment().subtract(8, 'days').format('MM/DD/YYYY'));
    reports.updateBtn.click();
    reports.attendanceReportTable.should('not.exist');
    cy.contains('No attendance records found for the selected date').should('be.visible');

    //verify with correct date
    reports.weekOfInput.clear().type(moment().add(2, 'days').format('MM/DD/YYYY'));
    reports.updateBtn.click();

    reports.attendanceReportTable.within(() => {
      // Find the row for "Atkinson, Mark"
      cy.contains('td', 'Atkinson, Mark')
        .parent('tr')
        .within(() => {
          cy.get('td').eq(1).should('contain.text', 'Atkinson, Mark');
          cy.get('td').eq(2).should('contain.text', 'KinderGarden');
          cy.get('td').eq(3).should('contain.text', '(No Grade)');
          cy.get('td').eq(4).should('contain.text', 'After Care');
        });

      // Now check "Test, Baby"
      cy.contains('td', 'Test, Baby')
        .parent('tr')
        .within(() => {
          cy.get('td').eq(1).should('contain.text', 'Test, Baby');
          cy.get('td').eq(2).should('contain.text', 'Toddler');
          cy.get('td').eq(3).should('contain.text', '(No Grade)');
          cy.get('td').eq(4).should('contain.text', 'Half Day AM');
        });
    });

    //Verify with group
    reports.selectGroup.select(NEW_CHILD_DATA.DEFAULT_GROUP);
    reports.updateBtn.click();
    reports.attendanceReportTable.within(() => {
      // Now check "Test, Baby"
      cy.contains('td', 'Test, Baby')
        .parent('tr')
        .within(() => {
          cy.get('td').eq(1).should('contain.text', 'Test, Baby');
          cy.get('td').eq(2).should('contain.text', 'Toddler');
          cy.get('td').eq(3).should('contain.text', '(No Grade)');
          cy.get('td').eq(4).should('contain.text', 'Half Day AM');
        });
    });

    reports.selectGroup.select(SECOND_CHILD_DATA.DEFAULT_GROUP);
    reports.updateBtn.click();
    cy.contains('td', 'Atkinson, Mark')
      .parent('tr')
      .within(() => {
        cy.get('td').eq(1).should('contain.text', 'Atkinson, Mark');
        cy.get('td').eq(2).should('contain.text', 'KinderGarden');
        cy.get('td').eq(3).should('contain.text', '(No Grade)');
        cy.get('td').eq(4).should('contain.text', 'After Care');
      });

    const differentGroup = Object.values(GROUPS).find(
      (group) => group.NAME !== NEW_CHILD_DATA.DEFAULT_GROUP && group.NAME !== SECOND_CHILD_DATA.DEFAULT_GROUP
    )?.NAME;

    if (differentGroup) {
      reports.selectGroup.select(differentGroup);
      reports.updateBtn.click();
      reports.attendanceReportTable.should('not.exist');
    }

    reports.selectGroup.select('All');
    reports.updateBtn.click();
    reports.attendanceReportTable.contains('td', NEW_CHILD_DATA.DEFAULT_GROUP).should('be.visible');
    reports.attendanceReportTable.within(() => {
      cy.contains('td', 'Atkinson, Mark')
        .parent('tr')
        .within(() => {
          cy.get('td').eq(1).should('contain.text', 'Atkinson, Mark');
          cy.get('td').eq(2).should('contain.text', 'KinderGarden');
          cy.get('td').eq(3).should('contain.text', '(No Grade)');
          cy.get('td').eq(4).should('contain.text', 'After Care');
        });
      cy.contains('td', 'Test, Baby')
        .parent('tr')
        .within(() => {
          cy.get('td').eq(1).should('contain.text', 'Test, Baby');
          cy.get('td').eq(2).should('contain.text', 'Toddler');
          cy.get('td').eq(3).should('contain.text', '(No Grade)');
          cy.get('td').eq(4).should('contain.text', 'Half Day AM');
        });
    });
  };

  const GroupByScheduleTypeCheckBox = () => {
    reports.groupByScheduleTypeCheckbox.click({ force: true });
    reports.updateBtn.click();
    cy.contains('h2', 'After Care')
      .should('exist')
      .parents('.group-title-container')
      .next()
      .within(() => {
        reports.attendanceReportTable.within(() => {
          cy.contains('td', 'Atkinson, Mark').should('exist');
          cy.contains('td', 'KinderGarden').should('exist');
          cy.contains('td', 'After Care - Su, M, T, W, R, F, Sa').should('exist');
        });
      });

    cy.contains('h2', 'Before Care')
      .should('exist')
      .parents('.group-title-container')
      .next()
      .within(() => {
        reports.attendanceReportTable.within(() => {
          cy.contains('td', 'Atkinson, Mark').should('exist');
          cy.contains('td', 'KinderGarden').should('exist');
          cy.contains('td', 'Before Care - Su, M, T, W, R, F, Sa').should('exist');
        });
      });

    cy.contains('h2', 'Full-time')
      .should('exist')
      .scrollIntoView()
      .should('exist')
      .parents('.group-title-container')
      .next()
      .within(() => {
        reports.attendanceReportTable.within(() => {
          cy.contains('td', 'Atkinson, Mark').should('exist');
          cy.contains('td', 'KinderGarden').should('exist');
          cy.contains('td', 'Full-time - Su, M, T, W, R, F, Sa').should('exist');
        });
      });

    cy.contains('h2', 'Half Day AM')
      .should('exist')
      .scrollIntoView()
      .should('exist')
      .parents('.group-title-container')
      .next()
      .within(() => {
        reports.attendanceReportTable.within(() => {
          cy.contains('td', 'Test, Baby').should('exist');
          cy.contains('td', 'Toddler').should('exist');
          cy.contains('td', 'Half Day AM - Su, M, T, W, R, F, Sa').should('exist');
        });
      });

    cy.contains('h2', 'Half Day PM')
      .scrollIntoView()
      .should('exist')
      .parents('.group-title-container')
      .next()
      .should('contain.text', 'No attendance records found for the selected date.');
  };
});
