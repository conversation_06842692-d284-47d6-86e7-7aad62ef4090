import { checkRowData, getFormattedDatesForActivities } from 'cypress/e2e/utils';
import checkinCheckout from 'cypress/pages/checkin-checkout';
import navigation from 'cypress/pages/navigation';
import people from 'cypress/pages/people';
import reports from 'cypress/pages/reports';
import search from 'cypress/pages/search';
import {
  BASE_URLS,
  CHECK_IN_OUT_DATA,
  CHECK_IN_OUT_INFORMATION_COLUMNS,
  CHECK_IN_OUT_INFORMATION_DATA,
  CHECK_IN_OUT_REPORT_COLUMNS,
  CHECK_IN_OUT_REPORT_DATA,
  PEOPLE_DIRECTORY,
  STAFF_LOGIN_DATA
} from 'cypress/support/constants';

context('Check In/Out without PIN Code report tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('Checks Check In/Out without PIN Code report', () => {
    checkPinCheckInAsStaff();
    checkInStaff();
    checkTableData();
    checkTypeFiltering();
    checkDateFiltering();
  });

  const dates = getFormattedDatesForActivities();
  const today = dates.today;

  const checkPinCheckInAsStaff = () => {
    cy.login(BASE_URLS.PIN_CODE_CHECK_IN_OUT, 'Staff');
    people.pinCode.type(PEOPLE_DIRECTORY.PEOPLE_PIN);
    people.pinSubmit.click();
    cy.wait(500);
    cy.containsOkAndClick();
  };

  const checkInStaff = () => {
    cy.login(BASE_URLS.CHECK_IN_OUT_WITHOUT_PIN_CODE);
    search.searchPeopleIcon.click();
    search.searchPeopleInp.clear().type(STAFF_LOGIN_DATA.FIRST_NAME);
    search.searchManageButton.first().click();

    people.newCheckinButton.click();
    checkinCheckout.checkinModalSave.click();
    cy.containsOkAndClick();
  };

  const checkTableData = () => {
    cy.visit(BASE_URLS.CHECK_IN_OUT_WITHOUT_PIN_CODE);
    navigation.loadingSpinner.should('not.exist');

    reports.startDateInput.clear();
    reports.startDateInput.type(CHECK_IN_OUT_DATA.START_DATE);
    reports.endDateInput.clear();
    reports.endDateInput.type(today);
    reports.updateBtn.click();

    reports.checkInOutRatioTable.within(() => {
      cy.get('th').each((header, index) => {
        if (index < 3) {
          cy.wrap(header).should('include.text', CHECK_IN_OUT_REPORT_COLUMNS[index]);
        }
      });
      checkRowData(1, CHECK_IN_OUT_REPORT_DATA.initial);
    });

    reports.checkInOutInformationTable.within(() => {
      cy.get('tbody > tr')
        .first()
        .within(() => {
          cy.get('th').each((header, index) => {
            cy.wrap(header).should('include.text', CHECK_IN_OUT_INFORMATION_COLUMNS[index]);
          });
        });

      cy.get('tbody > tr')
        .not(':first')
        .each((row, rowIndex) => {
          const rowData = Object.values(CHECK_IN_OUT_INFORMATION_DATA[rowIndex]);
          checkRowData(rowIndex + 1, rowData);
        });
    });
  };

  const checkTypeFiltering = () => {
    reports.selectPersonType.select('Staff');
    reports.updateBtn.click();

    reports.checkInOutRatioTable.within(() => {
      checkRowData(1, CHECK_IN_OUT_REPORT_DATA.staff);
    });

    reports.checkInOutInformationTable.within(() => {
      cy.get('tbody > tr').not(':first').should('have.length', 1);

      cy.get('tbody > tr')
        .not(':first')
        .within(() => {
          cy.get('td').eq(2).should('include.text', today);
        });
    });

    reports.selectPersonType.select('Child');
    reports.updateBtn.click();

    reports.checkInOutInformationTable.within(() => {
      cy.get('tbody > tr').not(':first').should('have.length', 2);
    });
  };

  const checkDateFiltering = () => {
    reports.selectPersonType.select('All');
    reports.endDateInput.clear();
    reports.endDateInput.type(CHECK_IN_OUT_DATA.END_DATE);
    reports.updateBtn.click();

    reports.checkInOutRatioTable.within(() => {
      checkRowData(1, CHECK_IN_OUT_REPORT_DATA.child);
    });

    reports.checkInOutInformationTable.within(() => {
      cy.get('tbody > tr').not(':first').should('have.length', 2);
    });

    reports.selectPersonType.select('Staff');
    reports.updateBtn.click();

    reports.checkInOutInformationTable.should('not.exist');
    reports.checkInOutRatioTable.should('not.exist');
  };
});
