import groups from 'cypress/pages/groups';
import navigation from 'cypress/pages/navigation';
import people from 'cypress/pages/people';
import relationships from 'cypress/pages/relationships';
import reports from 'cypress/pages/reports';
import { BASE_URLS, CONTACT_ROSTER_DATA, CONTACT_ROSTER_REPORT, NEW_PARENT_DATA } from 'cypress/support/constants';

context('Contatc Roster report tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('Checks Contact Roster report', () => {
    createsChildrenAndGroup();
    checkRosterReport();
  });
});

const createsChildrenAndGroup = () => {
  cy.login(BASE_URLS.GROUPS);

  groups.addGroupBtn.click();
  groups.addGroupName.type('New Group');
  groups.addGroupCapacity.type('2');
  groups.infantGroupCheckbox.check({ force: true });
  groups.saveGroupButton.click();
  cy.containsOkAndClick();

  //Adding new children
  navigation.peopleNav.click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.addPersonButton.should('be.visible').click();
  people.firstNameInput.type(CONTACT_ROSTER_REPORT.ACTIVE_CHILD_NAME);
  people.lastNameInput.type(CONTACT_ROSTER_REPORT.ACTIVE_CHILD_LAST_NAME);
  people.defaultGroupInput.select(CONTACT_ROSTER_REPORT.GROUP_NAME);
  people.modalSaveButton.click();
  people.firstNameLastName.should('be.visible');
  cy.wait(500);

  people.dropdownMenu.scrollIntoView().click();
  people.uploadNewAvatar.click();
  people.chooseFile.selectFile('cypress/fixtures/photo1.jpg', { force: true });
  people.uploadFile.click();
  cy.wait(5000);
  people.uploadFile.should('not.exist');

  cy.get('.people-card-user-img img')
    .should('be.visible')
    .and(($img) => {
      const imgElement = $img[0] as HTMLImageElement;
      expect(imgElement.naturalWidth).to.be.greaterThan(0);
    });

  people.personBirthday.first().type(CONTACT_ROSTER_DATA[1].dob);
  cy.get('a[href="#standardOutlook"]').click();
  people.allergies.last().type(CONTACT_ROSTER_DATA[1].allergies);
  people.importantNotes.last().type(CONTACT_ROSTER_DATA[1].importantNotes);
  people.specialNeeds.last().type(CONTACT_ROSTER_DATA[1].specialNeeds);
  people.savePersonProfileButton.click();
  cy.containsOkAndClick();

  //Adding parent
  relationships.relationshipHeaderNavigation.click();
  people.newRelationship.click();
  cy.contains('Create New').click();
  relationships.firstNameInput.type(NEW_PARENT_DATA.FIRST_NAME);
  relationships.lastNameInput.type(NEW_PARENT_DATA.LAST_NAME);
  cy.get('button.multiselect.dropdown-toggle.custom-select[title="None selected"]').eq(1).click();
  cy.contains('label.form-check-label', NEW_PARENT_DATA.TYPE).prev('input[type="checkbox"]').check();
  relationships.primaryCareTaker.check({ force: true });
  relationships.relationshipDescription.type(NEW_PARENT_DATA.DESCRIPTION);
  relationships.relationshipSaveBtn.click();
  cy.wait(2000);

  people.firstNameLastName.should('include.text', NEW_PARENT_DATA.FIRST_NAME, NEW_PARENT_DATA.LAST_NAME);
  people.profileType.should('include.text', NEW_PARENT_DATA.TYPE);
  people.inputEmailAddress.type(NEW_PARENT_DATA.EMAIL);
  people.personAccountSaveBtn.click();
  cy.get('button').contains('Proceed').click();
  cy.containsOkAndClick();

  //Adding new inactive children
  navigation.manageNav.click({ force: true });
  navigation.peopleNav.click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.addPersonButton.should('be.visible').click();
  people.firstNameInput.type(CONTACT_ROSTER_REPORT.INACTIVE_CHILD_NAME);
  people.lastNameInput.type(CONTACT_ROSTER_REPORT.INACTIVE_CHILD_LAST_NAME);
  people.defaultGroupInput.select(CONTACT_ROSTER_REPORT.GROUP_NAME);
  people.modalSaveButton.click();
  people.firstNameLastName.should('be.visible');
  cy.wait(500);

  people.dropdownMenu.scrollIntoView().click();
  people.deactivateUser.click();
  people.deactivationReason.select('Never Enrolled');
  people.withdrawDate.type('2024-01-01');
  people.deactivatePersonBtn.click();
  cy.containsOkAndClick();
  people.firstNameLastName.should('be.visible');
  cy.wait(500);

  people.personBirthday.first().type(CONTACT_ROSTER_DATA[0].dob);
  cy.get('a[href="#standardOutlook"]').click();
  people.allergies.last().type(CONTACT_ROSTER_DATA[0].allergies);
  people.savePersonProfileButton.click();
  cy.containsOkAndClick();
};

const checkRosterReport = () => {
  cy.login(BASE_URLS.CONTACT_ROSTER_REPORT);

  reports.selectGroup.select(CONTACT_ROSTER_REPORT.GROUP_NAME);
  reports.updateBtn.click();

  reports.includeChildAvatars.click({ force: true });
  reports.updateBtn.click();
  cy.wait(500);
  reports.personAvatar.find('.people-list-user-img').should('have.css', 'background-image').and('not.eq', 'none');

  reports.includeChildAvatars.click({ force: true });
  reports.includeInactivePeople.click({ force: true });
  reports.updateBtn.click();
  cy.wait(500);
  reports.personName.should('have.length', 2);

  CONTACT_ROSTER_DATA.forEach((data, index) => {
    reports.personName.eq(index).should('include.text', data.name);
    reports.dateOfBirth.eq(index).should('include.text', data.dob);
    reports.allergies.eq(index).should('include.text', data.allergies);
    reports.importantNotes.eq(index).should('include.text', data.importantNotes);
    reports.specialNeeds.eq(index).should('include.text', data.specialNeeds);
  });

  reports.familyName.should('include.text', NEW_PARENT_DATA.FIRST_NAME);
  reports.familyName.should('include.text', NEW_PARENT_DATA.TYPE);
  reports.familyName.should('include.text', NEW_PARENT_DATA.DESCRIPTION);
  reports.familyContact.should('include.text', NEW_PARENT_DATA.EMAIL);
};
