import announcements from 'cypress/pages/announcements';
import {
  ANNOUNCEMENTS_DATA,
  ANNOUNCEMENTS_REMINDER,
  BASE_URLS,
  GROUPS,
  LOGIN_DATA,
  STAFF_LOGIN_DATA
} from 'cypress/support/constants';
import { getFormattedDate, getFormattedFutureDate } from '../../utils';

context('Announcements page Tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('should verify functionalities in announcements page', () => {
    cy.login(BASE_URLS.ANNOUNCEMENTS);
    createAnnouncement();
    createAnnouncementWithRemider();
    checkfiltering();
    announcementEdit();
    announcementDelete();
  });
});

const formattedDate = getFormattedDate();
const formattedTomorrow = getFormattedFutureDate(1);

const createAnnouncement = () => {
  //Announcement All
  announcements.addAnnouncementsBtn.click();
  announcements.announcementTitleInput.type(ANNOUNCEMENTS_DATA[0].title);
  announcements.announcementDescriptionInput.type(ANNOUNCEMENTS_DATA[0].description);
  announcements.saveAnnouncementBtn.click();

  //Announcement Staff
  announcements.addAnnouncementsBtn.click();
  announcements.announcementTitleInput.type(ANNOUNCEMENTS_DATA[1].title);
  announcements.announcementDescriptionInput.type(ANNOUNCEMENTS_DATA[1].description);
  announcements.audienceSelectedRoles.check({ force: true });
  announcements.allStaffs.check({ force: true });
  announcements.saveAnnouncementBtn.click();

  //Announcement Group
  announcements.addAnnouncementsBtn.click();
  announcements.announcementTitleInput.type(ANNOUNCEMENTS_DATA[2].title);
  announcements.announcementDescriptionInput.type(ANNOUNCEMENTS_DATA[2].description);
  announcements.selectedGroups.check({ force: true });
  announcements.selectGroupsInput.select(ANNOUNCEMENTS_DATA[2].recipients, { force: true });
  announcements.saveAnnouncementBtn.click();

  //Check announcements created
  for (let n = 0; n < 3; n++) {
    announcements.announcementTitle.eq(n).should('include.text', ANNOUNCEMENTS_DATA[n].title);
    announcements.scheduledAnnouncementDate.eq(n).should('include.text', formattedDate);
    announcements.recipientsAnnouncement.eq(n).should('include.text', ANNOUNCEMENTS_DATA[n].recipients);
    announcements.nextReminderAnnouncement.eq(n).should('include.text', ANNOUNCEMENTS_DATA[n].nextReminder);
  }
};

const createAnnouncementWithRemider = () => {
  //Create announcement with reminder
  announcements.addAnnouncementsBtn.click();
  announcements.announcementTitleInput.type(ANNOUNCEMENTS_REMINDER.TITLE);
  announcements.announcementDescriptionInput.type(ANNOUNCEMENTS_REMINDER.DESCRIPTION);
  announcements.selectedGroups.check({ force: true });
  announcements.selectGroupsInput.select(ANNOUNCEMENTS_REMINDER.RECIPIENT, { force: true });

  announcements.announcementStartDateInput.click({ force: true });
  announcements.announcementStartDateInput.clear();
  announcements.announcementStartDateInput.type(formattedTomorrow, { force: true });
  announcements.announcementEndDateInput.click({ force: true });
  announcements.announcementEndDateInput.clear();
  announcements.announcementEndDateInput.type(formattedTomorrow, { force: true });

  announcements.addReminderBtn.click();
  announcements.reminderDateInput.click();
  announcements.reminderDateInput.type(formattedTomorrow, { force: true });
  announcements.reminderTimeInput.click();
  announcements.reminderTimeInput.type(ANNOUNCEMENTS_REMINDER.TIME);
  announcements.setReminderBtn.click();
  announcements.saveAnnouncementBtn.click();

  //Check announcement with reminder
  announcements.announcementTitle.eq(3).should('include.text', ANNOUNCEMENTS_REMINDER.TITLE);
  announcements.scheduledAnnouncementDate.eq(3).should('include.text', formattedTomorrow);
  announcements.recipientsAnnouncement.eq(3).should('include.text', ANNOUNCEMENTS_REMINDER.RECIPIENT);
  announcements.nextReminderAnnouncement.eq(3).should('include.text', ANNOUNCEMENTS_REMINDER.TIME_FORMATTED);
};

const checkfiltering = () => {
  announcements.announcementTitle.should('have.length', 4);

  //By Group
  announcements.selectGroupAnnouncements.select(GROUPS.TODDLER.NAME);
  announcements.updateAnnouncementsBtn.click();
  announcements.announcementTitle.should('have.length', 2);

  announcements.selectGroupAnnouncements.select(GROUPS.KINDERGARDEN.NAME);
  announcements.updateAnnouncementsBtn.click();
  announcements.announcementTitle.should('have.length', 2);
  announcements.selectGroupAnnouncements.select('');
  announcements.updateAnnouncementsBtn.click();

  //By Role
  announcements.selectRoleAnnouncements.select(LOGIN_DATA.TYPE);
  announcements.updateAnnouncementsBtn.click();
  announcements.announcementTitle.should('have.length', 1);

  announcements.selectRoleAnnouncements.select(STAFF_LOGIN_DATA.TYPE);
  announcements.updateAnnouncementsBtn.click();
  announcements.announcementTitle.should('have.length', 2);
  announcements.selectRoleAnnouncements.select('');
  announcements.updateAnnouncementsBtn.click();

  //By Date
  announcements.startDateAnnouncements.click();
  announcements.startDateAnnouncements.clear();
  announcements.startDateAnnouncements.type(formattedTomorrow, { force: true });
  announcements.updateAnnouncementsBtn.click();
  announcements.announcementTitle.should('have.length', 1);

  //No announcements message
  announcements.selectRoleAnnouncements.select(STAFF_LOGIN_DATA.TYPE);
  announcements.updateAnnouncementsBtn.click();
  announcements.noAnnouncementsAlert.should('be.visible');
  announcements.noAnnouncementsCloseAlert.click();
  announcements.noAnnouncementsAlert.should('not.exist');
};

const announcementEdit = () => {
  cy.reload();
  announcements.announcementOptions.eq(0).click();
  announcements.editAnnouncement.eq(0).click();
  announcements.announcementTitleInput.clear();
  announcements.announcementTitleInput.type(ANNOUNCEMENTS_REMINDER.TITLE_EDITED);
  announcements.saveAnnouncementBtn.click();
  announcements.announcementTitle.eq(0).should('include.text', ANNOUNCEMENTS_REMINDER.TITLE_EDITED);
};

const announcementDelete = () => {
  announcements.announcementOptions.eq(0).click();
  announcements.deleteAnnouncement.eq(0).click();
  cy.contains('Yes, delete it!').click();
  announcements.announcementTitle.should('have.length', 3);
};
