import { getPaginationCount } from 'cypress/e2e/utils';
import activities from 'cypress/pages/activities';
import { ACTIVITIES_PAGINATION } from 'cypress/support/constants';

export const paginationFlow = () => {
  validatePagination();
  activities.filterPublishedCheckbox.check({ force: true });
  validatePagination();
  activities.paginationPreviousButton.click();
  validatePagination();
  activities.filterPublishedCheckbox.check({ force: true });
  activities.filterDraftCheckbox.check({ force: true });
  validatePagination();
  activities.paginationPreviousButton.click();
  validatePagination();
  activities.filterDraftCheckbox.check({ force: true });
  activities.filterApprovalCheckbox.check({ force: true });
  validatePagination();
  activities.paginationPreviousButton.click();
  validatePagination();
  activities.filterApprovalCheckbox.check({ force: true });
  activities.filterPublishedCheckbox.check({ force: true });
  activities.filterDraftCheckbox.check({ force: true });
  validatePagination();
  activities.paginationPreviousButton.click();
  validatePagination();
};

export const validatePagination = () => {
  activities.paginationRecordCount.then(($el) => {
    const [start, end, total] = getPaginationCount($el.text());

    const isEndGreaterThan200 = !(total < ACTIVITIES_PAGINATION.PAGINATION_TEST_TOTAL_COUNT);
    if (isEndGreaterThan200) {
      const currentPageDifference = end - start;
      expect(currentPageDifference).to.eq(ACTIVITIES_PAGINATION.DIFF_COUNT);
      navigateToNextPageAndValidate();
    }
  });
};

export const navigateToNextPageAndValidate = () => {
  activities.paginationNextButton.click();
  activities.paginationRecordCount.then(($el) => {
    const [start, end] = getPaginationCount($el.text());
    expect(end - start).to.eq(ACTIVITIES_PAGINATION.DIFF_COUNT);
  });
};

export function typeDraftTheme() {
  cy.get('input[type="radio"][name="publish"][value="no"]', { timeout: 3000 }).check({ force: true });
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);
}
