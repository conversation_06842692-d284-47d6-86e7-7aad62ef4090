import { ADD_THEMES, BASE_URLS, NEW_THEMES, STAFF_THEMES, THEMES_LAYOUT } from 'cypress/support/constants';
import { paginationFlow, typeDraftTheme } from './utils';
import activities from 'cypress/pages/activities';

context('All Themes Page Tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('should verify pagination controls in all themes page', () => {
    cy.login(BASE_URLS.ACTIVITIES_THEMES_URL);
    paginationFlow();
    cy.performLogout();
  });

  it('should verify pagination controls in all themes staff', () => {
    cy.login(BASE_URLS.ACTIVITIES_THEMES_URL, 'Staff');
    paginationFlow();
  });

  it('Check functionalities in content: all themes', () => {
    cy.login(BASE_URLS.ACTIVITIES_THEMES_URL);
    checkLayoutFunctionalitiesAllThemes();
  });

  it('Check functionalities in content: all themes (staff) ', () => {
    cy.login(BASE_URLS.ACTIVITIES_THEMES_URL);
    checkCreateThemesData();
    checkAllThemesStaff();
  });
});

const checkLayoutFunctionalitiesAllThemes = () => {
  //Adds published theme
  activities.createNewThemeBtn.should('be.visible').click({ force: true });
  cy.wait(3000);
  activities.themeNameInput.should('be.visible').type(NEW_THEMES[1].name);
  activities.saveTheme.click();
  cy.wait(2000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);

  //Adds draft theme
  activities.createNewThemeBtn.should('be.visible').click({ force: true });
  cy.wait(3000);
  cy.get('input#inputThemeName', { timeout: 3000 }).type(NEW_THEMES[2].name);
  activities.saveTheme.click();
  cy.wait(2000);
  typeDraftTheme();

  //Checks sorting and filtering using search
  activities.searchThemeBtn.click();
  cy.wait(500);
  activities.themeTitle.should('have.length', 3);
  activities.inputThemeSearch.type('Publish');
  activities.searchThemeBtn.click();
  cy.wait(500);
  activities.themeTitle.should('have.length', 1);

  //Checks dropdown functionalities and table content
  activities.dropdownThemeOptions.click();
  activities.editTheme.click();
  cy.get('textarea#inputThemeDescription').type('Test');
  activities.saveTheme.click();
  cy.wait(2000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);
  cy.contains('OK', { timeout: 5000 }).click();
  activities.inputThemeSearch.clear();
  cy.wait(1000);
  activities.searchThemeBtn.click();
  cy.wait(1000);

  for (let n = 0; n < 3; n++) {
    activities.themeTitle.eq(n).should('include.text', NEW_THEMES[n].name);
    activities.themeCreatedByName.eq(n).should('include.text', NEW_THEMES[n].createdBy);
    activities.themeCreatedByOrg.eq(n).should('include.text', NEW_THEMES[n].createdOrg);
    activities.dropdownThemeOptions.eq(n).should('include.text', NEW_THEMES[n].type);
  }
  activities.themeModifiedByName.eq(0).should('include.text', NEW_THEMES[1].createdBy);
  activities.themeModifiedByOrg.eq(0).should('include.text', NEW_THEMES[1].createdOrg);
  activities.themeApprovedByName.eq(0).should('include.text', NEW_THEMES[1].createdBy);
  activities.themeApprovedByOrg.eq(0).should('include.text', NEW_THEMES[1].createdOrg);

  activities.inputThemeSearch.type('Draft');
  activities.searchThemeBtn.click();
  cy.wait(1000);
  activities.dropdownThemeOptions.click();
  activities.publishTheme.click({ force: true });
  cy.wait(1000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);
  activities.allThemesHeader.click();
  cy.wait(1000);
  activities.inputThemeSearch.type('Draft');
  activities.searchThemeBtn.click();
  cy.wait(1000);
  activities.dropdownThemeOptions.should('include.text', NEW_THEMES[1].type);

  activities.allThemesHeader.click();
  cy.wait(1000);
  activities.inputThemeSearch.type('Publish');
  activities.searchThemeBtn.click();
  cy.wait(1000);
  activities.dropdownThemeOptions.click();
  activities.unpublishTheme.click({ force: true });
  cy.wait(1000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);
  activities.allThemesHeader.click();
  cy.wait(1000);
  activities.inputThemeSearch.type('Publish');
  activities.searchThemeBtn.click();
  cy.wait(1000);
  activities.dropdownThemeOptions.should('include.text', NEW_THEMES[2].type);

  activities.dropdownThemeOptions.click();
  activities.deleteTheme.click();
  cy.contains('Yes, delete it').click({ force: true });
  cy.wait(1000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);

  activities.allThemesHeader.click();
  cy.wait(1000);
  activities.themeTitle.should('have.length', 2);

  activities.dropdownThemeOptions.eq(0).click();
  activities.reviewTheme.click();
  cy.wait(1000);
  activities.themeApproveReviewBtn.click();
  cy.contains('OK', { timeout: 5000 }).click();
  activities.themeApprovedByName.eq(0).should('include.text', NEW_THEMES[1].createdBy);
  activities.themeApprovedByOrg.eq(0).should('include.text', NEW_THEMES[1].createdOrg);
};

const checkCreateThemesData = () => {
  activities.createNewThemeBtn.should('be.visible').click({ force: true });
  cy.wait(3000);
  activities.themeNameInput.should('be.visible').type(ADD_THEMES[0].name, { force: true });
  activities.themeDescriptionInput.type(ADD_THEMES[0].description);
  activities.addDaysBtn.click();
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);
  activities.addActivityTheme.click();
  activities.existingActivities.select(ADD_THEMES[0].activity, { force: true });
  activities.copyActivityBtn.click();
  activities.saveTheme.click();
  cy.wait(2000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);
  cy.contains('OK', { timeout: 5000 }).click();

  activities.createNewThemeBtn.should('be.visible').click({ force: true });
  cy.wait(3000);
  activities.themeNameInput.should('be.visible').type(ADD_THEMES[1].name, { force: true });
  activities.saveTheme.click();
  cy.wait(2000);
  typeDraftTheme();
  cy.wait(1000);
  cy.performLogout();
};

const checkAllThemesStaff = () => {
  cy.login(BASE_URLS.ACTIVITIES_THEMES_URL, 'Staff');
  //Check layout
  cy.url().should('include', BASE_URLS.ACTIVITIES_THEMES_URL);
  cy.contains(THEMES_LAYOUT.ALL_ACTIVITIES).should('be.visible');
  cy.contains(THEMES_LAYOUT.ALL_THEMES).should('be.visible');
  cy.contains(THEMES_LAYOUT.SCHEDULED).should('be.visible');
  activities.filterPublishedCheckbox.should('exist');
  activities.filterDraftCheckbox.should('exist');
  activities.filterApprovalCheckbox.should('exist');
  activities.inputThemeSearch.should('exist');
  activities.createNewThemeBtn.should('exist');
  activities.themeTitle.should('have.length', 3);

  //Check create draft themes
  activities.createNewThemeBtn.should('be.visible').click({ force: true });
  cy.wait(3000);
  activities.themeNameInput.should('be.visible').type(ADD_THEMES[2].name, { force: true });
  activities.saveTheme.click();
  cy.wait(2000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);

  activities.allThemesHeader.click();
  cy.wait(1000);
  activities.createNewThemeBtn.should('be.visible').click({ force: true });
  cy.wait(3000);
  activities.themeNameInput.should('be.visible').type(ADD_THEMES[3].name, { force: true });
  activities.saveTheme.click();
  cy.wait(2000);
  cy.contains('OK', { timeout: 5000 }).click();

  //Check draft theme filter
  activities.filterDraftCheckbox.check({ force: true });
  activities.themeTitle.should('have.length', 4);
  activities.filterDraftCheckbox.uncheck({ force: true });

  //Check publish theme filter
  activities.filterPublishedCheckbox.check({ force: true });
  activities.themeTitle.should('have.length', 1);
  activities.filterPublishedCheckbox.uncheck({ force: true });
  activities.themeTitle.should('have.length', 5);

  //Check search functionality
  activities.inputThemeSearch.clear();
  activities.inputThemeSearch.type(THEMES_LAYOUT.SEARCH);
  activities.searchThemeBtn.click();
  cy.wait(500);
  activities.themeTitle.should('have.length', 2);
  activities.inputThemeSearch.clear();
  activities.searchThemeBtn.click();
  cy.wait(500);

  //Check theme data
  activities.themeItem.each(($themeItem, index) => {
    const expectedTheme = STAFF_THEMES[index];
    cy.wrap($themeItem).within(() => {
      activities.themeTitle.should('have.text', expectedTheme.name);
      activities.themeCreatedByName.should('have.text', expectedTheme.createdBy);
      activities.themeCreatedByOrg.should('have.text', expectedTheme.createdOrg);
      activities.dropdownThemeOptions.should('include.text', expectedTheme.type);
    });
  });

  activities.themeUpdateRequestedByName.eq(0).should('have.text', STAFF_THEMES[0].createdBy);

  //Check dropdown options
  activities.dropdownThemeOptions.first().click();
  cy.get('.dropdown-menu.show').within(() => {
    cy.get('.dropdown-item').should('contain.text', THEMES_LAYOUT.REVIEW);
  });
  activities.dropdownThemeOptions.last().click();
  cy.get('.dropdown-menu.show').within(() => {
    cy.get('.dropdown-item').should('contain.text', THEMES_LAYOUT.PUBLISH);
  });
};
