import admin from 'cypress/pages/admin';
import billing from 'cypress/pages/billing';
import navigation from 'cypress/pages/navigation';
import people from 'cypress/pages/people';
import relationships from 'cypress/pages/relationships';
import reports from 'cypress/pages/reports';
import search from 'cypress/pages/search';
import {
  BASE_URLS,
  CHILD_DATA_PAST_DUE,
  NEW_PARENT_DATA,
  RECEIPT_ROWS,
  RECEIPTS_REPORT_DATA,
  TRANSACTION_ACTIONS
} from 'cypress/support/constants';

context('Receipts Report Tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('Should check receipts report', () => {
    addFamily();
    addManualPayments();
    checkReceiptsReport();
  });
});

const addFamily = () => {
  cy.login(BASE_URLS.DEFAULT);
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA_PAST_DUE.FULL_NAME);
  search.searchManageButton.first().click();

  relationships.relationshipHeaderNavigation.click();
  people.newRelationship.click();
  cy.contains('Create New').click();
  relationships.firstNameInput.type(NEW_PARENT_DATA.FIRST_NAME);
  relationships.lastNameInput.type(NEW_PARENT_DATA.LAST_NAME);
  cy.get('.multiselect-selected-text').click({ multiple: true, force: true });
  cy.get('.form-check-label').contains('Family').click({ force: true });
  relationships.relationshipSaveBtn.click();
  cy.reload();
  navigation.loadingSpinner.should('not.exist');
};

const addManualPayments = () => {
  people.transactionsHeaderNav.click();
  people.invoiceDateRangeInput.clear();
  people.invoiceDateRangeInput.type(TRANSACTION_ACTIONS.DATE_RANGE);
  cy.get('button.applyBtn.btn-primary').contains('Apply').click();

  people.invoiceNumberAmount.should('be.visible');
  people.invoiceNumberAmount.should('contain.text', TRANSACTION_ACTIONS.INVOICE_NUMBER_AMOUNT);
  people.transactionsActionsBtn.click();
  people.creditBtn.click();

  billing.creditAmountInput.clear().type(TRANSACTION_ACTIONS.CREDIT_AMOUNT_FIRST_PAYMENT);
  billing.creditReasonSelect.select(TRANSACTION_ACTIONS.CREDIT_REASON);
  admin.saveEdits.click();
  admin.saveEdits.should('not.be.visible');
  cy.containsOkAndClick();
  people.invoiceDateRangeInput.clear();
  people.invoiceDateRangeInput.type('01/01/2019 - 01/31/2020');
  cy.get('button.applyBtn.btn-primary').contains('Apply').click();
  people.invoiceNumberAmount.should('be.visible');
  people.invoiceNumberAmount.should('contain.text', TRANSACTION_ACTIONS.INVOICE_NUMBER_AMOUNT_AFTER_FIRST_PAYMENT);

  people.transactionsActionsBtn.click();
  people.creditBtn.click();
  billing.creditAmountInput.clear().type(TRANSACTION_ACTIONS.CREDIT_AMOUNT_SECOND_PAYMENT);
  billing.creditReasonSelect.select(TRANSACTION_ACTIONS.CREDIT_REASON_RECEIPT);
  billing.checkNumberInput.clear().type(TRANSACTION_ACTIONS.CHECK_NUMBER);
  admin.saveEdits.click();
  admin.saveEdits.should('not.be.visible');
  cy.containsOkAndClick();
  people.invoiceDateRangeInput.clear();
  people.invoiceDateRangeInput.type('01/01/2020 - 01/31/2020');
  cy.get('button.applyBtn.btn-primary').contains('Apply').click();
  people.invoiceNumberAmount.should('be.visible');
  people.invoiceNumberAmount.should('contain.text', TRANSACTION_ACTIONS.INVOICE_NUMBER_AMOUNT_AFTER_SECOND_PAYMENT);
};

const checkReceiptsReport = () => {
  cy.visit(BASE_URLS.RECEIPTS_REPORT);
  navigation.loadingSpinner.should('not.exist');

  //Check date filtering
  reports.updateBtn.click();
  billing.receiptType.should('have.length', 2);

  //Check table content
  reports.startDateInput.clear().type(RECEIPTS_REPORT_DATA.START_DATE);
  reports.updateBtn.click();

  RECEIPT_ROWS.forEach((expected, index) => {
    billing.receiptType.eq(index).should('have.text', expected.type);
    billing.receiptPerson.eq(index).invoke('text').should('eq', expected.person);
    billing.receiptDescription.eq(index).should('have.text', expected.description);
    billing.receiptInvoiceNumber.eq(index).invoke('text').should('eq', expected.invoice);
    billing.receiptAmount.eq(index).should('have.text', expected.amount);
  });
  billing.receiptTotalAmount.should('include.text', RECEIPTS_REPORT_DATA.TOTAL_AMOUNT);

  //Check filtering by payment type
  billing.paymentTypeSelect.parent().find('.select2-selection').click();
  cy.get('.select2-search__field').type(`${RECEIPTS_REPORT_DATA.PAYMENT_MANUAL_CASH}{enter}`);
  reports.updateBtn.click();
  billing.receiptTotalAmount.should('be.visible');
  billing.receiptDescription.should('have.length', 1);

  billing.paymentTypeSelect.parent().find('.select2-selection').click();
  cy.get('.select2-search__field').type(`${RECEIPTS_REPORT_DATA.PAYMENT_MANUAL_CHECK}{enter}`);
  reports.updateBtn.click();
  billing.receiptTotalAmount.should('be.visible');
  billing.receiptDescription.should('have.length', 2);

  billing.paymentTypeSelect.parent().find('.select2-selection').click();
  cy.get('.select2-search__field').type(
    [
      RECEIPTS_REPORT_DATA.PAYMENT_CREDIT_MEMO_CASH,
      RECEIPTS_REPORT_DATA.PAYMENT_CREDIT_MEMO_CHECK,
      RECEIPTS_REPORT_DATA.PAYMENT_CREDIT_MEMO_MANUAL_CREDIT_CARD
    ].join('{enter}') + '{enter}'
  );
  reports.updateBtn.click();
  billing.receiptTotalAmount.should('be.visible');
  billing.receiptDescription.should('have.length', 5);
};
