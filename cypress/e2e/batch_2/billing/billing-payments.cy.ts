import admin from 'cypress/pages/admin';
import billing from 'cypress/pages/billing';
import people from 'cypress/pages/people';
import {
  BANK_ACCOUNT_PAYMENT_METHODS_DATA,
  BASE_URLS,
  CREDIT_CARD_PAYMENT_METHODS_DATA,
  SEARCH_DATA
} from 'cypress/support/constants';

context('Payment methods', () => {
  it('Should add and remove payment methods from parent profile', () => {
    addAndRemovePaymentMethods();
  });
});

const addAndRemovePaymentMethods = () => {
  cy.login(BASE_URLS.PEOPLE_DIRECTORY);
  cy.wait(2000);

  people.personName.contains(SEARCH_DATA.NAME_LABEL).click();
  billing.billingTabParents.click();

  billing.addBankAccountBtn.click();
  cy.wait(1000);
  cy.get('.adyen-checkout__pm__holderName__input').type(BANK_ACCOUNT_PAYMENT_METHODS_DATA.HOLDER_NAME);
  cy.get('iframe.js-iframe').then(($iframe) => {
    const body = $iframe.contents().find('body');
    cy.wrap(body).find('#encryptedBankAccountNumber').type(BANK_ACCOUNT_PAYMENT_METHODS_DATA.NUMBER);
  });
  cy.get('iframe.js-iframe').then(($iframe) => {
    const body = $iframe.contents().find('body');
    cy.wrap(body).find('#encryptedBankLocationId').type(BANK_ACCOUNT_PAYMENT_METHODS_DATA.ROUTING);
  });
  cy.get('input[name="street"]').type(BANK_ACCOUNT_PAYMENT_METHODS_DATA.STREET);
  cy.get('input[name="houseNumberOrName"]').type(BANK_ACCOUNT_PAYMENT_METHODS_DATA.HOUSE_NUBER);
  cy.get('input[name="postalCode"]').type(BANK_ACCOUNT_PAYMENT_METHODS_DATA.POSTAL_CODE);
  cy.get('input[name="city"]').type(BANK_ACCOUNT_PAYMENT_METHODS_DATA.CITY);

  cy.get('.adyen-checkout__dropdown__button[title="Select country"]').click();
  cy.get('.adyen-checkout__dropdown__element[data-value="US"]').click();
  cy.get('.adyen-checkout__dropdown__button[title="Select state or province"]').click();
  cy.get('.adyen-checkout__dropdown__element[data-value="TX"]').click();
  cy.get('input[name="is-checking"]').check({ force: true });

  admin.saveEdits.click();
  cy.wait(2000);

  billing.bankAccount.should('include.text', BANK_ACCOUNT_PAYMENT_METHODS_DATA.NAME);
  billing.lastFourBankAccount.should('include.text', BANK_ACCOUNT_PAYMENT_METHODS_DATA.LAST_FOUR);
  billing.statusBankAccount.should('include.text', BANK_ACCOUNT_PAYMENT_METHODS_DATA.STATUS);

  billing.addCreditCardBtn.click();
  cy.wait(1000);
  cy.get('iframe.js-iframe').then(($iframe) => {
    const body = $iframe.contents().find('body');
    cy.wrap(body).find('#encryptedCardNumber').type(CREDIT_CARD_PAYMENT_METHODS_DATA.CARD_NUMBER);
  });
  cy.get('iframe.js-iframe').then(($iframe) => {
    const body = $iframe.contents().find('body');
    cy.wrap(body).find('#encryptedExpiryDate').type(CREDIT_CARD_PAYMENT_METHODS_DATA.EXPIRATION_DATE);
  });
  cy.get('iframe.js-iframe').then(($iframe) => {
    const body = $iframe.contents().find('body');
    cy.wrap(body).find('#encryptedSecurityCode').type(CREDIT_CARD_PAYMENT_METHODS_DATA.CVC);
  });

  admin.saveEdits.click();
  cy.wait(2000);

  billing.creditCard.should('include.text', CREDIT_CARD_PAYMENT_METHODS_DATA.NAME);
  billing.cardLastFourDigits.should('include.text', CREDIT_CARD_PAYMENT_METHODS_DATA.LAST_FOUR);
  billing.statusCreditCard.should('include.text', CREDIT_CARD_PAYMENT_METHODS_DATA.STATUS);

  billing.removePaymentMethodBankAccount.click();
  cy.contains('Yes, remove bank account').click();
  cy.wait(2000);
  billing.bankAccount.should('not.exist');

  billing.removePaymentMethodCreditCard.click();
  cy.contains('Yes, remove credit card').click();
  cy.wait(2000);
  billing.creditCard.should('not.exist');

  billing.addBankAccountBtn.should('exist');
  billing.addCreditCardBtn.should('exist');
};
