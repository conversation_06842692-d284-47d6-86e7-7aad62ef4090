import billing from 'cypress/pages/billing';
import navigation from 'cypress/pages/navigation';
import { ACH_CHARGEBACK, BASE_URLS } from 'cypress/support/constants';
import { StatusCodes } from 'http-status-codes';

context('Validate ACH Chargebacks report', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.ACH_REPORT_URL);
  });

  it('should verify if ACH reports are working as expected', () => {
    navigation.loadingSpinner.should('not.exist');
    billing.startDateChargebackInput.clear().type(ACH_CHARGEBACK.START_DATE);
    billing.updateChargebacksBtton.click();

    billing.chargebackReportTable.find('tr').then(($rows) => {
      if ($rows.length !== ACH_CHARGEBACK.MIN_REPORT_ROWS) {
        billing.actionDropdownButon.first().click();
        billing.actionDropmenuItems
          .first()
          .children('a')
          .should(($a) => {
            expect($a.eq(0)).to.contain(ACH_CHARGEBACK.VIEW_INVOICE);
            expect($a.eq(1)).to.contain(ACH_CHARGEBACK.CHARGE_FAMILY);
            expect($a.eq(2)).to.contain(ACH_CHARGEBACK.MARK_RESOLVED);
          });
        billing.actionDropdownButon.first().click();
        billing.billingTableRows
          .children('td')
          .nextUntil(`[data-cy="actions-column"]`)
          .each(($el) => {
            expect($el.text()).not.eq('');
          });

        billing.showResolvedCheckbox.should('be.not.checked').then(() => {
          billing.chargebackStatusColumn.should(($el) => {
            expect($el.text()).not.be.eq('Resolved');
          });
        });

        cy.request('GET', ACH_CHARGEBACK.ADYEN_API + epochDate()).should((response) => {
          expect(response.status).to.eq(StatusCodes.OK);
        });
      }
    });
  });
});

const epochDate = (): number => {
  const todaysDate = new Date();
  return Math.floor(todaysDate.getTime() / 1000);
};
