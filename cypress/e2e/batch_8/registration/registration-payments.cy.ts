import registration from 'cypress/pages/registration';
import {
  BASE_URLS,
  CHILD_CARE_DATA,
  CHILD_DATA,
  COUPONS_DISCOUNT,
  PAYMENT_BANK_ACCOUNT,
  PAYMENT_CARD_DATA,
  TUTOR_DATA,
  REGISTRATION_FLOW_TYPES
} from '../../../support/constants';
import { registrationProcess } from './utils';

context('registration flow for admins, payments and warnings', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(`/registration?orgId=${CHILD_CARE_DATA.ORG_ID}`, 'Admin');
  });

  it("payment by credit card", () => {
    let callCount = 0;
    registrationProcess(
      CHILD_DATA,
      TUTOR_DATA,
      CHILD_CARE_DATA,
      COUPONS_DISCOUNT,
      false,
      REGISTRATION_FLOW_TYPES.ADMIN
    );
    cy.window().then((win) => {
      const originalMethod = win.Meteor.callAsync; //its async call now
      win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
          if (methodName === 'approveRegistration') {
              callCount++;
              return originalMethod.apply(win.Meteor, [methodName, ...args]);
          }
          return originalMethod.apply(win.Meteor, [methodName, ...args]);
      });
      registration.payNowBtn.click();
      registration.disabledPayBtn.should('exist');
      registration.creditCardRadio.eq(0).click();
      cy.wait(3000);

      registration.creditCardInput
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedCardNumber")
            .click()
            .type(PAYMENT_CARD_DATA.CARD_NUMBER);
        });

      registration.expirationDateInput
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedExpiryDate")
            .click()
            .type(PAYMENT_CARD_DATA.EXPIRATION_DATE);
        });

      registration.securityCodeInput
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedSecurityCode")
            .click()
            .type(PAYMENT_CARD_DATA.CVC);
        });

      // Make sure pay now can't be clicked multiple times.
      registration.payNowBtnMModal.eq(0).click({force: true});
      cy.wait(500);
      registration.payNowBtnMModal.should('be.disabled');
      registration.payNowBtnMModal.eq(0).click({force: true});
      cy.wait(500);

      // Assert that method is still only called once
      cy.wrap(null).should(() => {
        expect(callCount).to.equal(1);
      });
    });

    //TODO Disabled because of lambda issue on pipeline
    //cy.url().should("contain", BASE_URLS.REGISTRATION_COMPLETED, { timeout: 15000 });
  });

  it("payment by bank account", () => {
    let callCount = 0;
    registrationProcess(
      CHILD_DATA,
      TUTOR_DATA,
      CHILD_CARE_DATA,
      COUPONS_DISCOUNT,
      false,
      REGISTRATION_FLOW_TYPES.ADMIN
    );

    cy.window().then((win) => {
      const originalMethod = win.Meteor.callAsync;
      win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
          if (methodName === 'approveRegistration') {
              callCount++;
              return originalMethod.apply(win.Meteor, [methodName, ...args]);
          }
          return originalMethod.apply(win.Meteor, [methodName, ...args]);
      });
      registration.payNowBtn.click();
      registration.disabledPayBtn.should('exist');
      registration.bankAccountRadio.eq(0).click();
      cy.wait(3000);
      cy.get(
        ".adyen-checkout__pm__holderName > .adyen-checkout__label > .adyen-checkout__input-wrapper > .adyen-checkout__input"
      ).as("bankHolder");
      cy.get("@bankHolder").type(PAYMENT_BANK_ACCOUNT.NAME);

      cy.get("[data-cse=encryptedBankAccountNumber]")
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedBankAccountNumber")
            .click()
            .type(PAYMENT_BANK_ACCOUNT.NUMBER);
        });

      cy.get("[data-cse=encryptedBankLocationId]")
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedBankLocationId")
            .click()
            .type(PAYMENT_BANK_ACCOUNT.ROUTING);
        });

      registration.certifyAccount.eq(0).click();
      cy.get('input[name="street"]').click().type(TUTOR_DATA.PHYSICAL_ADDRESS);
      cy.get('input[name="houseNumberOrName"]')
        .click()
        .type(TUTOR_DATA.HOUSE_NUMBER);
      cy.get('input[name="postalCode"]').click().type(TUTOR_DATA.ZIP_CODE);
      cy.get('input[name="city"]').click().type(TUTOR_DATA.CITY);

      cy.contains("Select country").click();
      cy.get("ul").contains("United States").click();

      cy.contains("Select state or province").click();
      cy.get("ul").contains("Texas").click();

      // Make sure pay now can't be clicked multiple times.
      registration.payNowBtnMModal.eq(0).click({force: true});
      cy.wait(500);
      registration.payNowBtnMModal.should('be.disabled');
      registration.payNowBtnMModal.eq(0).click({force: true});
      cy.wait(500);

      // Assert that method is only called once
      cy.wrap(null).should(() => {
        expect(callCount).to.equal(1);
      });
    });

    //TODO Disabled because of lambda issue on pipeline
    //cy.url().should("contain", BASE_URLS.REGISTRATION_COMPLETED, { timeout: 15000 });
  });

  it("no payment required", () => {
    let callCount = 0;
    registrationProcess(
      CHILD_DATA,
      TUTOR_DATA,
      CHILD_CARE_DATA,
      COUPONS_DISCOUNT,
      true,
      REGISTRATION_FLOW_TYPES.ADMIN
    );

    cy.window().then((win) => {
      const originalMethod = win.Meteor.callAsync;
      win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
          if (methodName === 'approveRegistration') {
              callCount++;
              return originalMethod.apply(win.Meteor, [methodName, ...args]);
          }
          return originalMethod.apply(win.Meteor, [methodName, ...args]);
      });
      // Make sure submit can't be clicked multiple times.
      registration.submitBtn.click({ force: true });
      cy.wait(500);
      registration.disabledPayBtn.should('exist');
      registration.disabledPayBtn.click();
      cy.wait(500);

      // Assert that method is only called once
      cy.wrap(null).should(() => {
        expect(callCount).to.equal(1);
      });
    });

    //TODO Disabled because of lambda issue on pipeline
    //cy.url().should("contain", BASE_URLS.REGISTRATION_COMPLETED, { timeout: 15000 });
  });
});

context('registration flow for parents, payments and warnings', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(`/registration?orgId=${CHILD_CARE_DATA.ORG_ID}&personId=${TUTOR_DATA.ID}`, 'Parent');
  });

  it("payment by credit card", () => {
    let callCount = 0;
    registrationProcess(
      CHILD_DATA,
      TUTOR_DATA,
      CHILD_CARE_DATA,
      COUPONS_DISCOUNT,
      false,
      REGISTRATION_FLOW_TYPES.PARENT
    );
    cy.window().then((win) => {
      const originalMethod = win.Meteor.callAsync;
      win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
          if (methodName === 'updateFamily') {
              callCount++;
              return originalMethod.apply(win.Meteor, [methodName, ...args]);
          }
          return originalMethod.apply(win.Meteor, [methodName, ...args]);
      });
      registration.payNowBtn.click();
      registration.disabledPayBtn.should('exist');
      registration.creditCardRadio.eq(0).click();
      cy.wait(3000);

      registration.creditCardInput
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedCardNumber")
            .click()
            .type(PAYMENT_CARD_DATA.CARD_NUMBER);
        });

      registration.expirationDateInput
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedExpiryDate")
            .click()
            .type(PAYMENT_CARD_DATA.EXPIRATION_DATE);
        });

      registration.securityCodeInput
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedSecurityCode")
            .click()
            .type(PAYMENT_CARD_DATA.CVC);
        });

      // Make sure pay now can't be clicked multiple times.
      registration.payNowBtnMModal.eq(0).click({force: true});
      cy.wait(500);
      registration.payNowBtnMModal.should('be.disabled');
      registration.payNowBtnMModal.eq(0).click({force: true});
      cy.wait(500);
      // Assert that the method was called only once
      cy.wrap(null).should(() => {
        expect(callCount).to.equal(1);
      });
    });

    //TODO Disabled because of lambda issue on pipeline
    //cy.url().should("contain", BASE_URLS.REGISTRATION_COMPLETED, { timeout: 15000 });
  });

  it("payment by bank account", () => {
    let callCount = 0;
    registrationProcess(
      CHILD_DATA,
      TUTOR_DATA,
      CHILD_CARE_DATA,
      COUPONS_DISCOUNT,
      false,
      REGISTRATION_FLOW_TYPES.PARENT
    );

    cy.window().then((win) => {
      const originalMethod = win.Meteor.callAsync;
      win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
          if (methodName === 'updateFamily') {
              callCount++;
              return originalMethod.apply(win.Meteor, [methodName, ...args]);
          }
          return originalMethod.apply(win.Meteor, [methodName, ...args]);
      });
      registration.payNowBtn.click();
      registration.disabledPayBtn.should('exist');
      registration.bankAccountRadio.eq(0).click();
      cy.wait(3000);
      cy.get(
        ".adyen-checkout__pm__holderName > .adyen-checkout__label > .adyen-checkout__input-wrapper > .adyen-checkout__input"
      ).as("bankHolder");
      cy.get("@bankHolder").type(PAYMENT_BANK_ACCOUNT.NAME);

      cy.get("[data-cse=encryptedBankAccountNumber]")
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedBankAccountNumber")
            .click()
            .type(PAYMENT_BANK_ACCOUNT.NUMBER);
        });

      cy.get("[data-cse=encryptedBankLocationId]")
        .find("iframe")
        .then(($element) => {
          const $body = $element.contents().find("body");

          let input = cy.wrap($body);
          input
            .find("#encryptedBankLocationId")
            .click()
            .type(PAYMENT_BANK_ACCOUNT.ROUTING);
        });

      registration.certifyAccount.eq(0).click();
      cy.get('input[name="street"]').click().type(TUTOR_DATA.PHYSICAL_ADDRESS);
      cy.get('input[name="houseNumberOrName"]')
        .click()
        .type(TUTOR_DATA.HOUSE_NUMBER);
      cy.get('input[name="postalCode"]').click().type(TUTOR_DATA.ZIP_CODE);
      cy.get('input[name="city"]').click().type(TUTOR_DATA.CITY);

      cy.contains("Select country").click();
      cy.get("ul").contains("United States").click();

      cy.contains("Select state or province").click();
      cy.get("ul").contains("Texas").click();

      // Make sure pay now can't be clicked multiple times.
      registration.payNowBtnMModal.eq(0).click({force: true});
      cy.wait(500);
      registration.payNowBtnMModal.should('be.disabled');
      registration.payNowBtnMModal.eq(0).click({force: true});
      cy.wait(500);

      // Assert that method is only called once
      cy.wrap(null).should(() => {
        expect(callCount).to.equal(1);
      });
    });

    //TODO Disabled because of lambda issue on pipeline
    //cy.url().should("contain", BASE_URLS.REGISTRATION_COMPLETED, { timeout: 15000 });
  });

  it("no payment required", () => {
    let callCount = 0;
    registrationProcess(
      CHILD_DATA,
      TUTOR_DATA,
      CHILD_CARE_DATA,
      COUPONS_DISCOUNT,
      true,
      REGISTRATION_FLOW_TYPES.PARENT
    );

    cy.window().then((win) => {
      const originalMethod = win.Meteor.callAsync;
      win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
          if (methodName === 'updateFamily') {
              callCount++;
              return originalMethod.apply(win.Meteor, [methodName, ...args]);
          }
          return originalMethod.apply(win.Meteor, [methodName, ...args]);
      });

      // Make sure submit can't be clicked multiple times.
      registration.submitBtn.click({ force: true });
      registration.disabledPayBtn.should('exist');
      registration.disabledPayBtn.click();
      cy.wait(500);

      // Assert that method is only called once
      cy.wrap(null).should(() => {
        expect(callCount).to.equal(1);
      });
    });
    //TODO Disabled because of lambda issue on pipeline 
    //cy.url().should("contain", BASE_URLS.REGISTRATION_COMPLETED, { timeout: 15000 });
  });
});
