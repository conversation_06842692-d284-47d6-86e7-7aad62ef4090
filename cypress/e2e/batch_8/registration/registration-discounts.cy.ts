import people from 'cypress/pages/people';
import {
  BASE_URLS,
  CHILD_CARE_DATA,
  CHILD_DATA_SCENARIOS,
  DISTRICT,
  EMPLOYEE_ID,
  EMPLOYEE_ID_DISCOUNT,
  LOGIN_DATA,
  PARENT_LOGIN_DATA,
  PAYMENT_CARD_DATA,
  REGISTER_CHILD_DATA,
  REGISTRATION_PROGRAM,
  SUBSIDY,
  TUTOR_DATA_SCENARIOS
} from '../../../support/constants';
import {
  paymentByCreditCard,
  registrationProcessAddProgram,
  registrationProcessChildParentProgram,
  useDisctrictEmail,
  useEmployeeId,
  useSubsidy
} from './utils';
import dashboard from 'cypress/pages/dashboard';
import billingAdmin from 'cypress/pages/billing-admin';
import reports from 'cypress/pages/reports';
import overview from 'cypress/pages/overview';
import relationships from 'cypress/pages/relationships';

context('registration full flow', () => {
  it('Check child, family, invoice, billing and scheduling after successful registration', () => {
    cy.setDefaultDatabase();
    cy.visit(`/registration?orgId=${CHILD_DATA_SCENARIOS.ORG_ID}`);
    registrationProcessChildParentProgram(CHILD_DATA_SCENARIOS, TUTOR_DATA_SCENARIOS);

    paymentByCreditCard(PAYMENT_CARD_DATA);

    /*cy.login(BASE_URLS.PEOPLE_DIRECTORY);
    
    //Check Family created
    people.personName.eq(0).contains(TUTOR_DATA_SCENARIOS.FIRST_NAME).click();
    people.firstNameInput.should("have.value", TUTOR_DATA_SCENARIOS.FIRST_NAME);
    people.lastNameInput.should("have.value", TUTOR_DATA_SCENARIOS.LAST_NAME);
    people.profileType.should("include.text", TUTOR_DATA_SCENARIOS.TYPE);
    people.inputEmailAddress.should("have.value", TUTOR_DATA_SCENARIOS.EMAIL);

    people.personPhonePrimary.find('input.form-control.form-control-solid.phone').should("have.value", TUTOR_DATA_SCENARIOS.PHONE);
    people.associatedParentTab.should("include.text", CHILD_DATA_SCENARIOS.INITIALS);
    people.associatedParentTab.click();
    cy.wait(2000);

    //Check Child created
    people.associatedParentTab.should("include.text", TUTOR_DATA_SCENARIOS.INITIALS);
    people.profileHeaderNavigation.click();
    people.firstNameInput.should("have.value", CHILD_DATA_SCENARIOS.FIRST_NAME);
    people.lastNameInput.should("have.value", CHILD_DATA_SCENARIOS.LAST_NAME);

    people.profileType.should("include.text", CHILD_DATA_SCENARIOS.TYPE);
    people.childGender.should("have.value", CHILD_DATA_SCENARIOS.GENDER);
    people.childGrade.should("have.value", CHILD_DATA_SCENARIOS.GRADE);
    people.personBirthday.find('input.form-control.form-control-solid.profile-date-input').should("have.value", CHILD_DATA_SCENARIOS.BIRTH);

    //Check scheduled plan
    people.schedulingTab.click();
    people.priorSchedules.click();
    people.linkedPlan.should("include.text", CHILD_DATA_SCENARIOS.LINKED_PLAN);
    people.daysScheduled.should("include.text", CHILD_DATA_SCENARIOS.DAYS_SCHEDULED);
    billing.billingTabParents.click();
    billing.planDescription.should("include.text", CHILD_DATA_SCENARIOS.LINKED_PLAN);

    //Check invoice created
    people.transactionsHeaderNav.click();
    people.invoiceNumberChild.should("include.text", CHILD_DATA_SCENARIOS.FIRST_NAME);
    people.invoiceNumberDescription.should("include.text", CHILD_DATA_SCENARIOS.PLAN_DESCRIPTION);
    people.invoiceNumberAmount.should("include.text", CHILD_DATA_SCENARIOS.AMOUNT);*/
  });

  it('Check reports after successful registration using subsidy', () => {
    cy.setDefaultDatabase();
    cy.visit(`/registration?orgId=${CHILD_DATA_SCENARIOS.ORG_ID}`);
    registrationProcessChildParentProgram(CHILD_DATA_SCENARIOS, TUTOR_DATA_SCENARIOS);

    useSubsidy(SUBSIDY);

    cy.login(BASE_URLS.BILLING_ADMIN);

    //Check registration status widget
    dashboard.dashboardItemLabel.contains(CHILD_DATA_SCENARIOS.FIRST_NAME).click();

    //Check registration status report
    billingAdmin.tutorName.should('include.text', TUTOR_DATA_SCENARIOS.FULL_NAME);
    billingAdmin.childName.should('include.text', CHILD_DATA_SCENARIOS.FULL_NAME);
    billingAdmin.birthday.should('include.text', CHILD_DATA_SCENARIOS.BIRTH);
    billingAdmin.orgName.should('include.text', LOGIN_DATA.ORGNAME);
    billingAdmin.subsidyProgramName.should('include.text', SUBSIDY.AGENCY);
    billingAdmin.voucherName.should('include.text', SUBSIDY.VOUCHER);

    //Review registration
    billingAdmin.actionBtn.click();
    billingAdmin.review.click();
    billingAdmin.amountVoucher.type(SUBSIDY.AMOUNT);
    billingAdmin.selectReimbursementType.select(SUBSIDY.REIMBURSEMENT);
    billingAdmin.approveBtn.click();
    cy.contains('OK', { timeout: 30000 }).should('be.visible').click();

    cy.visit(BASE_URLS.ENROLLMENT_STATUS);

    //Check enrollment status report
    reports.updateBtn.click();
    reports.enrolledChildName.eq(0).should('include.text', CHILD_DATA_SCENARIOS.FULL_NAME);
    reports.orgName.eq(0).should('include.text', LOGIN_DATA.ORGNAME);
  });

  it('Check reports after successful registration using district employee email', () => {
    cy.setDefaultDatabase();
    cy.visit(`/registration?orgId=${CHILD_DATA_SCENARIOS.ORG_ID}`);
    registrationProcessChildParentProgram(CHILD_DATA_SCENARIOS, TUTOR_DATA_SCENARIOS);

    useDisctrictEmail(DISTRICT);

    cy.login(BASE_URLS.BILLING_ADMIN);

    //Check district employee review widget
    billingAdmin.overviewValues
      .contains('District Employee Review')
      .parents('.card')
      .within(() => {
        dashboard.dashboardItemLabel.should('include.text', CHILD_DATA_SCENARIOS.FIRST_NAME);
        overview.footerBtn.contains('All Recent Enrollments').click();
      });

    //Check district employee review report
    reports.updateBtn.click();
    billingAdmin.tutorName.should('include.text', TUTOR_DATA_SCENARIOS.FULL_NAME);
    billingAdmin.childName.should('include.text', CHILD_DATA_SCENARIOS.FULL_NAME);
    billingAdmin.birthday.should('include.text', CHILD_DATA_SCENARIOS.BIRTH);
    billingAdmin.orgName.should('include.text', LOGIN_DATA.ORGNAME);
    billingAdmin.districtEmail.should('include.text', DISTRICT.EMAIL);

    //Review registration
    billingAdmin.actionBtn.click();
    billingAdmin.review.click();
    billingAdmin.approveDiscountDistrict.click();
    billingAdmin.approveBtn.click();
    cy.contains('OK', { timeout: 30000 }).should('be.visible').click();

    cy.visit(BASE_URLS.ENROLLMENT_STATUS);

    //Check enrollment status report
    reports.updateBtn.click();
    reports.enrolledChildName.eq(0).should('include.text', CHILD_DATA_SCENARIOS.FULL_NAME);
    reports.orgName.eq(0).should('include.text', LOGIN_DATA.ORGNAME);
  });

  it('Check registration using employeeID', () => {
    cy.setDefaultDatabase();
    cy.visit(`/registration?orgId=${CHILD_DATA_SCENARIOS.ORG_ID}`);
    registrationProcessChildParentProgram(CHILD_DATA_SCENARIOS, TUTOR_DATA_SCENARIOS);

    useEmployeeId(EMPLOYEE_ID);
  });

  it('Check registration using employeeID discount as a parent', () => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.PEOPLE_DIRECTORY, 'Parent');
    people.newChildBtn.should('be.visible');
    cy.visit(`/registration?orgId=${CHILD_CARE_DATA.ORG_ID}&personId=${REGISTER_CHILD_DATA.PARENT_ID}`);
    registrationProcessAddProgram(CHILD_DATA_SCENARIOS);

    useEmployeeId(EMPLOYEE_ID_DISCOUNT);
    //finishPayRegistration();
  });

  /*it('Check created child as a parent', () => {
    cy.login(BASE_URLS.PEOPLE_DIRECTORY, 'Parent');
    checkCreatedChild();
  });*/
});

export const checkCreatedChild = () => {
  cy.wait(1000);
  people.peopleItem.contains('[data-cy="person-name"]', CHILD_DATA_SCENARIOS.FULL_NAME).click();
  people.firstNameLastName.should('include.text', CHILD_DATA_SCENARIOS.FULL_NAME);
  relationships.relationshipHeaderNavigation.click();
  people.relationshipFullName.should('include.text', PARENT_LOGIN_DATA.FULLNAME);
  people.programsHeaderNavigation.click();
  people.enrolledProgram.each(($card) => {
    cy.wrap($card).within(() => {
      const dataId = $card.attr('data-id');
      cy.get('.col-2').eq(3).should('contain', 'Days').should('contain', 'M');
      people.editEnrolledProgramBtn(dataId).should('be.visible').click();
    });
  });

  people.editEnrolledProgramForm.within(() => {
    cy.get('h4').should('contain', REGISTRATION_PROGRAM.PLAN);
  });
};
