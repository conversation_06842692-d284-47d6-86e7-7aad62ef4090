import registration from 'cypress/pages/registration';
import { CHILD_CARE_DATA, CHILD_DATA, BASE_URLS, COUPONS_DISCOUNT } from '../../../support/constants';
import { parentDiyProcess } from './utils';

context('parent diy add programs tab, payments and warnings', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.DEFAULT, 'Parent');
    cy.visit(`/people/${CHILD_CARE_DATA.ID}#programs`);
  });
    it("payment by credit card", () => {
        let callCount = 0;
        parentDiyProcess(
            CHILD_DATA,
            CHILD_CARE_DATA,
            COUPONS_DISCOUNT,
            false
        );
        cy.window().then((win) => {
            const originalMethod = win.Meteor.callAsync;
            win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
                if (methodName === 'updateFamily') {
                    callCount++;
                    return originalMethod.apply(win.Meteor, [methodName, ...args]);
                }
                return originalMethod.apply(win.Meteor, [methodName, ...args]);
            });

            registration.payNowBtn.click();
            cy.get(`[data-cy=payment-select]`).select('Credit Card');
            cy.wait(500)

            // Make sure pay now can't be clicked multiple times.
            registration.payNowBtnMModal.eq(0).click({force: true});
            registration.payNowBtnMModal.should('be.disabled');
            registration.payNowBtnMModal.eq(0).click({force: true});

            // Assert that method is still only called once
            cy.wrap(null).should(() => {
                expect(callCount).to.equal(1);
            });
        });
    });

    it("payment by bank account", () => {
        let callCount = 0;
        parentDiyProcess(
            CHILD_DATA,
            CHILD_CARE_DATA,
            COUPONS_DISCOUNT,
            false
        );
        cy.window().then((win) => {
            const originalMethod = win.Meteor.callAsync;
            win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
                if (methodName === 'updateFamily') {
                    callCount++;
                    expect(args[0].payment_source).to.equal('bank_account');
                    return originalMethod.apply(win.Meteor, [methodName, ...args]);
                }
                return originalMethod.apply(win.Meteor, [methodName, ...args]);
            });
    
            registration.payNowBtn.click();
            cy.get(`[data-cy=payment-select]`).select('Bank Account');
            cy.wait(500)
    
            // Make sure pay now can't be clicked multiple times.
            registration.payNowBtnMModal.eq(0).click({force: true});
            cy.wait(500);
            registration.payNowBtnMModal.should('be.disabled');
            registration.payNowBtnMModal.eq(0).click({force: true});
    
            // Assert that method is only called once
            cy.wrap(null).should(() => {
                expect(callCount).to.equal(1);
            });
        });
    });

    it("no payment required", () => {
        let callCount = 0;
        parentDiyProcess(
            CHILD_DATA,
            CHILD_CARE_DATA,
            COUPONS_DISCOUNT,
            true
        );
        cy.window().then((win) => {
            const originalMethod = win.Meteor.callAsync;
            win.Meteor.callAsync = cy.stub().callsFake((methodName, ...args) => {
                if (methodName === 'updateFamily') {
                    callCount++;
                    return originalMethod.apply(win.Meteor, [methodName, ...args]);
                }
                return originalMethod.apply(win.Meteor, [methodName, ...args]);
            });

            // Ensure the submit button can't be clicked multiple times
            registration.submitBtn.click({force: true});
            cy.wait(500);
            registration.disabledPayBtn.should('exist');
            registration.disabledPayBtn.click({force: true});

            // Assert that method is still only called once
            cy.wrap(null).should(() => {
                expect(callCount).to.equal(1);
            });
        });
    });
});
