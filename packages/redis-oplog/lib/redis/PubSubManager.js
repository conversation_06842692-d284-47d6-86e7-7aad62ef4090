import { getRedisListener, getRedisPusher } from "./getRedisClient";
import Config from "../config";

/**
 * Manages communication with Redis
 * Unifies all libraries that use this
 */
export default class PubSubManager {
  constructor() {
    this.channelHandlers = {};
    this.queue = new Meteor._AsynchronousQueue();

    this.listener = getRedisListener();
    this.pusher = getRedisPusher();

    this._initMessageListener();
  }

  /**
   * Pushes to Redis
   * @param {string} channel
   * @param {object} message
   */
  async publish(channel, message) {
    await this.pusher.publish(channel, EJSON.stringify(message));
  }

  /**
   * @param {string} channel
   * @param {function} handler
   */
  subscribe(channel, handler) {
    this.queue.queueTask(async () => {
      if (!this.channelHandlers[channel]) {
        await this._initChannel(channel);
      }

      this.channelHandlers[channel].push(handler);
    });
  }

  /**
   * @param {string} channel
   * @param {function} handler
   */
  unsubscribe(channel, handler) {
    this.queue.queueTask(async () => {
      if (!this.channelHandlers[channel]) {
        return;
      }

      this.channelHandlers[channel] = this.channelHandlers[channel].filter(
        (_handler) => {
          return _handler !== handler;
        }
      );

      if (this.channelHandlers[channel].length === 0) {
        await this._destroyChannel(channel);
      }
    });
  }

  /**
   * Initializes listening for redis messages
   * @private
   */
  _initMessageListener() {
    const self = this;

    this.listener.on(
      "message",
      Meteor.bindEnvironment(async function (channel, _message) {
        // Log when messages are received
        const timestamp = new Date().toISOString();
        const prefix = Config.logPrefix || 'RedisOplog';
        const level = Config.logLevel || 'DEBUG';
        
        // Try to parse the message to log useful info
        try {
          const messagePreview = _message.length > 100 ? 
            _message.substring(0, 97) + '...' : 
            _message;
          console.log(`[${timestamp}] [${prefix}] [${level}]: Redis received message on channel: "${channel}" with content: ${messagePreview}`);
        } catch (e) {
          console.log(`[${timestamp}] [${prefix}] [${level}]: Redis received raw message on channel: "${channel}"`);
        }
        
        if (self.channelHandlers[channel]) {
          const message = EJSON.parse(_message);
          for (const channelHandler of self.channelHandlers[channel]) {
            await channelHandler(message);
          }
        }
      })
    );
  }

  /**
   * @param channel
   * @private
   */
  async _initChannel(channel) {
    await this.listener.subscribe(channel);

    this.channelHandlers[channel] = [];
  }

  /**
   * @param channel
   * @private
   */
  async _destroyChannel(channel) {
    await this.listener.unsubscribe(channel);

    delete this.channelHandlers[channel];
  }
}
