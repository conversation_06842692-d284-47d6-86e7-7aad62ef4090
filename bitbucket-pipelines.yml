image: node:20.18-bullseye
definitions:
  steps:
    - step: &run-jest-tests
        name: "Run Jest Tests"
        size: 4x
        services:
          - mongo
        caches:
          - node
        script:
          - echo "Running Jest Tests"
          - cp $BITBUCKET_CLONE_DIR/__tests__/settings-unit-tests.json $BITBUCKET_CLONE_DIR/meteor.development.local.settings
          - curl https://install.meteor.com/?release=3.0.4 | sh
          - export METEOR_ALLOW_SUPERUSER=true
          - npm install --unsafe-perm --production=false
          - meteor npm rebuild
          - meteor npm run jest
          - node scripts/generate-coverage-results.js
          # no more requirement of mocha test cases so commenting it.
          # - MONGO_URL='' NODE_ENV=development meteor npm test
          # - node tests/test-setup.js && npm run test
        artifacts:
          - meteor.development.local.settings
          - coverage-results.csv
    - step: &run-tests-part-1
          name: "Run Cypress Tests - Registration section"
          size: 4x
          services:
            - mongo
          caches:
            - node
          script:
            - chmod +x bitbucket_mongo_data.sh
            - chmod +x monitor.sh
            - chmod +x run-cypress.sh
            - chmod +x test-pipeline-setup.sh
            - ./monitor.sh &
            - MONITOR_PID=$!
            - echo "Running Cypress Tests - Part 1"
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install --unsafe-perm --production=false
            - meteor npm rebuild
            - ./test-pipeline-setup.sh
            - apt-get update
            - echo "Y" | apt-get install libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
            - npm run test:e2e:ci:part1
            - npm run generate_json_cypress_reports
            - npm run generate_html_cypress_report
            - kill $MONITOR_PID
            - cat resource-usage.log
            - cp $BITBUCKET_CLONE_DIR/tests/settings-mocha.json $BITBUCKET_CLONE_DIR/meteor.development.local.settings
          artifacts:
            - meteor.development.local.settings
            - cypress/screenshots**
            - cypress/mochawesome-report**
            - resource-usage.log
    - step: &run-tests-part-2
          name: "Run Cypress Tests - Billing section"
          size: 4x
          services:
            - mongo
          caches:
            - node
          script:
            - chmod +x bitbucket_mongo_data.sh
            - chmod +x monitor.sh
            - chmod +x run-cypress.sh
            - chmod +x test-pipeline-setup.sh
            - ./monitor.sh &
            - MONITOR_PID=$!
            - echo "Running Cypress Tests - Part 2"
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install --unsafe-perm --production=false
            - meteor npm rebuild
            - ./test-pipeline-setup.sh
            - apt-get update
            - echo "Y" | apt-get install libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
            - npm run test:e2e:ci:part2
            - npm run generate_json_cypress_reports
            - npm run generate_html_cypress_report
            - kill $MONITOR_PID
            - cat resource-usage.log
            - cp $BITBUCKET_CLONE_DIR/tests/settings-mocha.json $BITBUCKET_CLONE_DIR/meteor.development.local.settings
          artifacts:
            - meteor.development.local.settings
            - cypress/screenshots**
            - cypress/mochawesome-report**
            - resource-usage.log
    - step: &run-tests-part-3
          name: "Run Cypress Tests - Admin & Content sections"
          size: 4x
          services:
            - mongo
          caches:
            - node
          script:
            - chmod +x bitbucket_mongo_data.sh
            - chmod +x monitor.sh
            - chmod +x run-cypress.sh
            - chmod +x test-pipeline-setup.sh
            - ./monitor.sh &
            - MONITOR_PID=$!
            - echo "Running Cypress Tests - Part 3"
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install --unsafe-perm --production=false
            - meteor npm rebuild
            - ./test-pipeline-setup.sh
            - apt-get update
            - echo "Y" | apt-get install libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
            - npm run test:e2e:ci:part3
            - npm run generate_json_cypress_reports
            - npm run generate_html_cypress_report
            - kill $MONITOR_PID
            - cat resource-usage.log
            - cp $BITBUCKET_CLONE_DIR/tests/settings-mocha.json $BITBUCKET_CLONE_DIR/meteor.development.local.settings
          artifacts:
            - meteor.development.local.settings
            - cypress/screenshots**
            - cypress/mochawesome-report**
            - resource-usage.log
    - step: &run-tests-part-4
          name: "Run Cypress Tests - Reports & Header sections"
          size: 4x
          services:
            - mongo
          caches:
            - node
          script:
            - chmod +x bitbucket_mongo_data.sh
            - chmod +x monitor.sh
            - chmod +x run-cypress.sh
            - chmod +x test-pipeline-setup.sh
            - ./monitor.sh &
            - MONITOR_PID=$!
            - echo "Running Cypress Tests - Part 4"
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install --unsafe-perm --production=false
            - meteor npm rebuild
            - ./test-pipeline-setup.sh
            - apt-get update
            - echo "Y" | apt-get install libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
            - npm run test:e2e:ci:part4
            - npm run generate_json_cypress_reports
            - npm run generate_html_cypress_report
            - kill $MONITOR_PID
            - cat resource-usage.log
            - cp $BITBUCKET_CLONE_DIR/tests/settings-mocha.json $BITBUCKET_CLONE_DIR/meteor.development.local.settings
          artifacts:
            - meteor.development.local.settings
            - cypress/screenshots**
            - cypress/mochawesome-report**
            - resource-usage.log
    - step: &run-tests-part-5
          name: "Run Cypress Tests - My Site & Time sections"
          size: 4x
          services:
            - mongo
          caches:
            - node
          script:
            - chmod +x bitbucket_mongo_data.sh
            - chmod +x monitor.sh
            - chmod +x run-cypress.sh
            - chmod +x test-pipeline-setup.sh
            - ./monitor.sh &
            - MONITOR_PID=$!
            - echo "Running Cypress Tests - Part 5"
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install --unsafe-perm --production=false
            - meteor npm rebuild
            - ./test-pipeline-setup.sh
            - apt-get update
            - echo "Y" | apt-get install libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
            - npm run test:e2e:ci:part5
            - npm run generate_json_cypress_reports
            - npm run generate_html_cypress_report
            - kill $MONITOR_PID
            - cat resource-usage.log
            - cp $BITBUCKET_CLONE_DIR/tests/settings-mocha.json $BITBUCKET_CLONE_DIR/meteor.development.local.settings
          artifacts:
            - meteor.development.local.settings
            - cypress/screenshots**
            - cypress/mochawesome-report**
            - resource-usage.log
    - step: &run-tests-part-6
          name: "Run Cypress Tests - Manage & Dashboard sections"
          size: 4x
          services:
            - mongo
          caches:
            - node
          script:
            - chmod +x bitbucket_mongo_data.sh
            - chmod +x monitor.sh
            - chmod +x run-cypress.sh
            - chmod +x test-pipeline-setup.sh
            - ./monitor.sh &
            - MONITOR_PID=$!
            - echo "Running Cypress Tests - Part 6"
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install --unsafe-perm --production=false
            - meteor npm rebuild
            - ./test-pipeline-setup.sh
            - apt-get update
            - echo "Y" | apt-get install libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb
            - npm run test:e2e:ci:part6
            - npm run generate_json_cypress_reports
            - npm run generate_html_cypress_report
            - kill $MONITOR_PID
            - cat resource-usage.log
            - cp $BITBUCKET_CLONE_DIR/tests/settings-mocha.json $BITBUCKET_CLONE_DIR/meteor.development.local.settings
          artifacts:
            - meteor.development.local.settings
            - cypress/screenshots**
            - cypress/mochawesome-report**
            - resource-usage.log
    - step: &deploy-test
        name: "Deploy to Test"
        size: 4x
        caches:
          - node
        script:
          - cp .deploy/settings-test.json .deploy/settings.json
          - cp .deploy/mup-test.js .deploy/mup.js
          - echo "Removing files from .ebextensions for QA deployment..."
          - cd .deploy
          - sed -i "s|<ACCOUNTING_EMAIL>|$ACCOUNTING_EMAIL|g" settings.json
          - sed -i "s|<BILLING_ERRORS_EMAIL>|$BILLING_ERRORS_EMAIL|g" settings.json
          - sed -i "s|<GROUP_SYNC_ERRORS_EMAIL>|$GROUP_SYNC_ERRORS_EMAIL|g" settings.json
          - sed -i "s|<API_BYPASS_AWS>|${API_BYPASS_AWS:-false}|g" settings.json
          - sed -i "s|<ALLOW_LEGACY_LOGIN>|${ALLOW_LEGACY_LOGIN:-false}|g" settings.json
          - sed -i "s|<ENROLL_URL>|$ENROLL_URL|g" settings.json
          - sed -i "s|<ZKTECO_URL>|$ZKTECO_URL|g" settings.json
          - sed -i "s|<ZKTECO_KEY>|$ZKTECO_KEY|g" settings.json
          - sed -i "s|<BEANSTALK_NAME>|$BEANSTALK_NAME|g" mup.js
          - sed -i "s|<ROOT_URL>|$ROOT_URL|g" mup.js
          - sed -i "s|<AWS_KEY_PAIR_NAME>|$AWS_KEY_PAIR_NAME|g" mup.js
          - sed -i "s|<NODE_OPTIONS>|$NODE_OPTIONS|g" mup.js
          - sed -i "s|<TOOL_NODE_FLAGS>|$TOOL_NODE_FLAGS|g" mup.js
          - sed -i "s|<MONGO_URL_TEST>|$MONGO_URL_TEST|g" mup.js
          - sed -i "s|<AWS_DEPLOY_KEY>|$AWS_DEPLOY_KEY|g" mup.js
          - sed -i "s|<AWS_DEPLOY_SECRET>|$AWS_DEPLOY_SECRET|g" mup.js
          - sed -i "s|<IDP_CLIENT_SECRET>|$IDP_CLIENT_SECRET|g" settings.json
          - sed -i "s|<AWS_ACCESS_KEY_ID>|$AWS_ACCESS_KEY_ID|g" settings.json
          - sed -i "s|<AWS_SECRET_ACCESS_KEY>|$AWS_SECRET_ACCESS_KEY|g" settings.json
          - sed -i "s|<AWS_BILLING_REGION>|$AWS_BILLING_REGION|g" settings.json
          - sed -i "s|<AWS_BILLING_KEY>|$AWS_BILLING_KEY|g" settings.json
          - sed -i "s|<AWS_REGION>|$AWS_REGION|g" settings.json
          - sed -i "s|<AWS_BILLING_SECRET>|$AWS_BILLING_SECRET|g" settings.json
          - sed -i "s|<SINGLE_INVOICE_QUEUE_URL>|$SINGLE_INVOICE_QUEUE_URL|g" settings.json
          - sed -i "s|<SINGLE_INVOICE_LAMBDA_NAME>|$SINGLE_INVOICE_LAMBDA_NAME|g" settings.json
          - sed -i "s|<PAY_INVOICE_URL>|$PAY_INVOICE_URL|g" settings.json
          - sed -i "s|<COMPLETE_ADYEN_BALANCE_PLATFORM_ONBOARDING_URL>|$COMPLETE_ADYEN_BALANCE_PLATFORM_ONBOARDING_URL|g" settings.json
          - sed -i "s|<INVOICE_ORG_QUEUE_URL>|$INVOICE_ORG_QUEUE_URL|g" settings.json
          - sed -i "s|<AUTOPAY_QUEUE_URL>|$AUTOPAY_QUEUE_URL|g" settings.json
          - sed -i "s|<BILLING_ORGS_FUNCTION_NAME>|$BILLING_ORGS_FUNCTION_NAME|g" settings.json
          - sed -i "s|<COGNITO_USER_POOL_ID>|$COGNITO_USER_POOL_ID|g" settings.json
          - sed -i "s|<COGNITO_ACCESS_KEY_ID>|$COGNITO_ACCESS_KEY_ID|g" settings.json
          - sed -i "s|<COGNITO_SECRET_KEY_ID>|$COGNITO_SECRET_KEY_ID|g" settings.json
          - sed -i "s|<COGNITO_CLIENT_ID>|$COGNITO_CLIENT_ID|g" settings.json
          - sed -i "s|<COGNITO_REGION>|$COGNITO_REGION|g" settings.json
          - sed -i "s|<KMS_KEY_ID>|$KMS_KEY_ID|g" settings.json
          - sed -i "s|<KMS_ACCESS_KEY>|$KMS_ACCESS_KEY|g" settings.json
          - sed -i "s|<KMS_SECRET_KEY>|$KMS_SECRET_KEY|g" settings.json
          - sed -i "s|<KMS_REGION>|$KMS_REGION|g" settings.json
          - sed -i "s|<KMS_SIGNING_ALGO>|$KMS_SIGNING_ALGO|g" settings.json
          - sed -i "s|<KMS_ALGO_ID>|$KMS_ALGO_ID|g" settings.json
          - sed -i "s|<MONTI_APP_ID>|$MONTI_APP_ID|g" settings.json
          - sed -i "s|<MONTI_APP_SECRET>|$MONTI_APP_SECRET|g" settings.json
          - sed -i "s|<ADYEN_BALANCE_PLATFORM_API_KEY>|${ADYEN_BALANCE_PLATFORM_API_KEY//&/\\&}|g" settings.json
          - sed -i "s|<ADYEN_LEGAL_ENTITY_MANAGEMENT_API_KEY>|$ADYEN_LEGAL_ENTITY_MANAGEMENT_API_KEY|g" settings.json
          - sed -i "s|<ADYEN_ACCOUNT_ID>|$ADYEN_ACCOUNT_ID|g" settings.json
          - sed -i "s|<ADYEN_LIABLE_BALANCE_ACCOUNT_ID>|$ADYEN_LIABLE_BALANCE_ACCOUNT_ID|g" settings.json
          - sed -i "s|<ADYEN_REPORT_PW>|$ADYEN_REPORT_PW|g" settings.json
          - sed -i "s|<ADYEN_REPORT_USER>|$ADYEN_REPORT_USER|g" settings.json
          - sed -i "s|<ADYEN_REPORT_DOWNLOAD_URL_PREFIX>|$ADYEN_REPORT_DOWNLOAD_URL_PREFIX|g" settings.json
          - sed -i "s|<ADYEN_TRANSFER_FUNDS_URL>|$ADYEN_TRANSFER_FUNDS_URL|g" settings.json
          - sed -i "s|<BILLING_ERRORS_EMAIL>|$BILLING_ERRORS_EMAIL|g" settings.json
          - sed -i "s|<GROUP_SYNC_ERRORS_EMAIL>|$GROUP_SYNC_ERRORS_EMAIL|g" settings.json
          - sed -i "s|<ROLLBAR_CLIENT>|$ROLLBAR_CLIENT|g" settings.json
          - sed -i "s|<ROLLBAR_SERVER>|$ROLLBAR_SERVER|g" settings.json
          - sed -i "s|<TINYMCE_API_KEY>|$TINYMCE_API_KEY|g" settings.json
          - sed -i "s|<FLEXMONSTER_LICENSE_KEY>|$FLEXMONSTER_LICENSE_KEY|g" settings.json
          - sed -i "s|<FLEXMONSTER_BK_LICENSE_KEY>|$FLEXMONSTER_BK_LICENSE_KEY|g" settings.json
          - sed -i "s|<KINDERCONNECT_NOTIFICATION_EMAIL>|$KINDERCONNECT_NOTIFICATION_EMAIL|g" settings.json
          - sed -i "s|<REDSHIFT_DB_HOST>|$REDSHIFT_DB_HOST|g" settings.json
          - sed -i "s|<REDSHIFT_DB_PORT>|$REDSHIFT_DB_PORT|g" settings.json
          - sed -i "s|<REDSHIFT_DB_NAME>|$REDSHIFT_DB_NAME|g" settings.json
          - sed -i "s|<REDSHIFT_DB_USER>|$REDSHIFT_DB_USER|g" settings.json
          - sed -i "s|<REDSHIFT_DB_PASSWORD>|$REDSHIFT_DB_PASSWORD|g" settings.json
          - sed -i "s|<REDIS_TLS>|$REDIS_TLS|g" settings.json
          - sed -i "s|<REDIS_PORT>|$REDIS_PORT|g" settings.json
          - sed -i "s|<REDIS_HOST>|$REDIS_HOST|g" settings.json
          - sed -i "s|<REDIS_AUTH_TOKEN>|$REDIS_AUTH_TOKEN|g" settings.json
          - sed -i "s|<REDIS_REJECT_UNAUTHORIZED>|$REDIS_REJECT_UNAUTHORIZED|g" settings.json
          - sed -i "s|<REDIS_CLUSTER_MODE>|$REDIS_CLUSTER_MODE|g" settings.json
          - sed -i "s|<REDIS_ENABLE_READY_CHECK>|$REDIS_ENABLE_READY_CHECK|g" settings.json
          - sed -i "s|<REDIS_ENABLE_OFFLINE_QUEUE>|$REDIS_ENABLE_OFFLINE_QUEUE|g" settings.json
          - sed -i "s|<REDIS_CONNECT_TIMEOUT>|$REDIS_CONNECT_TIMEOUT|g" settings.json
          - sed -i "s|<REDIS_COMMAND_TIMEOUT>|$REDIS_COMMAND_TIMEOUT|g" settings.json
          - sed -i "s|<REDIS_RETRY_INTERVALS>|$REDIS_RETRY_INTERVALS|g" settings.json
          - sed -i "s|<REDIS_MUTATION_OPTIMISTIC>|$REDIS_MUTATION_OPTIMISTIC|g" settings.json
          - sed -i "s|<REDIS_MUTATION_PUSH>|$REDIS_MUTATION_PUSH|g" settings.json
          - sed -i "s|<REDIS_DEBUG>|$REDIS_DEBUG|g" settings.json
          - sed -i "s|<REDIS_LOGGER_ENABLED>|$REDIS_LOGGER_ENABLED|g" settings.json
          - sed -i "s|<EVENTBRIDGE_URL_TEST>|$EVENTBRIDGE_URL_TEST|g" settings.json
          - sed -i "s|<MOMENT_EVENTBRIDGE_URL_TEST>|$MOMENT_EVENTBRIDGE_URL_TEST|g" settings.json
          - sed -i "s|<SMS_SQS_QUEUE_URL>|$SMS_SQS_QUEUE_URL|g" settings.json
          - sed -i "s|<INSTANCE_TYPE>|$INSTANCE_TYPE|g" mup.js
          - cd ..
          - curl https://install.meteor.com/?release=3.0.4 | sh
          - export METEOR_ALLOW_SUPERUSER=true
          - npm install --unsafe-perm
          - meteor npm rebuild
          - cd .deploy
          - npm i --unsafe-perm -g mup mup-aws-beanstalk
          - DEBUG=mup* mup deploy --verbose
        artifacts:
          - .deploy/settings.json
          - .deploy/mup.js
  caches:
    cypress: $HOME/.cache/Cypress
  services:
    mongo:
      image: mongo:6.0
pipelines:
  pull-requests:
    '**':
      - parallel:
        - step: *run-jest-tests
        - step: *run-tests-part-1
        - step: *run-tests-part-2
        - step: *run-tests-part-3
        - step: *run-tests-part-4
        - step: *run-tests-part-5
        - step: *run-tests-part-6
  custom:
    deploy-to-red:
      - step:
          name: "Deploy to QA Red Environment"
          deployment: red
          <<: *deploy-test
    deploy-to-orange:
      - step:
          name: "Deploy to QA Orange Environment"
          deployment: orange
          <<: *deploy-test
    deploy-to-yellow:
      - step:
          name: "Deploy to QA Yellow Environment"
          deployment: yellow
          <<: *deploy-test
    deploy-to-green:
      - step:
          name: "Deploy to QA Green Environment"
          deployment: green
          <<: *deploy-test
    deploy-to-magenta:
      - step:
          name: "Deploy to QA Magenta Environment"
          deployment: magenta
          <<: *deploy-test
    deploy-to-marigold:
      - step:
          name: "Deploy to QA Marigold Environment"
          deployment: marigold
          <<: *deploy-test
    deploy-to-apricot:
      - step:
          name: "Deploy to QA Apricot Environment"
          deployment: apricot
          <<: *deploy-test
    deploy-to-blue:
      - step:
          name: "Deploy to QA Blue Environment"
          deployment: blue
          <<: *deploy-test
    deploy-to-indigo:
      - step:
          <<: *deploy-test
          name: "Deploy to QA Indigo Environment"
          deployment: indigo
    deploy-to-new-indigo:
      - step:
          <<: *deploy-test
          name: "Deploy to QA New Indigo Environment"
          deployment: newindigo
    deploy-to-violet:
      - step:
          name: "Deploy to QA Violet Environment"
          deployment: violet
          <<: *deploy-test
    deploy-to-alpha:
      - step:
          <<: *deploy-test
          name: "Deploy to QA Alpha Environment"
          deployment: alpha
    deploy-to-beta:
      - step:
          name: "Deploy to QA Beta Environment"
          deployment: beta
          <<: *deploy-test
    deploy-to-gamma:
      - step:
          <<: *deploy-test
          name: "Deploy to QA Gamma Environment"
          deployment: gamma
    deploy-to-delta:
      - step:
          name: "Deploy to QA Delta Environment"
          deployment: delta
          <<: *deploy-test
    deploy-to-epsilon:
      - step:
          <<: *deploy-test
          name: "Deploy to QA Epsilon Environment"
          deployment: epsilon
    deploy-to-zeta:
      - step:
          name: "Deploy to QA Zeta Environment"
          deployment: zeta
          <<: *deploy-test
    deploy-to-purple:
      - step:
          name: "Deploy to QA Purple Environment"
          deployment: purple
          <<: *deploy-test
    deploy-to-ecs-staging-omega:
      - step:
          name: Deploy to Omega ECS Staging Environment
          image: atlassian/default-image:3
          size: 4x
          caches:
           - node
           - docker
          services:
           - docker
          deployment: ECS-Staging-Omega
          script:
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install
            - mkdir -p ../webbuild
            - meteor build --architecture=os.linux.x86_64 ../webbuild
            # Prepare Docker build
            # Install AWS CLI
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            # Log in to AWS ECR
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
            - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
            - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
            - ./appconfig_helper
            - node build_env_file.js
            - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            # Build Docker image
            - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
            - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
            - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile2 .
            - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
            - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
            - docker push $REPOSITORY_URI:$IMAGE_TAG
            # Deploy to AWS ECS
            - set -e
            - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
            - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
            - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
            - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
            - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
            - set +e

    deploy-to-ecs-staging-alpha:
      - step:
          name: Deploy to Alpha ECS Staging Environment
          image: atlassian/default-image:3
          size: 4x
          caches:
           - node
           - docker
          services:
           - docker
          deployment: ECS-Staging-Alpha
          script:
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install
            - mkdir -p ../webbuild
            - meteor build --architecture=os.linux.x86_64 ../webbuild
            # Prepare Docker build
            # Install AWS CLI
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            # Log in to AWS ECR
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
            - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
            - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
            - ./appconfig_helper
            - node build_env_file.js
            - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            # Build Docker image
            - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
            - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
            - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile2 .
            - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
            - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
            - docker push $REPOSITORY_URI:$IMAGE_TAG
            # Deploy to AWS ECS
            - set -e
            - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
            - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
            - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
            - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
            - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
            - set +e

    deploy-to-ecs-staging-zeta:
      - step:
          name: Deploy to Zeta ECS Staging Environment
          image: atlassian/default-image:3
          size: 4x
          caches:
           - node
           - docker
          services:
           - docker
          deployment: ECS-Staging-Zeta
          script:
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install --unsafe-perm
            - meteor npm rebuild
            - mkdir -p ../webbuild
            - meteor build --architecture=os.linux.x86_64 ../webbuild
            # Prepare Docker build
            # Install AWS CLI
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            # Log in to AWS ECR
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
            - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
            - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
            - ./appconfig_helper
            - node build_env_file.js
            - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            # Build Docker image
            - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
            - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
            - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile2 .
            - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
            - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
            - docker push $REPOSITORY_URI:$IMAGE_TAG
            # Deploy to AWS ECS
            - set -e
            - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
            - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
            - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
            - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
            - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
            - set +e

    deploy-to-ecs-staging-gamma:
      - step:
          name: Deploy to Gamma ECS Staging Environment
          image: atlassian/default-image:3
          size: 4x
          caches:
           - node
           - docker
          services:
           - docker
          deployment: ECS-Staging-Gamma
          script:
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install
            - mkdir -p ../webbuild
            - meteor build --architecture=os.linux.x86_64 ../webbuild
            # Prepare Docker build
            # Install AWS CLI
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            # Log in to AWS ECR
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
            - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
            - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
            - ./appconfig_helper
            - node build_env_file.js
            - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            # Build Docker image
            - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
            - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
            - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile2 .
            - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
            - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
            - docker push $REPOSITORY_URI:$IMAGE_TAG
            # Deploy to AWS ECS
            - set -e
            - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
            - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
            - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
            - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
            - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
            - set +e

    deploy-to-ecs-flex-report:
      - step:
          name: Deploy to Flex Report Beta Testing ECS Staging Environment
          image: atlassian/default-image:3
          size: 4x
          caches:
           - node
           - docker
          services:
           - docker
          deployment: ECS-Staging-Flex
          script:
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install
            - mkdir -p ../webbuild
            - meteor build --architecture=os.linux.x86_64 ../webbuild
            # Prepare Docker build
            # Install AWS CLI
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            # Log in to AWS ECR
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
            - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
            - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
            - ./appconfig_helper
            - node build_env_file.js
            - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            # Build Docker image
            - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
            - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
            - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile2 .
            - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
            - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
            - docker push $REPOSITORY_URI:$IMAGE_TAG
            # Deploy to AWS ECS
            - set -e
            - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
            - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
            - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
            - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
            - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
            - set +e

    deploy-to-ecs-prod-stage:
      - step:
          name: Deploy to ECS Prod Stage Environment
          image: atlassian/default-image:3
          size: 4x
          caches:
           - node
           - docker
          services:
           - docker
          deployment: ECS-Prod-Stage
          script:
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install
            - mkdir -p ../webbuild
            - meteor build --architecture=os.linux.x86_64 ../webbuild
            # Prepare Docker build
            # Install AWS CLI
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            # Log in to AWS ECR
            - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
            - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
            - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
            - ./appconfig_helper
            - node build_env_file.js
            - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
            - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
            # Build Docker image
            - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
            - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
            - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile .
            - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
            - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
            - docker push $REPOSITORY_URI:$IMAGE_TAG
            # Deploy to AWS ECS
            - set -e
            - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
            - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
            - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
            - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
            - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
            - set +e

    race-condition-load-test:
      - step:
          name: "Run Race Condition Load Tests"
          size: 4x
          services:
            - mongo
          caches:
            - node
          script:
            - echo "🚀 Starting Race Condition Load Tests Pipeline"
            - echo "🔧 Setting up environment..."
            - cp $BITBUCKET_CLONE_DIR/__tests__/settings-unit-tests.json $BITBUCKET_CLONE_DIR/meteor.development.local.settings
            - curl https://install.meteor.com/?release=3.0.4 | sh
            - export METEOR_ALLOW_SUPERUSER=true
            - npm install --unsafe-perm --production=false
            - meteor npm rebuild
            - echo "✅ Environment setup completed"
            # Run the pipeline-optimized script which handles both Jest and fallback approaches
            - echo "🧪 Running comprehensive race condition tests..."
            - chmod +x scripts/pipeline-race-condition-test.sh
            - ./scripts/pipeline-race-condition-test.sh --verbose
          artifacts:
            - meteor.development.local.settings
  branches:
    production:
      - parallel:
          - step:
              name: "Production Deploy"
              script:
                - echo "Production deploy placeholder. No actions defined."
          - step:
              <<: *run-jest-tests
          - step:
              <<: *run-tests-part-1
          - step:
              <<: *run-tests-part-2
          - step:
              <<: *run-tests-part-3
          - step:
              <<: *run-tests-part-4
          - step:
              <<: *run-tests-part-5
          - step:
              <<: *run-tests-part-6
      - parallel:
          - step:
              name: "Deploy to Manage"
              size: 4x
              trigger: manual
              deployment: prod-manage
              script:
              - cd .deploy
              - cp mup-prod.js mup.js
              - $BITBUCKET_CLONE_DIR/scripts/configure_deploy.sh
              - curl https://install.meteor.com/?release=3.0.4 | sh
              - export METEOR_ALLOW_SUPERUSER=true
              - npm install --unsafe-perm
              - meteor npm rebuild
              - npm i --unsafe-perm -g mup mup-aws-beanstalk
              - DEBUG=mup* mup deploy --verbose
          - step:
              name: "Deploy to Manage - White Label"
              size: 4x
              trigger: manual
              deployment: prod-manage-wl
              script:
                - cd .deploy
                - cp mup-prod.js mup.js
                - $BITBUCKET_CLONE_DIR/scripts/configure_deploy.sh
                - curl https://install.meteor.com/?release=3.0.4 | sh
                - export METEOR_ALLOW_SUPERUSER=true
                - npm install --unsafe-perm
                - meteor npm rebuild
                - npm i --unsafe-perm -g mup mup-aws-beanstalk
                - DEBUG=mup* mup deploy --verbose
          - step:
              name: "Deploy to Manage - Prod Stage"
              size: 4x
              trigger: manual
              deployment: prod-stage
              script:
                - cd .deploy
                - cp mup-prod.js mup.js
                - $BITBUCKET_CLONE_DIR/scripts/configure_deploy.sh
                - curl https://install.meteor.com/?release=3.0.4 | sh
                - export METEOR_ALLOW_SUPERUSER=true
                - npm install --unsafe-perm
                - meteor npm rebuild
                - npm i --unsafe-perm -g mup mup-aws-beanstalk
                - DEBUG=mup* mup deploy --verbose
          - step:
              name: Deploy to Manage ECS Production Environment
              size: 4x
              image: atlassian/default-image:3
              trigger: manual
              caches:
                - node
                - docker
              services:
                - docker
              deployment: ECS-Production-Manage
              script:
                - curl https://install.meteor.com/?release=3.0.4 | sh
                - export METEOR_ALLOW_SUPERUSER=true
                - npm install
                - mkdir -p ../webbuild
                - meteor build --architecture=os.linux.x86_64 ../webbuild
                # Prepare Docker build
                # Install AWS CLI
                - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
                - unzip awscliv2.zip
                - ./aws/install
                - aws --version
                # Log in to AWS ECR
                - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
                - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
                - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
                - ./appconfig_helper
                - node build_env_file.js
                - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
                - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
                # Build Docker image
                - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
                - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
                - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile .
                - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
                - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
                - docker push $REPOSITORY_URI:$IMAGE_TAG
                # Deploy to AWS ECS
                - set -e
                - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
                - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
                - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
                - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
                - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
                - set +e

          - step:
              name: Deploy to Scheduler ECS Production Environment
              size: 4x
              image: atlassian/default-image:3
              trigger: manual
              caches:
                - node
                - docker
              services:
                - docker
              deployment: ECS-Production-Scheduler
              script:
                - curl https://install.meteor.com/?release=3.0.4 | sh
                - export METEOR_ALLOW_SUPERUSER=true
                - npm install
                - mkdir -p ../webbuild
                - meteor build --architecture=os.linux.x86_64 ../webbuild
                # Prepare Docker build
                # Install AWS CLI
                - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
                - unzip awscliv2.zip
                - ./aws/install
                - aws --version
                # Log in to AWS ECR
                - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
                - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
                - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
                - ./appconfig_helper
                - node build_env_file.js
                - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
                - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
                # Build Docker image
                - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
                - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
                - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile .
                - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
                - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
                - docker push $REPOSITORY_URI:$IMAGE_TAG
                # Deploy to AWS ECS
                - set -e
                - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
                - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
                - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
                - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
                - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
                - set +e

          - step:
              name: Deploy to RAS ECS Production Environment
              size: 4x
              image: atlassian/default-image:3
              trigger: manual
              caches:
                - node
                - docker
              services:
                - docker
              deployment: ECS-RAS
              script:
                - curl https://install.meteor.com/?release=3.0.4 | sh
                - export METEOR_ALLOW_SUPERUSER=true
                - npm install
                - mkdir -p ../webbuild
                - meteor build --architecture=os.linux.x86_64 ../webbuild
                # Prepare Docker build
                # Install AWS CLI
                - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
                - unzip awscliv2.zip
                - ./aws/install
                - aws --version
                # Log in to AWS ECR
                - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
                - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
                - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
                - ./appconfig_helper
                - node build_env_file.js
                - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
                - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
                # Build Docker image
                - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
                - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
                - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile .
                - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
                - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
                - docker push $REPOSITORY_URI:$IMAGE_TAG
                # Deploy to AWS ECS
                - set -e
                - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
                - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
                - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
                - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
                - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
                - set +e

          - step:
              name: Deploy to KLC ECS Production Environment
              size: 4x
              image: atlassian/default-image:3
              trigger: manual
              caches:
                - node
                - docker
              services:
                - docker
              deployment: ECS-KLC
              script:
                - curl https://install.meteor.com/?release=3.0.4 | sh
                - export METEOR_ALLOW_SUPERUSER=true
                - npm install
                - mkdir -p ../webbuild
                - meteor build --architecture=os.linux.x86_64 ../webbuild
                # Prepare Docker build
                # Install AWS CLI
                - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
                - unzip awscliv2.zip
                - ./aws/install
                - aws --version
                # Log in to AWS ECR
                - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
                - cd ../webbuild && file=$(ls | head -n 1) && tar -zxf $file
                - cd $BITBUCKET_CLONE_DIR/.deploy/ecs
                - ./appconfig_helper
                - node build_env_file.js
                - cp docker_start $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile2 $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp Dockerfile-klc-prod $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp env.txt $BITBUCKET_CLONE_DIR/../webbuild/bundle
                - cp build_env_file.js $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
                - cp appconfig_helper $BITBUCKET_CLONE_DIR/../webbuild/bundle/programs/server
                # Build Docker image
                - IMAGE_TAG="$BITBUCKET_BUILD_NUMBER-$BITBUCKET_COMMIT"
                - FULL_IMAGE="$REPOSITORY_URI:$IMAGE_TAG"
                - cd $BITBUCKET_CLONE_DIR/../webbuild/bundle && docker build --no-cache -t $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER -f Dockerfile-klc-prod .
                - docker tag $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER $REPOSITORY_URI:$IMAGE_TAG
                - docker push $REPOSITORY_URI:$BITBUCKET_BUILD_NUMBER
                - docker push $REPOSITORY_URI:$IMAGE_TAG
                # Deploy to AWS ECS
                - set -e
                - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_FAMILY" --region "$AWS_REGION")
                - NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq --arg IMAGE "$FULL_IMAGE" '.taskDefinition | .containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
                - NEW_TASK_INFO=$(aws ecs register-task-definition --region "$AWS_REGION" --cli-input-json "$NEW_TASK_DEFINITION")
                - NEW_REVISION=$(echo "$NEW_TASK_INFO" | jq '.taskDefinition.revision')
                - aws ecs update-service --cluster "$ECS_CLUSTER_NAME" --service "$ECS_SERVICE_NAME" --task-definition "${TASK_FAMILY}:${NEW_REVISION}"
                - set +e  

